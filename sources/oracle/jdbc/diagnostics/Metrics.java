package oracle.jdbc.diagnostics;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import oracle.jdbc.internal.Monitor;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Metrics.class */
public class Metrics {
    static final byte METRIC_DURATION_BEGIN_IDX = 0;
    static final byte METRIC_DURATION_END_IDX = 1;
    private static final Map<String, ConsolidatedMetric> consolidatedMetricsMap = Collections.synchronizedMap(new HashMap());
    private static final String PRINT_FORMAT = "%s : occurrences %d / min duration %d / max duration %d / avg duration %d" + System.lineSeparator();

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Metrics$ConnectionEvent.class */
    public enum ConnectionEvent {
        CONNECT("Connection Time"),
        NET_SESSION_ESTABLISHMENT("NS session time"),
        SOCKET_ESTABLISHMENT("Socket Connect"),
        NS_CONNECT_SEND1("Send NS_CONNECT_1"),
        NS_CONNECT_RECEIVE1("Receive NS_CONNECT_1"),
        NS_CONNECT_SEND2("Send NS_CONNECT_2"),
        NS_CONNECT_RECEIVE2("Receive NS_CONNECT_2"),
        NS_ACCEPT("Process NS_ACCEPT"),
        NS_REDIRECT("Receive NS_REDIRECT"),
        ASO_NEGOTIATION("Complete ASO roundtrip"),
        SSL_BEGIN_HANDSHAKE("Begin Handshake"),
        SSL_HANDSHAKE("SSL session"),
        SSL_CONTEXT_INIT("SSL Context Initialization"),
        SSL_HS_ROUND_TRIP_RUNTASKS("SSL Handshake RunTasks"),
        SSL_HS_ROUND_TRIP_WRAP("SSL Handshake Wrap"),
        SSL_HS_ROUND_TRIP_UNWRAP("SSL Handshake UnWrap"),
        SSL_HS_ROUND_TRIP_RECEIVE("SSL Handshake Receive"),
        SSL_HS_ROUND_TRIP_SEND("SSL Handshake Send"),
        SSL_BEGIN_HANDSHAKE_RENEGOTIATION("Renegotiation Begin Handshake"),
        SSL_RENEGOTIATION("SSL Renegotiation"),
        SSL_RENEGO_ROUND_TRIP_RUNTASKS("SSL Renegotiation RunTasks"),
        SSL_RENEGO_ROUND_TRIP_WRAP("SSL Renegotiation Wrap"),
        SSL_RENEGO_ROUND_TRIP_UNWRAP("SSL Renegotiation UnWrap"),
        SSL_RENEGO_ROUND_TRIP_RECEIVE("SSL Renegotiation Receive"),
        SSL_RENEGO_ROUND_TRIP_SEND("SSL Renegotiation Send"),
        TTC_NEGOTIATION("TTC negotiation"),
        TTC_TTIINIT_OPTIMIZATION("TTC TTIINIT Optimization"),
        TTC_TTICOOKIE_OPTIMIZATION("TTC TTICookie Optimization"),
        TTC_RENEGOTIATION("TTC re-negotiation"),
        TTC_TTIPRO("TTIPRO roundtrip"),
        TTC_TTIDTY("TTIDTY roundtrip"),
        AUTH("Authentication"),
        SEND_OSESS("Send OSESSKEY"),
        RECEIVE_OSESS("Receive OSESSKEY"),
        SEND_OAUTH("Send OAUTH"),
        AUTH_GEN_PK("Generate PK"),
        RECEIVE_OAUTH("Receive OAUTH"),
        OVERSION("OVERSION roundtrip");

        private final String name;

        ConnectionEvent(String name) {
            this.name = name;
        }

        public String getName() {
            return this.name;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Metrics$MetricReport.class */
    public static class MetricReport {
        private int occurrences;
        private long averageDuration;

        protected MetricReport(int occurrences, long averageDuration) {
            this.occurrences = 0;
            this.averageDuration = 0L;
            this.occurrences = occurrences;
            this.averageDuration = averageDuration;
        }

        public int getOccurrenceCount() {
            return this.occurrences;
        }

        public long getAverageDuration() {
            return this.averageDuration;
        }

        public String toString() {
            return "MetricReport {occurrences=" + this.occurrences + ", averageDuration=" + this.averageDuration + '}';
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Metrics$ConsolidatedMetric.class */
    private static class ConsolidatedMetric {
        private int occurrences;
        private long minDuration;
        private long maxDuration;
        private long aggregateDuration;
        private final Monitor monitor;

        private ConsolidatedMetric() {
            this.occurrences = 0;
            this.minDuration = Long.MAX_VALUE;
            this.maxDuration = Long.MIN_VALUE;
            this.aggregateDuration = 0L;
            this.monitor = Monitor.newInstance();
        }

        private void setMinDuration(long minDuration) {
            if (minDuration < this.minDuration) {
                this.minDuration = minDuration;
            }
        }

        private void setMaxDuration(long maxDuration) {
            if (maxDuration > this.maxDuration) {
                this.maxDuration = maxDuration;
            }
        }

        private void aggregateDuration(long duration) {
            this.aggregateDuration += duration;
        }

        public void add(long duration) {
            Monitor.CloseableLock ignore = this.monitor.acquireCloseableLock();
            Throwable th = null;
            if (duration >= 0) {
                try {
                    try {
                        this.occurrences++;
                        setMinDuration(duration);
                        setMaxDuration(duration);
                        aggregateDuration(duration);
                    } catch (Throwable th2) {
                        th = th2;
                        throw th2;
                    }
                } catch (Throwable th3) {
                    if (ignore != null) {
                        if (th != null) {
                            try {
                                ignore.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            ignore.close();
                        }
                    }
                    throw th3;
                }
            }
            if (ignore != null) {
                if (0 != 0) {
                    try {
                        ignore.close();
                        return;
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                        return;
                    }
                }
                ignore.close();
            }
        }

        public int getOccurrencesCount() {
            return this.occurrences;
        }

        public long getMinDuration() {
            return this.minDuration;
        }

        public long getMaxDuration() {
            return this.maxDuration;
        }

        public long getAverageDuration() {
            if (this.occurrences > 0) {
                return this.aggregateDuration / this.occurrences;
            }
            return 0L;
        }
    }

    public static void enable(boolean isEnabled) {
        AbstractDiagnosable.enableMetrics(isEnabled);
    }

    public void begin(ConnectionEvent event) {
    }

    public void end(ConnectionEvent event) {
    }

    public void close() {
    }

    protected void add(Map<String, long[]> connectionMetricsMap) {
        connectionMetricsMap.forEach((key, value) -> {
            ConsolidatedMetric consolidatedMetric = consolidatedMetricsMap.computeIfAbsent(key, v -> {
                return new ConsolidatedMetric();
            });
            long duration = value[1] - value[0];
            if (duration >= 0) {
                consolidatedMetric.add(duration);
            }
        });
    }

    public static void clear() {
        consolidatedMetricsMap.clear();
    }

    public static MetricReport getReport(ConnectionEvent event) {
        ConsolidatedMetric consolidatedMetric = consolidatedMetricsMap.get(event.getName());
        if (consolidatedMetric != null) {
            return new MetricReport(consolidatedMetric.getOccurrencesCount(), consolidatedMetric.getAverageDuration());
        }
        return null;
    }

    private static String getFormattedMetricReport(String spacePrefix, ConnectionEvent connectionEvent, TimeUnit timeUnit) {
        int occurrenceCount;
        StringBuilder stringBuilder = new StringBuilder();
        String eventName = connectionEvent.getName();
        int retryIdentifier = 1;
        do {
            occurrenceCount = 0;
            ConsolidatedMetric consolidatedMetric = consolidatedMetricsMap.get(eventName);
            if (consolidatedMetric != null) {
                occurrenceCount = consolidatedMetric.getOccurrencesCount();
                if (occurrenceCount > 0) {
                    long minDuration = timeUnit.convert(consolidatedMetric.getMinDuration(), TimeUnit.NANOSECONDS);
                    long maxDuration = timeUnit.convert(consolidatedMetric.getMaxDuration(), TimeUnit.NANOSECONDS);
                    long avgDuration = timeUnit.convert(consolidatedMetric.getAverageDuration(), TimeUnit.NANOSECONDS);
                    stringBuilder.append(spacePrefix);
                    stringBuilder.append(String.format(PRINT_FORMAT, eventName, Integer.valueOf(occurrenceCount), Long.valueOf(minDuration), Long.valueOf(maxDuration), Long.valueOf(avgDuration)));
                    int i = retryIdentifier;
                    retryIdentifier++;
                    eventName = connectionEvent.getName() + " " + i;
                }
            }
        } while (occurrenceCount > 0);
        return stringBuilder.toString();
    }

    public static String getReport(TimeUnit timeUnit) {
        if (!consolidatedMetricsMap.isEmpty()) {
            return "Printing metrics in time units " + timeUnit + System.lineSeparator() + getFormattedMetricReport("", ConnectionEvent.CONNECT, timeUnit) + getFormattedMetricReport(" ", ConnectionEvent.NET_SESSION_ESTABLISHMENT, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SOCKET_ESTABLISHMENT, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.NS_CONNECT_SEND1, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.NS_CONNECT_RECEIVE1, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.NS_CONNECT_SEND2, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.NS_CONNECT_RECEIVE2, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.NS_ACCEPT, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.NS_REDIRECT, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.ASO_NEGOTIATION, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SSL_BEGIN_HANDSHAKE, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SSL_HANDSHAKE, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SSL_CONTEXT_INIT, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SSL_HS_ROUND_TRIP_RUNTASKS, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_HS_ROUND_TRIP_WRAP, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_HS_ROUND_TRIP_UNWRAP, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_HS_ROUND_TRIP_RECEIVE, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_HS_ROUND_TRIP_SEND, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SSL_BEGIN_HANDSHAKE_RENEGOTIATION, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SSL_RENEGOTIATION, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_RENEGO_ROUND_TRIP_RUNTASKS, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_RENEGO_ROUND_TRIP_WRAP, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_RENEGO_ROUND_TRIP_UNWRAP, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_RENEGO_ROUND_TRIP_RECEIVE, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.SSL_RENEGO_ROUND_TRIP_SEND, timeUnit) + getFormattedMetricReport(" ", ConnectionEvent.TTC_NEGOTIATION, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.TTC_TTIPRO, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.TTC_TTIDTY, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.TTC_RENEGOTIATION, timeUnit) + getFormattedMetricReport(" ", ConnectionEvent.AUTH, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SEND_OSESS, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.RECEIVE_OSESS, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.SEND_OAUTH, timeUnit) + getFormattedMetricReport("    ", ConnectionEvent.AUTH_GEN_PK, timeUnit) + getFormattedMetricReport("  ", ConnectionEvent.RECEIVE_OAUTH, timeUnit) + getFormattedMetricReport(" ", ConnectionEvent.OVERSION, timeUnit);
        }
        return "Metrics are either cleared or not collected.";
    }

    public static void print(OutputStream outputStream, TimeUnit timeUnit) throws IOException {
        outputStream.write(getReport(timeUnit).getBytes());
    }
}
