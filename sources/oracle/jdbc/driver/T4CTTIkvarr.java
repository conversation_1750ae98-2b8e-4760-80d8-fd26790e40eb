package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.KeywordValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkvarr.class */
class T4CTTIkvarr extends T4CTTIMsg {
    KeywordValue[] kpdkvarrptr;
    long kpdkvarrflg;

    T4CTTIkvarr(T4CConnection _conn) {
        super(_conn, (byte) 0);
        this.kpdkvarrptr = null;
    }

    void unmarshal() throws SQLException, IOException {
        int kpdkvarrlen = (int) this.meg.unmarshalUB4();
        if (kpdkvarrlen > 0) {
            this.kpdkvarrptr = new KeywordValueI[kpdkvarrlen];
            for (int i = 0; i < kpdkvarrlen; i++) {
                this.kpdkvarrptr[i] = KeywordValueI.unmarshal(this.meg);
            }
        } else {
            this.kpdkvarrptr = null;
        }
        this.kpdkvarrflg = this.meg.unmarshalUB4();
    }
}
