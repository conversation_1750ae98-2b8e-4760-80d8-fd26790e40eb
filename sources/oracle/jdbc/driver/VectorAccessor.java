package oracle.jdbc.driver;

import java.sql.Clob;
import java.sql.SQLException;
import oracle.jdbc.VectorMetaData;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.driver.utils.AutoCloseableAdapter;
import oracle.jdbc.driver.utils.ThrowingRunnable;
import oracle.sql.BLOB;
import oracle.sql.CLOB;
import oracle.sql.VECTOR;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorAccessor.class */
public class VectorAccessor extends LobCommonAccessor {
    static final int MAXLENGTH = 4000;
    private final Class<?> defaultConversion;

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.diagnostics.Diagnosable
    public /* bridge */ /* synthetic */ Diagnosable getDiagnosable() {
        return super.getDiagnosable();
    }

    @Override // oracle.jdbc.driver.Accessor
    public /* bridge */ /* synthetic */ String toString() {
        return super.toString();
    }

    VectorAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        int oracleTypeCode;
        super(Representation.VECTOR, stmt, 4000, isStoredInBindData);
        init(stmt, 127, 127, form, isOutBind);
        initForDataAccess(external_type, max_len, null);
        if (oracle.jdbc.internal.OracleTypes.isVector(external_type)) {
            oracleTypeCode = external_type;
        } else if (external_type == 127) {
            oracleTypeCode = -105;
        } else {
            throw ((SQLException) DatabaseError.createSqlException(stmt.connection, 4, Integer.toString(external_type)).fillInStackTrace());
        }
        VectorMetaData metaData = VectorMetaDataImpl.create(-1, oracleTypeCode, false);
        setVectorMetaData(metaData);
        this.defaultConversion = getDefaultConversion(this.statement.connection.vectorDefaultGetObjectType);
    }

    VectorAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(Representation.VECTOR, stmt, 4000, false);
        init(stmt, 127, 127, form, false);
        initForDescribe(127, max_len, nullable, flags, precision, scale, contflag, total_elems, form, null);
        initForDataAccess(0, max_len, null);
        this.defaultConversion = getDefaultConversion(stmt.connection.vectorDefaultGetObjectType);
    }

    @Override // oracle.jdbc.driver.LobCommonAccessor
    final boolean isPrefetched() {
        return this.lobPrefetchSizeForThisColumn > -1;
    }

    static Class<?> getDefaultConversion(String configured) {
        if (configured == null) {
            return null;
        }
        switch (configured) {
            case "double[]":
                return double[].class;
            case "float[]":
                return float[].class;
            case "byte[]":
                return byte[].class;
            case "short[]":
                return short[].class;
            case "int[]":
                return int[].class;
            case "long[]":
                return long[].class;
            case "boolean[]":
                return boolean[].class;
            case "String":
                return String.class;
            case "java.sql.Clob":
            case "Clob":
                return Clob.class;
            case "oracle.sql.VECTOR":
            case "VECTOR":
                return VECTOR.class;
            case "preferred-array-class":
                return Object.class;
            default:
                return null;
        }
    }

    @Override // oracle.jdbc.driver.LobCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.defaultConversion == null) {
            throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4, "JDBC 4.3 does not specify a default conversion for VECTOR. A default conversion may be configured using the  \"oracle.jdbc.vectorDefaultGetObjectType\" connection property");
        }
        if (Clob.class.isAssignableFrom(this.defaultConversion)) {
            return getCLOB(currentRow);
        }
        return decode(currentRow, this.defaultConversion);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    CLOB getCLOB(int currentRow) throws SQLException {
        String string = getString(currentRow);
        if (string == null) {
            return null;
        }
        char[] charArray = string.toCharArray();
        byte[] quasiLocator = T4CConnection.setupClobVectorQuasiLocator(charArray.length, this.statement.connection.conversion.serverCharSetId);
        CLOB clob = new CLOB(this.statement.connection, quasiLocator);
        clob.setPrefetchedData(charArray);
        clob.setLength(charArray.length);
        clob.setActivePrefetch(true);
        return clob;
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException {
        return (String) decode(currentRow, String.class);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    double[] getDoubleArray(int currentRow) throws SQLException {
        return (double[]) decode(currentRow, double[].class);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    float[] getFloatArray(int currentRow) throws SQLException {
        return (float[]) decode(currentRow, float[].class);
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    byte[] getBytes(int currentRow) throws SQLException {
        return (byte[]) decode(currentRow, byte[].class);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    short[] getShortArray(int currentRow) throws SQLException {
        return (short[]) decode(currentRow, short[].class);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    int[] getIntArray(int currentRow) throws SQLException {
        return (int[]) decode(currentRow, int[].class);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    long[] getLongArray(int currentRow) throws SQLException {
        return (long[]) decode(currentRow, long[].class);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    boolean[] getBooleanArray(int currentRow) throws SQLException {
        return (boolean[]) decode(currentRow, boolean[].class);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.GeneratedAccessor
    public VECTOR getOracleObject(int currentRow) throws SQLException {
        byte[] data = copyVectorData(currentRow);
        if (data == null) {
            return null;
        }
        return VECTOR.fromData(data);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    VECTOR getVECTOR(int currentRow) throws SQLException {
        return getOracleObject(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    VECTOR.SparseDoubleArray getSparseDoubleArray(int currentRow) throws SQLException {
        return (VECTOR.SparseDoubleArray) decode(currentRow, VECTOR.SparseDoubleArray.class);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    VECTOR.SparseFloatArray getSparseFloatArray(int currentRow) throws SQLException {
        return (VECTOR.SparseFloatArray) decode(currentRow, VECTOR.SparseFloatArray.class);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    VECTOR.SparseByteArray getSparseByteArray(int currentRow) throws SQLException {
        return (VECTOR.SparseByteArray) decode(currentRow, VECTOR.SparseByteArray.class);
    }

    private <T> T decode(int i, Class<T> cls) throws SQLException {
        long position = this.rowData.getPosition();
        try {
            ByteArray vectorData = getVectorData(i);
            if (vectorData == null) {
                return null;
            }
            T t = (T) VectorData.decode(vectorData, cls, !(vectorData instanceof ReadOnlyByteArray));
            this.rowData.setPosition(position);
            return t;
        } finally {
            this.rowData.setPosition(position);
        }
    }

    private byte[] copyVectorData(int currentRow) throws SQLException {
        if (isNull(currentRow)) {
            return null;
        }
        if (!isPrefetched()) {
            return copyLobVectorData(currentRow);
        }
        long length = getPrefetchedLength(currentRow);
        if (length > 2147483647L) {
            throw vectorTooLarge(length);
        }
        if (length > getPrefetchedDataLength(currentRow)) {
            return copyLobVectorData(currentRow);
        }
        return this.rowData.get(getPrefetchedDataOffset(currentRow), (int) length);
    }

    private ByteArray getVectorData(int currentRow) throws Exception {
        if (isNull(currentRow)) {
            return null;
        }
        if (!isPrefetched()) {
            byte[] data = copyLobVectorData(currentRow);
            return new SimpleByteArray(getDiagnosable(), data);
        }
        long length = getPrefetchedLength(currentRow);
        if (length > 2147483647L) {
            throw vectorTooLarge(length);
        }
        if (length > getPrefetchedDataLength(currentRow)) {
            byte[] data2 = copyLobVectorData(currentRow);
            return new SimpleByteArray(getDiagnosable(), data2);
        }
        this.rowData.setPosition(getPrefetchedDataOffset(currentRow));
        return this.rowData;
    }

    private byte[] copyLobVectorData(int currentRow) throws Exception {
        long length;
        byte[] locator = super.getBytesInternal(currentRow);
        BLOB blob = new BLOB(this.statement.connection, locator);
        AutoCloseableAdapter<SQLException> blobFree = autoFree(blob);
        Throwable th = null;
        try {
            if (isPrefetched()) {
                length = getPrefetchedLength(currentRow);
            } else {
                length = blob.length();
            }
            long length2 = length;
            if (length2 > 2147483647L) {
                throw vectorTooLarge(length2);
            }
            byte[] data = new byte[(int) length2];
            int prefetchedLength = isPrefetched() ? getPrefetchedDataLength(currentRow) : 0;
            if (prefetchedLength > 0) {
                this.rowData.get(getPrefetchedDataOffset(currentRow), data, 0, prefetchedLength);
            }
            blob.getBytes(prefetchedLength + 1, ((int) length2) - prefetchedLength, data);
            if (blobFree != null) {
                if (0 != 0) {
                    try {
                        blobFree.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    blobFree.close();
                }
            }
            return data;
        } catch (Throwable th3) {
            if (blobFree != null) {
                if (0 != 0) {
                    try {
                        blobFree.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    blobFree.close();
                }
            }
            throw th3;
        }
    }

    private static AutoCloseableAdapter<SQLException> autoFree(BLOB blob) throws SQLException {
        ThrowingRunnable<SQLException> throwingRunnable;
        byte[] locator = blob.shareBytes();
        if (PhysicalConnection.isTemporary(locator) && PhysicalConnection.isMemoryLocator(locator)) {
            blob.getClass();
            throwingRunnable = blob::free;
        } else {
            throwingRunnable = () -> {
            };
        }
        ThrowingRunnable<SQLException> free = throwingRunnable;
        return AutoCloseableAdapter.adapt(free);
    }

    private SQLException vectorTooLarge(long length) {
        return unrecognizedData("Vector length exceeds 2GB: " + length);
    }

    private static SQLException unrecognizedData(String message) {
        return DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 89, message);
    }
}
