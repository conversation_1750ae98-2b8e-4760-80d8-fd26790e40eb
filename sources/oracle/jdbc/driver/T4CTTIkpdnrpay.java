package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrpay.class */
class T4CTTIkpdnrpay {
    int payloadType;
    private int payloadFlag;
    private int chunkNumber;
    private byte[] rawPayload = null;
    byte[] toid = null;
    static final int KPDNRPAYRAW = 1;
    static final int KPDNRPAYADT = 2;
    static final int KPDNRPAYJSON = 3;
    T4CConnection connection;
    T4CMAREngine mar;
    T4Ctoh toh;

    T4CTTIkpdnrpay(T4CConnection _connection) {
        this.connection = null;
        this.toh = null;
        this.connection = _connection;
        this.mar = this.connection.mare;
        this.toh = new T4Ctoh(this.connection);
    }

    public void receive() throws SQLException, IOException {
        this.payloadType = (int) this.mar.unmarshalUB4();
        this.payloadFlag = (int) this.mar.unmarshalUB4();
        this.chunkNumber = (int) this.mar.unmarshalUB4();
        int rawPayloadLength = this.mar.unmarshalSWORD();
        if (rawPayloadLength > 0) {
            this.rawPayload = new byte[rawPayloadLength];
            int[] intAr = new int[1];
            this.mar.unmarshalCLR(this.rawPayload, 0, intAr, this.rawPayload.length);
            int i = intAr[0];
        }
        this.mar.unmarshalSWORD();
        if (this.payloadType > 1) {
            this.toh.unmarshal(this.mar);
            int lengthOfPayload = this.toh.imageLength;
            if (this.payloadType == 2) {
                if (lengthOfPayload > 0) {
                    byte[] image = new byte[lengthOfPayload];
                    this.mar.unmarshalCLR(image, 0, new int[1], lengthOfPayload);
                    this.rawPayload = image;
                } else {
                    this.rawPayload = null;
                }
                this.toid = new byte[16];
                for (int i2 = 0; i2 < 16; i2++) {
                    this.toid[i2] = this.toh.toid[i2 + 4];
                }
                return;
            }
            if (this.payloadType == 3) {
                if (lengthOfPayload > 0) {
                    int bufferToAllocate = lengthOfPayload;
                    if (lengthOfPayload > 4) {
                        bufferToAllocate -= 4;
                    }
                    byte[] image2 = new byte[bufferToAllocate];
                    int[] intAr2 = new int[1];
                    if (lengthOfPayload > 4) {
                        this.mar.unmarshalCLR(image2, 0, intAr2, image2.length, 4);
                    } else {
                        this.mar.unmarshalCLR(image2, 0, intAr2, image2.length);
                    }
                    this.rawPayload = image2;
                    return;
                }
                this.rawPayload = null;
            }
        }
    }

    public byte[] getRawPayload() {
        return this.rawPayload;
    }
}
