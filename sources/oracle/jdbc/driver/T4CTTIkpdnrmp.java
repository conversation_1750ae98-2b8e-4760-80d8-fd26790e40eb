package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.aq.AQFactory;
import oracle.jdbc.aq.AQMessageProperties;
import oracle.jdbc.internal.JMSFactory;
import oracle.jdbc.internal.JMSMessageProperties;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrmp.class */
class T4CTTIkpdnrmp {
    byte[] messageId;
    AQMessagePropertiesI aqMessageProperties;
    JMSMessagePropertiesI jmsMessageProperties;
    private T4CTTIaqm aqm;
    private T4CTTIaqjms aqjms;
    private T4Ctoh toh;
    T4CMAREngine mar;

    T4CTTIkpdnrmp(T4CConnection connection) {
        this.aqm = null;
        this.aqjms = null;
        this.toh = null;
        this.toh = new T4Ctoh(connection);
        this.aqm = new T4CTTIaqm(connection, this.toh);
        this.mar = connection.mare;
        this.aqjms = new T4CTTIaqjms(connection);
    }

    public void receive() throws SQLException, IOException {
        int messageIdLength = this.mar.unmarshalSWORD();
        if (messageIdLength > 0) {
            this.messageId = new byte[messageIdLength];
            int[] intAr = new int[1];
            this.mar.unmarshalCLR(this.messageId, 0, intAr, this.messageId.length);
            int i = intAr[0];
        }
        int aqMessagePropertiesLength = this.mar.unmarshalSWORD();
        if (aqMessagePropertiesLength > 0) {
            this.mar.unmarshalUB1();
            this.aqm.receive();
            this.aqMessageProperties = (AQMessagePropertiesI) AQFactory.createAQMessageProperties();
            this.aqMessageProperties.setPriority(this.aqm.aqmpri);
            this.aqMessageProperties.setDelay(this.aqm.aqmdel);
            this.aqMessageProperties.setExpiration(this.aqm.aqmexp);
            this.aqMessageProperties.setShardNum(this.aqm.aqmshardNum);
            if (this.aqm.aqmcorBytes != null) {
                String aqmcor = this.mar.conv.CharBytesToString(this.aqm.aqmcorBytes, this.aqm.aqmcorBytesLength, true);
                this.aqMessageProperties.setCorrelation(aqmcor);
            }
            this.aqMessageProperties.setAttempts(this.aqm.aqmatt);
            if (this.aqm.aqmeqnBytes != null) {
                String aqmeqn = this.mar.conv.CharBytesToString(this.aqm.aqmeqnBytes, this.aqm.aqmeqnBytesLength, true);
                this.aqMessageProperties.setExceptionQueue(aqmeqn);
            }
            this.aqMessageProperties.setMessageState(AQMessageProperties.MessageState.getMessageState(this.aqm.aqmsta));
            if (this.aqm.aqmeqt != null) {
                this.aqMessageProperties.setEnqueueTime(this.aqm.aqmeqt.timestampValue());
            }
            AQAgentI senderAgent = new AQAgentI();
            if (this.aqm.senderAgentName != null) {
                senderAgent.setName(this.mar.conv.CharBytesToString(this.aqm.senderAgentName, this.aqm.senderAgentNameLength, true));
            }
            if (this.aqm.senderAgentAddress != null) {
                senderAgent.setAddress(this.mar.conv.CharBytesToString(this.aqm.senderAgentAddress, this.aqm.senderAgentAddressLength, true));
            }
            senderAgent.setProtocol(this.aqm.senderAgentProtocol);
            this.aqMessageProperties.setSender(senderAgent);
            this.aqMessageProperties.setPreviousQueueMessageId(this.aqm.originalMsgId);
            this.aqMessageProperties.setDeliveryMode(AQMessageProperties.DeliveryMode.getDeliveryMode(this.aqm.aqmflg));
            if (this.aqm.aqmetiBytes != null) {
                String aqmeti = this.mar.conv.CharBytesToString(this.aqm.aqmetiBytes, this.aqm.aqmetiBytes.length, true);
                this.aqMessageProperties.setTransactionGroup(aqmeti);
            }
        }
        int jmsMessagePropertiesLength = this.mar.unmarshalSWORD();
        if (jmsMessagePropertiesLength > 0) {
            this.mar.unmarshalUB1();
            this.aqjms.receive();
            this.jmsMessageProperties = (JMSMessagePropertiesI) JMSFactory.createJMSMessageProperties();
            if (this.aqjms.aqjmshdrprop != null) {
                this.jmsMessageProperties.setHeaderProperties(this.mar.conv.CharBytesToString(this.aqjms.aqjmshdrprop, this.aqjms.aqjmshdrprop.length));
            }
            if (this.aqjms.aqjmsuserprop != null) {
                this.jmsMessageProperties.setUserProperties(this.mar.conv.CharBytesToString(this.aqjms.aqjmsuserprop, this.aqjms.aqjmsuserprop.length));
            }
            this.jmsMessageProperties.setJMSMessageType(JMSMessageProperties.JMSMessageType.getJMSMessageType(this.aqjms.aqjmsflags));
        }
    }

    public AQMessagePropertiesI getAqMessageProperties() {
        return this.aqMessageProperties;
    }

    public JMSMessagePropertiesI getJmsMessageProperties() {
        return this.jmsMessageProperties;
    }

    public byte[] getMessageId() {
        return this.messageId;
    }
}
