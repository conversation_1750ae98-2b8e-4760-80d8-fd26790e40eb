package oracle.jdbc.driver;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.sql.ClientInfoStatus;
import java.sql.SQLClientInfoException;
import java.sql.SQLDataException;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.sql.SQLInvalidAuthorizationSpecException;
import java.sql.SQLNonTransientConnectionException;
import java.sql.SQLNonTransientException;
import java.sql.SQLRecoverableException;
import java.sql.SQLSyntaxErrorException;
import java.sql.SQLTimeoutException;
import java.sql.SQLTransactionRollbackException;
import java.sql.SQLTransientConnectionException;
import java.sql.SQLTransientException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.ns.SQLnetDef;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/SQLStateMapping.class */
public class SQLStateMapping {
    private static final String CLASS_NAME = SQLStateMapping.class.getName();
    static final SQLStateMapping DEFAULT_SQLSTATE = new SQLStateMapping(SQLnetDef.NSPCNCON, Integer.MAX_VALUE, "99999", SqlExceptionType.SQLEXCEPTION);
    public final int low;
    final int high;
    public final String sqlState;
    public final SqlExceptionType exception;
    static final String mappingResource = "errorMap.xml";
    static SQLStateMapping[] all;
    private static final int NUMEBER_OF_MAPPINGS_IN_ERRORMAP_XML = 128;

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/SQLStateMapping$SqlExceptionType.class */
    public enum SqlExceptionType {
        SQLEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.1
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLException(reason, sqlState, vendorCode);
            }
        },
        SQLNONTRANSIENTEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.2
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLNonTransientException(reason, sqlState, vendorCode);
            }
        },
        SQLTRANSIENTEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.3
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLTransientException(reason, sqlState, vendorCode);
            }
        },
        SQLDATAEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.4
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLDataException(reason, sqlState, vendorCode);
            }
        },
        SQLFEATURENOTSUPPORTEDEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.5
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLFeatureNotSupportedException(reason, sqlState, vendorCode);
            }
        },
        SQLINTEGRITYCONSTRAINTVIOLATIONEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.6
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLIntegrityConstraintViolationException(reason, sqlState, vendorCode);
            }
        },
        SQLINVALIDAUTHORIZATIONSPECEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.7
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLInvalidAuthorizationSpecException(reason, sqlState, vendorCode);
            }
        },
        SQLNONTRANSIENTCONNECTIONEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.8
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLNonTransientConnectionException(reason, sqlState, vendorCode);
            }
        },
        SQLSYNTAXERROREXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.9
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLSyntaxErrorException(reason, sqlState, vendorCode);
            }
        },
        SQLTIMEOUTEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.10
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLTimeoutException(reason, sqlState, vendorCode);
            }
        },
        SQLTRANSACTIONROLLBACKEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.11
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLTransactionRollbackException(reason, sqlState, vendorCode);
            }
        },
        SQLTRANSIENTCONNECTIONEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.12
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLTransientConnectionException(reason, sqlState, vendorCode);
            }
        },
        SQLCLIENTINFOEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.13
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLClientInfoException(reason, sqlState, vendorCode, (Map<String, ClientInfoStatus>) null);
            }
        },
        SQLRECOVERABLEEXCEPTION { // from class: oracle.jdbc.driver.SQLStateMapping.SqlExceptionType.14
            @Override // oracle.jdbc.driver.SQLStateMapping.SqlExceptionType
            SQLException newInstance(String reason, String sqlState, int vendorCode) {
                return new SQLRecoverableException(reason, sqlState, vendorCode);
            }
        };

        abstract SQLException newInstance(String str, String str2, int i);
    }

    public SQLStateMapping(int l, int h, String s, SqlExceptionType ex) {
        this.low = l;
        this.sqlState = s;
        this.exception = ex;
        this.high = h;
    }

    public boolean isIncluded(int value) {
        return this.low <= value && value <= this.high;
    }

    public SQLException newSQLException(String reason, int vendorCode) {
        return this.exception.newInstance(reason, this.sqlState, vendorCode);
    }

    boolean lessThan(SQLStateMapping rhs) {
        return this.low < rhs.low ? this.high < rhs.high : this.high <= rhs.high;
    }

    public String toString() {
        return super.toString() + "(" + this.low + ", " + this.high + ", " + this.sqlState + ", " + this.exception + ")";
    }

    public static void main(String[] args) throws IOException, NumberFormatException {
        SQLStateMapping[] a = doGetMappings();
        System.out.println("a\t" + a);
        for (int i = 0; i < a.length; i++) {
            System.out.println("low:\t" + a[i].low + "\thigh:\t" + a[i].high + "\tsqlState:\t" + a[i].sqlState + "\tsqlException:\t" + a[i].exception);
        }
    }

    public static SQLStateMapping[] getMappings() {
        if (all == null) {
            try {
                all = doGetMappings();
            } catch (Throwable e) {
                CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "getMappings", "ORA Error number to SQLState code mapping resource not loaded correctly ", (String) null, e);
                all = new SQLStateMapping[0];
            }
        }
        return all;
    }

    static SQLStateMapping[] doGetMappings() throws IOException, NumberFormatException {
        InputStream s = SQLStateMapping.class.getResourceAsStream(mappingResource);
        ArrayList<SQLStateMapping> list = new ArrayList<>(128);
        load(s, list);
        return (SQLStateMapping[]) list.toArray(new SQLStateMapping[0]);
    }

    static void load(InputStream is, List<SQLStateMapping> list) throws IOException, NumberFormatException {
        Reader r = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
        Tokenizer st = new Tokenizer(r);
        int oraErrorFrom = -1;
        int oraErrorTo = -1;
        String sqlState = null;
        SqlExceptionType sqlException = null;
        String comment = null;
        int s = 0;
        while (true) {
            String token = st.next();
            if (token != null) {
                switch (s) {
                    case 0:
                        if (!token.equals("<")) {
                            break;
                        } else {
                            s = 1;
                            break;
                        }
                    case 1:
                        if (token.equals("!")) {
                            s = 2;
                            break;
                        } else {
                            if (!token.equals("oraErrorSqlStateSqlExceptionMapping")) {
                                throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"oraErrorSqlStateSqlExceptionMapping\".");
                            }
                            s = 6;
                            break;
                        }
                    case 2:
                        if (!token.equals("-")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"-\".");
                        }
                        s = 3;
                        break;
                    case 3:
                        if (!token.equals("-")) {
                            break;
                        } else {
                            s = 4;
                            break;
                        }
                    case 4:
                        if (!token.equals("-")) {
                            s = 3;
                            break;
                        } else {
                            s = 5;
                            break;
                        }
                    case 5:
                        if (!token.equals(">")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \">\".");
                        }
                        s = 0;
                        break;
                    case 6:
                        if (!token.equals(">")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \">\".");
                        }
                        s = 7;
                        break;
                    case 7:
                        if (!token.equals("<")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"<\".");
                        }
                        s = 8;
                        break;
                    case 8:
                        if (token.equals("!")) {
                            s = 9;
                            break;
                        } else if (token.equals("error")) {
                            s = 14;
                            break;
                        } else {
                            if (!token.equals("/")) {
                                throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected one of \"!--\", \"error\", \"/\".");
                            }
                            s = 16;
                            break;
                        }
                    case 9:
                        if (!token.equals("-")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"-\".");
                        }
                        s = 10;
                        break;
                    case 10:
                        if (!token.equals("-")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"-\".");
                        }
                        s = 11;
                        break;
                    case 11:
                        if (!token.equals("-")) {
                            break;
                        } else {
                            s = 12;
                            break;
                        }
                    case 12:
                        if (!token.equals("-")) {
                            s = 11;
                            break;
                        } else {
                            s = 13;
                            break;
                        }
                    case 13:
                        if (!token.equals(">")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \">\".");
                        }
                        s = 7;
                        break;
                    case 14:
                        if (token.equals("/")) {
                            s = 15;
                            break;
                        } else if (token.equals("oraErrorFrom")) {
                            s = 19;
                            break;
                        } else if (token.equals("oraErrorTo")) {
                            s = 21;
                            break;
                        } else if (token.equals("sqlState")) {
                            s = 23;
                            break;
                        } else if (token.equals("sqlException")) {
                            s = 25;
                            break;
                        } else {
                            if (!token.equals("comment")) {
                                throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected one of \"oraErrorFrom\", \"oraErrorTo\", \"sqlState\", \"sqlException\", \"comment\", \"/\".");
                            }
                            s = 27;
                            break;
                        }
                    case 15:
                        if (token.equals(">")) {
                            try {
                                createOne(list, oraErrorFrom, oraErrorTo, sqlState, sqlException, comment);
                                oraErrorFrom = -1;
                                oraErrorTo = -1;
                                sqlState = null;
                                sqlException = null;
                                comment = null;
                                s = 7;
                                break;
                            } catch (IOException e) {
                                throw new IOException("Invalid error element at line " + st.lineno + " of errorMap.xml. " + e.getMessage());
                            }
                        } else {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \">\".");
                        }
                    case 16:
                        if (!token.equals("oraErrorSqlStateSqlExceptionMapping")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"oraErrorSqlStateSqlExceptionMapping\".");
                        }
                        s = 17;
                        break;
                    case 17:
                        if (!token.equals(">")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \">\".");
                        }
                        s = 18;
                        break;
                    case 18:
                        break;
                    case 19:
                        if (!token.equals("=")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"=\".");
                        }
                        s = 20;
                        break;
                    case 20:
                        try {
                            oraErrorFrom = Integer.parseInt(token);
                            s = 14;
                            break;
                        } catch (NumberFormatException e2) {
                            throw new IOException("Unexpected value \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected an integer.");
                        }
                    case 21:
                        if (!token.equals("=")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"=\".");
                        }
                        s = 22;
                        break;
                    case 22:
                        try {
                            oraErrorTo = Integer.parseInt(token);
                            s = 14;
                            break;
                        } catch (NumberFormatException e3) {
                            throw new IOException("Unexpected value \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected an integer.");
                        }
                    case 23:
                        if (!token.equals("=")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"=\".");
                        }
                        s = 24;
                        break;
                    case 24:
                        sqlState = token;
                        s = 14;
                        break;
                    case 25:
                        if (!token.equals("=")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"=\".");
                        }
                        s = 26;
                        break;
                    case 26:
                        try {
                            sqlException = SqlExceptionType.valueOf(token);
                            s = 14;
                            break;
                        } catch (Exception e4) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected SQLException subclass name.");
                        }
                    case 27:
                        if (!token.equals("=")) {
                            throw new IOException("Unexpected token \"" + token + "\" at line " + st.lineno + " of errorMap.xml. Expected \"=\".");
                        }
                        s = 28;
                        break;
                    case 28:
                        comment = token;
                        s = 14;
                        break;
                    default:
                        throw new IOException("Unknown parser state " + s + " at line " + st.lineno + " of errorMap.xml.");
                }
            } else {
                return;
            }
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/SQLStateMapping$Tokenizer.class */
    private static final class Tokenizer {
        int lineno = 1;
        Reader r;
        int c;

        Tokenizer(Reader r) throws IOException {
            this.r = r;
            this.c = r.read();
        }

        /* JADX WARN: Removed duplicated region for block: B:57:0x0147  */
        /* JADX WARN: Removed duplicated region for block: B:59:0x014c A[RETURN] */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        java.lang.String next() throws java.io.IOException {
            /*
                Method dump skipped, instructions count: 334
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.SQLStateMapping.Tokenizer.next():java.lang.String");
        }
    }

    private static void createOne(List<SQLStateMapping> list, int oraErrorFrom, int oraErrorTo, String sqlState, SqlExceptionType sqlException, String comment) throws IOException {
        if (oraErrorFrom == -1) {
            throw new IOException("oraErrorFrom is a required attribute");
        }
        if (oraErrorTo == -1) {
            oraErrorTo = oraErrorFrom;
        }
        if (sqlState == null || sqlState.length() == 0) {
            throw new IOException("sqlState is a required attribute");
        }
        if (sqlException == null) {
            throw new IOException("sqlException is a required attribute");
        }
        if (comment == null || comment.length() < 8) {
            throw new IOException("a lengthy comment in required");
        }
        SQLStateMapping m = new SQLStateMapping(oraErrorFrom, oraErrorTo, sqlState, sqlException);
        add(list, m);
    }

    static void add(List<SQLStateMapping> l, SQLStateMapping a) {
        int i = l.size();
        while (i > 0 && !l.get(i - 1).lessThan(a)) {
            i--;
        }
        l.add(i, a);
    }
}
