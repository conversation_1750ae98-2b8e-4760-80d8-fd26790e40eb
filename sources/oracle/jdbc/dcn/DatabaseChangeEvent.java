package oracle.jdbc.dcn;

import java.util.EventObject;

/* loaded from: ojdbc8.jar:oracle/jdbc/dcn/DatabaseChangeEvent.class */
public abstract class DatabaseChangeEvent extends EventObject {
    public abstract EventType getEventType();

    public abstract AdditionalEventType getAdditionalEventType();

    public abstract TableChangeDescription[] getTableChangeDescription();

    public abstract QueryChangeDescription[] getQueryChangeDescription();

    public abstract String getConnectionInformation();

    public abstract String getDatabaseName();

    public abstract int getRegistrationId();

    public abstract long getRegId();

    public abstract byte[] getTransactionId();

    public abstract String getTransactionId(boolean z);

    @Override // java.util.EventObject
    public abstract String toString();

    protected DatabaseChangeEvent(Object source) {
        super(source);
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/dcn/DatabaseChangeEvent$EventType.class */
    public enum EventType {
        NONE(0),
        STARTUP(1),
        SHUTDOWN(2),
        SHUTDOWN_ANY(3),
        DEREG(5),
        OBJCHANGE(6),
        QUERYCHANGE(7);

        private final int code;

        EventType(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }

        public static final EventType getEventType(int code) {
            if (code == STARTUP.getCode()) {
                return STARTUP;
            }
            if (code == SHUTDOWN.getCode()) {
                return SHUTDOWN;
            }
            if (code == SHUTDOWN_ANY.getCode()) {
                return SHUTDOWN_ANY;
            }
            if (code == DEREG.getCode()) {
                return DEREG;
            }
            if (code == OBJCHANGE.getCode()) {
                return OBJCHANGE;
            }
            if (code == QUERYCHANGE.getCode()) {
                return QUERYCHANGE;
            }
            return NONE;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/dcn/DatabaseChangeEvent$AdditionalEventType.class */
    public enum AdditionalEventType {
        NONE(0),
        TIMEOUT(1),
        GROUPING(2);

        private final int code;

        AdditionalEventType(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }

        public static final AdditionalEventType getEventType(int code) {
            if (code == TIMEOUT.getCode()) {
                return TIMEOUT;
            }
            if (code == GROUPING.getCode()) {
                return GROUPING;
            }
            return NONE;
        }
    }
}
