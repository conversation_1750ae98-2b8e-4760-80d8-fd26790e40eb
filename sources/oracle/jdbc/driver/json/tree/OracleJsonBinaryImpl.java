package oracle.jdbc.driver.json.tree;

import java.util.Arrays;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.RAW;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonBinaryImpl.class */
public class OracleJsonBinaryImpl implements OracleJsonBinary {
    protected static final char[] HEX_UPPER = "0123456789ABCDEF".toCharArray();
    protected static final char[] HEX_LOWER = "0123456789abcdef".toCharArray();
    private byte[] bytes;
    boolean isId;

    public OracleJsonBinaryImpl(byte[] bytes, boolean isId) {
        this.bytes = bytes;
        this.isId = isId;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.BINARY;
    }

    @Override // oracle.sql.json.OracleJsonBinary
    public boolean isId() {
        return this.isId;
    }

    @Override // oracle.sql.json.OracleJsonBinary
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonBinary)) {
            return false;
        }
        OracleJsonBinary b = (OracleJsonBinary) other;
        byte[] bytes1 = getBytes();
        byte[] bytes2 = b.getBytes();
        return Arrays.equals(bytes1, bytes2);
    }

    @Override // oracle.sql.json.OracleJsonBinary
    public int hashCode() {
        return Arrays.hashCode(getBytes());
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        return JsonSerializerImpl.serializeString(getString());
    }

    @Override // oracle.sql.json.OracleJsonBinary
    public byte[] getBytes() {
        return this.bytes;
    }

    @Override // oracle.sql.json.OracleJsonBinary
    public String getString() {
        return getString(this.bytes, this.isId);
    }

    public static String getString(byte[] bytes, boolean isId) {
        return new String(serializeBinary(bytes, isId ? HEX_LOWER : HEX_UPPER));
    }

    @Override // oracle.sql.json.OracleJsonBinary
    public RAW getRAW() {
        return new RAW(getBytes());
    }

    private static char[] serializeBinary(byte[] bytes, char[] hex) {
        char[] chars = new char[bytes.length * 2];
        int i = 0;
        int j = 0;
        while (i < bytes.length) {
            int i2 = i;
            i++;
            byte b = bytes[i2];
            int i3 = j;
            int j2 = j + 1;
            chars[i3] = hex[(b >> 4) & 15];
            j = j2 + 1;
            chars[j2] = hex[b & 15];
        }
        return chars;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaBinaryImpl(this.bytes, this.isId));
        }
        return c.cast(new JsonpPrimitive.JsonpBinaryImpl(this.bytes, this.isId));
    }
}
