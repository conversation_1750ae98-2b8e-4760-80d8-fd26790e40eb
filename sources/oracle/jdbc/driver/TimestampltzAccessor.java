package oracle.jdbc.driver;

import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Map;
import java.util.TimeZone;
import oracle.sql.CharacterSet;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TimestampltzAccessor.class */
class TimestampltzAccessor extends DateTimeCommonAccessor {
    static final int MAXLENGTH = 11;

    TimestampltzAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        super(Representation.TIMESTAMPLTZ, stmt, 11, isStoredInBindData);
        init(stmt, CharacterSet.WE8BS2000_CHARSET, CharacterSet.WE8BS2000_CHARSET, form, isOutBind);
        initForDataAccess(external_type, max_len, null);
    }

    TimestampltzAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(Representation.TIMESTAMPLTZ, stmt, 11, false);
        init(stmt, CharacterSet.WE8BS2000_CHARSET, CharacterSet.WE8BS2000_CHARSET, form, false);
        initForDescribe(CharacterSet.WE8BS2000_CHARSET, max_len, nullable, flags, precision, scale, contflag, total_elems, form, null);
        initForDataAccess(0, max_len, null);
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        Calendar dbTzCal = this.statement.connection.getDbTzCalendar();
        String sessTzStr = this.statement.connection.getSessionTimeZone();
        if (sessTzStr == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 198).fillInStackTrace());
        }
        TimeZone zone = TimeZone.getTimeZone(sessTzStr);
        Calendar sessTzCal = Calendar.getInstance(zone);
        getBytesInternal(currentRow, this.tmpBytes);
        int year = oracleYear(this.tmpBytes);
        dbTzCal.set(1, year);
        dbTzCal.set(2, oracleMonth(this.tmpBytes));
        dbTzCal.set(5, oracleDay(this.tmpBytes));
        dbTzCal.set(11, oracleHour(this.tmpBytes));
        dbTzCal.set(12, oracleMin(this.tmpBytes));
        dbTzCal.set(13, oracleSec(this.tmpBytes));
        dbTzCal.set(14, 0);
        TIMESTAMPLTZ.TimeZoneAdjust(this.statement.connection, dbTzCal, sessTzCal);
        int year2 = sessTzCal.get(1);
        int month = sessTzCal.get(2) + 1;
        int date = sessTzCal.get(5);
        int hour = sessTzCal.get(11);
        int minute = sessTzCal.get(12);
        int second = sessTzCal.get(13);
        int nanos = 0;
        boolean am = hour < 12;
        String regname = sessTzCal.getTimeZone().getID();
        if (regname.length() > 3 && regname.startsWith("GMT")) {
            regname = regname.substring(3);
        }
        if (getLength(currentRow) == 11) {
            nanos = oracleNanos(this.tmpBytes);
        }
        return toText(year2, month, date, hour, minute, second, nanos, am, regname);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Date getDate(int currentRow, Calendar cal) throws SQLException {
        return getDate(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Date getDate(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        Calendar dbTzCal = this.statement.connection.getDbTzCalendar();
        String sessTzStr = this.statement.connection.getSessionTimeZone();
        if (sessTzStr == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 198).fillInStackTrace());
        }
        getBytesInternal(currentRow, this.tmpBytes);
        int year = oracleYear(this.tmpBytes);
        dbTzCal.set(1, year);
        dbTzCal.set(2, oracleMonth(this.tmpBytes));
        dbTzCal.set(5, oracleDay(this.tmpBytes));
        dbTzCal.set(11, oracleHour(this.tmpBytes));
        dbTzCal.set(12, oracleMin(this.tmpBytes));
        dbTzCal.set(13, oracleSec(this.tmpBytes));
        dbTzCal.set(14, 0);
        long millis = TIMESTAMPLTZ.TimeZoneAdjustUTC(this.statement.connection, dbTzCal);
        return new Date(millis);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Time getTime(int currentRow, Calendar cal) throws SQLException {
        return getTime(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Time getTime(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        Calendar dbTzCal = this.statement.connection.getDbTzCalendar();
        String sessTzStr = this.statement.connection.getSessionTimeZone();
        if (sessTzStr == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 198).fillInStackTrace());
        }
        getBytesInternal(currentRow, this.tmpBytes);
        int year = oracleYear(this.tmpBytes);
        dbTzCal.set(1, year);
        dbTzCal.set(2, oracleMonth(this.tmpBytes));
        dbTzCal.set(5, oracleDay(this.tmpBytes));
        dbTzCal.set(11, oracleHour(this.tmpBytes));
        dbTzCal.set(12, oracleMin(this.tmpBytes));
        dbTzCal.set(13, oracleSec(this.tmpBytes));
        dbTzCal.set(14, 0);
        long millis = TIMESTAMPLTZ.TimeZoneAdjustUTC(this.statement.connection, dbTzCal);
        return new Time(millis);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Timestamp getTimestamp(int currentRow, Calendar cal) throws SQLException {
        return getTimestamp(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Timestamp getTimestamp(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        Calendar dbTzCal = this.statement.connection.getDbTzCalendar();
        String sessTzStr = this.statement.connection.getSessionTimeZone();
        if (sessTzStr == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 198).fillInStackTrace());
        }
        getBytesInternal(currentRow, this.tmpBytes);
        int year = oracleYear(this.tmpBytes);
        dbTzCal.set(1, year);
        dbTzCal.set(2, oracleMonth(this.tmpBytes));
        dbTzCal.set(5, oracleDay(this.tmpBytes));
        dbTzCal.set(11, oracleHour(this.tmpBytes));
        dbTzCal.set(12, oracleMin(this.tmpBytes));
        dbTzCal.set(13, oracleSec(this.tmpBytes));
        dbTzCal.set(14, 0);
        long millis = TIMESTAMPLTZ.TimeZoneAdjustUTC(this.statement.connection, dbTzCal);
        Timestamp result = new Timestamp(millis);
        if (getLength(currentRow) == 11) {
            result.setNanos(oracleNanos(this.tmpBytes));
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        return getTIMESTAMPLTZ(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Datum getOracleObject(int currentRow) throws SQLException {
        return getTIMESTAMPLTZ(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow, Map<String, Class<?>> map) throws SQLException {
        return getTIMESTAMPLTZ(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMPLTZ getTIMESTAMPLTZ(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        return new TIMESTAMPLTZ(getBytesInternal(currentRow));
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMPTZ getTIMESTAMPTZ(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        return TIMESTAMPLTZ.toTIMESTAMPTZ(this.statement.connection, getBytesInternal(currentRow));
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMP getTIMESTAMP(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        TIMESTAMPTZ tmtz = getTIMESTAMPTZ(currentRow);
        return TIMESTAMPTZ.toTIMESTAMP(this.statement.connection, tmtz.getBytes());
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    DATE getDATE(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        TIMESTAMPTZ tmtz = getTIMESTAMPTZ(currentRow);
        return TIMESTAMPTZ.toDATE(this.statement.connection, tmtz.getBytes());
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Calendar getCalendar(int currentRow) throws SQLException {
        return (Calendar) JavaToJavaConverter.convert(new TIMESTAMPLTZ(getBytesInternal(currentRow)), Calendar.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalDate getLocalDate(int currentRow) throws SQLException {
        return (LocalDate) JavaToJavaConverter.convert(new TIMESTAMPLTZ(getBytesInternal(currentRow)), LocalDate.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalDateTime getLocalDateTime(int currentRow) throws SQLException {
        return (LocalDateTime) JavaToJavaConverter.convert(new TIMESTAMPLTZ(getBytesInternal(currentRow)), LocalDateTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalTime getLocalTime(int currentRow) throws SQLException {
        return (LocalTime) JavaToJavaConverter.convert(new TIMESTAMPLTZ(getBytesInternal(currentRow)), LocalTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    OffsetDateTime getOffsetDateTime(int currentRow) throws SQLException {
        return (OffsetDateTime) JavaToJavaConverter.convert(new TIMESTAMPLTZ(getBytesInternal(currentRow)), OffsetDateTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    OffsetTime getOffsetTime(int currentRow) throws SQLException {
        return (OffsetTime) JavaToJavaConverter.convert(new TIMESTAMPLTZ(getBytesInternal(currentRow)), OffsetTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    ZonedDateTime getZonedDateTime(int currentRow) throws SQLException {
        return (ZonedDateTime) JavaToJavaConverter.convert(new TIMESTAMPLTZ(getBytesInternal(currentRow)), ZonedDateTime.class, this.statement.connection, null, null);
    }
}
