package oracle.jdbc.connector;

import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Enumeration;
import java.util.Hashtable;
import javax.resource.ResourceException;
import javax.resource.spi.ConnectionEvent;
import javax.resource.spi.ConnectionEventListener;
import javax.resource.spi.ConnectionRequestInfo;
import javax.resource.spi.EISSystemException;
import javax.resource.spi.IllegalStateException;
import javax.resource.spi.LocalTransaction;
import javax.resource.spi.ManagedConnection;
import javax.resource.spi.ManagedConnectionMetaData;
import javax.resource.spi.security.PasswordCredential;
import javax.security.auth.Subject;
import javax.sql.XAConnection;
import javax.transaction.xa.XAResource;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.xa.OracleXAConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/connector/OracleManagedConnection.class */
public class OracleManagedConnection implements ManagedConnection {
    private OracleXAConnection xaConnection;
    private Hashtable connectionListeners;
    private Connection connection = null;
    private PrintWriter logWriter = null;
    private PasswordCredential passwordCredential = null;
    private OracleLocalTransaction localTxn = null;

    OracleManagedConnection(XAConnection xaconn) {
        this.xaConnection = null;
        this.connectionListeners = null;
        this.xaConnection = (OracleXAConnection) xaconn;
        this.connectionListeners = new Hashtable(10);
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    public Object getConnection(Subject subject, ConnectionRequestInfo cxRequestInfo) throws SQLException, ResourceException, EISSystemException {
        try {
            if (this.connection != null) {
                this.connection.close();
            }
            this.connection = this.xaConnection.getConnection();
            return this.connection;
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.IllegalStateException */
    public void destroy() throws SQLException, ResourceException, IllegalStateException, EISSystemException {
        try {
            if (this.xaConnection != null) {
                OracleConnection _pconn = (OracleConnection) this.xaConnection.getPhysicalHandle();
                if ((this.localTxn != null && this.localTxn.isBeginCalled) || _pconn.getTxnMode() == 1 || _pconn.inSessionlessTxnMode()) {
                    throw new IllegalStateException("Could not close connection while transaction is active");
                }
            }
            if (this.connection != null) {
                this.connection.close();
            }
            if (this.xaConnection != null) {
                this.xaConnection.close();
            }
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.IllegalStateException */
    public void cleanup() throws SQLException, ResourceException, IllegalStateException, EISSystemException {
        try {
            if (this.connection != null) {
                OracleConnection _pconn = (OracleConnection) this.connection;
                if ((this.localTxn != null && this.localTxn.isBeginCalled) || _pconn.getTxnMode() == 1 || _pconn.inSessionlessTxnMode()) {
                    throw new IllegalStateException("Could not close connection while transaction is active");
                }
                this.connection.close();
            }
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    public void associateConnection(Object connection) {
    }

    public void addConnectionEventListener(ConnectionEventListener listener) {
        this.connectionListeners.put(listener, listener);
    }

    public void removeConnectionEventListener(ConnectionEventListener listener) {
        this.connectionListeners.remove(listener);
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.ResourceException */
    public XAResource getXAResource() throws ResourceException {
        try {
            return this.xaConnection.getXAResource();
        } catch (SQLException e) {
            throw new ResourceException(e);
        }
    }

    public LocalTransaction getLocalTransaction() throws ResourceException {
        if (this.localTxn == null) {
            this.localTxn = new OracleLocalTransaction(this);
        }
        return this.localTxn;
    }

    public ManagedConnectionMetaData getMetaData() throws ResourceException {
        return new OracleManagedConnectionMetaData(this);
    }

    public void setLogWriter(PrintWriter out) throws ResourceException {
        this.logWriter = out;
    }

    public PrintWriter getLogWriter() throws ResourceException {
        return this.logWriter;
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    Connection getPhysicalConnection() throws ResourceException, EISSystemException {
        try {
            return this.xaConnection.getPhysicalHandle();
        } catch (Exception exc) {
            throw new EISSystemException("Exception: " + exc.getMessage(), exc);
        }
    }

    void setPasswordCredential(PasswordCredential pcred) {
        this.passwordCredential = pcred;
    }

    PasswordCredential getPasswordCredential() {
        return this.passwordCredential;
    }

    void eventOccurred(int eventType) throws ResourceException {
        Enumeration allListeners = this.connectionListeners.keys();
        while (allListeners.hasMoreElements()) {
            ConnectionEventListener listener = (ConnectionEventListener) allListeners.nextElement();
            ConnectionEvent ce = new ConnectionEvent(this, eventType);
            switch (eventType) {
                case 1:
                    listener.connectionClosed(ce);
                    break;
                case 2:
                    listener.localTransactionStarted(ce);
                    break;
                case 3:
                    listener.localTransactionCommitted(ce);
                    break;
                case 4:
                    listener.localTransactionRolledback(ce);
                    break;
                case 5:
                    listener.connectionErrorOccurred(ce);
                    break;
                default:
                    throw new IllegalArgumentException("Illegal eventType in eventOccurred(): " + eventType);
            }
        }
    }
}
