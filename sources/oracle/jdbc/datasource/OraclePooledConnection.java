package oracle.jdbc.datasource;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import javax.sql.ConnectionEventListener;
import javax.sql.PooledConnection;
import javax.transaction.xa.XAResource;
import oracle.jdbc.OracleShardingKey;
import oracle.jdbc.driver.OracleCloseCallback;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/OraclePooledConnection.class */
public interface OraclePooledConnection extends PooledConnection {
    Connection getLogicalHandle() throws SQLException;

    @Deprecated
    void setLastAccessedTime(long j) throws SQLException;

    @Deprecated
    long getLastAccessedTime() throws SQLException;

    void registerCloseCallback(OracleCloseCallback oracleCloseCallback, Object obj);

    @Deprecated
    void registerImplicitCacheConnectionEventListener(ConnectionEventListener connectionEventListener);

    void setStatementCacheSize(int i) throws SQLException;

    int getStatementCacheSize() throws SQLException;

    void setImplicitCachingEnabled(boolean z) throws SQLException;

    boolean getImplicitCachingEnabled() throws SQLException;

    void setExplicitCachingEnabled(boolean z) throws SQLException;

    boolean getExplicitCachingEnabled() throws SQLException;

    void purgeImplicitCache() throws SQLException;

    void purgeExplicitCache() throws SQLException;

    PreparedStatement getStatementWithKey(String str) throws SQLException;

    CallableStatement getCallWithKey(String str) throws SQLException;

    XAResource getXAResource() throws SQLException;

    boolean setShardingKeyIfValid(OracleShardingKey oracleShardingKey, OracleShardingKey oracleShardingKey2, int i) throws SQLException;

    void setShardingKey(OracleShardingKey oracleShardingKey, OracleShardingKey oracleShardingKey2) throws SQLException;
}
