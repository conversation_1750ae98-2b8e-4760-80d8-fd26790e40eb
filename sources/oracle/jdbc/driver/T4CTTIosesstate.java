package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIosesstate.class */
final class T4CTTIosesstate extends T4CTTIfun {
    private long flags_kpdssSessionStateOps;

    T4CTTIosesstate(T4CConnection _conn) {
        super(_conn, (byte) 17);
        setFunCode((short) 176);
    }

    void doOSESSSTATE(long flags_kpdssSessionStateOps, boolean isOneWay) throws SQLException, IOException {
        this.flags_kpdssSessionStateOps = flags_kpdssSessionStateOps;
        if (isOneWay) {
            setTTCCode((byte) 26);
            doOneWayRPC();
        } else {
            setTTCCode((byte) 17);
            doPigRPC();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalSB8(this.flags_kpdssSessionStateOps);
    }
}
