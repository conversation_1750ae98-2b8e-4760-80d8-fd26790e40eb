package oracle.jdbc.diagnostics;

import java.util.Iterator;
import java.util.logging.Level;
import java.util.logging.LogRecord;
import java.util.logging.SimpleFormatter;
import oracle.jdbc.diagnostics.TraceAttributes;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/OracleSimpleFormatter.class */
public class OracleSimpleFormatter extends SimpleFormatter {
    @Override // java.util.logging.Formatter
    public String formatMessage(LogRecord record) {
        if (record instanceof OracleLogRecord) {
            OracleLogRecord rec = (OracleLogRecord) record;
            TraceAttributes attributes = rec.getTraceAttributes();
            StringBuilder result = new StringBuilder();
            result.append(rec.getSecurityLabel().getLabel()).append(":").append("thread-").append(rec.getThreadID()).append(" ").append(rec.getThreadName()).append(" ");
            if (attributes != null) {
                Iterator<TraceAttributes.Entry> it = attributes.iterator();
                while (it.hasNext()) {
                    TraceAttributes.Entry e = it.next();
                    if (e.value != null && (e.key != OracleTraceKey.SQL || rec.getLevel().intValue() < Level.INFO.intValue())) {
                        result.append(e.key).append("=").append(e.value).append(",");
                    }
                }
            }
            result.append(super.formatMessage(record));
            return result.toString();
        }
        return super.formatMessage(record);
    }
}
