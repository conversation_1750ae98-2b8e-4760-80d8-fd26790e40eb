package oracle.jdbc.driver;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.logging.Level;
import javax.transaction.xa.XAException;
import javax.transaction.xa.XAResource;
import oracle.jdbc.babelfish.BabelfishConnection;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.proxy._Proxy_;
import oracle.jdbc.xa.OracleXAResource;
import oracle.jdbc.xa.client.OracleXAConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CXAConnection.class */
public class T4CXAConnection extends OracleXAConnection {
    private final String CLASS_NAME;
    public static final long serialVersionUID = 1;

    public T4CXAConnection(Connection _physicalConnection) throws XAException {
        super(_physicalConnection);
        this.CLASS_NAME = T4CXAConnection.class.getName();
        this.xaResource = null;
    }

    @Override // oracle.jdbc.xa.client.OracleXAConnection, oracle.jdbc.xa.OracleXAConnection, oracle.jdbc.pool.OraclePooledConnection, oracle.jdbc.datasource.OraclePooledConnection
    public XAResource getXAResource() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.xaResource == null) {
                    oracle.jdbc.internal.OracleConnection physicalConnection = this.physicalConn instanceof BabelfishConnection ? (T4CConnection) ((_Proxy_) this.physicalConn)._getDelegate_() : this.physicalConn;
                    this.xaResource = new T4CXAResource(physicalConnection, this, this.isXAResourceTransLoose);
                    if (this.logicalHandle != null) {
                        ((OracleXAResource) this.xaResource).setLogicalConnection(this.logicalHandle);
                    }
                }
                XAResource xAResource = this.xaResource;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return xAResource;
            } catch (XAException xae) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, this.CLASS_NAME, "getXAResource", (String) null, (String) null, xae);
                this.xaResource = null;
                if (xae.getCause() instanceof SQLException) {
                    throw ((SQLException) xae.getCause());
                }
                throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), (Exception) xae);
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }
}
