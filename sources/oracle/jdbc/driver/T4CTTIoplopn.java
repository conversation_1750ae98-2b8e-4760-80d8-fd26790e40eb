package oracle.jdbc.driver;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoplopn.class */
final class T4CTTIoplopn extends T4CTTIfun {
    private int errorSetId;
    private short errorSetMode;
    private short operationMode;

    T4CTTIoplopn(T4CConnection t4cConnection) {
        super(t4cConnection, (byte) 17);
        setFunCode((short) 203);
    }

    void doOPLOPN(int errorSetId, short errorSetMode, short operationMode) throws IOException {
        this.errorSetId = errorSetId;
        this.errorSetMode = errorSetMode;
        this.operationMode = operationMode;
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB2(this.errorSetId);
        this.meg.marshalUB1(this.errorSetMode);
        this.meg.marshalUB1(this.operationMode);
    }
}
