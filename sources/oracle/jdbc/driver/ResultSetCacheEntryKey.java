package oracle.jdbc.driver;

import java.util.Arrays;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ResultSetCacheEntryKey.class */
final class ResultSetCacheEntryKey {
    private final byte[] key = new byte[32];

    ResultSetCacheEntryKey(byte[] compileKey, byte[] runtimeKey) {
        System.arraycopy(compileKey, 0, this.key, 0, 16);
        System.arraycopy(runtimeKey, 0, this.key, 16, 16);
    }

    public boolean equals(Object other) {
        if (!(other instanceof ResultSetCacheEntryKey)) {
            return false;
        }
        return Arrays.equals(this.key, ((ResultSetCacheEntryKey) other).key);
    }

    public int hashCode() {
        return Arrays.hashCode(this.key);
    }
}
