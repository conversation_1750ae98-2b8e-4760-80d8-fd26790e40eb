package oracle.jdbc.driver;

import java.sql.SQLException;
import oracle.sql.Datum;
import oracle.sql.NUMBER;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ShortBinder.class */
class ShortBinder extends VarnumBinder {
    int paramVal;

    ShortBinder(int x) {
        this.paramVal = x;
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        byte[] b;
        int offset;
        int i = byteoffset + 1;
        int val = this.paramVal;
        if (bindUseDBA) {
            long pos = bindData.getPosition();
            bindDataOffsets[bindDataIndex] = pos;
            stmt.lastBoundDataOffsets[bindPosition] = pos;
            b = stmt.connection.methodTempLittleByteBuffer;
            offset = 0;
        } else {
            b = bindBytes;
            offset = byteoffset + 1;
        }
        int len = NUMBER.toBytes(val, b, offset);
        if (bindUseDBA) {
            bindData.put(b, 0, len);
            bindIndicators[indoffset] = 0;
            bindDataLengths[bindDataIndex] = len;
            stmt.lastBoundDataLengths[bindPosition] = len;
        } else {
            b[byteoffset] = (byte) len;
            bindIndicators[indoffset] = 0;
        }
        bindIndicators[lenoffset] = (short) (len + 1);
        return localCheckSum;
    }

    @Override // oracle.jdbc.driver.Binder
    Datum getDatum(OraclePreparedStatement stmt, int bindPosition, int formOfUse, int internalType) throws SQLException {
        return SQLUtil.makeDatum(stmt.connection, NUMBER.toBytes(this.paramVal), internalType, (String) null, 0);
    }
}
