package oracle.jdbc.driver.resource;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import oracle.jdbc.driver.resource.ResourceProvider;
import oracle.jdbc.spi.OracleResourceProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/DriverResources.class */
public class DriverResources {
    private final Map<ResourceType<?, ?>, ResourceProvider<?, ?>> providers;

    public DriverResources(ProviderProperties providerProperties) throws SQLException {
        Collection<ResourceType<?, ?>> resourceTypes = ResourceType.allTypes();
        Map<ResourceType<?, ?>, ResourceProvider<?, ?>> providers = new HashMap<>(resourceTypes.size());
        for (ResourceType<?, ?> resourceType : resourceTypes) {
            ResourceProvider<?, ?> provider = createProvider(resourceType, providerProperties);
            providers.put(resourceType, provider);
        }
        this.providers = Collections.unmodifiableMap(providers);
    }

    public boolean isProviderConfigured(ResourceType<?, ?> resourceType) {
        return null != this.providers.get(resourceType);
    }

    public <U> U getResource(ResourceType<?, U> resourceType) throws SQLException {
        ResourceProvider<?, ?> resourceProvider = this.providers.get(resourceType);
        if (resourceProvider == null) {
            return null;
        }
        return (U) resourceProvider.getResource();
    }

    private static <T extends OracleResourceProvider, U> ResourceProvider<T, U> createProvider(ResourceType<T, U> resourceType, ProviderProperties providerProperties) {
        String providerName = providerProperties.getProviderName(resourceType);
        if (providerName == null) {
            return null;
        }
        try {
            ResourceProvider.Builder<T, U> builder = DriverResourceProvider.builder(providerName, (ResourceType) resourceType);
            Map<String, String> parameterValues = providerProperties.getParameterValues(resourceType);
            for (Map.Entry<String, String> entry : parameterValues.entrySet()) {
                builder.parameterValue(entry.getKey(), entry.getValue());
            }
            return builder.build();
        } catch (SQLException sqlException) {
            return new InvalidProvider(sqlException);
        }
    }
}
