package oracle.jdbc.driver.json;

import jakarta.json.JsonArray;
import jakarta.json.JsonException;
import jakarta.json.JsonNumber;
import jakarta.json.JsonObject;
import jakarta.json.JsonString;
import jakarta.json.JsonValue;
import jakarta.json.stream.JsonGenerationException;
import jakarta.json.stream.JsonGenerator;
import jakarta.json.stream.JsonParser;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.SQLException;
import java.sql.Wrapper;
import java.util.Map;
import oracle.jdbc.driver.json.tree.OracleJsonDecimalImpl;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonGenerationException;
import oracle.sql.json.OracleJsonGenerator;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/JakartaGeneratorWrapper.class */
public class JakartaGeneratorWrapper implements Wrapper, JsonGenerator {
    OracleJsonGenerator wrapped;

    public JakartaGeneratorWrapper(OracleJsonGenerator wrapped) {
        this.wrapped = wrapped;
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public void close() throws JsonException {
        try {
            this.wrapped.close();
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public void flush() throws JsonException {
        try {
            this.wrapped.flush();
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator writeKey(String key) throws JsonException {
        try {
            this.wrapped.writeKey(key);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    private JsonException translate(OracleJsonException e) {
        if (e instanceof OracleJsonGenerationException) {
            return new JsonGenerationException(e.getMessage(), e);
        }
        return new JsonException(e.getMessage(), e);
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String key, JsonValue arg) throws JsonException {
        try {
            this.wrapped.writeKey(key);
            write(arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    public JsonGenerator write(JsonValue arg) throws JsonException {
        if (arg instanceof Wrapper) {
            Wrapper w = (Wrapper) arg;
            try {
                if (w.isWrapperFor(OracleJsonValue.class)) {
                    this.wrapped.write((OracleJsonValue) w.unwrap(OracleJsonValue.class));
                    return this;
                }
            } catch (SQLException e) {
                throw new IllegalStateException(e);
            }
        }
        writeJsonValue(arg);
        return this;
    }

    private void writeJsonValue(JsonValue value) throws JsonException {
        switch (AnonymousClass1.$SwitchMap$jakarta$json$JsonValue$ValueType[value.getValueType().ordinal()]) {
            case 1:
                JsonObject obj = (JsonObject) value;
                writeStartObject();
                for (Map.Entry<String, JsonValue> entry : obj.entrySet()) {
                    writeKey(entry.getKey());
                    writeJsonValue(entry.getValue());
                }
                writeEnd();
                break;
            case 2:
                JsonArray<JsonValue> arr = (JsonArray) value;
                writeStartArray();
                for (JsonValue v : arr) {
                    writeJsonValue(v);
                }
                writeEnd();
                break;
            case 3:
                JsonString str = (JsonString) value;
                write(str.getString());
                break;
            case 4:
                JsonNumber num = (JsonNumber) value;
                writeOraNum(num.bigDecimalValue());
                break;
            case 5:
                write(true);
                break;
            case 6:
                write(false);
                break;
            case 7:
                writeNull();
                break;
        }
    }

    public void writeJsonParser(Object p) throws JsonException {
        JsonParser parser = (JsonParser) p;
        int depth = 0;
        while (parser.hasNext()) {
            switch (AnonymousClass1.$SwitchMap$jakarta$json$stream$JsonParser$Event[parser.next().ordinal()]) {
                case 1:
                    writeEnd();
                    depth--;
                    break;
                case 2:
                    writeEnd();
                    depth--;
                    break;
                case 3:
                    writeKey(parser.getString());
                    break;
                case 4:
                    writeStartArray();
                    depth++;
                    break;
                case 5:
                    writeStartObject();
                    depth++;
                    break;
                case 6:
                    write(false);
                    break;
                case 7:
                    writeNull();
                    break;
                case 8:
                    writeOraNum(parser.getBigDecimal());
                    break;
                case 9:
                    write(parser.getString());
                    break;
                case 10:
                    write(true);
                    break;
            }
            if (depth <= 0) {
                return;
            }
        }
    }

    /* renamed from: oracle.jdbc.driver.json.JakartaGeneratorWrapper$1, reason: invalid class name */
    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/JakartaGeneratorWrapper$1.class */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] $SwitchMap$jakarta$json$JsonValue$ValueType;
        static final /* synthetic */ int[] $SwitchMap$jakarta$json$stream$JsonParser$Event = new int[JsonParser.Event.values().length];

        static {
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.END_ARRAY.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.END_OBJECT.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.KEY_NAME.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.START_ARRAY.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.START_OBJECT.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.VALUE_FALSE.ordinal()] = 6;
            } catch (NoSuchFieldError e6) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.VALUE_NULL.ordinal()] = 7;
            } catch (NoSuchFieldError e7) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.VALUE_NUMBER.ordinal()] = 8;
            } catch (NoSuchFieldError e8) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.VALUE_STRING.ordinal()] = 9;
            } catch (NoSuchFieldError e9) {
            }
            try {
                $SwitchMap$jakarta$json$stream$JsonParser$Event[JsonParser.Event.VALUE_TRUE.ordinal()] = 10;
            } catch (NoSuchFieldError e10) {
            }
            $SwitchMap$jakarta$json$JsonValue$ValueType = new int[JsonValue.ValueType.values().length];
            try {
                $SwitchMap$jakarta$json$JsonValue$ValueType[JsonValue.ValueType.OBJECT.ordinal()] = 1;
            } catch (NoSuchFieldError e11) {
            }
            try {
                $SwitchMap$jakarta$json$JsonValue$ValueType[JsonValue.ValueType.ARRAY.ordinal()] = 2;
            } catch (NoSuchFieldError e12) {
            }
            try {
                $SwitchMap$jakarta$json$JsonValue$ValueType[JsonValue.ValueType.STRING.ordinal()] = 3;
            } catch (NoSuchFieldError e13) {
            }
            try {
                $SwitchMap$jakarta$json$JsonValue$ValueType[JsonValue.ValueType.NUMBER.ordinal()] = 4;
            } catch (NoSuchFieldError e14) {
            }
            try {
                $SwitchMap$jakarta$json$JsonValue$ValueType[JsonValue.ValueType.TRUE.ordinal()] = 5;
            } catch (NoSuchFieldError e15) {
            }
            try {
                $SwitchMap$jakarta$json$JsonValue$ValueType[JsonValue.ValueType.FALSE.ordinal()] = 6;
            } catch (NoSuchFieldError e16) {
            }
            try {
                $SwitchMap$jakarta$json$JsonValue$ValueType[JsonValue.ValueType.NULL.ordinal()] = 7;
            } catch (NoSuchFieldError e17) {
            }
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String arg) throws JsonException {
        try {
            this.wrapped.write(arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(BigDecimal arg) throws JsonException {
        try {
            this.wrapped.write(arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    public JsonGenerator write(BigInteger arg) {
        this.wrapped.write(arg);
        return this;
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(int arg) throws JsonException {
        try {
            this.wrapped.write(arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(long arg) throws JsonException {
        try {
            this.wrapped.write(arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(double arg) throws JsonException {
        try {
            this.wrapped.write(arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(boolean arg) throws JsonException {
        try {
            this.wrapped.write(arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String key, String arg) throws JsonException {
        try {
            this.wrapped.write(key, arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String key, BigInteger arg) throws JsonException {
        try {
            this.wrapped.write(key, arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String key, BigDecimal arg) throws JsonException {
        try {
            this.wrapped.write(key, arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String key, int arg) throws JsonException {
        try {
            this.wrapped.write(key, arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String key, long arg) throws JsonException {
        try {
            this.wrapped.write(key, arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String key, double arg) throws JsonException {
        try {
            this.wrapped.write(key, arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator write(String key, boolean arg) throws JsonException {
        try {
            this.wrapped.write(key, arg);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator writeEnd() throws JsonException {
        try {
            this.wrapped.writeEnd();
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator writeNull() throws JsonException {
        try {
            this.wrapped.writeNull();
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator writeNull(String key) throws JsonException {
        try {
            this.wrapped.writeNull(key);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator writeStartArray() throws JsonException {
        try {
            this.wrapped.writeStartArray();
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator writeStartArray(String key) throws JsonException {
        try {
            this.wrapped.writeStartArray(key);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator writeStartObject() throws JsonException {
        try {
            this.wrapped.writeStartObject();
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonGenerator writeStartObject(String key) throws JsonException {
        try {
            this.wrapped.writeStartObject(key);
            return this;
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }

    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> cls) throws SQLException {
        if (cls.isInstance(this.wrapped)) {
            return (T) this.wrapped;
        }
        throw new SQLException(OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, cls.getName()).getMessage());
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return iface.isInstance(this.wrapped);
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    private void writeOraNum(BigDecimal value) throws JsonException {
        try {
            this.wrapped.write(new OracleJsonDecimalImpl(value));
        } catch (OracleJsonException e) {
            throw translate(e);
        }
    }
}
