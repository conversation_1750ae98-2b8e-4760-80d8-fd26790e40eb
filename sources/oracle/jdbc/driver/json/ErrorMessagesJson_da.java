package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_da.class */
public class ErrorMessagesJson_da extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "En I/O-undtagelse opstod"}, new Object[]{"26302", "Året \"{0}\" understøttes ikke"}, new Object[]{"26303", "Overløb, værdi er for stor: {0}."}, new Object[]{"26304", "Ikke-understøttet valg (ikke implementeret)."}, new Object[]{"26305", "Binær JSON er ugyldig eller beskadiget."}, new Object[]{"26306", "Ikke understøttet binær JSON-version: {0}."}, new Object[]{"26307", "Den UTF-8-kodede nøglelængde må ikke være større end 256 byte. Følgende nøgle overskrider denne grænse: \"{0}\"."}, new Object[]{"26308", "Den angivne JSON er for stor til at blive kodet som binær JSON. Den kodede billedstørrelse må ikke overstige 2 GB."}, new Object[]{"26309", "Binær JSON er ugyldig eller beskadiget. Det angivne billede indeholder kun {0} byte."}, new Object[]{"26310", "Den angivne java.time.Period har dage sat, men Oracle-år til måned-intervallet understøtter ikke dage."}, new Object[]{"26311", "Generator lukket før afslutning."}, new Object[]{"26312", "En objektnøgle skal være angivet i denne kontekst."}, new Object[]{"26313", "Ugyldig skrivning. Der er allerede skrevet en komplet værdi."}, new Object[]{"26314", "Afslutning er ikke tilladt i denne kontekst."}, new Object[]{"26315", "Nøgle er ikke tilladt i denne kontekst."}, new Object[]{"26316", "Forventet værdi efter nøgle."}, new Object[]{"26317", "Parser-tilstand skal være {0}."}, new Object[]{"26318", "Parser-tilstand må ikke være {0}."}, new Object[]{"26319", "Parser skal have en værdi."}, new Object[]{"26320", "\"{0}\" er ikke en understøttet wrapper-type."}, new Object[]{"26321", "Dette objekt kan ikke modificeres. Du kan oprette en modificerbar kopi ved at bruge OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Dette array kan ikke modificeres. Du kan oprette en modificerbar kopi ved at bruge OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "JSON-objekt indeholder dubleret nøgle: {0}."}, new Object[]{"26324", "Kan ikke registrere kodning automatisk, ikke nok tegn."}, new Object[]{"26325", "Forventede EOF-token, men modtog {0}."}, new Object[]{"26326", "Uventet tegn {0} på linje {1}, kolonne {2}."}, new Object[]{"26327", "Uventet tegn {0} på linje {1}, kolonne {2}. Forventede: {3}."}, new Object[]{"26328", "Ugyldigt token {0} på linje {1}, kolonne {2}. Forventede tokens er: {3}."}, new Object[]{"26329", "JsonParser#getString() er kun gyldig for parser-tilstandene KEY_NAME, VALUE_STRING, VALUE_NUMBER. Men den aktuelle parser-tilstand er {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() er kun gyldig for parser-tilstanden VALUE_NUMBER. Men den aktuelle parser-tilstand er {0}."}, new Object[]{"26331", "JsonParser#getInt() er kun gyldig for parser-tilstanden VALUE_NUMBER. Men den aktuelle parser-tilstand er {0}."}, new Object[]{"26332", "JsonParser#getLong() er kun gyldig for parser-tilstanden VALUE_NUMBER. Men den aktuelle parser-tilstand er {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() er kun gyldig for parser-tilstanden VALUE_NUMBER. Men den aktuelle parser-tilstand er {0}."}, new Object[]{"26334", "JsonParser#getArray() er kun gyldig for parser-tilstanden START_ARRAY. Men den aktuelle parser-tilstand er {0}."}, new Object[]{"26335", "JsonParser#getObject() er kun gyldig for parser-tilstanden START_OBJECT. Men den aktuelle parser-tilstand er {0}."}, new Object[]{"26336", "Et tidsstempel med en region understøttes ikke. Kun forskudte tidszoner understøttes."}, new Object[]{"26337", "Objekter og arrays i JSON-værdien må ikke indlejres dybere end {0} niveauer"}, new Object[]{"26338", "Nøglerne til et JSON-objekt må ikke overskride 65.535 bytes"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
