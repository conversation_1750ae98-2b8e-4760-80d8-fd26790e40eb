package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_pl.class */
public class ErrorMessagesJson_pl extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Wystąpił wyjątek we-wy"}, new Object[]{"26302", "Rok \"{0}\" nie jest obsługiwany"}, new Object[]{"26303", "Przepełnienie, zbyt duża wartość: {0}."}, new Object[]{"26304", "Nieobsługiwana opcja (niezaimplementowana)."}, new Object[]{"26305", "Binarny plik JSON jest niepoprawny lub uszkodzony."}, new Object[]{"26306", "Nieobsługiwana wersja binarnego pliku JSON: {0}."}, new Object[]{"26307", "Długość klucza w formacie UTF-8 nie może przekraczać 256 bajtów. Limit ten został przekroczony przez następujący klucz: \"{0}\"."}, new Object[]{"26308", "Podany plik JSON jest zbyt duży, aby mógł zostać zakodowany jako binarny plik JSON. Rozmiar zakodowanych obrazów nie może przekraczać 2 GB."}, new Object[]{"26309", "Binarny plik JSON jest niepoprawny lub uszkodzony. Podany obraz zawiera tylko {0} bajty(-ów)."}, new Object[]{"26310", "Podana właściwość java.time.Period ma ustawione dni, lecz interwał Oracle od roku do miesiąca nie obsługuje dni."}, new Object[]{"26311", "Generator został zamknięty przed zakończeniem."}, new Object[]{"26312", "W tym kontekście musi zostać określony klucz kontekstu."}, new Object[]{"26313", "Niepoprawny zapis. Pełna wartość już została zapisana."}, new Object[]{"26314", "Koniec nie jest dozwolony w tym kontekście."}, new Object[]{"26315", "Klucz nie jest dozwolony w tym kontekście."}, new Object[]{"26316", "Oczekiwano wartości po kluczu."}, new Object[]{"26317", "stanem analizatora składni musi być \"{0}\"."}, new Object[]{"26318", "Stanem analizatora składni nie może być \"{0}\"."}, new Object[]{"26319", "Analizator składni musi być ustawiony dla wartości."}, new Object[]{"26320", "\"{0}\" nie jest obsługiwanym typem izolatora."}, new Object[]{"26321", "Tego obiektu nie można zmodyfikować. Aby utworzyć modyfikowalną kopię, proszę użyć OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Tej tablicy nie można zmodyfikować. Aby utworzyć modyfikowalną kopię, proszę użyć OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "obiekt JSON zawiera zduplikowany klucz: {0}."}, new Object[]{"26324", "Nie można automatycznie wykryć kodowania; za mało znaków."}, new Object[]{"26325", "Oczekiwano tokenu EOF, lecz uzyskano {0}."}, new Object[]{"26326", "Nieoczekiwany znak {0} (linia {1}, kolumna {2})."}, new Object[]{"26327", "Nieoczekiwany znak {0} (linia {1}, kolumna {2}); Oczekiwano: {3}."}, new Object[]{"26328", "Niepoprawny token {0} (linia {1}, kolumna {2}). Oczekiwane tokeny: {3}."}, new Object[]{"26329", "Metoda JsonParser#getString() jest poprawna tylko przy następujących stanach analizatora składni: KEY_NAME, VALUE_STRING, VALUE_NUMBER. Bieżącym stanem jest {0}."}, new Object[]{"26330", "Metoda JsonParser#isIntegralNumber() jest poprawna tylko przy stanie VALUE_NUMBER analizatora składni. Bieżącym stanem jest {0}."}, new Object[]{"26331", "Metoda JsonParser#getInt() jest poprawna tylko przy stanie VALUE_NUMBER analizatora składni. Bieżącym stanem jest {0}."}, new Object[]{"26332", "Metoda JsonParser#getLong() jest poprawna tylko przy stanie VALUE_NUMBER analizatora składni. Bieżącym stanem jest {0}."}, new Object[]{"26333", "Metoda JsonParser#getBigDecimal() jest poprawna tylko przy stanie VALUE_NUMBER analizatora składni. Bieżącym stanem jest {0}."}, new Object[]{"26334", "Metoda JsonParser#getArray() jest poprawna tylko przy stanie START_ARRAY analizatora składni. Bieżącym stanem jest {0}."}, new Object[]{"26335", "Metoda JsonParser#getObject() jest poprawna tylko przy stanie START_ARRAY analizatora składni. Bieżącym stanem jest {0}."}, new Object[]{"26336", "Znacznik czasu z regionem nie jest obsługiwany. Obsługiwane są tylko strefy czasowe z przesunięciem."}, new Object[]{"26337", "Zagnieżdzenie obiektów i tablic w wartości JSON nie może przekraczać {0} poziomów"}, new Object[]{"26338", "Rozmiar kluczy obiektu JSON nie może przekraczać 65 535 bajtów"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
