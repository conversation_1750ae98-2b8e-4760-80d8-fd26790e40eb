package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.OracleStatement;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VarcharAccessor.class */
class VarcharAccessor extends CharCommonAccessor {
    private static final String CLASS_NAME = VarcharAccessor.class.getName();

    VarcharAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        super(stmt, stmt.connection.getMaxSizeForVarchar(stmt.sqlKind, max_len, stmt.connection.plsqlVarcharParameter4KOnly), form, isStoredInBindData);
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "VarcharAccessor", "representationMaxLength:{0}", (String) null, (String) null, (Object) Integer.valueOf(this.representationMaxLength));
        init(stmt, 1, 9, max_len, form, external_type, isOutBind, this.representationMaxLength);
    }

    VarcharAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int maxCodePointLen) throws SQLException {
        super(stmt, stmt.sqlKind == OracleStatement.SqlKind.PLSQL_BLOCK ? stmt.connection.maxVcsBytesPlsql : stmt.connection.maxVarcharLength, form, false);
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "VarcharAccessor", "representationMaxLength:{0}", (String) null, (String) null, (Object) Integer.valueOf(this.representationMaxLength));
        init(stmt, 1, 9, max_len, nullable, flags, precision, scale, contflag, total_elems, form, maxCodePointLen);
    }
}
