package oracle.jdbc.driver.json.binary;

import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.CharsetEncoder;
import java.nio.charset.CodingErrorAction;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.Period;
import oracle.jdbc.OracleType;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.tree.OracleJsonBinaryImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDateImpl;
import oracle.jdbc.driver.json.tree.OracleJsonIntervalDSImpl;
import oracle.jdbc.driver.json.tree.OracleJsonIntervalYMImpl;
import oracle.jdbc.driver.json.tree.OracleJsonNumberImpl;
import oracle.jdbc.driver.json.tree.OracleJsonTimestampImpl;
import oracle.jdbc.driver.json.tree.OracleJsonTimestampTZImpl;
import oracle.sql.VECTOR;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonDate;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonDouble;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonFloat;
import oracle.sql.json.OracleJsonGenerator;
import oracle.sql.json.OracleJsonIntervalDS;
import oracle.sql.json.OracleJsonIntervalYM;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonParser;
import oracle.sql.json.OracleJsonString;
import oracle.sql.json.OracleJsonTimestamp;
import oracle.sql.json.OracleJsonTimestampTZ;
import oracle.sql.json.OracleJsonVector;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JsonSerializerImpl.class */
public class JsonSerializerImpl extends AbstractGenerator {
    private static final byte[] CHAR_TYPES = new byte[65536];
    private static final byte CHAR_OTHER = 0;
    private static final byte CHAR_CONTROL = 1;
    private static final byte CHAR_LF = 2;
    private static final byte CHAR_QUOTATION = 3;
    private static final byte CHAR_REVERSE_SOLIDUS = 4;
    private static final byte CHAR_SURROGATE = 5;
    private static final byte CHAR_BACKSPACE = 6;
    private static final byte CHAR_FORMFEED = 7;
    private static final byte CHAR_CR = 8;
    private static final byte CHAR_TAB = 9;
    private static final byte CHAR_ASCII = 10;
    private static final int ASCII_MAX = 127;
    private JsonOutput writer;
    private StreamContext ctx = new StreamContext(getExceptionFactory());

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JsonSerializerImpl$JsonOutput.class */
    private interface JsonOutput extends Closeable {
        void flush() throws IOException;

        void write(char c) throws IOException;

        void writeAscii(CharSequence charSequence) throws IOException;

        void writeAscii(byte b) throws IOException;

        void writeSurrogates(char c, char c2) throws IOException;

        boolean utf8();

        void utf8(byte[] bArr, int i, int i2) throws IOException;
    }

    static {
        for (int i = 0; i <= 127; i++) {
            CHAR_TYPES[i] = 10;
        }
        char c = 0;
        while (true) {
            char i2 = c;
            if (i2 > 31) {
                break;
            }
            CHAR_TYPES[i2] = 1;
            c = (char) (i2 + 1);
        }
        CHAR_TYPES[127] = 1;
        CHAR_TYPES[10] = 2;
        CHAR_TYPES[92] = 4;
        CHAR_TYPES[34] = 3;
        CHAR_TYPES[8] = 6;
        CHAR_TYPES[12] = 7;
        CHAR_TYPES[13] = 8;
        CHAR_TYPES[9] = 9;
        char c2 = 55296;
        while (true) {
            char c3 = c2;
            if (c3 <= 56319) {
                CHAR_TYPES[c3] = 5;
                c2 = (char) (c3 + 1);
            } else {
                return;
            }
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JsonSerializerImpl$WriterJsonOutput.class */
    private static final class WriterJsonOutput implements JsonOutput {
        char[] buffer = new char[1024];
        int pos = 0;
        Writer writer;

        WriterJsonOutput(Writer writer) {
            this.writer = writer;
        }

        @Override // java.io.Closeable, java.lang.AutoCloseable
        public void close() throws IOException {
            flush();
            this.writer.close();
            this.buffer = null;
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void flush() throws IOException {
            this.writer.write(this.buffer, 0, this.pos);
            this.pos = 0;
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void writeAscii(CharSequence value) throws IOException {
            for (int i = 0; i < value.length(); i++) {
                if (this.pos >= this.buffer.length) {
                    flush();
                }
                char[] cArr = this.buffer;
                int i2 = this.pos;
                this.pos = i2 + 1;
                cArr[i2] = value.charAt(i);
            }
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void writeAscii(byte value) throws IOException {
            if (this.pos >= this.buffer.length) {
                flush();
            }
            char[] cArr = this.buffer;
            int i = this.pos;
            this.pos = i + 1;
            cArr[i] = (char) value;
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void write(char c) throws IOException {
            if (this.pos >= this.buffer.length) {
                flush();
            }
            char[] cArr = this.buffer;
            int i = this.pos;
            this.pos = i + 1;
            cArr[i] = c;
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void writeSurrogates(char c1, char c2) throws IOException {
            write(c1);
            write(c2);
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public boolean utf8() {
            return false;
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void utf8(byte[] array, int offset, int len) throws IOException {
            flush();
            this.writer.write(new String(array, offset, len, StandardCharsets.UTF_8));
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JsonSerializerImpl$UTF8JsonOutput.class */
    private static class UTF8JsonOutput implements JsonOutput {
        OutputStream out;
        CharsetEncoder encoder;
        CharBuffer cbuffer;
        byte[] buffer = new byte[1024];
        int pos = 0;

        public UTF8JsonOutput(OutputStream out) {
            this.out = out;
        }

        private void initEncoder() {
            if (this.encoder == null) {
                this.encoder = StandardCharsets.UTF_8.newEncoder().onMalformedInput(CodingErrorAction.REPLACE).onUnmappableCharacter(CodingErrorAction.REPLACE);
                this.cbuffer = CharBuffer.allocate(2);
            }
        }

        @Override // java.io.Closeable, java.lang.AutoCloseable
        public void close() throws IOException {
            flush();
            this.out.close();
            this.buffer = null;
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void writeAscii(CharSequence value) throws IOException {
            for (int i = 0; i < value.length(); i++) {
                if (this.pos >= this.buffer.length) {
                    flush();
                }
                byte[] bArr = this.buffer;
                int i2 = this.pos;
                this.pos = i2 + 1;
                bArr[i2] = (byte) value.charAt(i);
            }
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void writeAscii(byte value) throws IOException {
            if (this.pos >= this.buffer.length) {
                flush();
            }
            byte[] bArr = this.buffer;
            int i = this.pos;
            this.pos = i + 1;
            bArr[i] = value;
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void flush() throws IOException {
            this.out.write(this.buffer, 0, this.pos);
            this.pos = 0;
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void write(char c) throws IOException {
            if (c <= 127) {
                writeAscii((byte) c);
                return;
            }
            flush();
            initEncoder();
            this.cbuffer.clear();
            this.cbuffer.append(c);
            this.cbuffer.flip();
            encodeChars();
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void writeSurrogates(char c1, char c2) throws IOException {
            flush();
            initEncoder();
            this.cbuffer.clear();
            this.cbuffer.append(c1);
            this.cbuffer.append(c2);
            this.cbuffer.flip();
            encodeChars();
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public boolean utf8() {
            return true;
        }

        private void encodeChars() {
            ByteBuffer b = ByteBuffer.wrap(this.buffer);
            this.encoder.encode(this.cbuffer, b, false);
            this.pos = b.position();
        }

        @Override // oracle.jdbc.driver.json.binary.JsonSerializerImpl.JsonOutput
        public void utf8(byte[] array, int offset, int len) throws IOException {
            flush();
            this.out.write(array, offset, len);
        }
    }

    public JsonSerializerImpl(OutputStream out) {
        this.writer = new UTF8JsonOutput(out);
    }

    public JsonSerializerImpl(Writer writer) {
        this.writer = new WriterJsonOutput(writer);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeStartObject() {
        writeSeparator();
        this.ctx.startObject();
        writeAscii('{');
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeStartArray() {
        writeSeparator();
        this.ctx.startArray();
        writeAscii('[');
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeEnd() {
        if (this.ctx.inObject()) {
            this.ctx.end();
            writeAscii('}');
        } else {
            this.ctx.end();
            writeAscii(']');
        }
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String value) {
        primitive();
        writeQuotedString(value, this.writer, getExceptionFactory());
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator, oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeKey(String key) {
        writeSeparator();
        this.ctx.pendingKey();
        writeQuotedString(key, this.writer, getExceptionFactory());
        writeAscii(':');
        return this;
    }

    private static void writeQuotedString(String value, JsonOutput writer, OracleJsonExceptions.ExceptionFactory f) {
        try {
            writer.writeAscii((byte) 34);
            writeEscaped(value, writer);
            writer.writeAscii((byte) 34);
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(f, e, new Object[0]);
        }
    }

    public static String serializeString(String value) {
        StringWriter w = new StringWriter();
        WriterJsonOutput o = new WriterJsonOutput(w);
        writeQuotedString(value, o, null);
        try {
            o.close();
            return w.toString();
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(BigDecimal value) {
        primitive();
        writeBigDecimal(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(BigInteger value) {
        primitive();
        writeBigInteger(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(int value) {
        primitive();
        writeInt(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(long value) {
        primitive();
        writeLong(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(double value) {
        assertFinite(value);
        primitive();
        writeDouble(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(float value) {
        assertFinite(value);
        primitive();
        writeFloat(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(boolean value) {
        primitive();
        writeBoolean(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeNull() {
        primitive();
        writeNullInternal();
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator, java.io.Closeable, java.lang.AutoCloseable
    public void close() {
        this.ctx.close();
        try {
            this.writer.close();
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
        }
    }

    protected OracleJsonExceptions.ExceptionFactory getExceptionFactory() {
        return OracleJsonExceptions.ORACLE_FACTORY;
    }

    @Override // oracle.sql.json.OracleJsonGenerator, java.io.Flushable
    public void flush() {
        try {
            this.writer.flush();
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
        }
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeBinary(OracleJsonBinary value) {
        return write(((OracleJsonBinaryImpl) value).getString());
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeDouble(OracleJsonDouble value) {
        return writeNumber(value);
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeFloat(OracleJsonFloat value) {
        return writeNumber(value);
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeOraNumber(OracleJsonDecimal value) {
        return writeNumber(value);
    }

    private OracleJsonGenerator writeNumber(OracleJsonNumber value) {
        primitive();
        try {
            this.writer.writeAscii(numberToString(value));
            return this;
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    private String numberToString(OracleJsonNumber oracleJsonNumber) {
        if (oracleJsonNumber instanceof OracleJsonNumberImpl) {
            return ((OracleJsonNumberImpl) oracleJsonNumber).getString();
        }
        return oracleJsonNumber.toString();
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeTimestamp(OracleJsonTimestamp value) {
        return write(((OracleJsonTimestampImpl) value).getString());
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeTimestampTZ(OracleJsonTimestampTZ value) {
        return write(((OracleJsonTimestampTZImpl) value).getString());
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeDate(OracleJsonDate value) {
        return write(((OracleJsonDateImpl) value).getString());
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeIntervalDS(OracleJsonIntervalDS value) {
        return write(((OracleJsonIntervalDSImpl) value).getString());
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeIntervalYM(OracleJsonIntervalYM value) {
        return write(((OracleJsonIntervalYMImpl) value).getString());
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeVector(OracleJsonVector value) {
        writeStartArray();
        VECTOR v = value.getVECTOR();
        try {
            OracleType type = v.getType();
            if (type == OracleType.VECTOR_INT8) {
                int[] ints = v.toIntArray();
                for (int i : ints) {
                    primitive();
                    writeInt(i);
                }
            } else if (type == OracleType.VECTOR_FLOAT32) {
                float[] dbls = value.getFloatArray();
                for (float f : dbls) {
                    primitive();
                    writeFloat(f);
                }
            } else {
                double[] dbls2 = value.getDoubleArray();
                for (double d : dbls2) {
                    primitive();
                    writeDouble(d);
                }
            }
            writeEnd();
            return this;
        } catch (SQLException e) {
            throw new OracleJsonException(e);
        }
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeString(OracleJsonString value) {
        return write(value.getString());
    }

    private void writeAscii(CharSequence s) {
        try {
            this.writer.writeAscii(s);
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
        }
    }

    private void writeBigDecimal(BigDecimal value) {
        writeAscii(value.toString());
    }

    private void writeBigInteger(BigInteger value) {
        writeAscii(new BigDecimal(value).toString());
    }

    private void writeInt(int value) {
        writeAscii(new BigDecimal(value).toString());
    }

    private void writeLong(long value) {
        writeAscii(new BigDecimal(value).toString());
    }

    private void writeFloat(float value) {
        writeAscii(Float.toString(value));
    }

    private void writeDouble(double value) {
        writeAscii(Double.toString(value));
    }

    private void writeBoolean(boolean value) {
        writeAscii(String.valueOf(value));
    }

    private void writeNullInternal() {
        writeAscii("null");
    }

    private void writeAscii(char c) {
        try {
            this.writer.writeAscii((byte) c);
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
        }
    }

    private void primitive() {
        writeSeparator();
        this.ctx.primitive();
    }

    private void writeSeparator() {
        if (this.ctx.hasChildren() && !this.ctx.pendingKey) {
            writeAscii(',');
        }
    }

    private static void writeEscaped(String value, JsonOutput writer) throws IOException {
        int length = value.length();
        int i = 0;
        while (i < length) {
            char c = value.charAt(i);
            switch (CHAR_TYPES[c]) {
                case 0:
                    writer.write(c);
                    break;
                case 1:
                    escape(c, writer);
                    break;
                case 2:
                    writer.writeAscii("\\n");
                    break;
                case 3:
                    writer.writeAscii("\\\"");
                    break;
                case 4:
                    writer.writeAscii("\\\\");
                    break;
                case 5:
                    writer.writeSurrogates(value.charAt(i), value.charAt(i + 1));
                    i++;
                    break;
                case 6:
                    writer.writeAscii("\\b");
                    break;
                case 7:
                    writer.writeAscii("\\f");
                    break;
                case 8:
                    writer.writeAscii("\\r");
                    break;
                case 9:
                    writer.writeAscii("\\t");
                    break;
                case 10:
                    writer.writeAscii((byte) c);
                    break;
            }
            i++;
        }
    }

    private void assertFinite(double value) {
        if (Double.isInfinite(value) || Double.isNaN(value)) {
            throw new NumberFormatException(Double.toString(value));
        }
    }

    private static void escape(int cp, JsonOutput writer) throws IOException {
        for (char c : Character.toChars(cp)) {
            String hex = Integer.toHexString(c).toUpperCase();
            writer.writeAscii("\\u");
            for (int i = 0; i < 4 - hex.length(); i++) {
                writer.writeAscii((byte) 48);
            }
            writer.writeAscii(hex);
        }
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(LocalDateTime local) {
        byte[] bytes = OsonPrimitiveConversions.toOracleTimestamp(getExceptionFactory(), local);
        write(OsonPrimitiveConversions.timestampToString(getExceptionFactory(), bytes));
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(OffsetDateTime off) {
        byte[] bytes = OsonPrimitiveConversions.toOracleTimestampTZ(getExceptionFactory(), off);
        write(OsonPrimitiveConversions.timestampTZToString(getExceptionFactory(), bytes));
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String key, LocalDateTime value) {
        writeKey(key);
        write(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String key, OffsetDateTime value) {
        writeKey(key);
        write(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String key, byte[] value) {
        writeKey(key);
        write(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(byte[] value) {
        return write(OracleJsonBinaryImpl.getString(value, false));
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeId(byte[] value) {
        return write(OracleJsonBinaryImpl.getString(value, true));
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(Period value) {
        return write(OracleJsonIntervalYMImpl.serializePeriod(value, getExceptionFactory()));
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(Duration value) {
        return write(OracleJsonIntervalDSImpl.serializeDuration(value, getExceptionFactory()));
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected void writeStringFromParser(OracleJsonParser parser) {
        if (!this.writer.utf8() || !(parser instanceof OsonParserImpl)) {
            write(parser.getString());
            return;
        }
        primitive();
        OsonParserImpl oparser = (OsonParserImpl) parser;
        byte[] arr = oparser.getContext().b.buffer.array();
        writeQuotedUTF8String(arr, oparser.getCurrentStringPos(), oparser.getCurrentStringLen());
    }

    private void writeQuotedUTF8String(byte[] array, int offset, int len) {
        try {
            writeAscii('\"');
            int end = offset + len;
            int i = offset;
            while (i < end) {
                byte b = array[i];
                switch (CHAR_TYPES[b & 255]) {
                    case 1:
                        escape(b, this.writer);
                        break;
                    case 2:
                        this.writer.writeAscii("\\n");
                        break;
                    case 3:
                        this.writer.writeAscii("\\\"");
                        break;
                    case 4:
                        this.writer.writeAscii("\\\\");
                        break;
                    case 5:
                    default:
                        if ((b & 224) == 192) {
                            this.writer.utf8(array, i, 2);
                            i++;
                            break;
                        } else if ((b & 240) == 224) {
                            this.writer.utf8(array, i, 3);
                            i += 2;
                            break;
                        } else {
                            this.writer.utf8(array, i, 4);
                            i += 3;
                            break;
                        }
                    case 6:
                        this.writer.writeAscii("\\b");
                        break;
                    case 7:
                        this.writer.writeAscii("\\f");
                        break;
                    case 8:
                        this.writer.writeAscii("\\r");
                        break;
                    case 9:
                        this.writer.writeAscii("\\t");
                        break;
                    case 10:
                        this.writer.writeAscii(b);
                        break;
                }
                i++;
            }
            writeAscii('\"');
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
        }
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected void writeDecimalFromParser(OracleJsonParser parser) {
        primitive();
        try {
            this.writer.writeAscii(parser.getString());
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
        }
    }
}
