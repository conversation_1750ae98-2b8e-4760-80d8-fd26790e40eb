package oracle.jdbc.driver.resource;

import java.sql.SQLException;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.spi.OracleResourceProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ResourceProvider.class */
public interface ResourceProvider<T extends OracleResourceProvider, U> {

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ResourceProvider$Builder.class */
    public interface Builder<T extends OracleResourceProvider, U> {
        ResourceProvider<T, U> build() throws SQLException;

        Builder<T, U> parameterValue(String str, CharSequence charSequence) throws SQLException;

        Builder<T, U> parameterValue(String str, OpaqueString opaqueString) throws SQLException;
    }

    U getResource() throws SQLException;

    static <T extends OracleResourceProvider, U> Builder<T, U> builder(String providerName, ResourceType<T, U> resourceType) throws SQLException {
        return DriverResourceProvider.builder(providerName, (ResourceType) resourceType);
    }
}
