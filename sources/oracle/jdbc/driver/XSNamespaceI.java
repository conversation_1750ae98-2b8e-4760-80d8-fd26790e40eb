package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.XSAttribute;
import oracle.jdbc.internal.XSNamespace;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/XSNamespaceI.class */
final class XSNamespaceI extends XSNamespace {
    String namespaceName = null;
    byte[] namespaceNameBytes = null;
    String kpxsnshandler = null;
    byte[] kpxsnshandlerBytes = null;
    XSAttributeI[] attributes = null;
    byte[] timestampBytes = null;
    long flag = 0;
    long[] aclList = null;

    XSNamespaceI() {
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public void setNamespaceName(String _namespaceName) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.namespaceName = _namespaceName;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public void setNamespaceHandler(String handler) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.kpxsnshandler = handler;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public void setTimestamp(TIMESTAMPTZ _timestamp) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.timestampBytes = _timestamp.toBytes();
    }

    private void setTimestamp(byte[] _timestamp) throws SQLException {
        this.timestampBytes = _timestamp;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public void setACLIdList(long[] _aclList) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.aclList = _aclList;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public void setFlag(long _flag) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.flag = _flag;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public void setAttributes(XSAttribute[] _attributes) throws SQLException {
        InternalFactory.xsSecurityCheck();
        if (_attributes != null) {
            XSAttributeI[] _attr = new XSAttributeI[_attributes.length];
            for (int i = 0; i < _attributes.length; i++) {
                _attr[i] = (XSAttributeI) _attributes[i];
            }
            this.attributes = _attr;
        }
    }

    void doCharConversion(DBConversion conv) throws SQLException {
        if (this.namespaceName != null) {
            this.namespaceNameBytes = conv.StringToCharBytes(this.namespaceName);
        } else {
            this.namespaceNameBytes = null;
        }
        if (this.kpxsnshandler != null) {
            this.kpxsnshandlerBytes = conv.StringToCharBytes(this.kpxsnshandler);
        } else {
            this.kpxsnshandlerBytes = null;
        }
        if (this.attributes != null) {
            for (int i = 0; i < this.attributes.length; i++) {
                this.attributes[i].doCharConversion(conv);
            }
        }
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public String getNamespaceName() {
        InternalFactory.xsSecurityCheck();
        return this.namespaceName;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public String getNamespaceHandler() {
        InternalFactory.xsSecurityCheck();
        return this.kpxsnshandler;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public TIMESTAMPTZ getTimestamp() {
        InternalFactory.xsSecurityCheck();
        return new TIMESTAMPTZ(this.timestampBytes);
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public long getFlag() {
        InternalFactory.xsSecurityCheck();
        return this.flag;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public XSAttribute[] getAttributes() {
        InternalFactory.xsSecurityCheck();
        return this.attributes;
    }

    @Override // oracle.jdbc.internal.XSNamespace
    public long[] getACLIdList() {
        InternalFactory.xsSecurityCheck();
        return this.aclList;
    }

    void marshal(T4CMAREngine mar) throws IOException {
        if (this.namespaceNameBytes != null) {
            mar.marshalUB4(this.namespaceNameBytes.length);
            mar.marshalCLR(this.namespaceNameBytes, this.namespaceNameBytes.length);
        } else {
            mar.marshalUB4(0L);
        }
        if (this.kpxsnshandlerBytes != null) {
            mar.marshalUB4(this.kpxsnshandlerBytes.length);
            mar.marshalCLR(this.kpxsnshandlerBytes, this.kpxsnshandlerBytes.length);
        } else {
            mar.marshalUB4(0L);
        }
        if (this.timestampBytes != null) {
            mar.marshalUB4(this.timestampBytes.length);
            mar.marshalCLR(this.timestampBytes, this.timestampBytes.length);
        } else {
            mar.marshalUB4(0L);
        }
        mar.marshalUB4(this.flag);
        if (this.attributes != null) {
            mar.marshalUB4(this.attributes.length);
            mar.marshalUB1((short) 28);
            for (int i = 0; i < this.attributes.length; i++) {
                this.attributes[i].marshal(mar);
            }
        } else {
            mar.marshalUB4(0L);
        }
        if (this.aclList != null) {
            mar.marshalUB4(this.aclList.length);
            mar.marshalUB1((short) 8);
            for (int i2 = 0; i2 < this.aclList.length; i2++) {
                mar.marshalSB8(this.aclList[i2]);
            }
            return;
        }
        mar.marshalUB4(0L);
    }

    static XSNamespaceI unmarshal(T4CMAREngine mar) throws SQLException, IOException {
        int[] intArr = new int[1];
        String namespaceName = null;
        int namespaceNameLength = (int) mar.unmarshalUB4();
        if (namespaceNameLength > 0) {
            byte[] namespaceNameBytesTemp = new byte[namespaceNameLength];
            mar.unmarshalCLR(namespaceNameBytesTemp, 0, intArr);
            namespaceName = mar.conv.CharBytesToString(namespaceNameBytesTemp, intArr[0]);
        }
        int handlerLength = (int) mar.unmarshalUB4();
        String handler = null;
        if (handlerLength > 0) {
            byte[] handlerBytesTemp = new byte[namespaceNameLength];
            mar.unmarshalCLR(handlerBytesTemp, 0, intArr);
            handler = mar.conv.CharBytesToString(handlerBytesTemp, intArr[0]);
        }
        byte[] timestampBytes = null;
        if (mar.unmarshalUB1() == 1) {
            int timestampLength = (int) mar.unmarshalUB4();
            timestampBytes = mar.unmarshalNBytes(timestampLength);
        }
        long flag = mar.unmarshalUB4();
        int nbOfAttr = (int) mar.unmarshalUB4();
        XSAttribute[] attributes = new XSAttribute[nbOfAttr];
        if (nbOfAttr > 0) {
            mar.unmarshalUB1();
        }
        for (int i = 0; i < nbOfAttr; i++) {
            attributes[i] = XSAttributeI.unmarshal(mar);
        }
        int aclListLength = (int) mar.unmarshalUB4();
        long[] aclList = null;
        if (aclListLength > 0) {
            aclList = new long[aclListLength];
            for (int i2 = 0; i2 < aclListLength; i2++) {
                aclList[i2] = mar.unmarshalSB8();
            }
        }
        XSNamespaceI namespace = new XSNamespaceI();
        namespace.setNamespaceName(namespaceName);
        namespace.setNamespaceHandler(handler);
        namespace.setTimestamp(timestampBytes);
        namespace.setFlag(flag);
        namespace.setAttributes(attributes);
        namespace.setACLIdList(aclList);
        return namespace;
    }
}
