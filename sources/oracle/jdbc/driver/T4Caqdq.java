package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.aq.AQDequeueOptions;
import oracle.jdbc.aq.AQMessageProperties;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4Caqdq.class */
final class T4Caqdq extends T4CTTIfun {
    T4CTTIaqm aqm;
    T4Ctoh toh;
    static final int AQDVER_DEFAULT = 1;
    private String queueName;
    private AQDequeueOptions dequeueOptions;
    private byte[] payloadToid;
    private byte[] queueNameBytes;
    private byte[] consumerNameBytes;
    private byte[] correlationBytes;
    private byte[] conditionBytes;
    private int nbExtensions;
    private byte[][] extensionTextValues;
    private byte[][] extensionBinaryValues;
    private int[] extensionKeywords;
    private byte[] payload;
    private boolean hasAMessageBeenDequeued;
    private byte[] dequeuedMessageId;
    private int aqdver;
    private boolean isRawQueue;
    private boolean isJsonQueue;
    private AQMessagePropertiesI properties;

    T4Caqdq(T4CConnection _connection) {
        super(_connection, (byte) 3);
        this.dequeueOptions = null;
        this.payloadToid = null;
        this.queueNameBytes = null;
        this.consumerNameBytes = null;
        this.correlationBytes = null;
        this.conditionBytes = null;
        this.nbExtensions = 0;
        this.extensionTextValues = (byte[][]) null;
        this.extensionBinaryValues = (byte[][]) null;
        this.extensionKeywords = null;
        this.payload = null;
        this.hasAMessageBeenDequeued = false;
        this.dequeuedMessageId = null;
        this.aqdver = 1;
        this.isRawQueue = false;
        this.properties = null;
        setFunCode((short) 122);
        this.toh = new T4Ctoh(_connection);
        this.aqm = new T4CTTIaqm(this.connection, this.toh);
    }

    /* JADX WARN: Type inference failed for: r1v20, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v23, types: [byte[], byte[][]] */
    void doOAQDQ(String _queueName, AQDequeueOptions _dequeueOptions, byte[] _payloadToid, int _payloadVersion, boolean _isRawQueue, AQMessagePropertiesI _properties) throws SQLException, IOException {
        this.queueName = _queueName;
        this.dequeueOptions = _dequeueOptions;
        this.payloadToid = _payloadToid;
        this.aqdver = _payloadVersion;
        this.isRawQueue = _isRawQueue;
        this.isJsonQueue = AQMessageI.compareToid(this.payloadToid, TypeDescriptor.JSONTOID);
        this.properties = _properties;
        if (this.queueName != null && this.queueName.length() != 0) {
            this.queueNameBytes = this.meg.conv.StringToCharBytes(this.queueName);
        } else {
            this.queueNameBytes = null;
        }
        String consumerNameStr = this.dequeueOptions.getConsumerName();
        if (consumerNameStr != null && consumerNameStr.length() > 0) {
            this.consumerNameBytes = this.meg.conv.StringToCharBytes(consumerNameStr);
        } else {
            this.consumerNameBytes = null;
        }
        String correlation = this.dequeueOptions.getCorrelation();
        if (correlation != null && correlation.length() != 0) {
            this.correlationBytes = this.meg.conv.StringToCharBytes(correlation);
        } else {
            this.correlationBytes = null;
        }
        String condition = this.dequeueOptions.getCondition();
        if (condition != null && condition.length() > 0) {
            this.conditionBytes = this.meg.conv.StringToCharBytes(condition);
        } else {
            this.conditionBytes = null;
        }
        String transformation = this.dequeueOptions.getTransformation();
        if (transformation != null && transformation.length() > 0) {
            this.nbExtensions = 1;
            this.extensionTextValues = new byte[this.nbExtensions];
            this.extensionBinaryValues = new byte[this.nbExtensions];
            this.extensionKeywords = new int[this.nbExtensions];
            this.extensionTextValues[0] = this.meg.conv.StringToCharBytes(transformation);
            this.extensionBinaryValues[0] = null;
            this.extensionKeywords[0] = 196;
        } else {
            this.nbExtensions = 0;
        }
        this.hasAMessageBeenDequeued = false;
        this.dequeuedMessageId = null;
        this.payload = null;
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.queueNameBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        if (this.consumerNameBytes != null && this.consumerNameBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.consumerNameBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalSB4(this.dequeueOptions.getDequeueMode().getCode());
        this.meg.marshalSB4(this.dequeueOptions.getNavigation().getCode());
        this.meg.marshalSB4(this.dequeueOptions.getVisibility().getCode());
        this.meg.marshalSB4(this.dequeueOptions.getWait());
        byte[] mesgId = this.dequeueOptions.getDequeueMessageId();
        boolean sendMsgId = false;
        if (mesgId != null && mesgId.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(mesgId.length);
            sendMsgId = true;
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (this.correlationBytes != null && this.correlationBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.correlationBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalPTR();
        this.meg.marshalSWORD(this.payloadToid.length);
        this.meg.marshalUB2(this.aqdver);
        this.meg.marshalPTR();
        if (this.dequeueOptions.getRetrieveMessageId()) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(16);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        int aqdflg = 0;
        int reqShardNum = this.dequeueOptions.getShardNum();
        if (this.connection.autocommit) {
            aqdflg = 32;
        }
        if (this.dequeueOptions.getDeliveryFilter() == AQDequeueOptions.DeliveryFilter.BUFFERED) {
            aqdflg |= 2;
        } else if (this.dequeueOptions.getDeliveryFilter() == AQDequeueOptions.DeliveryFilter.PERSISTENT_OR_BUFFERED) {
            aqdflg |= 16;
        }
        if (reqShardNum != -1) {
            aqdflg |= 64;
        }
        this.meg.marshalUB4(aqdflg);
        if (this.conditionBytes != null && this.conditionBytes.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.conditionBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (this.nbExtensions > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.nbExtensions);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (this.connection.getTTCVersion() >= 14) {
            this.meg.marshalNULLPTR();
        }
        if (this.connection.getTTCVersion() >= 16) {
            this.meg.marshalUB4(reqShardNum);
        }
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalCHR(this.queueNameBytes);
        }
        if (this.consumerNameBytes != null && this.consumerNameBytes.length != 0) {
            this.meg.marshalCHR(this.consumerNameBytes);
        }
        if (sendMsgId) {
            this.meg.marshalB1Array(mesgId);
        }
        if (this.correlationBytes != null && this.correlationBytes.length != 0) {
            this.meg.marshalCHR(this.correlationBytes);
        }
        this.meg.marshalB1Array(this.payloadToid);
        if (this.conditionBytes != null && this.conditionBytes.length > 0) {
            this.meg.marshalCHR(this.conditionBytes);
        }
        if (this.nbExtensions > 0) {
            this.meg.marshalKPDKV(this.extensionTextValues, this.extensionBinaryValues, this.extensionKeywords);
        }
    }

    byte[] getPayload() {
        return this.payload;
    }

    boolean hasAMessageBeenDequeued() {
        return this.hasAMessageBeenDequeued;
    }

    byte[] getDequeuedMessageId() {
        return this.dequeuedMessageId;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.hasAMessageBeenDequeued = true;
        int msgPropLength = (int) this.meg.unmarshalUB4();
        if (msgPropLength > 0) {
            this.aqm.initToDefaultValues();
            this.aqm.receive();
            this.properties.setPriority(this.aqm.aqmpri);
            this.properties.setDelay(this.aqm.aqmdel);
            this.properties.setExpiration(this.aqm.aqmexp);
            if (this.aqm.aqmcorBytes != null) {
                String aqmcor = this.meg.conv.CharBytesToString(this.aqm.aqmcorBytes, this.aqm.aqmcorBytesLength, true);
                this.properties.setCorrelation(aqmcor);
            }
            this.properties.setAttempts(this.aqm.aqmatt);
            if (this.aqm.aqmeqnBytes != null) {
                String aqmeqn = this.meg.conv.CharBytesToString(this.aqm.aqmeqnBytes, this.aqm.aqmeqnBytesLength, true);
                this.properties.setExceptionQueue(aqmeqn);
            }
            this.properties.setMessageState(AQMessageProperties.MessageState.getMessageState(this.aqm.aqmsta));
            this.properties.setEnqueueTime(this.aqm.aqmeqt.timestampValue());
            AQAgentI senderAgent = new AQAgentI();
            if (this.aqm.senderAgentName != null) {
                senderAgent.setName(this.meg.conv.CharBytesToString(this.aqm.senderAgentName, this.aqm.senderAgentNameLength, true));
            }
            if (this.aqm.senderAgentAddress != null) {
                senderAgent.setAddress(this.meg.conv.CharBytesToString(this.aqm.senderAgentAddress, this.aqm.senderAgentAddressLength, true));
            }
            senderAgent.setProtocol(this.aqm.senderAgentProtocol);
            this.properties.setSender(senderAgent);
            this.properties.setPreviousQueueMessageId(this.aqm.originalMsgId);
            this.properties.setDeliveryMode(AQMessageProperties.DeliveryMode.getDeliveryMode(this.aqm.aqmflg));
            if (this.aqm.aqmetiBytes != null) {
                String aqmeti = this.meg.conv.CharBytesToString(this.aqm.aqmetiBytes, this.aqm.aqmetiBytes.length, true);
                this.properties.setTransactionGroup(aqmeti);
            }
        }
        this.toh.unmarshal(this.meg);
        int lengthOfPayload = this.toh.imageLength;
        if (lengthOfPayload > 0) {
            int bufferToAllocate = lengthOfPayload;
            if (this.isRawQueue || this.isJsonQueue) {
                if (lengthOfPayload > 4) {
                    bufferToAllocate -= 4;
                }
                byte[] image = new byte[Math.min(bufferToAllocate, this.dequeueOptions.getMaximumBufferLength())];
                int[] intAr = new int[1];
                if (lengthOfPayload > 4) {
                    this.meg.unmarshalCLR(image, 0, intAr, image.length, 4);
                } else {
                    this.meg.unmarshalCLR(image, 0, intAr, image.length);
                }
                this.payload = image;
            } else {
                byte[] image2 = new byte[bufferToAllocate];
                this.meg.unmarshalCLR(image2, 0, new int[1], image2.length);
                this.payload = image2;
            }
        }
        if (this.dequeueOptions.getRetrieveMessageId()) {
            byte[] aqdmsi = new byte[16];
            this.meg.unmarshalBuffer(aqdmsi, 0, 16);
            this.dequeuedMessageId = aqdmsi;
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processError() throws SQLException {
        if (this.connection.getT4CTTIoer().retCode != 25228) {
            this.connection.getT4CTTIoer().processError();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
