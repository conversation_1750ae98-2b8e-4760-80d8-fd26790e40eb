package oracle.jdbc.driver;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.SQLException;
import java.util.Vector;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIrxd.class */
class T4CTTIrxd extends T4CTTIMsg {
    private static final String CLASS_NAME;
    static final byte[] NO_BYTES;
    byte[] buffer;
    byte[] bufferCHAR;
    BitSet bvcColSent;
    int nbOfColumns;
    boolean bvcFound;
    int rowCount;
    static final byte TTICMD_UNAUTHORIZED = 1;
    private int[] indicesOfColumnsToBeCopied;
    static int call_count;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CTTIrxd.class.desiredAssertionStatus();
        CLASS_NAME = T4CTTIrxd.class.getName();
        NO_BYTES = new byte[0];
        call_count = 0;
    }

    T4CTTIrxd(T4CConnection _conn) {
        super(_conn, (byte) 7);
        this.bvcColSent = null;
        this.nbOfColumns = 0;
        this.bvcFound = false;
        this.rowCount = 0;
        this.indicesOfColumnsToBeCopied = null;
    }

    void init() {
        this.rowCount = 0;
    }

    void setNumberOfColumns(int nbCol) {
        this.nbOfColumns = nbCol;
        this.bvcFound = false;
        if (this.bvcColSent == null || this.bvcColSent.length() < this.nbOfColumns) {
            this.bvcColSent = new BitSet(this.nbOfColumns);
        } else {
            this.bvcColSent.clear();
        }
    }

    void unmarshalBVC(int nbOfColumnSent) throws SQLException, IOException {
        this.bvcColSent.clear();
        int nbOfUB1 = (this.nbOfColumns / 8) + (this.nbOfColumns % 8 != 0 ? 1 : 0);
        for (int ub1 = 0; ub1 < nbOfUB1; ub1++) {
            byte bvc = (byte) (this.meg.unmarshalUB1() & 255);
            int _ub1 = ub1;
            debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalBVC", "bvc byte: {0} value: {1}", (String) null, (Throwable) null, () -> {
                return new Object[]{Integer.valueOf(_ub1), Integer.toHexString(bvc & 255)};
            });
            this.bvcColSent.set(ub1, bvc);
        }
        this.bvcFound = true;
    }

    void readBitVector(byte[] bitVec, int length) throws SQLException, IOException {
        this.bvcColSent.clear();
        if (length == 0) {
            this.bvcFound = false;
            return;
        }
        for (int i = 0; i < length; i++) {
            byte bvc = bitVec[i];
            this.bvcColSent.set(i, bvc);
        }
        this.bvcFound = true;
    }

    Vector<IOException> marshal(byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bindIndicatorSubRange, byte[] byteBufferForCharConversion, DBConversion conversion, InputStream[] parameterStream, byte[] ibtBindBytes, char[] ibtBindChars, short[] ibtBindIndicators, byte[] ioVector, int rowId, int[] oacmxlArr, boolean isPlsql, int[] returnParamMeta, int[] nbPostPonedColumnsInOut, int[][] indexOfPostPonedColumnInOut, boolean sendFirstPostPonedColumnOnly, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, boolean bindUseDBA) throws IOException {
        if (bindUseDBA) {
            return marshalBindDBA(bindIndicators, bindIndicatorSubRange, parameterStream, ibtBindBytes, ibtBindChars, ibtBindIndicators, ioVector, rowId, oacmxlArr, isPlsql, returnParamMeta, nbPostPonedColumnsInOut, indexOfPostPonedColumnInOut, sendFirstPostPonedColumnOnly, bindData, bindDataOffsets, bindDataLengths);
        }
        return marshalPrimitive(bindBytes, bindChars, bindIndicators, bindIndicatorSubRange, byteBufferForCharConversion, conversion, parameterStream, ibtBindBytes, ibtBindChars, ibtBindIndicators, ioVector, rowId, oacmxlArr, isPlsql, returnParamMeta, nbPostPonedColumnsInOut, indexOfPostPonedColumnInOut, sendFirstPostPonedColumnOnly);
    }

    /* JADX WARN: Removed duplicated region for block: B:103:0x039e  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    java.util.Vector<java.io.IOException> marshalBindDBA(short[] r16, int r17, java.io.InputStream[] r18, byte[] r19, char[] r20, short[] r21, byte[] r22, int r23, int[] r24, boolean r25, int[] r26, int[] r27, int[][] r28, boolean r29, oracle.jdbc.driver.ByteArray r30, long[] r31, int[] r32) throws java.io.IOException {
        /*
            Method dump skipped, instructions count: 3011
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CTTIrxd.marshalBindDBA(short[], int, java.io.InputStream[], byte[], char[], short[], byte[], int, int[], boolean, int[], int[], int[][], boolean, oracle.jdbc.driver.ByteArray, long[], int[]):java.util.Vector");
    }

    /* JADX WARN: Removed duplicated region for block: B:77:0x02e2  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    java.util.Vector<java.io.IOException> marshalPrimitive(byte[] r16, char[] r17, short[] r18, int r19, byte[] r20, oracle.jdbc.driver.DBConversion r21, java.io.InputStream[] r22, byte[] r23, char[] r24, short[] r25, byte[] r26, int r27, int[] r28, boolean r29, int[] r30, int[] r31, int[][] r32, boolean r33) throws java.io.IOException {
        /*
            Method dump skipped, instructions count: 2680
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CTTIrxd.marshalPrimitive(byte[], char[], short[], int, byte[], oracle.jdbc.driver.DBConversion, java.io.InputStream[], byte[], char[], short[], byte[], int, int[], boolean, int[], int[], int[][], boolean):java.util.Vector");
    }

    boolean unmarshal(Accessor[] accessors, int definesLength) throws SQLException, IOException {
        return unmarshal(accessors, 0, definesLength);
    }

    void copyRowsAsNeeded(Accessor[] accessors, int definesLength) throws SQLException, IOException {
        if (this.rowCount == 1) {
            copyRowsAsNeededByOffset(accessors, definesLength);
            return;
        }
        int lastIndex = Math.min(definesLength, accessors.length);
        for (int colIndex = 0; colIndex < lastIndex; colIndex++) {
            Accessor acc = accessors[colIndex];
            if (!acc.isUseLess && !this.bvcColSent.get(acc.physicalColumnIndex)) {
                acc.copyRow();
            }
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIrxd$MinHeap.class */
    static class MinHeap {
        private final Accessor[] accessors;
        private final int[] heap;
        private int length;

        MinHeap(Accessor[] acc, int[] indices, int len) {
            this.accessors = acc;
            this.heap = indices;
            this.length = len;
            heapify();
        }

        private void heapify() {
            int node = (this.length - 2) / 2;
            for (int i = node; i >= 0; i--) {
                heapify(i);
            }
        }

        private void heapify(int start) {
            int i = start;
            while (true) {
                int current = i;
                int left = (2 * current) + 1;
                int right = (2 * current) + 2;
                int min = current;
                if (left < this.length && this.accessors[this.heap[left]].previousOffset() < this.accessors[this.heap[min]].previousOffset()) {
                    min = left;
                }
                if (right < this.length && this.accessors[this.heap[right]].previousOffset() < this.accessors[this.heap[min]].previousOffset()) {
                    min = right;
                }
                if (min != current) {
                    int tmp = this.heap[current];
                    this.heap[current] = this.heap[min];
                    this.heap[min] = tmp;
                    i = min;
                } else {
                    return;
                }
            }
        }

        int removeLeast() {
            int least = this.heap[0];
            int[] iArr = this.heap;
            int[] iArr2 = this.heap;
            int i = this.length - 1;
            this.length = i;
            iArr[0] = iArr2[i];
            heapify(0);
            return least;
        }

        public String toString() {
            String s = "<<" + this.length + ":";
            for (int i = 0; i < this.length; i++) {
                s = s + "" + this.heap[i] + "|" + this.accessors[this.heap[i]].previousOffset() + ", ";
            }
            return s + ">>";
        }
    }

    void copyRowsAsNeededByOffset(Accessor[] accessors, int definesLength) throws SQLException, IOException {
        int lastIndex = Math.min(definesLength, accessors.length);
        int colIndex = 0;
        while (colIndex < lastIndex) {
            Accessor acc = accessors[colIndex];
            if (!acc.isUseLess && !this.bvcColSent.get(acc.physicalColumnIndex)) {
                break;
            } else {
                colIndex++;
            }
        }
        if (colIndex < lastIndex) {
            if (this.indicesOfColumnsToBeCopied == null || this.indicesOfColumnsToBeCopied.length != this.nbOfColumns) {
                this.indicesOfColumnsToBeCopied = new int[this.nbOfColumns];
            }
            int numColumnsToBeCopied = 0;
            while (colIndex < lastIndex) {
                Accessor acc2 = accessors[colIndex];
                if (!acc2.isUseLess && !this.bvcColSent.get(acc2.physicalColumnIndex)) {
                    int i = numColumnsToBeCopied;
                    numColumnsToBeCopied++;
                    this.indicesOfColumnsToBeCopied[i] = colIndex;
                }
                colIndex++;
            }
            if (isLoggable(Level.FINEST)) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "copyRowsAsNeededByOffset", dumpIndicesOfColumnsToBeCopied(numColumnsToBeCopied, this.indicesOfColumnsToBeCopied, accessors), (String) null, (Throwable) null);
            }
            MinHeap heap = new MinHeap(accessors, this.indicesOfColumnsToBeCopied, numColumnsToBeCopied);
            while (numColumnsToBeCopied > 0) {
                accessors[heap.removeLeast()].copyRow();
                numColumnsToBeCopied--;
            }
        }
    }

    boolean unmarshal(Accessor[] accessors, int from_col, int definesLength) throws Exception {
        this.rowCount++;
        for (int colIndex = from_col; colIndex < definesLength && colIndex < accessors.length; colIndex++) {
            if (accessors[colIndex] != null && accessors[colIndex].physicalColumnIndex < 0) {
                int physicalIndex = 0;
                for (int j = 0; j < definesLength && j < accessors.length; j++) {
                    if (accessors[j] != null) {
                        accessors[j].physicalColumnIndex = physicalIndex;
                        if (!accessors[j].isUseLess) {
                            physicalIndex++;
                        }
                    }
                }
            }
        }
        if (this.bvcFound && from_col == 0) {
            copyRowsAsNeeded(accessors, definesLength);
        }
        for (int colIndex2 = from_col; colIndex2 < definesLength; colIndex2++) {
            try {
                if (colIndex2 >= accessors.length) {
                    break;
                }
                if (accessors[colIndex2] != null && (!this.bvcFound || accessors[colIndex2].isUseLess || this.bvcColSent.get(accessors[colIndex2].physicalColumnIndex))) {
                    if (accessors[colIndex2].statement.statementType == 2 || accessors[colIndex2].statement.sqlKind.isPlsqlOrCall()) {
                        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshal", "out binds only have one row", (String) null, (Throwable) null);
                        accessors[colIndex2].setCapacity(1);
                    }
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshal", "unmarshal call unmarshalOneRow", (String) null, (Throwable) null);
                    if (accessors[colIndex2].unmarshalOneRow()) {
                        return true;
                    }
                }
            } catch (Exception e) {
                throw e;
            }
        }
        this.bvcFound = false;
        return false;
    }

    String dumpIndicesOfColumnsToBeCopied(int numColumnsToBeCopied, int[] indicesOfColumnsToBeCopied, Accessor[] accessors) throws SQLException {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        StringBuilder sbAppend = new StringBuilder().append("dump indicesOfColumnsToBeCopied call_count: ");
        int i = call_count;
        call_count = i + 1;
        pw.println(sbAppend.append(i).append(" numColumnsToBeCopied: ").append(numColumnsToBeCopied).toString());
        for (int kkk = 0; kkk < numColumnsToBeCopied; kkk++) {
            long o = accessors[indicesOfColumnsToBeCopied[kkk]].getOffset(0);
            pw.println("copy order: " + kkk + " index: " + indicesOfColumnsToBeCopied[kkk] + " offset: " + o);
        }
        pw.println();
        return sw.toString();
    }

    boolean unmarshal(Accessor[] accessors, int row_number, int from_col, int to_col) throws SQLException, IOException {
        return false;
    }

    @Override // oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIrxd$BitSet.class */
    private static final class BitSet {
        private static final int[] SET_MASK = {1, 2, 4, 8, 16, 32, 64, 128};
        private static final int[] CLEAR_MASK = {DatabaseError.EOJ_OUT_OF_MEMORY_ERROR, DatabaseError.EOJ_INVALID_NAME_FOR_CLIENTINFO, 251, 247, CharacterSet.WE8BS2000L5_CHARSET, 223, 191, 127};
        private final int capacity;
        private final byte[] bits;

        BitSet(int size) {
            this.bits = new byte[(size + 7) / 8];
            this.capacity = this.bits.length * 8;
        }

        final int length() {
            return this.capacity;
        }

        final void set(int bit) {
            byte[] bArr = this.bits;
            int i = bit / 8;
            bArr[i] = (byte) (bArr[i] | SET_MASK[bit % 8]);
        }

        final void set(int byteIndex, byte eightBits) {
            this.bits[byteIndex] = eightBits;
        }

        final void clear(int bit) {
            byte[] bArr = this.bits;
            int i = bit / 8;
            bArr[i] = (byte) (bArr[i] & CLEAR_MASK[bit % 8]);
        }

        final boolean get(int bit) {
            return (this.bits[bit / 8] & SET_MASK[bit % 8]) > 0;
        }

        final void clear() {
            for (int i = 0; i < this.bits.length; i++) {
                this.bits[i] = 0;
            }
        }
    }
}
