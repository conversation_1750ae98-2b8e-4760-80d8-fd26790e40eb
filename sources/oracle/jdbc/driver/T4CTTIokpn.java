package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIokpn.class */
final class T4CTTIokpn extends T4CTTIfun {
    static final int REGISTER_KPNDEF = 1;
    static final int UNREGISTER_KPNDEF = 2;
    static final int POST_KPNDEF = 3;
    static final int ENABLE_KPNDEF = 4;
    static final int DISABLE_KPNDEF = 5;
    static final int EXISTINGCLIENT_KPNDEF = 0;
    static final int NEWCLIENT_KPNDEF = 1;
    static final int CLIENTCON_KPNDEF = 4;
    static final int KPUN_PRS_RAW = 1;
    static final int KPUN_VER_10200 = 2;
    static final int KPUN_VER_11100 = 3;
    static final int KPUN_VER_11200 = 4;
    static final int KPUN_VER_12100 = 6;
    static final int OCI_SUBSCR_NAMESPACE_ANONYMOUS = 0;
    static final int OCI_SUBSCR_NAMESPACE_AQ = 1;
    static final int OCI_SUBSCR_NAMESPACE_DBCHANGE = 2;
    static final int OCI_SUBSCR_NAMESPACE_MAX = 3;
    static final int KPD_CHNF_OPFILTER = 1;
    static final int KPD_CHNF_INSERT = 2;
    static final int KPD_CHNF_UPDATE = 4;
    static final int KPD_CHNF_DELETE = 8;
    static final int KPD_CHNF_ROWID = 16;
    static final int KPD_CQ_QUERYNF = 32;
    static final int KPD_CQ_BEST_EFFORT = 64;
    static final int KPD_CQ_CLQRYCACHE = 128;
    static final int KPD_CHNF_INVALID_REGID = 0;
    static final int KPD_NTFN_CONNID_LEN = 29;
    static final int KKCN_CTX_RAW = 0;
    static final int SUBSCR_QOS_RELIABLE = 1;
    static final int SUBSCR_QOS_PAYLOAD = 2;
    static final int SUBSCR_QOS_REPLICATE = 4;
    static final int SUBSCR_QOS_SECURE = 8;
    static final int SUBSCR_QOS_PURGE_ON_NTFN = 16;
    static final int SUBSCR_QOS_MULTICBK = 32;
    static final int SUBSCR_QOS_ASYNC_DEQ = 512;
    static final int SUBSCR_QOS_TX_ACK = 2048;
    static final int SUBSCR_QOS_AUTO_ACK = 1024;
    static final byte SUBSCR_NTFN_GROUPING_CLASS_NONE = 0;
    static final byte SUBSCR_NTFN_GROUPING_CLASS_TIME = 1;
    static final byte SUBSCR_NTFN_GROUPING_TYPE_SUMMARY = 1;
    static final byte SUBSCR_NTFN_GROUPING_TYPE_LAST = 2;
    private int opcode;
    private int mode;
    private int nbOfRegistrationInfo;
    private long reregid;
    private String jmsConnectionId;
    private String[] databaseInstances;
    private int[] namespace;
    private int[] kpdnrgrpval;
    private int[] kpdnrgrprepcnt;
    private int[] payloadType;
    private int[] qosFlags;
    private int[] timeout;
    private int[] dbchangeOpFilter;
    private int[] dbchangeTxnLag;
    private byte[] kpncid;
    private byte[][] registeredAgentName;
    private byte[][] kpdnrcx;
    private byte[] kpdnrgrpcla;
    private byte[] kpdnrgrptyp;
    private TIMESTAMPTZ[] kpdnrgrpstatim;
    private long[] dbchangeRegistrationId;
    private byte[] userArr;
    private byte[] locationArr;
    private byte[] subscriberName;
    private byte[] selectorByteArray;
    private long regid;
    private long[] registrationId;
    ArrayList<String> listenerAddresses;
    T4CTTIkpdnrri kpninst;
    T4CTTIkpdnrgnc kpngcret;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CTTIokpn.class.desiredAssertionStatus();
    }

    T4CTTIokpn(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.reregid = -1L;
        this.jmsConnectionId = null;
        this.databaseInstances = null;
        this.dbchangeTxnLag = null;
        this.kpncid = null;
        this.registeredAgentName = (byte[][]) null;
        this.kpdnrcx = (byte[][]) null;
        this.kpdnrgrptyp = null;
        this.kpdnrgrpstatim = null;
        this.dbchangeRegistrationId = null;
        this.userArr = null;
        this.locationArr = null;
        this.subscriberName = null;
        this.selectorByteArray = null;
        this.regid = 0L;
        this.registrationId = null;
        this.listenerAddresses = new ArrayList<>();
        this.kpninst = null;
        this.kpngcret = null;
        setFunCode((short) 125);
    }

    void doOKPN(int _opcode, int _mode, String user, String location, int _nbOfRegistrationInfo, int[] _namespace, String[] _registeredAgentName, byte[][] _kpdnrcx, int[] _payloadType, int[] _qosFlags, int[] _timeout, int[] _dbchangeOpFilter, int[] _dbchangeTxnLag, long[] _dbchangeRegistrationId, byte[] _kpdnrgrpcla, int[] _kpdnrgrpval, byte[] _kpdnrgrptyp, TIMESTAMPTZ[] _kpdnrgrpstatim, int[] _kpdnrgrprepcnt, long[] _kpdnrregid) throws SQLException, IOException {
        doOKPN(_opcode, _mode, user, location, _nbOfRegistrationInfo, _namespace, _registeredAgentName, _kpdnrcx, _payloadType, _qosFlags, _timeout, _dbchangeOpFilter, _dbchangeTxnLag, _dbchangeRegistrationId, _kpdnrgrpcla, _kpdnrgrpval, _kpdnrgrptyp, _kpdnrgrpstatim, _kpdnrgrprepcnt, _kpdnrregid, null);
    }

    /* JADX WARN: Type inference failed for: r1v21, types: [byte[], byte[][]] */
    void doOKPN(int _opcode, int _mode, String user, String location, int _nbOfRegistrationInfo, int[] _namespace, String[] _registeredAgentName, byte[][] _kpdnrcx, int[] _payloadType, int[] _qosFlags, int[] _timeout, int[] _dbchangeOpFilter, int[] _dbchangeTxnLag, long[] _dbchangeRegistrationId, byte[] _kpdnrgrpcla, int[] _kpdnrgrpval, byte[] _kpdnrgrptyp, TIMESTAMPTZ[] _kpdnrgrpstatim, int[] _kpdnrgrprepcnt, long[] _kpdnrregid, String selector) throws SQLException, IOException {
        this.opcode = _opcode;
        this.mode = _mode;
        this.nbOfRegistrationInfo = _nbOfRegistrationInfo;
        this.namespace = _namespace;
        this.kpdnrcx = _kpdnrcx;
        this.payloadType = _payloadType;
        this.qosFlags = _qosFlags;
        this.timeout = _timeout;
        this.dbchangeOpFilter = _dbchangeOpFilter;
        if (this.dbchangeOpFilter == null) {
            this.dbchangeOpFilter = new int[this.nbOfRegistrationInfo];
        }
        this.dbchangeTxnLag = _dbchangeTxnLag;
        if (this.dbchangeTxnLag == null) {
            this.dbchangeTxnLag = new int[this.nbOfRegistrationInfo];
        }
        this.dbchangeRegistrationId = _dbchangeRegistrationId;
        if (this.dbchangeRegistrationId == null) {
            this.dbchangeRegistrationId = new long[this.nbOfRegistrationInfo];
        }
        this.kpdnrgrpcla = _kpdnrgrpcla;
        if (this.kpdnrgrpcla == null) {
            this.kpdnrgrpcla = new byte[this.nbOfRegistrationInfo];
        }
        this.kpdnrgrpval = _kpdnrgrpval;
        if (this.kpdnrgrpval == null) {
            this.kpdnrgrpval = new int[this.nbOfRegistrationInfo];
        }
        this.kpdnrgrptyp = _kpdnrgrptyp;
        if (this.kpdnrgrptyp == null) {
            this.kpdnrgrptyp = new byte[this.nbOfRegistrationInfo];
        }
        this.kpdnrgrpstatim = _kpdnrgrpstatim;
        if (this.kpdnrgrpstatim == null) {
            this.kpdnrgrpstatim = new TIMESTAMPTZ[this.nbOfRegistrationInfo];
        }
        this.kpdnrgrprepcnt = _kpdnrgrprepcnt;
        if (this.kpdnrgrprepcnt == null) {
            this.kpdnrgrprepcnt = new int[this.nbOfRegistrationInfo];
        }
        this.registrationId = _kpdnrregid;
        if (this.registrationId == null) {
            this.registrationId = new long[this.nbOfRegistrationInfo];
        }
        if (this.registrationId[0] != 0) {
            this.reregid = this.registrationId[0];
        }
        this.registeredAgentName = new byte[this.nbOfRegistrationInfo];
        for (int i = 0; i < this.nbOfRegistrationInfo; i++) {
            if (_registeredAgentName[i] != null) {
                this.registeredAgentName[i] = this.meg.conv.StringToCharBytes(_registeredAgentName[i]);
            }
        }
        if (user != null) {
            this.userArr = this.meg.conv.StringToCharBytes(user);
        } else {
            this.userArr = null;
        }
        if (location != null) {
            this.locationArr = this.meg.conv.StringToCharBytes(location);
        } else {
            this.locationArr = null;
        }
        if (selector != null) {
            this.selectorByteArray = this.meg.conv.StringToCharBytes(selector);
        } else {
            this.selectorByteArray = null;
        }
        this.regid = 0L;
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB1((byte) this.opcode);
        this.meg.marshalUB4(this.mode);
        if (this.userArr != null) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.userArr.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.locationArr != null) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.locationArr.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalPTR();
        this.meg.marshalUB4(this.nbOfRegistrationInfo);
        this.meg.marshalUB2(1);
        this.meg.marshalUB2(6);
        if (this.connection.getTTCVersion() >= 4) {
            this.meg.marshalNULLPTR();
            this.meg.marshalPTR();
            if (this.connection.getTTCVersion() >= 5) {
                this.meg.marshalNULLPTR();
                this.meg.marshalPTR();
                if (this.connection.getTTCVersion() >= 7) {
                    this.meg.marshalPTR();
                    this.meg.marshalPTR();
                    this.meg.marshalPTR();
                    this.meg.marshalPTR();
                    this.meg.marshalPTR();
                    this.meg.marshalSWORD(29);
                    this.meg.marshalPTR();
                }
            }
        }
        if (this.userArr != null) {
            this.meg.marshalCHR(this.userArr);
        }
        if (this.locationArr != null) {
            this.meg.marshalCHR(this.locationArr);
        }
        if (!$assertionsDisabled && (this.namespace == null || this.registeredAgentName == null || this.payloadType == null || this.qosFlags == null || this.timeout == null)) {
            throw new AssertionError(" namespace : " + this.namespace + ", registeredAgentName : " + this.registeredAgentName + ", payloadType : " + this.payloadType + ", qosFlags : " + this.qosFlags + ", timeout : " + this.timeout);
        }
        if (!$assertionsDisabled && (this.namespace.length != this.nbOfRegistrationInfo || this.registeredAgentName.length != this.nbOfRegistrationInfo || this.payloadType.length != this.nbOfRegistrationInfo || this.qosFlags.length != this.nbOfRegistrationInfo || this.timeout.length != this.nbOfRegistrationInfo)) {
            throw new AssertionError(" namespace.length = " + this.namespace.length + ", registeredAgentName.length = " + this.registeredAgentName.length + ", qosFlags.length = " + this.qosFlags.length + ", timeout.length = " + this.timeout.length);
        }
        for (int i = 0; i < this.nbOfRegistrationInfo; i++) {
            this.meg.marshalUB4(this.namespace[i]);
            byte[] kpdnrnm = this.registeredAgentName[i];
            if (kpdnrnm != null && kpdnrnm.length > 0) {
                this.meg.marshalUB4(kpdnrnm.length);
                this.meg.marshalCLR(kpdnrnm, 0, kpdnrnm.length);
            } else {
                this.meg.marshalUB4(0L);
            }
            if (this.kpdnrcx != null && this.kpdnrcx[i] != null && this.kpdnrcx[i].length > 0) {
                this.meg.marshalUB4(this.kpdnrcx[i].length);
                this.meg.marshalCLR(this.kpdnrcx[i], 0, this.kpdnrcx[i].length);
            } else {
                this.meg.marshalUB4(0L);
            }
            this.meg.marshalUB4(this.payloadType[i]);
            if (this.connection.getTTCVersion() >= 4) {
                this.meg.marshalUB4(this.qosFlags[i]);
                if (this.selectorByteArray == null || this.selectorByteArray.length == 0) {
                    this.meg.marshalUB4(0L);
                } else {
                    this.meg.marshalUB4(this.selectorByteArray.length);
                    this.meg.marshalCLR(this.selectorByteArray, this.selectorByteArray.length);
                }
                this.meg.marshalUB4(this.timeout[i]);
                this.meg.marshalUB4(0);
                this.meg.marshalUB4(this.dbchangeOpFilter[i]);
                this.meg.marshalUB4(this.dbchangeTxnLag[i]);
                this.meg.marshalUB4((int) this.dbchangeRegistrationId[i]);
                if (this.connection.getTTCVersion() >= 5) {
                    this.meg.marshalUB1(this.kpdnrgrpcla[i]);
                    this.meg.marshalUB4(this.kpdnrgrpval[i]);
                    this.meg.marshalUB1(this.kpdnrgrptyp[i]);
                    if (this.kpdnrgrpstatim[i] == null) {
                        this.meg.marshalDALC(null);
                    } else {
                        this.meg.marshalDALC(this.kpdnrgrpstatim[i].shareBytes());
                    }
                    this.meg.marshalSB4(this.kpdnrgrprepcnt[i]);
                    this.meg.marshalSB8(this.registrationId[i]);
                }
            }
        }
    }

    long getRegistrationId() {
        return this.regid;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int kpngnnosl;
        int kpnrl = (int) this.meg.unmarshalUB4();
        for (int i = 0; i < kpnrl; i++) {
            this.meg.unmarshalUB4();
        }
        int[] regidArr = new int[kpnrl];
        for (int i2 = 0; i2 < kpnrl; i2++) {
            regidArr[i2] = (int) this.meg.unmarshalUB4();
        }
        this.regid = regidArr[0];
        if (this.connection.getTTCVersion() >= 5) {
            int kpngrl = (int) this.meg.unmarshalUB4();
            this.registrationId = new long[kpngrl];
            for (int j = 0; j < kpngrl; j++) {
                this.registrationId[j] = this.meg.unmarshalSB8();
                if (this.connection.getTTCVersion() >= 7 && (kpngnnosl = (int) this.meg.unmarshalUB4()) > 0) {
                    this.subscriberName = new byte[kpngnnosl];
                    int[] intAr = new int[1];
                    this.meg.unmarshalCLR(this.subscriberName, 0, intAr, kpngnnosl);
                }
            }
            if (this.registrationId[0] == 0 && this.reregid != -1) {
                this.registrationId[0] = this.reregid;
            }
            this.regid = this.registrationId[0];
            if (this.connection.getTTCVersion() >= 7) {
                int kpninstl = (int) this.meg.unmarshalUB4();
                this.databaseInstances = new String[kpninstl];
                for (int i3 = 0; i3 < kpninstl; i3++) {
                    this.kpninst = new T4CTTIkpdnrri(this.connection);
                    this.kpninst.receive();
                    byte[] kpdnrrinm = this.kpninst.getKpdnrrinm();
                    if (kpdnrrinm != null) {
                        this.databaseInstances[i3] = this.meg.conv.CharBytesToString(kpdnrrinm, kpdnrrinm.length);
                    }
                }
                int kpngcretl = (int) this.meg.unmarshalUB4();
                this.listenerAddresses = new ArrayList<>();
                for (int i4 = 0; i4 < kpngcretl; i4++) {
                    this.kpngcret = new T4CTTIkpdnrgnc(this.connection);
                    this.kpngcret.receive();
                    byte[] kpdnrgnclsc = this.kpngcret.getKpdnrgnclsc();
                    if (kpdnrgnclsc != null) {
                        String listenerAddress = this.meg.conv.CharBytesToString(kpdnrgnclsc, kpdnrgnclsc.length);
                        this.listenerAddresses.add(listenerAddress);
                    }
                }
                int kpncidl = this.meg.unmarshalUB2();
                if (kpncidl > 0) {
                    this.kpncid = new byte[kpncidl];
                    this.kpncid = this.meg.unmarshalCHR(kpncidl);
                    if (this.kpncid != null) {
                        this.jmsConnectionId = this.meg.conv.CharBytesToString(this.kpncid, this.kpncid.length);
                    }
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }

    ArrayList<String> getListenerAddresses() {
        return this.listenerAddresses;
    }

    String getListenerAddress() throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    long[] getRegistrationIdArray() {
        return this.registrationId;
    }

    String getJMSConnectionId() {
        return this.jmsConnectionId;
    }

    String[] getDatabaseInstances() {
        return this.databaseInstances;
    }
}
