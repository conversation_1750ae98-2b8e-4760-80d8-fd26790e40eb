package oracle.jdbc.driver;

import java.util.Arrays;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ReplayContext.class */
class ReplayContext implements oracle.jdbc.internal.ReplayContext {
    long flags_kpdxcAppContCtl;
    short queue_kpdxcAppContCtl;
    byte[] replayctx_kpdxcAppContCtl;
    long errcode_kpdxcAppContCtl;

    ReplayContext(long flags_kpdxcAppContCtl, short queue_kpdxcAppContCtl, byte[] replayctx_kpdxcAppContCtl, long errcode_kpdxcAppContCtl) {
        this.flags_kpdxcAppContCtl = flags_kpdxcAppContCtl;
        this.queue_kpdxcAppContCtl = queue_kpdxcAppContCtl;
        this.errcode_kpdxcAppContCtl = errcode_kpdxcAppContCtl;
        this.replayctx_kpdxcAppContCtl = replayctx_kpdxcAppContCtl;
    }

    @Override // oracle.jdbc.internal.ReplayContext
    public byte[] getContext() {
        return this.replayctx_kpdxcAppContCtl;
    }

    @Override // oracle.jdbc.internal.ReplayContext
    public short getQueue() {
        return this.queue_kpdxcAppContCtl;
    }

    @Override // oracle.jdbc.internal.ReplayContext
    public long getDirectives() {
        return this.flags_kpdxcAppContCtl;
    }

    @Override // oracle.jdbc.internal.ReplayContext
    public long getErrorCode() {
        return this.errcode_kpdxcAppContCtl;
    }

    private String getDirectivesAsString() {
        String ret = "[0";
        if ((this.flags_kpdxcAppContCtl & 1) == 1) {
            ret = ret + "|DIRECTIVE_ENQUEUE_CALL";
        }
        if ((this.flags_kpdxcAppContCtl & 2) == 2) {
            ret = ret + "|DIRECTIVE_SESSSTATE_STABLE_CRSR";
        }
        if ((this.flags_kpdxcAppContCtl & 4) == 4) {
            ret = ret + "|DIRECTIVE_REPLAY_ENABLED";
        }
        if ((this.flags_kpdxcAppContCtl & 8) == 8) {
            ret = ret + "|DIRECTIVE_EMPTY_QUEUE";
        }
        return ret + "]";
    }

    boolean isDuplicate(ReplayContext cxt) {
        return cxt != null && this.flags_kpdxcAppContCtl == cxt.flags_kpdxcAppContCtl && this.queue_kpdxcAppContCtl == cxt.queue_kpdxcAppContCtl && this.errcode_kpdxcAppContCtl == cxt.errcode_kpdxcAppContCtl && Arrays.equals(this.replayctx_kpdxcAppContCtl, cxt.replayctx_kpdxcAppContCtl);
    }

    public String toString() {
        return "ReplayContext[Directives=" + getDirectivesAsString() + ",Queue=" + ((int) this.queue_kpdxcAppContCtl) + ",ErrorCode=" + this.errcode_kpdxcAppContCtl + ",Context=" + this.replayctx_kpdxcAppContCtl + "]";
    }
}
