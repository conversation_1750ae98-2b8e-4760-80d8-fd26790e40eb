package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CBfileAccessor.class */
class T4CBfileAccessor extends B<PERSON>leAccessor {
    T4CMAREngine mare;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CBfileAccessor.class.desiredAssertionStatus();
    }

    T4CBfileAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, 530, form, external_type, isOutBind, false);
        this.mare = _mare;
    }

    T4CBfileAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, 530, nullable, flags, precision, scale, contflag, total_elems, form);
        this.mare = _mare;
        this.definedColumnType = _definedColumnType;
        this.definedColumnSize = _definedColumnSize;
    }

    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        boolean isStream = false;
        if (!isUseless()) {
            if (isUnexpected()) {
                long pos = this.rowData.getPosition();
                unmarshalColumnMetadata();
                unmarshalBytes();
                this.rowData.setPosition(pos);
                setNull(this.lastRowProcessed, true);
            } else if (isNullByDescribe()) {
                setNull(this.lastRowProcessed, true);
                unmarshalColumnMetadata();
                if (this.statement.connection.versionNumber < 9200) {
                    processIndicator(0);
                }
            } else {
                unmarshalColumnMetadata();
                isStream = unmarshalBytes();
            }
        }
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
        return isStream;
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            long previousOffset = getOffset(this.previousRowProcessed);
            long previousPrefetchedDataOffset = getPrefetchedDataOffset(this.previousRowProcessed);
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            if (!isNull(this.previousRowProcessed)) {
                if (isPrefetched()) {
                    setPrefetchedLength(this.lastRowProcessed, getPrefetchedLength(this.previousRowProcessed));
                    setPrefetchedChunkSize(this.lastRowProcessed, getPrefetchedChunkSize(this.previousRowProcessed));
                    setPrefetchedDataLength(this.lastRowProcessed, getPrefetchedDataLength(this.previousRowProcessed));
                    setPrefetchedDataOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousPrefetchedDataOffset, getPrefetchedDataLength(this.previousRowProcessed));
                }
                setOffset(this.lastRowProcessed);
                this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
            }
        } else {
            this.previousRowProcessed = this.lastRowProcessed - 1;
            if (isPrefetched()) {
                setPrefetchedLength(this.lastRowProcessed, getPrefetchedLength(this.previousRowProcessed));
                setPrefetchedChunkSize(this.lastRowProcessed, getPrefetchedChunkSize(this.previousRowProcessed));
                setPrefetchedDataLength(this.lastRowProcessed, getPrefetchedDataLength(this.previousRowProcessed));
                setPrefetchedDataOffset(this.lastRowProcessed, getPrefetchedDataOffset(this.previousRowProcessed));
            }
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    boolean unmarshalBytes() throws SQLException, IOException {
        int len = (int) this.mare.unmarshalUB4();
        if (len == 0) {
            setNull(this.lastRowProcessed, true);
            processIndicator(0);
            return false;
        }
        if (isPrefetched()) {
            unmarshalPrefetchData();
        }
        setOffset(this.lastRowProcessed);
        int actualLength = ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare);
        setNull(this.lastRowProcessed, actualLength == 0);
        setLength(this.lastRowProcessed, actualLength);
        processIndicator(actualLength);
        return false;
    }

    void unmarshalPrefetchData() throws SQLException, IOException {
        setPrefetchedLength(this.lastRowProcessed, this.mare.unmarshalSB8());
        setPrefetchedDataOffset(this.lastRowProcessed);
        setPrefetchedDataLength(this.lastRowProcessed, 0);
        if (getPrefetchLength() > 0) {
            this.mare.unmarshalUB1();
        }
    }

    @Override // oracle.jdbc.driver.BfileAccessor, oracle.jdbc.driver.LobCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getObject(currentRow);
        }
        if (isNull(currentRow)) {
            return null;
        }
        if (this.definedColumnType == -13) {
            return getBFILE(currentRow);
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new LobCommonAccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CBfileAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CBfileAccessor(stmt, T4CBfileAccessor.this.describeMaxLength, T4CBfileAccessor.this.nullable, -1, T4CBfileAccessor.this.precision, T4CBfileAccessor.this.scale, T4CBfileAccessor.this.contflag, -1, T4CBfileAccessor.this.formOfUse, T4CBfileAccessor.this.definedColumnType, T4CBfileAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
