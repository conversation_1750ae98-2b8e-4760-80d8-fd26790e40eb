package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CJsonAccessor.class */
class T4CJsonAccessor extends JsonAccessor {
    T4CMAREngine mare;
    final int[] meta;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CJsonAccessor.class.desiredAssertionStatus();
    }

    T4CJsonAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, 4000, form, external_type, isOutBind, false);
        this.meta = new int[1];
        this.mare = _mare;
    }

    T4CJsonAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, 4000, nullable, flags, precision, scale, contflag, total_elems, form);
        this.meta = new int[1];
        this.mare = _mare;
        this.definedColumnType = _definedColumnType;
        this.definedColumnSize = _definedColumnSize;
    }

    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        boolean isStream = false;
        if (!isUseless()) {
            if (isUnexpected()) {
                long pos = this.rowData.getPosition();
                unmarshalColumnMetadata();
                unmarshalBytes();
                this.rowData.setPosition(pos);
                setNull(this.lastRowProcessed, true);
            } else if (isNullByDescribe()) {
                setNull(this.lastRowProcessed, true);
                unmarshalColumnMetadata();
                if (this.statement.connection.versionNumber < 9200) {
                    processIndicator(0);
                }
            } else {
                unmarshalColumnMetadata();
                isStream = unmarshalBytes();
            }
        }
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
        return isStream;
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.Accessor
    byte[] getBytesInternal(int currentRow) throws SQLException {
        if (isNull(currentRow)) {
            return null;
        }
        if (!isPrefetched()) {
            byte[] locator = super.getBytesInternal(currentRow);
            return ((T4CConnection) this.statement.connection).getOsonBytes(locator, 1L);
        }
        long dataLength = getPrefetchedLength(currentRow);
        if (dataLength > 2147483647L) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_OSON_TOO_LARGE).fillInStackTrace());
        }
        long prefetchedLength = getPrefetchedDataLength(currentRow);
        if (prefetchedLength == dataLength) {
            return getPrefetchedData(currentRow);
        }
        byte[] lobData = new byte[(int) dataLength];
        this.rowData.get(getPrefetchedDataOffset(currentRow), lobData, 0, (int) prefetchedLength);
        byte[] locator2 = super.getBytesInternal(currentRow);
        int lobOffset = (int) (1 + prefetchedLength);
        ((T4CConnection) this.statement.connection).getOsonBytes(locator2, lobOffset, (int) (dataLength - prefetchedLength), lobData, (int) prefetchedLength);
        return lobData;
    }

    boolean unmarshalBytes() throws SQLException, IOException {
        int len = (int) this.mare.unmarshalUB4();
        if (len == 0) {
            setNull(this.lastRowProcessed, true);
            processIndicator(0);
            return false;
        }
        if (isPrefetched()) {
            unmarshalPrefetchData();
        }
        setOffset(this.lastRowProcessed);
        int actualLength = ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare);
        setNull(this.lastRowProcessed, actualLength == 0);
        setLength(this.lastRowProcessed, actualLength);
        processIndicator(actualLength);
        return false;
    }

    void unmarshalPrefetchData() throws SQLException, IOException {
        setPrefetchedLength(this.lastRowProcessed, this.mare.unmarshalSB8());
        setPrefetchedChunkSize(this.lastRowProcessed, (int) this.mare.unmarshalUB4());
        setPrefetchedDataOffset(this.lastRowProcessed);
        if (getPrefetchLength() > 0) {
            setPrefetchedDataLength(this.lastRowProcessed, ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare));
        } else {
            setPrefetchedDataLength(this.lastRowProcessed, 0);
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new LobCommonAccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CJsonAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CJsonAccessor(stmt, T4CJsonAccessor.this.describeMaxLength, T4CJsonAccessor.this.nullable, -1, T4CJsonAccessor.this.precision, T4CJsonAccessor.this.scale, T4CJsonAccessor.this.contflag, -1, T4CJsonAccessor.this.formOfUse, T4CJsonAccessor.this.definedColumnType, T4CJsonAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
