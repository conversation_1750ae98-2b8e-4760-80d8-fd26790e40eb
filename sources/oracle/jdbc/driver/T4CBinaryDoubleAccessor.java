package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CBinaryDoubleAccessor.class */
class T4CBinaryDoubleAccessor extends BinaryDoubleAccessor {
    T4CMAREngine mare;
    boolean underlyingLongRaw;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CBinaryDoubleAccessor.class.desiredAssertionStatus();
    }

    T4CBinaryDoubleAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, form, external_type, isOutBind, false);
        this.underlyingLongRaw = false;
        this.mare = _mare;
    }

    T4CBinaryDoubleAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len == -1 ? _definedColumnSize : max_len, nullable, flags, precision, scale, contflag, total_elems, form);
        this.underlyingLongRaw = false;
        this.mare = _mare;
        if (stmt != null && stmt.implicitDefineForLobPrefetchDone) {
            this.definedColumnType = 0;
            this.definedColumnSize = 0;
        } else {
            this.definedColumnType = _definedColumnType;
            this.definedColumnSize = _definedColumnSize;
        }
        if (max_len == -1) {
            this.underlyingLongRaw = true;
        }
    }

    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        boolean isStream = false;
        if (!isUseless()) {
            if (isUnexpected()) {
                long pos = this.rowData.getPosition();
                unmarshalColumnMetadata();
                unmarshalBytes();
                this.rowData.setPosition(pos);
                setNull(this.lastRowProcessed, true);
            } else if (isNullByDescribe()) {
                setNull(this.lastRowProcessed, true);
                unmarshalColumnMetadata();
                if (this.statement.connection.versionNumber < 9200) {
                    processIndicator(0);
                }
            } else {
                unmarshalColumnMetadata();
                isStream = unmarshalBytes();
            }
        }
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
        return isStream;
    }

    boolean unmarshalBytes() throws SQLException, IOException {
        int len;
        setOffset(this.lastRowProcessed);
        if (this.statement.maxFieldSize > 0) {
            len = ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare, this.statement.maxFieldSize);
        } else {
            len = ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare);
        }
        processIndicator(len);
        setLength(this.lastRowProcessed, len);
        setNull(this.lastRowProcessed, len == 0);
        return false;
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.BinaryDoubleAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getObject(currentRow);
        }
        if (isNull(currentRow)) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case -1:
            case 1:
            case 12:
                return getString(currentRow);
            case oracle.jdbc.OracleTypes.TINYINT /* -6 */:
                return Byte.valueOf(getByte(currentRow));
            case -5:
                return Long.valueOf(getLong(currentRow));
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
            case -3:
            case -2:
                return getBytes(currentRow);
            case 2:
            case 3:
                return getBigDecimal(currentRow);
            case 4:
                return Integer.valueOf(getInt(currentRow));
            case 5:
                return Short.valueOf(getShort(currentRow));
            case 6:
            case 8:
                return Double.valueOf(getDouble(currentRow));
            case 7:
                return Float.valueOf(getFloat(currentRow));
            case 101:
                return getBINARY_DOUBLE(currentRow);
            default:
                throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4);
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new AccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CBinaryDoubleAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CBinaryDoubleAccessor(stmt, T4CBinaryDoubleAccessor.this.describeMaxLength, T4CBinaryDoubleAccessor.this.nullable, -1, T4CBinaryDoubleAccessor.this.precision, T4CBinaryDoubleAccessor.this.scale, T4CBinaryDoubleAccessor.this.contflag, -1, T4CBinaryDoubleAccessor.this.formOfUse, T4CBinaryDoubleAccessor.this.definedColumnType, T4CBinaryDoubleAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
