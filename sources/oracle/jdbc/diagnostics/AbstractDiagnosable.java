package oracle.jdbc.diagnostics;

import java.io.FileInputStream;
import java.io.IOException;
import java.security.AccessController;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.logging.LogManager;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.clio.annotations.Sensitive;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.driver.DatabaseError;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/AbstractDiagnosable.class */
public abstract class AbstractDiagnosable implements Diagnosable {
    protected Diagnostic diagnostic;
    private int suspendCount = 0;
    public static final Predicate<String> IS_VALID_BUFFER_SIZE;
    protected static final Predicate<String> IS_VALID_BOOLEAN_STRING;
    protected static final Predicate<String> IS_VALID_LOGGER_NAME;
    private static volatile boolean isGlobalDebugEnabled;
    private static volatile boolean isWriteLogsToDiagnoseFirstFailure;
    private static AtomicBoolean isMetricsEnabled;
    static final /* synthetic */ boolean $assertionsDisabled;

    protected abstract void setDebugEnabled(boolean z);

    protected abstract void setDiagnoseFirstFailureEnabled(boolean z);

    protected abstract boolean isSensitivePermitted();

    protected abstract String getDiagnosticLoggerName();

    protected abstract TraceAttributes getTraceAttributes();

    static {
        $assertionsDisabled = !AbstractDiagnosable.class.desiredAssertionStatus();
        IS_VALID_BUFFER_SIZE = size -> {
            try {
                Integer sz = Integer.valueOf(Integer.parseInt(size));
                return sz.intValue() > 0;
            } catch (NumberFormatException e) {
                return false;
            }
        };
        IS_VALID_BOOLEAN_STRING = value -> {
            return "true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value);
        };
        IS_VALID_LOGGER_NAME = value2 -> {
            return (value2 == null || value2.isEmpty()) ? false : true;
        };
        isGlobalDebugEnabled = Boolean.parseBoolean(getSystemProperty(OracleConnection.CONNECTION_PROPERTY_ENABLE_LOGGING, "false", IS_VALID_BOOLEAN_STRING));
        isWriteLogsToDiagnoseFirstFailure = Boolean.parseBoolean(getSystemProperty(OracleConnection.CONNECTION_PROPERTY_WRITE_LOGS_TO_DIAGNOSE_FIRST_FAILURE, "false", IS_VALID_BOOLEAN_STRING));
        isMetricsEnabled = new AtomicBoolean(Boolean.parseBoolean(getSystemProperty(oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_ENABLE_METRICS, "false", IS_VALID_BOOLEAN_STRING)));
    }

    private boolean isDiagnoseFirstFailureActive() {
        return isDiagnoseFirstFailureEnabled() && this.suspendCount == 0;
    }

    private boolean isDebugActive() {
        return isDebugEnabled() && this.suspendCount == 0;
    }

    protected void setSensitiveEnabled(boolean enabled) {
        if (enabled && !isSensitivePermitted()) {
            throw new IllegalStateException(DatabaseError.formatSqlException(null, DatabaseError.EOJ_DIAGNOSTICS_PERMIT_SENSITIVE_NOT_ENABLED, null, null, OracleConnection.CONNECTION_PROPERTY_PERMIT_SENSITIVE_DIAGNOSTICS).getMessage());
        }
        this.diagnostic.enableSensitive(enabled);
    }

    public void enableDiagnoseFirstFailureDump(boolean enableDump) {
        this.diagnostic.enableDiagnoseFirstFailureDump(enableDump);
    }

    protected static String getSystemProperty(String key, String defaultValue, Predicate isValid) {
        if (key == null) {
            return defaultValue;
        }
        String value = (String) AccessController.doPrivileged(() -> {
            return System.getProperty(key, defaultValue);
        });
        return isValid.test(value) ? value : defaultValue;
    }

    protected void abstractPropertyChange(PropertyChangeEvent event) {
        Object newValue;
        newValue = event.getNewValue();
        switch (event.getPropertyName()) {
            case "Event-Enable-DiagnoseFirstFailure-By-Logger-Name":
                if (((String) newValue).equalsIgnoreCase(getDiagnosticLoggerName())) {
                    setDiagnoseFirstFailureEnabled(true);
                    break;
                }
                break;
            case "Event-Disable-DiagnoseFirstFailure-By-Logger-Name":
                if (((String) newValue).equalsIgnoreCase(getDiagnosticLoggerName())) {
                    setDiagnoseFirstFailureEnabled(false);
                    break;
                }
                break;
            case "Event-Enable-DiagnoseFirstFailure":
                setDiagnoseFirstFailureEnabled(true);
                break;
            case "Event-Disable-DiagnoseFirstFailure":
                setDiagnoseFirstFailureEnabled(false);
                break;
            case "Event-Enable-Logging-By-Logger-Name":
                if (((String) newValue).equalsIgnoreCase(getDiagnosticLoggerName())) {
                    setDebugEnabled(true);
                    break;
                }
                break;
            case "Event-Disable-Logging-By-Logger-Name":
                if (((String) newValue).equalsIgnoreCase(getDiagnosticLoggerName())) {
                    setDebugEnabled(false);
                    break;
                }
                break;
            case "Event-Enable-Logging":
                setDebugEnabled(true);
                break;
            case "Event-Disable-Logging":
                setDebugEnabled(false);
                break;
            case "Event-Enable-Sensitive-Diagnostics-By-Logger-Name":
                if (((String) newValue).equalsIgnoreCase(getDiagnosticLoggerName())) {
                    setSensitiveEnabled(true);
                    break;
                }
                break;
            case "Event-Disable-Sensitive-Diagnostics-By-Logger-Name":
                if (((String) newValue).equalsIgnoreCase(getDiagnosticLoggerName())) {
                    setSensitiveEnabled(false);
                    break;
                }
                break;
            case "Event-Enable-Sensitive-Diagnostics":
                setSensitiveEnabled(true);
                break;
            case "Event-Disable-Sensitive-Diagnostics":
                setSensitiveEnabled(false);
                break;
            case "Event-Update-Diagnostic-Level-By-Logger-Name":
                if (((Object[]) newValue)[0].equals(getDiagnosticLoggerName())) {
                    this.diagnostic.setLoggingLevel((String) ((Object[]) newValue)[1]);
                    break;
                }
                break;
            case "Event-Update-Diagnostic-Level":
                this.diagnostic.setLoggingLevel((String) newValue);
                break;
            case "Event-Update-DiagnoseFirstFailure-Size-By-Logger-Name":
                if (((Object[]) newValue)[0].equals(getDiagnosticLoggerName()) && IS_VALID_BUFFER_SIZE.test(String.valueOf(((Object[]) newValue)[1]))) {
                    this.diagnostic.setDiagnoseFirstFailureBufferSize(((Integer) ((Object[]) newValue)[1]).intValue());
                    break;
                }
                break;
            case "Event-Update-DiagnoseFirstFailure-Size":
                if (IS_VALID_BUFFER_SIZE.test(String.valueOf(newValue))) {
                    this.diagnostic.setDiagnoseFirstFailureBufferSize(((Integer) newValue).intValue());
                    break;
                }
                break;
        }
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public boolean isSensitiveEnabled() {
        return this.diagnostic != null && this.diagnostic.isSensitiveEnabled();
    }

    protected static void onConfigFileChange(String loggingConfigFileName) {
        AccessController.doPrivileged(() -> {
            try {
                LogManager.getLogManager().readConfiguration(new FileInputStream(loggingConfigFileName));
                return null;
            } catch (IOException | SecurityException exception) {
                throw new RuntimeException(exception);
            }
        });
    }

    public static void enableGlobalDebug(boolean enabled) {
        isGlobalDebugEnabled = enabled;
    }

    public static boolean isGlobalDebugEnabled() {
        return isGlobalDebugEnabled;
    }

    public static void enableWriteLogsToDiagnoseFirstFailure(boolean enabled) {
        isWriteLogsToDiagnoseFirstFailure = enabled;
    }

    public static boolean isWriteLogsToDiagnoseFirstFailureEnabled() {
        return isWriteLogsToDiagnoseFirstFailure;
    }

    public static void enableMetrics(boolean isEnabled) {
        isMetricsEnabled.set(isEnabled);
    }

    public static boolean isMetricsEnabled() {
        return isMetricsEnabled.get();
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T trace(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object... args) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, args);
        } else if (isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, args);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object... args) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, args);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, args);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, new Object[0]);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, new Object[0]);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object arg) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object arg1, Object arg2) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object arg1, Object arg2, Object arg3) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object arg1, Object arg2, Object arg3, Object arg4) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object arg1, Object arg2, Object arg3, Object arg4, Object arg5) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4, arg5);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4, arg5);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object arg1, Object arg2, Object arg3, Object arg4, Object arg5, Object arg6) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4, arg5, arg6);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4, arg5, arg6);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object arg1, Object arg2, Object arg3, Object arg4, Object arg5, Object arg6, Object arg7) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debug(Level level, SecurityLabel label, String className, String methodName, String publicMessage, String privateMessage, T thrown, Object arg1, Object arg2, Object arg3, Object arg4, Object arg5, Object arg6, Object arg7, Object arg8) {
        if (isDebugActive()) {
            this.diagnostic.debug(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
        } else if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            this.diagnostic.trace(level, label, className, methodName, getTraceAttributes(), thrown, publicMessage, privateMessage, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
        }
        return thrown;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T tracep(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Supplier<Object[]> supplier) {
        if (isDebugActive() && isLoggable(level)) {
            return (T) this.diagnostic.debug(level, securityLabel, str, str2, getTraceAttributes(), t, str3, str4, supplier.get());
        }
        if (isDiagnoseFirstFailureActive()) {
            return (T) this.diagnostic.trace(level, securityLabel, str, str2, getTraceAttributes(), t, str3, str4, supplier.get());
        }
        return t;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final <T extends Throwable> T debugp(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Supplier<Object[]> supplier) {
        if (isDebugActive() && isLoggable(level)) {
            return (T) this.diagnostic.debug(level, securityLabel, str, str2, getTraceAttributes(), t, str3, str4, supplier.get());
        }
        if (isWriteLogsToDiagnoseFirstFailure && isDiagnoseFirstFailureActive()) {
            return (T) this.diagnostic.trace(level, securityLabel, str, str2, getTraceAttributes(), t, str3, str4, supplier.get());
        }
        return t;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Object secure(@Sensitive Object value) {
        if (this.diagnostic.isSensitiveEnabled() && isSensitivePermitted()) {
            return value;
        }
        return "*****";
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public void suspendLogging() {
        this.suspendCount++;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public void resumeLogging() {
        this.suspendCount--;
        if (!$assertionsDisabled && this.suspendCount < 0) {
            throw new AssertionError();
        }
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public boolean isLoggable(Level level) {
        return this.diagnostic.isLoggable(level);
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public void onClose() {
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public void begin(Metrics.ConnectionEvent event) {
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public void end(Metrics.ConnectionEvent event) {
    }
}
