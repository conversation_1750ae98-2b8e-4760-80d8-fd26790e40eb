package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIOtxse.class */
final class T4CTTIOtxse extends T4CTTIfun {
    static final int OTXSTA = 1;
    static final int OTXDET = 2;
    static final int OTXPCD = 4;
    static final int OCI_TRANS_NEW = 1;
    static final int OCI_TRANS_JOIN = 2;
    static final int OCI_TRANS_RESUME = 4;
    static final int OCI_TRANS_SESSIONLESS = 16;
    static final int OCI_TRANS_STARTMASK = 255;
    static final int OCI_TRANS_READONLY = 256;
    static final int OCI_TRANS_READWRITE = 512;
    static final int OCI_TRANS_SERIALIZABLE = 1024;
    static final int OCI_TRANS_ISOLMASK = 65280;
    static final int OCI_TRANS_LOOSE = 65536;
    static final int OCI_TRANS_TIGHT = 131072;
    static final int OCI_TRANS_TYPEMASK = 983040;
    static final int OCI_TRANS_NOMIGRATE = 1048576;
    static final int OCI_TRANS_SEPARABLE = 2097152;
    static final int OCI_TRANS_PROMOTE = 8;
    static final int K2GSESSIONLESS = 5135422;
    boolean sendTransactionContext;
    private int operation;
    private int formatId;
    private int gtridLength;
    private int bqualLength;
    private int timeout;
    private int flag;
    private int[] xidapp;
    private byte[] transactionContext;
    private byte[] xid;
    private int applicationValue;
    private byte[] ctx;

    T4CTTIOtxse(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.sendTransactionContext = false;
        this.xidapp = null;
        this.xid = null;
        this.applicationValue = -1;
        this.ctx = null;
        setFunCode((short) 103);
    }

    void doOTXSE(int _operation, byte[] _transactionContext, byte[] _xid, int _formatId, int _gtridLength, int _bqualLength, int _timeout, int _flag, int[] _xidapp) throws SQLException, IOException {
        doOTXSE(_operation, _transactionContext, _xid, _formatId, _gtridLength, _bqualLength, _timeout, _flag, _xidapp, false);
    }

    void doOTXSE(int _operation, byte[] _transactionContext, byte[] _xid, int _formatId, int _gtridLength, int _bqualLength, int _timeout, int _flag, int[] _xidapp, boolean doPig) throws SQLException, IOException {
        if (_operation != 1 && _operation != 2 && _operation != 5 && _operation != 4) {
            throw new SQLException("Invalid operation.");
        }
        this.operation = _operation;
        this.formatId = _formatId;
        this.gtridLength = _gtridLength;
        this.bqualLength = _bqualLength;
        this.timeout = _timeout;
        this.flag = _flag;
        this.xidapp = _xidapp;
        this.transactionContext = _transactionContext;
        this.xid = _xid;
        this.applicationValue = -1;
        this.ctx = null;
        if (this.operation == 2 && this.transactionContext == null && this.formatId != K2GSESSIONLESS) {
            throw new SQLException("Transaction context cannot be null when detach is called.");
        }
        if (doPig) {
            setTTCCode((byte) 17);
            doPigRPC();
        } else {
            setTTCCode((byte) 3);
            doRPC();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        int xidopc = this.operation;
        this.meg.marshalSWORD(xidopc);
        if (this.operation == 2 && this.formatId != K2GSESSIONLESS) {
            this.sendTransactionContext = true;
            this.meg.marshalPTR();
        } else {
            this.sendTransactionContext = false;
            this.meg.marshalNULLPTR();
        }
        if (this.transactionContext == null) {
            this.meg.marshalUB4(0L);
        } else {
            this.meg.marshalUB4(this.transactionContext.length);
        }
        this.meg.marshalUB4(this.formatId);
        this.meg.marshalUB4(this.gtridLength);
        this.meg.marshalUB4(this.bqualLength);
        if (this.xid != null) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (this.xid != null) {
            this.meg.marshalUB4(this.xid.length);
        } else {
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB4(this.flag);
        this.meg.marshalUWORD(this.timeout);
        if (this.xidapp != null) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        boolean sendInternalName = false;
        boolean sendExternalName = false;
        if (this.connection.getTTCVersion() >= 5) {
            if (this.connection.internalName != null) {
                sendInternalName = true;
                this.meg.marshalPTR();
                this.meg.marshalUB4(this.connection.internalName.length);
            } else {
                this.meg.marshalNULLPTR();
                this.meg.marshalUB4(0L);
            }
            if (this.connection.externalName != null) {
                sendExternalName = true;
                this.meg.marshalPTR();
                this.meg.marshalUB4(this.connection.externalName.length);
            } else {
                this.meg.marshalNULLPTR();
                this.meg.marshalUB4(0L);
            }
        }
        if (this.sendTransactionContext) {
            this.meg.marshalB1Array(this.transactionContext);
        }
        if (this.xid != null) {
            this.meg.marshalB1Array(this.xid);
        }
        if (this.xidapp != null) {
            this.meg.marshalUB4(this.xidapp[0]);
        }
        if (this.connection.getTTCVersion() >= 5) {
            if (sendInternalName) {
                this.meg.marshalCHR(this.connection.internalName);
            }
            if (sendExternalName) {
                this.meg.marshalCHR(this.connection.externalName);
            }
        }
    }

    byte[] getContext() {
        return this.ctx;
    }

    int getApplicationValue() {
        return this.applicationValue;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.applicationValue = (int) this.meg.unmarshalUB4();
        int length = this.meg.unmarshalUB2();
        this.ctx = this.meg.unmarshalNBytes(length);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
