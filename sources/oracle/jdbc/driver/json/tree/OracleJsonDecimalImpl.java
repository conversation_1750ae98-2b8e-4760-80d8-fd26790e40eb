package oracle.jdbc.driver.json.tree;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Locale;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.binary.OsonPrimitiveConversions;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.NUMBER;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonDecimalImpl.class */
public class OracleJsonDecimalImpl extends OracleJsonNumberImpl implements OracleJsonDecimal {
    public static String NEGATIVE_INF = "\"-Inf\"";
    public static String POSITIVE_INF = "\"Inf\"";
    byte[] raw;
    OracleJsonDecimal.TargetType type;
    private static DecimalFormat SCIENTIFIC_FORMAT_POSITIVE_EXP;
    private static DecimalFormat SCIENTIFIC_FORMAT_NEGATIVE_EXP;

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonDecimalImpl$NumberType.class */
    public enum NumberType {
        NONE,
        SB4,
        SB8,
        DEC128
    }

    static {
        StringBuilder pattern = new StringBuilder(40 + 5);
        pattern.append("0.");
        for (int i = 0; i < 40; i++) {
            pattern.append('#');
        }
        pattern.append("E0");
        DecimalFormat fmt = new DecimalFormat(pattern.toString(), DecimalFormatSymbols.getInstance(Locale.US));
        fmt.setRoundingMode(RoundingMode.HALF_UP);
        SCIENTIFIC_FORMAT_POSITIVE_EXP = fmt;
        SCIENTIFIC_FORMAT_NEGATIVE_EXP = (DecimalFormat) fmt.clone();
        DecimalFormatSymbols symbols = new DecimalFormatSymbols();
        symbols.setExponentSeparator("E+");
        SCIENTIFIC_FORMAT_POSITIVE_EXP.setDecimalFormatSymbols(symbols);
    }

    public OracleJsonDecimalImpl(BigDecimal value) {
        this.raw = OsonPrimitiveConversions.toNumber(value);
    }

    public OracleJsonDecimalImpl(long value, OracleJsonDecimal.TargetType type) {
        this.raw = OsonPrimitiveConversions.toNumber(value);
        this.type = type;
    }

    public OracleJsonDecimalImpl(int value, OracleJsonDecimal.TargetType type) {
        this.raw = OsonPrimitiveConversions.toNumber(value);
        this.type = type;
    }

    public OracleJsonDecimalImpl(byte[] raw, OracleJsonDecimal.TargetType type) {
        this.raw = raw;
        this.type = type;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.DECIMAL;
    }

    @Override // oracle.sql.json.OracleJsonDecimal
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonDecimal)) {
            return false;
        }
        OracleJsonDecimal othern = (OracleJsonDecimal) other;
        return bigDecimalValue().equals(othern.bigDecimalValue());
    }

    @Override // oracle.sql.json.OracleJsonDecimal
    public int hashCode() {
        return bigDecimalValue().hashCode();
    }

    public void reset(byte[] raw, OracleJsonDecimal.TargetType type) {
        this.raw = raw;
        this.type = type;
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public BigDecimal bigDecimalValue() {
        return OsonPrimitiveConversions.toBigDecimal(this.raw);
    }

    public byte[] raw() {
        return this.raw;
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public String getString() {
        int len = this.raw.length;
        if (len <= 2) {
            if (OsonPrimitiveConversions.isNegInf(this.raw, len, 0)) {
                return NEGATIVE_INF;
            }
            if (OsonPrimitiveConversions.isPosInf(this.raw)) {
                return POSITIVE_INF;
            }
        }
        BigDecimal bd = OsonPrimitiveConversions.toBigDecimal(this.raw);
        int precision = bd.precision();
        if (precision <= 40) {
            return bd.toPlainString();
        }
        return format(bd);
    }

    private static String format(BigDecimal x) {
        DecimalFormat decimalFormat;
        if (x.scale() < 0) {
            decimalFormat = (DecimalFormat) SCIENTIFIC_FORMAT_NEGATIVE_EXP.clone();
        } else {
            decimalFormat = (DecimalFormat) SCIENTIFIC_FORMAT_POSITIVE_EXP.clone();
        }
        DecimalFormat format = decimalFormat;
        return format.format(x);
    }

    public boolean isDec() {
        return this.type == OracleJsonDecimal.TargetType.DECIMAL;
    }

    public boolean isSB4() {
        return this.type == OracleJsonDecimal.TargetType.INT;
    }

    public boolean isSB8() {
        return this.type == OracleJsonDecimal.TargetType.LONG;
    }

    @Override // oracle.sql.json.OracleJsonDecimal
    public NUMBER getNUMBER() {
        return new NUMBER(raw());
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaNumberImpl(this.raw, this.type));
        }
        return c.cast(new JsonpPrimitive.JsonpNumberImpl(this.raw, this.type));
    }

    @Override // oracle.sql.json.OracleJsonDecimal
    public OracleJsonDecimal.TargetType getTargetType() {
        return this.type;
    }
}
