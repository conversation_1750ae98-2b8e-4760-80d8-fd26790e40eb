package oracle.jdbc.driver.parser;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import oracle.jdbc.driver.parser.Parser;
import oracle.jdbc.driver.parser.util.Array;
import oracle.jdbc.driver.parser.util.Service;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/Earley.class */
public class Earley extends Parser {
    public int identifier;
    protected int string_literal;
    protected int digits;
    public boolean isCaseSensitive;
    protected PredictedTerminals[] terminalPredictions;
    public Map<Integer, long[]> predicts;
    public boolean skipRanges;

    protected void initCell00(List<LexerToken> src, Matrix matrix) {
        long[] content = null;
        for (int i = 0; i < this.rules.length; i++) {
            Parser.Tuple t = this.rules[i];
            String head = this.allSymbols[t.head];
            if (head.charAt(head.length() - 1) != ')') {
                content = Array.insert(content, makeMatrixCellElem(i, 0, t));
            }
        }
        matrix.initCells(src.size());
        matrix.put(0, 0, new Parser.EarleyCell(content));
        matrix.allXs = Array.insert(matrix.allXs, 0);
    }

    public void parse(List<LexerToken> src, Matrix m) {
        try {
            initCell00(src, m);
            predict(m);
            while (scan(m, src)) {
                complete(m, src.size());
                predict(m);
            }
        } catch (Exception e) {
            for (StackTraceElement elem : e.getStackTrace()) {
                if (elem.toString().contains("UnitTest.assertion") || elem.toString().contains("SqlEarley.main")) {
                    System.err.println(e.toString());
                    System.err.println("matrix.lastY=" + m.lastY() + ", src.size()=" + src.size());
                    return;
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.parser.Parser
    public ParseNode parse(List<LexerToken> src) {
        Matrix matrix = new Matrix(this);
        parse(src, matrix);
        return forest(src, matrix);
    }

    public Earley(RuleTuple[] originalRules) {
        this(originalRules, true);
    }

    public Earley(RuleTuple[] originalRules, boolean precomputePredictions) {
        super(originalRules);
        this.identifier = -1;
        this.string_literal = -1;
        this.digits = -1;
        this.isCaseSensitive = false;
        this.terminalPredictions = null;
        this.predicts = new HashMap();
        this.skipRanges = true;
        this.identifier = this.symbolIndexes.get("identifier").intValue();
        try {
            this.string_literal = this.symbolIndexes.get("string_literal").intValue();
        } catch (NullPointerException e) {
        }
        try {
            this.digits = this.symbolIndexes.get("digits").intValue();
        } catch (NullPointerException e2) {
        }
    }

    protected void precomputePredictions() {
        int before;
        Map<Integer, int[]> closure = new HashMap<>();
        Map<Integer, long[]> symbolHead2rules = new HashMap<>();
        for (int i = 0; i < this.rules.length; i++) {
            int[] tmp = closure.get(Integer.valueOf(this.rules[i].head));
            long[] tmp1 = symbolHead2rules.get(Integer.valueOf(this.rules[i].head));
            int[] tmp2 = Array.insert(tmp, this.rules[i].rhs[0]);
            long[] tmp12 = Array.insert(tmp1, makeMatrixCellElem(i, 0, this.rules[i]));
            closure.put(Integer.valueOf(this.rules[i].head), tmp2);
            symbolHead2rules.put(Integer.valueOf(this.rules[i].head), tmp12);
        }
        do {
            before = size(closure);
            Iterator<Integer> it = closure.keySet().iterator();
            while (it.hasNext()) {
                int k = it.next().intValue();
                int[] v = closure.get(Integer.valueOf(k));
                int[] tmp3 = Array.merge(v, new int[0]);
                for (int i2 : v) {
                    tmp3 = Array.merge(tmp3, closure.get(Integer.valueOf(i2)));
                }
                closure.put(Integer.valueOf(k), tmp3);
            }
        } while (before != size(closure));
        this.terminalPredictions = new PredictedTerminals[this.allSymbols.length];
        Iterator<Integer> it2 = closure.keySet().iterator();
        while (it2.hasNext()) {
            int k2 = it2.next().intValue();
            long[] tmp4 = symbolHead2rules.get(Integer.valueOf(k2));
            for (int n : closure.get(Integer.valueOf(k2))) {
                tmp4 = Array.merge(tmp4, symbolHead2rules.get(Integer.valueOf(n)));
                String rhs0 = this.allSymbols[n];
                if (rhs0.charAt(0) == '\'') {
                    if (this.terminalPredictions[k2] == null) {
                        this.terminalPredictions[k2] = new PredictedTerminals();
                    }
                    this.terminalPredictions[k2].add(n);
                } else if (n == this.identifier || n == this.digits || n == this.string_literal) {
                    if (this.terminalPredictions[k2] == null) {
                        this.terminalPredictions[k2] = new PredictedTerminals();
                    }
                    this.terminalPredictions[k2].invalidate();
                }
            }
            this.predicts.put(Integer.valueOf(k2), tmp4);
        }
    }

    private int size(Map<Integer, int[]> closure) {
        int ret = 0;
        for (int[] tmp : closure.values()) {
            ret += tmp.length;
        }
        return ret;
    }

    protected boolean scan(Matrix matrix, List<LexerToken> src) {
        int y = matrix.lastY();
        if (src.size() <= y) {
            return false;
        }
        LexerToken token = src.get(y);
        Integer suspect = this.symbolIndexes.get("'" + (this.isCaseSensitive ? token.content : token.content.toUpperCase()) + "'");
        boolean ret = false;
        for (int i = matrix.allXs.length - 1; 0 <= i; i--) {
            int x = matrix.allXs[i];
            if (scan(matrix, y, src, x, suspect)) {
                ret = true;
            }
        }
        if (scan(matrix, y, src, y, suspect)) {
            ret = true;
        }
        return ret;
    }

    private boolean scan(Matrix matrix, int y, List<LexerToken> src, int x, Integer suspect) {
        long[] content = null;
        Cell candidateRules = matrix.get(x, y);
        if (candidateRules == null) {
            return false;
        }
        for (int j = 0; j < candidateRules.size(); j++) {
            int pos = candidateRules.getPosition(j);
            int ruleNo = candidateRules.getRule(j);
            Parser.Tuple t = this.rules[ruleNo];
            if (t.size() - 1 >= pos && isScannedSymbol(y, src, pos, t, suspect) && lookaheadOK(t, pos + 1, matrix)) {
                content = Array.insert(content, makeMatrixCellElem(ruleNo, pos + 1, t));
                if (t.rhs.length == pos + 1) {
                    matrix.enqueue(Service.lPair(x, t.head));
                }
            }
        }
        if (content == null) {
            return false;
        }
        matrix.put(x, y + 1, new Parser.EarleyCell(content));
        matrix.allXs = Array.insert(matrix.allXs, x);
        return true;
    }

    protected boolean isScannedSymbol(int y, List<LexerToken> src, int pos, Parser.Tuple t, Integer suspect) {
        int symbol = t.content(pos);
        LexerToken token = src.get(y);
        if (symbol == this.digits && token.type == Token.DIGITS) {
            return true;
        }
        if (symbol == this.string_literal && token.type == Token.QUOTED_STRING) {
            return true;
        }
        return (suspect != null && suspect.intValue() == symbol) || isIdentifier(y, src, symbol, suspect);
    }

    protected boolean isIdentifier(int y, List<LexerToken> src, int symbol, Integer suspect) {
        if (symbol != this.identifier) {
            return false;
        }
        LexerToken token = src.get(y);
        return token.type == Token.IDENTIFIER;
    }

    @Deprecated
    protected boolean notConfusedAsId(int symbol, int head, int pos) {
        return true;
    }

    protected void predict(Matrix matrix) {
        PredictedTerminals terminal;
        int y = matrix.lastY();
        Parser.EarleyCell cell = matrix.get(y, y);
        if (cell == null) {
            cell = new Parser.EarleyCell();
        }
        Set<Integer> symbols = new HashSet<>();
        Map<Integer, Parser.EarleyCell> xRange = matrix.getXRange(y);
        Iterator<Integer> it = xRange.keySet().iterator();
        while (it.hasNext()) {
            int mid = it.next().intValue();
            Cell candidateRules = matrix.get(mid, y);
            for (int j = 0; j < candidateRules.size(); j++) {
                int pos = candidateRules.getPosition(j);
                int ruleNo = candidateRules.getRule(j);
                Parser.Tuple t = this.rules[ruleNo];
                if (t.size() > pos) {
                    symbols.add(Integer.valueOf(t.content(pos)));
                }
            }
        }
        Iterator<Integer> it2 = symbols.iterator();
        while (it2.hasNext()) {
            int symbol = it2.next().intValue();
            if (matrix.LAsuspect == null || (terminal = this.terminalPredictions[symbol]) == null || terminal.matches(matrix.LAsuspect)) {
                long[] ls = this.predicts.get(Integer.valueOf(symbol));
                merge(cell, ls, matrix);
            }
        }
        if (cell.size() > 0) {
            matrix.put(y, y, cell);
        }
    }

    protected void merge(Parser.EarleyCell cell, long[] ls, Matrix m) {
        cell.merge(ls);
    }

    protected void complete(Matrix matrix, int srcLength) {
        Integer predecessor;
        int symPre;
        Map<Integer, Integer> skipIntervals = new HashMap<>();
        while (true) {
            long completionCandidate = matrix.dequeue();
            if (completionCandidate == -1) {
                break;
            }
            int symbol = Service.lY(completionCandidate);
            int mid = Service.lX(completionCandidate);
            int y = matrix.lastY();
            int indexX = Array.indexOf(matrix.allXs, mid);
            if (matrix.allXs.length - 1 < indexX) {
                indexX = matrix.allXs.length - 1;
            }
            if (mid < matrix.allXs[indexX]) {
                indexX--;
            }
            for (int i = indexX; 0 <= i; i--) {
                int x = matrix.allXs[i];
                int skipTo = y;
                Parser.EarleyCell pres = matrix.get(x, mid);
                if (pres != null) {
                    long mask = symbol << 48;
                    int start = Array.indexOf(pres.getContent(), 0, pres.size() - 1, mask);
                    int stop = Array.indexOf(pres.getContent(), 0, pres.size() - 1, mask | 281474976710655L) + 1;
                    Parser.EarleyCell content = matrix.get(x, y);
                    for (int ii = start; ii < stop && ii < pres.size(); ii++) {
                        int dotPre = pres.getPosition(ii);
                        int rulePre = pres.getRule(ii);
                        Parser.Tuple tPre = this.rules[rulePre];
                        if (tPre.size() != dotPre && (symPre = tPre.content(dotPre)) == symbol && (y >= srcLength || lookaheadOK(tPre, dotPre + 1, matrix))) {
                            if (content == null) {
                                content = new Parser.EarleyCell();
                            }
                            long promotedRule = makeMatrixCellElem(rulePre, dotPre + 1, tPre);
                            int before = content.size();
                            content.insertContent(promotedRule);
                            int after = content.size();
                            if (before < after) {
                                matrix.put(x, y, content);
                                if (this.skipRanges && tPre.rhs.length == dotPre + 1 && mid < skipTo && isOptimizable(tPre, symPre, mid, y)) {
                                    skipTo = mid;
                                }
                            }
                            if (tPre.size() == dotPre + 1 && before < after) {
                                matrix.enqueue(Service.lPair(x, tPre.head));
                            }
                        }
                    }
                    if (this.skipRanges && x < skipTo && skipTo < y && ((predecessor = skipIntervals.get(Integer.valueOf(x + 1))) == null || skipTo < predecessor.intValue())) {
                        skipIntervals.put(Integer.valueOf(x + 1), Integer.valueOf(skipTo));
                    }
                }
            }
        }
        Iterator<Integer> it = skipIntervals.keySet().iterator();
        while (it.hasNext()) {
            int x2 = it.next().intValue();
            int y2 = skipIntervals.get(Integer.valueOf(x2)).intValue();
            if (x2 < y2) {
                matrix.allXs = Array.delete(matrix.allXs, x2, y2);
            }
        }
    }

    protected boolean isOptimizable(Parser.Tuple tuple, int preSym, int mid, int y) {
        return isOptimizable(tuple.head, preSym, mid, y);
    }

    protected boolean isOptimizable(int headSym, int preSym, int mid, int y) {
        return (this.allSymbols[headSym].charAt(0) == '\"' || this.allSymbols[preSym].charAt(0) == '\"') ? false : true;
    }

    protected boolean lookaheadOK(Parser.Tuple tPre, int pos, Matrix matrix) {
        return true;
    }

    public void toString(int ruleNo, int pos, StringBuffer sb) {
        Parser.Tuple rule = this.rules[ruleNo];
        sb.append(rule.toString(pos));
    }

    public void initCell(Matrix matrix, int[] heads, int pos) {
        Parser.EarleyCell cell = new Parser.EarleyCell();
        for (int i = 0; i < this.rules.length; i++) {
            Parser.Tuple t = this.rules[i];
            int length = heads.length;
            int i2 = 0;
            while (true) {
                if (i2 < length) {
                    int h = heads[i2];
                    if (t.head != h || !lookaheadOK(t, 0, matrix)) {
                        i2++;
                    } else {
                        cell.insertContent(makeMatrixCellElem(i, 0, t));
                        break;
                    }
                }
            }
        }
        matrix.put(pos, pos, cell);
        matrix.allXs = Array.insert(matrix.allXs, pos);
    }

    @Override // oracle.jdbc.driver.parser.Parser
    public ParseNode treeForACell(List<LexerToken> src, Matrix m, Parser.EarleyCell cell, int x, int y) {
        int rule = -1;
        int pos = -1;
        for (int i = 0; i < cell.size(); i++) {
            rule = cell.getRule(i);
            pos = cell.getPosition(i);
            if (this.rules[rule].rhs.length == pos) {
                return tree(src, m, x, y, rule, pos);
            }
        }
        if (rule != -1 && pos != -1) {
            return tree(src, m, x, y, rule, pos);
        }
        return null;
    }

    protected ParseNode tree(List<LexerToken> src, Matrix m, int x, int y, int rule, int pos) {
        Parser.EarleyCell post;
        Parser.EarleyCell pre;
        int h = this.rules[rule].head;
        if (pos != 0 && (pre = m.get(x, y - 1)) != null) {
            long demotedRule = makeMatrixCellElem(rule, pos - 1, this.rules[rule]);
            int indexOfDemotedRule = Array.indexOf(pre.getContent(), 0, pre.size() - 1, demotedRule);
            long ruleAtTheIndex = pre.content[indexOfDemotedRule];
            if (ruleAtTheIndex == demotedRule) {
                Parser.Tuple t = this.rules[rule];
                LexerToken token = src.get(y - 1);
                Integer suspect = this.symbolIndexes.get("'" + (this.isCaseSensitive ? token.content : token.content.toUpperCase()) + "'");
                if (isScannedSymbol(y - 1, src, pos - 1, t, suspect)) {
                    ParseNode branch = new ParseNode(y - 1, y, this.rules[rule].rhs[pos - 1], this);
                    if (x + 1 == y) {
                        if (this.rules[rule].rhs.length == 1) {
                            branch.addContent(h);
                        }
                        return branch;
                    }
                    int head = h;
                    if (pos != this.rules[rule].rhs.length) {
                        head = -1;
                    }
                    ParseNode ret = new ParseNode(x, y, head, head, this);
                    ret.lft = tree(src, m, x, y - 1, rule, pos - 1);
                    ret.lft.parent = ret;
                    ret.rgt = branch;
                    ret.rgt.parent = ret;
                    return ret;
                }
            }
        }
        if (pos != 0) {
            long demotedRule2 = makeMatrixCellElem(rule, pos - 1, this.rules[rule]);
            TreeMap<Integer, Parser.EarleyCell> cellsAtY = (TreeMap) m.getXRange(y);
            Iterator<Integer> it = (isAsc(h) ? cellsAtY.keySet() : cellsAtY.descendingKeySet()).iterator();
            while (it.hasNext()) {
                int mid = it.next().intValue();
                Parser.EarleyCell pre2 = m.get(x, mid);
                if (pre2 != null && (post = m.get(mid, y)) != null && pre2.content[Array.indexOf(pre2.content, 0, pre2.size() - 1, demotedRule2)] == demotedRule2) {
                    for (int j = 0; j <= post.size() - 1; j++) {
                        int rJ = post.getRule(j);
                        int pJ = post.getPosition(j);
                        if (this.rules[rJ].rhs.length == pJ && this.rules[rJ].head == this.rules[rule].rhs[pos - 1]) {
                            if (x != mid) {
                                ParseNode ret2 = new ParseNode(x, y, this.rules[rule].rhs.length != pos ? -1 : h, this);
                                ret2.lft = tree(src, m, x, mid, rule, pos - 1);
                                ret2.lft.parent = ret2;
                                ret2.rgt = tree(src, m, mid, y, rJ, pJ);
                                ret2.rgt.parent = ret2;
                                return ret2;
                            }
                            if (rJ != rule || pJ != pos) {
                                diagnose(m, x, y);
                                ParseNode ret3 = tree(src, m, mid, y, rJ, pJ);
                                if (this.rules[rule].rhs.length == pos) {
                                    ret3.addContent(h);
                                }
                                return ret3;
                            }
                        }
                    }
                }
            }
        }
        throw new AssertionError("unwind " + this.rules[rule].toString(pos) + " @[" + x + "," + y + ")");
    }

    protected void diagnose(Matrix m, int x, int y) {
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/Earley$PredictedTerminals.class */
    public class PredictedTerminals implements Serializable {
        private boolean isValid = true;
        int[] symbols = null;

        public PredictedTerminals() {
        }

        void add(int sym) {
            if (this.isValid) {
                this.symbols = Array.insert(this.symbols, sym);
            } else {
                invalidate();
            }
        }

        void invalidate() {
            this.symbols = null;
            this.isValid = false;
        }

        public boolean matches(Integer lookahead) {
            if (this.isValid && this.symbols != null) {
                return lookahead != null && this.symbols[Array.indexOf(this.symbols, lookahead.intValue())] == lookahead.intValue();
            }
            return true;
        }

        public String toString() {
            if (!this.isValid) {
                return "*invalid*";
            }
            if (this.symbols == null) {
                return "*symbols == null*";
            }
            StringBuilder ret = new StringBuilder("{");
            for (int s : this.symbols) {
                ret.append(Earley.this.allSymbols[s]);
                ret.append(',');
            }
            return ret.toString();
        }
    }
}
