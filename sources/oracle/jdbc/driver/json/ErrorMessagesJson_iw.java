package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_iw.class */
public class ErrorMessagesJson_iw extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "אירע חריג קלט/פלט"}, new Object[]{"26302", "השנה \"{0}\" אינה נתמכת"}, new Object[]{"26303", "גלישה, הערך גדול מדי: {0}."}, new Object[]{"26304", "אפשרות לא נתמכת (לא מומשה)."}, new Object[]{"26305", "ה-JSON הבינארי אינו תקף או שהוא פגום."}, new Object[]{"26306", "גרסה לא נתמכת של JSON בינארי: {0}."}, new Object[]{"26307", "אסור שאורך המפתח המקודד לפי UTF-8 יעלה על 256 בייט. המפתח להלן חורג מגבול זה: \"{0}\"."}, new Object[]{"26308", "ה-JSON שצוין גדול מדי לקידוד כ-JSON בינארי. אסור שגודל התמונות המקודדות יעלה על 2 ג'יגהבייט."}, new Object[]{"26309", "ה-JSON הבינארי אינו תקף או שהוא פגום. התמונה שצוינה מכילה רק {0} בייט."}, new Object[]{"26310", "ל-java.time.Period שצוין הוגדרו ימים אך המרווח של שנה עד חודש ב-Oracle אינו תומך בימים."}, new Object[]{"26311", "המחולל נסגר לפני הסיום."}, new Object[]{"26312", "חובה לציין מפתח אובייקט בהקשר זה."}, new Object[]{"26313", "כתיבה לא תקפה. כבר נכתב ערך שלם."}, new Object[]{"26314", "סיום אסור בהקשר זה."}, new Object[]{"26315", "מפתח אסור בהקשר זה."}, new Object[]{"26316", "מצופה ערך אחרי מפתח."}, new Object[]{"26317", "מצב ה-parser חייב להיות {0}."}, new Object[]{"26318", "אסור שמצב ה-parser יהיה {0}."}, new Object[]{"26319", "ה-parser חייב להיות על ערך."}, new Object[]{"26320", "\"{0}\" אינו סוג wrapper נתמך."}, new Object[]{"26321", "אי אפשר לשנות אובייקט זה. ליצירת עותק ניתן לשינוי, השתמש ב-OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "אי אפשר לשנות מערך זה. ליצירת עותק ניתן לשינוי, השתמש ב-OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "אובייקט JSON מכיל מפתח כפול: {0}."}, new Object[]{"26324", "לא ניתן לזהות באופן אוטומטי הצפנה, אין מספיק תווים."}, new Object[]{"26325", "היה צפוי אסימון סוף קובץ, אך התקבל  {0}."}, new Object[]{"26326", "תו לא צפוי {0} בשורה {1}, בעמודה {2}."}, new Object[]{"26327", "תו לא צפוי {0} בשורה {1}, עמודה {2}. היה צפוי {3}."}, new Object[]{"26328", "אסימון לא תקף {0} בשורה {1}, עמודה {2}. האסימונים הצפויים הם: {3}."}, new Object[]{"26329", "JsonParser#getString() תקף רק במצבי parser KEY_NAME, VALUE_STRING, VALUE_NUMBER; tך מצב ה-parser הנוכחי הוא {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() תקף רק למצבparser  VALUE_NUMBER, אך מצב ה-parser הנוכחי הוא {0}."}, new Object[]{"26331", "JsonParser#getInt() תקף רק למצבparser  VALUE_NUMBER, אך מצב ה-parser הנוכחי הוא {0}."}, new Object[]{"26332", "JsonParser#getLong() תקף רק למצבparser  VALUE_NUMBER, אך מצב ה-parser הנוכחי הוא {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() תקף רק למצבparser  VALUE_NUMBER, אך מצב ה-parser הנוכחי הוא {0}."}, new Object[]{"26334", "JsonParser#getArray() תקף רק למצבparser  START_ARRAY. אך מצב ה-parser הנוכחי הוא {0}."}, new Object[]{"26335", "JsonParser#getObject() תקף רק למצב parser  START_OBJECT, אך מצב ה-parser הנוכחי הוא {0}."}, new Object[]{"26336", "חותמת זמן עם אזור לא נתמכת. רק אזורי זמן של היסט נתמכים."}, new Object[]{"26337", "האובייקטים והמערכים בערך JSON אינם יכולים לקנן ביותר מ-{0} רמות"}, new Object[]{"26338", "המפתחות של אובייקט JSON לא יעלו על 65,535 בייטים"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
