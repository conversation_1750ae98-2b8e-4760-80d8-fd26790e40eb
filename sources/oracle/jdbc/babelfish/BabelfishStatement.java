package oracle.jdbc.babelfish;

import java.lang.reflect.Method;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import oracle.jdbc.OracleStatement;
import oracle.jdbc.proxy.annotation.GetCreator;
import oracle.jdbc.proxy.annotation.GetDelegate;
import oracle.jdbc.proxy.annotation.GetProxy;
import oracle.jdbc.proxy.annotation.OnError;
import oracle.jdbc.proxy.annotation.Post;
import oracle.jdbc.proxy.annotation.ProxyFor;

@ProxyFor({OracleStatement.class, Statement.class})
/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/BabelfishStatement.class */
public abstract class BabelfishStatement extends BabelfishGenericProxy {
    @Override // oracle.jdbc.babelfish.BabelfishGenericProxy
    @GetCreator
    protected abstract Object getCreator();

    @Override // oracle.jdbc.babelfish.BabelfishGenericProxy
    @GetDelegate
    protected abstract Object getDelegate();

    @GetProxy
    protected abstract Object proxify(Object obj, Object obj2);

    @Override // oracle.jdbc.babelfish.BabelfishGenericProxy
    @OnError(SQLException.class)
    protected Object translateError(Method m, SQLException ex) throws SQLException {
        throw this.translator.translateError(ex);
    }

    @Override // oracle.jdbc.babelfish.BabelfishGenericProxy
    @Post
    protected Object post_Methods(Method m, Object result) {
        if (result instanceof BabelfishGenericProxy) {
            ((BabelfishGenericProxy) result).setTranslator(this.translator);
        }
        return result;
    }

    public void addBatch(String sql) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            ((Statement) getDelegate()).addBatch(translatedSql);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public boolean execute(String sql) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Statement) getDelegate()).execute(translatedSql);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public boolean execute(String sql, int autoGeneratedKeys) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Statement) getDelegate()).execute(translatedSql, autoGeneratedKeys);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public boolean execute(String sql, int[] columnIndexes) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Statement) getDelegate()).execute(translatedSql, columnIndexes);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public boolean execute(String sql, String[] columnNames) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Statement) getDelegate()).execute(translatedSql, columnNames);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public ResultSet executeQuery(String sql) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            ResultSet resultSet = (ResultSet) proxify(((Statement) getDelegate()).executeQuery(translatedSql), this);
            ((BabelfishGenericProxy) resultSet).setTranslator(this.translator);
            return resultSet;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public int executeUpdate(String sql) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Statement) getDelegate()).executeUpdate(translatedSql);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public int executeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Statement) getDelegate()).executeUpdate(translatedSql, autoGeneratedKeys);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public int executeUpdate(String sql, int[] columnIndexes) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Statement) getDelegate()).executeUpdate(translatedSql, columnIndexes);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public int executeUpdate(String sql, String[] columnNames) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Statement) getDelegate()).executeUpdate(translatedSql, columnNames);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }
}
