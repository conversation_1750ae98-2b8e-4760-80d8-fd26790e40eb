package oracle.jdbc.driver.json.tree;

import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonObjectImpl.class */
public class OracleJsonObjectImpl implements OracleJsonObject {
    final Map<String, OracleJsonValue> map;

    public OracleJsonObjectImpl() {
        this.map = new LinkedHashMap();
    }

    public OracleJsonObjectImpl(OracleJsonObject other) {
        this.map = new HashMap();
        for (Map.Entry<String, OracleJsonValue> e : other.entrySet()) {
            String key = e.getKey();
            OracleJsonValue value = e.getValue();
            switch (value.getOracleJsonType()) {
                case OBJECT:
                    this.map.put(key, new OracleJsonObjectImpl(value.asJsonObject()));
                    break;
                case ARRAY:
                    this.map.put(key, new OracleJsonArrayImpl(value.asJsonArray()));
                    break;
                default:
                    this.map.put(key, value);
                    break;
            }
        }
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.OBJECT;
    }

    private void checkNull(OracleJsonValue v) {
        if (v == null) {
            throw new NullPointerException();
        }
    }

    @Override // java.util.Map
    public int size() {
        return this.map.size();
    }

    @Override // java.util.Map
    public boolean isEmpty() {
        return this.map.isEmpty();
    }

    @Override // java.util.Map
    public boolean containsKey(Object key) {
        return this.map.containsKey(key);
    }

    @Override // java.util.Map
    public boolean containsValue(Object value) {
        return this.map.containsValue(value);
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // java.util.Map
    public OracleJsonValue get(Object key) {
        return this.map.get(key);
    }

    @Override // java.util.Map
    public OracleJsonValue put(String key, OracleJsonValue value) {
        checkNull(value);
        return this.map.put(key, value);
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // java.util.Map
    public OracleJsonValue remove(Object key) {
        return this.map.remove(key);
    }

    @Override // java.util.Map
    public void putAll(Map<? extends String, ? extends OracleJsonValue> m) {
        for (Map.Entry<? extends String, ? extends OracleJsonValue> e : m.entrySet()) {
            checkNull(e.getValue());
            this.map.put(e.getKey(), e.getValue());
        }
    }

    @Override // java.util.Map
    public void clear() {
        this.map.clear();
    }

    @Override // java.util.Map
    public Set<String> keySet() {
        return this.map.keySet();
    }

    @Override // java.util.Map
    public Collection<OracleJsonValue> values() {
        return this.map.values();
    }

    @Override // java.util.Map
    public Set<Map.Entry<String, OracleJsonValue>> entrySet() {
        return this.map.entrySet();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public String getString(String name) {
        return this.map.get(name).asJsonString().getString();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public String getString(String name, String defaultValue) {
        OracleJsonValue v = this.map.get(name);
        return (v == null || v.getOracleJsonType() != OracleJsonValue.OracleJsonType.STRING) ? defaultValue : v.asJsonString().getString();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public int getInt(String name) {
        return ((OracleJsonNumber) this.map.get(name)).intValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public int getInt(String name, int defaultValue) {
        OracleJsonValue v = this.map.get(name);
        return v instanceof OracleJsonNumber ? ((OracleJsonNumber) v).intValue() : defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public double getDouble(String name) {
        return ((OracleJsonNumber) this.map.get(name)).doubleValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public long getLong(String key) {
        return ((OracleJsonNumber) this.map.get(key)).longValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public BigDecimal getBigDecimal(String key) {
        return ((OracleJsonNumber) this.map.get(key)).bigDecimalValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public double getDouble(String name, double defaultValue) {
        OracleJsonValue v = this.map.get(name);
        return v instanceof OracleJsonNumber ? ((OracleJsonNumber) v).doubleValue() : defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public long getLong(String key, long defaultValue) {
        OracleJsonValue v = this.map.get(key);
        return v instanceof OracleJsonNumber ? ((OracleJsonNumber) v).longValue() : defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public BigDecimal getBigDecimal(String key, BigDecimal defaultValue) {
        OracleJsonValue v = this.map.get(key);
        return v instanceof OracleJsonNumber ? ((OracleJsonNumber) v).bigDecimalValue() : defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public boolean getBoolean(String name) {
        OracleJsonValue v = this.map.get(name);
        if (v.equals(OracleJsonValue.TRUE)) {
            return true;
        }
        if (v.equals(OracleJsonValue.FALSE)) {
            return false;
        }
        throw new ClassCastException();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public boolean getBoolean(String name, boolean defaultValue) {
        OracleJsonValue v = this.map.get(name);
        if (v == null) {
            return defaultValue;
        }
        if (v.equals(OracleJsonValue.TRUE)) {
            return true;
        }
        if (v.equals(OracleJsonValue.FALSE)) {
            return false;
        }
        return defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public boolean isNull(String name) {
        return this.map.get(name).equals(OracleJsonValue.NULL);
    }

    @Override // oracle.sql.json.OracleJsonObject
    public LocalDateTime getLocalDateTime(String key) {
        OracleJsonValue v = this.map.get(key);
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.DATE) {
            return v.asJsonDate().getLocalDateTime();
        }
        return v.asJsonTimestamp().getLocalDateTime();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OffsetDateTime getOffsetDateTime(String key) {
        OracleJsonValue v = this.map.get(key);
        return v.asJsonTimestampTZ().getOffsetDateTime();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public LocalDateTime getLocalDateTime(String key, LocalDateTime defaultValue) {
        OracleJsonValue v = this.map.get(key);
        if (v == null) {
            return defaultValue;
        }
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.DATE) {
            return v.asJsonDate().getLocalDateTime();
        }
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.TIMESTAMP) {
            return v.asJsonTimestamp().getLocalDateTime();
        }
        return defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OffsetDateTime getOffsetDateTime(String key, OffsetDateTime defaultValue) {
        OracleJsonValue v = this.map.get(key);
        if (v == null) {
            return defaultValue;
        }
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.TIMESTAMPTZ) {
            return v.asJsonTimestampTZ().getOffsetDateTime();
        }
        return defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public byte[] getBytes(String name) {
        return get((Object) name).asJsonBinary().getBytes();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public byte[] getBytes(String name, byte[] defaultValue) {
        OracleJsonValue v = this.map.get(name);
        if (v == null) {
            return defaultValue;
        }
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.BINARY) {
            return v.asJsonBinary().getBytes();
        }
        return defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, byte[] value) {
        return this.map.put(name, new OracleJsonBinaryImpl(value, false));
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, String value) {
        return put(name, (OracleJsonValue) new OracleJsonStringImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, int value) {
        return put(name, (OracleJsonValue) new OracleJsonDecimalImpl(value, OracleJsonDecimal.TargetType.INT));
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, long value) {
        return put(name, (OracleJsonValue) new OracleJsonDecimalImpl(value, OracleJsonDecimal.TargetType.LONG));
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, BigDecimal value) {
        return put(name, (OracleJsonValue) new OracleJsonDecimalImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, double value) {
        return put(name, (OracleJsonValue) new OracleJsonDoubleImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, boolean value) {
        return put(name, value ? OracleJsonValue.TRUE : OracleJsonValue.FALSE);
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue putNull(String name) {
        return put(name, OracleJsonValue.NULL);
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String key, OffsetDateTime value) {
        return put(key, (OracleJsonValue) new OracleJsonTimestampTZImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String key, LocalDateTime value) {
        return put(key, (OracleJsonValue) new OracleJsonTimestampImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        StringWriter writer = new StringWriter();
        JsonSerializerImpl ser = new JsonSerializerImpl(writer);
        ser.write(this);
        ser.close();
        return writer.toString();
    }

    @Override // java.util.Map
    public boolean equals(Object other) {
        return this.map.equals(other);
    }

    @Override // java.util.Map
    public int hashCode() {
        return this.map.hashCode();
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaObjectImpl(this));
        }
        return c.cast(new JsonpObjectImpl(this));
    }
}
