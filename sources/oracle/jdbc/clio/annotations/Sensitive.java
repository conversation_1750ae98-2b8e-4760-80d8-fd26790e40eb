package oracle.jdbc.clio.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.LOCAL_VARIABLE, ElementType.METHOD, ElementType.TYPE_USE})
@Retention(RetentionPolicy.CLASS)
/* loaded from: ojdbc8.jar:oracle/jdbc/clio/annotations/Sensitive.class */
public @interface Sensitive {

    /* loaded from: ojdbc8.jar:oracle/jdbc/clio/annotations/Sensitive$Dependency.class */
    public enum Dependency {
        INDEPENDENT,
        DEPENDENT
    }

    Dependency value() default Dependency.INDEPENDENT;
}
