package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.NotificationRegistration;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrnf.class */
class T4CTTIkpdnrnf {
    int messageType;
    int errorCode;
    long registrationId;
    byte[] notificationQueue = null;
    byte[] consumerName = null;
    String consumerNameString = null;
    T4CConnection connection = null;
    T4CMAREngine mar;
    static final int STOP_TFN_KPDNRNF = 4;
    static final int CONS_CLOSE_ERR = 24035;
    static final int DEST_CLOSE_ERR = 24010;

    T4CTTIkpdnrnf(T4CConnection connection) {
        this.mar = connection.mare;
    }

    public void receive() throws SQLException, IOException {
        this.messageType = (int) this.mar.unmarshalUB4();
        this.errorCode = (int) this.mar.unmarshalUB4();
        this.registrationId = this.mar.unmarshalUB4();
        if (this.messageType == 4) {
            int jdbcRegId = PhysicalConnection.ntfManager.getJDBCRegId(Long.valueOf(this.registrationId));
            NTFJMSRegistration registration = (NTFJMSRegistration) PhysicalConnection.ntfManager.getRegistration(jdbcRegId);
            registration.setState(NotificationRegistration.RegistrationState.DISABLED);
        }
        int notificationQueueLength = (int) this.mar.unmarshalUB4();
        if (notificationQueueLength > 0) {
            this.notificationQueue = new byte[notificationQueueLength];
            int[] intAr = new int[1];
            this.mar.unmarshalCLR(this.notificationQueue, 0, intAr, this.notificationQueue.length);
            int i = intAr[0];
        }
        int consumerNameLength = (int) this.mar.unmarshalUB4();
        if (consumerNameLength > 0) {
            this.consumerName = new byte[consumerNameLength];
            int[] intAr2 = new int[1];
            this.mar.unmarshalCLR(this.consumerName, 0, intAr2, this.consumerName.length);
            int i2 = intAr2[0];
        }
    }

    public String getNotificationQueue() throws SQLException {
        return this.mar.conv.CharBytesToString(this.notificationQueue, this.notificationQueue.length);
    }

    public long getRegistrationId() {
        return this.registrationId;
    }

    public String getConsumerName() throws SQLException {
        if (this.consumerName == null) {
            return null;
        }
        return this.mar.conv.CharBytesToString(this.consumerName, this.consumerName.length);
    }
}
