package oracle.jdbc.driver;

import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CResultSetAccessor.class */
class T2CResultSetAccessor extends ResultSetAccessor {
    T2CResultSetAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind) throws SQLException {
        super(stmt, max_len * 2, form, external_type, isOutBind, true);
    }

    T2CResultSetAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(stmt, max_len * 2, nullable, flags, precision, scale, contflag, total_elems, form);
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    byte[] getBytes(int currentRow) throws SQLException {
        if (isNull(currentRow)) {
            return null;
        }
        int len = getLength(currentRow);
        int align = ((T2CConnection) this.statement.connection).byteAlign;
        long adjusted_Offset = (getOffset(currentRow) + (align - 1)) & ((align - 1) ^ (-1));
        this.rowData.setPosition(adjusted_Offset);
        byte[] result = this.rowData.getBytes(len);
        return result;
    }
}
