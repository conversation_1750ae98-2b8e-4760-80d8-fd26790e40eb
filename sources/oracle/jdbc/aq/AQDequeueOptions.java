package oracle.jdbc.aq;

import java.sql.SQLException;
import java.util.Arrays;

/* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQDequeueOptions.class */
public class AQDequeueOptions {
    public static final int DEQUEUE_WAIT_FOREVER = -1;
    public static final int DEQUEUE_NO_WAIT = 0;
    private String transformation;
    private String condition;
    public static final int MAX_RAW_PAYLOAD = 67108787;
    private int shardNum = -1;
    private String attrConsumerName = null;
    private String attrCorrelation = null;
    private DequeueMode attrDeqMode = DequeueMode.REMOVE;
    private byte[] attrDeqMsgId = null;
    private NavigationOption attrNavigation = NavigationOption.NEXT_MESSAGE;
    private VisibilityOption attrVisibility = VisibilityOption.ON_COMMIT;
    private int attrWait = -1;
    private int maxBufferLength = MAX_RAW_PAYLOAD;
    private DeliveryFilter attrDeliveryMode = DeliveryFilter.PERSISTENT;
    private boolean retrieveMsgId = false;

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQDequeueOptions$DequeueMode.class */
    public enum DequeueMode {
        BROWSE(1),
        LOCKED(2),
        REMOVE(3),
        REMOVE_NODATA(4);

        private final int mode;

        DequeueMode(int _mode) {
            this.mode = _mode;
        }

        public final int getCode() {
            return this.mode;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQDequeueOptions$NavigationOption.class */
    public enum NavigationOption {
        FIRST_MESSAGE(1),
        NEXT_MESSAGE(3),
        NEXT_TRANSACTION(2);

        private final int mode;

        NavigationOption(int _mode) {
            this.mode = _mode;
        }

        public final int getCode() {
            return this.mode;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQDequeueOptions$VisibilityOption.class */
    public enum VisibilityOption {
        ON_COMMIT(2),
        IMMEDIATE(1);

        private final int mode;

        VisibilityOption(int _mode) {
            this.mode = _mode;
        }

        public final int getCode() {
            return this.mode;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQDequeueOptions$DeliveryFilter.class */
    public enum DeliveryFilter {
        PERSISTENT(1),
        BUFFERED(2),
        PERSISTENT_OR_BUFFERED(3);

        private final int mode;

        DeliveryFilter(int _mode) {
            this.mode = _mode;
        }

        public final int getCode() {
            return this.mode;
        }
    }

    public void setConsumerName(String consumerName) throws SQLException {
        this.attrConsumerName = consumerName;
    }

    public String getConsumerName() {
        return this.attrConsumerName;
    }

    public void setShardNum(int shNum) {
        this.shardNum = shNum;
    }

    public int getShardNum() {
        return this.shardNum;
    }

    public void setCorrelation(String correlation) throws SQLException {
        this.attrCorrelation = correlation;
    }

    public String getCorrelation() {
        return this.attrCorrelation;
    }

    public void setDequeueMode(DequeueMode deqMode) throws SQLException {
        this.attrDeqMode = deqMode;
    }

    public DequeueMode getDequeueMode() {
        return this.attrDeqMode;
    }

    public void setDequeueMessageId(byte[] deqMsgId) throws SQLException {
        this.attrDeqMsgId = deqMsgId;
    }

    public byte[] getDequeueMessageId() {
        return this.attrDeqMsgId;
    }

    public void setNavigation(NavigationOption navigation) throws SQLException {
        this.attrNavigation = navigation;
    }

    public NavigationOption getNavigation() {
        return this.attrNavigation;
    }

    public void setVisibility(VisibilityOption visibility) throws SQLException {
        this.attrVisibility = visibility;
    }

    public VisibilityOption getVisibility() {
        return this.attrVisibility;
    }

    public void setWait(int wait) throws SQLException {
        this.attrWait = wait;
    }

    public int getWait() {
        return this.attrWait;
    }

    public void setMaximumBufferLength(int length) throws SQLException {
        if (length > 0) {
            this.maxBufferLength = length;
        }
    }

    public int getMaximumBufferLength() {
        return this.maxBufferLength;
    }

    public void setDeliveryFilter(DeliveryFilter delivery) throws SQLException {
        this.attrDeliveryMode = delivery;
    }

    public DeliveryFilter getDeliveryFilter() {
        return this.attrDeliveryMode;
    }

    public void setRetrieveMessageId(boolean retrieveIt) {
        this.retrieveMsgId = retrieveIt;
    }

    public boolean getRetrieveMessageId() {
        return this.retrieveMsgId;
    }

    public void setTransformation(String _transformation) {
        this.transformation = _transformation;
    }

    public String getTransformation() {
        return this.transformation;
    }

    public void setCondition(String _condition) {
        this.condition = _condition;
    }

    public String getCondition() {
        return this.condition;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AQDequeueOptions {");
        sb.append("attrConsumerName=" + this.attrConsumerName + ", ");
        sb.append("attrCorrelation=" + this.attrCorrelation + ", ");
        sb.append("attrDeqMode=" + this.attrDeqMode + ", ");
        sb.append("attrDeqMsgId=" + Arrays.toString(this.attrDeqMsgId) + ", ");
        sb.append("attrNavigation=" + this.attrNavigation + ", ");
        sb.append("attrVisibility=" + this.attrVisibility + ", ");
        sb.append("attrWait=" + this.attrWait + ", ");
        sb.append("maxBufferLength=" + this.maxBufferLength + ", ");
        sb.append("attrDeliveryMode=" + this.attrDeliveryMode + ", ");
        sb.append("retrieveMsgId=" + this.retrieveMsgId + ", ");
        sb.append("transformation=" + this.transformation + ", ");
        sb.append("condition=" + this.condition + "}");
        return sb.toString();
    }
}
