package oracle.jdbc.diagnostics;

import java.util.EventObject;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/PropertyChangeEvent.class */
public class PropertyChangeEvent extends EventObject {
    private String propertyName;
    private Object newValue;
    private Object oldValue;

    public PropertyChangeEvent(Object source, String propertyName, Object oldValue, Object newValue) {
        super(source);
        this.propertyName = propertyName;
        this.newValue = newValue;
        this.oldValue = oldValue;
    }

    public Object getNewValue() {
        return this.newValue;
    }

    public String getPropertyName() {
        return this.propertyName;
    }
}
