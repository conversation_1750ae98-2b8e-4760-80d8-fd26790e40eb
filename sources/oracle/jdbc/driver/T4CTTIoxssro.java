package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.KeywordValueLong;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxssro.class */
final class T4CTTIoxssro extends T4CTTIfun {
    private int functionId;
    private byte[] sessionId;
    private KeywordValueLong[] inKV;
    private int inFlags;
    private KeywordValueLong[] outKV;
    private int outFlags;

    T4CTTIoxssro(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.sessionId = null;
        this.inKV = null;
        this.outKV = null;
        this.outFlags = -1;
        setFunCode((short) 156);
    }

    void doOXSSRO(int _functionId, byte[] _sessionId, KeywordValueLong[] _inKV, int _inFlags) throws SQLException, IOException {
        this.functionId = _functionId;
        this.sessionId = _sessionId;
        this.inKV = _inKV;
        this.inFlags = _inFlags;
        this.outKV = null;
        this.outFlags = -1;
        if (this.inKV != null) {
            for (int i = 0; i < this.inKV.length; i++) {
                ((KeywordValueLongI) this.inKV[i]).doCharConversion(this.meg.conv);
            }
        }
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.functionId);
        boolean sendSessionId = false;
        if (this.sessionId != null && this.sessionId.length > 0) {
            sendSessionId = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sessionId.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendInKV = false;
        if (this.inKV != null && this.inKV.length > 0) {
            sendInKV = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.inKV.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB4(this.inFlags);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        if (sendSessionId) {
            this.meg.marshalB1Array(this.sessionId);
        }
        if (sendInKV) {
            for (int i = 0; i < this.inKV.length; i++) {
                ((KeywordValueLongI) this.inKV[i]).marshal(this.meg);
            }
        }
    }

    KeywordValueLong[] getOutKV() {
        return this.outKV;
    }

    int getOutFlags() {
        return this.outFlags;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int xsssoovn = (int) this.meg.unmarshalUB4();
        this.outKV = new KeywordValueLong[xsssoovn];
        for (int i = 0; i < xsssoovn; i++) {
            this.outKV[i] = KeywordValueLongI.unmarshal(this.meg);
        }
        this.outFlags = (int) this.meg.unmarshalUB4();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
