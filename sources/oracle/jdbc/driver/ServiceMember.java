package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ServiceMember.class */
class ServiceMember implements Diagnosable {
    private static final String CLASS_NAME = ServiceMember.class.getName();
    private String name;
    private String svc;
    private String db;
    private String hst;
    private boolean restarted = false;
    int connCount = 0;
    private MemberStatus status = MemberStatus.UNKNOWN;
    private ConcurrentHashMap<oracle.jdbc.internal.OracleConnection, oracle.jdbc.internal.OracleConnection> connections = new ConcurrentHashMap<>();

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/ServiceMember$MemberStatus.class */
    private enum MemberStatus {
        UNKNOWN,
        UP,
        DOWN
    }

    ServiceMember(String svcname, String iname, String dbuniq, String hstname) {
        this.name = iname;
        this.svc = svcname;
        this.db = dbuniq;
        this.hst = hstname;
    }

    void up() {
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "up", "Marked service member UP: {0}", (String) null, (String) null, (Object) this.name);
        this.status = MemberStatus.UP;
    }

    void down() {
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "down", "Marked service member DOWN: {0}", (String) null, (String) null, (Object) this.name);
        this.status = MemberStatus.DOWN;
    }

    boolean isDown() {
        return this.status == MemberStatus.DOWN;
    }

    boolean isUp() {
        return this.status == MemberStatus.UP;
    }

    void addConnection(oracle.jdbc.internal.OracleConnection oconn) {
        this.connections.put(oconn, oconn);
        this.connCount++;
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "addConnection", "Added connection to service member: {0}", (String) null, (String) null, (Object) getNetConnectionId(oconn));
    }

    void dropConnection(oracle.jdbc.internal.OracleConnection oconn) {
        this.connections.remove(oconn);
        if (this.connCount > 0) {
            this.connCount--;
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "dropConnection", "Removed connection to service member: {0}", (String) null, (String) null, (Object) getNetConnectionId(oconn));
    }

    void cleanupConnections() throws SQLException {
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "cleanupConnections", "Cleaning up connections to service member: {0}", (String) null, (String) null, (Object) this.name);
        ConcurrentHashMap<oracle.jdbc.internal.OracleConnection, oracle.jdbc.internal.OracleConnection> _conns = this.connections;
        this.connections = new ConcurrentHashMap<>();
        Iterator it = _conns.keySet().iterator();
        while (it.hasNext()) {
            oracle.jdbc.internal.OracleConnection oconn = (oracle.jdbc.internal.OracleConnection) it.next();
            oconn.abort();
            oconn.close();
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "cleanupConnections", "Cleaned up connection to service member: {0}", (String) null, (String) null, (Object) getNetConnectionId(oconn));
        }
        this.connCount = 0;
        _conns.clear();
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "cleanupConnections", "Cleaned up all connections to service member: {0}", (String) null, (String) null, (Object) this.name);
    }

    public String getName() {
        return this.name;
    }

    String getDatabase() {
        return this.db;
    }

    public String toString() {
        return "Service name: " + this.svc + ", Instance name: " + this.name + ", Database name: " + this.db + ", Host name: " + this.hst;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return CommonDiagnosable.getInstance();
    }

    private String getNetConnectionId(oracle.jdbc.internal.OracleConnection conn) {
        try {
            return conn.getNetConnectionId();
        } catch (SQLException e) {
            return e.getMessage();
        }
    }
}
