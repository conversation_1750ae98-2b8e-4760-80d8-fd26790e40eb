package oracle.jdbc.driver;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TSLTZBinder.class */
class TSLTZBinder extends DatumBinder {
    Binder theTSLTZCopyingBinder;

    static void init(Binder x) {
        x.type = (short) 231;
        x.bytelen = 11;
    }

    TSLTZBinder(byte[] val) {
        super(val);
        this.theTSLTZCopyingBinder = null;
        init(this);
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theTSLTZCopyingBinder == null) {
            this.theTSLTZCopyingBinder = new TSLTZCopyingBinder();
        }
        return this.theTSLTZCopyingBinder;
    }
}
