package oracle.jdbc.driver.json.binary;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import oracle.jdbc.driver.json.OracleJsonExceptions;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonBuffer.class */
public final class OsonBuffer {
    final ByteBuffer buffer;
    char[] charBuffer;
    final CharsetDecoder decoder = StandardCharsets.UTF_8.newDecoder();
    public Map<Integer, String> cache = new HashMap();

    public OsonBuffer(ByteBuffer buffer) {
        buffer.order(ByteOrder.BIG_ENDIAN);
        this.buffer = buffer;
    }

    public int getInt() {
        return this.buffer.getInt();
    }

    public ByteBuffer buffer() {
        return this.buffer;
    }

    public int position() {
        return this.buffer.position();
    }

    public void position(int pos) {
        this.buffer.position(pos);
    }

    public int getUB1() {
        return 255 & this.buffer.get();
    }

    public int getUB1(int i) {
        return 255 & this.buffer.get(i);
    }

    public int getUB2() {
        return 65535 & this.buffer.getShort();
    }

    public short getShort(int i) {
        return this.buffer.getShort(i);
    }

    public int getUB2(int i) {
        return 65535 & this.buffer.getShort(i);
    }

    public String readString(int len) {
        if (!this.buffer.hasArray()) {
            throw new UnsupportedOperationException();
        }
        byte[] raw = this.buffer.array();
        if (this.charBuffer == null || this.charBuffer.length < len) {
            this.charBuffer = new char[len];
        }
        int pos = this.buffer.position() + this.buffer.arrayOffset();
        for (int i = 0; i < len; i++) {
            byte b = raw[i + pos];
            if (b < 0) {
                String result = new String(this.buffer.array(), this.buffer.position() + this.buffer.arrayOffset(), len, StandardCharsets.UTF_8);
                this.buffer.position(pos + len);
                return result;
            }
            this.charBuffer[i] = (char) b;
        }
        this.buffer.position(pos + len);
        return new String(this.charBuffer, 0, len);
    }

    public void readBytes(OutputStream out, int len) throws IOException {
        if (!this.buffer.hasArray()) {
            throw new UnsupportedOperationException();
        }
        int pos = this.buffer.position() + this.buffer.arrayOffset();
        out.write(this.buffer.array(), pos, len);
    }

    public int getUB4int() {
        int res = this.buffer.getInt();
        if (res < 0) {
            throw OracleJsonExceptions.OVERFLOW.create(null, Integer.valueOf(res));
        }
        return res;
    }

    public int getUB4int(int i) {
        int res = this.buffer.getInt(i);
        if (res < 0) {
            throw OracleJsonExceptions.OVERFLOW.create(null, Integer.valueOf(res));
        }
        return res;
    }

    public int getInt(int i) {
        return this.buffer.getInt(i);
    }

    public void get(byte[] bytes) {
        this.buffer.get(bytes);
    }

    public double readDtyDouble() {
        byte[] res = new byte[8];
        this.buffer.get(res);
        return OsonPrimitiveConversions.canonicalFormatBytesToDouble(res);
    }

    public float readDtyFloat() {
        byte[] res = new byte[4];
        this.buffer.get(res);
        return OsonPrimitiveConversions.canonicalFormatBytesToFloat(res);
    }

    int binarySearchUb1(int fromPos, int count, int test) {
        int low = 0;
        int high = count - 1;
        byte[] bytes = this.buffer.array();
        while (low <= high) {
            int mid = (low + high) >>> 1;
            int midValue = bytes[fromPos + mid] & 255;
            if (midValue < test) {
                low = mid + 1;
            } else if (midValue > test) {
                high = mid - 1;
            } else {
                return mid;
            }
        }
        return -1;
    }

    int binarySearchUb2(int fromPos, int count, int test) {
        int low = 0;
        int high = count - 1;
        while (low <= high) {
            int mid = (low + high) >>> 1;
            int midValue = getUB2(fromPos + (mid * 2));
            if (midValue < test) {
                low = mid + 1;
            } else if (midValue > test) {
                high = mid - 1;
            } else {
                return mid;
            }
        }
        return -1;
    }

    int binarySearchUb4(int fromPos, int count, int test) {
        int low = 0;
        int high = count - 1;
        while (low <= high) {
            int mid = (low + high) >>> 1;
            int midValue = getUB4int(fromPos + (mid * 4));
            if (midValue < test) {
                low = mid + 1;
            } else if (midValue > test) {
                high = mid - 1;
            } else {
                return mid;
            }
        }
        return -1;
    }

    public int linearSearchUb1(int fromPos, int count, int test) {
        for (int i = fromPos; i < fromPos + count; i++) {
            if (getUB1(i) == test) {
                return i - fromPos;
            }
        }
        return -1;
    }

    int linearSearchUb2(int fromPos, int count, int test) {
        int endPos = fromPos + (count * 2);
        for (int i = fromPos; i < endPos; i += 2) {
            if (getUB2(i) == test) {
                return (i - fromPos) / 2;
            }
        }
        return -1;
    }

    public int linearSearchUb4(int fromPos, int count, int test) {
        int endPos = fromPos + (count * 4);
        for (int i = fromPos; i < endPos; i += 4) {
            if (getUB4int(i) == test) {
                return (i - fromPos) / 4;
            }
        }
        return -1;
    }
}
