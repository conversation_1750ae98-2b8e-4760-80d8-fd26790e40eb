package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.KeywordValueLong;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.XSKeyval;
import oracle.jdbc.internal.XSNamespace;
import oracle.jdbc.internal.XSPrincipal;
import oracle.jdbc.internal.XSSecureId;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxscre.class */
final class T4CTTIoxscre extends T4CTTIfun {
    private OracleConnection.XSSessionOperationCode opcode;
    private XSSecureId sidp;
    private byte[] cookie;
    private XSPrincipal username;
    private byte[] tenantBytes;
    private XSNamespace[] namespaces;
    private OracleConnection.XSSessionModeFlag mode;
    private XSKeyval kv;
    private byte[] sessionId;

    T4CTTIoxscre(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.sessionId = null;
        setFunCode((short) 179);
    }

    void doOXSCRE(OracleConnection.XSSessionOperationCode opcode, XSSecureId sidp, byte[] cookie, XSPrincipal username, String tenant, XSNamespace[] namespaces, OracleConnection.XSSessionModeFlag mode, XSKeyval kv) throws SQLException, IOException {
        KeywordValueLong[] kvl;
        this.opcode = opcode;
        this.sidp = sidp;
        this.cookie = cookie;
        this.username = username;
        if (username != null) {
            ((XSPrincipalI) username).doCharConversion(this.meg.conv);
        }
        if (tenant != null && tenant.length() > 0) {
            this.tenantBytes = this.meg.conv.StringToCharBytes(tenant);
        } else {
            this.tenantBytes = null;
        }
        this.namespaces = namespaces;
        if (namespaces != null) {
            for (XSNamespace xSNamespace : namespaces) {
                ((XSNamespaceI) xSNamespace).doCharConversion(this.meg.conv);
            }
        }
        this.mode = mode;
        this.kv = kv;
        if (kv != null && (kvl = kv.getKeyval()) != null) {
            for (KeywordValueLong keywordValueLong : kvl) {
                ((KeywordValueLongI) keywordValueLong).doCharConversion(this.meg.conv);
            }
        }
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.opcode.getCode());
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        boolean sendSidp = false;
        if (this.sidp != null) {
            sendSidp = true;
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        boolean sendcookie = false;
        if (this.cookie != null && this.cookie.length > 0) {
            sendcookie = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.cookie.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendUsername = false;
        if (this.username != null) {
            sendUsername = true;
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        boolean sendTenant = false;
        if (this.tenantBytes != null) {
            sendTenant = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.tenantBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendNamespaces = false;
        this.meg.marshalPTR();
        if (this.namespaces != null && this.namespaces.length > 0) {
            sendNamespaces = true;
            this.meg.marshalUB4(this.namespaces.length);
        } else {
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB4(this.mode.getCode());
        boolean sendKv = false;
        if (this.kv != null) {
            sendKv = true;
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (sendSidp) {
            ((XSSecureIdI) this.sidp).marshal(this.meg);
        }
        if (sendcookie) {
            this.meg.marshalB1Array(this.cookie);
        }
        if (sendUsername) {
            ((XSPrincipalI) this.username).marshal(this.meg);
        }
        if (sendTenant) {
            this.meg.marshalCHR(this.tenantBytes);
        }
        if (sendNamespaces) {
            for (int i = 0; i < this.namespaces.length; i++) {
                ((XSNamespaceI) this.namespaces[i]).marshal(this.meg);
            }
        }
        if (sendKv) {
            ((XSKeyvalI) this.kv).marshal(this.meg);
        }
    }

    byte[] getSessionId() {
        return this.sessionId;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int kpxscreopsidl = (int) this.meg.unmarshalUB4();
        this.sessionId = null;
        if (kpxscreopsidl > 0) {
            this.sessionId = this.meg.unmarshalNBytes(kpxscreopsidl);
        }
    }
}
