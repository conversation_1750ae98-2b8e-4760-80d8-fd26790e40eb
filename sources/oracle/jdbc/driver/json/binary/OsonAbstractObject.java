package oracle.jdbc.driver.json.binary;

import java.util.AbstractCollection;
import java.util.AbstractSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import oracle.jdbc.driver.json.binary.OsonStructureImpl;
import oracle.jdbc.driver.json.tree.OracleJsonNumberImpl;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonAbstractObject.class */
public class OsonAbstractObject extends OsonStructureImpl {
    boolean fidSorted;
    int fidArrayOffset;

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonAbstractObject$OsonEntrySet.class */
    protected class OsonEntrySet<T> extends AbstractSet<Map.Entry<String, T>> {
        protected OsonEntrySet() {
        }

        @Override // java.util.AbstractCollection, java.util.Collection, java.lang.Iterable, java.util.Set
        public Iterator<Map.Entry<String, T>> iterator() {
            return new OsonStructureImpl.PositionIter<Map.Entry<String, T>>() { // from class: oracle.jdbc.driver.json.binary.OsonAbstractObject.OsonEntrySet.1
                {
                    OsonAbstractObject osonAbstractObject = OsonAbstractObject.this;
                }

                @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl.PositionIter
                public Map.Entry<String, T> getValue(final int pos) {
                    return new Map.Entry<String, T>() { // from class: oracle.jdbc.driver.json.binary.OsonAbstractObject.OsonEntrySet.1.1
                        /* JADX WARN: Can't rename method to resolve collision */
                        @Override // java.util.Map.Entry
                        public String getKey() {
                            return OsonAbstractObject.this.getFieldName(pos);
                        }

                        @Override // java.util.Map.Entry
                        public T getValue() {
                            return (T) OsonAbstractObject.this.getValueInternal(OsonAbstractObject.this.getChildOffset(pos));
                        }

                        @Override // java.util.Map.Entry
                        public T setValue(T value) {
                            throw new UnsupportedOperationException();
                        }

                        @Override // java.util.Map.Entry
                        public int hashCode() {
                            return getKey().hashCode() ^ getValue().hashCode();
                        }

                        @Override // java.util.Map.Entry
                        public boolean equals(Object other) {
                            if (other == this) {
                                return true;
                            }
                            if (!(other instanceof Map.Entry)) {
                                return false;
                            }
                            Map.Entry<?, ?> o = (Map.Entry) other;
                            return getKey().equals(o.getKey()) && getValue().equals(o.getValue());
                        }
                    };
                }
            };
        }

        @Override // java.util.AbstractCollection, java.util.Collection, java.util.Set
        public int size() {
            return OsonAbstractObject.this.size;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonAbstractObject$OsonObjectValues.class */
    protected class OsonObjectValues<T> extends AbstractCollection<T> {
        protected OsonObjectValues() {
        }

        @Override // java.util.AbstractCollection, java.util.Collection, java.lang.Iterable
        public Iterator<T> iterator() {
            return new OsonStructureImpl.PositionIter<T>() { // from class: oracle.jdbc.driver.json.binary.OsonAbstractObject.OsonObjectValues.1
                {
                    OsonAbstractObject osonAbstractObject = OsonAbstractObject.this;
                }

                @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl.PositionIter
                public T getValue(int i) {
                    int i2 = i + 1;
                    return (T) OsonAbstractObject.this.getValueInternal(OsonAbstractObject.this.getChildOffset(i));
                }
            };
        }

        @Override // java.util.AbstractCollection, java.util.Collection
        public int size() {
            return OsonAbstractObject.this.size;
        }
    }

    public OsonAbstractObject(OsonContext ctx) {
        super(ctx);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.OBJECT;
    }

    public OsonAbstractObject(OsonContext ctx, int pos) {
        super(ctx);
        init(pos);
    }

    public OsonAbstractArray getJsonArrayInternal(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return null;
        }
        return getArrayInternal(childOffset);
    }

    public OsonAbstractObject getJsonObjectInternal(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return null;
        }
        return getJsonObjectInternal(childOffset);
    }

    public String getString(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        String res = getStringInternal(childOffset);
        if (res == null) {
            throw new ClassCastException();
        }
        return res;
    }

    public String getString(String key, String d) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return d;
        }
        String res = getStringInternal(childOffset);
        if (res == null) {
            return d;
        }
        return res;
    }

    public boolean getBoolean(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        Boolean res = getBooleanInternal(childOffset);
        if (res == null) {
            throw new ClassCastException();
        }
        return res.booleanValue();
    }

    public boolean getBoolean(String key, boolean d) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return d;
        }
        Boolean res = getBooleanInternal(childOffset);
        if (res == null) {
            return d;
        }
        return res.booleanValue();
    }

    public int getInt(String key, int d) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return d;
        }
        Object json = getValueInternal(childOffset);
        if (!(json instanceof OracleJsonNumberImpl)) {
            return d;
        }
        return ((OracleJsonNumberImpl) json).intValue();
    }

    public int getInt(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        return ((OracleJsonNumberImpl) getValueInternal(childOffset)).intValue();
    }

    public boolean isNull(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        return isNullInternal(childOffset);
    }

    public boolean containsKey(Object key) {
        return (key instanceof String) && getChildOffset((String) key) != -1;
    }

    public boolean containsValue(Object value) {
        for (int i = 0; i < this.size; i++) {
            Object v = getValueInternal(getChildOffset(i));
            if (v.equals(value)) {
                return true;
            }
        }
        return false;
    }

    public Object getInternal(Object key) {
        int childOffset;
        if (!(key instanceof String) || (childOffset = getChildOffset((String) key)) < 0) {
            return null;
        }
        return getValueInternal(childOffset);
    }

    public Object getInternal(int position) {
        int childOffset = getChildOffset(position);
        if (childOffset < 0) {
            return null;
        }
        return getValueInternal(childOffset);
    }

    public Set<String> keySet() {
        return new AbstractSet<String>() { // from class: oracle.jdbc.driver.json.binary.OsonAbstractObject.1
            @Override // java.util.AbstractCollection, java.util.Collection, java.lang.Iterable, java.util.Set
            public Iterator<String> iterator() {
                return new OsonStructureImpl.PositionIter<String>() { // from class: oracle.jdbc.driver.json.binary.OsonAbstractObject.1.1
                    {
                        OsonAbstractObject osonAbstractObject = OsonAbstractObject.this;
                    }

                    /* JADX WARN: Can't rename method to resolve collision */
                    @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl.PositionIter
                    public String getValue(int pos) {
                        return OsonAbstractObject.this.getFieldName(pos);
                    }
                };
            }

            @Override // java.util.AbstractCollection, java.util.Collection, java.util.Set
            public int size() {
                return OsonAbstractObject.this.size;
            }
        };
    }

    @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl
    protected void init(int pos) {
        int delegateObjectOffset;
        super.init(pos);
        int op = this.ctx.b.getUB1(pos);
        initChildOffseUb(op);
        this.fidArrayOffset = pos + 1;
        int childSizeBits = op & 24;
        OsonHeader header = this.ctx.getHeader();
        if (childSizeBits == 0) {
            this.size = this.ctx.b.getUB1(pos + 1);
            this.fidArrayOffset++;
            this.childArrayOffset = this.fidArrayOffset + (this.size * header.numFieldIdBytes());
        } else if (childSizeBits == 8) {
            this.size = this.ctx.b.getUB2(pos + 1);
            this.fidArrayOffset += 2;
            this.childArrayOffset = this.fidArrayOffset + (this.size * header.numFieldIdBytes());
        } else if (childSizeBits == 16) {
            this.size = this.ctx.b.getUB4int(pos + 1);
            this.fidArrayOffset += 4;
            this.childArrayOffset = this.fidArrayOffset + (this.size * header.numFieldIdBytes());
        } else if (childSizeBits == 24) {
            if (this.childOffsetUb == 4) {
                delegateObjectOffset = this.ctx.b.getUB4int(pos + 1) + header.getTreeSegmentOffset();
                this.childArrayOffset = pos + 1 + 4;
            } else {
                delegateObjectOffset = this.ctx.b.getUB2(pos + 1) + header.getTreeSegmentOffset();
                this.childArrayOffset = pos + 1 + 2;
            }
            int otherOp = this.ctx.b.getUB1(delegateObjectOffset);
            int otherSizeBits = otherOp & 24;
            this.fidArrayOffset = delegateObjectOffset + 1;
            if (otherSizeBits == 0) {
                this.size = this.ctx.b.getUB1(delegateObjectOffset + 1);
                this.fidArrayOffset++;
            } else if (otherSizeBits == 8) {
                this.size = this.ctx.b.getUB2(delegateObjectOffset + 1);
                this.fidArrayOffset += 2;
            } else if (otherSizeBits == 16) {
                this.size = this.ctx.b.getUB4int(delegateObjectOffset + 1);
                this.fidArrayOffset += 4;
            } else {
                throw new IllegalStateException();
            }
        }
        this.fidSorted = (op & 4) == 0 && header.fieldsSorted();
    }

    @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl
    protected int getChildOffset(int fieldPos) {
        OsonHeader header = this.ctx.getHeader();
        if (header.relativeOffsets()) {
            if (this.childOffsetUb == 2) {
                short relative = this.ctx.b.getShort(this.childArrayOffset + (fieldPos * 2));
                return (((short) (relative + (this.pos - header.getTreeSegmentOffset()))) & 65535) + header.getTreeSegmentOffset();
            }
            int relative2 = this.ctx.b.getInt(this.childArrayOffset + (fieldPos * 4));
            return ((relative2 + (this.pos - header.getTreeSegmentOffset())) & (-1)) + header.getTreeSegmentOffset();
        }
        if (this.childOffsetUb == 2) {
            return this.ctx.b.getUB2(this.childArrayOffset + (fieldPos * 2)) + header.getTreeSegmentOffset();
        }
        return this.ctx.b.getUB4int(this.childArrayOffset + (fieldPos * 4)) + header.getTreeSegmentOffset();
    }

    protected int getChildOffset(String key) {
        int fieldPos = getChildPosition(key);
        if (fieldPos == -1) {
            return -1;
        }
        return getChildOffset(fieldPos);
    }

    protected int getChildPosition(String key) {
        int fieldPos;
        OsonHeader header = this.ctx.getHeader();
        int id = header.getFieldId(key);
        if (id == -1) {
            return -1;
        }
        this.ctx.b.position(this.pos + 1);
        if (header.numFieldIdBytes() == 1) {
            if (this.fidSorted) {
                fieldPos = this.ctx.b.binarySearchUb1(this.fidArrayOffset, this.size, id);
            } else {
                fieldPos = this.ctx.b.linearSearchUb1(this.fidArrayOffset, this.size, id);
            }
        } else if (header.numFieldIdBytes() == 2) {
            if (this.fidSorted) {
                fieldPos = this.ctx.b.binarySearchUb2(this.fidArrayOffset, this.size, id);
            } else {
                fieldPos = this.ctx.b.linearSearchUb2(this.fidArrayOffset, this.size, id);
            }
        } else if (this.fidSorted) {
            fieldPos = this.ctx.b.binarySearchUb4(this.fidArrayOffset, this.size, id);
        } else {
            fieldPos = this.ctx.b.linearSearchUb4(this.fidArrayOffset, this.size, id);
        }
        if (fieldPos < 0) {
            return -1;
        }
        return fieldPos;
    }

    public String getFieldName(int child) {
        int fid;
        OsonHeader header = this.ctx.getHeader();
        if (header.numFieldIdBytes() == 1) {
            fid = this.ctx.b.getUB1(this.fidArrayOffset + child);
        } else if (header.numFieldIdBytes() == 2) {
            fid = this.ctx.b.getUB2(this.fidArrayOffset + (child * 2));
        } else {
            fid = this.ctx.b.getUB4int(this.fidArrayOffset + (child * 4));
        }
        return header.getFieldName(fid - 1);
    }

    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if (!(other instanceof Map)) {
            return false;
        }
        Map<?, ?> m = (Map) other;
        if (m.size() != this.size) {
            return false;
        }
        for (int i = 0; i < this.size; i++) {
            String key = getFieldName(i);
            Object value = getValueInternal(getChildOffset(i));
            if (!m.containsKey(key) || !value.equals(m.get(key))) {
                return false;
            }
        }
        return true;
    }

    public int hashCode() {
        int hc = 0;
        Iterator it = new OsonEntrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, ?> e = (Map.Entry) it.next();
            hc += e.hashCode();
        }
        return hc;
    }
}
