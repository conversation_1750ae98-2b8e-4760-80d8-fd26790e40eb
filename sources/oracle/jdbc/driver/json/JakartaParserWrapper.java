package oracle.jdbc.driver.json;

import jakarta.json.JsonArray;
import jakarta.json.JsonException;
import jakarta.json.JsonNumber;
import jakarta.json.JsonObject;
import jakarta.json.JsonValue;
import jakarta.json.stream.JsonLocation;
import jakarta.json.stream.JsonParser;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Wrapper;
import oracle.jdbc.driver.json.binary.OsonParserImpl;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonParser;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/JakartaParserWrapper.class */
public class JakartaParserWrapper implements Wrapper, JsonParser {
    OracleJsonParser wrapped;
    JsonArray currentVector;
    int vectorPosition;

    public JakartaParserWrapper(OracleJsonParser wrapped) {
        this.wrapped = wrapped;
    }

    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> cls) throws SQLException {
        if (cls.isInstance(this.wrapped)) {
            return (T) this.wrapped;
        }
        throw new SQLException(OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, cls.getName()).getMessage());
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return iface.isInstance(this.wrapped);
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public void close() throws JsonException {
        try {
            this.wrapped.close();
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    public BigDecimal getBigDecimal() {
        if (this.currentVector != null && this.vectorPosition < this.currentVector.size()) {
            return ((JsonNumber) this.currentVector.get(this.vectorPosition)).bigDecimalValue();
        }
        return this.wrapped.getBigDecimal();
    }

    public int getInt() {
        if (this.currentVector != null && this.vectorPosition < this.currentVector.size()) {
            return ((JsonNumber) this.currentVector.get(this.vectorPosition)).intValue();
        }
        return this.wrapped.getInt();
    }

    public JsonLocation getLocation() {
        JsonLocation NO_LOCATION = new JsonLocation() { // from class: oracle.jdbc.driver.json.JakartaParserWrapper.1
            public long getColumnNumber() {
                return -1L;
            }

            public long getLineNumber() {
                return -1L;
            }

            public long getStreamOffset() {
                if (JakartaParserWrapper.this.wrapped instanceof OsonParserImpl) {
                    return ((OsonParserImpl) JakartaParserWrapper.this.wrapped).getStreamOffset();
                }
                return -1L;
            }
        };
        return NO_LOCATION;
    }

    public long getLong() {
        if (this.currentVector != null && this.vectorPosition < this.currentVector.size()) {
            return ((JsonNumber) this.currentVector.get(this.vectorPosition)).longValue();
        }
        return this.wrapped.getLong();
    }

    public String getString() {
        return this.wrapped.getString();
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public boolean hasNext() throws JsonException {
        try {
            if (this.currentVector == null) {
                if (!this.wrapped.hasNext()) {
                    return false;
                }
            }
            return true;
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    public boolean isIntegralNumber() {
        if (this.currentVector != null && this.vectorPosition < this.currentVector.size()) {
            return ((JsonNumber) this.currentVector.get(this.vectorPosition)).isIntegral();
        }
        return this.wrapped.isIntegralNumber();
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonParser.Event next() throws JsonException {
        if (this.currentVector != null) {
            if (this.vectorPosition < this.currentVector.size() - 1) {
                this.vectorPosition++;
                return JsonParser.Event.VALUE_NUMBER;
            }
            if (this.vectorPosition == this.currentVector.size() - 1) {
                this.vectorPosition++;
                return JsonParser.Event.END_ARRAY;
            }
            this.currentVector = null;
        }
        try {
            OracleJsonParser.Event event = this.wrapped.next();
            switch (event) {
                case END_ARRAY:
                    return JsonParser.Event.END_ARRAY;
                case END_OBJECT:
                    return JsonParser.Event.END_OBJECT;
                case KEY_NAME:
                    return JsonParser.Event.KEY_NAME;
                case START_ARRAY:
                    return JsonParser.Event.START_ARRAY;
                case START_OBJECT:
                    return JsonParser.Event.START_OBJECT;
                case VALUE_BINARY:
                case VALUE_TIMESTAMP:
                case VALUE_TIMESTAMPTZ:
                case VALUE_DATE:
                case VALUE_INTERVALDS:
                case VALUE_INTERVALYM:
                case VALUE_STRING:
                    return JsonParser.Event.VALUE_STRING;
                case VALUE_DOUBLE:
                case VALUE_FLOAT:
                case VALUE_DECIMAL:
                    return JsonParser.Event.VALUE_NUMBER;
                case VALUE_FALSE:
                    return JsonParser.Event.VALUE_FALSE;
                case VALUE_TRUE:
                    return JsonParser.Event.VALUE_TRUE;
                case VALUE_VECTOR:
                    this.currentVector = (JsonArray) this.wrapped.getValue().asJsonVector().wrap(JsonArray.class);
                    this.vectorPosition = -1;
                    return JsonParser.Event.START_ARRAY;
                case VALUE_NULL:
                default:
                    return JsonParser.Event.VALUE_NULL;
            }
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonValue getValue() throws JsonException {
        try {
            if (this.currentVector != null) {
                if (this.vectorPosition == -1) {
                    return this.currentVector;
                }
                if (this.vectorPosition < this.currentVector.size()) {
                    return (JsonValue) this.currentVector.get(this.vectorPosition);
                }
                throw new IllegalStateException();
            }
            return (JsonValue) this.wrapped.getValue().wrap(JsonValue.class);
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonObject getObject() throws JsonException {
        try {
            return (JsonObject) this.wrapped.getObject().wrap(JsonObject.class);
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public JsonArray getArray() throws JsonException {
        if (this.currentVector != null) {
            if (this.vectorPosition == -1) {
                return this.currentVector;
            }
            throw new IllegalStateException();
        }
        try {
            return (JsonArray) this.wrapped.getArray().wrap(JsonArray.class);
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public void skipObject() throws JsonException {
        try {
            this.wrapped.skipObject();
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: jakarta.json.JsonException */
    public void skipArray() throws JsonException {
        try {
            this.wrapped.skipArray();
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }
}
