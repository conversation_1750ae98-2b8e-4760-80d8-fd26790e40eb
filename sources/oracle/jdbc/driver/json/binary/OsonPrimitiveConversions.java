package oracle.jdbc.driver.json.binary;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.Period;
import java.time.ZoneOffset;
import java.util.concurrent.TimeUnit;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.net.ns.SQLnetDef;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonPrimitiveConversions.class */
public final class OsonPrimitiveConversions {
    private static final int HUNDIGMAX = 66;
    private static final int BIGINTARRAYMAX = 54;
    private static final int BIGLENMAX = 22;
    private static final byte DIGEND = 21;
    private static final int LNXSGNBT = 128;
    private static final byte LNXDIGS = 20;
    private static final int LNXBASE = 100;
    private static final int LNXEXPMX = 127;
    private static final byte LNXEXPBS = 64;
    private static final int LNXEXPMN = 0;
    private static final byte ODIGEND = 9;
    private static final byte MAX_LONG_BASE100_DIGITS = 9;
    public static int SIZE_TIMESTAMP = 11;
    public static int SIZE_TIMESTAMP_NOFRAC = 7;
    public static int SIZE_DATE = 7;
    public static int SIZE_TIMESTAMPTZ = 13;
    static int OFFSET_HOUR = 20;
    static int OFFSET_MINUTE = 60;
    private static byte REGIONIDBIT = Byte.MIN_VALUE;
    private static final int MAXYEAR = 9999;
    static final int INTERVAL_BYTE_OFFSET = 60;
    static final int INTERVAL_INT_OFFSET = Integer.MIN_VALUE;
    static final int SECONDS_PER_DAY = 86400;
    static final int HOURS_PER_DAY = 24;
    static final int MINUTES_PER_HOUR = 60;
    static final int SECONDS_PER_MINUTE = 60;

    private static int nanos(byte[] bytes) {
        int nanos = bytes.length == SIZE_TIMESTAMP_NOFRAC ? 0 : getNanos(bytes, 7);
        return nanos;
    }

    private static void yearError(OracleJsonExceptions.ExceptionFactory f, int year) {
        if (year < 1 || year > MAXYEAR) {
            throw OracleJsonExceptions.BAD_YEAR.create(f, Integer.valueOf(year));
        }
    }

    public static boolean isPosInf(byte[] b) {
        return _isInf(b);
    }

    public static boolean isNegInf(byte[] b, int len, int offset) {
        return len == 1 && b[offset] == 0;
    }

    private static void appendInt(StringBuilder result, int n, int i) {
        if (n < 0) {
            result.append("-");
            n = -n;
        }
        int tmp = n;
        while (tmp > 0) {
            tmp /= 10;
            i--;
        }
        while (i > 0) {
            result.append('0');
            i--;
        }
        if (n != 0) {
            result.append(n);
        }
    }

    public static String timestampToString(OracleJsonExceptions.ExceptionFactory f, byte[] bytes) {
        StringBuilder result = new StringBuilder(27);
        int year = getJavaYear(bytes[0] & 255, bytes[1] & 255);
        yearError(f, year);
        int month = bytes[2] & 255;
        int day = bytes[3] & 255;
        int hours = (bytes[4] & 255) - 1;
        int minutes = (bytes[5] & 255) - 1;
        int seconds = (bytes[6] & 255) - 1;
        int nanos = nanos(bytes);
        isoTimestamp(result, false, year, month, day, hours, minutes, seconds, nanos);
        return result.toString();
    }

    private static void isoTimestamp(StringBuilder result, boolean alwaysNanos, int year, int month, int day, int hours, int minutes, int seconds, int nanos) {
        appendInt(result, year, 4);
        result.append("-");
        appendInt(result, month, 2);
        result.append("-");
        appendInt(result, day, 2);
        result.append("T");
        appendInt(result, hours, 2);
        result.append(":");
        appendInt(result, minutes, 2);
        result.append(":");
        appendInt(result, seconds, 2);
        if (nanos > 0 || alwaysNanos) {
            result.append(OracleConnection.CLIENT_INFO_KEY_SEPARATOR);
            if (nanos % 1000 > 0) {
                appendInt(result, nanos, 9);
            } else {
                appendInt(result, (int) TimeUnit.NANOSECONDS.toMicros(nanos), 6);
            }
        }
    }

    public static String timestampTZToString(OracleJsonExceptions.ExceptionFactory f, byte[] bytes) {
        StringBuilder result = new StringBuilder();
        OffsetDateTime odt = timestamptzToOffsetDateTime(f, bytes);
        int year = odt.getYear();
        int month = odt.getMonthValue();
        int day = odt.getDayOfMonth();
        int hours = odt.getHour();
        int minutes = odt.getMinute();
        int seconds = odt.getSecond();
        int nanos = odt.getNano();
        isoTimestamp(result, true, year, month, day, hours, minutes, seconds, nanos);
        ZoneOffset off = odt.getOffset();
        int offSeconds = Math.abs(off.getTotalSeconds());
        int offHours = offSeconds / 3600;
        int offMinutes = (offSeconds % 3600) / 60;
        if (offHours == 0 && offMinutes == 0) {
            result.append("Z");
            return result.toString();
        }
        if (off.getTotalSeconds() < 0) {
            result.append("-");
        } else {
            result.append("+");
        }
        appendInt(result, offHours, 2);
        result.append(":");
        appendInt(result, offMinutes, 2);
        return result.toString();
    }

    public static void assertNoRegionTimestampTZ(OracleJsonExceptions.ExceptionFactory f, byte[] bytes) {
        if ((bytes[11] & REGIONIDBIT) != 0) {
            throw OracleJsonExceptions.BAD_TIMESTAMP_TZ.create(f, new Object[0]);
        }
    }

    public static OffsetDateTime timestamptzToOffsetDateTime(OracleJsonExceptions.ExceptionFactory f, byte[] bytes) {
        assertNoRegionTimestampTZ(f, bytes);
        LocalDateTime ldt = timestampToLocalDateTime(f, bytes);
        OffsetDateTime utc = OffsetDateTime.of(ldt, ZoneOffset.UTC);
        int offHour = bytes[11] - OFFSET_HOUR;
        int offMinute = Math.abs(bytes[12] - OFFSET_MINUTE);
        ZoneOffset zoneOffset = ZoneOffset.ofHoursMinutes(offHour, ((int) Math.signum(offHour)) * offMinute);
        return utc.withOffsetSameInstant(zoneOffset);
    }

    public static byte[] toOracleTimestampTZ(OracleJsonExceptions.ExceptionFactory f, OffsetDateTime i) {
        try {
            Timestamp ts = Timestamp.from(i.toInstant());
            return TIMESTAMPTZ.toBytes((Connection) null, ts, i.getOffset());
        } catch (SQLException e) {
            throw new IllegalStateException(e);
        }
    }

    public static LocalDateTime dateToLocalDateTime(OracleJsonExceptions.ExceptionFactory f, byte[] bytes) {
        int year = getJavaYear(bytes[0] & 255, bytes[1] & 255);
        yearError(f, year);
        int month = bytes[2] & 255;
        int day = bytes[3] & 255;
        int hours = (bytes[4] & 255) - 1;
        int minutes = (bytes[5] & 255) - 1;
        int seconds = (bytes[6] & 255) - 1;
        return LocalDateTime.of(year, month, day, hours, minutes, seconds);
    }

    public static LocalDateTime timestampToLocalDateTime(OracleJsonExceptions.ExceptionFactory f, byte[] bytes) {
        int year = getJavaYear(bytes[0] & 255, bytes[1] & 255);
        yearError(f, year);
        int month = bytes[2] & 255;
        int day = bytes[3] & 255;
        int hours = (bytes[4] & 255) - 1;
        int minutes = (bytes[5] & 255) - 1;
        int seconds = (bytes[6] & 255) - 1;
        int nanos = nanos(bytes);
        return LocalDateTime.of(year, month, day, hours, minutes, seconds, nanos);
    }

    public static byte[] toOracleDate(OracleJsonExceptions.ExceptionFactory f, LocalDateTime local) {
        int year = local.getYear();
        yearError(f, year);
        byte[] result = new byte[SIZE_DATE];
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) local.getMonthValue();
        result[3] = (byte) local.getDayOfMonth();
        result[4] = (byte) (local.getHour() + 1);
        result[5] = (byte) (local.getMinute() + 1);
        result[6] = (byte) (local.getSecond() + 1);
        return result;
    }

    public static byte[] toOracleTimestamp(OracleJsonExceptions.ExceptionFactory f, LocalDateTime local) {
        int year = local.getYear();
        yearError(f, year);
        int nanos = local.getNano();
        byte[] result = new byte[nanos == 0 ? SIZE_TIMESTAMP_NOFRAC : SIZE_TIMESTAMP];
        result[0] = (byte) ((year / 100) + 100);
        result[1] = (byte) ((year % 100) + 100);
        result[2] = (byte) local.getMonthValue();
        result[3] = (byte) local.getDayOfMonth();
        result[4] = (byte) (local.getHour() + 1);
        result[5] = (byte) (local.getMinute() + 1);
        result[6] = (byte) (local.getSecond() + 1);
        if (nanos > 0) {
            result[7] = (byte) (nanos >> 24);
            result[8] = (byte) ((nanos >> 16) & 255);
            result[9] = (byte) ((nanos >> 8) & 255);
            result[10] = (byte) (nanos & 255);
        }
        return result;
    }

    public static String dateToString(OracleJsonExceptions.ExceptionFactory f, byte[] bytes) {
        int year = getJavaYear(bytes[0] & 255, bytes[1] & 255);
        yearError(f, year);
        int month = bytes[2] & 255;
        int day = bytes[3] & 255;
        int hours = (bytes[4] & 255) - 1;
        int minutes = (bytes[5] & 255) - 1;
        int seconds = (bytes[6] & 255) - 1;
        StringBuilder result = new StringBuilder(27);
        appendInt(result, year, 4);
        result.append("-");
        appendInt(result, month, 2);
        result.append("-");
        appendInt(result, day, 2);
        result.append("T");
        appendInt(result, hours, 2);
        result.append(":");
        appendInt(result, minutes, 2);
        result.append(":");
        appendInt(result, seconds, 2);
        return result.toString();
    }

    public static Duration intervalDSToDuration(byte[] raw) {
        int d = getDaysFromIntervalDS(raw);
        int h = getHoursFromIntervalDS(raw);
        int m = getMinutesFromIntervalDS(raw);
        int s = getSecondsFromIntervalDS(raw);
        int n = getNanosFromIntervalDS(raw);
        long secs = (d * 86400) + (h * 3600) + (m * 60) + s;
        return Duration.ofSeconds(secs, n);
    }

    public static String serializeIntervalDS(OracleJsonExceptions.ExceptionFactory f, byte[] raw) {
        long days = getDaysFromIntervalDS(raw);
        long hrs = getHoursFromIntervalDS(raw);
        long mins = getMinutesFromIntervalDS(raw);
        long secs = getSecondsFromIntervalDS(raw);
        long nanos = getNanosFromIntervalDS(raw);
        int micros = (int) (nanos / 1000);
        long nanos2 = nanos % 1000;
        if (days < 0 || hrs < 0 || mins < 0 || secs < 0 || micros < 0 || nanos2 < 0) {
            throw OracleJsonExceptions.NOT_IMPLEMENTED.create(f, new Object[0]);
        }
        boolean tim = hrs > 0 || mins > 0 || secs > 0 || micros > 0 || nanos2 > 0;
        StringBuilder result = new StringBuilder();
        result.append("P");
        if (days > 0 || !tim) {
            result.append(days);
            result.append("D");
        }
        if (tim) {
            result.append("T");
            if (hrs > 0) {
                result.append(hrs).append("H");
            }
            if (mins > 0) {
                result.append(mins).append("M");
            }
            if (secs > 0 || micros > 0 || nanos2 > 0) {
                result.append(secs);
                if (micros > 0 || nanos2 > 0) {
                    String microsStr = String.valueOf(micros);
                    result.append(OracleConnection.CLIENT_INFO_KEY_SEPARATOR);
                    for (int i = 0; i < 6 - microsStr.length(); i++) {
                        result.append("0");
                    }
                    result.append(micros);
                    if (nanos2 > 0) {
                        String nanosStr = String.valueOf(nanos2);
                        for (int i2 = 0; i2 < 3 - nanosStr.length(); i2++) {
                            result.append("0");
                        }
                        result.append(nanos2);
                    }
                }
                result.append("S");
            }
        }
        return result.toString();
    }

    public static byte[] durationToIntervalDS(Duration duration) {
        long seconds = duration.getSeconds();
        long days = seconds / 86400;
        long hours = duration.toHours() % 24;
        long min = duration.toMinutes() % 60;
        long sec = seconds % 60;
        int frac = duration.getNano();
        int idays = (int) days;
        if (idays != days) {
            throw new IllegalArgumentException();
        }
        byte[] bytes = new byte[11];
        writeIntervalInt(idays, 0, bytes);
        bytes[4] = (byte) (hours + 60);
        bytes[5] = (byte) (min + 60);
        bytes[6] = (byte) (sec + 60);
        writeIntervalInt(frac, 7, bytes);
        return bytes;
    }

    private static void writeIntervalInt(int value, int offset, byte[] bytes) {
        int value2 = value - Integer.MIN_VALUE;
        int offset2 = offset + 1;
        bytes[offset] = (byte) (((-16777216) & value2) >> 24);
        int offset3 = offset2 + 1;
        bytes[offset2] = (byte) ((16711680 & value2) >> 16);
        int offset4 = offset3 + 1;
        bytes[offset3] = (byte) ((65280 & value2) >> 8);
        int i = offset4 + 1;
        bytes[offset4] = (byte) (255 & value2);
    }

    static int getDaysFromIntervalDS(byte[] bytes) {
        return (((((bytes[0] & 255) << 24) | ((bytes[1] & 255) << 16)) | ((bytes[2] & 255) << 8)) | (bytes[3] & 255)) - Integer.MIN_VALUE;
    }

    static int getHoursFromIntervalDS(byte[] bytes) {
        return bytes[4] - 60;
    }

    static int getMinutesFromIntervalDS(byte[] bytes) {
        return bytes[5] - 60;
    }

    static int getSecondsFromIntervalDS(byte[] bytes) {
        return bytes[6] - 60;
    }

    static int getNanosFromIntervalDS(byte[] bytes) {
        return (((((bytes[7] & 255) << 24) | ((bytes[8] & 255) << 16)) | ((bytes[9] & 255) << 8)) | (bytes[10] & 255)) - Integer.MIN_VALUE;
    }

    public static Period intervalYMToPeriod(byte[] raw) {
        int years = getYearFromIntervalYM(raw);
        int months = getMonthFromIntervalYM(raw);
        return Period.of(years, months, 0);
    }

    public static byte[] periodToIntervalYM(OracleJsonExceptions.ExceptionFactory f, Period p) {
        int years = p.getYears();
        int months = p.getMonths();
        if (months > 11) {
            throw OracleJsonExceptions.NOT_IMPLEMENTED.create(f, new Object[0]);
        }
        if (years < 0 || months < 0) {
            throw OracleJsonExceptions.NOT_IMPLEMENTED.create(f, new Object[0]);
        }
        if (p.getDays() != 0) {
            throw OracleJsonExceptions.NO_DAYS_ALLOWED.create(f, new Object[0]);
        }
        byte[] result = new byte[5];
        writeIntervalInt(p.getYears(), 0, result);
        result[4] = (byte) (p.getMonths() + 60);
        return result;
    }

    private static int getMonthFromIntervalYM(byte[] raw) {
        return raw[4] - 60;
    }

    private static int getYearFromIntervalYM(byte[] raw) {
        return (((((raw[0] & 255) << 24) | ((raw[1] & 255) << 16)) | ((raw[2] & 255) << 8)) | (raw[3] & 255)) - Integer.MIN_VALUE;
    }

    public static String serializeIntervalYM(OracleJsonExceptions.ExceptionFactory f, byte[] raw) {
        int years = getYearFromIntervalYM(raw);
        int months = getMonthFromIntervalYM(raw);
        if (years < 0 || months < 0) {
            throw OracleJsonExceptions.NOT_IMPLEMENTED.create(f, new Object[0]);
        }
        StringBuilder result = new StringBuilder();
        result.append('P');
        if (years > 0 || months == 0) {
            result.append(years).append('Y');
        }
        if (months > 0) {
            result.append(months).append('M');
        }
        return result.toString();
    }

    private static final int getNanos(byte[] buffer, int off) {
        int nanos = (buffer[off] & 255) << 24;
        return nanos | ((buffer[off + 1] & 255) << 16) | ((buffer[off + 2] & 255) << 8) | (buffer[off + 3] & 255 & 255);
    }

    private static int getJavaYear(int cent, int decade) {
        int year = ((cent - 100) * 100) + (decade - 100);
        if (year < 0) {
            year++;
        }
        return year;
    }

    static byte[] doubleToCanonicalFormatBytes(double _d) {
        int b0;
        double d = _d;
        if (d == 0.0d) {
            d = 0.0d;
        } else if (d != d) {
            d = Double.NaN;
        }
        long longBits = Double.doubleToLongBits(d);
        byte[] b = new byte[8];
        int lowInt = (int) longBits;
        int highInt = (int) (longBits >> 32);
        int b7 = lowInt;
        int lowInt2 = lowInt >> 8;
        int b6 = lowInt2;
        int lowInt3 = lowInt2 >> 8;
        int b5 = lowInt3;
        int b4 = lowInt3 >> 8;
        int b3 = highInt;
        int highInt2 = highInt >> 8;
        int b2 = highInt2;
        int highInt3 = highInt2 >> 8;
        int b1 = highInt3;
        int highInt4 = highInt3 >> 8;
        if ((highInt4 & 128) == 0) {
            b0 = highInt4 | 128;
        } else {
            b0 = highInt4 ^ (-1);
            b1 ^= -1;
            b2 ^= -1;
            b3 ^= -1;
            b4 ^= -1;
            b5 ^= -1;
            b6 ^= -1;
            b7 ^= -1;
        }
        b[7] = (byte) b7;
        b[6] = (byte) b6;
        b[5] = (byte) b5;
        b[4] = (byte) b4;
        b[3] = (byte) b3;
        b[2] = (byte) b2;
        b[1] = (byte) b1;
        b[0] = (byte) b0;
        return b;
    }

    static double canonicalFormatBytesToDouble(byte[] b) {
        int b0;
        int b1;
        int b2;
        int b3;
        int b4;
        int b5;
        int b6;
        int b7;
        byte b8 = b[0];
        byte b9 = b[1];
        byte b10 = b[2];
        byte b11 = b[3];
        byte b12 = b[4];
        byte b13 = b[5];
        byte b14 = b[6];
        byte b15 = b[7];
        if ((b8 & 128) != 0) {
            b0 = b8 & Byte.MAX_VALUE;
            b1 = b9 & 255;
            b2 = b10 & 255;
            b3 = b11 & 255;
            b4 = b12 & 255;
            b5 = b13 & 255;
            b6 = b14 & 255;
            b7 = b15 & 255;
        } else {
            b0 = (b8 ^ (-1)) & 255;
            b1 = (b9 ^ (-1)) & 255;
            b2 = (b10 ^ (-1)) & 255;
            b3 = (b11 ^ (-1)) & 255;
            b4 = (b12 ^ (-1)) & 255;
            b5 = (b13 ^ (-1)) & 255;
            b6 = (b14 ^ (-1)) & 255;
            b7 = (b15 ^ (-1)) & 255;
        }
        int hiBits = (b0 << 24) | (b1 << 16) | (b2 << 8) | b3;
        int loBits = (b4 << 24) | (b5 << 16) | (b6 << 8) | b7;
        long longBits = (hiBits << 32) | (loBits & SQLnetDef.NSPDDLSLMAX);
        return Double.longBitsToDouble(longBits);
    }

    static byte[] floatToCanonicalFormatBytes(float _f) {
        int b0;
        float f = _f;
        if (f == 0.0f) {
            f = 0.0f;
        } else if (f != f) {
            f = Float.NaN;
        }
        int intBits = Float.floatToIntBits(f);
        byte[] b = new byte[4];
        int b3 = intBits;
        int intBits2 = intBits >> 8;
        int b2 = intBits2;
        int intBits3 = intBits2 >> 8;
        int b1 = intBits3;
        int intBits4 = intBits3 >> 8;
        if ((intBits4 & 128) == 0) {
            b0 = intBits4 | 128;
        } else {
            b0 = intBits4 ^ (-1);
            b1 ^= -1;
            b2 ^= -1;
            b3 ^= -1;
        }
        b[3] = (byte) b3;
        b[2] = (byte) b2;
        b[1] = (byte) b1;
        b[0] = (byte) b0;
        return b;
    }

    static float canonicalFormatBytesToFloat(byte[] b) {
        int b0;
        int b1;
        int b2;
        int b3;
        byte b4 = b[0];
        byte b5 = b[1];
        byte b6 = b[2];
        byte b7 = b[3];
        if ((b4 & 128) != 0) {
            b0 = b4 & Byte.MAX_VALUE;
            b1 = b5 & 255;
            b2 = b6 & 255;
            b3 = b7 & 255;
        } else {
            b0 = (b4 ^ (-1)) & 255;
            b1 = (b5 ^ (-1)) & 255;
            b2 = (b6 ^ (-1)) & 255;
            b3 = (b7 ^ (-1)) & 255;
        }
        int intBits = (b0 << 24) | (b1 << 16) | (b2 << 8) | b3;
        return Float.intBitsToFloat(intBits);
    }

    public static byte[] toNumber(BigDecimal BigDecNum) {
        BigDecimal DBTMP;
        int leftdigs;
        byte bidx;
        int blen;
        int mantlen;
        int oidx;
        int exponent;
        BigDecimal DBTMP2;
        if (BigDecNum == null) {
            throw new IllegalArgumentException();
        }
        byte[] mantissa = new byte[66];
        long[] bnum = new long[54];
        long[] digit = new long[22];
        byte dstart = 21;
        BigDecimal BDABS = BigDecNum.abs();
        if (BigDecNum.signum() == 0) {
            return _makeZero();
        }
        boolean positive = BigDecNum.signum() != -1;
        int scale = BigDecNum.scale();
        if (scale < 0) {
            BigDecNum.setScale(0);
            scale = 0;
        }
        int rad = BDABS.compareTo(BigDecimal.valueOf(1L));
        int moves = 0;
        if (rad == -1) {
            do {
                moves++;
                DBTMP2 = BDABS.movePointRight(moves);
            } while (DBTMP2.compareTo(BigDecimal.valueOf(1L)) < 0);
            leftdigs = -moves;
        } else {
            do {
                moves++;
                DBTMP = BDABS.movePointLeft(moves);
            } while (DBTMP.compareTo(BigDecimal.valueOf(1L)) >= 0);
            leftdigs = moves;
        }
        byte[] temp = BDABS.movePointRight(scale).toBigInteger().toByteArray();
        if (temp.length > 54) {
            throw new IllegalArgumentException();
        }
        for (int i = 0; i < temp.length; i++) {
            if (temp[i] < 0) {
                bnum[i] = temp[i] + 256;
            } else {
                bnum[i] = temp[i];
            }
        }
        int blen2 = temp.length;
        switch (blen2 % 3) {
            case 1:
                digit[21] = bnum[0];
                bidx = (byte) (0 + 1);
                blen = blen2 - 1;
                break;
            case 2:
                digit[21] = (bnum[0] << 8) + bnum[0 + 1];
                bidx = (byte) (0 + 2);
                blen = blen2 - 2;
                break;
            default:
                long value = (bnum[0] << 16) + (bnum[0 + 1] << 8) + bnum[0 + 2];
                digit[21] = value % 1000000;
                digit[21 - 1] = value / 1000000;
                dstart = (byte) (21 - (digit[21 - 1] != 0 ? 1 : 0));
                bidx = (byte) (0 + 3);
                blen = blen2 - 3;
                break;
        }
        while (blen != 0) {
            long value2 = (bnum[bidx] << 4) + (bnum[bidx + 1] >> 4);
            byte b = 21;
            while (true) {
                byte digidx = b;
                if (digidx >= dstart) {
                    long value3 = value2 + (digit[digidx] << 12);
                    digit[digidx] = value3 % 1000000;
                    value2 = value3 / 1000000;
                    b = (byte) (digidx - 1);
                } else {
                    if (value2 != 0) {
                        dstart = (byte) (dstart - 1);
                        digit[dstart] = value2;
                    }
                    long value4 = ((bnum[bidx + 1] & 15) << 8) + bnum[bidx + 2];
                    byte b2 = 21;
                    while (true) {
                        byte digidx2 = b2;
                        if (digidx2 >= dstart) {
                            long value5 = value4 + (digit[digidx2] << 12);
                            digit[digidx2] = value5 % 1000000;
                            value4 = value5 / 1000000;
                            b2 = (byte) (digidx2 - 1);
                        } else {
                            if (value4 != 0) {
                                dstart = (byte) (dstart - 1);
                                digit[dstart] = value4;
                            }
                            bidx = (byte) (bidx + 3);
                            blen -= 3;
                        }
                    }
                }
            }
        }
        byte b3 = (byte) (digit[dstart] / 10000);
        mantissa[0] = b3;
        if (b3 != 0) {
            mantlen = (3 * (21 - dstart)) + 3;
            mantissa[0 + 1] = (byte) ((digit[dstart] % 10000) / 100);
            mantissa[0 + 2] = (byte) (digit[dstart] % 100);
            oidx = 0 + 3;
        } else {
            byte b4 = (byte) ((digit[dstart] % 10000) / 100);
            mantissa[0] = b4;
            if (b4 != 0) {
                mantlen = (3 * (21 - dstart)) + 2;
                mantissa[0 + 1] = (byte) (digit[dstart] % 100);
                oidx = 0 + 2;
            } else {
                mantissa[0] = (byte) digit[dstart];
                mantlen = (3 * (21 - dstart)) + 1;
                oidx = 0 + 1;
            }
        }
        byte b5 = dstart;
        while (true) {
            byte digidx3 = (byte) (b5 + 1);
            if (digidx3 <= 21) {
                mantissa[oidx] = (byte) (digit[digidx3] / 10000);
                mantissa[oidx + 1] = (byte) ((digit[digidx3] % 10000) / 100);
                mantissa[oidx + 2] = (byte) (digit[digidx3] % 100);
                oidx += 3;
                b5 = digidx3;
            } else {
                for (int i2 = oidx - 1; i2 >= 0 && mantissa[i2] == 0; i2--) {
                    mantlen--;
                }
                if (scale > 0 && (scale & 1) != 0) {
                    int len = mantlen;
                    byte[] buf = new byte[len + 1];
                    if (mantissa[0] <= 9) {
                        int i3 = 0;
                        while (i3 < len - 1) {
                            buf[i3] = (byte) (((mantissa[i3] % 10) * 10) + (mantissa[i3 + 1] / 10));
                            i3++;
                        }
                        buf[i3] = (byte) ((mantissa[i3] % 10) * 10);
                        if (buf[len - 1] == 0) {
                            mantlen--;
                        }
                    } else {
                        buf[len] = (byte) ((mantissa[len - 1] % 10) * 10);
                        int i4 = len - 1;
                        while (i4 > 0) {
                            buf[i4] = (byte) ((mantissa[i4] / 10) + ((mantissa[i4 - 1] % 10) * 10));
                            i4--;
                        }
                        buf[i4] = (byte) (mantissa[i4] / 10);
                        if (buf[len] > 0) {
                            mantlen++;
                        }
                    }
                    System.arraycopy(buf, 0, mantissa, 0, mantlen);
                }
                if (mantlen > 20) {
                    mantlen = 20;
                    if (mantissa[20] >= 50) {
                        int i5 = 20 - 1;
                        mantissa[i5] = (byte) (mantissa[i5] + 1);
                        while (true) {
                            if (mantissa[i5] == 100) {
                                if (i5 == 0) {
                                    leftdigs++;
                                    mantissa[i5] = 1;
                                } else {
                                    mantissa[i5] = 0;
                                    i5--;
                                    mantissa[i5] = (byte) (mantissa[i5] + 1);
                                }
                            }
                        }
                    }
                    for (int i6 = 20 - 1; i6 >= 0 && mantissa[i6] == 0; i6--) {
                        mantlen--;
                    }
                }
                if (leftdigs <= 0) {
                    if (mantissa[0] < 10) {
                        exponent = ((-(2 - leftdigs)) / 2) + 1;
                    } else {
                        exponent = (-(2 - leftdigs)) / 2;
                    }
                } else {
                    exponent = (leftdigs - 1) / 2;
                }
                if (exponent > 62) {
                    throw new IllegalArgumentException();
                }
                if (exponent < -65) {
                    throw new IllegalArgumentException();
                }
                byte[] oranum = new byte[mantlen + 1];
                oranum[0] = (byte) exponent;
                System.arraycopy(mantissa, 0, oranum, 1, mantlen);
                return _toLnxFmt(oranum, positive);
            }
        }
    }

    public static byte[] toNumber(BigInteger BigIntNum) {
        byte[] temp;
        int exponent;
        byte bidx;
        int blen;
        int mantlen;
        int oidx;
        if (BigIntNum == null) {
            throw new IllegalArgumentException();
        }
        byte[] mantissa = new byte[66];
        long[] bnum = new long[54];
        long[] digit = new long[22];
        byte dstart = 21;
        boolean positive = true;
        if (BigIntNum.signum() == 0) {
            return _makeZero();
        }
        if (BigIntNum.signum() == -1) {
            BigInteger Num = BigIntNum.abs();
            positive = false;
            temp = Num.toByteArray();
            exponent = (int) Math.floor(Num.bitLength() * 0.1505149978319906d);
        } else {
            temp = BigIntNum.toByteArray();
            exponent = (int) Math.floor(BigIntNum.bitLength() * 0.1505149978319906d);
        }
        if (BigIntNum.abs().compareTo(BigInteger.valueOf(100L).pow(exponent)) < 0) {
            exponent--;
        }
        if (temp.length > 54) {
            throw new IllegalArgumentException();
        }
        for (int i = 0; i < temp.length; i++) {
            if (temp[i] < 0) {
                bnum[i] = temp[i] + 256;
            } else {
                bnum[i] = temp[i];
            }
        }
        int blen2 = temp.length;
        switch (blen2 % 3) {
            case 1:
                digit[21] = bnum[0];
                bidx = (byte) (0 + 1);
                blen = blen2 - 1;
                break;
            case 2:
                digit[21] = (bnum[0] << 8) + bnum[0 + 1];
                bidx = (byte) (0 + 2);
                blen = blen2 - 2;
                break;
            default:
                long value = (bnum[0] << 16) + (bnum[0 + 1] << 8) + bnum[0 + 2];
                digit[21] = value % 1000000;
                digit[21 - 1] = value / 1000000;
                dstart = (byte) (21 - (digit[21 - 1] != 0 ? 1 : 0));
                bidx = (byte) (0 + 3);
                blen = blen2 - 3;
                break;
        }
        while (blen != 0) {
            long value2 = (bnum[bidx] << 4) + (bnum[bidx + 1] >> 4);
            byte b = 21;
            while (true) {
                byte digidx = b;
                if (digidx >= dstart) {
                    long value3 = value2 + (digit[digidx] << 12);
                    digit[digidx] = value3 % 1000000;
                    value2 = value3 / 1000000;
                    b = (byte) (digidx - 1);
                } else {
                    if (value2 != 0) {
                        dstart = (byte) (dstart - 1);
                        digit[dstart] = value2;
                    }
                    long value4 = ((bnum[bidx + 1] & 15) << 8) + bnum[bidx + 2];
                    byte b2 = 21;
                    while (true) {
                        byte digidx2 = b2;
                        if (digidx2 >= dstart) {
                            long value5 = value4 + (digit[digidx2] << 12);
                            digit[digidx2] = value5 % 1000000;
                            value4 = value5 / 1000000;
                            b2 = (byte) (digidx2 - 1);
                        } else {
                            if (value4 != 0) {
                                dstart = (byte) (dstart - 1);
                                digit[dstart] = value4;
                            }
                            bidx = (byte) (bidx + 3);
                            blen -= 3;
                        }
                    }
                }
            }
        }
        byte b3 = (byte) (digit[dstart] / 10000);
        mantissa[0] = b3;
        if (b3 != 0) {
            mantlen = (3 * (21 - dstart)) + 3;
            mantissa[0 + 1] = (byte) ((digit[dstart] % 10000) / 100);
            mantissa[0 + 2] = (byte) (digit[dstart] % 100);
            oidx = 0 + 3;
        } else {
            byte b4 = (byte) ((digit[dstart] % 10000) / 100);
            mantissa[0] = b4;
            if (b4 != 0) {
                mantlen = (3 * (21 - dstart)) + 2;
                mantissa[0 + 1] = (byte) (digit[dstart] % 100);
                oidx = 0 + 2;
            } else {
                mantissa[0] = (byte) digit[dstart];
                mantlen = (3 * (21 - dstart)) + 1;
                oidx = 0 + 1;
            }
        }
        byte b5 = dstart;
        while (true) {
            byte digidx3 = (byte) (b5 + 1);
            if (digidx3 <= 21) {
                mantissa[oidx] = (byte) (digit[digidx3] / 10000);
                mantissa[oidx + 1] = (byte) ((digit[digidx3] % 10000) / 100);
                mantissa[oidx + 2] = (byte) (digit[digidx3] % 100);
                oidx += 3;
                b5 = digidx3;
            } else {
                for (int i2 = oidx - 1; i2 >= 0 && mantissa[i2] == 0; i2--) {
                    mantlen--;
                }
                if (mantlen > 19) {
                    mantlen = 19;
                    if (mantissa[20] >= 50) {
                        int i3 = 20 - 1;
                        mantissa[i3] = (byte) (mantissa[i3] + 1);
                        while (true) {
                            if (mantissa[i3] == 100) {
                                if (i3 == 0) {
                                    exponent++;
                                    mantissa[i3] = 1;
                                } else {
                                    mantissa[i3] = 0;
                                    i3--;
                                    mantissa[i3] = (byte) (mantissa[i3] + 1);
                                }
                            }
                        }
                        for (int i4 = 19 - 1; i4 >= 0 && mantissa[i4] == 0; i4--) {
                            mantlen--;
                        }
                    }
                }
                if (exponent > 62) {
                    throw new IllegalArgumentException();
                }
                byte[] oranum = new byte[mantlen + 1];
                oranum[0] = (byte) exponent;
                System.arraycopy(mantissa, 0, oranum, 1, mantlen);
                return _toLnxFmt(oranum, positive);
            }
        }
    }

    static byte[] _makeZero() {
        byte[] num = {Byte.MIN_VALUE};
        return num;
    }

    static byte[] _toLnxFmt(byte[] num, boolean pos) {
        byte[] tmp;
        int numl = num.length;
        if (pos) {
            tmp = new byte[numl];
            tmp[0] = (byte) (num[0] + 128 + 64 + 1);
            for (int i = 1; i < numl; i++) {
                tmp[i] = (byte) (num[i] + 1);
            }
        } else {
            if (numl - 1 < 20) {
                tmp = new byte[numl + 1];
            } else {
                tmp = new byte[numl];
            }
            tmp[0] = (byte) ((((num[0] + 128) + 64) + 1) ^ (-1));
            int i2 = 1;
            while (i2 < numl) {
                tmp[i2] = (byte) (101 - num[i2]);
                i2++;
            }
            if (i2 <= 20) {
                tmp[i2] = 102;
            }
        }
        return tmp;
    }

    static byte[] toNumber(int value) {
        return lnxmin(value);
    }

    public static byte[] toNumber(long value) {
        return lnxmin(value);
    }

    public static byte[] lnxmin(long longNum) {
        if (longNum <= 2147483647L && longNum >= -2147483648L) {
            return lnxmin32((int) longNum);
        }
        return lnxmin64(longNum);
    }

    private static byte[] lnxmin32(int val) {
        byte[] b;
        if (val == 0) {
            b = new byte[]{Byte.MIN_VALUE};
        } else if (val < 0) {
            if (val == Integer.MIN_VALUE) {
                b = new byte[7];
                encodeIntMinValue(b, 0);
            } else if ((-val) < 100) {
                b = new byte[]{62, (byte) (101 + val), 102};
            } else if ((-val) < 10000) {
                int x = (-val) % 100;
                if (x != 0) {
                    b = new byte[]{0, 0, (byte) (101 - x), 102};
                } else {
                    b = new byte[]{0, 0, 102};
                }
                b[0] = 61;
                b[1] = (byte) (101 - ((-val) / 100));
            } else if ((-val) < 1000000) {
                int x2 = (-val) % 100;
                if (x2 != 0) {
                    b = new byte[]{0, 0, (byte) (101 - (((-val) % 10000) / 100)), (byte) (101 - x2), 102};
                } else {
                    int x3 = ((-val) % 10000) / 100;
                    if (x3 != 0) {
                        b = new byte[]{0, 0, (byte) (101 - x3), 102};
                    } else {
                        b = new byte[]{0, 0, 102};
                    }
                }
                b[0] = 60;
                b[1] = (byte) (101 - ((-val) / 10000));
            } else if ((-val) < 100000000) {
                int x4 = (-val) % 100;
                if (x4 != 0) {
                    b = new byte[]{0, 0, (byte) (101 - (((-val) % 1000000) / 10000)), (byte) (101 - (((-val) % 10000) / 100)), (byte) (101 - x4), 102};
                } else {
                    int x5 = ((-val) % 10000) / 100;
                    if (x5 != 0) {
                        b = new byte[]{0, 0, (byte) (101 - (((-val) % 1000000) / 10000)), (byte) (101 - x5), 102};
                    } else {
                        int x6 = ((-val) % 1000000) / 10000;
                        if (x6 != 0) {
                            b = new byte[]{0, 0, (byte) (101 - x6), 102};
                        } else {
                            b = new byte[]{0, 0, 102};
                        }
                    }
                }
                b[0] = 59;
                b[1] = (byte) (101 - ((-val) / 1000000));
            } else {
                int x7 = (-val) % 100;
                if (x7 != 0) {
                    b = new byte[]{0, 0, (byte) (101 - (((-val) % 100000000) / 1000000)), (byte) (101 - (((-val) % 1000000) / 10000)), (byte) (101 - (((-val) % 10000) / 100)), (byte) (101 - x7), 102};
                } else {
                    int x8 = ((-val) % 10000) / 100;
                    if (x8 != 0) {
                        b = new byte[]{0, 0, (byte) (101 - (((-val) % 100000000) / 1000000)), (byte) (101 - (((-val) % 1000000) / 10000)), (byte) (101 - x8), 102};
                    } else {
                        int x9 = ((-val) % 1000000) / 10000;
                        if (x9 != 0) {
                            b = new byte[]{0, 0, (byte) (101 - (((-val) % 100000000) / 1000000)), (byte) (101 - x9), 102};
                        } else {
                            int x10 = ((-val) % 100000000) / 1000000;
                            if (x10 != 0) {
                                b = new byte[]{0, 0, (byte) (101 - x10), 102};
                            } else {
                                b = new byte[]{0, 0, 102};
                            }
                        }
                    }
                }
                b[0] = 58;
                b[1] = (byte) (101 - ((-val) / 100000000));
            }
        } else if (val < 100) {
            b = new byte[]{-63, (byte) (val + 1)};
        } else if (val < 10000) {
            int x11 = val % 100;
            if (x11 != 0) {
                b = new byte[]{0, 0, (byte) (x11 + 1)};
            } else {
                b = new byte[2];
            }
            b[0] = -62;
            b[1] = (byte) ((val / 100) + 1);
        } else if (val < 1000000) {
            int x12 = val % 100;
            if (x12 != 0) {
                b = new byte[]{0, 0, (byte) (((val % 10000) / 100) + 1), (byte) (x12 + 1)};
            } else {
                int x13 = (val % 10000) / 100;
                if (x13 != 0) {
                    b = new byte[]{0, 0, (byte) (x13 + 1)};
                } else {
                    b = new byte[2];
                }
            }
            b[0] = -61;
            b[1] = (byte) ((val / 10000) + 1);
        } else if (val < 100000000) {
            int x14 = val % 100;
            if (x14 != 0) {
                b = new byte[]{0, 0, (byte) (((val % 1000000) / 10000) + 1), (byte) (((val % 10000) / 100) + 1), (byte) (x14 + 1)};
            } else {
                int x15 = (val % 10000) / 100;
                if (x15 != 0) {
                    b = new byte[]{0, 0, (byte) (((val % 1000000) / 10000) + 1), (byte) (x15 + 1)};
                } else {
                    int x16 = (val % 1000000) / 10000;
                    if (x16 != 0) {
                        b = new byte[]{0, 0, (byte) (x16 + 1)};
                    } else {
                        b = new byte[2];
                    }
                }
            }
            b[0] = -60;
            b[1] = (byte) ((val / 1000000) + 1);
        } else {
            int x17 = val % 100;
            if (x17 != 0) {
                b = new byte[]{0, 0, (byte) (((val % 100000000) / 1000000) + 1), (byte) (((val % 1000000) / 10000) + 1), (byte) (((val % 10000) / 100) + 1), (byte) (x17 + 1)};
            } else {
                int x18 = (val % 10000) / 100;
                if (x18 != 0) {
                    b = new byte[]{0, 0, (byte) (((val % 100000000) / 1000000) + 1), (byte) (((val % 1000000) / 10000) + 1), (byte) (x18 + 1)};
                } else {
                    int x19 = (val % 1000000) / 10000;
                    if (x19 != 0) {
                        b = new byte[]{0, 0, (byte) (((val % 100000000) / 1000000) + 1), (byte) (x19 + 1)};
                    } else {
                        int x20 = (val % 100000000) / 1000000;
                        if (x20 != 0) {
                            b = new byte[]{0, 0, (byte) (x20 + 1)};
                        } else {
                            b = new byte[2];
                        }
                    }
                }
            }
            b[0] = -59;
            b[1] = (byte) ((val / 100000000) + 1);
        }
        return b;
    }

    private static void encodeIntMinValue(byte[] b, int offset) {
        b[offset] = 58;
        b[offset + 1] = 80;
        b[offset + 2] = 54;
        b[offset + 3] = 53;
        b[offset + 4] = 65;
        b[offset + 5] = 53;
        b[offset + 6] = 102;
    }

    private static byte[] lnxmin64(long val) {
        long val2;
        boolean z = val >= 0;
        boolean sign = z;
        if (z) {
            val = -val;
        }
        int exponent = log100(val);
        int digits = exponent;
        long qL = val / 100;
        long leastDigit = 0;
        if (0 == (qL * 100) - val) {
            do {
                digits--;
                val2 = qL;
                qL = val2 / 100;
                leastDigit = 0;
            } while (0 == (qL * 100) - val2);
            if (digits == 0) {
                if (sign) {
                    byte[] dst = new byte[2];
                    lnxminEncodeP1((int) 0, exponent, dst, 0);
                    return dst;
                }
                byte[] dst2 = new byte[3];
                lnxminEncodeN1((int) 0, exponent, dst2, 0);
                return dst2;
            }
        }
        if (sign) {
            byte[] dst3 = new byte[digits + 2];
            lnxminEncodeP(qL, exponent, digits, (int) leastDigit, dst3, 0);
            return dst3;
        }
        byte[] dst4 = new byte[digits + 3];
        lnxminEncodeN(qL, exponent, digits, (int) leastDigit, dst4, 0);
        return dst4;
    }

    private static int log100(long longNum) {
        if (longNum > -10000000000L) {
            return 4;
        }
        if (longNum > -1000000000000L) {
            return 5;
        }
        if (longNum > -100000000000000L) {
            return 6;
        }
        if (longNum > -10000000000000000L) {
            return 7;
        }
        return longNum > -1000000000000000000L ? 8 : 9;
    }

    private static void lnxminEncodeP1(int digit, int exponent, byte[] dst, int offset) {
        dst[offset] = (byte) (193 + exponent);
        dst[offset + 1] = (byte) (1 + digit);
    }

    private static void lnxminEncodeN1(int digit, int exponent, byte[] dst, int offset) {
        dst[offset] = (byte) (62 - exponent);
        dst[offset + 1] = (byte) (101 - digit);
        dst[offset + 2] = 102;
    }

    private static void lnxminEncodeP(long val, int exponent, int digits, int leastDigit, byte[] dst, int offset) {
        dst[offset] = (byte) (193 + exponent);
        int pos = offset + digits + 1;
        int pos2 = pos - 1;
        dst[pos] = (byte) (1 + leastDigit);
        while (true) {
            int i = digits;
            digits--;
            if (i <= 4) {
                break;
            }
            long qL = val / 100;
            int i2 = pos2;
            pos2--;
            dst[i2] = (byte) ((1 + (qL * 100)) - val);
            val = qL;
        }
        int i3 = (int) val;
        while (true) {
            int val32 = i3;
            if (val32 <= -100) {
                int qI = val32 / 100;
                int i4 = pos2;
                pos2--;
                dst[i4] = (byte) ((1 + (qI * 100)) - val32);
                i3 = qI;
            } else {
                dst[pos2] = (byte) (1 - val32);
                return;
            }
        }
    }

    private static void lnxminEncodeN(long val, int exponent, int digits, int leastDigit, byte[] dst, int offset) {
        dst[offset] = (byte) (64 - (2 + exponent));
        int pos = offset + digits + 2;
        int pos2 = pos - 1;
        dst[pos] = 102;
        int pos3 = pos2 - 1;
        dst[pos2] = (byte) (101 - leastDigit);
        while (true) {
            int i = digits;
            digits--;
            if (i <= 4) {
                break;
            }
            long qL = val / 100;
            int i2 = pos3;
            pos3--;
            dst[i2] = (byte) (101 - ((qL * 100) - val));
            val = qL;
        }
        int i3 = (int) val;
        while (true) {
            int val32 = i3;
            if (val32 <= -100) {
                int qI = val32 / 100;
                int i4 = pos3;
                pos3--;
                dst[i4] = (byte) (101 - ((qI * 100) - val32));
                i3 = qI;
            } else {
                dst[pos3] = (byte) (101 + val32);
                return;
            }
        }
    }

    public static BigDecimal toBigDecimalLong(byte[] num) {
        int mantlen = getOraNumLength(num) - 1;
        if (mantlen > 9) {
            return null;
        }
        int scale = (getScale(num) - mantlen) + 1;
        if (mantlen + scale > 9) {
            return null;
        }
        long mantissa = 0;
        long n = 1;
        for (int i = mantlen; i > 0; i--) {
            try {
                mantissa += Math.multiplyExact(unpackBase100(num, i), n);
                n *= 100;
            } catch (ArithmeticException e) {
                return null;
            }
        }
        int baseTenScale = scale * 2;
        while (baseTenScale > 0) {
            try {
                mantissa = Math.multiplyExact(mantissa, 10L);
                baseTenScale--;
            } catch (ArithmeticException e2) {
                return null;
            }
        }
        while (baseTenScale < 0 && mantissa % 10 == 0) {
            mantissa /= 10;
            baseTenScale++;
        }
        if (!_isPositive(num)) {
            mantissa = -mantissa;
        }
        return BigDecimal.valueOf(mantissa, -baseTenScale);
    }

    public static BigDecimal toBigDecimal(byte[] num) {
        if (_isZero(num)) {
            return BigDecimal.valueOf(0L);
        }
        if (_isInf(num)) {
            throw new IllegalArgumentException();
        }
        BigDecimal result = toBigDecimalLong(num);
        return result == null ? toBigDecimalFull(num) : result;
    }

    public static BigDecimal toBigDecimalFull(byte[] num) {
        byte oidx;
        int clen;
        int numbytes;
        int arycnt;
        long[] digit = new long[10];
        boolean positive = _isPositive(num);
        byte[] temp = _fromLnxFmt(num);
        int clen2 = temp.length - 1;
        if ((clen2 & 1) == 1) {
            digit[9] = temp[1];
            oidx = (byte) (1 + 1);
            clen = clen2 - 1;
        } else {
            digit[9] = (temp[1] * 100) + temp[1 + 1];
            oidx = (byte) (1 + 2);
            clen = clen2 - 2;
        }
        byte cnt = 9;
        while (clen != 0) {
            long value = (temp[oidx] * 100) + temp[oidx + 1];
            byte b = 9;
            while (true) {
                byte digidx = b;
                if (digidx < cnt) {
                    break;
                }
                long value2 = value + (digit[digidx] * 10000);
                digit[digidx] = value2 & 65535;
                value = value2 >> 16;
                b = (byte) (digidx - 1);
            }
            if (value != 0) {
            }
            cnt = (byte) (cnt - 1);
            digit[cnt] = value;
            oidx = (byte) (oidx + 2);
            clen -= 2;
        }
        if ((digit[cnt] >> 8) != 0) {
            numbytes = (2 * (9 - cnt)) + 2;
        } else {
            numbytes = (2 * (9 - cnt)) + 1;
        }
        byte[] barray = new byte[numbytes];
        if ((numbytes & 1) == 1) {
            barray[0] = (byte) digit[cnt];
            arycnt = 0 + 1;
        } else {
            barray[0] = (byte) (digit[cnt] >> 8);
            int arycnt2 = 0 + 1;
            barray[arycnt2] = (byte) (digit[cnt] & 255);
            arycnt = arycnt2 + 1;
        }
        while (true) {
            cnt = (byte) (cnt + 1);
            if (cnt > 9) {
                break;
            }
            barray[arycnt] = (byte) (digit[cnt] >> 8);
            barray[arycnt + 1] = (byte) (digit[cnt] & 255);
            arycnt += 2;
        }
        BigInteger bigtemp = new BigInteger(positive ? 1 : -1, barray);
        BigDecimal result = new BigDecimal(bigtemp);
        int scale = (temp[0] - clen2) + 1;
        BigDecimal result2 = result.movePointRight(scale * 2);
        if (scale < 0 && temp[clen2] % 10 == 0) {
            result2 = result2.setScale(-((scale * 2) + 1));
        }
        return result2;
    }

    private static boolean _isZero(byte[] num) {
        if (num[0] == Byte.MIN_VALUE && num.length == 1) {
            return true;
        }
        return false;
    }

    private static boolean _isInf(byte[] num) {
        if (num.length == 2 && num[0] == -1 && num[1] == 101) {
            return true;
        }
        if (num[0] == 0 && num.length == 1) {
            return true;
        }
        return false;
    }

    private static boolean _isPositive(byte[] num) {
        if ((num[0] & Byte.MIN_VALUE) != 0) {
            return true;
        }
        return false;
    }

    private static int getOraNumLength(byte[] num) {
        int n = num.length - 1;
        return (_isPositive(num) || (n == 20 && num[n] != 102)) ? num.length : num.length - 1;
    }

    private static int getScale(byte[] num) {
        if (_isPositive(num)) {
            return (byte) ((num[0] & (-129)) - 65);
        }
        return (byte) (((num[0] ^ (-1)) & (-129)) - 65);
    }

    private static long unpackBase100(byte[] num, int i) {
        return _isPositive(num) ? num[i] - 1 : (byte) (101 - num[i]);
    }

    private static byte[] _fromLnxFmt(byte[] num) {
        byte[] tmp;
        int numl = num.length;
        if (_isPositive(num)) {
            tmp = new byte[numl];
            tmp[0] = (byte) ((num[0] & (-129)) - 65);
            for (int i = 1; i < numl; i++) {
                tmp[i] = (byte) (num[i] - 1);
            }
        } else {
            if (numl - 1 == 20 && num[numl - 1] != 102) {
                tmp = new byte[numl];
            } else {
                tmp = new byte[numl - 1];
            }
            tmp[0] = (byte) (((num[0] ^ (-1)) & (-129)) - 65);
            for (int i2 = 1; i2 < tmp.length; i2++) {
                tmp[i2] = (byte) (101 - num[i2]);
            }
        }
        return tmp;
    }
}
