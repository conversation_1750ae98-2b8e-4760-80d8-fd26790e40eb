package oracle.jdbc.diagnostics;

import java.util.Map;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.spi.OracleResourceProvider;
import oracle.jdbc.spi.TraceEventListenerProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/DefaultTraceEventListenerProvider.class */
public final class DefaultTraceEventListenerProvider implements TraceEventListenerProvider {
    private final TraceEventListener traceEventListener;
    public static final TraceEventListener NO_OP_TRACE_EVENT_LISTENER = (seq, traceContext, userContext) -> {
        return null;
    };

    public DefaultTraceEventListenerProvider() {
        if (DMSFactory.isDMSEnabled() && DMSFactory.getDMSVersion() == DMSFactory.DMSVersion.v11) {
            this.traceEventListener = DMSTraceEventListener.INSTANCE;
        } else {
            this.traceEventListener = NO_OP_TRACE_EVENT_LISTENER;
        }
    }

    @Override // oracle.jdbc.spi.OracleResourceProvider
    public String getName() {
        return OracleConnection.CONNECTION_PROPERTY_PROVIDER_TRACE_EVENT_LISTENER_DEFAULT;
    }

    @Override // oracle.jdbc.spi.TraceEventListenerProvider
    public TraceEventListener getTraceEventListener(Map<OracleResourceProvider.Parameter, CharSequence> parameterValues) {
        return this.traceEventListener;
    }
}
