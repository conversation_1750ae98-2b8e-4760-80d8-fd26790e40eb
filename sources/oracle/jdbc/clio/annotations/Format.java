package oracle.jdbc.clio.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.LOCAL_VARIABLE, ElementType.METHOD})
@Retention(RetentionPolicy.CLASS)
/* loaded from: ojdbc8.jar:oracle/jdbc/clio/annotations/Format.class */
public @interface Format {

    /* loaded from: ojdbc8.jar:oracle/jdbc/clio/annotations/Format$Style.class */
    public enum Style {
        STRING,
        BYTE_ARRAY,
        BYTE_ARRAY_CLONE,
        INTEGER_ARRAY,
        LONG_ARRAY,
        BYTE_BUFFER,
        ISO_DATETIME,
        PACKET_DUMP,
        PROPERTIES
    }

    Style format();

    long[] args() default {};
}
