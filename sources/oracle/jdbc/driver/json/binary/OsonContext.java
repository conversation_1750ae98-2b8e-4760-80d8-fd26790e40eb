package oracle.jdbc.driver.json.binary;

import oracle.jdbc.driver.json.OracleJsonExceptions;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonContext.class */
public class OsonContext {
    protected final OsonBuffer b;
    protected final OsonHeader header;
    protected final OsonValueFactory valueFactory;
    protected final OracleJsonExceptions.ExceptionFactory exceptionFactory;

    public OsonContext(OsonBuffer buffer, OsonHeader header, OsonValueFactory valueFactory, OracleJsonExceptions.ExceptionFactory exceptionFactory) {
        this.b = buffer;
        this.header = header;
        this.exceptionFactory = exceptionFactory;
        this.valueFactory = valueFactory;
    }

    public OsonContext(OsonBuffer buffer, OsonHeader header) {
        this(buffer, header, OracleOsonValueFactory.INSTANCE, OracleJsonExceptions.ORACLE_FACTORY);
    }

    public OsonContext(OsonBuffer buffer) {
        this(buffer, new OsonHeader(buffer, OracleJsonExceptions.ORACLE_FACTORY));
    }

    public OsonContext(OsonContext other) {
        this(other.b, other.header);
    }

    public OsonBuffer getBuffer() {
        return this.b;
    }

    public OsonHeader getHeader() {
        return this.header;
    }

    public OsonValueFactory getFactory() {
        return this.valueFactory;
    }

    public OracleJsonExceptions.ExceptionFactory getExceptionFactory() {
        return this.exceptionFactory;
    }
}
