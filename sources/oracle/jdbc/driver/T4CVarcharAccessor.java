package oracle.jdbc.driver;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.text.DateFormatSymbols;
import java.util.Calendar;
import java.util.GregorianCalendar;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.NUMBER;
import oracle.sql.RAW;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CVarcharAccessor.class */
class T4CVarcharAccessor extends VarcharAccessor implements T4CAccessor {
    T4CMAREngine mare;
    static final int MAX_CALL_LENGTH_PRE102 = 4001;
    static final int MIN_SQL_LENGTH = 32;
    boolean underlyingLong;
    private T4CMarshaller marshaller;
    static final int NONE = -1;
    static final int DAY = 1;
    static final int MM_MONTH = 2;
    static final int FULL_MONTH = 3;
    static final int MON_MONTH = 4;
    static final int YY_YEAR = 5;
    static final int RR_YEAR = 6;
    static final int HH_HOUR = 7;
    static final int HH24_HOUR = 8;
    static final int MINUTE = 9;
    static final int SECOND = 10;
    static final int NSECOND = 11;
    static final int AM = 12;
    static final int TZR = 13;
    static final int TZH = 14;
    static final int TZM = 15;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CVarcharAccessor.class.desiredAssertionStatus();
    }

    T4CVarcharAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, form, external_type, isOutBind, false);
        this.underlyingLong = false;
        this.marshaller = null;
        this.mare = _mare;
        calculateSizeTmpByteArray();
    }

    T4CVarcharAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int maxCodePointLen, int _oacmxl, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, nullable, flags, precision, scale, contflag, total_elems, form, maxCodePointLen);
        this.underlyingLong = false;
        this.marshaller = null;
        this.mare = _mare;
        this.definedColumnType = _definedColumnType;
        this.definedColumnSize = _definedColumnSize;
        calculateSizeTmpByteArray();
        this.oacmxl = _oacmxl;
        if (this.oacmxl == -1) {
            this.underlyingLong = true;
            this.oacmxl = 4000;
        }
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        return getMarshaller().unmarshalOneRow(this);
    }

    int readStreamFromWire(byte[] buffer, int offset, int length, int[] escapeSequenceArr, boolean[] readHeaderArr, boolean[] readAsNonStreamArr, T4CMAREngine mare, T4CTTIoer11 oer) throws SQLException, IOException {
        return getMarshaller().readStreamFromWire(buffer, offset, length, escapeSequenceArr, readHeaderArr, readAsNonStreamArr, mare, oer);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    NUMBER getNUMBER(int currentRow) throws SQLException {
        NUMBER result = null;
        if (this.definedColumnType == 0) {
            result = super.getNUMBER(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                return StringToNUMBER(s.trim());
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    DATE getDATE(int currentRow) throws SQLException {
        DATE result = null;
        if (this.definedColumnType == 0) {
            result = super.getDATE(currentRow);
        } else {
            Date d = getDate(currentRow);
            if (d != null) {
                result = new DATE(d);
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMP getTIMESTAMP(int currentRow) throws SQLException {
        TIMESTAMP result = null;
        if (this.definedColumnType == 0) {
            result = super.getTIMESTAMP(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                Calendar cal = DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTMPFM"), nanos);
                Timestamp ts = new Timestamp(cal.getTimeInMillis());
                ts.setNanos(nanos[0]);
                result = new TIMESTAMP(ts);
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMPTZ getTIMESTAMPTZ(int currentRow) throws SQLException {
        TIMESTAMPTZ result = null;
        if (this.definedColumnType == 0) {
            result = super.getTIMESTAMPTZ(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                Calendar cal = DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTZNFM"), nanos);
                Timestamp ts = new Timestamp(cal.getTimeInMillis());
                ts.setNanos(nanos[0]);
                result = new TIMESTAMPTZ(this.statement.connection, ts, cal);
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMPLTZ getTIMESTAMPLTZ(int currentRow) throws SQLException {
        TIMESTAMPLTZ result = null;
        if (this.definedColumnType == 0) {
            result = super.getTIMESTAMPLTZ(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                Calendar cal = DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTZNFM"), nanos);
                Timestamp ts = new Timestamp(cal.getTimeInMillis());
                ts.setNanos(nanos[0]);
                result = new TIMESTAMPLTZ(this.statement.connection, ts, cal);
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    RAW getRAW(int currentRow) throws SQLException {
        RAW result = null;
        if (this.definedColumnType == 0) {
            result = super.getRAW(currentRow);
        } else if (!this.rowNull[currentRow]) {
            if (this.definedColumnType == -2 || this.definedColumnType == -3 || this.definedColumnType == -4) {
                result = new RAW(getBytesFromHexChars(currentRow));
            } else {
                result = new RAW(super.getBytes(currentRow));
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Datum getOracleObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getOracleObject(currentRow);
        }
        if (this.rowNull == null) {
            throw ((SQLException) DatabaseError.createSqlException(21).fillInStackTrace());
        }
        if (this.rowNull[currentRow]) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.TIMESTAMPLTZ /* -102 */:
                return getTIMESTAMPLTZ(currentRow);
            case oracle.jdbc.OracleTypes.TIMESTAMPTZ /* -101 */:
                return getTIMESTAMPTZ(currentRow);
            case oracle.jdbc.OracleTypes.LONGNVARCHAR /* -16 */:
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case -1:
            case 1:
            case 12:
                return super.getOracleObject(currentRow);
            case oracle.jdbc.OracleTypes.ROWID /* -8 */:
                return getROWID(currentRow);
            case oracle.jdbc.OracleTypes.BIT /* -7 */:
            case oracle.jdbc.OracleTypes.TINYINT /* -6 */:
            case -5:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 16:
                return getNUMBER(currentRow);
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
            case -3:
            case -2:
                return getRAW(currentRow);
            case 91:
                return getDATE(currentRow);
            case 92:
                return getDATE(currentRow);
            case 93:
                return getTIMESTAMP(currentRow);
            default:
                throw ((SQLException) DatabaseError.createSqlException(4).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    byte getByte(int currentRow) throws SQLException {
        byte result = 0;
        if (this.definedColumnType == 0) {
            result = super.getByte(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.byteValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    int getInt(int currentRow) throws SQLException {
        int result = 0;
        if (this.definedColumnType == 0) {
            result = super.getInt(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.intValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    short getShort(int currentRow) throws SQLException {
        short result = 0;
        if (this.definedColumnType == 0) {
            result = super.getShort(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.shortValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    long getLong(int currentRow) throws SQLException {
        long result = 0;
        if (this.definedColumnType == 0) {
            result = super.getLong(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.longValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    float getFloat(int currentRow) throws SQLException {
        float result = 0.0f;
        if (this.definedColumnType == 0) {
            result = super.getFloat(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.floatValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    double getDouble(int currentRow) throws SQLException {
        double result = 0.0d;
        if (this.definedColumnType == 0) {
            result = super.getDouble(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.doubleValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Date getDate(int currentRow) throws SQLException {
        Date result = null;
        if (this.definedColumnType == 0) {
            result = super.getDate(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                try {
                    result = new Date(DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCDATEFM"), nanos).getTimeInMillis());
                } catch (NumberFormatException ex) {
                    throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 132, (Object) null, ex).fillInStackTrace());
                }
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Timestamp getTimestamp(int currentRow) throws SQLException {
        Timestamp result = null;
        if (this.definedColumnType == 0) {
            result = super.getTimestamp(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                try {
                    Calendar cal = DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTMPFM"), nanos);
                    result = new Timestamp(cal.getTimeInMillis());
                    result.setNanos(nanos[0]);
                } catch (NumberFormatException ex) {
                    throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 132, (Object) null, ex).fillInStackTrace());
                }
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Time getTime(int currentRow) throws SQLException {
        Time result = null;
        if (this.definedColumnType == 0) {
            result = super.getTime(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                try {
                    Calendar cal = DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTZNFM"), nanos);
                    result = new Time(cal.getTimeInMillis());
                } catch (NumberFormatException ex) {
                    throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 132, (Object) null, ex).fillInStackTrace());
                }
            }
        }
        return result;
    }

    private final T4CMarshaller getMarshaller() {
        if (this.marshaller == null) {
            this.marshaller = this.describeType == 8 ? T4CMarshaller.LONG : T4CMarshaller.VARCHAR;
        }
        return this.marshaller;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getObject(currentRow);
        }
        if (isUnexpected()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 21).fillInStackTrace());
        }
        if (isNull(currentRow)) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.TIMESTAMPLTZ /* -102 */:
                return getTIMESTAMPLTZ(currentRow);
            case oracle.jdbc.OracleTypes.TIMESTAMPTZ /* -101 */:
                return getTIMESTAMPTZ(currentRow);
            case oracle.jdbc.OracleTypes.LONGNVARCHAR /* -16 */:
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case -1:
            case 1:
            case 12:
                return getString(currentRow);
            case oracle.jdbc.OracleTypes.ROWID /* -8 */:
                return getROWID(currentRow);
            case oracle.jdbc.OracleTypes.BIT /* -7 */:
            case 16:
                return Boolean.valueOf(getBoolean(currentRow));
            case oracle.jdbc.OracleTypes.TINYINT /* -6 */:
                return Byte.valueOf(getByte(currentRow));
            case -5:
                return Long.valueOf(getLong(currentRow));
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
            case -3:
            case -2:
                return getBytesFromHexChars(currentRow);
            case 2:
            case 3:
                return getBigDecimal(currentRow);
            case 4:
                return Integer.valueOf(getInt(currentRow));
            case 5:
                return Short.valueOf(getShort(currentRow));
            case 6:
            case 8:
                return Double.valueOf(getDouble(currentRow));
            case 7:
                return Float.valueOf(getFloat(currentRow));
            case 91:
                return getDate(currentRow);
            case 92:
                return getTime(currentRow);
            case 93:
                return getTimestamp(currentRow);
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
        }
    }

    static final NUMBER StringToNUMBER(String str) throws SQLException {
        try {
            return new NUMBER(new BigDecimal(str));
        } catch (NumberFormatException ex) {
            throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 132, (Object) null, ex).fillInStackTrace());
        }
    }

    static final Calendar DATEStringToCalendar(String value, String fmt, int[] out) throws SQLException {
        char c;
        char c2;
        char[] fmtChars = (fmt + " ").toCharArray();
        String value2 = value + " ";
        int sLength = Math.min(value2.length(), fmtChars.length);
        int state = -1;
        int index = 0;
        int prevIndex = 0;
        int veIndex = 0;
        int day = 0;
        int month = 0;
        int year = 0;
        int hour = 0;
        int minute = 0;
        int second = 0;
        int nsecond = 0;
        String am = null;
        String tzr = null;
        boolean needToProcessValue = false;
        String[] sMonths = null;
        String[] lMonths = null;
        int i = 0;
        while (i < sLength) {
            switch (fmtChars[i]) {
                case DatabaseError.EOJ_CONV_WAS_NULL /* 65 */:
                case 'a':
                    if (i + 1 < sLength && (fmtChars[i + 1] == 'M' || fmtChars[i + 1] == 'm')) {
                        state = 12;
                        index = i;
                        i++;
                        break;
                    }
                    break;
                case 'B':
                case 'C':
                case DatabaseError.EOJ_USE_XA_EXPLICIT /* 69 */:
                case 'G':
                case ShardingKeyInfo.GWS_KEY_PUSH_BIND_INDEX_20_1 /* 73 */:
                case 'J':
                case DatabaseError.EOJ_INVALID_FORWARD_RSET_OP /* 75 */:
                case 'L':
                case 'N':
                case DatabaseError.EOJ_USER_CREDENTIALS_FAIL /* 79 */:
                case 'P':
                case 'Q':
                case DatabaseError.EOJ_UPDATE_CONFLICTS /* 85 */:
                case 'V':
                case DatabaseError.WARN_IGNORE_FETCH_DIRECTION /* 87 */:
                case DatabaseError.EOJ_UNSUPPORTED_SYNTAX /* 88 */:
                case 'Z':
                case '[':
                case '\\':
                case ']':
                case '^':
                case '_':
                case '`':
                case 'b':
                case 'c':
                case 'e':
                case 'g':
                case 'i':
                case 'j':
                case 'k':
                case 'l':
                case 'n':
                case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                case 'p':
                case 'q':
                case DatabaseError.EOJ_SETSVPT_IN_GLOBAL_TXN /* 117 */:
                case 'v':
                case 'w':
                case 'x':
                default:
                    needToProcessValue = true;
                    break;
                case DatabaseError.EOJ_INVALID_ARGUMENTS /* 68 */:
                case 'd':
                    if (state != 1) {
                        state = 1;
                        index = i;
                        break;
                    }
                    break;
                case 'F':
                case 'f':
                    if (state != 11) {
                        state = 11;
                        index = i;
                        break;
                    }
                    break;
                case 'H':
                case 'h':
                    if (state != 7) {
                        state = 7;
                        index = i;
                        break;
                    } else if (i + 2 < sLength && (fmtChars[i + 1] == '2' || fmtChars[i + 4] == '4')) {
                        state = 8;
                        i += 2;
                        break;
                    }
                    break;
                case DatabaseError.EOJ_FAIL_REF_SETVALUE /* 77 */:
                case 'm':
                    if (state != 2 || state != 4 || state != 3 || state != 9) {
                        index = i;
                        if (i + 4 < sLength && ((fmtChars[i + 1] == 'O' || fmtChars[i + 1] == 'o') && ((fmtChars[i + 2] == 'N' || fmtChars[i + 2] == 'n') && ((fmtChars[i + 3] == 'T' || fmtChars[i + 3] == 't') && (fmtChars[i + 4] == 'H' || fmtChars[i + 4] == 'h'))))) {
                            state = 3;
                            i += 4;
                            break;
                        } else if (i + 2 < sLength && ((fmtChars[i + 1] == 'O' || fmtChars[i + 1] == 'o') && (fmtChars[i + 2] == 'N' || fmtChars[i + 2] == 'n'))) {
                            state = 4;
                            i += 2;
                            break;
                        } else if (i + 1 < sLength && (fmtChars[i + 1] == 'M' || fmtChars[i + 1] == 'm')) {
                            state = 2;
                            i++;
                            break;
                        } else if (i + 1 < sLength && (fmtChars[i + 1] == 'I' || fmtChars[i + 1] == 'i')) {
                            state = 9;
                            i++;
                            break;
                        }
                    }
                    break;
                case 'R':
                case 'r':
                    if (state != 6) {
                        state = 6;
                        index = i;
                        break;
                    }
                    break;
                case 'S':
                case 's':
                    if (i + 1 < sLength && (fmtChars[i + 1] == 'S' || fmtChars[i + 1] == 's')) {
                        state = 10;
                        index = i;
                        i++;
                        break;
                    }
                    break;
                case 'T':
                case DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN /* 116 */:
                    if (i + 2 < sLength && ((fmtChars[i + 1] == 'Z' || fmtChars[i + 1] == 'z') && (fmtChars[i + 2] == 'R' || fmtChars[i + 2] == 'r'))) {
                        state = 13;
                        index = i;
                        i += 2;
                        break;
                    }
                    break;
                case DatabaseError.EOJ_INTERNAL_ERROR /* 89 */:
                case 'y':
                    if (state != 5) {
                        state = 5;
                        index = i;
                        break;
                    }
                    break;
            }
            if (needToProcessValue && state != -1) {
                int length = i - index;
                int skip = index - prevIndex;
                int vsIndex = veIndex + skip;
                veIndex = vsIndex + length;
                switch (state) {
                    case 1:
                        day = Integer.parseInt(value2.substring(vsIndex, veIndex));
                        break;
                    case 2:
                        month = Integer.parseInt(value2.substring(vsIndex, veIndex));
                        break;
                    case 3:
                        int x = vsIndex;
                        while (x < value2.length() && value2.charAt(x) != fmtChars[i]) {
                            x++;
                        }
                        veIndex = x;
                        if (veIndex != vsIndex) {
                            String temp = value2.substring(vsIndex, veIndex);
                            String temp2 = temp.trim();
                            if (lMonths == null) {
                                lMonths = new DateFormatSymbols().getMonths();
                            }
                            month = 0;
                            while (month < lMonths.length && !temp2.equalsIgnoreCase(lMonths[month])) {
                                month++;
                            }
                            if (month >= 12) {
                                throw ((SQLException) DatabaseError.createSqlException(59).fillInStackTrace());
                            }
                        }
                        break;
                    case 4:
                        int x2 = vsIndex;
                        while (x2 < value2.length() && value2.charAt(x2) != fmtChars[i]) {
                            x2++;
                        }
                        veIndex = x2;
                        if (veIndex != vsIndex) {
                            String temp3 = value2.substring(vsIndex, veIndex);
                            String temp4 = temp3.trim();
                            if (sMonths == null) {
                                sMonths = new DateFormatSymbols().getShortMonths();
                            }
                            month = 0;
                            while (month < sMonths.length && !temp4.equalsIgnoreCase(sMonths[month])) {
                                month++;
                            }
                            if (month >= 12) {
                                throw DatabaseError.createSqlException(59);
                            }
                        }
                        break;
                    case 5:
                        year = Integer.parseInt(value2.substring(vsIndex, veIndex));
                        if (length == 2) {
                            year += 2000;
                            break;
                        }
                        break;
                    case 6:
                        int year2 = Integer.parseInt(value2.substring(vsIndex, veIndex));
                        if (length == 2 && year2 < 50) {
                            year = year2 + 2000;
                            break;
                        } else {
                            year = year2 + 1900;
                            break;
                        }
                        break;
                    case 7:
                    case 8:
                        veIndex = vsIndex + 2;
                        hour = Integer.parseInt(value2.substring(vsIndex, veIndex));
                        break;
                    case 9:
                        minute = Integer.parseInt(value2.substring(vsIndex, veIndex));
                        break;
                    case 10:
                        second = Integer.parseInt(value2.substring(vsIndex, veIndex));
                        break;
                    case 11:
                        int x3 = vsIndex;
                        while (x3 < value2.length() && (c2 = value2.charAt(x3)) >= '0' && c2 <= '9') {
                            x3++;
                        }
                        veIndex = vsIndex + (x3 - vsIndex);
                        if (veIndex != vsIndex) {
                            nsecond = Integer.parseInt(value2.substring(vsIndex, veIndex));
                            break;
                        }
                        break;
                    case 12:
                        if (veIndex > 0) {
                            am = value2.substring(vsIndex, veIndex);
                            break;
                        }
                        break;
                    case 13:
                        veIndex = vsIndex;
                        for (int x4 = vsIndex; x4 < value2.length() && (((c = value2.charAt(x4)) >= '0' && c <= '9') || ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z'))); x4++) {
                            veIndex = x4;
                        }
                        if (veIndex != vsIndex) {
                            tzr = value2.substring(vsIndex, veIndex);
                            break;
                        }
                        break;
                    default:
                        System.out.println("\n\n\n             ***** ERROR(1) ****\n");
                        break;
                }
                prevIndex = i;
                state = -1;
                needToProcessValue = false;
            }
            i++;
        }
        Calendar cal = new GregorianCalendar(year, month, day, hour, minute, second);
        if (am != null) {
            cal.set(9, am.equalsIgnoreCase("AM") ? 0 : 1);
        }
        if (tzr != null) {
        }
        if (nsecond != 0) {
            out[0] = nsecond;
        }
        return cal;
    }

    @Override // oracle.jdbc.driver.Accessor
    int getBytes(int currentRow, byte[] buffer, int offset) throws SQLException {
        if (isNull(currentRow)) {
            return 0;
        }
        return this.rowData.getUtf8Bytes(getOffset(currentRow), getLength(currentRow), buffer, offset, this.statement.connection.conversion.getCharacterSet(this.formOfUse));
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new AccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CVarcharAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CVarcharAccessor(stmt, T4CVarcharAccessor.this.describeMaxLength, T4CVarcharAccessor.this.nullable, -1, T4CVarcharAccessor.this.precision, T4CVarcharAccessor.this.scale, T4CVarcharAccessor.this.contflag, -1, T4CVarcharAccessor.this.formOfUse, T4CVarcharAccessor.this.describeMaxLengthChars, T4CVarcharAccessor.this.oacmxl, T4CVarcharAccessor.this.definedColumnType, T4CVarcharAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
