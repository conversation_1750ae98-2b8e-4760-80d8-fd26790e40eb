package oracle.jdbc.driver;

import oracle.jdbc.internal.Monitor;
import oracle.sql.NUMBER;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VarnumBinder.class */
abstract class VarnumBinder extends Binder {
    static final boolean DEBUG = false;
    static final boolean SLOW_CONVERSIONS = true;
    Binder theVarnumCopyingBinder = null;
    static final int LNXSGNBT = 128;
    static final byte LNXDIGS = 20;
    static final byte LNXEXPBS = 64;
    static final int LNXEXPMX = 127;
    static final int MANTISSA_SIZE = 53;
    static final int expShift = 52;
    static final long fractHOB = 4503599627370496L;
    static final long fractMask = 4503599627370495L;
    static final int expBias = 1023;
    static final int maxSmallBinExp = 62;
    static final int minSmallBinExp = -21;
    static final long expOne = 4607182418800017408L;
    static final long highbyte = -72057594037927936L;
    static final long highbit = Long.MIN_VALUE;
    static final long lowbytes = 72057594037927935L;
    static volatile FDBigInt[] b5p;
    static final double[] factorTable = {1.0E254d, 1.0E252d, 1.0E250d, 1.0E248d, 1.0E246d, 1.0E244d, 1.0E242d, 1.0E240d, 1.0E238d, 1.0E236d, 1.0E234d, 1.0E232d, 1.0E230d, 1.0E228d, 1.0E226d, 1.0E224d, 1.0E222d, 1.0E220d, 1.0E218d, 1.0E216d, 1.0E214d, 1.0E212d, 1.0E210d, 1.0E208d, 1.0E206d, 1.0E204d, 1.0E202d, 1.0E200d, 1.0E198d, 1.0E196d, 1.0E194d, 1.0E192d, 1.0E190d, 1.0E188d, 1.0E186d, 1.0E184d, 1.0E182d, 1.0E180d, 1.0E178d, 1.0E176d, 1.0E174d, 1.0E172d, 1.0E170d, 1.0E168d, 1.0E166d, 1.0E164d, 1.0E162d, 1.0E160d, 1.0E158d, 1.0E156d, 1.0E154d, 1.0E152d, 1.0E150d, 1.0E148d, 1.0E146d, 1.0E144d, 1.0E142d, 1.0E140d, 1.0E138d, 1.0E136d, 1.0E134d, 1.0E132d, 1.0E130d, 1.0E128d, 1.0E126d, 1.0E124d, 1.0E122d, 1.0E120d, 1.0E118d, 1.0E116d, 1.0E114d, 1.0E112d, 1.0E110d, 1.0E108d, 1.0E106d, 1.0E104d, 1.0E102d, 1.0E100d, 1.0E98d, 1.0E96d, 1.0E94d, 1.0E92d, 1.0E90d, 1.0E88d, 1.0E86d, 1.0E84d, 1.0E82d, 1.0E80d, 1.0E78d, 1.0E76d, 1.0E74d, 1.0E72d, 1.0E70d, 1.0E68d, 1.0E66d, 1.0E64d, 1.0E62d, 1.0E60d, 1.0E58d, 1.0E56d, 1.0E54d, 1.0E52d, 1.0E50d, 1.0E48d, 1.0E46d, 1.0E44d, 1.0E42d, 1.0E40d, 1.0E38d, 1.0E36d, 1.0E34d, 1.0E32d, 1.0E30d, 1.0E28d, 1.0E26d, 1.0E24d, 1.0E22d, 1.0E20d, 1.0E18d, 1.0E16d, 1.0E14d, 1.0E12d, 1.0E10d, 1.0E8d, 1000000.0d, 10000.0d, 100.0d, 1.0d, 0.01d, 1.0E-4d, 1.0E-6d, 1.0E-8d, 1.0E-10d, 1.0E-12d, 1.0E-14d, 1.0E-16d, 1.0E-18d, 1.0E-20d, 1.0E-22d, 1.0E-24d, 1.0E-26d, 1.0E-28d, 1.0E-30d, 1.0E-32d, 1.0E-34d, 1.0E-36d, 1.0E-38d, 1.0E-40d, 1.0E-42d, 1.0E-44d, 1.0E-46d, 1.0E-48d, 1.0E-50d, 1.0E-52d, 1.0E-54d, 1.0E-56d, 1.0E-58d, 1.0E-60d, 1.0E-62d, 1.0E-64d, 1.0E-66d, 1.0E-68d, 1.0E-70d, 1.0E-72d, 1.0E-74d, 1.0E-76d, 1.0E-78d, 1.0E-80d, 1.0E-82d, 1.0E-84d, 1.0E-86d, 1.0E-88d, 1.0E-90d, 1.0E-92d, 1.0E-94d, 1.0E-96d, 1.0E-98d, 1.0E-100d, 1.0E-102d, 1.0E-104d, 1.0E-106d, 1.0E-108d, 1.0E-110d, 1.0E-112d, 1.0E-114d, 1.0E-116d, 1.0E-118d, 1.0E-120d, 1.0E-122d, 1.0E-124d, 1.0E-126d, 1.0E-128d, 1.0E-130d, 1.0E-132d, 1.0E-134d, 1.0E-136d, 1.0E-138d, 1.0E-140d, 1.0E-142d, 1.0E-144d, 1.0E-146d, 1.0E-148d, 1.0E-150d, 1.0E-152d, 1.0E-154d, 1.0E-156d, 1.0E-158d, 1.0E-160d, 1.0E-162d, 1.0E-164d, 1.0E-166d, 1.0E-168d, 1.0E-170d, 1.0E-172d, 1.0E-174d, 1.0E-176d, 1.0E-178d, 1.0E-180d, 1.0E-182d, 1.0E-184d, 1.0E-186d, 1.0E-188d, 1.0E-190d, 1.0E-192d, 1.0E-194d, 1.0E-196d, 1.0E-198d, 1.0E-200d, 1.0E-202d, 1.0E-204d, 1.0E-206d, 1.0E-208d, 1.0E-210d, 1.0E-212d, 1.0E-214d, 1.0E-216d, 1.0E-218d, 1.0E-220d, 1.0E-222d, 1.0E-224d, 1.0E-226d, 1.0E-228d, 1.0E-230d, 1.0E-232d, 1.0E-234d, 1.0E-236d, 1.0E-238d, 1.0E-240d, 1.0E-242d, 1.0E-244d, 1.0E-246d, 1.0E-248d, 1.0E-250d, 1.0E-252d, 1.0E-254d};
    static final int[] small5pow = {1, 5, 25, 125, 625, 3125, 15625, 78125, 390625, 1953125, 9765625, 48828125, 244140625, 1220703125};
    static final long[] long5pow = {1, 5, 25, 125, 625, 3125, 15625, 78125, 390625, 1953125, 9765625, 48828125, 244140625, 1220703125, 6103515625L, 30517578125L, 152587890625L, 762939453125L, 3814697265625L, 19073486328125L, 95367431640625L, 476837158203125L, 2384185791015625L, 11920928955078125L, 59604644775390625L, 298023223876953125L, 1490116119384765625L};
    static final int[] n5bits = {0, 3, 5, 7, 10, 12, 14, 17, 19, 21, 24, 26, 28, 31, 33, 35, 38, 40, 42, 45, 47, 49, 52, 54, 56, 59, 61};
    private static final Monitor M5POW = Monitor.newInstance();

    static void init(Binder x) {
        x.type = (short) 6;
        x.bytelen = 22;
    }

    VarnumBinder() {
        init(this);
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theVarnumCopyingBinder == null) {
            this.theVarnumCopyingBinder = new VarnumCopyingBinder();
        }
        return this.theVarnumCopyingBinder;
    }

    static int setLongInternal(byte[] b, int offset, long val) {
        return NUMBER.toBytes(val, b, offset);
    }

    static int countBits(long v) {
        if (v == 0) {
            return 0;
        }
        while ((v & highbyte) == 0) {
            v <<= 8;
        }
        while (v > 0) {
            v <<= 1;
        }
        int n = 0;
        while ((v & lowbytes) != 0) {
            v <<= 8;
            n += 8;
        }
        while (v != 0) {
            v <<= 1;
            n++;
        }
        return n;
    }

    boolean roundup(char[] digits, int nDigits) {
        int i = nDigits - 1;
        int q = digits[i];
        if (q == 57) {
            while (q == 57 && i > 0) {
                digits[i] = '0';
                i--;
                q = digits[i];
            }
            if (q == 57) {
                digits[0] = '1';
                return true;
            }
        }
        digits[i] = (char) (q + 1);
        return false;
    }

    static FDBigInt big5pow(int p) {
        if (p < 0) {
            throw new RuntimeException("Assertion botch: negative power of 5");
        }
        if (b5p != null && b5p.length > p && b5p[p] != null) {
            return b5p[p];
        }
        Monitor.CloseableLock lock = M5POW.acquireCloseableLock();
        Throwable th = null;
        try {
            if (b5p != null && b5p.length > p && b5p[p] != null) {
                FDBigInt fDBigInt = b5p[p];
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return fDBigInt;
            }
            if (b5p == null) {
                b5p = new FDBigInt[p + 1];
            } else if (b5p.length <= p) {
                FDBigInt[] t = new FDBigInt[p + 1];
                System.arraycopy(b5p, 0, t, 0, b5p.length);
                b5p = t;
            }
            if (b5p[p] != null) {
                FDBigInt fDBigInt2 = b5p[p];
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return fDBigInt2;
            }
            if (p < small5pow.length) {
                FDBigInt[] fDBigIntArr = b5p;
                FDBigInt fDBigInt3 = new FDBigInt(small5pow[p]);
                fDBigIntArr[p] = fDBigInt3;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                return fDBigInt3;
            }
            if (p < long5pow.length) {
                FDBigInt[] fDBigIntArr2 = b5p;
                FDBigInt fDBigInt4 = new FDBigInt(long5pow[p]);
                fDBigIntArr2[p] = fDBigInt4;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th5) {
                            th.addSuppressed(th5);
                        }
                    } else {
                        lock.close();
                    }
                }
                return fDBigInt4;
            }
            int q = p >> 1;
            int r = p - q;
            FDBigInt bigq = b5p[q];
            if (bigq == null) {
                bigq = big5pow(q);
            }
            if (r < small5pow.length) {
                FDBigInt[] fDBigIntArr3 = b5p;
                FDBigInt fDBigIntMult = bigq.mult(small5pow[r]);
                fDBigIntArr3[p] = fDBigIntMult;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th6) {
                            th.addSuppressed(th6);
                        }
                    } else {
                        lock.close();
                    }
                }
                return fDBigIntMult;
            }
            FDBigInt bigr = b5p[r];
            if (bigr == null) {
                bigr = big5pow(r);
            }
            FDBigInt[] fDBigIntArr4 = b5p;
            FDBigInt fDBigIntMult2 = bigq.mult(bigr);
            fDBigIntArr4[p] = fDBigIntMult2;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th7) {
                        th.addSuppressed(th7);
                    }
                } else {
                    lock.close();
                }
            }
            return fDBigIntMult2;
        } catch (Throwable th8) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th9) {
                        th.addSuppressed(th9);
                    }
                } else {
                    lock.close();
                }
            }
            throw th8;
        }
    }

    static FDBigInt multPow52(FDBigInt v, int p5, int p2) throws IllegalArgumentException {
        if (p5 != 0) {
            if (p5 < small5pow.length) {
                v = v.mult(small5pow[p5]);
            } else {
                v = v.mult(big5pow(p5));
            }
        }
        if (p2 != 0) {
            v.lshiftMe(p2);
        }
        return v;
    }

    static FDBigInt constructPow52(int p5, int p2) throws IllegalArgumentException {
        FDBigInt v = new FDBigInt(big5pow(p5));
        if (p2 != 0) {
            v.lshiftMe(p2);
        }
        return v;
    }

    int dtoa(byte[] b, int offset, double val, boolean neg, boolean forFloat, char[] digits, int binExp, long fractBits, int nSignificantBits) throws IllegalArgumentException {
        int exp100;
        int ndigit;
        boolean low;
        boolean high;
        long lowDigitDifference;
        boolean z;
        boolean z2;
        long halfULP;
        int ndigits;
        int digitno;
        int c;
        int c2;
        int decExponent = Integer.MAX_VALUE;
        int nDigits = -1;
        int nFractBits = countBits(fractBits);
        int nTinyBits = (nFractBits - binExp) - 1;
        boolean done = false;
        if (nTinyBits < 0) {
            nTinyBits = 0;
        }
        if (binExp <= 62 && binExp >= minSmallBinExp && nTinyBits < long5pow.length && nFractBits + n5bits[nTinyBits] < 64 && nTinyBits == 0) {
            if (binExp > nSignificantBits) {
                halfULP = 1 << ((binExp - nSignificantBits) - 1);
            } else {
                halfULP = 0;
            }
            if (binExp >= 52) {
                fractBits <<= binExp - 52;
            } else {
                fractBits >>>= 52 - binExp;
            }
            int decExponent2 = 0;
            long lvalue = fractBits;
            long insignificant = halfULP;
            int i = 0;
            while (insignificant >= 10) {
                insignificant /= 10;
                i++;
            }
            if (i != 0) {
                long pow10 = long5pow[i] << i;
                long residue = lvalue % pow10;
                lvalue /= pow10;
                decExponent2 = 0 + i;
                if (residue >= (pow10 >> 1)) {
                    lvalue++;
                }
            }
            if (lvalue <= 2147483647L) {
                int ivalue = (int) lvalue;
                ndigits = 10;
                digitno = 10 - 1;
                while (true) {
                    c2 = ivalue % 10;
                    ivalue /= 10;
                    if (c2 != 0) {
                        break;
                    }
                    decExponent2++;
                }
                while (ivalue != 0) {
                    int i2 = digitno;
                    digitno--;
                    digits[i2] = (char) (c2 + 48);
                    decExponent2++;
                    c2 = ivalue % 10;
                    ivalue /= 10;
                }
                digits[digitno] = (char) (c2 + 48);
            } else {
                ndigits = 20;
                digitno = 20 - 1;
                while (true) {
                    c = (int) (lvalue % 10);
                    lvalue /= 10;
                    if (c != 0) {
                        break;
                    }
                    decExponent2++;
                }
                while (lvalue != 0) {
                    int i3 = digitno;
                    digitno--;
                    digits[i3] = (char) (c + 48);
                    decExponent2++;
                    c = (int) (lvalue % 10);
                    lvalue /= 10;
                }
                digits[digitno] = (char) (c + 48);
            }
            int ndigits2 = ndigits - digitno;
            if (digitno != 0) {
                System.arraycopy(digits, digitno, digits, 0, ndigits2);
            }
            decExponent = decExponent2 + 1;
            nDigits = ndigits2;
            done = true;
        }
        if (!done) {
            double d2 = Double.longBitsToDouble(expOne | (fractBits & (-4503599627370497L)));
            int decExp = (int) Math.floor(((d2 - 1.5d) * 0.289529654d) + 0.176091259d + (binExp * 0.301029995663981d));
            int B5 = Math.max(0, -decExp);
            int B2 = B5 + nTinyBits + binExp;
            int S5 = Math.max(0, decExp);
            int S2 = S5 + nTinyBits;
            int M2 = B2 - nSignificantBits;
            long fractBits2 = fractBits >>> (53 - nFractBits);
            int B22 = B2 - (nFractBits - 1);
            int common2factor = Math.min(B22, S2);
            int B23 = B22 - common2factor;
            int S22 = S2 - common2factor;
            int M22 = M2 - common2factor;
            if (nFractBits == 1) {
                M22--;
            }
            if (M22 < 0) {
                B23 -= M22;
                S22 -= M22;
                M22 = 0;
            }
            int Bbits = nFractBits + B23 + (B5 < n5bits.length ? n5bits[B5] : B5 * 3);
            int tenSbits = S22 + 1 + (S5 + 1 < n5bits.length ? n5bits[S5 + 1] : (S5 + 1) * 3);
            if (Bbits < 64 && tenSbits < 64) {
                if (Bbits < 32 && tenSbits < 32) {
                    int bi = (((int) fractBits2) * small5pow[B5]) << B23;
                    int s = small5pow[S5] << S22;
                    int m = small5pow[B5] << M22;
                    int tens = s * 10;
                    ndigit = 0;
                    int q = bi / s;
                    int bi2 = 10 * (bi % s);
                    int m2 = m * 10;
                    low = bi2 < m2;
                    high = bi2 + m2 > tens;
                    if (q != 0 || high) {
                        ndigit = 0 + 1;
                        digits[0] = (char) (48 + q);
                    } else {
                        decExp--;
                    }
                    if (decExp <= -3 || decExp >= 8) {
                        low = false;
                        high = false;
                    }
                    while (!low && !high) {
                        int q2 = bi2 / s;
                        bi2 = 10 * (bi2 % s);
                        m2 *= 10;
                        if (m2 > 0) {
                            low = bi2 < m2;
                            z2 = bi2 + m2 > tens;
                        } else {
                            low = true;
                            z2 = true;
                        }
                        high = z2;
                        int i4 = ndigit;
                        ndigit++;
                        digits[i4] = (char) (48 + q2);
                    }
                    lowDigitDifference = (bi2 << 1) - tens;
                } else {
                    long bl = (fractBits2 * long5pow[B5]) << B23;
                    long s2 = long5pow[S5] << S22;
                    long m3 = long5pow[B5] << M22;
                    long tens2 = s2 * 10;
                    ndigit = 0;
                    int q3 = (int) (bl / s2);
                    long bl2 = 10 * (bl % s2);
                    long m4 = m3 * 10;
                    low = bl2 < m4;
                    high = bl2 + m4 > tens2;
                    if (q3 != 0 || high) {
                        ndigit = 0 + 1;
                        digits[0] = (char) (48 + q3);
                    } else {
                        decExp--;
                    }
                    if (decExp <= -3 || decExp >= 8) {
                        low = false;
                        high = false;
                    }
                    while (!low && !high) {
                        int q4 = (int) (bl2 / s2);
                        bl2 = 10 * (bl2 % s2);
                        m4 *= 10;
                        if (m4 > 0) {
                            low = bl2 < m4;
                            z = bl2 + m4 > tens2;
                        } else {
                            low = true;
                            z = true;
                        }
                        high = z;
                        int i5 = ndigit;
                        ndigit++;
                        digits[i5] = (char) (48 + q4);
                    }
                    lowDigitDifference = (bl2 << 1) - tens2;
                }
            } else {
                FDBigInt Bval = multPow52(new FDBigInt(fractBits2), B5, B23);
                FDBigInt Sval = constructPow52(S5, S22);
                FDBigInt Mval = constructPow52(B5, M22);
                int shiftBias = Sval.normalizeMe();
                Bval.lshiftMe(shiftBias);
                Mval.lshiftMe(shiftBias);
                FDBigInt tenSval = Sval.mult(10);
                ndigit = 0;
                int q5 = Bval.quoRemIteration(Sval);
                FDBigInt Mval2 = Mval.mult(10);
                low = Bval.cmp(Mval2) < 0;
                high = Bval.add(Mval2).cmp(tenSval) > 0;
                if (q5 != 0 || high) {
                    ndigit = 0 + 1;
                    digits[0] = (char) (48 + q5);
                } else {
                    decExp--;
                }
                if (decExp <= -3 || decExp >= 8) {
                    low = false;
                    high = false;
                }
                while (!low && !high) {
                    int q6 = Bval.quoRemIteration(Sval);
                    Mval2 = Mval2.mult(10);
                    low = Bval.cmp(Mval2) < 0;
                    high = Bval.add(Mval2).cmp(tenSval) > 0;
                    int i6 = ndigit;
                    ndigit++;
                    digits[i6] = (char) (48 + q6);
                }
                if (high && low) {
                    Bval.lshiftMe(1);
                    lowDigitDifference = Bval.cmp(tenSval);
                } else {
                    lowDigitDifference = 0;
                }
            }
            decExponent = decExp + 1;
            nDigits = ndigit;
            if (high) {
                if (low) {
                    if (lowDigitDifference == 0) {
                        if ((digits[nDigits - 1] & 1) != 0 && roundup(digits, nDigits)) {
                            decExponent++;
                        }
                    } else if (lowDigitDifference > 0 && roundup(digits, nDigits)) {
                        decExponent++;
                    }
                } else if (roundup(digits, nDigits)) {
                    decExponent++;
                }
            }
        }
        while (nDigits - decExponent > 0 && digits[nDigits - 1] == '0') {
            nDigits--;
        }
        boolean oddExp = decExponent % 2 != 0;
        if (oddExp) {
            if (nDigits % 2 == 0) {
                int i7 = nDigits;
                nDigits++;
                digits[i7] = '0';
            }
            exp100 = (decExponent - 1) / 2;
        } else {
            if (nDigits % 2 == 1) {
                int i8 = nDigits;
                nDigits++;
                digits[i8] = '0';
            }
            exp100 = (decExponent - 2) / 2;
        }
        int i9 = DatabaseError.EOJ_SETSVPT_IN_GLOBAL_TXN - exp100;
        int digidx = 0;
        int len = 1;
        if (neg) {
            b[offset] = (byte) (62 - exp100);
            if (oddExp) {
                digidx = 0 + 1;
                b[offset + 1] = (byte) (101 - (digits[0] - '0'));
                len = 1 + 1;
            }
            while (digidx < nDigits) {
                b[offset + len] = (byte) (101 - (((digits[digidx] - '0') * 10) + (digits[digidx + 1] - '0')));
                digidx += 2;
                len++;
            }
            int i10 = len;
            len++;
            b[offset + i10] = 102;
        } else {
            b[offset] = (byte) (192 + exp100 + 1);
            if (oddExp) {
                digidx = 0 + 1;
                b[offset + 1] = (byte) ((digits[0] - '0') + 1);
                len = 1 + 1;
            }
            while (digidx < nDigits) {
                b[offset + len] = (byte) (((digits[digidx] - '0') * 10) + (digits[digidx + 1] - '0') + 1);
                digidx += 2;
                len++;
            }
        }
        if (neg) {
            int trimmedLen = len;
            for (int i11 = offset + (len - 2); i11 > offset && b[i11] == 101; i11--) {
                trimmedLen--;
            }
            if (trimmedLen != len) {
                b[offset + (trimmedLen - 1)] = 102;
                len = trimmedLen;
            }
        } else {
            int trimmedLen2 = len;
            for (int i12 = offset + (len - 1); i12 > offset && b[i12] == 1; i12--) {
                trimmedLen2--;
            }
            if (trimmedLen2 != len) {
                len = trimmedLen2;
            }
        }
        return len;
    }
}
