package oracle.jdbc.diagnostics;

import java.util.function.Supplier;
import java.util.logging.Level;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.clio.annotations.Sensitive;
import oracle.jdbc.diagnostics.Metrics;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Diagnosable.class */
public interface Diagnosable extends PropertyChangeListener {
    default Object secure(@Sensitive Object value) {
        return getDiagnosable().secure(value);
    }

    @Sensitive(Sensitive.Dependency.DEPENDENT)
    default Object format(Format.Style f, Object value, long... args) {
        return getDiagnosable().format(f, value, args);
    }

    default <T extends Throwable> T trace(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object... objArr) {
        return (T) getDiagnosable().trace(level, securityLabel, str, str2, str3, str4, t, objArr);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object... objArr) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, (String) t, objArr);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, t);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object obj) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, (String) t, obj);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object obj, Object obj2) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, t, obj, obj2);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object obj, Object obj2, Object obj3) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, t, obj, obj2, obj3);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object obj, Object obj2, Object obj3, Object obj4) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, t, obj, obj2, obj3, obj4);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object obj, Object obj2, Object obj3, Object obj4, Object obj5) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, t, obj, obj2, obj3, obj4, obj5);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object obj, Object obj2, Object obj3, Object obj4, Object obj5, Object obj6) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, t, obj, obj2, obj3, obj4, obj5, obj6);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object obj, Object obj2, Object obj3, Object obj4, Object obj5, Object obj6, Object obj7) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, t, obj, obj2, obj3, obj4, obj5, obj6, obj7);
    }

    default <T extends Throwable> T debug(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Object obj, Object obj2, Object obj3, Object obj4, Object obj5, Object obj6, Object obj7, Object obj8) {
        return (T) getDiagnosable().debug(level, securityLabel, str, str2, str3, str4, t, obj, obj2, obj3, obj4, obj5, obj6, obj7, obj8);
    }

    default <T extends Throwable> T tracep(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Supplier<Object[]> supplier) {
        return (T) getDiagnosable().tracep(level, securityLabel, str, str2, str3, str4, t, supplier);
    }

    default <T extends Throwable> T debugp(Level level, SecurityLabel securityLabel, String str, String str2, String str3, String str4, T t, Supplier<Object[]> supplier) {
        return (T) getDiagnosable().debugp(level, securityLabel, str, str2, str3, str4, t, supplier);
    }

    default void suspendLogging() {
        getDiagnosable().suspendLogging();
    }

    default void resumeLogging() {
        getDiagnosable().resumeLogging();
    }

    default void beginCurrentSql(String sql) {
        getDiagnosable().beginCurrentSql(sql);
    }

    default void endCurrentSql() {
        getDiagnosable().endCurrentSql();
    }

    default Diagnosable getDiagnosable() {
        throw new NoSuchMethodError();
    }

    default boolean isLoggable(Level level) {
        return getDiagnosable().isLoggable(level);
    }

    default boolean isDebugEnabled() {
        return getDiagnosable().isDebugEnabled();
    }

    default boolean isSensitiveEnabled() {
        return getDiagnosable().isSensitiveEnabled();
    }

    default boolean isDiagnoseFirstFailureEnabled() {
        return getDiagnosable().isDiagnoseFirstFailureEnabled();
    }

    @Override // oracle.jdbc.diagnostics.PropertyChangeListener
    default void propertyChange(PropertyChangeEvent event) {
        getDiagnosable().propertyChange(event);
    }

    default void begin(Metrics.ConnectionEvent event) {
        getDiagnosable().begin(event);
    }

    default void end(Metrics.ConnectionEvent event) {
        getDiagnosable().end(event);
    }

    default void onClose() {
        getDiagnosable().onClose();
    }
}
