package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.aq.AQEnqueueOptions;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4Caqe.class */
final class T4Caqe extends T4CTTIfun {
    static final int KPD_AQ_BUFMSG = 2;
    static final int KPD_AQ_EITHER = 16;
    static final int KPD_AQ_DQBYSHRD = 64;
    static final int OCI_COMMIT_ON_SUCCESS = 32;
    static final int ATTR_TRANSFORMATION = 196;
    static final int AQEVER_DEFAULT = 1;
    T4CTTIaqm aqm;
    T4Ctoh toh;
    private byte[] queueNameBytes;
    private AQEnqueueOptions enqueueOptions;
    private AQMessagePropertiesI messageProperties;
    private byte[] messageData;
    private byte[] messageOid;
    private int aqever;
    private boolean isRawQueue;
    private int nbExtensions;
    private byte[][] extensionTextValues;
    private byte[][] extensionBinaryValues;
    private int[] extensionKeywords;
    private AQAgentI[] attrRecipientList;
    private byte[][] recipientTextValues;
    private byte[][] recipientBinaryValues;
    private int[] recipientKeywords;
    private byte[] aqmcorBytes;
    private byte[] aqmeqnBytes;
    private boolean retrieveMessageId;
    private byte[] outMsgid;
    private byte[] senderAgentName;
    private byte[] senderAgentAddress;
    private byte senderAgentProtocol;
    private boolean isJsonQueue;

    T4Caqe(T4CConnection _connection) {
        super(_connection, (byte) 3);
        this.queueNameBytes = null;
        this.enqueueOptions = null;
        this.messageProperties = null;
        this.messageData = null;
        this.messageOid = null;
        this.aqever = 1;
        this.isRawQueue = false;
        this.nbExtensions = 0;
        this.extensionTextValues = (byte[][]) null;
        this.extensionBinaryValues = (byte[][]) null;
        this.extensionKeywords = null;
        this.attrRecipientList = null;
        this.recipientTextValues = (byte[][]) null;
        this.recipientBinaryValues = (byte[][]) null;
        this.recipientKeywords = null;
        this.retrieveMessageId = false;
        this.outMsgid = null;
        this.senderAgentName = null;
        this.senderAgentAddress = null;
        this.senderAgentProtocol = (byte) 0;
        setFunCode((short) 121);
        this.toh = new T4Ctoh(_connection);
        this.aqm = new T4CTTIaqm(this.connection, this.toh);
    }

    /* JADX WARN: Type inference failed for: r1v25, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v28, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v39, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v44, types: [byte[], byte[][]] */
    void doOAQEQ(String _queueName, AQEnqueueOptions _enqueueOptions, AQMessagePropertiesI _messageProperties, byte[] _messageData, byte[] _messageOid, int _messageVersion, boolean _isRawQueue) throws SQLException, IOException {
        this.enqueueOptions = _enqueueOptions;
        this.messageProperties = _messageProperties;
        String aqmcor = this.messageProperties.getCorrelation();
        if (aqmcor != null && aqmcor.length() != 0) {
            this.aqmcorBytes = this.meg.conv.StringToCharBytes(aqmcor);
        } else {
            this.aqmcorBytes = null;
        }
        String aqmeqn = this.messageProperties.getExceptionQueue();
        if (aqmeqn != null && aqmeqn.length() != 0) {
            this.aqmeqnBytes = this.meg.conv.StringToCharBytes(aqmeqn);
        } else {
            this.aqmeqnBytes = null;
        }
        AQAgentI senderAgent = (AQAgentI) this.messageProperties.getSender();
        if (senderAgent != null) {
            if (senderAgent.getName() != null) {
                this.senderAgentName = this.meg.conv.StringToCharBytes(senderAgent.getName());
            } else {
                this.senderAgentName = null;
            }
            if (senderAgent.getAddress() != null) {
                this.senderAgentAddress = this.meg.conv.StringToCharBytes(senderAgent.getAddress());
            } else {
                this.senderAgentAddress = null;
            }
            this.senderAgentProtocol = (byte) senderAgent.getProtocol();
        } else {
            this.senderAgentName = null;
            this.senderAgentAddress = null;
            this.senderAgentProtocol = (byte) 0;
        }
        this.messageData = _messageData;
        this.messageOid = _messageOid;
        this.aqever = _messageVersion;
        this.isRawQueue = _isRawQueue;
        this.isJsonQueue = AQMessageI.compareToid(this.messageOid, TypeDescriptor.JSONTOID);
        if (_queueName != null && _queueName.length() != 0) {
            this.queueNameBytes = this.meg.conv.StringToCharBytes(_queueName);
        } else {
            this.queueNameBytes = null;
        }
        this.attrRecipientList = (AQAgentI[]) this.messageProperties.getRecipientList();
        if (this.attrRecipientList != null && this.attrRecipientList.length > 0) {
            this.recipientTextValues = new byte[this.attrRecipientList.length * 3];
            this.recipientBinaryValues = new byte[this.attrRecipientList.length * 3];
            this.recipientKeywords = new int[this.attrRecipientList.length * 3];
            for (int i = 0; i < this.attrRecipientList.length; i++) {
                if (this.attrRecipientList[i].getName() != null) {
                    this.recipientTextValues[3 * i] = this.meg.conv.StringToCharBytes(this.attrRecipientList[i].getName());
                }
                if (this.attrRecipientList[i].getAddress() != null) {
                    this.recipientTextValues[(3 * i) + 1] = this.meg.conv.StringToCharBytes(this.attrRecipientList[i].getAddress());
                }
                this.recipientBinaryValues[(3 * i) + 2] = new byte[1];
                this.recipientBinaryValues[(3 * i) + 2][0] = (byte) this.attrRecipientList[i].getProtocol();
                this.recipientKeywords[3 * i] = 3 * i;
                this.recipientKeywords[(3 * i) + 1] = (3 * i) + 1;
                this.recipientKeywords[(3 * i) + 2] = (3 * i) + 2;
            }
        }
        String transformation = this.enqueueOptions.getTransformation();
        if (transformation != null && transformation.length() > 0) {
            this.nbExtensions = 1;
            this.extensionTextValues = new byte[this.nbExtensions];
            this.extensionBinaryValues = new byte[this.nbExtensions];
            this.extensionKeywords = new int[this.nbExtensions];
            this.extensionTextValues[0] = this.meg.conv.StringToCharBytes(transformation);
            this.extensionBinaryValues[0] = null;
            this.extensionKeywords[0] = 196;
        } else {
            this.nbExtensions = 0;
        }
        this.outMsgid = null;
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.queueNameBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.aqm.initToDefaultValues();
        this.aqm.aqmpri = this.messageProperties.getPriority();
        this.aqm.aqmdel = this.messageProperties.getDelay();
        this.aqm.aqmexp = this.messageProperties.getExpiration();
        this.aqm.aqmcorBytes = this.aqmcorBytes;
        this.aqm.aqmeqnBytes = this.aqmeqnBytes;
        this.aqm.senderAgentName = this.senderAgentName;
        this.aqm.senderAgentAddress = this.senderAgentAddress;
        this.aqm.senderAgentProtocol = this.senderAgentProtocol;
        this.aqm.originalMsgId = this.messageProperties.getPreviousQueueMessageId();
        this.aqm.aqmshardNum = this.messageProperties.getShardNum();
        this.aqm.marshal();
        AQAgentI[] attrRecipientList = (AQAgentI[]) this.messageProperties.getRecipientList();
        if (attrRecipientList != null && attrRecipientList.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(attrRecipientList.length * 3);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalSB4(this.enqueueOptions.getVisibility().getCode());
        boolean sendRelativeMessageId = false;
        if (this.enqueueOptions.getRelativeMessageId() != null && this.enqueueOptions.getRelativeMessageId().length > 0) {
            sendRelativeMessageId = true;
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.enqueueOptions.getRelativeMessageId().length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalSWORD(this.enqueueOptions.getSequenceDeviation().getCode());
        this.meg.marshalPTR();
        this.meg.marshalSWORD(16);
        this.meg.marshalUB2(this.aqever);
        if (!this.isRawQueue) {
            if (this.isJsonQueue) {
                this.meg.marshalNULLPTR();
                this.meg.marshalNULLPTR();
                this.meg.marshalUB4(0L);
            } else {
                this.meg.marshalPTR();
                this.meg.marshalNULLPTR();
                this.meg.marshalUB4(0L);
            }
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.messageData.length);
        }
        if (this.enqueueOptions.getRetrieveMessageId()) {
            this.retrieveMessageId = true;
            this.meg.marshalPTR();
            this.meg.marshalSWORD(16);
        } else {
            this.retrieveMessageId = false;
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        int aqeflg = 0;
        if (this.connection.autocommit) {
            aqeflg = 32;
        }
        if (this.enqueueOptions.getDeliveryMode() == AQEnqueueOptions.DeliveryMode.BUFFERED) {
            aqeflg |= 2;
        }
        this.meg.marshalUB4(aqeflg);
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
        if (this.nbExtensions > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.nbExtensions);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalNULLPTR();
        this.meg.marshalSWORD(0);
        this.meg.marshalNULLPTR();
        this.meg.marshalSWORD(0);
        this.meg.marshalNULLPTR();
        if (this.connection.getTTCVersion() >= 4) {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
            this.meg.marshalNULLPTR();
            this.meg.marshalNULLPTR();
            if (this.connection.getTTCVersion() >= 14) {
                if (this.isJsonQueue) {
                    this.meg.marshalPTR();
                } else {
                    this.meg.marshalNULLPTR();
                }
            }
        }
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalCHR(this.queueNameBytes);
        }
        if (attrRecipientList != null && attrRecipientList.length > 0) {
            this.meg.marshalKPDKV(this.recipientTextValues, this.recipientBinaryValues, this.recipientKeywords);
        }
        if (sendRelativeMessageId) {
            this.meg.marshalB1Array(this.enqueueOptions.getRelativeMessageId());
        }
        this.meg.marshalB1Array(this.messageOid);
        if (!this.isRawQueue) {
            if (!this.isJsonQueue) {
                this.toh.init(this.messageOid, this.messageData.length);
                this.toh.marshal(this.meg);
                this.meg.marshalCLR(this.messageData, 0, this.messageData.length);
            }
        } else {
            this.meg.marshalB1Array(this.messageData);
        }
        if (this.nbExtensions > 0) {
            this.meg.marshalKPDKV(this.extensionTextValues, this.extensionBinaryValues, this.extensionKeywords);
        }
        if (this.isJsonQueue) {
            byte[] quasiLocator = this.connection.setupJsonQuasiLocator(this.messageData.length);
            this.meg.marshalUB4(quasiLocator.length);
            this.meg.marshalB1Array(quasiLocator);
            if (this.connection.isZeroCopyIOEnabled(quasiLocator)) {
                this.meg.writeZeroCopyIO(this.messageData, 0, this.messageData.length);
            } else {
                this.meg.marshalCLR(this.messageData, 0, this.messageData.length);
            }
        }
    }

    byte[] getMessageId() {
        return this.outMsgid;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        if (this.retrieveMessageId) {
            this.outMsgid = new byte[16];
            this.meg.unmarshalBuffer(this.outMsgid, 0, 16);
        }
        this.meg.unmarshalUB2();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
