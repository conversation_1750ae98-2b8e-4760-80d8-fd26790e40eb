package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.oracore.OracleTypeADT;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoac.class */
class T4CTTIoac implements Diagnosable {
    static final short UACFIND = 1;
    static final short UACFALN = 2;
    static final short UACFRCP = 4;
    static final short UACFBBV = 8;
    static final short UACFNCP = 16;
    static final short UACFBLP = 32;
    static final short UACFARR = 64;
    static final short UACFIGN = 128;
    static final int UACFNSCL = 1;
    static final int UACFBUC = 2;
    static final int UACFSKP = 4;
    static final int UACFCHRCNT = 8;
    static final int UACFNOADJ = 16;
    static final int UACFCUS = 4096;
    static final int UACFLSZ = 33554432;
    static final int UACFVFSP = 134217728;
    static final long UACFSALD = 34359738368L;
    int oaccsi;
    short oaccsfrm;
    static int maxBindArrayLength;
    T4CMAREngine meg;
    T4CConnection connection;
    short requestedtype;
    short oacdty;
    short oacflg;
    short oacpre;
    short oacscl;
    int oacmxl;
    int oacmxlc;
    int oacmal;
    long oacfl2;
    int oactoidl;
    int oacvsn;
    private static final String CLASS_NAME = T4CTTIoac.class.getName();
    static final byte[] NO_BYTES = new byte[0];
    int oaccollid = 0;
    byte[] oactoid = NO_BYTES;

    T4CTTIoac(T4CConnection _connection) {
        this.connection = _connection;
        this.meg = this.connection.mare;
    }

    void init(short _type, int _oacmxl) {
        this.requestedtype = _type;
        if (_type == 9 || _type == 1 || _type == 996) {
            this.oacdty = (short) 1;
        } else if (_type == 104) {
            this.oacdty = (short) 11;
        } else if (_type == 6 || _type == 2) {
            this.oacdty = (short) 2;
        } else if (_type == 15) {
            this.oacdty = (short) 23;
        } else if (_type == 116) {
            this.oacdty = (short) 102;
        } else {
            this.oacdty = _type;
        }
        if (this.oacdty == 1 || this.oacdty == 96) {
            this.oacfl2 = 16L;
        } else {
            this.oacfl2 = 0L;
        }
        if (this.oacdty == 102) {
            this.oacmxl = 4;
        } else {
            this.oacmxl = _oacmxl;
        }
        this.oacflg = (short) 3;
        this.oacscl = (short) 0;
    }

    protected short getRequestedType() {
        return this.requestedtype;
    }

    boolean isNType() {
        boolean ret = this.oaccsfrm == 2;
        return ret;
    }

    void unmarshal() throws SQLException, IOException {
        this.oacdty = this.meg.unmarshalUB1();
        this.oacflg = this.meg.unmarshalUB1();
        this.oacpre = this.meg.unmarshalUB1();
        debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshal", "oacdty={0}; oacflg={1}; oacpre={2}", null, null, () -> {
            return new Object[]{OracleLog.toHex((byte) this.oacdty), OracleLog.toHex((byte) this.oacflg), OracleLog.toHex((byte) this.oacpre)};
        });
        this.oacscl = this.meg.unmarshalSB1();
        this.oacmxl = this.meg.unmarshalSB4();
        this.oacmal = this.meg.unmarshalSB4();
        this.oacfl2 = this.meg.unmarshalSB8();
        this.oactoid = this.meg.unmarshalDALC();
        this.oactoidl = this.oactoid == null ? 0 : this.oactoid.length;
        this.oacvsn = this.meg.unmarshalUB2();
        this.oaccsi = this.meg.unmarshalUB2();
        this.oaccsfrm = this.meg.unmarshalUB1();
        if (this.connection.getTTCVersion() >= 2) {
            this.oacmxlc = (int) this.meg.unmarshalUB4();
        }
        if (this.connection.getTTCVersion() >= 8) {
            this.oaccollid = (int) this.meg.unmarshalUB4();
        }
        if (this.oacmxl > 0) {
            switch (this.oacdty) {
                case 2:
                    this.oacmxl = 22;
                    break;
                case 12:
                    this.oacmxl = 7;
                    break;
                case 181:
                    this.oacmxl = 13;
                    break;
            }
        }
    }

    void setMal(int _oacmal) {
        this.oacmal = _oacmal;
    }

    void addFlg(short flag) {
        this.oacflg = (short) (this.oacflg | flag);
    }

    void addFlg2(long flag) {
        this.oacfl2 |= flag;
    }

    void setFormOfUse(short _oaccsfrm) {
        this.oaccsfrm = _oaccsfrm;
    }

    void setCharset(int _cs) {
        this.oaccsi = _cs;
    }

    void setMxlc(int _oacmxlc) {
        this.oacmxlc = _oacmxlc;
    }

    void setPrecision(short _oacpre) {
        this.oacpre = _oacpre;
    }

    void setScale(short _oacscl) {
        this.oacscl = _oacscl;
    }

    void setTimestampFractionalSecondsPrecision(short _oacfspre) {
        this.oacscl = _oacfspre;
    }

    void setADT(OracleTypeADT otype) {
        this.oactoid = otype.getTOID();
        this.oacvsn = otype.getTypeVersion();
        this.oaccsi = 2;
        this.oaccsfrm = (short) otype.getCharSetForm();
    }

    void marshal() throws IOException {
        this.meg.marshalUB1(this.oacdty);
        this.meg.marshalUB1(this.oacflg);
        this.meg.marshalUB1(this.oacpre);
        if (this.oacdty == 2 || this.oacdty == 180 || this.oacdty == 181 || this.oacdty == 231 || this.oacdty == 183) {
            this.meg.marshalUB2(this.oacscl);
        } else {
            this.meg.marshalUB1(this.oacscl);
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "marshal", "T4CTTIoac.marshal() oacdty: {0} oacmxl: 0x{1}", null, null, Short.valueOf(this.oacdty), Integer.toHexString(this.oacmxl));
        this.meg.marshalUB4(this.oacmxl);
        this.meg.marshalSB4(this.oacmal);
        this.meg.marshalSB8(this.oacfl2);
        this.meg.marshalDALC(this.oactoid);
        this.meg.marshalUB2(this.oacvsn);
        this.meg.marshalUB2(this.oaccsi);
        this.meg.marshalUB1(this.oaccsfrm);
        if (this.connection.getTTCVersion() >= 2) {
            this.meg.marshalUB4(this.oacmxlc);
        }
        if (this.connection.getTTCVersion() >= 8) {
            this.meg.marshalUB4(this.oaccollid);
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "marshal", "T4CTTIoac.marshal: oacdty={0},oacflg={1},oacpre={2},oacscl={3},oacmxl={4},oacmal={5},oacfl2={6},oactoid={7},oacvsn={8},oaccsi={9},oaccsfrm={10},oacmxlc={11}", (String) null, (String) null, Short.valueOf(this.oacdty), Short.valueOf(this.oacflg), Short.valueOf(this.oacpre), Short.valueOf(this.oacscl), Integer.valueOf(this.oacmxl), Integer.valueOf(this.oacmal), Long.toHexString(this.oacfl2).toUpperCase(), Parameter.arg(Format.Style.BYTE_ARRAY, this.oactoid, new long[0]), Integer.valueOf(this.oacvsn), Integer.valueOf(this.oaccsi), Short.valueOf(this.oaccsfrm), Integer.valueOf(this.oacmxlc));
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.connection.getDiagnosable();
    }
}
