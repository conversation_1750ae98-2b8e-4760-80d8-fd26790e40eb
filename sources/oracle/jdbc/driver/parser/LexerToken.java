package oracle.jdbc.driver.parser;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.StringTokenizer;
import oracle.jdbc.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/LexerToken.class */
public class LexerToken {
    public String content;
    public int begin;
    public int end;
    public Token type;
    public static boolean isSqlPlusCmd = false;
    public static String[] customLineCommentPrefixes = new String[0];
    public static final int QuotedStrings = 1;
    public static final int SqlPlusComments = 2;
    public static final int PlSqlMacros = 4;
    public static final int JavaLineComments = 8;
    public static final int SqlLineComments = 16;
    public static final int JavaSqlMultilineComments = 32;
    public static final int HtmlComments = 64;
    public static final int JsonStrings = 128;
    public static final int PoundComments = 256;
    public static final int RegExp = 512;
    public static final int SQL = 63;
    public static final int GRAMMAR = 49;
    public static final int ARBORI = 57;
    public static final int TEST = 49;
    public static final int JAVA = 41;
    public static final int JS = 937;

    public LexerToken(CharSequence text, int from, int to, Token t) {
        this.content = text.toString();
        this.begin = from;
        this.end = to;
        this.type = t;
    }

    public String toString() {
        return "[" + this.begin + "," + this.end + ") " + this.content + "   <" + this.type + ">";
    }

    private static LinkedList<LexerToken> tokenize(String sourceExpr, int flags, String extraOper, InterruptedException interrupted) throws InterruptedException {
        int context;
        int lineEnd;
        int nextSlash;
        LinkedList<LexerToken> ret = new LinkedList<>();
        String operation = "(){}[]^-|!*+.><='\",;:%@?/\\~" + extraOper;
        if ((flags & 64) == 64) {
            operation = "></-=\"!";
        }
        if ((flags & 256) == 256) {
            operation = operation + "#";
        }
        StringTokenizer st = new StringTokenizer(sourceExpr, operation + " \n\r\t", true);
        int pos = 0;
        boolean isWrapped = false;
        int contextualFlags = flags;
        while (st.hasMoreTokens()) {
            if (interrupted != null && Thread.interrupted()) {
                throw interrupted;
            }
            String token = st.nextToken();
            pos += token.length();
            LexerToken last = null;
            if (ret.size() > 0) {
                last = ret.getLast();
            }
            if (isWrapped) {
                if ("/".equals(token) && last != null && "\n".equals(last.content)) {
                    ret.add(new LexerToken("\"/\"", pos - "\"/\"".length(), pos, Token.IDENTIFIER));
                    isWrapped = false;
                } else if ("\n".equals(token)) {
                    ret.add(new LexerToken(token, pos - token.length(), pos, Token.WS));
                } else if ("\n".equals(last.content)) {
                    last.content = "?";
                }
            } else if (last != null && last.type == Token.COMMENT && (contextualFlags & 32) == 32 && (!last.content.endsWith("*/") || last.content.equals("/*/"))) {
                if ("*".equals(token) || "/".equals(token)) {
                    last.content += token;
                } else {
                    last.content = "/* ... ";
                }
                last.end = pos;
                if (last != null && last.type == Token.COMMENT && last.content.endsWith("*/") && !last.content.equals("/*/")) {
                    last.content = sourceExpr.substring(last.begin, last.end);
                }
            } else if (last != null && last.type == Token.COMMENT && (contextualFlags & 64) == 64 && !last.content.endsWith(">")) {
                if ("-".equals(token)) {
                    last.content += token;
                } else if (">".equals(token) && (last.content.length() <= 2 || last.content.charAt(2) != '-')) {
                    last.content += token;
                } else if (">".equals(token) && (last.content.length() <= 2 || (last.content.charAt(last.content.length() - 2) == '-' && last.content.charAt(last.content.length() - 3) == '-'))) {
                    last.content += token;
                }
                last.end = pos;
                if (last != null && last.type == Token.COMMENT && last.content.endsWith(">")) {
                    last.content = sourceExpr.substring(last.begin, last.end);
                }
            } else if (last != null && ((last.type == Token.LINE_COMMENT || last.type == Token.DBTOOLS_COMMAND || last.type == Token.SQLPLUS_COMMAND) && !"\n".equals(token) && !"\r".equals(token))) {
                last.content += token;
                last.end += token.length();
            } else {
                if (last != null && ((last.type == Token.LINE_COMMENT || last.type == Token.DBTOOLS_COMMAND || last.type == Token.SQLPLUS_COMMAND) && ("\n".equals(token) || "\r".equals(token)))) {
                    last.end = last.begin + last.content.length();
                }
                if (last != null && last.type == Token.QUOTED_STRING && !last.isStandardLiteral(contextualFlags) && !last.isAltLiteral()) {
                    last.content += token;
                    last.end = last.begin + last.content.length();
                } else if (last != null && last.type == Token.DQUOTED_STRING && (("\"" != token || last.content.endsWith("\\")) && (!last.content.endsWith("\"") || (((contextualFlags & 128) == 128 && last.content.endsWith("\\\"")) || last.content.length() == 1)))) {
                    last.content += token;
                    last.end = last.begin + last.content.length();
                } else if (last != null && last.type == Token.DQUOTED_STRING && "\"".equals(token)) {
                    last.end = pos;
                    last.content = sourceExpr.substring(last.begin, last.end);
                } else if (last == null || last.type != Token.BQUOTED_STRING || "`".equals(token) || (last.content.endsWith("`") && last.content.length() > 1)) {
                    if (last != null && last.type == Token.BQUOTED_STRING && "`".equals(token)) {
                        if (!insideTemplateVar(sourceExpr.substring(last.begin + 1, pos)) && (sourceExpr.charAt(pos - 2) != '\\' || (pos - 3 >= 0 && sourceExpr.charAt(pos - 3) == '\\'))) {
                            last.end = pos;
                            last.content = sourceExpr.substring(last.begin, last.end);
                        }
                    } else if (last != null && last.type == Token.REGEXP && pos <= last.end) {
                        if ("/".equals(token) && last.end == pos) {
                            last.content = sourceExpr.substring(last.begin, last.end);
                        }
                    } else if ((contextualFlags & 32) == 32 && "*".equals(token) && last != null && "/".equals(last.content)) {
                        last.content += token;
                        last.end = last.begin + last.content.length();
                        last.type = Token.COMMENT;
                    } else if ((contextualFlags & 512) == 512 && !"*".equals(token) && !"/".equals(token) && last != null && "/".equals(last.content) && 0 < (nextSlash = terminatingSlash(sourceExpr, last.end, (lineEnd = sourceExpr.indexOf(10, last.end)))) && (nextSlash < lineEnd || lineEnd == -1)) {
                        last.content += token;
                        last.end = nextSlash + 1;
                        last.type = Token.REGEXP;
                    } else {
                        if (((contextualFlags & 16) == 16 && "-".equals(token) && last != null && "-".equals(last.content)) || ((contextualFlags & 8) == 8 && "/".equals(token) && last != null && "/".equals(last.content))) {
                            boolean isHttp = false;
                            if ("/".equals(token)) {
                                int start = last.begin - "http:".length();
                                if (0 <= start && "http:".equalsIgnoreCase(sourceExpr.substring(start, last.begin))) {
                                    isHttp = true;
                                }
                                int start2 = last.begin - "https:".length();
                                if (0 <= start2 && "https:".equalsIgnoreCase(sourceExpr.substring(start2, last.begin))) {
                                    isHttp = true;
                                }
                            }
                            if (!isHttp) {
                                last.content += token;
                                last.type = Token.LINE_COMMENT;
                                last.end += token.length();
                            }
                        }
                        if ((contextualFlags & 64) == 64 && "!".equals(token) && last != null && "<".equals(last.content)) {
                            last.content += token;
                            last.end = last.begin + last.content.length();
                            last.type = Token.COMMENT;
                        } else if ((contextualFlags & 2) == 2 && (("rem".equalsIgnoreCase(token) || "rema".equalsIgnoreCase(token) || "remar".equalsIgnoreCase(token) || "remark".equalsIgnoreCase(token) || "pro".equalsIgnoreCase(token) || "prom".equalsIgnoreCase(token) || "promp".equalsIgnoreCase(token) || "prompt".equalsIgnoreCase(token)) && (last == null || "\n".equals(last.content) || "\r".equals(last.content)))) {
                            ret.add(new LexerToken(token, pos - token.length(), -9, Token.LINE_COMMENT));
                        } else {
                            String[] strArr = customLineCommentPrefixes;
                            int length = strArr.length;
                            int i = 0;
                            while (true) {
                                if (i < length) {
                                    String pref = strArr[i];
                                    if (pref.equalsIgnoreCase(token)) {
                                        ret.add(new LexerToken(token, pos - token.length(), -9, Token.LINE_COMMENT));
                                        break;
                                    }
                                    if (last == null || !pref.equalsIgnoreCase(last.content)) {
                                        i++;
                                    } else {
                                        last.content += token;
                                        last.end = last.begin + last.content.length() + token.length();
                                        last.type = Token.LINE_COMMENT;
                                        break;
                                    }
                                } else if ((contextualFlags & 256) == 256 && "#".equals(token)) {
                                    ret.add(new LexerToken(token, pos - token.length(), -9, Token.LINE_COMMENT));
                                } else if ("soda".equalsIgnoreCase(token) && (last == null || "\n".equals(last.content) || "\r".equals(last.content))) {
                                    ret.add(new LexerToken(token, pos - token.length(), -9, Token.DBTOOLS_COMMAND));
                                } else if ((contextualFlags & 2) == 2 && isSqlPlusPrefix(token) && (last == null || "\n".equals(last.content) || "\r".equals(last.content))) {
                                    ret.add(new LexerToken(token, pos - token.length(), -9, Token.SQLPLUS_COMMAND));
                                } else {
                                    if (isSqlPlusCmd && last != null && "-".equals(last.content) && ("\n".equals(token) || "\r".equals(token))) {
                                        last.type = Token.SQLPLUSLINECONTINUE_SKIP;
                                    }
                                    String lastUpper = "N/A";
                                    if (last != null) {
                                        lastUpper = last.content.toUpperCase();
                                    }
                                    if ((contextualFlags & 4) == 4 && ("$IF".equalsIgnoreCase(token) || "$ELSIF".equalsIgnoreCase(token) || "$ELSE".equalsIgnoreCase(token) || "$END".equalsIgnoreCase(token) || "$ERROR".equalsIgnoreCase(token))) {
                                        ret.add(new LexerToken(token, pos - token.length(), pos, Token.MACRO_SKIP));
                                    } else if (last != null && last.type == Token.MACRO_SKIP && lastUpper.startsWith("$IF") && lastUpper.endsWith("$THEN")) {
                                        ret.add(new LexerToken(token, pos - token.length(), pos, Token.MACRO_SKIP));
                                    } else if (last != null && last.type == Token.MACRO_SKIP && (lastUpper.startsWith("$IF") || lastUpper.startsWith("$ELSIF") || lastUpper.startsWith("$ELSE") || lastUpper.startsWith("$ERROR"))) {
                                        last.content += token;
                                        last.end += token.length();
                                    } else if (last != null && last.type == Token.IDENTIFIER && last.end == -11 && last.content.startsWith("@") && !"\n".equals(token) && !"\r".equals(token)) {
                                        last.content += token;
                                    } else if (last != null && last.type == Token.IDENTIFIER && last.end == -11 && last.content.startsWith("@") && ("\n".equals(token) || "\r".equals(token))) {
                                        last.end = pos - 1;
                                        ret.add(new LexerToken(token, pos - 1, pos, Token.WS));
                                    } else if ((contextualFlags & 1) == 1 && "'".equals(token)) {
                                        if (last != null && ("q".equalsIgnoreCase(last.content) || "N".equalsIgnoreCase(last.content) || "u".equalsIgnoreCase(last.content) || "nq".equalsIgnoreCase(last.content) || "uq".equalsIgnoreCase(last.content))) {
                                            last.content += token;
                                            last.type = Token.QUOTED_STRING;
                                        } else {
                                            ret.add(new LexerToken(token, pos - 1, -10, Token.QUOTED_STRING));
                                        }
                                    } else if ((contextualFlags & 1) == 1 && "\"".equals(token)) {
                                        ret.add(new LexerToken(token, pos - 1, -11, Token.DQUOTED_STRING));
                                    } else if ("`".equals(token) && 0 <= operation.indexOf(96)) {
                                        ret.add(new LexerToken(token, pos - 1, -11, Token.BQUOTED_STRING));
                                    } else if (token.length() == 1 && 0 <= " \n\r\t".indexOf(token.charAt(0))) {
                                        ret.add(new LexerToken(token, pos - 1, pos, Token.WS));
                                    } else if (token.length() == 1 && 0 <= operation.indexOf(token.charAt(0))) {
                                        ret.add(new LexerToken(token, pos - 1, pos, Token.OPERATION));
                                    } else if ('0' <= token.charAt(0) && token.charAt(0) <= '9' && (last == null || !"#".equals(last.content))) {
                                        if (isHexLiteral(token)) {
                                            ret.add(new LexerToken(token, pos - token.length(), pos, Token.DIGITS));
                                        } else if (!fixedExponent(token, ret, pos - token.length())) {
                                            if (token.charAt(token.length() - 1) == 'K' || token.charAt(token.length() - 1) == 'k' || token.charAt(token.length() - 1) == 'M' || token.charAt(token.length() - 1) == 'm' || token.charAt(token.length() - 1) == 'G' || token.charAt(token.length() - 1) == 'g' || token.charAt(token.length() - 1) == 'T' || token.charAt(token.length() - 1) == 't' || token.charAt(token.length() - 1) == 'P' || token.charAt(token.length() - 1) == 'p' || token.charAt(token.length() - 1) == 'E' || token.charAt(token.length() - 1) == 'e') {
                                                ret.add(new LexerToken(token.substring(0, token.length() - 1), pos - token.length(), pos - 1, Token.DIGITS));
                                                ret.add(new LexerToken(token.substring(token.length() - 1), pos - 1, pos, Token.DIGITS));
                                            } else {
                                                ret.add(new LexerToken(token, pos - token.length(), pos, Token.DIGITS));
                                            }
                                        }
                                    } else {
                                        if ("WRAPPED".equalsIgnoreCase(token) && last != null) {
                                            Iterator<LexerToken> descIter = ret.descendingIterator();
                                            boolean sawId = false;
                                            while (descIter.hasNext()) {
                                                LexerToken t = descIter.next();
                                                if (sawId && OracleConnection.CLIENT_INFO_KEY_SEPARATOR.equalsIgnoreCase(t.content)) {
                                                    sawId = false;
                                                } else {
                                                    if (sawId && ("PROCEDURE".equalsIgnoreCase(t.content) || "FUNCTION".equalsIgnoreCase(t.content) || "TRIGGER".equalsIgnoreCase(t.content) || "TYPE".equalsIgnoreCase(t.content) || "PACKAGE".equalsIgnoreCase(t.content) || "BODY".equalsIgnoreCase(t.content))) {
                                                        isWrapped = true;
                                                        break;
                                                    }
                                                    if (t.type != Token.WS && t.type != Token.COMMENT && t.type != Token.LINE_COMMENT) {
                                                        if (t.type != Token.IDENTIFIER && t.type != Token.DQUOTED_STRING) {
                                                            break;
                                                        }
                                                        sawId = true;
                                                    }
                                                }
                                            }
                                        }
                                        ret.add(new LexerToken(token, pos - token.length(), pos, Token.IDENTIFIER));
                                        if (contextualFlags == flags && (context = inferContext(token)) != -1) {
                                            contextualFlags |= context;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (ret.size() > 0) {
            LexerToken last2 = ret.getLast();
            last2.end = sourceExpr.length();
        }
        return ret;
    }

    private static int inferContext(String token) {
        if ("var".equals(token) || "const".equals(token) || "let".equals(token)) {
            return JS;
        }
        return -1;
    }

    private static boolean insideTemplateVar(String str) {
        StringTokenizer st = new StringTokenizer(str, "${}", true);
        int nesting = 0;
        String str2 = null;
        while (true) {
            String prior = str2;
            if (!st.hasMoreTokens()) {
                break;
            }
            String token = st.nextToken();
            if ("{".equals(token) && "$".equals(prior)) {
                nesting++;
            } else if ("}".equals(token)) {
                nesting--;
            }
            str2 = token;
        }
        return 0 < nesting;
    }

    private static int terminatingSlash(String sourceExpr, int start, int end) {
        String input;
        if (end < 0) {
            input = sourceExpr.substring(start);
        } else {
            input = sourceExpr.substring(start, end);
        }
        StringTokenizer st = new StringTokenizer(input, "\\/ ()[]", true);
        String prior = null;
        int pos = start;
        int parenBalance = 0;
        while (st.hasMoreTokens()) {
            String current = st.nextToken();
            if ("[".equals(prior) && "]".equals(current)) {
                prior = null;
                pos += current.length();
            } else if ("[".equals(prior)) {
                pos += current.length();
            } else if ("\\".equals(prior)) {
                prior = null;
                pos += current.length();
            } else {
                if (")".equals(prior)) {
                    parenBalance--;
                }
                if ("(".equals(prior)) {
                    parenBalance++;
                }
                if ("/".equals(current)) {
                    if (parenBalance != 0) {
                        return -1;
                    }
                    if (" ".equals(prior) && input.charAt(0) == ' ') {
                        if ("g".equalsIgnoreCase(st.nextToken())) {
                            return pos + 1;
                        }
                        return -1;
                    }
                    return pos;
                }
                prior = current;
                pos += current.length();
            }
        }
        return -1;
    }

    private static boolean isSqlPlusPrefix(String token) {
        if ("@".equals(token) || "INPUT".equalsIgnoreCase(token) || "HOST".equalsIgnoreCase(token) || "PAUSE".equalsIgnoreCase(token)) {
            return true;
        }
        return false;
    }

    private static boolean fixedExponent(String input, List<LexerToken> ret, int pos) {
        String test = input.toLowerCase();
        if (!test.contains("e") && !test.contains("f") && !test.contains("d")) {
            return false;
        }
        StringTokenizer st = new StringTokenizer(test, "efd", true);
        while (st.hasMoreTokens()) {
            String token = st.nextToken();
            pos += token.length();
            if ('0' <= token.charAt(0) && token.charAt(0) <= '9') {
                ret.add(new LexerToken(token, pos - token.length(), pos, Token.DIGITS));
            } else {
                ret.add(new LexerToken(token, pos - token.length(), pos, Token.IDENTIFIER));
            }
        }
        return true;
    }

    private static boolean isHexLiteral(String iNput) {
        if (iNput.length() < 3 || iNput.charAt(0) != '0') {
            return false;
        }
        String input = iNput.toLowerCase();
        if (input.charAt(1) != 'x') {
            return false;
        }
        for (int i = 2; i < input.length(); i++) {
            if (!Character.isDigit(input.charAt(i)) && input.charAt(i) != 'a' && input.charAt(i) != 'b' && input.charAt(i) != 'c' && input.charAt(i) != 'd' && input.charAt(i) != 'e' && input.charAt(i) != 'f') {
                return false;
            }
        }
        return true;
    }

    public static List<LexerToken> parse(String input) {
        return parse(input, false);
    }

    public static List<LexerToken> parse(String input, boolean keepWSandCOMMENTS) {
        return parse(input, keepWSandCOMMENTS, 63);
    }

    public static List<LexerToken> parse(String input, boolean keepWSandCOMMENTS, int flags) {
        try {
            return parse(input, keepWSandCOMMENTS, flags, null);
        } catch (InterruptedException e) {
            throw new AssertionError("parse(...,interrupted==false) has thrown InterruptedException");
        }
    }

    public static List<LexerToken> parse(String input, boolean keepWSandCOMMENTS, int flags, InterruptedException interrupted) throws InterruptedException {
        ArrayList<LexerToken> ret = new ArrayList<>();
        parse(input, keepWSandCOMMENTS, flags, "", ret, interrupted);
        return ret;
    }

    private static void parse(String input, boolean keepWSandCOMMENTS, int flags, String extraOper, ArrayList<LexerToken> ret, InterruptedException interrupted) throws InterruptedException {
        LexerToken last = null;
        Iterator<LexerToken> it = tokenize(input, flags, extraOper, interrupted).iterator();
        while (it.hasNext()) {
            LexerToken token = it.next();
            if (token.type == Token.IDENTIFIER && last != null && last.type == Token.DQUOTED_STRING && token.content.startsWith("___")) {
                last.content += token.content;
                last.end = token.end;
            } else {
                if (token.type == Token.QUOTED_STRING) {
                    if (last != null && last.type == Token.QUOTED_STRING) {
                        last.content += token.content;
                        last.end = token.end;
                    } else if (last != null && last.type == Token.IDENTIFIER && "n".equalsIgnoreCase(last.content) && last.end == token.begin) {
                        last.begin = token.begin;
                        last.end = token.end;
                        last.type = token.type;
                        last.content = token.content;
                    }
                }
                if (token.content.startsWith("@")) {
                    token.end = token.begin + token.content.length();
                }
                if ("#".equals(token.content) && last != null && last.type == Token.IDENTIFIER) {
                    last.end++;
                    last.content += "#";
                } else if ((token.type == Token.IDENTIFIER || token.type == Token.DIGITS) && last != null && last.content.endsWith("#") && last.type == Token.IDENTIFIER) {
                    last.end += token.content.length();
                    last.content += token.content;
                } else {
                    if (keepWSandCOMMENTS || (token.type != Token.WS && token.type != Token.COMMENT && token.type != Token.LINE_COMMENT && token.type != Token.MACRO_SKIP && token.type != Token.SQLPLUSLINECONTINUE_SKIP)) {
                        ret.add(token);
                    }
                    last = token;
                }
            }
        }
    }

    private static char matchingDelimiter(char ch) {
        if ('<' == ch) {
            return '>';
        }
        if ('[' == ch) {
            return ']';
        }
        if ('{' == ch) {
            return '}';
        }
        if ('(' == ch) {
            return ')';
        }
        return ch;
    }

    boolean isStandardLiteral(int flags) {
        if (this.content.length() < 2) {
            return false;
        }
        if (this.content.charAt(0) != '\'' && this.content.charAt(0) != 'n' && this.content.charAt(0) != 'N' && this.content.charAt(0) != 'u' && this.content.charAt(0) != 'U') {
            return false;
        }
        String text = this.content;
        if (text.charAt(0) == 'n' || text.charAt(0) == 'N' || text.charAt(0) == 'u' || text.charAt(0) == 'U') {
            if (text.length() < 3) {
                return false;
            }
            text = text.substring(1);
        }
        return text.length() >= 2 && text.charAt(0) == '\'' && text.charAt(text.length() - 1) == '\'' && (flags != 937 || text.charAt(text.length() - 2) != '\\' || text.length() < 3 || text.charAt(text.length() - 3) == '\\');
    }

    boolean isAltLiteral() {
        String text;
        if (this.content.length() < 5) {
            return false;
        }
        if (this.content.charAt(0) != 'q' && this.content.charAt(0) != 'Q' && this.content.charAt(0) != 'n' && this.content.charAt(0) != 'N' && this.content.charAt(0) != 'u' && this.content.charAt(0) != 'U') {
            return false;
        }
        String text2 = this.content;
        if ((this.content.charAt(0) == 'n' || this.content.charAt(0) == 'N' || this.content.charAt(0) == 'u' || this.content.charAt(0) == 'U') && (this.content.charAt(1) == 'q' || this.content.charAt(1) == 'Q')) {
            if (text2.length() < 6) {
                return false;
            }
            text = text2.substring(2);
        } else if (this.content.charAt(0) == 'q' || this.content.charAt(0) == 'Q' || this.content.charAt(0) == 'u' || this.content.charAt(0) == 'U') {
            text = text2.substring(1);
        } else {
            return false;
        }
        if (text.charAt(0) == '\'' && text.charAt(text.length() - 1) == '\'') {
            String text3 = text.substring(1, text.length() - 1);
            return matchingDelimiter(text3.charAt(0)) == text3.charAt(text3.length() - 1);
        }
        return false;
    }
}
