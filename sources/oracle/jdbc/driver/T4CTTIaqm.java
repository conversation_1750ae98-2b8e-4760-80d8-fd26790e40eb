package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.ns.SQLnetDef;
import oracle.sql.TIMESTAMP;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIaqm.class */
class T4CTTIaqm implements Diagnosable {
    private static final String CLASS_NAME = T4CTTIaqm.class.getName();
    static final int ATTR_ORIGINAL_MSGID = 69;
    static final byte ATTR_AGENT_NAME = 64;
    static final byte ATTR_AGENT_ADDRESS = 65;
    static final byte ATTR_AGENT_PROTOCOL = 66;
    static final int AQM_MSG_NO_DELAY = 0;
    static final int AQM_MSG_NO_EXPIRATION = -1;
    static final int AQM_MSGPROP_CORRID_SIZE = 128;
    int aqmpri;
    int aqmdel;
    int aqmexp;
    byte[] aqmcorBytes;
    int aqmcorBytesLength;
    int aqmatt;
    byte[] aqmeqnBytes;
    int aqmeqnBytesLength;
    int aqmsta;
    TIMESTAMP aqmeqt;
    byte[] aqmetiBytes;
    byte[] originalMsgId;
    byte[] aqmuprBytes;
    int aqmuprBytesLength;
    T4Ctoh toh;
    int aqmcsn;
    int aqmdsn;
    int aqmflg;
    int aqmshardNum;
    T4CMAREngine mar;
    T4CConnection connection;
    private byte[] aqmeqtBuffer = new byte[7];
    private int[] retInt = new int[1];
    byte[] senderAgentName = null;
    int senderAgentNameLength = 0;
    byte[] senderAgentAddress = null;
    int senderAgentAddressLength = 0;
    byte senderAgentProtocol = 0;

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.connection.getDiagnosable();
    }

    T4CTTIaqm(T4CConnection _connection, T4Ctoh _toh) {
        this.toh = _toh;
        this.connection = _connection;
        this.mar = this.connection.mare;
    }

    void initToDefaultValues() {
        this.aqmpri = 0;
        this.aqmdel = 0;
        this.aqmexp = -1;
        this.aqmcorBytes = null;
        this.aqmcorBytesLength = 0;
        this.aqmatt = 0;
        this.aqmeqnBytes = null;
        this.aqmeqnBytesLength = 0;
        this.aqmsta = 0;
        this.aqmeqt = null;
        this.aqmetiBytes = null;
        this.aqmuprBytes = null;
        this.aqmuprBytesLength = 0;
        this.senderAgentName = null;
        this.senderAgentNameLength = 0;
        this.senderAgentAddress = null;
        this.senderAgentAddressLength = 0;
        this.senderAgentProtocol = (byte) 0;
        this.originalMsgId = null;
        this.aqmcsn = 0;
        this.aqmdsn = 0;
        this.aqmflg = 0;
        this.aqmshardNum = -1;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v27, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r0v29, types: [byte[], byte[][]] */
    void marshal() throws IOException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshal", "aqmpri={0}, aqmdel={1}, aqmexp={2}, aqmsta={3}", (String) null, (Throwable) null, Integer.valueOf(this.aqmpri), Integer.valueOf(this.aqmdel), Integer.valueOf(this.aqmexp), Integer.valueOf(this.aqmsta));
        this.mar.marshalSB4(this.aqmpri);
        this.mar.marshalSB4(this.aqmdel);
        this.mar.marshalSB4(this.aqmexp);
        if (this.aqmcorBytes != null && this.aqmcorBytes.length != 0) {
            this.mar.marshalSWORD(this.aqmcorBytes.length);
            this.mar.marshalCLR(this.aqmcorBytes, 0, this.aqmcorBytes.length);
        } else {
            this.mar.marshalSWORD(0);
        }
        this.mar.marshalSB4(0);
        if (this.aqmeqnBytes != null && this.aqmeqnBytes.length != 0) {
            this.mar.marshalSWORD(this.aqmeqnBytes.length);
            this.mar.marshalCLR(this.aqmeqnBytes, 0, this.aqmeqnBytes.length);
        } else {
            this.mar.marshalSWORD(0);
        }
        this.mar.marshalSB4(this.aqmsta);
        this.mar.marshalSWORD(0);
        if (this.connection.getTTCVersion() >= 3) {
            if (this.aqmetiBytes != null && this.aqmetiBytes.length > 0) {
                this.mar.marshalSWORD(this.aqmetiBytes.length);
                this.mar.marshalCLR(this.aqmetiBytes, 0, this.aqmetiBytes.length);
            } else {
                this.mar.marshalSWORD(0);
            }
        }
        [2][0] = this.senderAgentProtocol;
        ?? r0 = {this.senderAgentName, this.senderAgentAddress, 0, 0};
        ?? r02 = {0, 0, new byte[1], this.originalMsgId};
        int[] extensionKeywords = {64, 65, 66, 69};
        this.mar.marshalSWORD(4);
        this.mar.marshalUB1((short) 14);
        this.mar.marshalKPDKV(r0, r02, extensionKeywords);
        if (this.connection.getTTCVersion() >= 3) {
            this.mar.marshalUB4(0L);
            this.mar.marshalUB4(0L);
            this.mar.marshalUB4(0L);
            if (this.connection.getTTCVersion() >= 4) {
                this.mar.marshalUB4(0L);
            }
        }
        if (this.connection.getTTCVersion() >= 16) {
            if (this.aqmshardNum != -1) {
                this.mar.marshalUB4(this.aqmshardNum);
            } else {
                this.mar.marshalUB4(SQLnetDef.NSPDDLSLMAX);
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v61, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r0v65, types: [byte[], byte[][]] */
    void receive() throws SQLException, IOException {
        this.aqmpri = this.mar.unmarshalSB4();
        this.aqmdel = this.mar.unmarshalSB4();
        this.aqmexp = this.mar.unmarshalSB4();
        int iUnmarshalSWORD = this.mar.unmarshalSWORD();
        if (iUnmarshalSWORD > 0) {
            this.aqmcorBytes = new byte[iUnmarshalSWORD];
            int[] iArr = new int[1];
            this.mar.unmarshalCLR(this.aqmcorBytes, 0, iArr, this.aqmcorBytes.length);
            this.aqmcorBytesLength = iArr[0];
        } else {
            this.aqmcorBytes = null;
        }
        this.aqmatt = this.mar.unmarshalSB4();
        int iUnmarshalSWORD2 = this.mar.unmarshalSWORD();
        if (iUnmarshalSWORD2 > 0) {
            this.aqmeqnBytes = new byte[iUnmarshalSWORD2];
            int[] iArr2 = new int[1];
            this.mar.unmarshalCLR(this.aqmeqnBytes, 0, iArr2, this.aqmeqnBytes.length);
            this.aqmeqnBytesLength = iArr2[0];
        } else {
            this.aqmeqnBytes = null;
        }
        this.aqmsta = this.mar.unmarshalSB4();
        if (this.mar.unmarshalSB4() > 0) {
            this.mar.unmarshalCLR(this.aqmeqtBuffer, 0, this.retInt, 7);
            this.aqmeqt = new TIMESTAMP(this.aqmeqtBuffer);
        }
        if (this.connection.getTTCVersion() >= 3) {
            int iUnmarshalSWORD3 = this.mar.unmarshalSWORD();
            if (iUnmarshalSWORD3 > 0) {
                this.aqmetiBytes = new byte[iUnmarshalSWORD3];
                this.mar.unmarshalCLR(this.aqmetiBytes, 0, new int[1], this.aqmetiBytes.length);
            } else {
                this.aqmetiBytes = null;
            }
        }
        int iUnmarshalSWORD4 = this.mar.unmarshalSWORD();
        if (iUnmarshalSWORD4 > 0) {
            this.mar.unmarshalUB1();
            ?? r0 = new byte[iUnmarshalSWORD4];
            int[] iArr3 = new int[iUnmarshalSWORD4];
            ?? r02 = new byte[iUnmarshalSWORD4];
            int[] iArr4 = new int[iUnmarshalSWORD4];
            this.mar.unmarshalKPDKV(r0, iArr3, r02, iArr4);
            for (int i = 0; i < iUnmarshalSWORD4; i++) {
                if (iArr4[i] == 64 && r0[i] != 0 && iArr3[i] > 0) {
                    this.senderAgentName = r0[i];
                    this.senderAgentNameLength = iArr3[i];
                }
                if (iArr4[i] == 65 && r0[i] != 0 && iArr3[i] > 0) {
                    this.senderAgentAddress = r0[i];
                    this.senderAgentAddressLength = iArr3[i];
                }
                if (iArr4[i] == 66 && r02[i] != 0 && r02[i].length > 0) {
                    this.senderAgentProtocol = r02[i][0] ? (byte) 1 : (byte) 0;
                }
                if (iArr4[i] == 69 && r02[i] != 0 && r02[i].length > 0) {
                    this.originalMsgId = r02[i];
                }
            }
        }
        if (this.connection.getTTCVersion() >= 3) {
            int iUnmarshalSWORD5 = this.mar.unmarshalSWORD();
            if (iUnmarshalSWORD5 > 0) {
                if (iUnmarshalSWORD5 > 1) {
                    throw new SQLException("Unexpected user properties length " + iUnmarshalSWORD5);
                }
                T4Ctoh t4Ctoh = new T4Ctoh(this.connection);
                t4Ctoh.unmarshal(this.mar);
                this.aqmuprBytes = new byte[t4Ctoh.imageLength];
                int[] iArr5 = new int[1];
                this.mar.unmarshalCLR(this.aqmuprBytes, 0, iArr5, this.aqmuprBytes.length);
                this.aqmuprBytesLength = iArr5[0];
            }
            this.aqmcsn = (int) this.mar.unmarshalUB4();
            this.aqmdsn = (int) this.mar.unmarshalUB4();
            if (this.connection.getTTCVersion() >= 4) {
                this.aqmflg = (int) this.mar.unmarshalUB4();
            }
        }
        if (this.connection.getTTCVersion() >= 16) {
            this.aqmshardNum = (int) this.mar.unmarshalUB4();
        }
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "reveive", "aqmpri={0}, aqmdel={1}, aqmexp={2}, aqmatt={3}, aqmsta={4}, aqmcsn={5}, aqmdsn={6}", (String) null, (Throwable) null, Integer.valueOf(this.aqmpri), Integer.valueOf(this.aqmdel), Integer.valueOf(this.aqmexp), Integer.valueOf(this.aqmatt), Integer.valueOf(this.aqmsta), Integer.valueOf(this.aqmcsn), Integer.valueOf(this.aqmdsn));
    }
}
