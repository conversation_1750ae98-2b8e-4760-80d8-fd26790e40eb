package oracle.jdbc.datasource;

import java.sql.SQLException;
import java.util.concurrent.ExecutorService;
import javax.sql.DataSource;
import oracle.jdbc.OracleConnectionBuilder;
import oracle.jdbc.driver.OracleDriver;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/OracleDataSource.class */
public interface OracleDataSource extends DataSource, OracleCommonDataSource {
    OracleConnectionBuilder createConnectionBuilder() throws SQLException;

    static void setExecutorService(ExecutorService threadPool) throws SQLException {
        OracleDriver.setExecutorService(threadPool);
    }
}
