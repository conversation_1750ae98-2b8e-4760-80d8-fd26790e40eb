package oracle.jdbc.driver.json.binary;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.SQLException;
import java.sql.Wrapper;
import java.time.Duration;
import java.time.Period;
import java.util.Map;
import javax.json.JsonException;
import oracle.jdbc.driver.json.JakartaGeneratorWrapper;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.JsonpGeneratorWrapper;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.sql.json.OracleJsonArray;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonDate;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonDouble;
import oracle.sql.json.OracleJsonFloat;
import oracle.sql.json.OracleJsonGenerator;
import oracle.sql.json.OracleJsonIntervalDS;
import oracle.sql.json.OracleJsonIntervalYM;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonParser;
import oracle.sql.json.OracleJsonString;
import oracle.sql.json.OracleJsonTimestamp;
import oracle.sql.json.OracleJsonTimestampTZ;
import oracle.sql.json.OracleJsonValue;
import oracle.sql.json.OracleJsonVector;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/AbstractGenerator.class */
public abstract class AbstractGenerator implements OracleJsonGenerator {
    @Override // oracle.sql.json.OracleJsonGenerator
    public abstract OracleJsonGenerator writeKey(String str);

    protected abstract OracleJsonGenerator writeBinary(OracleJsonBinary oracleJsonBinary);

    protected abstract OracleJsonGenerator writeDouble(OracleJsonDouble oracleJsonDouble);

    protected abstract OracleJsonGenerator writeFloat(OracleJsonFloat oracleJsonFloat);

    protected abstract OracleJsonGenerator writeOraNumber(OracleJsonDecimal oracleJsonDecimal);

    protected abstract OracleJsonGenerator writeTimestamp(OracleJsonTimestamp oracleJsonTimestamp);

    protected abstract OracleJsonGenerator writeTimestampTZ(OracleJsonTimestampTZ oracleJsonTimestampTZ);

    protected abstract OracleJsonGenerator writeDate(OracleJsonDate oracleJsonDate);

    protected abstract OracleJsonGenerator writeString(OracleJsonString oracleJsonString);

    protected abstract OracleJsonGenerator writeIntervalDS(OracleJsonIntervalDS oracleJsonIntervalDS);

    protected abstract OracleJsonGenerator writeIntervalYM(OracleJsonIntervalYM oracleJsonIntervalYM);

    protected abstract OracleJsonGenerator writeVector(OracleJsonVector oracleJsonVector);

    protected abstract void writeStringFromParser(OracleJsonParser oracleJsonParser);

    protected abstract void writeDecimalFromParser(OracleJsonParser oracleJsonParser);

    AbstractGenerator() {
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator writeStartObject(String key) {
        writeKey(key);
        writeStartObject();
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator writeStartArray(String key) {
        writeKey(key);
        return writeStartArray();
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, OracleJsonValue value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, String value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, BigInteger value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, BigDecimal value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, int value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, long value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, double value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, float value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator write(String key, boolean value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public final OracleJsonGenerator writeNull(String key) {
        writeKey(key);
        return writeNull();
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String key, Period value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String key, Duration value) {
        writeKey(key);
        return write(value);
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(OracleJsonValue value) {
        writeOracleJsonValue(value);
        return this;
    }

    private void writeOracleJsonValue(OracleJsonValue value) {
        switch (value.getOracleJsonType()) {
            case OBJECT:
                OracleJsonObject obj = (OracleJsonObject) value;
                writeStartObject();
                for (Map.Entry<String, OracleJsonValue> entry : obj.entrySet()) {
                    writeKey(entry.getKey());
                    writeOracleJsonValue(entry.getValue());
                }
                writeEnd();
                return;
            case ARRAY:
                OracleJsonArray arr = (OracleJsonArray) value;
                writeStartArray();
                for (OracleJsonValue v : arr) {
                    writeOracleJsonValue(v);
                }
                writeEnd();
                return;
            case BINARY:
                writeBinary((OracleJsonBinary) value);
                return;
            case FLOAT:
                writeFloat((OracleJsonFloat) value);
                return;
            case DOUBLE:
                writeDouble((OracleJsonDouble) value);
                return;
            case DECIMAL:
                writeOraNumber((OracleJsonDecimal) value);
                return;
            case STRING:
                writeString((OracleJsonString) value);
                return;
            case TIMESTAMP:
                writeTimestamp((OracleJsonTimestamp) value);
                return;
            case TIMESTAMPTZ:
                writeTimestampTZ((OracleJsonTimestampTZ) value);
                return;
            case DATE:
                writeDate((OracleJsonDate) value);
                return;
            case INTERVALDS:
                writeIntervalDS((OracleJsonIntervalDS) value);
                return;
            case INTERVALYM:
                writeIntervalYM((OracleJsonIntervalYM) value);
                return;
            case TRUE:
                write(true);
                return;
            case FALSE:
                write(false);
                return;
            case NULL:
                writeNull();
                return;
            case VECTOR:
                writeVector((OracleJsonVector) value);
                return;
            default:
                throw new UnsupportedOperationException();
        }
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeParser(Object o) throws JsonException, jakarta.json.JsonException {
        try {
            if (o instanceof OracleJsonParser) {
                writeOracleJsonParser((OracleJsonParser) o);
            } else if ((o instanceof Wrapper) && ((Wrapper) o).isWrapperFor(OracleJsonParser.class)) {
                writeOracleJsonParser((OracleJsonParser) ((Wrapper) o).unwrap(OracleJsonParser.class));
            } else if (isInstance(o, Jsonp.JAKARTA_JSON_PARSER)) {
                ((JakartaGeneratorWrapper) wrap(JakartaGeneratorWrapper.class)).writeJsonParser(o);
            } else if (isInstance(o, Jsonp.JAVAX_JSON_PARSER)) {
                ((JsonpGeneratorWrapper) wrap(JsonpGeneratorWrapper.class)).writeJsonParser(o);
            } else {
                throw new IllegalArgumentException();
            }
            return this;
        } catch (SQLException e) {
            throw new IllegalArgumentException(e);
        }
    }

    private boolean isInstance(Object o, Class<?> c) {
        return c != null && c.isInstance(o);
    }

    private void writeOracleJsonParser(OracleJsonParser parser) {
        while (parser.hasNext()) {
            switch (parser.next()) {
                case START_OBJECT:
                    writeStartObject();
                    break;
                case START_ARRAY:
                    writeStartArray();
                    break;
                case END_ARRAY:
                case END_OBJECT:
                    writeEnd();
                    break;
                case KEY_NAME:
                    writeKey(parser.getString());
                    break;
                case VALUE_STRING:
                    writeStringFromParser(parser);
                    break;
                case VALUE_TRUE:
                    write(true);
                    break;
                case VALUE_FALSE:
                    write(false);
                    break;
                case VALUE_NULL:
                    writeNull();
                    break;
                case VALUE_BINARY:
                    writeBinary(parser.getValue().asJsonBinary());
                    break;
                case VALUE_DATE:
                    writeDate(parser.getValue().asJsonDate());
                    break;
                case VALUE_DECIMAL:
                    writeDecimalFromParser(parser);
                    break;
                case VALUE_DOUBLE:
                    write(parser.getDouble());
                    break;
                case VALUE_FLOAT:
                    write(parser.getFloat());
                    break;
                case VALUE_INTERVALDS:
                    writeIntervalDS(parser.getValue().asJsonIntervalDS());
                    break;
                case VALUE_INTERVALYM:
                    writeIntervalYM(parser.getValue().asJsonIntervalYM());
                    break;
                case VALUE_TIMESTAMP:
                    writeTimestamp(parser.getValue().asJsonTimestamp());
                    break;
                case VALUE_TIMESTAMPTZ:
                    writeTimestampTZ(parser.getValue().asJsonTimestampTZ());
                    break;
                case VALUE_VECTOR:
                    writeVector(parser.getValue().asJsonVector());
                    break;
                default:
                    throw new IllegalStateException();
            }
        }
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public <T> T wrap(Class<T> wrapper) {
        try {
            if (Jsonp.hasJakarta() && (Jsonp.isJakartaJsonStream(wrapper) || wrapper == JakartaGeneratorWrapper.class)) {
                return wrapper.cast(new JakartaGeneratorWrapper(this));
            }
            return wrapper.cast(new JsonpGeneratorWrapper(this));
        } catch (ClassCastException e) {
            throw OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, e, wrapper.getName());
        }
    }
}
