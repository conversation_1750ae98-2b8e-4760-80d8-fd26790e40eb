package oracle.jdbc.aq;

import java.sql.SQLException;
import java.util.Arrays;
import oracle.jdbc.aq.AQDequeueOptions;

/* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQEnqueueOptions.class */
public class AQEnqueueOptions {
    private byte[] attrRelativeMessageId = null;
    private SequenceDeviationOption attrSequenceDeviation = SequenceDeviationOption.BOTTOM;
    private VisibilityOption attrVisibility = VisibilityOption.ON_COMMIT;
    private DeliveryMode attrDeliveryMode = DeliveryMode.PERSISTENT;
    private boolean retrieveMsgId = false;
    private String transformation;

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQEnqueueOptions$VisibilityOption.class */
    public enum VisibilityOption {
        ON_COMMIT(2),
        IMMEDIATE(1);

        private final int mode;

        VisibilityOption(int _mode) {
            this.mode = _mode;
        }

        public final int getCode() {
            return this.mode;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQEnqueueOptions$SequenceDeviationOption.class */
    public enum SequenceDeviationOption {
        BOTTOM(0),
        BEFORE(2),
        TOP(3);

        private final int mode;

        SequenceDeviationOption(int _mode) {
            this.mode = _mode;
        }

        public final int getCode() {
            return this.mode;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQEnqueueOptions$DeliveryMode.class */
    public enum DeliveryMode {
        PERSISTENT(AQDequeueOptions.DeliveryFilter.PERSISTENT.getCode()),
        BUFFERED(AQDequeueOptions.DeliveryFilter.BUFFERED.getCode());

        private final int mode;

        DeliveryMode(int _mode) {
            this.mode = _mode;
        }

        public final int getCode() {
            return this.mode;
        }
    }

    public void setRelativeMessageId(byte[] relativeMessageId) throws SQLException {
        this.attrRelativeMessageId = relativeMessageId;
    }

    public byte[] getRelativeMessageId() {
        return this.attrRelativeMessageId;
    }

    public void setSequenceDeviation(SequenceDeviationOption sequenceDeviation) throws SQLException {
        this.attrSequenceDeviation = sequenceDeviation;
    }

    public SequenceDeviationOption getSequenceDeviation() {
        return this.attrSequenceDeviation;
    }

    public void setVisibility(VisibilityOption visibility) throws SQLException {
        this.attrVisibility = visibility;
    }

    public VisibilityOption getVisibility() {
        return this.attrVisibility;
    }

    public void setDeliveryMode(DeliveryMode delivery) throws SQLException {
        this.attrDeliveryMode = delivery;
    }

    public DeliveryMode getDeliveryMode() {
        return this.attrDeliveryMode;
    }

    public void setRetrieveMessageId(boolean retrieveIt) {
        this.retrieveMsgId = retrieveIt;
    }

    public boolean getRetrieveMessageId() {
        return this.retrieveMsgId;
    }

    public void setTransformation(String _transformation) {
        this.transformation = _transformation;
    }

    public String getTransformation() {
        return this.transformation;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("AQEnqueueOptions {");
        sb.append("attrRelativeMessageId=" + Arrays.toString(this.attrRelativeMessageId) + ", ");
        sb.append("attrSequenceDeviation=" + this.attrSequenceDeviation + ", ");
        sb.append("attrVisibility=" + this.attrVisibility + ", ");
        sb.append("attrDeliveryMode=" + this.attrDeliveryMode + ", ");
        sb.append("attrDeliveryMode=" + this.attrDeliveryMode + ", ");
        sb.append("retrieveMsgId=" + this.retrieveMsgId + ", ");
        sb.append("transformation=" + this.transformation + "}");
        return sb.toString();
    }
}
