package oracle.jdbc.driver;

import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import oracle.jdbc.aq.AQMessageProperties;
import oracle.jdbc.internal.JMSDequeueOptions;
import oracle.jdbc.internal.JMSMessageProperties;
import oracle.net.ns.SQLnetDef;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoaqdeq.class */
class T4CTTIoaqdeq extends T4CTTIfun {
    static final int AQTTC_DEQ_OPTS_DEFAULT = 0;
    static final int AQTTC_DEQ_OPTS_ON_COMMIT = 1;
    static final int AQTTC_DEQ_OPTS_IMMEDIATE = 2;
    static final int AQTTC_DEQ_OPTS_FOREVER = 4;
    static final int AQTTC_DEQ_OPTS_BROWSE = 8;
    static final int AQTTC_DEQ_OPTS_LOCKED = 16;
    static final int AQTTC_DEQ_OPTS_REMOVE = 32;
    static final int AQTTC_DEQ_OPTS_REMOVE_NO_DATA = 64;
    static final int AQTTC_DEQ_OPTS_BUFFERED = 128;
    static final int AQTTC_DEQ_OPT_PERS_BUFF = 256;
    static final int AQTCC_DEQ_STREAMING_ENABLED = 1;
    static final int AQTTC_DEQ_BYSHRD_FLG = 16;
    static final int AQDEQVER_DEFAULT = 1;
    static final int AQDEQVER_12_2 = 2;
    static final int AQDEQVER_12_1 = 1;
    private T4Ctoh toh;
    private T4CTTIaqm aqm;
    private T4CTTIaqjms aqjms;
    private byte[] payloadToid;
    private byte[] payload;
    private int aqdeqver;
    private byte[] dequeuedMessageId;
    private byte[] queueNameBytes;
    private byte[] consumerNameBytes;
    private byte[] correlationBytes;
    private byte[] conditionBytes;
    private byte[] jmsPropertyBytes;
    private boolean hasAMessageBeenDequeued;
    private String queueName;
    private JMSDequeueOptions dequeueOptions;
    private AQMessagePropertiesI aqMessageProperties;
    private JMSMessagePropertiesI jmsMessageProperties;
    private int reqShardNum;
    private OutputStream streamPayload;
    private boolean isStreamMode;

    T4CTTIoaqdeq(T4CConnection _connection) {
        super(_connection, (byte) 3);
        this.toh = null;
        this.aqm = null;
        this.aqjms = null;
        this.payloadToid = null;
        this.payload = null;
        this.aqdeqver = 0;
        this.dequeuedMessageId = null;
        this.queueNameBytes = null;
        this.consumerNameBytes = null;
        this.correlationBytes = null;
        this.conditionBytes = null;
        this.jmsPropertyBytes = null;
        this.hasAMessageBeenDequeued = false;
        this.dequeueOptions = null;
        this.aqMessageProperties = null;
        this.jmsMessageProperties = null;
        this.reqShardNum = -1;
        this.streamPayload = null;
        this.isStreamMode = false;
        setFunCode((short) 185);
        this.toh = new T4Ctoh(_connection);
        this.aqm = new T4CTTIaqm(this.connection, this.toh);
    }

    void doJMSDeq(String _queueName, JMSDequeueOptions _dequeueOptions, byte[] _payloadToid, AQMessagePropertiesI aqProperties, JMSMessagePropertiesI jmsProperties, OutputStream os) throws SQLException, IOException {
        this.streamPayload = os;
        setStreamMode(true);
        doJMSDeqRPC(_queueName, _dequeueOptions, _payloadToid, aqProperties, jmsProperties);
    }

    private void setStreamMode(boolean flag) {
        this.isStreamMode = flag;
    }

    void doJMSDeq(String _queueName, JMSDequeueOptions _dequeueOptions, byte[] _payloadToid, AQMessagePropertiesI aqProperties, JMSMessagePropertiesI jmsProperties) throws SQLException, IOException {
        setStreamMode(false);
        this.streamPayload = null;
        doJMSDeqRPC(_queueName, _dequeueOptions, _payloadToid, aqProperties, jmsProperties);
    }

    private void doJMSDeqRPC(String _queueName, JMSDequeueOptions _dequeueOptions, byte[] _payloadToid, AQMessagePropertiesI aqProperties, JMSMessagePropertiesI jmsProperties) throws SQLException, IOException {
        this.queueName = _queueName;
        this.dequeueOptions = _dequeueOptions;
        this.payloadToid = _payloadToid;
        this.aqMessageProperties = aqProperties;
        this.jmsMessageProperties = jmsProperties;
        if (this.queueName != null && this.queueName.length() != 0) {
            this.queueNameBytes = this.meg.conv.StringToCharBytes(this.queueName);
        } else {
            this.queueNameBytes = null;
        }
        String consumerNameStr = this.dequeueOptions.getConsumerName();
        if (consumerNameStr != null && consumerNameStr.length() > 0) {
            this.consumerNameBytes = this.meg.conv.StringToCharBytes(consumerNameStr);
        } else {
            this.consumerNameBytes = null;
        }
        String correlation = this.dequeueOptions.getCorrelation();
        if (correlation != null && correlation.length() != 0) {
            this.correlationBytes = this.meg.conv.StringToCharBytes(correlation);
        } else {
            this.correlationBytes = null;
        }
        String condition = this.dequeueOptions.getCondition();
        if (condition != null && condition.length() > 0) {
            this.conditionBytes = this.meg.conv.StringToCharBytes(condition);
        } else {
            this.conditionBytes = null;
        }
        this.hasAMessageBeenDequeued = false;
        this.dequeuedMessageId = null;
        this.payload = null;
        try {
            if (this.connection.getVersionNumber() >= 12200 && TypeDescriptor.isV2available(_payloadToid)) {
                this.aqdeqver = 2;
            } else {
                this.aqdeqver = 1;
            }
        } catch (Exception e) {
            this.aqdeqver = 1;
        }
        this.reqShardNum = this.dequeueOptions.getShardNum();
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        int aqdeqoptflg;
        int streamingMode;
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.queueNameBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (this.consumerNameBytes != null && this.consumerNameBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.consumerNameBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        byte[] mesgId = this.dequeueOptions.getDequeueMessageId();
        boolean sendMsgId = false;
        if (mesgId != null && mesgId.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(mesgId.length);
            sendMsgId = true;
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (this.correlationBytes != null && this.correlationBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.correlationBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (this.connection.autocommit) {
            aqdeqoptflg = 2;
        } else {
            aqdeqoptflg = 1;
        }
        int aqdeqoptflg2 = aqdeqoptflg | this.dequeueOptions.getDeliveryMode().getCode() | this.dequeueOptions.getDequeueMode().getCode() | this.dequeueOptions.getVisibility().getCode();
        if (this.dequeueOptions.getWait() == 4) {
            this.meg.marshalUB4(aqdeqoptflg2 | 4);
            this.meg.marshalSB4(-1);
        } else {
            this.meg.marshalUB4(aqdeqoptflg2);
            this.meg.marshalSB4(this.dequeueOptions.getWait());
        }
        if (this.conditionBytes != null && this.conditionBytes.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.conditionBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (this.payloadToid != null && this.payloadToid.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.payloadToid.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalUB2(this.aqdeqver);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalNULLPTR();
        if (this.dequeueOptions.isRetrieveMessageId()) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(16);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (isStreamEnabled()) {
            streamingMode = 1;
        } else {
            streamingMode = this.dequeueOptions.getStreamingMode().getCode();
        }
        if (this.reqShardNum != -1) {
            streamingMode |= 16;
        }
        this.meg.marshalUB4(streamingMode);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        if (this.connection.getTTCVersion() >= 14) {
            this.meg.marshalNULLPTR();
        }
        if (this.connection.getTTCVersion() >= 16) {
            if (this.reqShardNum != -1) {
                this.meg.marshalUB4(this.reqShardNum);
            } else {
                this.meg.marshalUB4(SQLnetDef.NSPDDLSLMAX);
            }
        }
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalCHR(this.queueNameBytes);
        }
        if (this.consumerNameBytes != null && this.consumerNameBytes.length != 0) {
            this.meg.marshalCHR(this.consumerNameBytes);
        }
        if (sendMsgId) {
            this.meg.marshalB1Array(mesgId);
        }
        if (this.correlationBytes != null && this.correlationBytes.length != 0) {
            this.meg.marshalCHR(this.correlationBytes);
        }
        if (this.conditionBytes != null && this.conditionBytes.length > 0) {
            this.meg.marshalCHR(this.conditionBytes);
        }
        this.meg.marshalB1Array(this.payloadToid);
    }

    private boolean isStreamEnabled() {
        return this.isStreamMode;
    }

    private void processStreamPayload() throws SQLException, IOException {
        byte ociCode;
        setStreamMode(false);
        Byte OCI_LAST_CHUNK = (byte) 3;
        Byte OCI_ONE_PIECE = (byte) 0;
        do {
            ociCode = (byte) this.meg.unmarshalUB1();
            if (ociCode == OCI_LAST_CHUNK.byteValue()) {
                break;
            }
            long payloadLen = this.meg.unmarshalSB8();
            if (payloadLen > 0) {
                byte[] payload = new byte[(int) payloadLen];
                this.meg.unmarshalBuffer(payload, 0, payload.length);
                this.streamPayload.write(payload);
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
            }
        } while (ociCode != OCI_ONE_PIECE.byteValue());
        this.streamPayload.close();
        this.streamPayload = null;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.hasAMessageBeenDequeued = true;
        int msgPropLength = (int) this.meg.unmarshalUB4();
        if (msgPropLength > 0) {
            this.aqm.initToDefaultValues();
            this.aqm.receive();
            this.aqMessageProperties.setPriority(this.aqm.aqmpri);
            this.aqMessageProperties.setDelay(this.aqm.aqmdel);
            this.aqMessageProperties.setExpiration(this.aqm.aqmexp);
            this.aqMessageProperties.setShardNum(this.aqm.aqmshardNum);
            if (this.aqm.aqmcorBytes != null) {
                String aqmcor = this.meg.conv.CharBytesToString(this.aqm.aqmcorBytes, this.aqm.aqmcorBytesLength, true);
                this.aqMessageProperties.setCorrelation(aqmcor);
            }
            this.aqMessageProperties.setAttempts(this.aqm.aqmatt);
            if (this.aqm.aqmeqnBytes != null) {
                String aqmeqn = this.meg.conv.CharBytesToString(this.aqm.aqmeqnBytes, this.aqm.aqmeqnBytesLength, true);
                this.aqMessageProperties.setExceptionQueue(aqmeqn);
            }
            this.aqMessageProperties.setMessageState(AQMessageProperties.MessageState.getMessageState(this.aqm.aqmsta));
            if (this.aqm.aqmeqt != null) {
                this.aqMessageProperties.setEnqueueTime(this.aqm.aqmeqt.timestampValue());
            }
            AQAgentI senderAgent = new AQAgentI();
            if (this.aqm.senderAgentName != null) {
                senderAgent.setName(this.meg.conv.CharBytesToString(this.aqm.senderAgentName, this.aqm.senderAgentNameLength, true));
            }
            if (this.aqm.senderAgentAddress != null) {
                senderAgent.setAddress(this.meg.conv.CharBytesToString(this.aqm.senderAgentAddress, this.aqm.senderAgentAddressLength, true));
            }
            senderAgent.setProtocol(this.aqm.senderAgentProtocol);
            this.aqMessageProperties.setSender(senderAgent);
            this.aqMessageProperties.setPreviousQueueMessageId(this.aqm.originalMsgId);
            this.aqMessageProperties.setDeliveryMode(AQMessageProperties.DeliveryMode.getDeliveryMode(this.aqm.aqmflg));
            if (this.aqm.aqmetiBytes != null) {
                String aqmeti = this.meg.conv.CharBytesToString(this.aqm.aqmetiBytes, this.aqm.aqmetiBytes.length, true);
                this.aqMessageProperties.setTransactionGroup(aqmeti);
            }
        }
        if (this.dequeueOptions.isRetrieveMessageId()) {
            byte[] aqdeqmsi = new byte[16];
            this.meg.unmarshalBuffer(aqdeqmsi, 0, 16);
            this.dequeuedMessageId = aqdeqmsi;
        }
        int payloadLen = (int) this.meg.unmarshalUB4();
        if (payloadLen > 0) {
            this.payload = new byte[payloadLen];
            this.meg.unmarshalBuffer(this.payload, 0, this.payload.length);
        }
        int jmsPropLen = (int) this.meg.unmarshalUB4();
        if (jmsPropLen > 0) {
            this.aqjms = new T4CTTIaqjms(this.connection);
            this.aqjms.receive();
            if (this.aqjms.aqjmshdrprop != null && this.aqjms.aqjmshdrprop.length > 0) {
                String headerPropertiesString = this.meg.conv.CharBytesToString(this.aqjms.aqjmshdrprop, this.aqjms.aqjmshdrprop.length, true);
                this.jmsMessageProperties.setHeaderProperties(headerPropertiesString);
            }
            if (this.aqjms.aqjmsuserprop != null && this.aqjms.aqjmsuserprop.length > 0) {
                String userPropertiesString = this.meg.conv.CharBytesToString(this.aqjms.aqjmsuserprop, this.aqjms.aqjmsuserprop.length, true);
                this.jmsMessageProperties.setUserProperties(userPropertiesString);
            }
            this.jmsMessageProperties.setJMSMessageType(JMSMessageProperties.JMSMessageType.getJMSMessageType(this.aqjms.aqjmsflags));
        }
    }

    boolean isHasAMessageBeenDequeued() {
        return this.hasAMessageBeenDequeued;
    }

    byte[] getPayload() {
        return this.payload;
    }

    byte[] getDequeuedMessageId() {
        return this.dequeuedMessageId;
    }

    AQMessagePropertiesI getAQMessageProperties() {
        return this.aqMessageProperties;
    }

    public JMSMessagePropertiesI getJMSMessageProperties() {
        return this.jmsMessageProperties;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readOAC() throws SQLException, IOException {
        if (isStreamEnabled()) {
            processStreamPayload();
        }
    }
}
