package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.NUMBER;
import oracle.sql.RAW;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CCharAccessor.class */
class T4CCharAccessor extends CharAccessor implements T4CAccessor {
    T4CMAREngine mare;
    boolean underlyingLong;
    private T4CMarshaller marshaller;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CCharAccessor.class.desiredAssertionStatus();
    }

    T4CCharAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, form, external_type, isOutBind, false);
        this.underlyingLong = false;
        this.marshaller = null;
        this.mare = _mare;
        calculateSizeTmpByteArray();
    }

    T4CCharAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int maxCodePointLen, int _oacmxl, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, nullable, flags, precision, scale, contflag, total_elems, form, maxCodePointLen);
        this.underlyingLong = false;
        this.marshaller = null;
        this.mare = _mare;
        this.definedColumnType = _definedColumnType;
        this.definedColumnSize = _definedColumnSize;
        calculateSizeTmpByteArray();
        this.oacmxl = _oacmxl;
        if (this.oacmxl == -1) {
            this.underlyingLong = true;
            this.oacmxl = 4000;
        }
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        return getMarshaller().unmarshalOneRow(this);
    }

    int readStreamFromWire(byte[] buffer, int offset, int length, int[] escapeSequenceArr, boolean[] readHeaderArr, boolean[] readAsNonStreamArr, T4CMAREngine mare, T4CTTIoer11 oer) throws SQLException, IOException {
        return getMarshaller().readStreamFromWire(buffer, offset, length, escapeSequenceArr, readHeaderArr, readAsNonStreamArr, mare, oer);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    NUMBER getNUMBER(int currentRow) throws SQLException {
        NUMBER result = null;
        if (this.definedColumnType == 0) {
            result = super.getNUMBER(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                return T4CVarcharAccessor.StringToNUMBER(s.trim());
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    DATE getDATE(int currentRow) throws SQLException {
        DATE result = null;
        if (this.definedColumnType == 0) {
            result = super.getDATE(currentRow);
        } else {
            Date d = getDate(currentRow);
            if (d != null) {
                result = new DATE(d);
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMP getTIMESTAMP(int currentRow) throws SQLException {
        TIMESTAMP result = null;
        if (this.definedColumnType == 0) {
            result = super.getTIMESTAMP(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                Calendar cal = T4CVarcharAccessor.DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTMPFM"), nanos);
                Timestamp ts = new Timestamp(cal.getTimeInMillis());
                ts.setNanos(nanos[0]);
                result = new TIMESTAMP(ts);
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMPTZ getTIMESTAMPTZ(int currentRow) throws SQLException {
        TIMESTAMPTZ result = null;
        if (this.definedColumnType == 0) {
            result = super.getTIMESTAMPTZ(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                Calendar cal = T4CVarcharAccessor.DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTZNFM"), nanos);
                Timestamp ts = new Timestamp(cal.getTimeInMillis());
                ts.setNanos(nanos[0]);
                result = new TIMESTAMPTZ(this.statement.connection, ts, cal);
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMPLTZ getTIMESTAMPLTZ(int currentRow) throws SQLException {
        TIMESTAMPLTZ result = null;
        if (this.definedColumnType == 0) {
            result = super.getTIMESTAMPLTZ(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                Calendar cal = T4CVarcharAccessor.DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTZNFM"), nanos);
                Timestamp ts = new Timestamp(cal.getTimeInMillis());
                ts.setNanos(nanos[0]);
                result = new TIMESTAMPLTZ(this.statement.connection, ts, cal);
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    RAW getRAW(int currentRow) throws SQLException {
        RAW result = null;
        if (this.definedColumnType == 0) {
            result = super.getRAW(currentRow);
        } else if (!this.rowNull[currentRow]) {
            if (this.definedColumnType == -2 || this.definedColumnType == -3 || this.definedColumnType == -4) {
                result = new RAW(getBytesFromHexChars(currentRow));
            } else {
                result = new RAW(super.getBytes(currentRow));
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Datum getOracleObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getOracleObject(currentRow);
        }
        if (this.rowNull == null) {
            throw ((SQLException) DatabaseError.createSqlException(21).fillInStackTrace());
        }
        if (this.rowNull[currentRow]) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.TIMESTAMPLTZ /* -102 */:
                return getTIMESTAMPLTZ(currentRow);
            case oracle.jdbc.OracleTypes.TIMESTAMPTZ /* -101 */:
                return getTIMESTAMPTZ(currentRow);
            case oracle.jdbc.OracleTypes.LONGNVARCHAR /* -16 */:
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case -1:
            case 1:
            case 12:
                return super.getOracleObject(currentRow);
            case oracle.jdbc.OracleTypes.ROWID /* -8 */:
                return getROWID(currentRow);
            case oracle.jdbc.OracleTypes.BIT /* -7 */:
            case oracle.jdbc.OracleTypes.TINYINT /* -6 */:
            case -5:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 16:
                return getNUMBER(currentRow);
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
            case -3:
            case -2:
                return getRAW(currentRow);
            case 91:
                return getDATE(currentRow);
            case 92:
                return getDATE(currentRow);
            case 93:
                return getTIMESTAMP(currentRow);
            default:
                throw ((SQLException) DatabaseError.createSqlException(4).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    byte getByte(int currentRow) throws SQLException {
        byte result = 0;
        if (this.definedColumnType == 0) {
            result = super.getByte(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.byteValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    int getInt(int currentRow) throws SQLException {
        int result = 0;
        if (this.definedColumnType == 0) {
            result = super.getInt(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.intValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    short getShort(int currentRow) throws SQLException {
        short result = 0;
        if (this.definedColumnType == 0) {
            result = super.getShort(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.shortValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    long getLong(int currentRow) throws SQLException {
        long result = 0;
        if (this.definedColumnType == 0) {
            result = super.getLong(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.longValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    float getFloat(int currentRow) throws SQLException {
        float result = 0.0f;
        if (this.definedColumnType == 0) {
            result = super.getFloat(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.floatValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    double getDouble(int currentRow) throws SQLException {
        double result = 0.0d;
        if (this.definedColumnType == 0) {
            result = super.getDouble(currentRow);
        } else {
            NUMBER tmp = getNUMBER(currentRow);
            if (tmp != null) {
                result = tmp.doubleValue();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Date getDate(int currentRow) throws SQLException {
        Date result = null;
        if (this.definedColumnType == 0) {
            result = super.getDate(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                try {
                    result = new Date(T4CVarcharAccessor.DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCDATEFM"), nanos).getTimeInMillis());
                } catch (NumberFormatException ex) {
                    throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 132, (Object) null, ex).fillInStackTrace());
                }
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Timestamp getTimestamp(int currentRow) throws SQLException {
        Timestamp result = null;
        if (this.definedColumnType == 0) {
            result = super.getTimestamp(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                try {
                    Calendar cal = T4CVarcharAccessor.DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTMPFM"), nanos);
                    result = new Timestamp(cal.getTimeInMillis());
                    result.setNanos(nanos[0]);
                } catch (NumberFormatException ex) {
                    throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 132, (Object) null, ex).fillInStackTrace());
                }
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Time getTime(int currentRow) throws SQLException {
        Time result = null;
        if (this.definedColumnType == 0) {
            result = super.getTime(currentRow);
        } else {
            String s = getString(currentRow);
            if (s != null) {
                int[] nanos = new int[1];
                try {
                    Calendar cal = T4CVarcharAccessor.DATEStringToCalendar(s, (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCSTZNFM"), nanos);
                    result = new Time(cal.getTimeInMillis());
                } catch (NumberFormatException ex) {
                    throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 132, (Object) null, ex).fillInStackTrace());
                }
            }
        }
        return result;
    }

    private final T4CMarshaller getMarshaller() {
        if (this.marshaller == null) {
            this.marshaller = this.describeType == 8 ? T4CMarshaller.LONG : T4CMarshaller.CHAR;
        }
        return this.marshaller;
    }

    @Override // oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getObject(currentRow);
        }
        if (isUnexpected()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 21).fillInStackTrace());
        }
        if (isNull(currentRow)) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.LONGNVARCHAR /* -16 */:
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case -1:
            case 1:
            case 12:
                return getString(currentRow);
            case oracle.jdbc.OracleTypes.BIT /* -7 */:
            case 16:
                return Boolean.valueOf(getBoolean(currentRow));
            case oracle.jdbc.OracleTypes.TINYINT /* -6 */:
                return Byte.valueOf(getByte(currentRow));
            case -5:
                return Long.valueOf(getLong(currentRow));
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
            case -3:
            case -2:
                return getBytesFromHexChars(currentRow);
            case 2:
            case 3:
                return getBigDecimal(currentRow);
            case 4:
                return Integer.valueOf(getInt(currentRow));
            case 5:
                return Short.valueOf(getShort(currentRow));
            case 6:
            case 8:
                return Double.valueOf(getDouble(currentRow));
            case 7:
                return Float.valueOf(getFloat(currentRow));
            case 91:
                return getDate(currentRow);
            case 92:
                return getTime(currentRow);
            case 93:
                return getTimestamp(currentRow);
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new AccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CCharAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CCharAccessor(stmt, T4CCharAccessor.this.describeMaxLength, T4CCharAccessor.this.nullable, -1, T4CCharAccessor.this.precision, T4CCharAccessor.this.scale, T4CCharAccessor.this.contflag, -1, T4CCharAccessor.this.formOfUse, T4CCharAccessor.this.describeMaxLengthChars, T4CCharAccessor.this.oacmxl, T4CCharAccessor.this.definedColumnType, T4CCharAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
