package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIosaga.class */
final class T4CTTIosaga extends T4CTTIfun {
    T4CTTIaqm aqm;
    T4Ctoh toh;
    private String sender;
    private byte[] senderBytes;
    private String recipient;
    private byte[] recipientBytes;
    private String coordinator;
    private byte[] coordinatorBytes;
    private String currentUser;
    private byte[] currentUserBytes;
    private String spareText;
    private byte[] spareTextBytes;
    private int opcode;
    private int flags;
    private int timeout;
    private int sagaVersion;
    private byte[] sagaId;
    private int spareNumeric;
    private int version;
    private byte[] response;

    T4CTTIosaga(T4CConnection _connection) {
        super(_connection, (byte) 3);
        this.senderBytes = null;
        this.recipientBytes = null;
        this.coordinatorBytes = null;
        this.currentUserBytes = null;
        this.spareTextBytes = null;
        this.flags = 0;
        this.timeout = 86400;
        this.sagaVersion = 1;
        this.spareNumeric = 0;
        this.version = 1;
        this.response = null;
        setFunCode((short) 195);
        this.toh = new T4Ctoh(_connection);
        this.aqm = new T4CTTIaqm(this.connection, this.toh);
    }

    public byte[] doOSAGA(int opcode, int flags, int timeout, int sagaVersion, byte[] sagaId, String sender, String recipient, String coordinator, String currentUser, int spareNumeric, String spareText) throws SQLException, IOException {
        this.opcode = opcode;
        this.flags = flags;
        this.timeout = timeout;
        this.sagaId = sagaId;
        this.sender = sender;
        this.recipient = recipient;
        this.coordinator = coordinator;
        this.currentUser = currentUser;
        this.spareNumeric = spareNumeric;
        this.spareText = spareText;
        if (sender != null && sender.length() != 0) {
            this.senderBytes = this.meg.conv.StringToCharBytes(sender);
        } else {
            this.senderBytes = null;
        }
        if (recipient != null && recipient.length() != 0) {
            this.recipientBytes = this.meg.conv.StringToCharBytes(recipient);
        } else {
            this.recipientBytes = null;
        }
        if (coordinator != null && coordinator.length() != 0) {
            this.coordinatorBytes = this.meg.conv.StringToCharBytes(coordinator);
        } else {
            this.coordinatorBytes = null;
        }
        if (currentUser != null && currentUser.length() != 0) {
            this.currentUserBytes = this.meg.conv.StringToCharBytes(currentUser);
        } else {
            this.currentUserBytes = null;
        }
        if (spareText != null && spareText.length() != 0) {
            this.spareTextBytes = this.meg.conv.StringToCharBytes(spareText);
        } else {
            this.spareTextBytes = null;
        }
        doRPC();
        return this.response;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.opcode);
        this.meg.marshalUB4(this.flags);
        this.meg.marshalUB4(this.timeout);
        this.meg.marshalUB4(this.sagaVersion);
        boolean sendSagaId = false;
        if (this.sagaId != null && this.sagaId.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sagaId.length);
            sendSagaId = true;
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.senderBytes != null && this.senderBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.senderBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.recipientBytes != null && this.recipientBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.recipientBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.coordinatorBytes != null && this.coordinatorBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.coordinatorBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.currentUserBytes != null && this.currentUserBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.currentUserBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalUB4(this.spareNumeric);
        if (this.spareTextBytes != null && this.spareTextBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.spareTextBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB4(this.version);
        if (sendSagaId) {
            this.meg.marshalB1Array(this.sagaId);
        }
        if (this.senderBytes != null && this.senderBytes.length != 0) {
            this.meg.marshalCHR(this.senderBytes);
        }
        if (this.recipientBytes != null && this.recipientBytes.length != 0) {
            this.meg.marshalCHR(this.recipientBytes);
        }
        if (this.coordinatorBytes != null && this.coordinatorBytes.length != 0) {
            this.meg.marshalCHR(this.coordinatorBytes);
        }
        if (this.currentUserBytes != null && this.currentUserBytes.length != 0) {
            this.meg.marshalCHR(this.currentUserBytes);
        }
        if (this.spareTextBytes != null && this.spareTextBytes.length != 0) {
            this.meg.marshalCHR(this.spareTextBytes);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int returnLen = (int) this.meg.unmarshalUB4();
        if (returnLen > 0) {
            this.response = this.meg.unmarshalNBytes(returnLen);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
