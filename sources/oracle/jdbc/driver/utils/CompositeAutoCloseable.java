package oracle.jdbc.driver.utils;

import java.lang.Exception;
import java.util.ArrayList;
import java.util.List;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/CompositeAutoCloseable.class */
public class CompositeAutoCloseable<E extends Exception> implements AutoCloseableAdapter<E> {
    private final List<AutoCloseableAdapter<E>> autoCloseableAdapters = new ArrayList();
    private final Class<E> exceptionClass;

    public CompositeAutoCloseable(Class<E> exceptionClass) {
        this.exceptionClass = exceptionClass;
    }

    public void add(AutoCloseableAdapter<E> autoCloseableAdapter) {
        if (autoCloseableAdapter == null) {
            return;
        }
        this.autoCloseableAdapters.add(autoCloseableAdapter);
    }

    public <T> void addAll(T[] closeables, ThrowingConsumer<T, E> closeFunction) {
        if (closeables == null || closeFunction == null) {
            return;
        }
        this.autoCloseableAdapters.add(() -> {
            for (Object obj : closeables) {
                if (obj != null) {
                    closeFunction.acceptOrThrow(obj);
                }
            }
        });
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: E extends java.lang.Exception */
    @Override // oracle.jdbc.driver.utils.AutoCloseableAdapter, java.lang.AutoCloseable
    public void close() throws Exception {
        Exception exception = null;
        for (int i = this.autoCloseableAdapters.size() - 1; i >= 0; i--) {
            AutoCloseableAdapter<E> autoCloseableAdapter = this.autoCloseableAdapters.get(i);
            if (autoCloseableAdapter != null) {
                try {
                    autoCloseableAdapter.close();
                } catch (Exception thrown) {
                    if (exception == null) {
                        exception = thrown;
                    } else {
                        exception.addSuppressed(thrown);
                    }
                }
            }
        }
        if (exception == null) {
            return;
        }
        if (this.exceptionClass.isInstance(exception)) {
            throw this.exceptionClass.cast(exception);
        }
        throw CheckedExceptionHandler.toRuntimeException(exception);
    }
}
