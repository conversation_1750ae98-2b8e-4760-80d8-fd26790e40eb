package oracle.jdbc.driver;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.ZonedDateTime;
import java.util.Map;
import oracle.sql.Datum;
import oracle.sql.TIMESTAMP;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TimestampAccessor.class */
class TimestampAccessor extends DateTimeCommonAccessor {
    static final int MAXLENGTH = 11;

    TimestampAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        super(Representation.TIMESTAMP, stmt, 11, isStoredInBindData);
        init(stmt, 180, 180, form, isOutBind);
        initForDataAccess(external_type, max_len, null);
    }

    TimestampAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(Representation.TIMESTAMP, stmt, 11, false);
        init(stmt, 180, 180, form, false);
        initForDescribe(180, max_len, nullable, flags, precision, scale, contflag, total_elems, form, null);
        initForDataAccess(0, max_len, null);
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        getBytesInternal(currentRow, this.tmpBytes);
        int len = getLength(currentRow);
        int year = oracleYear(this.tmpBytes);
        int nanos = -1;
        if (len == 11) {
            nanos = oracleNanos(this.tmpBytes);
        }
        int hour = oracleHour(this.tmpBytes);
        String result = toText(year, oracleMonth(this.tmpBytes) + 1, oracleDay(this.tmpBytes), hour, oracleMin(this.tmpBytes), oracleSec(this.tmpBytes), nanos, hour < 12, null);
        return result;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        if (this.externalType == 0) {
            if (this.statement.connection.j2ee13Compliant) {
                return getTimestamp(currentRow);
            }
            return getTIMESTAMP(currentRow);
        }
        switch (this.externalType) {
            case 93:
                return getTimestamp(currentRow);
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Datum getOracleObject(int currentRow) throws SQLException {
        return getTIMESTAMP(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow, Map<String, Class<?>> map) throws SQLException {
        return getObject(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalDate getLocalDate(int currentRow) throws SQLException {
        return (LocalDate) JavaToJavaConverter.convert(new TIMESTAMP(getBytesInternal(currentRow)), LocalDate.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalDateTime getLocalDateTime(int currentRow) throws SQLException {
        return (LocalDateTime) JavaToJavaConverter.convert(new TIMESTAMP(getBytesInternal(currentRow)), LocalDateTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalTime getLocalTime(int currentRow) throws SQLException {
        return (LocalTime) JavaToJavaConverter.convert(new TIMESTAMP(getBytesInternal(currentRow)), LocalTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    OffsetDateTime getOffsetDateTime(int currentRow) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NOT_IN_TIMEZONE, null, null, "DATE").fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    OffsetTime getOffsetTime(int currentRow) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NOT_IN_TIMEZONE, null, null, "DATE").fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    ZonedDateTime getZonedDateTime(int currentRow) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NOT_IN_TIMEZONE, null, null, "DATE").fillInStackTrace());
    }
}
