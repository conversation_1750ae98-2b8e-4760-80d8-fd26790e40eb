package oracle.jdbc.driver;

import java.io.IOException;
import oracle.jdbc.diagnostics.Diagnosable;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIMsg.class */
class T4CTTIMsg implements Diagnosable {
    private byte ttcCode;
    T4CConnection connection;
    T4CMAREngine meg;

    private T4CTTIMsg() {
        this.ttcCode = (byte) 0;
        this.connection = null;
        this.meg = null;
    }

    T4CTTIMsg(T4CConnection _conn, byte _ttccode) {
        this.connection = _conn;
        this.ttcCode = _ttccode;
        this.meg = _conn.getMarshalEngine();
    }

    final byte getTTCCode() {
        return this.ttcCode;
    }

    final void setTTCCode(byte _ttccode) {
        this.ttcCode = _ttccode;
    }

    void marshalTTCcode() throws IOException {
        this.meg.marshalUB1(this.ttcCode);
    }

    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.connection.getDiagnosable();
    }
}
