package oracle.jdbc.driver;

import java.sql.SQLException;

/* compiled from: T2CConnection.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CBanner.class */
class T2CBanner {
    private byte[] rawBanner = new byte[4096];
    private int rawBannerLen = 0;
    private String banner = null;
    private DBConversion conversion;
    static final int T2C_MAX_BANNER_LENGTH = 4096;

    T2CBanner(DBConversion value) {
        this.conversion = value;
    }

    String getBanner() throws SQLException {
        if (this.rawBannerLen > 0) {
            this.banner = this.conversion.CharBytesToString(this.rawBanner, this.rawBannerLen);
        }
        return this.banner;
    }
}
