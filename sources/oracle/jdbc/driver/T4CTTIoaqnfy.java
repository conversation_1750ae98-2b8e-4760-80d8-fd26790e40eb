package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.driver.T4CTTIkpdnrreq;
import oracle.sql.CharacterSet;
import oracle.sql.json.OracleJsonDatum;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoaqnfy.class */
final class T4CTTIoaqnfy extends T4CTTIfun {
    byte[] jmsConnectionId;
    boolean isMarshalOver;
    private volatile boolean needToBeClosed;
    OracleConnection conn;
    T4CTTIkpdnrnf notificationHeader;
    T4CTTIkpdnrmp messageProperties;
    T4CTTIkpdnrpay payload;
    NTFManager ntfManager;
    T4CMAREngine mar;
    String databaseUniqueIdentifier;
    boolean isDCNConnection;
    CharacterSet charset;
    NTFDCNConnection dcnConn;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CTTIoaqnfy.class.desiredAssertionStatus();
    }

    T4CTTIoaqnfy(T4CConnection connection, String jmsConnectionId, boolean isDCN) throws SQLException {
        this(connection, jmsConnectionId);
        this.isDCNConnection = isDCN;
        this.charset = CharacterSet.make(this.conn.getDbCsId());
    }

    T4CTTIoaqnfy(T4CConnection connection, String jmsConnectionId) throws SQLException {
        super(connection, (byte) 3);
        this.jmsConnectionId = null;
        this.isMarshalOver = false;
        this.needToBeClosed = false;
        this.conn = null;
        this.notificationHeader = null;
        this.messageProperties = null;
        this.payload = null;
        this.ntfManager = null;
        this.isDCNConnection = false;
        this.charset = null;
        this.dcnConn = null;
        if (!$assertionsDisabled && (connection == null || jmsConnectionId == null)) {
            throw new AssertionError("connection is " + connection + ", jmsConnectionId is " + jmsConnectionId);
        }
        setFunCode((short) 187);
        this.conn = connection;
        this.mar = connection.mare;
        this.jmsConnectionId = this.mar.conv.StringToCharBytes(jmsConnectionId);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        T4CTTIkpdnrreq req = new T4CTTIkpdnrreq((T4CConnection) this.conn);
        req.send(this.jmsConnectionId, T4CTTIkpdnrreq.OpCode.INIT_KPDNRREQ);
        this.isMarshalOver = true;
        if (this.dcnConn != null) {
            this.dcnConn.signalConnectionStarted();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readOAC() throws SQLException, IOException {
        this.notificationHeader = new T4CTTIkpdnrnf((T4CConnection) this.conn);
        this.messageProperties = new T4CTTIkpdnrmp((T4CConnection) this.conn);
        this.payload = new T4CTTIkpdnrpay((T4CConnection) this.conn);
        while (!this.needToBeClosed) {
            this.notificationHeader.receive();
            if (this.notificationHeader.messageType != 4) {
                this.messageProperties.receive();
                this.payload.receive();
                if (this.isDCNConnection) {
                    createAndGenerateDCNEvent();
                } else {
                    createAndGenerateEvent();
                }
            } else if (!this.isDCNConnection) {
                throwException(this.notificationHeader.errorCode);
            }
        }
        throw new RuntimeException("Close initiated");
    }

    private void throwException(int errCode) {
        try {
            int jdbcRegId = PhysicalConnection.ntfManager.getJDBCRegId(Long.valueOf(this.notificationHeader.getRegistrationId()));
            NTFJMSRegistration registration = (NTFJMSRegistration) PhysicalConnection.ntfManager.getRegistration(jdbcRegId);
            switch (errCode) {
                case 24010:
                    registration.raiseException(3);
                    break;
                case 24035:
                    registration.raiseException(2);
                    break;
            }
        } catch (Exception e) {
        }
    }

    public void createAndGenerateDCNEvent() throws SQLException {
        long regId = this.notificationHeader.getRegistrationId();
        NTFDCNRegistration registration = PhysicalConnection.ntfManager.getDCNRegistration(regId);
        NTFDCNEvent dcnEvent = new NTFDCNEvent(regId, this.payload.getRawPayload(), registration.getDatabaseVersion(), this.charset.getOracleId());
        registration.notify(dcnEvent);
    }

    public void createAndGenerateEvent() throws SQLException {
        try {
            int jdbcRegId = PhysicalConnection.ntfManager.getJDBCRegId(Long.valueOf(this.notificationHeader.getRegistrationId()));
            NTFJMSRegistration registration = (NTFJMSRegistration) PhysicalConnection.ntfManager.getRegistration(jdbcRegId);
            NTFJMSEvent jmsEvent = new NTFJMSEvent(this);
            jmsEvent.setAqMessageProperites(this.messageProperties.getAqMessageProperties());
            jmsEvent.setJmsMessageProperties(this.messageProperties.getJmsMessageProperties());
            jmsEvent.setMessageId(this.messageProperties.getMessageId());
            if (this.payload.payloadType == 1) {
                jmsEvent.setPayload(this.payload.getRawPayload());
            } else if (this.payload.payloadType == 2) {
                AQMessageI aqMsg = new AQMessageI(this.messageProperties.getAqMessageProperties());
                aqMsg.setPayload(this.payload.getRawPayload(), this.payload.toid);
                aqMsg.setMessageId(this.messageProperties.getMessageId());
                jmsEvent.setAQMessage(aqMsg);
                jmsEvent.setAdt(true);
            } else if (this.payload.payloadType == 3) {
                AQMessageI aqMsg2 = new AQMessageI(this.messageProperties.getAqMessageProperties());
                OracleJsonDatum jsonPayload = new OracleJsonDatum(this.payload.getRawPayload());
                aqMsg2.setPayload(jsonPayload);
                aqMsg2.setMessageId(this.messageProperties.getMessageId());
                jmsEvent.setAQMessage(aqMsg2);
                jmsEvent.setAdt(false);
            }
            jmsEvent.setConsumerName(this.notificationHeader.getConsumerName());
            jmsEvent.setQueueName(this.notificationHeader.getNotificationQueue());
            jmsEvent.setRegistration(this.notificationHeader.getNotificationQueue());
            registration.notify(jmsEvent);
        } catch (NullPointerException e) {
        }
    }

    public void stopListening() {
        this.needToBeClosed = true;
    }

    public void setDCNConnection(NTFDCNConnection conn) {
        this.dcnConn = conn;
    }
}
