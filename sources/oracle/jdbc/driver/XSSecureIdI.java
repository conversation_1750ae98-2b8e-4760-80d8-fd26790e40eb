package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import oracle.jdbc.internal.XSSecureId;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/XSSecureIdI.class */
final class XSSecureIdI extends XSSecureId {
    byte[] kpxssidpmac = null;
    byte[] kpxssidpmtid = null;
    long kpxssidpnonce = 0;

    XSSecureIdI() {
    }

    @Override // oracle.jdbc.internal.XSSecureId
    public void setMac(byte[] kpxssidpmac) throws SQLException {
        InternalFactory.xsSecurityCheck();
        if (kpxssidpmac != null) {
            this.kpxssidpmac = Arrays.copyOf(kpxssidpmac, kpxssidpmac.length);
        } else {
            this.kpxssidpmac = null;
        }
    }

    @Override // oracle.jdbc.internal.XSSecureId
    public void setMidtierId(byte[] kpxssidpmtid) throws SQLException {
        InternalFactory.xsSecurityCheck();
        if (kpxssidpmtid != null) {
            this.kpxssidpmtid = Arrays.copyOf(kpxssidpmtid, kpxssidpmtid.length);
        } else {
            this.kpxssidpmtid = null;
        }
    }

    @Override // oracle.jdbc.internal.XSSecureId
    public void setNonce(long kpxssidpnonce) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.kpxssidpnonce = kpxssidpnonce;
    }

    @Override // oracle.jdbc.internal.XSSecureId
    public byte[] getMac() {
        InternalFactory.xsSecurityCheck();
        if (this.kpxssidpmac == null) {
            return null;
        }
        return Arrays.copyOf(this.kpxssidpmac, this.kpxssidpmac.length);
    }

    @Override // oracle.jdbc.internal.XSSecureId
    public byte[] getMidtierId() {
        InternalFactory.xsSecurityCheck();
        if (this.kpxssidpmtid == null) {
            return null;
        }
        return Arrays.copyOf(this.kpxssidpmtid, this.kpxssidpmtid.length);
    }

    @Override // oracle.jdbc.internal.XSSecureId
    public long getNonce() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssidpnonce;
    }

    void marshal(T4CMAREngine mar) throws IOException {
        if (this.kpxssidpmac != null) {
            mar.marshalUB4(this.kpxssidpmac.length);
            mar.marshalCLR(this.kpxssidpmac, this.kpxssidpmac.length);
        } else {
            mar.marshalUB4(0L);
        }
        if (this.kpxssidpmtid != null) {
            mar.marshalUB4(this.kpxssidpmtid.length);
            mar.marshalCLR(this.kpxssidpmtid, this.kpxssidpmtid.length);
        } else {
            mar.marshalUB4(0L);
        }
        mar.marshalUB4(this.kpxssidpnonce);
    }

    static XSSecureIdI unmarshal(T4CMAREngine mar) throws SQLException, IOException {
        byte[] kpxssidpmac = null;
        byte[] kpxssidpmtid = null;
        int kpxssidpmacLength = (int) mar.unmarshalUB4();
        if (kpxssidpmacLength > 0) {
            kpxssidpmac = mar.unmarshalNBytes(kpxssidpmacLength);
        }
        int kpxssidpmtidLength = (int) mar.unmarshalUB4();
        if (kpxssidpmtidLength > 0) {
            kpxssidpmtid = mar.unmarshalNBytes(kpxssidpmtidLength);
        }
        long kpxssidpnonce = mar.unmarshalUB4();
        XSSecureIdI secid = new XSSecureIdI();
        secid.setMac(kpxssidpmac);
        secid.setMidtierId(kpxssidpmtid);
        secid.setNonce(kpxssidpnonce);
        return secid;
    }
}
