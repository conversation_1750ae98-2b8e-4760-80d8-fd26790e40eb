package oracle.jdbc.datasource;

import java.sql.SQLException;
import java.util.Properties;
import java.util.function.Supplier;
import javax.net.ssl.SSLContext;
import javax.sql.CommonDataSource;
import oracle.jdbc.AccessToken;
import oracle.jdbc.OracleHostnameResolver;
import oracle.jdbc.OracleShardingKeyBuilder;
import oracle.jdbc.pool.OracleShardingKeyBuilderImpl;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/OracleCommonDataSource.class */
public interface OracleCommonDataSource extends CommonDataSource {
    void setDataSourceName(String str) throws SQLException;

    String getDataSourceName();

    String getDatabaseName();

    void setDatabaseName(String str) throws SQLException;

    void setServerName(String str) throws SQLException;

    String getServerName();

    void setURL(String str) throws SQLException;

    String getURL() throws SQLException;

    void setUser(String str) throws SQLException;

    String getUser();

    void setPassword(String str) throws SQLException;

    String getDescription();

    void setDescription(String str) throws SQLException;

    String getNetworkProtocol();

    void setNetworkProtocol(String str) throws SQLException;

    void setPortNumber(int i) throws SQLException;

    int getPortNumber();

    void setConnectionProperties(Properties properties) throws SQLException;

    Properties getConnectionProperties() throws SQLException;

    void setConnectionProperty(String str, String str2) throws SQLException;

    String getConnectionProperty(String str) throws SQLException;

    void setMaxStatements(int i) throws SQLException;

    int getMaxStatements() throws SQLException;

    void setImplicitCachingEnabled(boolean z) throws SQLException;

    boolean getImplicitCachingEnabled() throws SQLException;

    void setExplicitCachingEnabled(boolean z) throws SQLException;

    boolean getExplicitCachingEnabled() throws SQLException;

    void setRoleName(String str) throws SQLException;

    String getRoleName();

    void setSSLContext(SSLContext sSLContext) throws SQLException;

    void setSingleShardTransactionSupport(boolean z) throws SQLException;

    void setHostnameResolver(OracleHostnameResolver oracleHostnameResolver);

    void setTokenSupplier(Supplier<? extends AccessToken> supplier);

    default OracleShardingKeyBuilder createShardingKeyBuilder() throws SQLException {
        return new OracleShardingKeyBuilderImpl();
    }
}
