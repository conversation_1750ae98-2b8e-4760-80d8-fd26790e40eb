package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.Arrays;
import oracle.jdbc.replay.ReplayableConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CSessionlessTransaction.class */
class T4CSessionlessTransaction {
    static final int SESSIONLESS_TXN_DEFAULT_TIMEOUT = 60;
    static final int FTSTART = 1;
    static final int FTRESUME = 2;
    static final int FTSUSPEND = 3;
    static final int FTPRESUSPEND = 4;
    static final int FTPOSTSUSPEND = 5;
    private byte[] GTRID = null;
    private int piggyBackQueueMaxSize = 5;
    private int[] operationsQueue = new int[this.piggyBackQueueMaxSize];
    private boolean[] postCallSuspQueue = new boolean[this.piggyBackQueueMaxSize];
    private int[] timeoutsQueue = new int[this.piggyBackQueueMaxSize];
    private byte[][] gtridsQueue = new byte[this.piggyBackQueueMaxSize];
    private int[] sequenceNumQueue = new int[this.piggyBackQueueMaxSize];
    private int queueTailIndex = 0;
    private T4CConnection conn;

    /* JADX WARN: Type inference failed for: r1v13, types: [byte[], byte[][]] */
    T4CSessionlessTransaction(T4CConnection connection) {
        if (connection == null) {
            throw new IllegalArgumentException("Connection cannot be null");
        }
        this.conn = connection;
    }

    private void setPiggyBackParameters(int operation, boolean postCallSuspend, byte[] GTRID, int timeout, int messageIndex) {
        this.operationsQueue[messageIndex] = operation;
        this.postCallSuspQueue[messageIndex] = postCallSuspend;
        this.timeoutsQueue[messageIndex] = timeout;
        this.gtridsQueue[messageIndex] = GTRID;
    }

    void resetQueueOfPiggyBackMessages() {
        for (int index = 0; index != this.piggyBackQueueMaxSize && this.operationsQueue[index] != 0; index++) {
            setPiggyBackParameters(0, false, null, 60, index);
            this.sequenceNumQueue[index] = 0;
        }
        this.queueTailIndex = 0;
    }

    /* JADX WARN: Type inference failed for: r0v13, types: [byte[], byte[][], java.lang.Object] */
    private void nextPiggyBackMessage() {
        if (this.queueTailIndex == this.piggyBackQueueMaxSize - 1) {
            int newSize = this.piggyBackQueueMaxSize * 2;
            int[] operationsQueue2 = new int[newSize];
            boolean[] postCallSuspQueue2 = new boolean[newSize];
            int[] timeoutsQueue2 = new int[newSize];
            ?? r0 = new byte[newSize];
            int[] sequenceNumQueue2 = new int[newSize];
            System.arraycopy(this.operationsQueue, 0, operationsQueue2, 0, this.piggyBackQueueMaxSize);
            System.arraycopy(this.postCallSuspQueue, 0, postCallSuspQueue2, 0, this.piggyBackQueueMaxSize);
            System.arraycopy(this.timeoutsQueue, 0, timeoutsQueue2, 0, this.piggyBackQueueMaxSize);
            System.arraycopy(this.gtridsQueue, 0, r0, 0, this.piggyBackQueueMaxSize);
            System.arraycopy(this.sequenceNumQueue, 0, sequenceNumQueue2, 0, this.piggyBackQueueMaxSize);
            this.operationsQueue = operationsQueue2;
            this.postCallSuspQueue = postCallSuspQueue2;
            this.timeoutsQueue = timeoutsQueue2;
            this.gtridsQueue = r0;
            this.sequenceNumQueue = sequenceNumQueue2;
            this.piggyBackQueueMaxSize = newSize;
        }
        this.queueTailIndex++;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:32:0x0105 A[Catch: IOException -> 0x0156, TryCatch #0 {IOException -> 0x0156, blocks: (B:11:0x0064, B:13:0x0077, B:15:0x0081, B:16:0x00a4, B:32:0x0105, B:33:0x0136, B:17:0x00b6, B:20:0x00cc, B:23:0x00dc, B:24:0x00e5, B:27:0x00ea, B:28:0x00f6, B:29:0x00ff, B:8:0x0017, B:9:0x0020, B:10:0x0021), top: B:39:0x000b }] */
    /* JADX WARN: Removed duplicated region for block: B:33:0x0136 A[Catch: IOException -> 0x0156, TryCatch #0 {IOException -> 0x0156, blocks: (B:11:0x0064, B:13:0x0077, B:15:0x0081, B:16:0x00a4, B:32:0x0105, B:33:0x0136, B:17:0x00b6, B:20:0x00cc, B:23:0x00dc, B:24:0x00e5, B:27:0x00ea, B:28:0x00f6, B:29:0x00ff, B:8:0x0017, B:9:0x0020, B:10:0x0021), top: B:39:0x000b }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void doSessionlessTransaction(int r13, boolean r14, byte[] r15, int r16, int r17) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 367
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CSessionlessTransaction.doSessionlessTransaction(int, boolean, byte[], int, int):void");
    }

    void ensureSessionlessTxnIsSupported() throws SQLException {
        if (!this.conn.hasServerCompileTimeCapability(44, 32)) {
            throw DatabaseError.createSqlException(this.conn.getConnectionDuringExceptionHandling(), 26207);
        }
    }

    void disallowMixUseOfXAandSessionlessAPIs(int txnMode) throws SQLException {
        if (this.conn.inSessionlessTxnMode() && txnMode != this.conn.getTxnMode()) {
            throw DatabaseError.createSqlException(this.conn.getConnectionDuringExceptionHandling(), DatabaseError.EOJ_MIX_USE_OF_XA_AND_SESSIONLESS_APIS);
        }
    }

    void doStartOrResume(byte[] GTRID, int timeout, int operation, int txnMode) throws SQLException {
        if (operation != 1 && operation != 2) {
            throw new IllegalArgumentException("Invalid operation!");
        }
        ensureSessionlessTxnIsSupported();
        disallowMixUseOfXAandSessionlessAPIs(txnMode);
        if (timeout < 0) {
            throw DatabaseError.createSqlException(this.conn.getConnectionDuringExceptionHandling(), 26206);
        }
        if (this.conn.getAutoCommit()) {
            throw DatabaseError.createSqlException(this.conn.getConnectionDuringExceptionHandling(), DatabaseError.EOJ_START_SESSIONLESS_TXN_WITH_AUTOCOMMIT);
        }
        if (GTRID == null || GTRID.length == 0 || GTRID.length > 64) {
            throw DatabaseError.createSqlException(this.conn.getConnectionDuringExceptionHandling(), 26200);
        }
        if (this.operationsQueue[this.queueTailIndex] != 0) {
            if (this.operationsQueue[this.queueTailIndex] == 5) {
                throw new IllegalStateException("No piggy back operation can follow a post call suspend");
            }
            nextPiggyBackMessage();
        }
        if (txnMode == 3) {
            enterXASessionlessTxnMode();
        } else {
            enterSessionlessTxnMode();
        }
        this.GTRID = (byte[]) GTRID.clone();
        this.operationsQueue[this.queueTailIndex] = operation;
        this.timeoutsQueue[this.queueTailIndex] = timeout;
        this.gtridsQueue[this.queueTailIndex] = this.GTRID;
    }

    void doSuspendImmediately() throws SQLException {
        ensureSessionlessTxnIsSupported();
        disallowMixUseOfXAandSessionlessAPIs(2);
        this.GTRID = null;
        doSessionlessTransaction(3, false, this.GTRID, 0, 0);
    }

    void doPreCallSuspend(int txnMode) throws SQLException {
        ensureSessionlessTxnIsSupported();
        disallowMixUseOfXAandSessionlessAPIs(txnMode);
        if (this.operationsQueue[this.queueTailIndex] != 0) {
            if (this.operationsQueue[this.queueTailIndex] == 5) {
                throw new IllegalStateException("No piggy back operation can follow a post call suspend");
            }
            nextPiggyBackMessage();
        }
        this.operationsQueue[this.queueTailIndex] = 4;
        this.GTRID = null;
    }

    void doPostCallSuspend() throws SQLException {
        ensureSessionlessTxnIsSupported();
        disallowMixUseOfXAandSessionlessAPIs(2);
        if (this.operationsQueue[this.queueTailIndex] != 0) {
            if (this.operationsQueue[this.queueTailIndex] == 4) {
                nextPiggyBackMessage();
            } else {
                if (this.operationsQueue[this.queueTailIndex] == 5) {
                    throw new IllegalStateException("No piggy back operation can follow a post call suspend");
                }
                this.postCallSuspQueue[this.queueTailIndex] = true;
                return;
            }
        }
        this.operationsQueue[this.queueTailIndex] = 5;
        this.postCallSuspQueue[this.queueTailIndex] = true;
        this.GTRID = null;
    }

    void sendPiggyBackMessages() throws SQLException {
        for (int index = 0; index != this.piggyBackQueueMaxSize && this.operationsQueue[index] != 0; index++) {
            doSessionlessTransaction(this.operationsQueue[index], this.postCallSuspQueue[index], this.gtridsQueue[index], this.timeoutsQueue[index], index);
        }
    }

    int getLastPiggyBackOperation() {
        return this.operationsQueue[this.queueTailIndex];
    }

    boolean lastPiggyBackOpIsPostCallSuspend() {
        return this.postCallSuspQueue[this.queueTailIndex];
    }

    int[] getListOfPiggyBackMessagesSequenceNumbers() {
        return Arrays.copyOfRange(this.sequenceNumQueue, 0, this.queueTailIndex + 1);
    }

    byte[] getGTRID() {
        if (this.GTRID != null) {
            return (byte[]) this.GTRID.clone();
        }
        return null;
    }

    void setGTRID(byte[] GTRID) {
        this.GTRID = GTRID != null ? (byte[]) GTRID.clone() : null;
    }

    void exitSessionlessTxnMode() {
        this.conn.setTxnMode(0);
    }

    void enterSessionlessTxnMode() throws SQLException {
        if (this.conn.acProxy != null) {
            ((ReplayableConnection) this.conn.acProxy).disableReplay();
        }
        this.conn.setTxnMode(2);
    }

    void enterXASessionlessTxnMode() throws SQLException {
        if (this.conn.acProxy != null) {
            ((ReplayableConnection) this.conn.acProxy).disableReplay();
        }
        this.conn.setTxnMode(3);
    }
}
