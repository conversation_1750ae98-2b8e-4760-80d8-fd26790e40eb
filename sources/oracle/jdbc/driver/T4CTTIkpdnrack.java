package oracle.jdbc.driver;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrack.class */
class T4CTTIkpdnrack {
    byte[] acknowledgmentQueue;
    long acknowledgementRegistrationId;
    byte[] lastReceivedMessageId;
    T4CMAREngine mar;

    T4CTTIkpdnrack(T4CConnection connection) {
        this.mar = connection.mare;
    }

    void send(byte[] _acknowledgmentQueue, long _acknowledgementRegistrationId, byte[] _lastReceivedMessageId) throws IOException {
        this.acknowledgmentQueue = _acknowledgmentQueue;
        this.acknowledgementRegistrationId = _acknowledgementRegistrationId;
        this.lastReceivedMessageId = _lastReceivedMessageId;
        marshal();
    }

    void marshal() throws IOException {
        if (this.acknowledgmentQueue != null && this.acknowledgmentQueue.length != 0) {
            this.mar.marshalSWORD(this.acknowledgmentQueue.length);
            this.mar.marshalCLR(this.acknowledgmentQueue, 0, this.acknowledgmentQueue.length);
        } else {
            this.mar.marshalSWORD(0);
        }
        this.mar.marshalUB4(this.acknowledgementRegistrationId);
        if (this.lastReceivedMessageId != null && this.lastReceivedMessageId.length != 0) {
            this.mar.marshalSWORD(this.lastReceivedMessageId.length);
            this.mar.marshalCLR(this.lastReceivedMessageId, 0, this.lastReceivedMessageId.length);
        } else {
            this.mar.marshalSWORD(0);
        }
    }
}
