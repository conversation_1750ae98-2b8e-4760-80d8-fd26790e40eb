package oracle.jdbc.diagnostics;

import java.util.logging.Level;
import java.util.logging.LogRecord;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/OracleLogRecord.class */
public class OracleLogRecord extends LogRecord {
    private ImmutableTraceAttributes immutableTraceAttributes;
    private String threadName;
    private final SecurityLabel securityLabel;

    public OracleLogRecord(Level level, SecurityLabel label, ImmutableTraceAttributes attributes, String msg) {
        super(level, msg);
        this.securityLabel = label;
        setTraceAttributes(attributes);
        setThreadName(Thread.currentThread().getName());
    }

    public void setTraceAttributes(ImmutableTraceAttributes attributes) {
        this.immutableTraceAttributes = attributes;
    }

    public TraceAttributes getTraceAttributes() {
        return this.immutableTraceAttributes;
    }

    public void setThreadName(String threadName) {
        this.threadName = threadName;
    }

    public String getThreadName() {
        return this.threadName;
    }

    public SecurityLabel getSecurityLabel() {
        return this.securityLabel;
    }
}
