package oracle.jdbc.driver.json.tree;

import java.sql.SQLException;
import java.sql.Wrapper;
import java.util.AbstractMap;
import java.util.AbstractSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import javax.json.JsonArray;
import javax.json.JsonNumber;
import javax.json.JsonObject;
import javax.json.JsonString;
import javax.json.JsonValue;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpObjectImpl.class */
public class JsonpObjectImpl extends AbstractMap<String, JsonValue> implements Wrapper, JsonObject {
    OracleJsonObject wrapped;

    public JsonpObjectImpl(OracleJsonObject wrapped) {
        this.wrapped = wrapped;
    }

    public JsonValue.ValueType getValueType() {
        return JsonValue.ValueType.OBJECT;
    }

    public boolean getBoolean(String name) {
        return this.wrapped.getBoolean(name);
    }

    public boolean getBoolean(String name, boolean value) {
        return this.wrapped.getBoolean(name, value);
    }

    public int getInt(String name) {
        return this.wrapped.getInt(name);
    }

    public int getInt(String name, int value) {
        return this.wrapped.getInt(name, value);
    }

    public JsonArray getJsonArray(String name) {
        return (JsonArray) this.wrapped.get(name).asJsonArray().wrap(JsonArray.class);
    }

    public JsonNumber getJsonNumber(String name) {
        return (JsonNumber) this.wrapped.get(name).wrap(JsonNumber.class);
    }

    public JsonObject getJsonObject(String name) {
        return (JsonObject) this.wrapped.get(name).asJsonObject().wrap(JsonObject.class);
    }

    public JsonString getJsonString(String name) {
        return (JsonString) this.wrapped.get(name).wrap(JsonString.class);
    }

    public String getString(String name) {
        return get((Object) name).getString();
    }

    public String getString(String name, String defaultValue) {
        JsonString jsonString = get((Object) name);
        if (jsonString == null || jsonString.getValueType() != JsonValue.ValueType.STRING) {
            return defaultValue;
        }
        return jsonString.getString();
    }

    public boolean isNull(String name) {
        return this.wrapped.isNull(name);
    }

    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> iface) throws SQLException {
        try {
            return iface.cast(this.wrapped);
        } catch (ClassCastException e) {
            throw new SQLException(e.getMessage(), e);
        }
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return iface.isAssignableFrom(OracleJsonObject.class);
    }

    @Override // java.util.AbstractMap, java.util.Map
    public Set<Map.Entry<String, JsonValue>> entrySet() {
        return new AbstractSet<Map.Entry<String, JsonValue>>() { // from class: oracle.jdbc.driver.json.tree.JsonpObjectImpl.1
            @Override // java.util.AbstractCollection, java.util.Collection, java.lang.Iterable, java.util.Set
            public Iterator<Map.Entry<String, JsonValue>> iterator() {
                return new Iterator<Map.Entry<String, JsonValue>>() { // from class: oracle.jdbc.driver.json.tree.JsonpObjectImpl.1.1
                    Iterator<Map.Entry<String, OracleJsonValue>> iter;

                    {
                        this.iter = JsonpObjectImpl.this.wrapped.entrySet().iterator();
                    }

                    @Override // java.util.Iterator
                    public boolean hasNext() {
                        return this.iter.hasNext();
                    }

                    /* JADX WARN: Can't rename method to resolve collision */
                    @Override // java.util.Iterator
                    public Map.Entry<String, JsonValue> next() {
                        final Map.Entry<String, OracleJsonValue> oentry = this.iter.next();
                        return new Map.Entry<String, JsonValue>() { // from class: oracle.jdbc.driver.json.tree.JsonpObjectImpl.1.1.1
                            /* JADX WARN: Can't rename method to resolve collision */
                            @Override // java.util.Map.Entry
                            public String getKey() {
                                return (String) oentry.getKey();
                            }

                            /* JADX WARN: Can't rename method to resolve collision */
                            @Override // java.util.Map.Entry
                            public JsonValue getValue() {
                                return (JsonValue) ((OracleJsonValue) oentry.getValue()).wrap(JsonValue.class);
                            }

                            @Override // java.util.Map.Entry
                            public JsonValue setValue(JsonValue value) {
                                throw new UnsupportedOperationException();
                            }
                        };
                    }
                };
            }

            @Override // java.util.AbstractCollection, java.util.Collection, java.util.Set
            public int size() {
                return JsonpObjectImpl.this.wrapped.size();
            }
        };
    }

    @Override // java.util.AbstractMap, java.util.Map
    public JsonValue get(Object name) {
        OracleJsonValue v = this.wrapped.get(name);
        if (v == null) {
            return null;
        }
        return (JsonValue) v.wrap(JsonValue.class);
    }

    @Override // java.util.AbstractMap, java.util.Map
    public boolean containsKey(Object name) {
        return this.wrapped.containsKey(name);
    }
}
