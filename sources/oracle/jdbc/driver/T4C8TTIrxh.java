package oracle.jdbc.driver;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import oracle.jdbc.util.RepConversion;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTIrxh.class */
class T4C8TTIrxh extends T4CTTIMsg {
    short rxhflg;
    int numRqsts;
    int iterNum;
    int numItersThisTime;
    int uacBufLength;
    static final byte RXHFU2O = 1;
    static final byte RXHFEOR = 2;
    static final byte RXHPLSV = 4;
    static final byte RXHFRXR = 8;
    static final byte RXHFKCO = 16;
    static final byte RXHFDCF = 32;
    public static final boolean TRACE = false;
    private static final String CLASS_NAME = T4C8TTIrxh.class.getName();
    private static final String _Copyright_2014_Oracle_All_Rights_Reserved_ = null;

    T4C8TTIrxh(T4CConnection _conn) {
        super(_conn, (byte) 0);
    }

    void unmarshalV10(T4CTTIrxd rxd) throws SQLException, IOException {
        int[] CLRRetLen = new int[1];
        this.rxhflg = this.meg.unmarshalUB1();
        this.numRqsts = this.meg.unmarshalUB2();
        this.iterNum = this.meg.unmarshalUB2();
        this.numRqsts += this.iterNum * 256;
        this.numItersThisTime = this.meg.unmarshalUB2();
        this.uacBufLength = this.meg.unmarshalUB2();
        byte[] bitVectorBytes = this.meg.unmarshalDALC(CLRRetLen);
        rxd.readBitVector(bitVectorBytes, CLRRetLen[0]);
        this.meg.unmarshalDALC(bitVectorBytes, 0);
    }

    void init() {
        this.rxhflg = (short) 0;
        this.numRqsts = 0;
        this.iterNum = 0;
        this.numItersThisTime = 0;
        this.uacBufLength = 0;
    }

    private /* synthetic */ Object[] lambda$print$0() {
        return new Object[]{new String(RepConversion.toHex(this.rxhflg), StandardCharsets.US_ASCII)};
    }

    void print() {
    }
}
