package oracle.jdbc.babelfish;

import java.io.File;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/Translator.class */
public class Translator {
    private final File localErrorTranslationFile;
    private final String translationProfile;
    private Connection conn;
    private CallableStatement queryTranslationStatement;
    private CallableStatement errorTranslationStatement;
    private final Map<String, String> queryCache;
    private final Map<Integer, TranslatedErrorInfo> errorCache;
    private final Map<Integer, TranslatedErrorInfo> localErrorCache;

    Translator(String translationProfile, File localErrorTranslationFile, TranslationCache translationCache) throws SQLException {
        this.translationProfile = translationProfile;
        this.localErrorTranslationFile = localErrorTranslationFile;
        this.queryCache = translationCache.getQueryCache();
        this.errorCache = translationCache.getErrorCache();
        this.localErrorCache = translationCache.getLocalErrorCache();
    }

    public SQLException translateError(SQLException ex) throws SQLException {
        if (this.conn == null) {
            return translateErrorLocal(ex);
        }
        TranslatedErrorInfo translatedErrorInfo = this.errorCache.get(Integer.valueOf(ex.getErrorCode()));
        if (translatedErrorInfo != null) {
            SQLException newex = new SQLException("[Translated Error Codes] " + ex.getMessage(), translatedErrorInfo.getSqlState(), translatedErrorInfo.getErrorCode(), ex);
            newex.setStackTrace(ex.getStackTrace());
            return newex;
        }
        try {
            this.errorTranslationStatement.clearParameters();
            this.errorTranslationStatement.setInt(1, ex.getErrorCode());
            this.errorTranslationStatement.registerOutParameter(2, 4);
            this.errorTranslationStatement.registerOutParameter(3, 12);
            this.errorTranslationStatement.execute();
            int tmpErrorCode = this.errorTranslationStatement.getInt(2);
            if (this.errorTranslationStatement.wasNull()) {
                tmpErrorCode = ex.getErrorCode();
            }
            String tmpSqlState = this.errorTranslationStatement.getString(3);
            if (tmpSqlState == null) {
                tmpSqlState = ex.getSQLState();
            }
            TranslatedErrorInfo translatedErrorInfo2 = new TranslatedErrorInfo(tmpErrorCode, tmpSqlState);
            this.errorCache.put(Integer.valueOf(ex.getErrorCode()), translatedErrorInfo2);
            SQLException newex2 = new SQLException("[Translated Error Codes] " + ex.getMessage(), translatedErrorInfo2.getSqlState(), translatedErrorInfo2.getErrorCode(), ex);
            newex2.setStackTrace(ex.getStackTrace());
            return newex2;
        } catch (SQLException e) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_SERVER_TRANSLATION_ERROR, (Object) null, e).fillInStackTrace());
        }
    }

    SQLException translateErrorLocal(SQLException ex) throws SQLException {
        if (this.localErrorCache == null) {
            return ex;
        }
        TranslatedErrorInfo translatedErrorInfo = this.localErrorCache.get(Integer.valueOf(ex.getErrorCode()));
        if (translatedErrorInfo != null) {
            String message = "[Translated Error Codes] " + ex.getMessage();
            SQLException exep = new SQLException(message, translatedErrorInfo.getSqlState(), translatedErrorInfo.getErrorCode(), ex);
            exep.setStackTrace(ex.getStackTrace());
            return exep;
        }
        SQLException newex = new SQLException("[Error Translation Not Available] " + ex.getMessage(), ex.getSQLState(), ex.getErrorCode(), ex);
        newex.setStackTrace(ex.getStackTrace());
        return newex;
    }

    String translateQuery(String sql) throws SQLException {
        if (this.conn != null) {
            String translatedSql = this.queryCache.get(sql);
            if (translatedSql != null) {
                return translatedSql;
            }
            String jdbcMarkerConvertedSql = convertParameterMarkersToOracleStyle(sql);
            try {
                this.queryTranslationStatement.clearParameters();
                this.queryTranslationStatement.setString(1, jdbcMarkerConvertedSql);
                this.queryTranslationStatement.registerOutParameter(2, 12);
                this.queryTranslationStatement.execute();
                String translatedSql2 = this.queryTranslationStatement.getString(2);
                if (translatedSql2 == null) {
                    translatedSql2 = jdbcMarkerConvertedSql;
                }
                this.queryCache.put(sql, translatedSql2);
                return translatedSql2;
            } catch (SQLException ex) {
                throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_SERVER_TRANSLATION_ERROR, (Object) null, ex).fillInStackTrace());
            }
        }
        throw ((SQLException) DatabaseError.createSqlException(279).fillInStackTrace());
    }

    public void activateServerTranslation(Connection babelfishConnection) throws SQLException {
        CallableStatement sessionstmt = babelfishConnection.prepareCall("begin execute immediate 'alter session set sql_translation_profile = ' || sys.dbms_assert.qualified_sql_name(?); end;");
        sessionstmt.setString(1, this.translationProfile);
        sessionstmt.execute();
        this.queryTranslationStatement = babelfishConnection.prepareCall("begin sys.dbms_sql_translator.translate_sql(?, ? ); end;");
        this.errorTranslationStatement = babelfishConnection.prepareCall("begin sys.dbms_sql_translator.translate_error(?, ?, ? ); end;");
        this.conn = babelfishConnection;
    }

    void deactivateServerTranslation() throws SQLException {
        this.queryTranslationStatement.close();
        this.errorTranslationStatement.close();
        this.conn = null;
    }

    String convertParameterMarkersToOracleStyle(String sql) {
        StringBuilder retStr = new StringBuilder();
        boolean inQuotesFlag = false;
        boolean inSingleLineComment = false;
        boolean inMultiLineComment = false;
        int parameterCount = 1;
        int i = 0;
        while (i < sql.length()) {
            char c = sql.charAt(i);
            if (inSingleLineComment) {
                retStr.append(c);
            } else {
                switch (c) {
                    case '\'':
                        if (!inMultiLineComment) {
                            inQuotesFlag = !inQuotesFlag;
                        }
                        retStr.append(c);
                        break;
                    case '*':
                        retStr.append(c);
                        if (!inQuotesFlag && inMultiLineComment && i < sql.length() - 1 && sql.charAt(i + 1) == '/') {
                            i++;
                            retStr.append(sql.charAt(i));
                            inMultiLineComment = false;
                            break;
                        } else {
                            break;
                        }
                    case '-':
                        retStr.append(c);
                        if (i < sql.length() - 1 && sql.charAt(i + 1) == '-') {
                            i++;
                            retStr.append(sql.charAt(i));
                            inSingleLineComment = true;
                            break;
                        } else {
                            break;
                        }
                        break;
                    case '/':
                        retStr.append(c);
                        if (!inQuotesFlag && i < sql.length() - 1 && sql.charAt(i + 1) == '*') {
                            i++;
                            retStr.append(sql.charAt(i));
                            inMultiLineComment = true;
                            break;
                        } else {
                            break;
                        }
                        break;
                    case '?':
                        if (!inQuotesFlag && !inMultiLineComment) {
                            int i2 = parameterCount;
                            parameterCount++;
                            retStr.append(":").append(i2);
                            break;
                        } else {
                            retStr.append(c);
                            break;
                        }
                    case '\\':
                        retStr.append(c);
                        if (i >= sql.length() - 1) {
                            break;
                        } else {
                            i++;
                            retStr.append(sql.charAt(i));
                            break;
                        }
                    default:
                        retStr.append(c);
                        break;
                }
            }
            i++;
        }
        return retStr.toString();
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
