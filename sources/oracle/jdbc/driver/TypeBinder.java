package oracle.jdbc.driver;

import java.sql.SQLException;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.sql.Datum;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TypeBinder.class */
abstract class TypeBinder extends Binder {
    public static final int BYTELEN = 24;
    protected byte[] paramVal;
    protected OracleTypeADT paramOtype;

    TypeBinder(byte[] val, OracleTypeADT otype) {
        this.paramVal = val;
        this.paramOtype = otype;
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        byte[] value = this.paramVal;
        if (clearPriorBindValues && stmt.isThinDriver()) {
            this.paramVal = null;
        }
        if (value == null) {
            bindIndicators[indoffset] = -1;
            if (bindUseDBA) {
                bindDataOffsets[bindDataIndex] = -1;
                bindDataLengths[bindDataIndex] = 0;
            }
        } else {
            bindIndicators[indoffset] = 0;
            int len = value.length;
            if (bindUseDBA) {
                long pos = bindData.getPosition();
                bindDataOffsets[bindDataIndex] = pos;
                stmt.lastBoundDataOffsets[bindPosition] = pos;
                bindData.put(value, 0, len);
                bindDataLengths[bindDataIndex] = len;
                stmt.lastBoundDataLengths[bindPosition] = len;
                bindIndicators[lenoffset] = (short) len;
            }
        }
        if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
            if (bindIndicators[indoffset] == -1) {
                localCheckSum = CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
            } else {
                localCheckSum = CRC64.updateChecksum(localCheckSum, value, 0, value.length);
            }
        }
        return localCheckSum;
    }

    @Override // oracle.jdbc.driver.Binder
    Datum getDatum(OraclePreparedStatement stmt, int bindPosition, int formOfUse, int internalType) throws SQLException {
        return SQLUtil.makeDatum(stmt.connection, this.paramVal, internalType, (String) null, 0);
    }
}
