package oracle.jdbc.driver.json.binary;

import java.util.BitSet;
import oracle.jdbc.driver.json.OracleJsonExceptions;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/StreamContext.class */
public class StreamContext {
    int depth;
    BitSet stack = new BitSet();
    BitSet hasChildren = new BitSet();
    boolean pendingKey;
    boolean done;
    OracleJsonExceptions.ExceptionFactory exceptionFactory;

    public StreamContext(OracleJsonExceptions.ExceptionFactory exceptionFactory) {
        this.exceptionFactory = exceptionFactory;
        init();
    }

    void init() {
        this.stack.clear();
        this.hasChildren.clear();
        this.depth = 0;
        this.pendingKey = false;
        this.done = false;
    }

    public void startObject() {
        beginValue();
        this.depth++;
        this.stack.set(this.depth);
        this.hasChildren.clear(this.depth);
    }

    public void pendingKey() {
        if (!inObject() || this.pendingKey) {
            throw OracleJsonExceptions.BAD_KEY.create(this.exceptionFactory, new Object[0]);
        }
        this.pendingKey = true;
    }

    public void startArray() {
        beginValue();
        this.depth++;
        this.stack.clear(this.depth);
        this.hasChildren.clear(this.depth);
    }

    public void end() {
        if (this.pendingKey) {
            throw OracleJsonExceptions.EXPECTED_VALUE.create(this.exceptionFactory, new Object[0]);
        }
        if (this.depth == 0) {
            throw OracleJsonExceptions.BAD_END.create(this.exceptionFactory, new Object[0]);
        }
        this.depth--;
        if (this.depth == 0) {
            this.done = true;
        }
    }

    public void primitive() {
        beginValue();
        if (this.depth == 0) {
            this.done = true;
        }
    }

    private void beginValue() {
        if (inObject() && !this.pendingKey) {
            throw OracleJsonExceptions.MISSING_KEY.create(this.exceptionFactory, new Object[0]);
        }
        if (this.done) {
            throw OracleJsonExceptions.EXTRA_EVENTS.create(this.exceptionFactory, new Object[0]);
        }
        this.pendingKey = false;
        this.hasChildren.set(this.depth);
    }

    public boolean inObject() {
        return this.depth > 0 && this.stack.get(this.depth);
    }

    public boolean hasChildren() {
        return this.hasChildren.get(this.depth);
    }

    public void close() {
        if (!this.done) {
            throw OracleJsonExceptions.GENERATION_INCOMPLETE.create(this.exceptionFactory, new Object[0]);
        }
    }

    public void setExceptionFactory(OracleJsonExceptions.ExceptionFactory f) {
        this.exceptionFactory = f;
    }
}
