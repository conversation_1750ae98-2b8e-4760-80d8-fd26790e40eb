package oracle.jdbc.driver.json.binary;

import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.OsonAbstractObject;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonTimestampTZ;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonObjectImpl.class */
public class OsonObjectImpl extends OsonAbstractObject implements OracleJsonObject {
    public OsonObjectImpl(OsonContext ctx, int pos) {
        super(ctx);
        init(pos);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        OsonContext newCtx;
        if (Jsonp.isJakartaJson(c)) {
            newCtx = new JakartaOsonContext(this.ctx);
        } else {
            newCtx = new JsonpOsonContext(this.ctx);
        }
        return c.cast(newCtx.valueFactory.createObject(newCtx, this.pos));
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // java.util.Map
    public OracleJsonValue get(Object key) {
        return (OracleJsonValue) getInternal(key);
    }

    public OracleJsonValue get(int position) {
        return (OracleJsonValue) getInternal(position);
    }

    @Override // java.util.Map
    public Collection<OracleJsonValue> values() {
        return new OsonAbstractObject.OsonObjectValues();
    }

    @Override // java.util.Map
    public Set<Map.Entry<String, OracleJsonValue>> entrySet() {
        return new OsonAbstractObject.OsonEntrySet();
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        StringWriter writer = new StringWriter();
        JsonSerializerImpl ser = new JsonSerializerImpl(writer);
        ser.write(this);
        ser.close();
        return writer.toString();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public byte[] getBytes(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        return ((OracleJsonBinary) getValueInternal(childOffset)).getBytes();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public byte[] getBytes(String key, byte[] d) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return d;
        }
        OracleJsonValue v = (OracleJsonValue) getValueInternal(childOffset);
        return v.getOracleJsonType() == OracleJsonValue.OracleJsonType.BINARY ? v.asJsonBinary().getBytes() : d;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public double getDouble(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        return ((OracleJsonNumber) getValueInternal(childOffset)).doubleValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public double getDouble(String key, double d) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return d;
        }
        OracleJsonNumber n = getNumeric(childOffset);
        return n == null ? d : n.doubleValue();
    }

    private OracleJsonNumber getNumeric(int childOffset) {
        Object v = getValueInternal(childOffset);
        if (v instanceof OracleJsonNumber) {
            return (OracleJsonNumber) v;
        }
        return null;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public long getLong(String key, long d) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return d;
        }
        OracleJsonNumber n = getNumeric(childOffset);
        return n == null ? d : n.longValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public BigDecimal getBigDecimal(String key, BigDecimal d) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return d;
        }
        OracleJsonNumber n = getNumeric(childOffset);
        return n == null ? d : n.bigDecimalValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public long getLong(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        return ((OracleJsonNumber) getValueInternal(childOffset)).longValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public BigDecimal getBigDecimal(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        return ((OracleJsonNumber) getValueInternal(childOffset)).bigDecimalValue();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public LocalDateTime getLocalDateTime(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        OracleJsonValue v = (OracleJsonValue) getValueInternal(childOffset);
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.DATE) {
            return v.asJsonDate().getLocalDateTime();
        }
        return v.asJsonTimestamp().getLocalDateTime();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OffsetDateTime getOffsetDateTime(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            throw new NullPointerException();
        }
        OracleJsonTimestampTZ v = (OracleJsonTimestampTZ) getValueInternal(childOffset);
        return v.getOffsetDateTime();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public LocalDateTime getLocalDateTime(String key, LocalDateTime defaultValue) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return defaultValue;
        }
        OracleJsonValue v = (OracleJsonValue) getValueInternal(childOffset);
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.DATE) {
            return v.asJsonDate().getLocalDateTime();
        }
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.TIMESTAMP) {
            return v.asJsonTimestamp().getLocalDateTime();
        }
        return defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OffsetDateTime getOffsetDateTime(String key, OffsetDateTime defaultValue) {
        int childOffset = getChildOffset(key);
        if (childOffset == -1) {
            return defaultValue;
        }
        OracleJsonValue v = (OracleJsonValue) getValueInternal(childOffset);
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.TIMESTAMPTZ) {
            return v.asJsonTimestampTZ().getOffsetDateTime();
        }
        return defaultValue;
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, String value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, int value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, long value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, BigDecimal value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, double value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, boolean value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue putNull(String name) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String name, byte[] values) {
        throw createNotModifiable();
    }

    @Override // java.util.Map
    public OracleJsonValue put(String key, OracleJsonValue value) {
        throw createNotModifiable();
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // java.util.Map
    public OracleJsonValue remove(Object key) {
        throw createNotModifiable();
    }

    @Override // java.util.Map
    public void putAll(Map<? extends String, ? extends OracleJsonValue> m) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String key, LocalDateTime value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonObject
    public OracleJsonValue put(String key, OffsetDateTime value) {
        throw createNotModifiable();
    }

    @Override // java.util.Map
    public void clear() {
        throw createNotModifiable();
    }

    private UnsupportedOperationException createNotModifiable() {
        throw OracleJsonExceptions.OBJ_NOT_MUTABLE.create(OracleJsonExceptions.ORACLE_FACTORY, new Object[0]);
    }
}
