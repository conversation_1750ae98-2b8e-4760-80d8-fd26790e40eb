package oracle.jdbc.driver;

import java.sql.SQLException;
import oracle.jdbc.internal.Monitor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/SensitiveScrollableResultSet.class */
class SensitiveScrollableResultSet extends InsensitiveScrollableResultSet {
    protected long beginLastFetchedIndex;
    protected long endLastFetchedIndex;

    SensitiveScrollableResultSet(PhysicalConnection conn, OracleStatement stmt) throws SQLException {
        super(conn, stmt);
        if (this.fetchedRowCount > 0) {
            this.beginLastFetchedIndex = 0L;
            this.endLastFetchedIndex = this.fetchedRowCount - 1;
        } else {
            this.beginLastFetchedIndex = -1L;
            this.endLastFetchedIndex = -1L;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public int getType() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen("getType");
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return 1005;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean next() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (super.next()) {
                    handleRefetch();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean first() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (super.first()) {
                    handleRefetch();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean last() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (super.last()) {
                    handleRefetch();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean absolute(int row) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (super.absolute(row)) {
                    handleRefetch();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean relative(int rows) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (super.relative(rows)) {
                    handleRefetch();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean previous() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (super.previous()) {
                    handleRefetch();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet
    int refreshRows(long firstRow, int numberOfRows) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                int n = super.refreshRows(firstRow, numberOfRows);
                if (n != 0) {
                    this.beginLastFetchedIndex = firstRow;
                    this.endLastFetchedIndex = (firstRow + n) - 1;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return n;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.InsensitiveScrollableResultSet, oracle.jdbc.driver.OracleResultSet
    void removeCurrentRowFromCache() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            long index = this.currentRow;
            super.removeCurrentRowFromCache();
            if (!isEmptyResultSet()) {
                if (index < this.beginLastFetchedIndex) {
                    this.beginLastFetchedIndex--;
                }
                if (index <= this.endLastFetchedIndex) {
                    this.endLastFetchedIndex--;
                }
                if (!isAfterLast()) {
                    handleRefetch();
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    protected boolean handleRefetch() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.beginLastFetchedIndex > this.currentRow || this.currentRow > this.endLastFetchedIndex) {
                    refreshRow();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }
}
