package oracle.jdbc.driver;

import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.util.Map;
import oracle.jdbc.OracleOpaque;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.oracore.OracleType;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.jdbc.oracore.OracleTypeCHAR;
import oracle.jdbc.oracore.OracleTypeFLOAT;
import oracle.jdbc.oracore.OracleTypeNUMBER;
import oracle.jdbc.oracore.OracleTypeRAW;
import oracle.jdbc.oracore.OracleTypeREF;
import oracle.sql.StructDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/StructMetaData.class */
class StructMetaData implements oracle.jdbc.internal.StructMetaData {
    StructDescriptor descriptor;
    OracleTypeADT otype;
    OracleType[] types;

    public StructMetaData(StructDescriptor desc) throws SQLException {
        if (desc == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "illegal operation: descriptor is null").fillInStackTrace());
        }
        this.descriptor = desc;
        this.otype = desc.getOracleTypeADT();
        this.types = this.otype.getAttrTypes();
    }

    @Override // java.sql.ResultSetMetaData
    public int getColumnCount() throws SQLException {
        return this.types.length;
    }

    @Override // java.sql.ResultSetMetaData
    public boolean isAutoIncrement(int column) throws SQLException {
        return false;
    }

    @Override // java.sql.ResultSetMetaData
    public boolean isSearchable(int column) throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public OracleResultSetMetaData.SecurityAttribute getSecurityAttribute(int column) throws SQLException {
        return OracleResultSetMetaData.SecurityAttribute.NONE;
    }

    @Override // java.sql.ResultSetMetaData
    public boolean isCurrency(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        return (this.types[idx] instanceof OracleTypeNUMBER) || (this.types[idx] instanceof OracleTypeFLOAT);
    }

    @Override // java.sql.ResultSetMetaData
    public boolean isCaseSensitive(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        return this.types[idx] instanceof OracleTypeCHAR;
    }

    @Override // java.sql.ResultSetMetaData
    public int isNullable(int column) throws SQLException {
        return 1;
    }

    @Override // java.sql.ResultSetMetaData
    public boolean isSigned(int column) throws SQLException {
        return true;
    }

    @Override // java.sql.ResultSetMetaData
    public int getColumnDisplaySize(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        if (this.types[idx] instanceof OracleTypeCHAR) {
            return ((OracleTypeCHAR) this.types[idx]).getLength();
        }
        if (this.types[idx] instanceof OracleTypeRAW) {
            return ((OracleTypeRAW) this.types[idx]).getLength();
        }
        return 0;
    }

    @Override // java.sql.ResultSetMetaData
    public String getColumnLabel(int column) throws SQLException {
        return getColumnName(column);
    }

    @Override // java.sql.ResultSetMetaData
    public String getColumnName(int column) throws SQLException {
        Monitor.CloseableLock lock = this.otype.getConnection().acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                String attributeName = this.otype.getAttributeName(column);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return attributeName;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // java.sql.ResultSetMetaData
    public String getSchemaName(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        if (this.types[idx] instanceof OracleTypeADT) {
            return ((OracleTypeADT) this.types[idx]).getSchemaName();
        }
        return "";
    }

    @Override // java.sql.ResultSetMetaData
    public int getPrecision(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        return this.types[idx].getPrecision();
    }

    @Override // oracle.jdbc.OracleResultSetMetaData, java.sql.ResultSetMetaData
    public int getScale(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        return this.types[idx].getScale();
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public boolean isVariableScale(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        return this.types[idx].getScale() == -127;
    }

    @Override // java.sql.ResultSetMetaData
    public String getTableName(int column) throws SQLException {
        return null;
    }

    @Override // java.sql.ResultSetMetaData
    public String getCatalogName(int column) throws SQLException {
        return null;
    }

    @Override // java.sql.ResultSetMetaData
    public int getColumnType(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        return this.types[idx].getTypeCode();
    }

    @Override // java.sql.ResultSetMetaData
    public String getColumnTypeName(int column) throws SQLException {
        int type = getColumnType(column);
        int idx = getValidColumnIndex(column);
        switch (type) {
            case oracle.jdbc.OracleTypes.INTERVALDS /* -104 */:
                return "INTERVALDS";
            case oracle.jdbc.OracleTypes.INTERVALYM /* -103 */:
                return "INTERVALYM";
            case oracle.jdbc.OracleTypes.TIMESTAMPLTZ /* -102 */:
                return "TIMESTAMP WITH LOCAL TIME ZONE";
            case oracle.jdbc.OracleTypes.TIMESTAMPTZ /* -101 */:
                return "TIMESTAMP WITH TIME ZONE";
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
                return "NCHAR";
            case oracle.jdbc.OracleTypes.BFILE /* -13 */:
                return "BFILE";
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
                return "NVARCHAR";
            case -2:
                return "RAW";
            case 1:
                return "CHAR";
            case 2:
                return "NUMBER";
            case 3:
                return "DECIMAL";
            case 6:
                return "FLOAT";
            case 8:
                return "DOUBLE";
            case 12:
                return "VARCHAR";
            case 91:
                return "DATE";
            case 93:
                return "TIMESTAMP";
            case 100:
                return "BINARY_FLOAT";
            case 101:
                return "BINARY_DOUBLE";
            case oracle.jdbc.OracleTypes.OTHER /* 1111 */:
            default:
                return null;
            case 2002:
            case 2003:
            case oracle.jdbc.OracleTypes.OPAQUE /* 2007 */:
            case 2008:
                return ((OracleTypeADT) this.types[idx]).getFullName();
            case oracle.jdbc.OracleTypes.BLOB /* 2004 */:
                return "BLOB";
            case oracle.jdbc.OracleTypes.CLOB /* 2005 */:
                return "CLOB";
            case 2006:
                return "REF " + ((OracleTypeREF) this.types[idx]).getFullName();
            case oracle.jdbc.OracleTypes.NCLOB /* 2011 */:
                return "NCLOB";
        }
    }

    @Override // java.sql.ResultSetMetaData
    public boolean isReadOnly(int column) throws SQLException {
        return false;
    }

    @Override // java.sql.ResultSetMetaData
    public boolean isWritable(int column) throws SQLException {
        return false;
    }

    @Override // java.sql.ResultSetMetaData
    public boolean isDefinitelyWritable(int column) throws SQLException {
        return false;
    }

    @Override // java.sql.ResultSetMetaData
    public String getColumnClassName(int column) throws SQLException {
        int type = getColumnType(column);
        switch (type) {
            case oracle.jdbc.OracleTypes.INTERVALDS /* -104 */:
                return "oracle.sql.INTERVALDS";
            case oracle.jdbc.OracleTypes.INTERVALYM /* -103 */:
                return "oracle.sql.INTERVALYM";
            case oracle.jdbc.OracleTypes.TIMESTAMPLTZ /* -102 */:
                return "oracle.sql.TIMESTAMPLTZ";
            case oracle.jdbc.OracleTypes.TIMESTAMPTZ /* -101 */:
                return "oracle.sql.TIMESTAMPTZ";
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case 1:
            case 12:
                return "java.lang.String";
            case oracle.jdbc.OracleTypes.BFILE /* -13 */:
                return oracle.jdbc.OracleBfile.class.getName();
            case -2:
                return "byte[]";
            case 2:
            case 3:
            case 6:
            case 8:
                return "java.math.BigDecimal";
            case 91:
                return "java.sql.Timestamp";
            case 93:
                return "oracle.sql.TIMESTAMP";
            case oracle.jdbc.OracleTypes.OTHER /* 1111 */:
            default:
                return null;
            case 2002:
            case 2008:
                return oracle.jdbc.OracleStruct.class.getName();
            case 2003:
                return oracle.jdbc.OracleArray.class.getName();
            case oracle.jdbc.OracleTypes.BLOB /* 2004 */:
                return oracle.jdbc.OracleBlob.class.getName();
            case oracle.jdbc.OracleTypes.CLOB /* 2005 */:
                return oracle.jdbc.OracleClob.class.getName();
            case 2006:
                return oracle.jdbc.OracleRef.class.getName();
            case oracle.jdbc.OracleTypes.OPAQUE /* 2007 */:
                return OracleOpaque.class.getName();
            case oracle.jdbc.OracleTypes.NCLOB /* 2011 */:
                return oracle.jdbc.OracleNClob.class.getName();
        }
    }

    @Override // oracle.jdbc.StructMetaData
    public String getOracleColumnClassName(int column) throws SQLException {
        int type = getColumnType(column);
        switch (type) {
            case oracle.jdbc.OracleTypes.INTERVALDS /* -104 */:
                return "INTERVALDS";
            case oracle.jdbc.OracleTypes.INTERVALYM /* -103 */:
                return "INTERVALYM";
            case oracle.jdbc.OracleTypes.TIMESTAMPLTZ /* -102 */:
                return "TIMESTAMPLTZ";
            case oracle.jdbc.OracleTypes.TIMESTAMPTZ /* -101 */:
                return "TIMESTAMPTZ";
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case 1:
            case 12:
                return "CHAR";
            case oracle.jdbc.OracleTypes.BFILE /* -13 */:
                return "BFILE";
            case -2:
                return "RAW";
            case 2:
            case 3:
            case 6:
            case 8:
                return "NUMBER";
            case 91:
                return "DATE";
            case 93:
                return "TIMESTAMP";
            case oracle.jdbc.OracleTypes.OTHER /* 1111 */:
            default:
                return null;
            case 2002:
                return "STRUCT";
            case 2003:
                return "ARRAY";
            case oracle.jdbc.OracleTypes.BLOB /* 2004 */:
                return "BLOB";
            case oracle.jdbc.OracleTypes.CLOB /* 2005 */:
                return "CLOB";
            case 2006:
                return "REF";
            case oracle.jdbc.OracleTypes.OPAQUE /* 2007 */:
                return "OPAQUE";
            case 2008:
                return "JAVA_STRUCT";
            case oracle.jdbc.OracleTypes.NCLOB /* 2011 */:
                return "NCLOB";
        }
    }

    @Override // oracle.jdbc.StructMetaData
    public int getLocalColumnCount() throws SQLException {
        return this.descriptor.getLocalAttributeCount();
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public boolean isColumnInvisible(int column) throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public boolean isColumnJSON(int column) throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public String getDomainName(int column) throws SQLException {
        return null;
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public String getDomainSchema(int column) throws SQLException {
        return null;
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public Map<String, String> getAnnotations(int column) throws SQLException {
        return null;
    }

    @Override // oracle.jdbc.StructMetaData
    public boolean isInherited(int column) throws SQLException {
        return column <= getColumnCount() - getLocalColumnCount();
    }

    @Override // oracle.jdbc.StructMetaData
    public String getAttributeJavaName(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        return this.descriptor.getAttributeJavaName(idx);
    }

    private int getValidColumnIndex(int column) throws SQLException {
        int index = column - 1;
        if (index < 0 || index >= this.types.length) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 3, "getValidColumnIndex").fillInStackTrace());
        }
        return index;
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public boolean isNCHAR(int column) throws SQLException {
        int idx = getValidColumnIndex(column);
        return this.types[idx].isNCHAR();
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        if (iface.isInterface()) {
            return iface.isInstance(this);
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 177).fillInStackTrace());
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> iface) throws SQLException {
        if (iface.isInterface() && iface.isInstance(this)) {
            return this;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 177).fillInStackTrace());
    }

    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    @Override // oracle.jdbc.OracleResultSetMetaData
    public VectorMetaData getVectorMetaData(int columnIndex) throws SQLException {
        int columnType = getColumnType(columnIndex);
        if (!OracleTypes.isVector(columnType)) {
            return null;
        }
        throw new SQLFeatureNotSupportedException();
    }
}
