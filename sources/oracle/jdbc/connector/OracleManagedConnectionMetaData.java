package oracle.jdbc.connector;

import java.sql.SQLException;
import javax.resource.ResourceException;
import javax.resource.spi.EISSystemException;
import javax.resource.spi.ManagedConnectionMetaData;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleDatabaseMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/connector/OracleManagedConnectionMetaData.class */
public class OracleManagedConnectionMetaData implements ManagedConnectionMetaData {
    private OracleManagedConnection managedConnection;
    private OracleDatabaseMetaData databaseMetaData;

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    OracleManagedConnectionMetaData(OracleManagedConnection omc) throws ResourceException, EISSystemException {
        this.managedConnection = null;
        this.databaseMetaData = null;
        try {
            this.managedConnection = omc;
            OracleConnection conn = (OracleConnection) omc.getPhysicalConnection();
            this.databaseMetaData = (OracleDatabaseMetaData) conn.getMetaData();
        } catch (Exception exc) {
            throw new EISSystemException("Exception: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    public String getEISProductName() throws ResourceException, EISSystemException {
        try {
            return this.databaseMetaData.getDatabaseProductName();
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    public String getEISProductVersion() throws ResourceException, EISSystemException {
        try {
            return this.databaseMetaData.getDatabaseProductVersion();
        } catch (Exception exc) {
            throw new EISSystemException("Exception: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    public int getMaxConnections() throws ResourceException, EISSystemException {
        try {
            return this.databaseMetaData.getMaxConnections();
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    public String getUserName() throws ResourceException, EISSystemException {
        try {
            return this.databaseMetaData.getUserName();
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }
}
