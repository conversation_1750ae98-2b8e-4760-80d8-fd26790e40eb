package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_pt_BR.class */
public class ErrorMessagesJson_pt_BR extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Exceção de entrada/saída"}, new Object[]{"26302", "Não há suporte para o ano \"{0}\""}, new Object[]{"26303", "Estouro; valor muito grande: {0}."}, new Object[]{"26304", "Opção sem suporte (não implementada)."}, new Object[]{"26305", "O JSON binário é inválido ou está corrompido."}, new Object[]{"26306", "Versão de JSON binário sem suporte: {0}."}, new Object[]{"26307", "O tamanho da chave codificada UTF-8 não deve ser maior que 256 bytes. A seguinte chave excede esse limite: \"{0}\"."}, new Object[]{"26308", "O JSON especificado é muito grande para ser codificado como JSON binário. O tamanho das imagens codificadas não deve exceder 2GB."}, new Object[]{"26309", "O JSON binário é inválido ou está corrompido. A imagem especificada contém apenas {0} bytes."}, new Object[]{"26310", "O java.time.Period especificado tem dias definidos, mas o intervalo de ano para mês da Oracle não suporta dias."}, new Object[]{"26311", "Gerador fechado antes do fim."}, new Object[]{"26312", "É necessário especificar uma chave de objeto nesse contexto."}, new Object[]{"26313", "Gravação inválida. Um valor completo já foi gravado."}, new Object[]{"26314", "Fim não permitido nesse contexto."}, new Object[]{"26315", "Chave não permitida nesse contexto."}, new Object[]{"26316", "Valor esperado após a chave."}, new Object[]{"26317", "O estado do parser deve ser {0}."}, new Object[]{"26318", "O estado do parser não deve ser {0}."}, new Object[]{"26319", "O parser deve se referir a um valor."}, new Object[]{"26320", "\"{0}\" não é um tipo de wrapper suportado."}, new Object[]{"26321", "Este objeto não pode ser modificado. Para fazer uma cópia modificável, use OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Este array não pode ser modificado. Para fazer uma cópia modificável, use OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "O objeto JSON contém uma chave duplicada: {0}."}, new Object[]{"26324", "Não é possível detectar automaticamente a codificação; caracteres insuficientes."}, new Object[]{"26325", "Token EOF esperado, mas obtido {0}."}, new Object[]{"26326", "Caractere inesperado {0} na linha {1}, coluna {2}."}, new Object[]{"26327", "Caractere inesperado {0} na linha {1}, coluna {2}. Esperado {3}."}, new Object[]{"26328", "Token inválido {0} na linha {1}, coluna {2}. Os tokens esperados são: {3}."}, new Object[]{"26329", "JsonParser#getString() só é válido nos estados KEY_NAME, VALUE_STRING, VALUE_NUMBER do parser, mas o estado atual do parser é {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() só é válido no estado VALUE_NUMBER do parser, mas o estado atual do parser é {0}."}, new Object[]{"26331", "JsonParser#getInt() só é válido no estado VALUE_NUMBER do parser, mas o estado atual do parser é {0}."}, new Object[]{"26332", "JsonParser#getLong() só é válido no estado VALUE_NUMBER do parser, mas o estado atual do parser é {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() só é válido no estado VALUE_NUMBER do parser, mas o estado atual do parser é {0}."}, new Object[]{"26334", "JsonParser#getArray() só é válido no estado VALUE_NUMBER do parser, mas o estado atual do parser é {0}."}, new Object[]{"26335", "JsonParser#getObject() só é válido no estado START_OBJECT do parser, mas o estado atual do parser é {0}."}, new Object[]{"26336", "Não há suporte para um timestamp com uma região. Só há suporte para fusos horários de deslocamento."}, new Object[]{"26337", "Os objetos e matrizes no valor JSON não podem aninhar mais do que {0} níveis"}, new Object[]{"26338", "As chaves de um objeto JSON não podem exceder 65.535 bytes"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
