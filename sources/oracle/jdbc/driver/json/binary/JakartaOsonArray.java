package oracle.jdbc.driver.json.binary;

import jakarta.json.JsonArray;
import jakarta.json.JsonNumber;
import jakarta.json.JsonObject;
import jakarta.json.JsonString;
import jakarta.json.JsonValue;
import jakarta.json.stream.JsonGenerator;
import java.io.StringWriter;
import java.sql.SQLException;
import java.sql.Wrapper;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import oracle.jdbc.driver.json.JakartaGeneratorWrapper;
import oracle.jdbc.driver.json.binary.OsonAbstractArray;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JakartaOsonArray.class */
public class JakartaOsonArray extends OsonAbstractArray implements JsonArray, Wrapper {
    public JakartaOsonArray(OsonContext ctx, int pos) {
        super(ctx, pos);
    }

    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> iface) throws SQLException {
        try {
            return iface.cast(new OsonArrayImpl(new OsonContext(this.ctx), this.pos));
        } catch (ClassCastException e) {
            throw new SQLException(e.getMessage(), e);
        }
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return iface.isAssignableFrom(OsonArrayImpl.class);
    }

    public JsonObject getJsonObject(int i) {
        return getJsonObjectInternal(getOffsetWithError(i));
    }

    public JsonArray getJsonArray(int i) {
        return getArrayInternal(getOffsetWithError(i));
    }

    public JsonNumber getJsonNumber(int i) {
        return (JsonNumber) getValueInternal(getOffsetWithError(i));
    }

    public JsonString getJsonString(int i) {
        return m396get(i);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonAbstractArray
    public String getString(int i) {
        return getJsonString(i).getString();
    }

    public String getString(int i, String defaultValue) {
        if (i < 0 || i >= size()) {
            return defaultValue;
        }
        JsonString jsonStringM396get = m396get(i);
        if (jsonStringM396get.getValueType() != JsonValue.ValueType.STRING) {
            return defaultValue;
        }
        return jsonStringM396get.getString();
    }

    /* JADX WARN: Multi-variable type inference failed */
    public <T extends JsonValue> List<T> getValuesAs(Class<T> c) {
        return this;
    }

    public JsonValue.ValueType getValueType() {
        return JsonValue.ValueType.ARRAY;
    }

    public Iterator<JsonValue> iterator() {
        return new OsonAbstractArray.ValueIter();
    }

    public boolean add(JsonValue e) {
        throw new UnsupportedOperationException();
    }

    public boolean addAll(Collection<? extends JsonValue> c) {
        throw new UnsupportedOperationException();
    }

    public boolean addAll(int index, Collection<? extends JsonValue> c) {
        throw new UnsupportedOperationException();
    }

    /* renamed from: get, reason: merged with bridge method [inline-methods] */
    public JsonValue m396get(int i) {
        return (JsonValue) getInternal(i);
    }

    public JsonValue set(int index, JsonValue element) {
        throw new UnsupportedOperationException();
    }

    public void add(int index, JsonValue element) {
        throw new UnsupportedOperationException();
    }

    /* renamed from: remove, reason: merged with bridge method [inline-methods] */
    public JsonValue m395remove(int index) {
        throw new UnsupportedOperationException();
    }

    public ListIterator<JsonValue> listIterator() {
        return listIterator(0);
    }

    public ListIterator<JsonValue> listIterator(int index) {
        if (index < 0 || index > this.size) {
            throw new IndexOutOfBoundsException();
        }
        return new OsonAbstractArray.ListIter(index);
    }

    public List<JsonValue> subList(int fromIndex, int toIndex) {
        return super.sublist(fromIndex, toIndex);
    }

    public String toString() {
        StringWriter writer = new StringWriter();
        JsonGenerator ser = new JakartaGeneratorWrapper(new JsonSerializerImpl(writer));
        ser.write(this);
        ser.close();
        return writer.toString();
    }
}
