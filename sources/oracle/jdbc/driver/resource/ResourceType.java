package oracle.jdbc.driver.resource;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.function.BiFunction;
import javax.net.ssl.SSLContext;
import oracle.jdbc.AccessToken;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.diagnostics.DefaultTraceEventListenerProvider;
import oracle.jdbc.driver.DefaultJsonProvider;
import oracle.jdbc.spi.AccessTokenProvider;
import oracle.jdbc.spi.ConnectionStringProvider;
import oracle.jdbc.spi.JsonProvider;
import oracle.jdbc.spi.OracleResourceProvider;
import oracle.jdbc.spi.OsonConverter;
import oracle.jdbc.spi.PasswordProvider;
import oracle.jdbc.spi.TlsConfigurationProvider;
import oracle.jdbc.spi.TraceEventListenerProvider;
import oracle.jdbc.spi.UsernameProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ResourceType.class */
public final class ResourceType<T extends OracleResourceProvider, U> {
    public static final ResourceType<ConnectionStringProvider, String> CONNECTION_STRING;
    public static final ResourceType<UsernameProvider, String> USERNAME;
    public static final ResourceType<PasswordProvider, char[]> PASSWORD;
    public static final ResourceType<AccessTokenProvider, AccessToken> ACCESS_TOKEN;
    public static final ResourceType<TlsConfigurationProvider, SSLContext> TLS_CONFIGURATION;
    public static final ResourceType<TraceEventListenerProvider, TraceEventListener> TRACE_EVENT_LISTENER;
    public static final ResourceType<JsonProvider, OsonConverter> JSON_PROVIDER;
    private static final Collection<ResourceType<?, ?>> ALL_TYPES;
    private final String providerNameProperty;
    private final String defaultProviderName;
    private final FutureTask<T> defaultProviderConstructorTask;
    private final Class<T> providerClass;
    private final BiFunction<T, Map<OracleResourceProvider.Parameter, CharSequence>, U> getResourceFunction;

    static {
        List<ResourceType<?, ?>> allTypes = new ArrayList<>(6);
        ResourceType<ConnectionStringProvider, String> resourceType = new ResourceType<>(OracleConnection.CONNECTION_PROPERTY_PROVIDER_CONNECTION_STRING, ConnectionStringProvider.class, (v0, v1) -> {
            return v0.getConnectionString(v1);
        });
        CONNECTION_STRING = resourceType;
        allTypes.add(resourceType);
        ResourceType<UsernameProvider, String> resourceType2 = new ResourceType<>(OracleConnection.CONNECTION_PROPERTY_PROVIDER_USERNAME, UsernameProvider.class, (v0, v1) -> {
            return v0.getUsername(v1);
        });
        USERNAME = resourceType2;
        allTypes.add(resourceType2);
        ResourceType<PasswordProvider, char[]> resourceType3 = new ResourceType<>(OracleConnection.CONNECTION_PROPERTY_PROVIDER_PASSWORD, PasswordProvider.class, (v0, v1) -> {
            return v0.getPassword(v1);
        });
        PASSWORD = resourceType3;
        allTypes.add(resourceType3);
        ResourceType<AccessTokenProvider, AccessToken> resourceType4 = new ResourceType<>(OracleConnection.CONNECTION_PROPERTY_PROVIDER_ACCESS_TOKEN, AccessTokenProvider.class, (v0, v1) -> {
            return v0.getAccessToken(v1);
        });
        ACCESS_TOKEN = resourceType4;
        allTypes.add(resourceType4);
        ResourceType<TlsConfigurationProvider, SSLContext> resourceType5 = new ResourceType<>(OracleConnection.CONNECTION_PROPERTY_PROVIDER_TLS_CONFIGURATION, TlsConfigurationProvider.class, (v0, v1) -> {
            return v0.getSSLContext(v1);
        });
        TLS_CONFIGURATION = resourceType5;
        allTypes.add(resourceType5);
        ResourceType<TraceEventListenerProvider, TraceEventListener> resourceType6 = new ResourceType<>(OracleConnection.CONNECTION_PROPERTY_PROVIDER_TRACE_EVENT_LISTENER, TraceEventListenerProvider.class, (v0, v1) -> {
            return v0.getTraceEventListener(v1);
        }, OracleConnection.CONNECTION_PROPERTY_PROVIDER_TRACE_EVENT_LISTENER_DEFAULT, DefaultTraceEventListenerProvider::new);
        TRACE_EVENT_LISTENER = resourceType6;
        allTypes.add(resourceType6);
        ResourceType<JsonProvider, OsonConverter> resourceType7 = new ResourceType<>(OracleConnection.CONNECTION_PROPERTY_PROVIDER_JSON, JsonProvider.class, (v0, v1) -> {
            return v0.getOsonConverter(v1);
        }, OracleConnection.CONNECTION_PROPERTY_PROVIDER_JSON_DEFAULT, DefaultJsonProvider::new);
        JSON_PROVIDER = resourceType7;
        allTypes.add(resourceType7);
        ALL_TYPES = Collections.unmodifiableList(allTypes);
    }

    public static Collection<ResourceType<?, ?>> allTypes() {
        return ALL_TYPES;
    }

    private ResourceType(String providerNameProperty, Class<T> providerClass, BiFunction<T, Map<OracleResourceProvider.Parameter, CharSequence>, U> getResourceFunction) {
        this(providerNameProperty, providerClass, getResourceFunction, null, () -> {
            return null;
        });
    }

    private ResourceType(String providerNameProperty, Class<T> providerClass, BiFunction<T, Map<OracleResourceProvider.Parameter, CharSequence>, U> getResourceFunction, String defaultProviderName, Callable<T> defaultProviderSupplier) {
        this.providerNameProperty = providerNameProperty;
        this.defaultProviderName = defaultProviderName;
        this.providerClass = providerClass;
        this.getResourceFunction = getResourceFunction;
        this.defaultProviderConstructorTask = new FutureTask<>(defaultProviderSupplier);
    }

    String getProviderNameProperty() {
        return this.providerNameProperty;
    }

    String getDefaultProviderName() {
        return this.defaultProviderName;
    }

    T getDefaultProvider() throws Exception {
        this.defaultProviderConstructorTask.run();
        return this.defaultProviderConstructorTask.get();
    }

    public Class<T> getProviderClass() {
        return this.providerClass;
    }

    public U getResource(T provider, Map<OracleResourceProvider.Parameter, CharSequence> parameterValues) {
        return this.getResourceFunction.apply(provider, parameterValues);
    }
}
