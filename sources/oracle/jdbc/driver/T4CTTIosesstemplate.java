package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIosesstemplate.class */
final class T4CTTIosesstemplate extends T4CTTIfun {
    private static final String CLASS_NAME = T4CTTIosesstemplate.class.getName();
    private oracle.jdbc.internal.StateSignatures stateSignatures;
    private long templateId;
    private byte[] fullOverflow;
    private List<byte[]> deltaOverflows;

    T4CTTIosesstemplate(T4CConnection _conn) {
        super(_conn, (byte) 17);
        setFunCode((short) 164);
    }

    void doOSESSTEMPLATE(oracle.jdbc.internal.DatabaseSessionState state) throws SQLException, IOException {
        this.stateSignatures = state.getStateSignatures();
        this.templateId = state.getId();
        this.fullOverflow = state.getCheckpoint();
        this.deltaOverflows = state.getUpdates();
        if (this.templateId == 0 && (this.fullOverflow == null || this.fullOverflow.length == 0)) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doOSESSTEMPLATE", "Skipped sending piggyback with null overflow", (String) null, (Throwable) null);
        } else {
            doPigRPC();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB8(this.stateSignatures.getSignatureFlags());
        this.meg.marshalUB8(this.stateSignatures.getClientSignature());
        this.meg.marshalUB8(this.stateSignatures.getServerSignature());
        this.meg.marshalUB4(this.stateSignatures.getVersion());
        this.meg.marshalUB8(this.templateId);
        this.meg.marshalPTR();
        this.meg.marshalUB4(1L);
        if (this.fullOverflow != null && this.fullOverflow.length > 0) {
            this.meg.marshalUB4(this.fullOverflow.length);
            this.meg.marshalCLR(this.fullOverflow, 0, this.fullOverflow.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
    }
}
