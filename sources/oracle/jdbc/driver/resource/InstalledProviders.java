package oracle.jdbc.driver.resource;

import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.ServiceConfigurationError;
import java.util.ServiceLoader;
import java.util.TreeMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import oracle.jdbc.spi.OracleResourceProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/InstalledProviders.class */
public final class InstalledProviders<T extends OracleResourceProvider> {
    private static final Logger LOGGER = Logger.getLogger(InstalledProviders.class.getName());
    private final Map<String, T> providers;

    private InstalledProviders(Map<String, T> providers) {
        this.providers = Collections.unmodifiableMap(new HashMap(providers));
    }

    T get(String name) {
        return this.providers.get(name);
    }

    public Collection<String> getNames() {
        return Collections.unmodifiableSet(this.providers.keySet());
    }

    public static <T extends OracleResourceProvider> InstalledProviders<T> load(ResourceType<T, ?> resourceType) {
        Map<String, T> providers = new TreeMap<>((Comparator<? super String>) String.CASE_INSENSITIVE_ORDER);
        loadFromServiceLoader(resourceType.getProviderClass(), providers);
        return new InstalledProviders<>(providers);
    }

    private static <T extends OracleResourceProvider> void loadFromServiceLoader(Class<T> providerClass, Map<String, T> map) {
        try {
            ServiceLoader<T> serviceLoader = ServiceLoader.load(providerClass);
            Iterator<T> it = serviceLoader.iterator();
            while (it.hasNext()) {
                T provider = it.next();
                String name = provider.getName();
                if (name != null) {
                    T tPutIfAbsent = map.putIfAbsent(name, provider);
                    if (tPutIfAbsent != null) {
                        log(Level.WARNING, "loadFromServiceLoader", String.format("Multiple implementations of %s found with name \"%s\". Both %s and %s have the same name.", providerClass.getName(), name, provider.getClass().getName(), tPutIfAbsent.getClass().getName()));
                    }
                }
            }
        } catch (Exception | ServiceConfigurationError exception) {
            log(Level.WARNING, "loadFromServiceLoader", String.format("A failure occurred when loading implementations of %s using java.util.ServiceLoader. This failure may prevent Oracle JDBC from using all available implementations of %1$s.", providerClass.getName()), exception);
        }
    }

    private static void log(Level level, String methodName, String message) {
        LOGGER.logp(level, InstalledProviders.class.getName(), methodName, message);
    }

    private static void log(Level level, String methodName, String message, Throwable throwable) {
        LOGGER.logp(level, InstalledProviders.class.getName(), methodName, message, throwable);
    }
}
