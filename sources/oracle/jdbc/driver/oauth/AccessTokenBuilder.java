package oracle.jdbc.driver.oauth;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;
import java.util.function.Supplier;
import java.util.logging.Level;
import oracle.jdbc.AccessToken;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.PasswordAuthentication;
import oracle.jdbc.driver.resource.DriverResources;
import oracle.jdbc.driver.resource.ResourceProvider;
import oracle.jdbc.driver.resource.ResourceType;
import oracle.jdbc.driver.utils.OracleWalletUtils;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.logging.annotations.Blind;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonString;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/oauth/AccessTokenBuilder.class */
public final class AccessTokenBuilder {
    private Supplier<? extends AccessToken> tokenSupplier;
    private String passwordAuthentication;
    private String userName;
    private OpaqueString password;
    private boolean isSepsCredentials;
    private OpaqueString accessToken;
    private String tokenAuthentication;
    private String tokenLocation;
    private String ociIamUrl;
    private Properties tlsConfig;
    private String ociConfigFile;
    private String ociProfile;
    private String ociTenancy;
    private String ociCompartment;
    private String ociDatabase;
    private String azureDatabaseApplicationIdUri;
    private String tenantId;
    private String clientId;
    private OpaqueString clientSecret;
    private String clientCertificate;
    private OpaqueString clientCertificatePassword;
    private String redirectUri;
    private String azureCredentials;
    private DriverResources driverResources;
    private final Diagnosable diagnosable;
    private static final String CLASS_NAME = AccessTokenBuilder.class.getName();

    public AccessTokenBuilder(Diagnosable diagnosable) {
        this.diagnosable = diagnosable;
    }

    public AccessTokenBuilder tokenSupplier(Supplier<? extends AccessToken> tokenSupplier) {
        this.tokenSupplier = tokenSupplier;
        return this;
    }

    public AccessTokenBuilder accessToken(OpaqueString accessToken) {
        this.accessToken = accessToken;
        return this;
    }

    public AccessTokenBuilder passwordAuthentication(String passwordAuthentication) {
        this.passwordAuthentication = passwordAuthentication;
        return this;
    }

    public AccessTokenBuilder userName(String userName) {
        this.userName = userName;
        return this;
    }

    public AccessTokenBuilder password(OpaqueString password) {
        this.password = password;
        return this;
    }

    public AccessTokenBuilder isSepsCredentials(boolean isSepsCredentials) {
        this.isSepsCredentials = isSepsCredentials;
        return this;
    }

    public AccessTokenBuilder tokenAuthentication(String tokenAuthentication) {
        this.tokenAuthentication = tokenAuthentication;
        return this;
    }

    public AccessTokenBuilder tokenLocation(String tokenLocation) {
        this.tokenLocation = tokenLocation;
        return this;
    }

    public AccessTokenBuilder ociIamUrl(String ociIamUrl) {
        this.ociIamUrl = ociIamUrl;
        return this;
    }

    public AccessTokenBuilder tlsConfig(Properties tlsConfig) {
        this.tlsConfig = tlsConfig;
        return this;
    }

    public AccessTokenBuilder ociConfigFile(String ociConfigFile) {
        this.ociConfigFile = ociConfigFile;
        return this;
    }

    public AccessTokenBuilder ociProfile(String ociProfile) {
        this.ociProfile = ociProfile;
        return this;
    }

    public AccessTokenBuilder ociTenancy(String ociTenancy) {
        this.ociTenancy = ociTenancy;
        return this;
    }

    public AccessTokenBuilder ociCompartment(String ociCompartment) {
        this.ociCompartment = ociCompartment;
        return this;
    }

    public AccessTokenBuilder ociDatabase(String ociDatabase) {
        this.ociDatabase = ociDatabase;
        return this;
    }

    public AccessTokenBuilder azureDatabaseApplicationIdUri(String azureDatabaseApplicationIdUri) {
        this.azureDatabaseApplicationIdUri = azureDatabaseApplicationIdUri;
        return this;
    }

    public AccessTokenBuilder tenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public AccessTokenBuilder clientId(String clientId) {
        this.clientId = clientId;
        return this;
    }

    public AccessTokenBuilder clientSecret(OpaqueString clientSecret) {
        this.clientSecret = clientSecret;
        return this;
    }

    public AccessTokenBuilder clientCertificate(String clientCertificate) {
        this.clientCertificate = clientCertificate;
        return this;
    }

    public AccessTokenBuilder clientCertificatePassword(OpaqueString clientCertificatePassword) {
        this.clientCertificatePassword = clientCertificatePassword;
        return this;
    }

    public AccessTokenBuilder redirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
        return this;
    }

    public AccessTokenBuilder driverResources(DriverResources driverResources) {
        this.driverResources = driverResources;
        return this;
    }

    public AccessTokenBuilder azureCredentials(String azureCredentials) {
        this.azureCredentials = azureCredentials;
        return this;
    }

    @Blind
    public AccessToken build() throws SQLException {
        AccessToken token = getToken();
        validateAzureToken(token);
        return token;
    }

    private AccessToken getToken() throws SQLException {
        boolean isUserNameConfigured = (this.userName == null || this.userName.isEmpty()) ? false : true;
        if (this.isSepsCredentials || (!isUserNameConfigured && (OpaqueString.isNull(this.password) || this.password.isEmpty()))) {
            if (this.tokenSupplier != null) {
                return getTokenFromSupplier();
            }
            if (!OpaqueString.isNull(this.accessToken)) {
                return getTokenFromProperty();
            }
            if (this.tokenAuthentication != null) {
                return getTokenFromTokenAuthentication();
            }
            if (this.driverResources != null && this.driverResources.isProviderConfigured(ResourceType.ACCESS_TOKEN)) {
                return getTokenFromProvider();
            }
        }
        if (!this.isSepsCredentials && isUserNameConfigured && TokenAuthentication.isInteractive(this.tokenAuthentication)) {
            return getTokenFromTokenAuthentication();
        }
        return getTokenFromPasswordAuthentication();
    }

    @Blind
    private AccessToken getTokenFromSupplier() throws SQLException {
        try {
            this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromSupplier", "Getting access token from a Supplier<AccessToken>", null, null);
            AccessToken accessToken = this.tokenSupplier.get();
            if (accessToken != null) {
                return accessToken;
            }
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "A null value was output by the Supplier configured with  OracleDataSource.setTokenSupplier(Supplier)").fillInStackTrace());
        } catch (RuntimeException runtimeException) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "An exception was thrown by the Supplier configured with  OracleDataSource.setTokenSupplier(Supplier).", runtimeException).fillInStackTrace());
        }
    }

    @Blind
    private AccessToken getTokenFromProperty() throws SQLException {
        this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromProperty", "Getting access token configured with oracle.jdbc.accessToken ", null, null);
        char[] clearText = this.accessToken.getChars();
        try {
            try {
                AccessToken accessTokenCreateJsonWebToken = AccessToken.createJsonWebToken(clearText);
                Arrays.fill(clearText, (char) 0);
                return accessTokenCreateJsonWebToken;
            } catch (Exception exception) {
                SQLException invalidConnectionProperty = DatabaseError.formatSqlException(null, DatabaseError.EOJ_INVALID_CONNECTION_PROPERTY, null, exception, "[OMITTED]", oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_ACCESS_TOKEN);
                throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.ORAERROR_INVALID_TOKEN, invalidConnectionProperty.getMessage() + ": " + exception.getMessage(), invalidConnectionProperty).fillInStackTrace());
            }
        } catch (Throwable th) {
            Arrays.fill(clearText, (char) 0);
            throw th;
        }
    }

    private AccessToken getTokenFromTokenAuthentication() throws SQLException {
        TokenAuthentication tokenAuthentication = TokenAuthentication.parseProperty(this.tokenAuthentication);
        switch (tokenAuthentication) {
            case OCI_TOKEN:
            case OAUTH:
                return getTokenFromFile(tokenAuthentication);
            case OCI_API_KEY:
            case OCI_INSTANCE_PRINCIPAL:
            case OCI_RESOURCE_PRINCIPAL:
            case OCI_DELEGATION_TOKEN:
            case OCI_INTERACTIVE:
            case OCI_DEFAULT:
                return getTokenFromOciPlugin(tokenAuthentication);
            case AZURE_SERVICE_PRINCIPAL:
            case AZURE_MANAGED_IDENTITY:
            case AZURE_DEVICE_CODE:
            case AZURE_INTERACTIVE:
            case AZURE_DEFAULT:
                return getTokenFromAzurePlugin(tokenAuthentication);
            default:
                throw new IllegalStateException("Unexpected value: " + tokenAuthentication);
        }
    }

    @Blind
    private AccessToken getTokenFromFile(TokenAuthentication authenticationMethod) throws SQLException {
        Path tokenPath;
        JsonWebToken jsonWebTokenFromFile;
        this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromFile", "Getting access token from the file system. tokenAuthentication={0}, tokenLocation={1}", null, null, this.tokenAuthentication, this.tokenLocation);
        boolean isOciToken = TokenAuthentication.OCI_TOKEN == authenticationMethod;
        if (!isOciToken && TokenAuthentication.OAUTH != authenticationMethod) {
            throw ((SQLException) DatabaseError.formatSqlException(null, DatabaseError.EOJ_INVALID_CONNECTION_PROPERTY, null, null, this.tokenAuthentication, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TOKEN_AUTHENTICATION).fillInStackTrace());
        }
        if (this.tokenLocation != null) {
            tokenPath = Paths.get(this.tokenLocation, new String[0]);
        } else if (isOciToken) {
            String userHome = System.getProperty("user.home");
            if (userHome == null) {
                throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, "System property \"user.home\" is not set. The default token location can not be resolved", DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE).fillInStackTrace());
            }
            tokenPath = Paths.get(userHome, ".oci", "db-token");
        } else {
            throw ((SQLException) DatabaseError.formatSqlException(null, DatabaseError.EOJ_MISSING_CONNECTION_PROPERTY, "This property must be set when oracle.jdbc.tokenAuthentication=" + this.tokenAuthentication, null, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TOKEN_LOCATION).fillInStackTrace());
        }
        try {
            if (isOciToken) {
                jsonWebTokenFromFile = JsonWebToken.fromOciFile(tokenPath);
            } else {
                jsonWebTokenFromFile = JsonWebToken.fromFile(tokenPath);
            }
            return jsonWebTokenFromFile;
        } catch (IOException ioException) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Connection property oracle.jdbc.tokenLocation locates a token file or private key file that can not be read.", ioException).fillInStackTrace());
        } catch (IllegalArgumentException illegalArgumentException) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.ORAERROR_INVALID_TOKEN, "An invalid token was configured by connection property oracle.jdbc.tokenLocation", illegalArgumentException).fillInStackTrace());
        } catch (GeneralSecurityException exception) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Connection property oracle.jdbc.tokenLocation locates a private key file that can not be read.", exception).fillInStackTrace());
        } catch (SQLException exception2) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Connection property oracle.jdbc.tokenLocation locates a private key file that cannot be read.", exception2).fillInStackTrace());
        }
    }

    private AccessToken getTokenFromOciPlugin(TokenAuthentication tokenAuthentication) throws SQLException {
        this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromOciPlugin", "Getting access token from the OCI token provider. tokenAuthentication={0}, ociTenancy={1}, ociCompartment={2}, ociDatabase={3}, ociConfigFile={4}, ociProfile={5}", null, null, tokenAuthentication, this.ociTenancy, this.ociCompartment, this.ociDatabase, this.ociConfigFile, this.ociProfile);
        ResourceProvider.Builder<?, AccessToken> builder = ResourceProvider.builder("ojdbc-provider-oci-token", ResourceType.ACCESS_TOKEN).parameterValue("authenticationMethod", tokenAuthentication.parameterValue).parameterValue("scope", composeOciScope()).parameterValue("tenantId", this.ociTenancy).parameterValue("configFile", this.ociConfigFile).parameterValue("profile", this.ociProfile);
        if (tokenAuthentication.isInteractive()) {
            builder.parameterValue("username", this.userName);
        }
        return (AccessToken) builder.build().getResource();
    }

    private String composeOciScope() throws SQLException {
        if (this.ociCompartment != null || this.ociDatabase == null) {
            return "urn:oracle:db::id::" + (this.ociCompartment == null ? "*" : this.ociDatabase == null ? this.ociCompartment : this.ociCompartment + "::" + this.ociDatabase);
        }
        throw ((SQLException) DatabaseError.formatSqlException(null, DatabaseError.EOJ_MISSING_CONNECTION_PROPERTY, "This property must be set when oracle.jdbc.ociDatabase is set.", null, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_OCI_COMPARTMENT).fillInStackTrace());
    }

    @Blind
    private AccessToken getTokenFromAzurePlugin(TokenAuthentication tokenAuthentication) throws Throwable {
        String clientId = this.clientId;
        OpaqueString clientSecret = this.clientSecret;
        if (this.azureCredentials != null && TokenAuthentication.AZURE_SERVICE_PRINCIPAL == tokenAuthentication && OpaqueString.isNull(clientSecret) && this.clientCertificate == null) {
            if (clientId == null) {
                throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "A client ID must be configured when connection property oracle.jdbc.azureCredentials is configured.").fillInStackTrace());
            }
            this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromAzurePlugin", "Reading client secret from wallet", null, null);
            clientSecret = OracleWalletUtils.getSecret(this.azureCredentials, null, "oracle.security.azure.credential." + clientId);
        } else if (this.isSepsCredentials && TokenAuthentication.AZURE_SERVICE_PRINCIPAL == tokenAuthentication) {
            if (clientId == null) {
                this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromAzurePlugin", "Using SEPS username as client ID.", null, null);
                clientId = this.userName;
            }
            if (OpaqueString.isNull(clientSecret) && this.clientCertificate == null) {
                this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromAzurePlugin", "Using SEPS password as client secret.", null, null);
                clientSecret = this.password;
            }
        }
        this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromAzurePlugin", "Getting access token from the Azure token provider. tokenAuthentication={0}, clientId={1}, isClientSecretConfigured={2}, clientCertificate={3}, isClientCertificatePasswordConfigured={4}, redirectUri={5}, tenantId={6}, azureDatabaseApplicationIdUri={7}", null, null, tokenAuthentication, clientId, Boolean.valueOf(!OpaqueString.isNull(clientSecret)), this.clientCertificate, Boolean.valueOf(!OpaqueString.isNull(this.clientCertificatePassword)), this.redirectUri, this.tenantId, this.azureDatabaseApplicationIdUri);
        ResourceProvider.Builder<?, AccessToken> builder = createAzureTokenBuilder(tokenAuthentication.parameterValue).parameterValue("clientId", clientId).parameterValue("clientSecret", clientSecret).parameterValue("clientCertificatePath", this.clientCertificate).parameterValue("clientCertificatePassword", this.clientCertificatePassword).parameterValue("redirectUri", this.redirectUri);
        if (tokenAuthentication.isInteractive()) {
            builder.parameterValue("username", this.userName);
        }
        return (AccessToken) builder.build().getResource();
    }

    private ResourceProvider.Builder<?, AccessToken> createAzureTokenBuilder(String authenticationMethod) throws SQLException {
        return ResourceProvider.builder("ojdbc-provider-azure-token", ResourceType.ACCESS_TOKEN).parameterValue("authenticationMethod", authenticationMethod).parameterValue("scope", composeAzureScope()).parameterValue("tenantId", this.tenantId);
    }

    private String composeAzureScope() throws SQLException {
        if (this.azureDatabaseApplicationIdUri == null) {
            throw ((SQLException) DatabaseError.formatSqlException(null, DatabaseError.EOJ_MISSING_CONNECTION_PROPERTY, "This property must be set when oracle.jdbc.tokenAuthentication is set to " + this.tokenAuthentication, null, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_AZURE_DB_APP_ID_URI).fillInStackTrace());
        }
        return this.azureDatabaseApplicationIdUri + "/.default";
    }

    private AccessToken getTokenFromPasswordAuthentication() throws SQLException {
        PasswordAuthentication authenticationMethod = PasswordAuthentication.parseProperty(this.passwordAuthentication);
        switch (authenticationMethod) {
            case OCI_TOKEN:
                return getTokenFromIam();
            case AZURE_TOKEN:
                this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromPasswordAuthentication", "Getting access token from the Azure token provider using password authentication. clientId={0}, tenantId={1}, azureDatabaseApplicationIdUri={2}", null, null, this.clientId, this.tenantId, this.azureDatabaseApplicationIdUri);
                return (AccessToken) createAzureTokenBuilder("password").parameterValue("clientId", this.clientId).parameterValue("username", this.userName).parameterValue("password", this.password).build().getResource();
            default:
                return null;
        }
    }

    @Blind
    private AccessToken getTokenFromIam() throws SQLException {
        if (this.ociIamUrl == null) {
            throw ((SQLException) DatabaseError.formatSqlException(null, DatabaseError.EOJ_MISSING_CONNECTION_PROPERTY, "This property must be set when oracle.jdbc.passwordAuthentication=" + PasswordAuthentication.OCI_TOKEN, null, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_OCI_IAM_URL).fillInStackTrace());
        }
        if (this.ociTenancy == null) {
            throw ((SQLException) DatabaseError.formatSqlException(null, DatabaseError.EOJ_MISSING_CONNECTION_PROPERTY, "This property must be set when oracle.jdbc.passwordAuthentication=" + PasswordAuthentication.OCI_TOKEN, null, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_OCI_TENANCY).fillInStackTrace());
        }
        if (this.ociDatabase != null && this.ociCompartment == null) {
            throw ((SQLException) DatabaseError.formatSqlException(null, DatabaseError.EOJ_MISSING_CONNECTION_PROPERTY, "This property must be set when oracle.jdbc.ociDatabase is set.", null, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_OCI_COMPARTMENT).fillInStackTrace());
        }
        if (this.userName == null) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "A user name must be provided when connection property oracle.jdbc.passwordAuthentication=" + PasswordAuthentication.OCI_TOKEN).fillInStackTrace());
        }
        if (this.password == null || this.password.isNull()) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "A password must be provided when connection property oracle.jdbc.passwordAuthentication=" + PasswordAuthentication.OCI_TOKEN).fillInStackTrace());
        }
        try {
            this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromIam", "Getting access token from IAM using password authentication. ociIamUrl={0}, ociTenancy={1}, ociCompartment={2}, ociDatabase={3}", null, null, this.ociIamUrl, this.ociTenancy, this.ociCompartment, this.ociDatabase);
            return IamDataplaneClient.requestBuilder().endPoint(this.ociIamUrl).tenancy(this.ociTenancy).compartment(this.ociCompartment).database(this.ociDatabase).user(this.userName).password(this.password).tlsConfig(this.tlsConfig).build();
        } catch (IOException ioException) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Failed to request a token from IAM endpoint: " + this.ociIamUrl + ", with tenant OCID: " + this.ociTenancy + (this.ociCompartment == null ? "" : ", with compartment OCID: " + this.ociCompartment) + (this.ociDatabase == null ? "" : ", with database OCID: " + this.ociDatabase), ioException).fillInStackTrace());
        }
    }

    @Blind
    private AccessToken getTokenFromProvider() throws SQLException {
        this.diagnosable.debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getTokenFromProvider", "Getting access token from AccessTokenProvider identified by oracle.jdbc.provider.accessToken", null, null);
        return (AccessToken) this.driverResources.getResource(ResourceType.ACCESS_TOKEN);
    }

    private void validateAzureToken(AccessToken token) throws SQLException {
        try {
            if (!(token instanceof JsonWebToken)) {
                return;
            }
            OracleJsonObject claims = ((JsonWebToken) token).getClaims();
            OracleJsonValue iss = claims.get("iss");
            if (!(iss instanceof OracleJsonString) || !iss.asJsonString().getString().startsWith("https://login.microsoftonline.com/")) {
                return;
            }
            OracleJsonValue ver = claims.get("ver");
            if ((ver instanceof OracleJsonString) && ver.asJsonString().getString().equals("2.0") && !claims.containsKey("roles")) {
                if (claims.containsKey("upn")) {
                } else {
                    throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.ORAERROR_INVALID_ENTRA_TOKEN).fillInStackTrace());
                }
            }
        } catch (Exception exception) {
            this.diagnosable.debug(Level.WARNING, SecurityLabel.KEYS, CLASS_NAME, "validateAzureToken", "Unable to validate token due to an unexpected exception", null, exception);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/oauth/AccessTokenBuilder$TokenAuthentication.class */
    enum TokenAuthentication {
        OCI_TOKEN(null),
        OCI_API_KEY("config-file"),
        OCI_INSTANCE_PRINCIPAL("instance-principal"),
        OCI_RESOURCE_PRINCIPAL("resource-principal"),
        OCI_DELEGATION_TOKEN("cloud-shell"),
        OCI_INTERACTIVE("interactive"),
        OCI_DEFAULT("auto-detect"),
        OAUTH(null),
        AZURE_SERVICE_PRINCIPAL("service-principal"),
        AZURE_MANAGED_IDENTITY("managed-identity"),
        AZURE_DEVICE_CODE("device-code"),
        AZURE_INTERACTIVE("interactive"),
        AZURE_DEFAULT("auto-detect");

        private final String parameterValue;
        private static final Map<String, TokenAuthentication> PROPERTY_VALUES;

        static {
            TreeMap<String, TokenAuthentication> propertyValues = new TreeMap<>((Comparator<? super String>) String.CASE_INSENSITIVE_ORDER);
            for (TokenAuthentication tokenAuthentication : values()) {
                propertyValues.put(tokenAuthentication.name(), tokenAuthentication);
            }
            PROPERTY_VALUES = Collections.unmodifiableMap(propertyValues);
        }

        TokenAuthentication(String parameterValue) {
            this.parameterValue = parameterValue;
        }

        boolean isInteractive() {
            switch (this) {
                case OCI_INTERACTIVE:
                case AZURE_DEVICE_CODE:
                case AZURE_INTERACTIVE:
                    return true;
                default:
                    return false;
            }
        }

        static boolean isInteractive(String propertyValue) {
            TokenAuthentication tokenAuthentication;
            if (propertyValue == null || (tokenAuthentication = PROPERTY_VALUES.get(propertyValue)) == null) {
                return false;
            }
            return tokenAuthentication.isInteractive();
        }

        static TokenAuthentication parseProperty(String propertyValue) throws SQLException {
            if (propertyValue == null) {
                return null;
            }
            TokenAuthentication tokenAuthentication = PROPERTY_VALUES.get(propertyValue);
            if (tokenAuthentication == null) {
                throw ((SQLException) DatabaseError.formatSqlException(null, DatabaseError.EOJ_INVALID_CONNECTION_PROPERTY, null, null, propertyValue, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TOKEN_AUTHENTICATION).fillInStackTrace());
            }
            return tokenAuthentication;
        }
    }
}
