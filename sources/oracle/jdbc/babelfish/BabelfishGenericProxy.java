package oracle.jdbc.babelfish;

import java.lang.reflect.Method;
import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.DatabaseMetaData;
import java.sql.NClob;
import java.sql.ParameterMetaData;
import java.sql.Ref;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.RowId;
import java.sql.SQLData;
import java.sql.SQLException;
import java.sql.SQLInput;
import java.sql.SQLOutput;
import java.sql.SQLXML;
import java.sql.Savepoint;
import java.sql.Struct;
import java.sql.Wrapper;
import oracle.jdbc.OracleResultSet;
import oracle.jdbc.proxy.annotation.GetCreator;
import oracle.jdbc.proxy.annotation.GetDelegate;
import oracle.jdbc.proxy.annotation.OnError;
import oracle.jdbc.proxy.annotation.Post;
import oracle.jdbc.proxy.annotation.ProxyFor;

@ProxyFor({Array.class, Blob.class, Clob.class, DatabaseMetaData.class, NClob.class, ParameterMetaData.class, Ref.class, ResultSet.class, ResultSetMetaData.class, RowId.class, Savepoint.class, SQLData.class, SQLInput.class, SQLOutput.class, SQLXML.class, Struct.class, Wrapper.class, OracleResultSet.class})
/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/BabelfishGenericProxy.class */
public abstract class BabelfishGenericProxy {
    Translator translator;

    @GetCreator
    protected abstract Object getCreator();

    @GetDelegate
    protected abstract Object getDelegate();

    @OnError(SQLException.class)
    protected Object translateError(Method m, SQLException ex) throws SQLException {
        throw this.translator.translateError(ex);
    }

    @Post
    protected Object post_Methods(Method m, Object result) {
        if (result instanceof BabelfishGenericProxy) {
            ((BabelfishGenericProxy) result).setTranslator(this.translator);
        }
        return result;
    }

    public void setTranslator(Translator translator) {
        this.translator = translator;
    }
}
