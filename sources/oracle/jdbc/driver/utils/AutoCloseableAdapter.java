package oracle.jdbc.driver.utils;

import java.lang.Exception;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/AutoCloseableAdapter.class */
public interface AutoCloseableAdapter<E extends Exception> extends AutoCloseable {
    @Override // java.lang.AutoCloseable
    void close() throws Exception;

    static <E extends Exception> AutoCloseableAdapter<E> adapt(ThrowingRunnable<E> throwingRunnable) {
        throwingRunnable.getClass();
        return throwingRunnable::runOrThrow;
    }

    static <E extends Exception> AutoCloseableAdapter<E> adapt(Iterable<? extends ThrowingRunnable<E>> autoCloseables, Class<E> exceptionClass) {
        return () -> {
            closeAll(autoCloseables, exceptionClass);
        };
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: E extends java.lang.Exception */
    static <E extends Exception> void closeAll(Iterable<? extends ThrowingRunnable<E>> throwingRunnables, Class<E> exceptionClass) throws Exception {
        Exception exception = null;
        for (ThrowingRunnable<E> throwingRunnable : throwingRunnables) {
            try {
                throwingRunnable.runOrThrow();
            } catch (Exception thrown) {
                if (exception == null) {
                    exception = thrown;
                } else {
                    exception.addSuppressed(thrown);
                }
            }
        }
        if (exception == null) {
            return;
        }
        if (exceptionClass.isInstance(exception)) {
            throw exceptionClass.cast(exception);
        }
        throw CheckedExceptionHandler.toRuntimeException(exception);
    }
}
