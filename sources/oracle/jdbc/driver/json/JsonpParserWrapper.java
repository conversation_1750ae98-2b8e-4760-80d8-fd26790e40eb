package oracle.jdbc.driver.json;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Wrapper;
import javax.json.JsonArray;
import javax.json.JsonException;
import javax.json.JsonObject;
import javax.json.JsonValue;
import javax.json.stream.JsonLocation;
import javax.json.stream.JsonParser;
import oracle.jdbc.driver.json.binary.OsonParserImpl;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonParser;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/JsonpParserWrapper.class */
public class JsonpParserWrapper implements Wrapper, JsonParser {
    OracleJsonParser wrapped;

    public JsonpParserWrapper(OracleJsonParser wrapped) {
        this.wrapped = wrapped;
    }

    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> cls) throws SQLException {
        if (cls.isInstance(this.wrapped)) {
            return (T) this.wrapped;
        }
        throw new SQLException(OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, cls.getName()).getMessage());
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return iface.isInstance(this.wrapped);
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.json.JsonException */
    public void close() throws JsonException {
        try {
            this.wrapped.close();
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    public BigDecimal getBigDecimal() {
        return this.wrapped.getBigDecimal();
    }

    public int getInt() {
        return this.wrapped.getInt();
    }

    public JsonLocation getLocation() {
        JsonLocation NO_LOCATION = new JsonLocation() { // from class: oracle.jdbc.driver.json.JsonpParserWrapper.1
            public long getColumnNumber() {
                return -1L;
            }

            public long getLineNumber() {
                return -1L;
            }

            public long getStreamOffset() {
                if (JsonpParserWrapper.this.wrapped instanceof OsonParserImpl) {
                    return ((OsonParserImpl) JsonpParserWrapper.this.wrapped).getStreamOffset();
                }
                return -1L;
            }
        };
        return NO_LOCATION;
    }

    public long getLong() {
        return this.wrapped.getLong();
    }

    public String getString() {
        return this.wrapped.getString();
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.json.JsonException */
    public boolean hasNext() throws JsonException {
        try {
            return this.wrapped.hasNext();
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    public boolean isIntegralNumber() {
        return this.wrapped.isIntegralNumber();
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.json.JsonException */
    public JsonParser.Event next() throws JsonException {
        try {
            OracleJsonParser.Event event = this.wrapped.next();
            switch (event) {
                case END_ARRAY:
                    return JsonParser.Event.END_ARRAY;
                case END_OBJECT:
                    return JsonParser.Event.END_OBJECT;
                case KEY_NAME:
                    return JsonParser.Event.KEY_NAME;
                case START_ARRAY:
                    return JsonParser.Event.START_ARRAY;
                case START_OBJECT:
                    return JsonParser.Event.START_OBJECT;
                case VALUE_BINARY:
                case VALUE_TIMESTAMP:
                case VALUE_TIMESTAMPTZ:
                case VALUE_DATE:
                case VALUE_INTERVALDS:
                case VALUE_INTERVALYM:
                case VALUE_STRING:
                    return JsonParser.Event.VALUE_STRING;
                case VALUE_DOUBLE:
                case VALUE_FLOAT:
                case VALUE_DECIMAL:
                    return JsonParser.Event.VALUE_NUMBER;
                case VALUE_FALSE:
                    return JsonParser.Event.VALUE_FALSE;
                case VALUE_TRUE:
                    return JsonParser.Event.VALUE_TRUE;
                case VALUE_NULL:
                    return JsonParser.Event.VALUE_NULL;
                case VALUE_VECTOR:
                default:
                    throw new UnsupportedOperationException(event.toString());
            }
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.json.JsonException */
    public JsonValue getValue() throws JsonException {
        try {
            return (JsonValue) this.wrapped.getValue().wrap(JsonValue.class);
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.json.JsonException */
    public JsonObject getObject() throws JsonException {
        try {
            return (JsonObject) this.wrapped.getObject().wrap(JsonObject.class);
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.json.JsonException */
    public JsonArray getArray() throws JsonException {
        try {
            return (JsonArray) this.wrapped.getArray().wrap(JsonArray.class);
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.json.JsonException */
    public void skipObject() throws JsonException {
        try {
            this.wrapped.skipObject();
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.json.JsonException */
    public void skipArray() throws JsonException {
        try {
            this.wrapped.skipArray();
        } catch (OracleJsonException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }
}
