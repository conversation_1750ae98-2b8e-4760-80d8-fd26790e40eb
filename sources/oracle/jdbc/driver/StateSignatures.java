package oracle.jdbc.driver;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/StateSignatures.class */
class StateSignatures implements oracle.jdbc.internal.StateSignatures {
    long signatureFlags;
    long clientSignature;
    long serverSignature;
    long version;

    StateSignatures(long signatureFlags, long clientSignature, long serverSignature) {
        this(signatureFlags, clientSignature, serverSignature, 0L);
    }

    StateSignatures(long signatureFlags, long clientSignature, long serverSignature, long version) {
        this.signatureFlags = signatureFlags;
        this.clientSignature = clientSignature;
        this.serverSignature = serverSignature;
        this.version = version;
    }

    @Override // oracle.jdbc.internal.StateSignatures
    public long getSignatureFlags() {
        return this.signatureFlags;
    }

    @Override // oracle.jdbc.internal.StateSignatures
    public long getClientSignature() {
        return this.clientSignature;
    }

    @Override // oracle.jdbc.internal.StateSignatures
    public long getServerSignature() {
        return this.serverSignature;
    }

    @Override // oracle.jdbc.internal.StateSignatures
    public long getVersion() {
        return this.version;
    }

    StateSignatures copy() {
        return new StateSignatures(this.signatureFlags, this.clientSignature, this.serverSignature, this.version);
    }

    public String toString() {
        return "StateSignatures[SignatureFlags=" + Long.toHexString(getSignatureFlags()) + ", ClientSignature=" + Long.toHexString(getClientSignature()) + ", ServerSignature=" + Long.toHexString(getServerSignature()) + ", Version=" + Long.toHexString(getVersion()) + "]";
    }
}
