package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_fi.class */
public class ErrorMessagesJson_fi extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Tapahtui I/O-poikkeus"}, new Object[]{"26302", "Vuotta {0} ei tueta"}, new Object[]{"26303", "Ylivuoto, arvo on liian suuri: {0}."}, new Object[]{"26304", "Ei-tuettu valinta (ei otettu käyttöön)."}, new Object[]{"26305", "Binaarinen JSON on virheellinen tai vioittunut."}, new Object[]{"26306", "Ei-tuettu binaarinen JSON-versio: {0}."}, new Object[]{"26307", "UTF-8-avaimen enimmäispituus on 256 tavua. Seuraava avain ylittää pituuden: {0}."}, new Object[]{"26308", "Määritetty JSON on liian suuri koodattavaksi binaarisena JSON-kuvana. Koodattavien kuvien enimmäiskoko on 2 Gt."}, new Object[]{"26309", "Binaarinen JSON on virheellinen tai vioittunut. Määritetty kuva sisältää vain {0} tavua."}, new Object[]{"26310", "Kohteessa java.time.Period on määritetty päiviä, mutta Oraclen vuosi-kuukausi-välissä ei tueta päiviä."}, new Object[]{"26311", "Luontiohjelma suljettiin ennen loppua."}, new Object[]{"26312", "Objektin avain on määritettävä tässä kontekstissa."}, new Object[]{"26313", "Virheellinen kirjoitus. Täysi arvo on jo kirjoitettu."}, new Object[]{"26314", "Loppu ei ole sallittu tässä kontekstissa."}, new Object[]{"26315", "Avain ei ole sallittu tässä kontekstissa."}, new Object[]{"26316", "Odotettiin arvoa avaimen jälkeen."}, new Object[]{"26317", "Jäsentäjän tilan on oltava {0}."}, new Object[]{"26318", "Jäsentäjän tila ei saa olla {0}."}, new Object[]{"26319", "Jäsentäjän on perustuttava arvoon."}, new Object[]{"26320", "kääreen tyyppiä {0} ei tueta."}, new Object[]{"26321", "Tätä objektia ei voi muokata. Voit luoda muokattavan kopion käyttämällä menetelmää OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Tätä taulukkoa ei voi muokata. Voit luoda muokattavan kopion käyttämällä menetelmää OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "JSON-objekti sisältää toistuvan avaimen: {0}."}, new Object[]{"26324", "Koodausta ei voi havaita automaattisesti, ei tarpeeksi merkkejä."}, new Object[]{"26325", "Odotettiin EOF-merkkiä, mutta saatiin {0}."}, new Object[]{"26326", "Odottamaton merkki {0} rivillä {1} sarakkeessa {2}."}, new Object[]{"26327", "Odottamaton merkki {0} rivillä {1} sarakkeessa {2}. Odotus oli {3}."}, new Object[]{"26328", "Virheellinen merkki {0} rivillä {1} sarakkeessa {2}. Odotetut merkit ovat: {3}."}, new Object[]{"26329", "JsonParser#getString() on sallittu vain jäsentimen tiloissa KEY_NAME, VALUE_STRING, VALUE_NUMBER. Mutta nykyinen jäsentimen tila on {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() on sallittu vain jäsentimen tilassa VALUE_NUMBER. Mutta nykyinen jäsentimen tila on {0}."}, new Object[]{"26331", "JsonParser#getInt() on sallittu vain jäsentimen tilassa VALUE_NUMBER. Mutta nykyinen jäsentimen tila on {0}."}, new Object[]{"26332", "JsonParser#getLong() on sallittu vain jäsentimen tilassa VALUE_NUMBER. Mutta nykyinen jäsentimen tila on {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() on sallittu vain jäsentimen tilassa VALUE_NUMBER. Mutta nykyinen jäsentimen tila on {0}."}, new Object[]{"26334", "JsonParser#getArray() on sallittu vain jäsentimen tilassa START_ARRAY, mutta nykyinen jäsentimen tila on {0}."}, new Object[]{"26335", "JsonParser#getObject() on sallittu vain jäsentimen tilassa START_OBJECT. Mutta nykyinen jäsentimen tila on {0}."}, new Object[]{"26336", "Aluetta sisältävää aikaleimaa ei tueta. Vain siirtymällä ilmoitettuja aikavyöhykkeitä tuetaan."}, new Object[]{"26337", "JSON-arvon objektien ja matriisien sisäkkäisyys ei voi olla yli {0} tasoa"}, new Object[]{"26338", "JSON-objektin avaimet eivät saa ylittää 65 535 tavua"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
