package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoappcontreplay.class */
final class T4CTTIoappcontreplay extends T4CTTIfun {
    private byte[] replayCtxts_kpdxcAppContReplay;
    private int flags_kpdxcAppContReplay;

    T4CTTIoappcontreplay(T4CConnection _conn) {
        super(_conn, (byte) 17);
        setFunCode((short) 177);
    }

    void doOAPPCONTREPLAY(oracle.jdbc.internal.ReplayContext replayContext) throws SQLException, IOException {
        this.replayCtxts_kpdxcAppContReplay = replayContext.getContext();
        this.flags_kpdxcAppContReplay = 0;
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalPTR();
        this.meg.marshalUB4(1L);
        this.meg.marshalUB4(this.flags_kpdxcAppContReplay);
        this.meg.marshalUB4(this.replayCtxts_kpdxcAppContReplay.length);
        this.meg.marshalCLR(this.replayCtxts_kpdxcAppContReplay, 0, this.replayCtxts_kpdxcAppContReplay.length);
    }
}
