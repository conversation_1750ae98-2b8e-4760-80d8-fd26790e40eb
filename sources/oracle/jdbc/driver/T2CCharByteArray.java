package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CCharByteArray.class */
class T2CCharByteArray extends AggregateByteArray {
    private static final String CLASS_NAME = T2CCharByteArray.class.getName();
    char[] charArray;
    DBConversion conversion;

    T2CCharByteArray(Diagnosable diagnosable, char[] _charArray, ByteArray _extension) {
        super(diagnosable, PhysicalConnection.EMPTY_BYTE_ARRAY, _extension);
        this.charArray = _charArray;
    }

    @Override // oracle.jdbc.driver.AggregateByteArray, oracle.jdbc.driver.SimpleByteArray, oracle.jdbc.driver.ByteArray
    long length() {
        return this.charArray.length + this.extension.length();
    }

    void setChars(char[] _chars) {
        this.charArray = _chars;
    }

    void setDBConversion(DBConversion _conversion) {
        this.conversion = _conversion;
    }

    @Override // oracle.jdbc.driver.AggregateByteArray, oracle.jdbc.driver.SimpleByteArray, oracle.jdbc.driver.ByteArray
    char[] getChars(long offset, int lengthInChars, CharacterSet charSet, int[] out_lengthInChars) throws SQLException {
        if (offset < this.charArray.length) {
            char[] returnValue = new char[lengthInChars];
            System.arraycopy(this.charArray, (int) offset, returnValue, 0, lengthInChars);
            out_lengthInChars[0] = lengthInChars;
            return returnValue;
        }
        return this.extension.getChars(offset - this.charArray.length, lengthInChars, charSet, out_lengthInChars);
    }

    @Override // oracle.jdbc.driver.AggregateByteArray, oracle.jdbc.driver.SimpleByteArray, oracle.jdbc.driver.ByteArray
    void get(long offset, byte[] dest, int destOffset, int length) {
        if (offset < this.charArray.length) {
            try {
                this.conversion.javaCharsToCHARBytes(this.charArray, (int) offset, dest, destOffset, length);
                return;
            } catch (SQLException ea) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "get", "T2CCharByteArray, conversion exception: ", (String) null, ea);
                return;
            }
        }
        this.extension.get(offset - this.charArray.length, dest, destOffset, length);
    }

    @Override // oracle.jdbc.driver.AggregateByteArray, oracle.jdbc.driver.SimpleByteArray, oracle.jdbc.driver.ByteArray
    byte get(long index) {
        return index < ((long) this.charArray.length) ? (byte) (this.charArray[(int) index] & 255) : this.extension.get(index - this.charArray.length);
    }
}
