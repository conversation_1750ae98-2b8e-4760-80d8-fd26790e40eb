package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.sql.CharacterSet;
import oracle.sql.SQLName;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIdcb.class */
class T4CTTIdcb extends T4CTTIMsg {
    private static final String CLASS_NAME = T4CTTIdcb.class.getName();
    static final int DCBRXFR = 1;
    static final int DCBFIOT = 2;
    static final int DCBFHAVECOOKIE = 4;
    static final int DCBFNEWCOOKIE = 8;
    static final int DCBFREM = 16;
    int numuds;
    int colOffset;
    byte[] ignoreBuff;
    OracleStatement statement;

    T4CTTIdcb(T4CConnection _conn) {
        super(_conn, (byte) 16);
        this.statement = null;
        this.ignoreBuff = new byte[40];
    }

    void init(OracleStatement stmt, int _offset) {
        this.statement = stmt;
        this.colOffset = _offset;
    }

    Accessor[] receive(Accessor[] accessors) throws SQLException, IOException {
        int length = this.meg.unmarshalUB1();
        if (this.ignoreBuff.length < length) {
            this.ignoreBuff = new byte[length];
        }
        this.meg.unmarshalNBytes(this.ignoreBuff, 0, length);
        return receiveCommon(accessors, false);
    }

    Accessor[] receiveFromRefCursor(Accessor[] accessors) throws SQLException, IOException {
        this.meg.unmarshalUB1();
        return receiveCommon(accessors, false);
    }

    Accessor[] receiveCommon(Accessor[] accessors, boolean fromOdny) throws SQLException, IOException {
        Accessor[] oldAccessors = null;
        if (fromOdny) {
            this.numuds = this.meg.unmarshalUB2();
        } else {
            this.numuds = (int) this.meg.unmarshalUB4();
            if (this.numuds > 0) {
                this.meg.unmarshalUB1();
            }
        }
        if (!this.statement.needToPrepareDefineBuffer && this.statement.numberOfDefinePositions != this.numuds) {
            this.statement.needToPrepareDefineBuffer = true;
        }
        if (this.statement.currentResultSet != null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_RESULT_DESCRIPTION_CHANGED).fillInStackTrace());
        }
        if (accessors != null) {
            oldAccessors = accessors;
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "receiveCommon", "DB re-described happened", (String) null, (Throwable) null);
        }
        Accessor[] newAccessors = new Accessor[this.numuds + this.colOffset];
        if (accessors != null && accessors.length == this.colOffset) {
            System.arraycopy(accessors, 0, newAccessors, 0, this.colOffset);
        }
        T4C8TTIuds uds = new T4C8TTIuds((T4CConnection) this.statement.connection);
        long localCheckSum = this.statement.checkSum;
        for (int i = 0; i < this.numuds; i++) {
            uds.unmarshal();
            String colnames = this.meg.conv.CharBytesToString(uds.getColumName(), uds.getColumNameByteLength());
            localCheckSum = fillupAccessors(newAccessors, oldAccessors, i, this.colOffset + i, uds, colnames, localCheckSum);
        }
        this.statement.checkSum = localCheckSum;
        if (!fromOdny) {
            this.meg.unmarshalDALC();
            if (this.connection.getTTCVersion() >= 3) {
                if (this.connection.getTTCVersion() >= 4) {
                    if (this.connection.getTTCVersion() >= 5) {
                        byte[] dcbqcky = this.meg.unmarshalDALC();
                        this.statement.setQueryCompileKey(dcbqcky);
                    }
                }
            }
        }
        if (!fromOdny) {
            this.statement.rowPrefetchInLastFetch = -1;
            this.statement.describedWithNames = true;
            this.statement.described = true;
            this.statement.numberOfDefinePositions = this.numuds;
            this.statement.accessors = newAccessors;
            this.statement.prepareAccessors();
            this.statement.allocateTmpByteArray();
        }
        return newAccessors;
    }

    long fillupAccessors(Accessor[] accessors, Accessor[] oldAccessors, int oldAccessorIndex, int accessorIndex, T4C8TTIuds ud, String colnames, long localCheckSum) throws SQLException, IOException {
        String type_name;
        int nbOfCharToAllocate;
        int[] definedColumnTypes = this.statement.definedColumnType;
        int[] definedColumnSizes = this.statement.definedColumnSize;
        int[] definedColumnFormOfUses = this.statement.definedColumnFormOfUse;
        int beginColumnIndex = this.statement.isRowidPrepended ? 1 : 0;
        String sql_name = null;
        String schema_name = null;
        int definedColumnType = 0;
        int definedColumnSize = 0;
        int definedColumnFormOfUse = 0;
        if (accessorIndex >= beginColumnIndex) {
            int defineColumnIndex = accessorIndex - beginColumnIndex;
            if (oldAccessors != null && this.statement.getResultSetConcurrency() == 1008) {
                defineColumnIndex = accessorIndex;
            }
            if (definedColumnTypes != null && definedColumnTypes.length > defineColumnIndex && definedColumnTypes[defineColumnIndex] != 0) {
                definedColumnType = definedColumnTypes[defineColumnIndex];
            }
            if (definedColumnSizes != null && definedColumnSizes.length > defineColumnIndex) {
                definedColumnSize = definedColumnSizes[defineColumnIndex];
            }
            if (definedColumnFormOfUses != null && definedColumnFormOfUses.length > defineColumnIndex && definedColumnFormOfUses[defineColumnIndex] > 0) {
                definedColumnFormOfUse = definedColumnFormOfUses[defineColumnIndex];
            }
        }
        int max_len = ud.udsoac.oacmxl;
        switch (ud.udsoac.oacdty) {
            case 1:
                if (ud.udsoac.oacmxlc != 0 && ud.udsoac.oacmxlc < max_len) {
                    max_len = 2 * ud.udsoac.oacmxlc;
                }
                int nbOfCharToAllocate2 = max_len;
                if ((definedColumnType == 1 || definedColumnType == 12) && definedColumnSize > 0 && definedColumnSize < max_len) {
                    nbOfCharToAllocate2 = definedColumnSize;
                }
                accessors[accessorIndex] = new T4CVarcharAccessor(this.statement, nbOfCharToAllocate2, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, ud.udsoac.oacmxlc, max_len, definedColumnType, definedColumnSize, this.meg);
                accessors[accessorIndex].describeMaxLength = ud.udsoac.oacmxl;
                break;
            case 2:
                accessors[accessorIndex] = new T4CNumberAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 6:
                accessors[accessorIndex] = new T4CVarnumAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 8:
                if (this.statement.isFetchStreams || ((definedColumnType == 1 || definedColumnType == 12) && this.connection.versionNumber >= 9000 && definedColumnSize < 4001)) {
                    if (definedColumnSize > 0) {
                        nbOfCharToAllocate = definedColumnSize;
                    } else {
                        nbOfCharToAllocate = max_len;
                    }
                    accessors[accessorIndex] = new T4CVarcharAccessor(this.statement, nbOfCharToAllocate, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, ud.udsoac.oacmxlc, -1, definedColumnType, definedColumnSize, this.meg);
                    accessors[accessorIndex].describeType = 8;
                    break;
                } else {
                    accessors[accessorIndex] = new T4CLongAccessor(this.statement, accessorIndex + 1, 0, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                    break;
                }
            case 11:
            case 104:
            case CharacterSet.F8EBCDIC1147_CHARSET /* 208 */:
                accessors[accessorIndex] = new T4CRowidAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                if (ud.udsoac.oacdty == 208) {
                    accessors[accessorIndex].describeType = CharacterSet.F8EBCDIC1147_CHARSET;
                    break;
                }
                break;
            case 12:
                accessors[accessorIndex] = new T4CDateAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 23:
                accessors[accessorIndex] = new T4CRawAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 24:
                if (this.statement.isFetchStreams || (definedColumnType == -2 && definedColumnSize < 2001 && this.connection.versionNumber >= 9000)) {
                    accessors[accessorIndex] = new T4CRawAccessor(this.statement, -1, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                    accessors[accessorIndex].describeType = 24;
                    break;
                } else {
                    accessors[accessorIndex] = new T4CLongRawAccessor(this.statement, accessorIndex + 1, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                    break;
                }
                break;
            case 96:
                if (ud.udsoac.oacmxlc != 0 && ud.udsoac.oacmxlc < max_len) {
                    max_len = 2 * ud.udsoac.oacmxlc;
                }
                int nbOfCharToAllocate3 = max_len;
                if ((definedColumnType == 1 || definedColumnType == 12) && definedColumnSize > 0 && definedColumnSize < max_len) {
                    nbOfCharToAllocate3 = definedColumnSize;
                }
                accessors[accessorIndex] = new T4CCharAccessor(this.statement, nbOfCharToAllocate3, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, ud.udsoac.oacmxlc, max_len, definedColumnType, definedColumnSize, this.meg);
                accessors[accessorIndex].describeMaxLength = ud.udsoac.oacmxl;
                break;
            case 100:
                accessors[accessorIndex] = new T4CBinaryFloatAccessor(this.statement, 4, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 101:
                accessors[accessorIndex] = new T4CBinaryDoubleAccessor(this.statement, 8, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 102:
                accessors[accessorIndex] = new T4CResultSetAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 109:
                sql_name = this.meg.conv.CharBytesToString(ud.getTypeName(), ud.getTypeCharLength());
                schema_name = this.meg.conv.CharBytesToString(ud.getSchemaName(), ud.getSchemaCharLength());
                if (PhysicalConnection.needToQuoteIdentifier(schema_name) || PhysicalConnection.needToQuoteIdentifier(sql_name)) {
                    type_name = String.format("\"%s\".\"%s\"", schema_name, sql_name);
                } else {
                    type_name = schema_name + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + sql_name;
                }
                accessors[accessorIndex] = new T4CNamedTypeAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, type_name, definedColumnType, definedColumnSize, this.meg);
                break;
            case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                sql_name = this.meg.conv.CharBytesToString(ud.getTypeName(), ud.getTypeCharLength());
                schema_name = this.meg.conv.CharBytesToString(ud.getSchemaName(), ud.getSchemaCharLength());
                String type_name2 = SQLName.getTypeName(schema_name, sql_name);
                accessors[accessorIndex] = new T4CRefTypeAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, type_name2, definedColumnType, definedColumnSize, this.meg);
                break;
            case 112:
                short formOfUse = 1;
                if (definedColumnFormOfUse != 0) {
                    formOfUse = (short) definedColumnFormOfUse;
                }
                if ((definedColumnType == -1 || definedColumnType == -16) && this.connection.versionNumber >= 9000) {
                    accessors[accessorIndex] = new T4CLongAccessor(this.statement, accessorIndex + 1, Integer.MAX_VALUE, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, formOfUse, definedColumnType, definedColumnSize, this.meg);
                    accessors[accessorIndex].describeType = 112;
                    break;
                } else if (isDTYCHR(definedColumnType) && this.connection.versionNumber >= 9000) {
                    int nbOfCharToAllocate4 = 32767;
                    if (definedColumnSize > 0 && definedColumnSize < 32767) {
                        nbOfCharToAllocate4 = definedColumnSize;
                    }
                    accessors[accessorIndex] = new T4CVarcharAccessor(this.statement, nbOfCharToAllocate4, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, formOfUse, ud.udsoac.oacmxlc, 32767, definedColumnType, definedColumnSize, this.meg);
                    accessors[accessorIndex].describeType = 112;
                    break;
                } else {
                    accessors[accessorIndex] = new T4CClobAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                    setLobPrefetch(accessors[accessorIndex], 2005 == definedColumnType || 2011 == definedColumnType, definedColumnSize, oldAccessors, oldAccessorIndex);
                    break;
                }
                break;
            case 113:
                if (definedColumnType == -4 && this.connection.versionNumber >= 9000) {
                    accessors[accessorIndex] = new T4CLongRawAccessor(this.statement, accessorIndex + 1, Integer.MAX_VALUE, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                    accessors[accessorIndex].describeType = 113;
                    break;
                } else if (definedColumnType == -3 && this.connection.versionNumber >= 9000) {
                    accessors[accessorIndex] = new T4CRawAccessor(this.statement, 4000, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                    accessors[accessorIndex].describeType = 113;
                    break;
                } else {
                    accessors[accessorIndex] = new T4CBlobAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                    setLobPrefetch(accessors[accessorIndex], 2004 == definedColumnType, definedColumnSize, oldAccessors, oldAccessorIndex);
                    break;
                }
                break;
            case 114:
                accessors[accessorIndex] = new T4CBfileAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                setLobPrefetch(accessors[accessorIndex], -13 == definedColumnType, definedColumnSize, oldAccessors, oldAccessorIndex);
                break;
            case 119:
                accessors[accessorIndex] = new T4CJsonAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                setLobPrefetch(accessors[accessorIndex], 2016 == definedColumnType, definedColumnSize, oldAccessors, oldAccessorIndex);
                break;
            case 127:
                if (definedColumnType == 0 || OracleTypes.isVector(definedColumnType)) {
                    accessors[accessorIndex] = new T4CVectorAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                    setLobPrefetch(accessors[accessorIndex], OracleTypes.isVector(definedColumnType), definedColumnSize, oldAccessors, oldAccessorIndex);
                } else if (isDTYCHR(definedColumnType)) {
                    short formOfUse2 = definedColumnFormOfUse != 0 ? (short) definedColumnFormOfUse : (short) 1;
                    int nbOfCharToAllocate5 = 32767;
                    if (definedColumnSize > 0 && definedColumnSize < 32767) {
                        nbOfCharToAllocate5 = definedColumnSize;
                    }
                    accessors[accessorIndex] = new T4CVarcharAccessor(this.statement, nbOfCharToAllocate5, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, formOfUse2, ud.udsoac.oacmxlc, 32767, definedColumnType, definedColumnSize, this.meg);
                    accessors[accessorIndex].describeType = 127;
                } else if (definedColumnType == 2005 || definedColumnType == 2011) {
                    short formOfUse3 = definedColumnFormOfUse != 0 ? (short) definedColumnFormOfUse : (short) 1;
                    accessors[accessorIndex] = new T4CClobAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, formOfUse3, definedColumnType, definedColumnSize, this.meg);
                    accessors[accessorIndex].describeType = 127;
                    setLobPrefetch(accessors[accessorIndex], true, definedColumnSize, oldAccessors, oldAccessorIndex);
                } else {
                    throw ((IOException) DatabaseError.createIOException(4, "defineColumnType " + definedColumnType + " is not supported for VECTOR columns").fillInStackTrace());
                }
                accessors[accessorIndex].setVectorMetaData(ud.getVectorMetaData());
                break;
            case 180:
                accessors[accessorIndex] = new T4CTimestampAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 181:
                accessors[accessorIndex] = new T4CTimestamptzAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 182:
                accessors[accessorIndex] = new T4CIntervalymAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 183:
                accessors[accessorIndex] = new T4CIntervaldsAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case CharacterSet.WE8BS2000_CHARSET /* 231 */:
                accessors[accessorIndex] = new T4CTimestampltzAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                break;
            case 252:
                accessors[accessorIndex] = new T4CBooleanAccessor(this.statement, max_len, ud.udsnull, ud.udsoac.oacflg, ud.udsoac.oacpre, ud.udsoac.oacscl, ud.udsoac.oacfl2, ud.udsoac.oacmal, ud.udsoac.oaccsfrm, definedColumnType, definedColumnSize, this.meg);
                accessors[accessorIndex].describeType = 252;
                break;
            default:
                throw ((IOException) DatabaseError.createIOException(5, "Type code is: " + ((int) ud.udsoac.oacdty)).fillInStackTrace());
        }
        if (ud.udsoac.oactoid.length > 0) {
            accessors[accessorIndex].internalOtype = new OracleTypeADT(ud.udsoac.oactoid, ud.udsoac.oacvsn, ud.udsoac.oaccsi, ud.udsoac.oaccsfrm, SQLName.getTypeName(schema_name, sql_name));
        } else {
            accessors[accessorIndex].internalOtype = null;
        }
        accessors[accessorIndex].columnName = colnames;
        accessors[accessorIndex].securityAttribute = OracleResultSetMetaData.SecurityAttribute.NONE;
        if ((ud.udsflg & 1) != 0) {
            accessors[accessorIndex].securityAttribute = OracleResultSetMetaData.SecurityAttribute.ENABLED;
        } else if ((ud.udsflg & 2) != 0) {
            accessors[accessorIndex].securityAttribute = OracleResultSetMetaData.SecurityAttribute.UNKNOWN;
        }
        accessors[accessorIndex].setColumnInvisible((ud.udsflg & 8) != 0);
        accessors[accessorIndex].setColumnJSON((ud.udsflg & 256) != 0);
        accessors[accessorIndex].setDomainName(ud.getDomainName());
        accessors[accessorIndex].setDomainSchema(ud.getDomainSchema());
        accessors[accessorIndex].setAnnotations(ud.getAnnotations());
        if (ud.udsoac.oacmxl == 0) {
            accessors[accessorIndex].isNullByDescribe = true;
        }
        accessors[accessorIndex].udskpos = ud.getKernelPosition();
        byte[] immutableArray = new byte[ud.udsoac.oactoid.length];
        System.arraycopy(ud.udsoac.oactoid, 0, immutableArray, 0, ud.udsoac.oactoid.length);
        if (isDebugEnabled()) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "fillupAccessors", "Name of Column # {0} = {1}, Column Nullable = {2}, Data Type = {3}, Flag = {4}, Precision = {5}, Scale = {6}, Max Length = {7}, Total nb of elems = {8}, Cont. flag = {9}, Toid = {10}, version = {11}, Char Set = {12}, CS Form = {13}, Sqlname = {14}", (String) null, (String) null, Integer.valueOf(accessorIndex), colnames, Boolean.valueOf(ud.udsnull), Short.valueOf(ud.udsoac.oacdty), Short.valueOf(ud.udsoac.oacflg), Short.valueOf(ud.udsoac.oacpre), Short.valueOf(ud.udsoac.oacscl), Integer.valueOf(ud.udsoac.oacmxl), Integer.valueOf(ud.udsoac.oacmal), Long.valueOf(ud.udsoac.oacfl2), Parameter.arg(Format.Style.BYTE_ARRAY, immutableArray, new long[0]), Integer.valueOf(ud.udsoac.oacvsn), Integer.valueOf(ud.udsoac.oaccsi), Short.valueOf(ud.udsoac.oaccsfrm), sql_name);
        }
        if (this.connection.checksumMode.needToCalculateFetchChecksum()) {
            long localCheckSum2 = CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(localCheckSum, ud.udsoac.oacdty), ud.udsoac.oacmxl), ud.udsoac.oacpre), ud.udsoac.oacscl), ud.udsoac.oaccsfrm);
            if (sql_name != null) {
                localCheckSum2 = CRC64.updateChecksum(localCheckSum2, SQLName.getTypeName(schema_name, sql_name));
            }
            localCheckSum = CRC64.updateChecksum(localCheckSum2, colnames);
        }
        return localCheckSum;
    }

    private void setLobPrefetch(Accessor accessor, boolean isDefined, int definedColumnSize, Accessor[] oldAccessors, int oldAccessorIndex) {
        if (oldAccessors != null) {
            int oldPrefetchSize = oldAccessors[oldAccessorIndex].lobPrefetchSizeForThisColumn;
            accessor.setPrefetchLength(oldPrefetchSize);
        } else if (this.connection.useLobPrefetch && isDefined) {
            accessor.setPrefetchLength(definedColumnSize);
        } else {
            accessor.setNoPrefetch();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }

    private static boolean isDTYCHR(int externalType) {
        switch (externalType) {
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case 1:
            case 12:
                return true;
            default:
                return false;
        }
    }
}
