package oracle.jdbc.driver.json.binary;

import oracle.jdbc.driver.json.tree.OracleJsonNumberImpl;
import oracle.sql.json.OracleJsonDecimal;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonValueFactory.class */
public abstract class OsonValueFactory {
    abstract OsonAbstractArray createArray(OsonContext osonContext, int i);

    abstract OsonAbstractObject createObject(OsonContext osonContext, int i);

    abstract Object createString(OsonContext osonContext, int i, int i2);

    abstract OracleJsonNumberImpl createNumber(byte[] bArr, OracleJsonDecimal.TargetType targetType);

    abstract OracleJsonNumberImpl createStringNumber(String str);

    abstract OracleJsonNumberImpl createDouble(double d);

    abstract Object createBinary(OsonContext osonContext, int i, int i2, boolean z);

    abstract Object createTimestamp(byte[] bArr);

    abstract Object createTimestampTZ(byte[] bArr);

    abstract OracleJsonNumberImpl createFloat(float f);

    abstract Object createDate(byte[] bArr);

    abstract Object createIntervalYM(byte[] bArr);

    abstract Object createIntervalDS(byte[] bArr);

    abstract Object createTrue();

    abstract Object createFalse();

    abstract Object createNull();

    abstract Object createVector(OsonContext osonContext, int i, int i2);
}
