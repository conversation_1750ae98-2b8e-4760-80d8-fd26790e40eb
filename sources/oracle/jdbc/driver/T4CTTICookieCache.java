package oracle.jdbc.driver;

import java.util.Optional;
import java.util.concurrent.locks.ReentrantLock;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTICookieCache.class */
final class T4CTTICookieCache {
    private final LRUCache<T4CTTICookie> lruCache;
    private final int cacheSize;
    private ReentrantLock lruCacheLock;

    public T4CTTICookieCache(int cacheSize) {
        if (cacheSize <= 0 || cacheSize > 200) {
            throw new IllegalArgumentException("cache size must be between 1 and 200");
        }
        this.cacheSize = cacheSize;
        this.lruCache = new LRUCache<>(false);
        this.lruCache.vacancy(this.cacheSize);
        this.lruCacheLock = new ReentrantLock();
    }

    public void post(String key, T4CTTICookie cookie) {
        if (key == null || key.length() == 0) {
            throw new IllegalArgumentException("key cannot be null nor empty");
        }
        if (cookie == null) {
            throw new IllegalArgumentException("cached value cannot be null");
        }
        this.lruCacheLock.lock();
        try {
            if (this.lruCache.hasKey(0, 0, key)) {
                throw new IllegalStateException();
            }
            if (this.lruCache.size() > this.cacheSize) {
                this.lruCache.removeLeastRecent();
            }
            this.lruCache.add(cookie, 0, 0, key);
        } finally {
            this.lruCacheLock.unlock();
        }
    }

    public void put(String key, T4CTTICookie cookie) {
        if (key == null || key.length() == 0) {
            throw new IllegalArgumentException("key cannot be null nor empty");
        }
        if (cookie == null) {
            throw new IllegalArgumentException("cached value cannot be null");
        }
        this.lruCacheLock.lock();
        try {
            this.lruCache.removeMostRecent(0, 0, key);
            this.lruCache.add(cookie, 0, 0, key);
        } finally {
            this.lruCacheLock.unlock();
        }
    }

    public Optional<T4CTTICookie> get(String key) {
        if (key == null || key.length() == 0) {
            throw new IllegalArgumentException("key cannot be null nor empty");
        }
        this.lruCacheLock.lock();
        try {
            T4CTTICookie cookie = this.lruCache.removeMostRecent(0, 0, key);
            if (cookie != null) {
                this.lruCache.add(cookie, 0, 0, key);
            }
            return cookie != null ? Optional.of(cookie) : Optional.empty();
        } finally {
            this.lruCacheLock.unlock();
        }
    }

    public void flush() {
        this.lruCacheLock.lock();
        do {
            try {
            } finally {
                this.lruCacheLock.unlock();
            }
        } while (this.lruCache.removeLeastRecent() != null);
    }
}
