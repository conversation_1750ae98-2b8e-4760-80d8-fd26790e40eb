package oracle.core.lvf;

import oracle.jdbc.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/core/lvf/VersionMgr.class */
public final class VersionMgr {
    public static final byte ALPHA = 1;
    public static final byte BETA = 2;
    public static final byte PROD = 3;
    public static final byte NONE = 4;
    private final byte MAX_LEN = 64;
    private final byte MAX_PRODLEN = 30;
    private final byte MAX_VERLEN = 15;
    private final byte MAX_DISTLEN = 5;
    private final String alpha = "Alpha";
    private final String beta = "Beta";
    private final String prod = "Production";
    private String version;

    public void setVersion(String pname, byte v1, byte v2, byte v3, byte v4, byte v5, char vc, String dist, byte rtype, int flags) {
        byte obcnt;
        String vsbuf;
        char[] outbuf = new char[64];
        String rel = "";
        byte length = (byte) pname.length();
        byte plen = length;
        if (length > 30) {
            plen = 30;
        }
        byte b = 0;
        while (true) {
            obcnt = b;
            byte b2 = plen;
            plen = (byte) (plen - 1);
            if (0 >= b2) {
                break;
            }
            outbuf[obcnt] = pname.charAt(obcnt);
            b = (byte) (obcnt + 1);
        }
        byte obcnt2 = (byte) (obcnt + 1);
        outbuf[obcnt] = '\t';
        if (v1 < 0) {
            v1 = 0;
        }
        if (v2 < 0) {
            v2 = 0;
        }
        if (v3 < 0) {
            v3 = 0;
        }
        if (v4 < 0) {
            v4 = 0;
        }
        if (v5 < 0) {
            v5 = 0;
        }
        if (v1 > 99) {
            v1 = 99;
        }
        if (v2 > 99) {
            v2 = 99;
        }
        if (v3 > 99) {
            v3 = 99;
        }
        if (v4 > 99) {
            v4 = 99;
        }
        if (v5 > 99) {
            v5 = 99;
        }
        if (vc != 0) {
            vsbuf = ((int) v1) + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + ((int) v2) + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + ((int) v3) + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + ((int) v4) + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + ((int) v5) + vc;
        } else {
            vsbuf = ((int) v1) + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + ((int) v2) + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + ((int) v3) + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + ((int) v4) + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + ((int) v5);
        }
        byte vslen = (byte) vsbuf.length();
        byte i = 0;
        while (true) {
            byte b3 = vslen;
            vslen = (byte) (vslen - 1);
            if (0 >= b3) {
                break;
            }
            byte b4 = obcnt2;
            obcnt2 = (byte) (obcnt2 + 1);
            byte b5 = i;
            i = (byte) (i + 1);
            outbuf[b4] = vsbuf.charAt(b5);
        }
        if (rtype != 4) {
            byte b6 = obcnt2;
            obcnt2 = (byte) (obcnt2 + 1);
            outbuf[b6] = '\t';
            if (dist != null) {
                byte length2 = (byte) dist.length();
                byte dtlen = length2;
                if (length2 > 5) {
                    dtlen = 5;
                }
                byte i2 = 0;
                while (true) {
                    byte b7 = dtlen;
                    dtlen = (byte) (dtlen - 1);
                    if (0 >= b7) {
                        break;
                    }
                    byte b8 = obcnt2;
                    obcnt2 = (byte) (obcnt2 + 1);
                    byte b9 = i2;
                    i2 = (byte) (i2 + 1);
                    outbuf[b8] = dist.charAt(b9);
                }
                byte b10 = obcnt2;
                obcnt2 = (byte) (obcnt2 + 1);
                outbuf[b10] = '\t';
            }
            switch (rtype) {
                case 1:
                    rel = "Alpha";
                    break;
                case 2:
                    rel = "Beta";
                    break;
                case 3:
                    rel = "Production";
                    break;
            }
            byte i3 = 0;
            byte rlen = (byte) rel.length();
            while (true) {
                byte b11 = rlen;
                rlen = (byte) (rlen - 1);
                if (0 < b11) {
                    byte b12 = obcnt2;
                    obcnt2 = (byte) (obcnt2 + 1);
                    byte b13 = i3;
                    i3 = (byte) (i3 + 1);
                    outbuf[b12] = rel.charAt(b13);
                }
            }
        }
        this.version = new String(outbuf, 0, (int) obcnt2);
    }

    public String getVersion() {
        return this.version;
    }
}
