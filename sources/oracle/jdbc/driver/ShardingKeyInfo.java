package oracle.jdbc.driver;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URL;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.SQLType;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import oracle.jdbc.OracleShardingKey;
import oracle.jdbc.OracleShardingKeyBuilder;
import oracle.jdbc.OracleType;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.OracleStatement;
import oracle.jdbc.pool.OracleShardingKeyBuilderImpl;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.CHAR;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.NUMBER;
import oracle.sql.RAW;
import oracle.sql.TIMESTAMP;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ShardingKeyInfo.class */
class ShardingKeyInfo {
    private static final String CLASS_NAME = ShardingKeyInfo.class.getName();
    static ConcurrentHashMap<Integer, KeyTokenInfo> sqlToShardingKeyTokensMap = new ConcurrentHashMap<>();
    protected static final int DEPTH = 128;
    public static final int GWS_KEY_RESERVED = 255;
    public static final int GWS_KEY_UNUSED = 0;
    public static final int GWS_KEY_RETURN_TUPLE_20_1 = 82;
    public static final int GWS_KEY_APPEND_KEY_TUPLE_20_1 = 125;
    public static final int GWS_KEY_APPEND_VALUE_KEY_20_1 = 93;
    public static final int GWS_KEY_PUSH_BIND_INDEX_20_1 = 73;
    public static final int GWS_KEY_PUSH_PARAMETER_20_1 = 80;
    public static final int GWS_KEY_PUSH_LITERAL_20_1 = 76;
    public static final int GWS_KEY_PUSH_SQL_TYPE_20_1 = 84;
    public static final int GWS_KEY_PUSH_SHORT_20_1 = 83;
    public static final int GWS_KEY_PUSH_EMPTY_KEY_20_1 = 91;
    public static final int GWS_KEY_PUSH_EMPTY_TUPLE_20_1 = 123;
    protected Stack stack = new Stack(128);

    ShardingKeyInfo() {
    }

    /* JADX WARN: Multi-variable type inference failed */
    List<List<Object>> evaluateShardingKeys(oracle.jdbc.internal.OracleStatement oracleStatement, byte[] keyRpnTokens, short dbCharSet) throws SQLException, IOException {
        int len = keyRpnTokens.length;
        int index = 0;
        while (index < len) {
            int i = index;
            index++;
            switch (keyRpnTokens[i]) {
                case GWS_KEY_PUSH_BIND_INDEX_20_1 /* 73 */:
                    int index2 = index + 1;
                    int i2 = (keyRpnTokens[index] & 255) << 24;
                    int index3 = index2 + 1;
                    int i3 = i2 + ((keyRpnTokens[index2] & 255) << 16);
                    int index4 = index3 + 1;
                    int i4 = i3 + ((keyRpnTokens[index3] & 255) << 8);
                    index = index4 + 1;
                    int num = i4 + (keyRpnTokens[index4] & 255);
                    this.stack.push(Integer.valueOf(num));
                    break;
                case 76:
                    int type = ((Integer) this.stack.pop(Integer.class)).intValue();
                    byte[] buf = new byte[((Short) this.stack.pop(Short.class)).shortValue()];
                    System.arraycopy(keyRpnTokens, index, buf, 0, buf.length);
                    index += buf.length;
                    this.stack.push(convertDatumToJavaObject(buf, type, dbCharSet));
                    break;
                case 80:
                    int type2 = ((Integer) this.stack.pop(Integer.class)).intValue();
                    int num2 = ((Integer) this.stack.pop(Integer.class)).intValue();
                    this.stack.push(((AbstractShardingPreparedStatement) oracleStatement).getBindValue(num2, type2));
                    break;
                case 82:
                    List<List<Object>> keyTuple = (List) this.stack.pop(List.class);
                    if (index == len) {
                        return keyTuple;
                    }
                    throw new SQLException("more than expected sharding key information expression");
                case 83:
                    int index5 = index + 1;
                    int i5 = (keyRpnTokens[index] & 255) << 8;
                    index = index5 + 1;
                    int num3 = (short) (i5 + (keyRpnTokens[index5] & 255));
                    this.stack.push(Short.valueOf((short) num3));
                    break;
                case 84:
                    int index6 = index + 1;
                    int i6 = (keyRpnTokens[index] & 255) << 8;
                    index = index6 + 1;
                    int num4 = (short) (i6 + (keyRpnTokens[index6] & 255));
                    this.stack.push(Integer.valueOf(num4));
                    break;
                case 91:
                    this.stack.push(new ArrayList());
                    break;
                case 93:
                    Object value = this.stack.pop(Object.class);
                    List<Object> key = (List) this.stack.pop(List.class);
                    key.add(value);
                    this.stack.push(key);
                    break;
                case 123:
                    this.stack.push(new ArrayList());
                    break;
                case 125:
                    List<Object> key2 = (List) this.stack.pop(List.class);
                    List<List<Object>> tuple = (List) this.stack.pop(List.class);
                    tuple.add(key2);
                    this.stack.push(tuple);
                    break;
                default:
                    throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_INVALID_SHARDING_KEY_INFO).fillInStackTrace());
            }
        }
        throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_INVALID_SHARDING_KEY_INFO).fillInStackTrace());
    }

    Object convertDatumToJavaObject(byte[] buf, int internalType, short dbCharSet) throws SQLException {
        Datum subkeyDatum = SQLUtil.makeDatum(null, buf, internalType, null, 0, (short) 873, dbCharSet);
        Object subkeyVal = SQLToJavaKeyObject(subkeyDatum, internalType);
        return subkeyVal;
    }

    OracleShardingKey[] getShardingKeys(oracle.jdbc.internal.OracleStatement statement, byte[] keyRpnTokens, short dbCharSet) throws SQLException, IOException {
        OracleShardingKey[] shardingKeys = new OracleShardingKey[2];
        List<List<Object>> keyTuple = evaluateShardingKeys(statement, keyRpnTokens, dbCharSet);
        OracleShardingKeyBuilder shardingKeyBuilder = new OracleShardingKeyBuilderImpl();
        if (keyTuple != null && !keyTuple.isEmpty()) {
            shardingKeys[0] = addSubKeys(shardingKeyBuilder, keyTuple.get(0)).build();
            if (keyTuple.size() > 1) {
                OracleShardingKeyBuilder superShardingKeyBuilder = new OracleShardingKeyBuilderImpl();
                shardingKeys[1] = addSubKeys(superShardingKeyBuilder, keyTuple.get(1)).build();
            }
        }
        return shardingKeys;
    }

    OracleShardingKeyBuilder addSubKeys(OracleShardingKeyBuilder shardingKeyBuilder, List<Object> subkeyVals) throws SQLException {
        for (Object subkeyVal : subkeyVals) {
            SQLType subkeyType = getKeyType(subkeyVal);
            shardingKeyBuilder.subkey(subkeyVal, subkeyType);
        }
        return shardingKeyBuilder;
    }

    SQLType getKeyType(Object val) throws SQLException {
        int externalType = sqlTypeForObject(val);
        SQLType sqltype = OracleType.toOracleType(externalType);
        return sqltype;
    }

    private int sqlTypeForObject(Object x) {
        if (x == null) {
            return 0;
        }
        if (!(x instanceof Datum)) {
            if (x instanceof String) {
                return 12;
            }
            if ((x instanceof BigDecimal) || (x instanceof BigInteger) || (x instanceof Boolean) || (x instanceof Integer) || (x instanceof Long)) {
                return 2;
            }
            if (x instanceof Float) {
                return 6;
            }
            if (x instanceof Double) {
                return 8;
            }
            if (x instanceof byte[]) {
                return -2;
            }
            if ((x instanceof Short) || (x instanceof Byte)) {
                return 2;
            }
            if (x instanceof Date) {
                return 91;
            }
            if (x instanceof Time) {
                return 92;
            }
            if (x instanceof Timestamp) {
                return 93;
            }
            if (x instanceof URL) {
                return 12;
            }
            return oracle.jdbc.OracleTypes.OTHER;
        }
        if (x instanceof BINARY_FLOAT) {
            return 100;
        }
        if (x instanceof BINARY_DOUBLE) {
            return 101;
        }
        if (x instanceof NUMBER) {
            return 2;
        }
        if (x instanceof DATE) {
            return 91;
        }
        if (x instanceof TIMESTAMP) {
            return 93;
        }
        if (x instanceof CHAR) {
            return 1;
        }
        if (x instanceof RAW) {
            return -2;
        }
        return oracle.jdbc.OracleTypes.OTHER;
    }

    static Object SQLToJavaKeyObject(Datum datum, int internalType) throws SQLException {
        Object ret_obj;
        if (datum == null) {
            return null;
        }
        switch (internalType) {
            case 1:
            case 96:
                ret_obj = datum.stringValue();
                break;
            case 2:
            case 6:
                ret_obj = datum.bigDecimalValue();
                break;
            case 12:
                ret_obj = datum.dateValue();
                break;
            case 180:
                ret_obj = datum.timestampValue();
                break;
            default:
                ret_obj = datum.toJdbc();
                break;
        }
        return ret_obj;
    }

    static KeyTokenInfo putKeyRpnTokens(String sql, String serviceName, String userName, String schemaName, byte[] keyRpnTokens, OracleStatement.SqlKind sqlkind) {
        KeyTokenInfo keyTokenInfo = new KeyTokenInfo(keyRpnTokens, sqlkind);
        int mapKey = calculateTokensHashKey(sql, serviceName, userName, schemaName);
        Diagnosable diag = CommonDiagnosable.getInstance();
        diag.trace(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "putKeyRpnTokens", "sql={0} serviceName={1} userName={2} schemaName={3} keyRpnTokens={4} mapKey={5}", (String) null, (Throwable) null, sql, serviceName, userName, schemaName, Arrays.toString(keyRpnTokens), Integer.valueOf(mapKey));
        return sqlToShardingKeyTokensMap.put(Integer.valueOf(mapKey), keyTokenInfo);
    }

    static KeyTokenInfo getKeyRpnTokens(String sql, String serviceName, String userName, String schemaName) {
        int mapKey = calculateTokensHashKey(sql, serviceName, userName, schemaName);
        return sqlToShardingKeyTokensMap.get(Integer.valueOf(mapKey));
    }

    static int calculateTokensHashKey(String... strKeys) {
        int tempHashCode = 1;
        for (String key : strKeys) {
            tempHashCode = (31 * tempHashCode) + key.hashCode();
        }
        return tempHashCode;
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/ShardingKeyInfo$Stack.class */
    protected static final class Stack {
        private final Object[] stack;
        private int top = -1;

        public Stack(int depth) {
            this.stack = new Object[depth];
        }

        public boolean isEmpty() {
            return this.top == -1;
        }

        public Stack push(Object value) {
            Object[] objArr = this.stack;
            int i = this.top + 1;
            this.top = i;
            objArr[i] = value;
            return this;
        }

        public <T> T pop(Class<T> cls) {
            T t = (T) this.stack[this.top];
            Object[] objArr = this.stack;
            int i = this.top;
            this.top = i - 1;
            objArr[i] = null;
            return t;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/ShardingKeyInfo$KeyTokenInfo.class */
    protected static final class KeyTokenInfo {
        private byte[] keyTokens;
        private OracleStatement.SqlKind sqlkind;

        public KeyTokenInfo(byte[] keyTokens, OracleStatement.SqlKind sqlkind) {
            this.keyTokens = keyTokens;
            this.sqlkind = sqlkind;
        }

        public byte[] getKeyTokens() {
            return this.keyTokens;
        }

        public OracleStatement.SqlKind getSqlKind() {
            return this.sqlkind;
        }
    }
}
