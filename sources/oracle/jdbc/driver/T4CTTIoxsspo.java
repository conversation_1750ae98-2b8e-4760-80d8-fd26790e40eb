package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.KeywordValueLong;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxsspo.class */
final class T4CTTIoxsspo extends T4CTTIfun {
    private int functionId;
    private byte[] sessionId;
    private KeywordValueLong[] inKV;
    private int inFlags;

    T4CTTIoxsspo(T4CConnection _conn) {
        super(_conn, (byte) 17);
        setFunCode((short) 157);
    }

    void doOXSSPO(int _functionId, byte[] _sessionId, KeywordValueLong[] _inKV, int _inFlags) throws SQLException, IOException {
        this.functionId = _functionId;
        this.sessionId = _sessionId;
        this.inKV = _inKV;
        this.inFlags = _inFlags;
        if (this.inKV != null) {
            for (int i = 0; i < this.inKV.length; i++) {
                ((KeywordValueLongI) this.inKV[i]).doCharConversion(this.meg.conv);
            }
        }
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.functionId);
        boolean sendSessionId = false;
        if (this.sessionId != null && this.sessionId.length > 0) {
            sendSessionId = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sessionId.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendInKV = false;
        if (this.inKV != null && this.inKV.length > 0) {
            sendInKV = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.inKV.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB4(this.inFlags);
        if (sendSessionId) {
            this.meg.marshalB1Array(this.sessionId);
        }
        if (sendInKV) {
            for (int i = 0; i < this.inKV.length; i++) {
                ((KeywordValueLongI) this.inKV[i]).marshal(this.meg);
            }
        }
    }
}
