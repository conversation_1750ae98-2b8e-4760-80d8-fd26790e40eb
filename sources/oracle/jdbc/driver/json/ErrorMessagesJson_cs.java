package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_cs.class */
public class ErrorMessagesJson_cs extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Do<PERSON>lo k výjimce vstupu/výstupu"}, new Object[]{"26302", "Rok \"{0}\" není podporován"}, new Object[]{"26303", "<PERSON><PERSON><PERSON>čení, příliš velk<PERSON> hodnota: {0}."}, new Object[]{"26304", "Nepodporovaná volba (není implementována)."}, new Object[]{"26305", "Binární soubor JSON je neplatný nebo poškozený."}, new Object[]{"26306", "Nepodporovaná verze binárního souboru JSON: {0}."}, new Object[]{"26307", "Délka klíče kódovaného v UTF-8 nesmí být vetší než 256 bajtů. Následující klíč tento limit překračuje: \"{0}\"."}, new Object[]{"26308", "Zadaný soubor JSON je příliš velký na to, aby mohl být kódován jako binární JSON. Velikost kódovaných obrazů nesmí překročit 2 GB."}, new Object[]{"26309", "Binární soubor JSON je neplatný nebo poškozený. Zadaný obraz obsahuje jen {0} bajtů."}, new Object[]{"26310", "Pro zadané období java.time.Period jsou nastaveny dny, ale interval Oracle roku až měsíce dny nepodporuje."}, new Object[]{"26311", "Generátor byl uzavřen před dokončením."}, new Object[]{"26312", "V tomto kontextu je nutné zadat klíč objektu."}, new Object[]{"26313", "Neplatný zápis. Již byla zapsána úplná hodnota."}, new Object[]{"26314", "Konec není v tomto kontextu povolen."}, new Object[]{"26315", "Klíč není v tomto kontextu povolen."}, new Object[]{"26316", "Očekávána hodnota za klíčem."}, new Object[]{"26317", "Stav syntaktického analyzátoru musí být {0}."}, new Object[]{"26318", "Stav syntaktického analyzátoru nesmí být {0}."}, new Object[]{"26319", "Syntaktický analyzátor musí být na hodnotě."}, new Object[]{"26320", "\"{0}\" není podporovaný typ obálky."}, new Object[]{"26321", "Tento objekt nelze změnit. Chcete-li vytvořit měnitelnou kopii, použijte zápis OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Toto pole nelze změnit. Chcete-li vytvořit měnitelnou kopii, použijte zápis OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "Objekt JSON obsahuje duplicitní klíč: {0}."}, new Object[]{"26324", "Nelze automaticky zjistit kódování z důvodu nedostatečného počtu znaků."}, new Object[]{"26325", "Byl očekáván token EOF, ale bylo přijato {0}."}, new Object[]{"26326", "Neočekávaný znak {0} na řádku {1}, ve sloupci {2}."}, new Object[]{"26327", "Neočekávaný znak {0} na řádku {1}, ve sloupci {2}. Očekávaná hodnota: {3}."}, new Object[]{"26328", "Neplatný token {0} na řádku {1}, ve sloupci {2}. Očekávané tokeny jsou: {3}."}, new Object[]{"26329", "Platné stavy syntaktického analyzátoru pro JsonParser#getString() jsou pouze KEY_NAME, VALUE_STRING a VALUE_NUMBER. Ale aktuální stav syntaktického analyzátoru je {0}."}, new Object[]{"26330", "Platný stav syntaktického analyzátoru pro JsonParser#isIntegralNumber() je pouze VALUE_NUMBER. Ale aktuální stav syntaktického analyzátoru je {0}."}, new Object[]{"26331", "Platný stav syntaktického analyzátoru pro JsonParser#getInt() je pouze VALUE_NUMBER. Ale aktuální stav syntaktického analyzátoru je {0}."}, new Object[]{"26332", "Platný stav syntaktického analyzátoru pro JsonParser#getLong() je pouze VALUE_NUMBER. Ale aktuální stav syntaktického analyzátoru je {0}."}, new Object[]{"26333", "Platný stav syntaktického analyzátoru pro JsonParser#getBigDecimal() je pouze VALUE_NUMBER. Ale aktuální stav syntaktického analyzátoru je {0}."}, new Object[]{"26334", "JsonParser#getArray() je platné pouze pro stav syntaktického analyzátoru START_ARRAY. Aktuální stav syntaktického analyzátoru je ale {0}."}, new Object[]{"26335", "Platný stav syntaktického analyzátoru pro JsonParser#getObject() je pouze START_OBJECT. Ale aktuální stav syntaktického analyzátoru je {0}."}, new Object[]{"26336", "Časová značka s oblastí není podporována. Podporovány jsou pouze časové zóny s posunem."}, new Object[]{"26337", "Objekty a pole v hodnotě JSON nesmí být vnořeny hlouběji než {0} úrovní"}, new Object[]{"26338", "Klíče objektu JSON nesmí překročit 65 535 bajtů"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
