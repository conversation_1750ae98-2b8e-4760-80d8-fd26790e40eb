package oracle.jdbc.driver.oauth;

import java.sql.SQLException;
import java.util.LongSummaryStatistics;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;
import oracle.jdbc.OracleDriver;
import oracle.jdbc.driver.oauth.OpaqueAccessToken;
import oracle.jdbc.logging.annotations.Blind;
import oracle.net.nt.Clock;
import oracle.net.nt.TimeoutInterruptHandler;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/oauth/AccessTokenCache.class */
public final class AccessTokenCache<T extends OpaqueAccessToken> implements Supplier<T> {
    private static final long EXPIRATION_THRESHOLD = 30000;
    private static final long UPDATE_THRESHOLD = 60000;
    private final Supplier<T> tokenSupplier;
    private final Executor executor;
    private final LongSummaryStatistics latency = new LongSummaryStatistics();
    private final AtomicInteger getCount = new AtomicInteger(0);
    private final ReentrantLock lock = new ReentrantLock();
    private final Condition updateCondition = this.lock.newCondition();
    private boolean isUpdating = false;
    private T token = null;
    private RuntimeException failure;

    public static <T extends OpaqueAccessToken> AccessTokenCache<T> create(Supplier<T> tokenSupplier) {
        try {
            return new AccessTokenCache<>(OracleDriver.getExecutorService(), tokenSupplier);
        } catch (SQLException sqlException) {
            throw new IllegalStateException(sqlException);
        }
    }

    private AccessTokenCache(Executor executor, @Blind Supplier<T> tokenSupplier) {
        this.executor = executor;
        this.tokenSupplier = tokenSupplier;
    }

    @Override // java.util.function.Supplier
    @Blind
    public T get() {
        this.getCount.incrementAndGet();
        T cachedToken = this.token;
        if (cachedToken != null && !isExpiring(cachedToken)) {
            return cachedToken;
        }
        this.lock.lock();
        while (cachedToken == this.token && this.failure == null) {
            try {
                try {
                    if (!this.isUpdating) {
                        this.isUpdating = true;
                        requestUpdate();
                    }
                    this.updateCondition.await();
                } catch (InterruptedException interruptedException) {
                    throw new RuntimeException(interruptedException);
                }
            } catch (Throwable th) {
                this.lock.unlock();
                throw th;
            }
        }
        if (this.failure != null) {
            RuntimeException updateFailure = this.failure;
            this.failure = null;
            throw updateFailure;
        }
        T t = this.token;
        this.lock.unlock();
        return t;
    }

    private void requestUpdate() {
        this.executor.execute(() -> {
            try {
                long start = System.nanoTime();
                T token = this.tokenSupplier.get();
                this.latency.accept(System.nanoTime() - start);
                update((OpaqueAccessToken) Objects.requireNonNull(token, "token supplier has output a null value"), null);
            } catch (RuntimeException failure) {
                update(null, failure);
            }
        });
    }

    private void update(@Blind T token, RuntimeException failure) {
        boolean isUpdating = this.getCount.getAndSet(0) != 0 && failure == null;
        this.lock.lock();
        try {
            this.token = token;
            this.failure = failure;
            this.isUpdating = isUpdating;
            this.updateCondition.signalAll();
            this.lock.unlock();
            if (isUpdating) {
                scheduleUpdate(TimeUnit.SECONDS.toMillis(token.expiration().toEpochSecond()));
            }
        } catch (Throwable th) {
            this.lock.unlock();
            throw th;
        }
    }

    private void scheduleUpdate(long expireTimeMillis) {
        long updateLatency = TimeUnit.NANOSECONDS.toMillis(Math.round(this.latency.getAverage() * 1.2d));
        long expirationDelay = (expireTimeMillis - UPDATE_THRESHOLD) - System.currentTimeMillis();
        TimeoutInterruptHandler.scheduleTask(this::requestUpdate, Math.max(0L, expirationDelay - updateLatency));
    }

    private static boolean isExpiring(@Blind OpaqueAccessToken accessToken) {
        return Clock.currentTimeMillis() + EXPIRATION_THRESHOLD >= TimeUnit.SECONDS.toMillis(accessToken.expiration().toEpochSecond());
    }
}
