package oracle.jdbc.driver;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.driver.OracleStatement;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleStatement;
import oracle.jdbc.oracore.OracleTypeDATE;
import oracle.jdbc.oracore.OracleTypeTIMESTAMP;
import oracle.jdbc.xa.OracleXAResource;
import oracle.net.ano.AnoServices;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CPreparedStatement.class */
class T4CPreparedStatement extends OraclePreparedStatement {
    private final String CLASS_NAME;
    static final byte[] EMPTY_BYTE = new byte[0];
    T4CConnection t4Connection;
    private long beyondRowData;
    private T4C8Oall streamingOAll;
    final String[] nlsStrings;

    T4CPreparedStatement(PhysicalConnection connection, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        this(connection, sql, resultSetType, null);
    }

    T4CPreparedStatement(PhysicalConnection connection, String sql, AutoKeyInfo autoKeyInfo) throws SQLException {
        this(connection, sql, OracleResultSet.ResultSetType.UNKNOWN, autoKeyInfo);
    }

    private T4CPreparedStatement(PhysicalConnection connection, String sql, OracleResultSet.ResultSetType resultSetType, AutoKeyInfo autoKeyInfo) throws SQLException {
        super(connection, sql, resultSetType, autoKeyInfo);
        this.CLASS_NAME = getClass().getName();
        this.beyondRowData = 0L;
        this.streamingOAll = null;
        this.nlsStrings = new String[]{"AUTH_NLS_LXLAN", "AUTH_NLS_LXCTERRITORY", "AUTH_NLS_LXCCURRENCY", "AUTH_NLS_LXCISOCURR", "AUTH_NLS_LXCNUMERICS", "AUTH_NLS_LXCDATEFM", "AUTH_NLS_LXCDATELANG", "AUTH_NLS_LXCSORT", "AUTH_NLS_LXCCALENDAR", "AUTH_NLS_LXCUNIONCUR", "AUTH_NLS_LXCTIMEFM", "AUTH_NLS_LXCSTMPFM", "AUTH_NLS_LXCTTZNFM", "AUTH_NLS_LXCSTZNFM", "SESSION_TIME_ZONE", "AL8KW_ENABLED_ROLES", "AL8KW_ERR_OVLAP", "AL8KW_EDITION", "AL8KW_AUX_SESSSTATE"};
        this.nbPostPonedColumns = new int[1];
        this.nbPostPonedColumns[0] = 0;
        this.indexOfPostPonedColumn = new int[1][3];
        this.t4Connection = (T4CConnection) connection;
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected Binder createRowidBinder(byte[] rowidBytes) throws SQLException {
        return new T4CRowidBinder(rowidBytes);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected Binder createURowidBinder(byte[] rowidBytes) throws SQLException {
        return new T4CURowidBinder(rowidBytes);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected Binder createRowidNullBinder() throws SQLException {
        return new T4CRowidNullBinder();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected Binder createURowidNullBinder() throws SQLException {
        return new T4CURowidNullBinder();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected Binder createBooleanBinder(boolean x) throws SQLException {
        short versionNumber = this.connection.getVersionNumber();
        boolean sendBooleanAsBoolean = this.connection.sendBooleanAsNativeBoolean && this.connection.sendBooleanInPLSQL;
        if (this.sqlKind.isPlsqlOrCall()) {
            if (versionNumber >= 23000 && sendBooleanAsBoolean) {
                return new BooleanBinder(x ? 1 : 0);
            }
            return new BooleanBinder21c(x ? 1 : 0);
        }
        if (versionNumber >= 23000 && this.connection.sendBooleanAsNativeBoolean) {
            return new BooleanBinder(x ? 1 : 0);
        }
        return new BooleanBinder21c(x ? 1 : 0);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected Binder createBooleanNullBinder() throws SQLException {
        short versionNumber = this.connection.getVersionNumber();
        boolean sendBooleanAsBoolean = this.connection.sendBooleanAsNativeBoolean && this.connection.sendBooleanInPLSQL;
        if (this.sqlKind.isPlsqlOrCall()) {
            if (versionNumber >= 23000 && sendBooleanAsBoolean) {
                return new BooleanNullBinder();
            }
            return BooleanNullBinder21c.getInstance();
        }
        if (versionNumber >= 23000 && this.connection.sendBooleanAsNativeBoolean) {
            return new BooleanNullBinder();
        }
        return BooleanNullBinder21c.getInstance();
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected boolean prepareDefineBufferOnRowPrefetchChange() {
        return false;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void prepareForNewResults(boolean resetPrefetch, boolean clearStreamList, boolean clearImplicitResults) throws SQLException {
        super.prepareForNewResults(resetPrefetch, clearStreamList, clearImplicitResults);
        this.beyondRowData = 0L;
    }

    void doOall8(T4C8Oall all8, boolean doParse, boolean doExecute, boolean doFetch, boolean doDescribe, boolean doDefine) throws SQLException, IOException {
        int rowsToFetch = prepareForOALL8(doFetch);
        if (rowsToFetch == -1) {
            return;
        }
        boolean isRowIdPrefixed = doDefine && doDescribe && this.isRowidPrepended;
        debug(Level.FINER, SecurityLabel.UNKNOWN, this.CLASS_NAME, "doOall8", "doParse={0}, doExecute={1}, doFetch={2}, doDescribe={3}, doDefine={4}, isRowIdPrefixed={5}. ", (String) null, (Throwable) null, Boolean.valueOf(doParse), Boolean.valueOf(doExecute), Boolean.valueOf(doFetch), Boolean.valueOf(doDescribe), Boolean.valueOf(doDefine), Boolean.valueOf(isRowIdPrefixed));
        initializeOall8(all8, isRowIdPrefixed, rowsToFetch);
        try {
            try {
                all8.doOALL(doParse, doExecute, doFetch, doDescribe, doDefine);
                handleOall8CompletionAlways(all8);
            } catch (SQLException ea) {
                if (!handleOall8Failure(ea)) {
                    throw ea;
                }
                handleOall8CompletionAlways(all8);
            }
        } catch (Throwable th) {
            handleOall8CompletionAlways(all8);
            throw th;
        }
    }

    private int prepareForOALL8(boolean doFetch) throws SQLException {
        this.t4Connection.assertLoggedOn("oracle.jdbc.driver.T4CPreparedStatement.doOall8");
        if (this.sqlKind == OracleStatement.SqlKind.UNINITIALIZED) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, this.CLASS_NAME, "prepareForOALL8", "sqlKind={0}. ", (String) null, (String) null, (Object) this.sqlKind);
            this.sqlKind = OracleStatement.SqlKind.OTHER;
        }
        int rowsToFetch = this.rowPrefetch;
        if (doFetch) {
            if (this.autoTuneRowPrefetch && this.sqlKind == OracleStatement.SqlKind.SELECT) {
                tuneRowPrefetch();
            }
            rowsToFetch = getMaximumRowFetchForOALL8();
            this.rowPrefetchInLastFetch = rowsToFetch;
            if (rowsToFetch == 0 && this.isAllFetched) {
                return -1;
            }
            this.rowData.setPosition(this.beyondRowData);
        }
        if (this.fetchMode == OracleStatement.FetchMode.OVERWRITE) {
            prepareAccessorRowCountsForOALL8(0);
        }
        prepareBindsByteArrayForOALL8();
        allocateTmpByteArray();
        return rowsToFetch;
    }

    private int getMaximumRowFetchForOALL8() {
        long fetchedRowCount = this.indexOfFirstRow + this.storedRowCount;
        boolean limitFetchByMaxRows = this.maxRows > 0 && this.maxRows <= fetchedRowCount + ((long) this.rowPrefetch);
        if (limitFetchByMaxRows) {
            this.isAllFetched = true;
            return (int) (this.maxRows - fetchedRowCount);
        }
        return this.rowPrefetch;
    }

    private void prepareAccessorRowCountsForOALL8(int fetchedRowCount) {
        if (this.accessors != null) {
            for (int i = 0; i < this.accessors.length; i++) {
                if (this.accessors[i] != null) {
                    this.accessors[i].lastRowProcessed = fetchedRowCount;
                }
            }
        }
        if (this.outBindAccessors != null) {
            for (int i2 = 0; i2 < this.outBindAccessors.length; i2++) {
                if (this.outBindAccessors[i2] != null) {
                    this.outBindAccessors[i2].lastRowProcessed = 0;
                }
            }
        }
    }

    private void prepareBindsByteArrayForOALL8() {
        if (this.bindIndicators != null) {
            int i = ((this.bindIndicators[this.bindIndicatorSubRange + 3] & 65535) << 16) + (this.bindIndicators[this.bindIndicatorSubRange + 4] & 65535);
            int maxNbBytes = 0;
            if (this.ibtBindChars != null) {
                maxNbBytes = this.ibtBindChars.length * this.connection.conversion.cMaxCharSize;
            }
            for (int P = 0; P < this.numberOfBindPositions; P++) {
                int subRangeOffset = this.bindIndicatorSubRange + 5 + (10 * P);
                int charPitch = this.bindIndicators[subRangeOffset + 2] & 65535;
                if (charPitch != 0) {
                    int formOfUse = this.bindIndicators[subRangeOffset + 9] & 65535;
                    if (!this.bindUseDBA) {
                        if (formOfUse == 2) {
                            maxNbBytes = Math.max(charPitch * this.connection.conversion.maxNCharSize, maxNbBytes);
                        } else {
                            maxNbBytes = Math.max(charPitch * this.connection.conversion.cMaxCharSize, maxNbBytes);
                        }
                    }
                }
            }
            if (!this.bindUseDBA) {
                if (this.tmpBindsByteArray == null) {
                    this.tmpBindsByteArray = new byte[maxNbBytes];
                    return;
                } else {
                    if (this.tmpBindsByteArray.length < maxNbBytes) {
                        this.tmpBindsByteArray = null;
                        this.tmpBindsByteArray = new byte[maxNbBytes];
                        return;
                    }
                    return;
                }
            }
            return;
        }
        this.tmpBindsByteArray = null;
    }

    private void initializeOall8(T4C8Oall all8, boolean isRowIdPrefixed, int rowsToFetch) throws SQLException {
        all8.typeOfStatement = this.sqlKind;
        all8.cursor = getCursorId();
        all8.sqlStmt = this.sqlObject.getSqlBytes(this.processEscapes, this.convertNcharLiterals);
        all8.rowsToFetch = rowsToFetch;
        all8.outBindAccessors = this.outBindAccessors;
        all8.numberOfBindPositions = this.numberOfBindPositions;
        all8.definesAccessors = this.accessors;
        all8.definesLength = getNumberOfDefinePositionsForOALL8();
        all8.bindBytes = this.bindBytes;
        all8.bindChars = this.bindChars;
        all8.bindIndicators = this.bindIndicators;
        all8.bindIndicatorSubRange = this.bindIndicatorSubRange;
        all8.conversion = this.connection.conversion;
        all8.tmpBindsByteArray = this.tmpBindsByteArray;
        all8.parameterStream = this.parameterStream;
        all8.oracleStatement = this;
        all8.ibtBindBytes = this.ibtBindBytes;
        all8.ibtBindChars = this.ibtBindChars;
        all8.ibtBindIndicators = this.ibtBindIndicators;
        all8.oacdefBindsSent = this.oacdefSent;
        all8.definedColumnType = getDefinedColumnTypesForOALL8(isRowIdPrefixed);
        all8.definedColumnSize = getDefinedColumnSizesForOALL8(isRowIdPrefixed);
        all8.definedColumnFormOfUse = getDefinedColumnFormsOfUseForOALL8(isRowIdPrefixed);
        all8.registration = this.registration;
        all8.bindData = this.bindData;
        all8.bindDataOffsets = this.bindDataOffsets;
        all8.bindDataLengths = this.bindDataLengths;
        all8.bindUseDBA = this.bindUseDBA;
        debug(Level.INFO, SecurityLabel.UNKNOWN, this.CLASS_NAME, "initializeOall8", "cursorId={0}", (String) null, (String) null, Integer.valueOf(getCursorId()));
    }

    private int getNumberOfDefinePositionsForOALL8() {
        if (this.sqlKind.isDML()) {
            return 0;
        }
        return this.numberOfDefinePositions;
    }

    private int[] getDefinedColumnTypesForOALL8(boolean isRowIdPrefixed) {
        if (isRowIdPrefixed) {
            int[] definedColumnTypeCopy = new int[this.definedColumnType.length + 1];
            System.arraycopy(this.definedColumnType, 0, definedColumnTypeCopy, 1, this.definedColumnType.length);
            definedColumnTypeCopy[0] = -8;
            return definedColumnTypeCopy;
        }
        return this.definedColumnType;
    }

    private int[] getDefinedColumnSizesForOALL8(boolean isRowIdPrefixed) {
        if (isRowIdPrefixed) {
            int[] definedColumnSizeCopy = new int[this.definedColumnSize.length + 1];
            System.arraycopy(this.definedColumnSize, 0, definedColumnSizeCopy, 1, this.definedColumnSize.length);
            return definedColumnSizeCopy;
        }
        return this.definedColumnSize;
    }

    private int[] getDefinedColumnFormsOfUseForOALL8(boolean isRowIdPrefixed) {
        if (isRowIdPrefixed) {
            int[] definedColumnFormOfUseCopy = new int[this.definedColumnFormOfUse.length + 1];
            System.arraycopy(this.definedColumnFormOfUse, 0, definedColumnFormOfUseCopy, 1, this.definedColumnFormOfUse.length);
            return definedColumnFormOfUseCopy;
        }
        return this.definedColumnFormOfUse;
    }

    private boolean handleOall8Failure(SQLException sqlException) {
        if (DatabaseError.isInternallyHandledWarning(sqlException)) {
            this.sqlWarning = DatabaseError.addSqlWarning(this.sqlWarning, 110);
            return true;
        }
        return false;
    }

    private void handleOall8CompletionAlways(T4C8Oall all8) throws SQLException {
        int tmpCursorID = all8.getCursorId();
        if (tmpCursorID != 0) {
            setCursorId(tmpCursorID);
        }
        this.oacdefSent = all8.oacdefBindsSent;
        if (this.connection.isPDBChanged) {
            this.connection.onPDBChange(this);
            this.connection.isPDBChanged = false;
        }
        this.beyondRowData = Math.max(this.beyondRowData, this.rowData.getPosition());
        updateStreamingOAll(all8);
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void allocateTmpByteArray() {
        if (this.tmpByteArray == null) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, this.CLASS_NAME, "allocateTmpByteArray", "allocate byte array of size={0}. ", (String) null, (String) null, (Object) Integer.valueOf(this.sizeTmpByteArray));
            this.tmpByteArray = new byte[this.sizeTmpByteArray];
        } else if (this.sizeTmpByteArray > this.tmpByteArray.length) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, this.CLASS_NAME, "allocateTmpByteArray", "Re-allocate byte array of size={0}. ", (String) null, (String) null, (Object) Integer.valueOf(this.sizeTmpByteArray));
            this.tmpByteArray = new byte[this.sizeTmpByteArray];
        } else {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, this.CLASS_NAME, "allocateTmpByteArray", "do not re-allocate byte array of size={0}, current size={1}. ", (String) null, (Throwable) null, Integer.valueOf(this.sizeTmpByteArray), Integer.valueOf(this.tmpByteArray.length));
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.driver.OracleStatement
    void releaseBuffers() {
        super.releaseBuffers();
        this.tmpByteArray = null;
        this.tmpBindsByteArray = null;
        if (this.t4Connection != null) {
            if (this.wrapper == null || !this.wrapper.isExecutingAsync()) {
                this.t4Connection.all8.bindChars = null;
                this.t4Connection.all8.bindBytes = null;
                this.t4Connection.all8.tmpBindsByteArray = null;
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void allocateRowidAccessor() throws SQLException {
        this.accessors[0] = new T4CRowidAccessor(this, 128, (short) 1, -8, false, this.t4Connection.mare);
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void reparseOnRedefineIfNeeded() throws SQLException {
        this.needToParse = true;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected void defineColumnTypeInternal(int column_index, int type, int size, short form, boolean sizeNotGiven, String typeName) throws SQLException {
        if (this.connection.disableDefinecolumntype) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, this.CLASS_NAME, "defineColumnTypeInternal", "defineColumnTypeInternal is DISABLED. ", (String) null, (Throwable) null);
            return;
        }
        if (type == -15 || type == -9 || type == -16) {
            form = 2;
        }
        if (column_index < 1) {
            throw ((SQLException) DatabaseError.createSqlException(3).fillInStackTrace());
        }
        if (this.currentResultSet != null && !this.currentResultSet.closed) {
            throw ((SQLException) DatabaseError.createSqlException(28).fillInStackTrace());
        }
        int idx = column_index - 1;
        if (this.definedColumnType == null || this.definedColumnType.length <= idx) {
            if (this.definedColumnType == null) {
                this.definedColumnType = new int[(idx + 1) * 4];
            } else {
                int[] n_definedColumnType = new int[(idx + 1) * 4];
                System.arraycopy(this.definedColumnType, 0, n_definedColumnType, 0, this.definedColumnType.length);
                this.definedColumnType = n_definedColumnType;
            }
        }
        this.definedColumnType[idx] = type;
        if (this.definedColumnSize == null || this.definedColumnSize.length <= idx) {
            if (this.definedColumnSize == null) {
                this.definedColumnSize = new int[(idx + 1) * 4];
            } else {
                int[] n_definedColumnSize = new int[(idx + 1) * 4];
                System.arraycopy(this.definedColumnSize, 0, n_definedColumnSize, 0, this.definedColumnSize.length);
                this.definedColumnSize = n_definedColumnSize;
            }
        }
        switch (type) {
            case oracle.jdbc.OracleTypes.BLOB /* 2004 */:
            case oracle.jdbc.OracleTypes.CLOB /* 2005 */:
                this.definedColumnSize[idx] = sizeNotGiven ? 0 : size;
                break;
            default:
                this.definedColumnSize[idx] = -1;
                break;
        }
        if (this.definedColumnFormOfUse == null || this.definedColumnFormOfUse.length <= idx) {
            if (this.definedColumnFormOfUse == null) {
                this.definedColumnFormOfUse = new int[(idx + 1) * 4];
            } else {
                int[] n_definedColumnFormOfUse = new int[(idx + 1) * 4];
                System.arraycopy(this.definedColumnFormOfUse, 0, n_definedColumnFormOfUse, 0, this.definedColumnFormOfUse.length);
                this.definedColumnFormOfUse = n_definedColumnFormOfUse;
            }
        }
        this.definedColumnFormOfUse[idx] = form;
        this.executeDoneForDefines = false;
    }

    @Override // oracle.jdbc.driver.OracleStatement, oracle.jdbc.OracleStatement
    public void clearDefines() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            doClearDefines();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected void doClearDefines() throws SQLException {
        this.connection.assertLockHeldByCurrentThread();
        super.doClearDefines();
        this.definedColumnType = null;
        this.definedColumnSize = null;
        this.definedColumnFormOfUse = null;
        if (this.t4Connection != null) {
            if (this.wrapper == null || !this.wrapper.isExecutingAsync()) {
                this.t4Connection.all8.definesAccessors = null;
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void doSetSnapshotSCN(long scn) throws SQLException {
        this.inScn = scn;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected ByteArray createBindData() {
        this.bindUseDBA = this.connection.bindUseDBA;
        return super.createBindData();
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected final void locationToPutBytes(Accessor acc, int row, int length) throws SQLException {
        acc.setOffset(row, allocateRowDataSpace(length));
    }

    @Override // oracle.jdbc.driver.OracleStatement
    long allocateRowDataSpace(int size) {
        long offset = this.beyondRowData;
        this.beyondRowData += size;
        return offset;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    boolean areOutBindsStoredInBindData() {
        return false;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:15:0x01b1  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x0124  */
    @Override // oracle.jdbc.driver.OracleStatement
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    oracle.jdbc.driver.Accessor allocateAccessor(int r11, int r12, int r13, int r14, short r15, java.lang.String r16, boolean r17) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 1101
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CPreparedStatement.allocateAccessor(int, int, int, int, short, java.lang.String, boolean):oracle.jdbc.driver.Accessor");
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void doDescribe(boolean includeNames) throws SQLException {
        if (!this.isOpen) {
            this.connection.open(this);
            this.isOpen = true;
        }
        byte[] sqlBytes = this.sqlObject.getSqlBytes(this.processEscapes, this.convertNcharLiterals);
        try {
            this.t4Connection.needLine();
            this.t4Connection.describe.doODNY(this, 0, this.accessors, sqlBytes);
            this.accessors = this.t4Connection.describe.getAccessors();
            this.numberOfDefinePositions = this.t4Connection.describe.numuds;
            for (int i = 0; i < this.numberOfDefinePositions; i++) {
                this.accessors[i].initMetadata();
            }
            this.describedWithNames = true;
            this.described = true;
        } catch (IOException ex) {
            ((T4CConnection) this.connection).handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void executeForDescribe() throws SQLException {
        this.t4Connection.assertLoggedOn("oracle.jdbc.driver.T4CPreparedStatement.execute_for_describe");
        T4C8Oall all8 = this.t4Connection.all8;
        try {
            try {
                doOall8(all8, true, true, this.definedColumnType != null, true, this.definedColumnType != null);
                updateAfterDescribeAlways(all8);
                updateAfterDescribeWithoutError(all8);
                for (int i = 0; i < this.numberOfDefinePositions; i++) {
                    this.accessors[i].initMetadata();
                }
                this.needToPrepareDefineBuffer = false;
            } catch (IOException e) {
                ((T4CConnection) this.connection).handleIOException(e);
                throw ((SQLException) DatabaseError.createSqlException(e).fillInStackTrace());
            } catch (SQLException e2) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, this.CLASS_NAME, "executeForDescribe", null, (String) null, e2);
                throw e2;
            }
        } catch (Throwable th) {
            updateAfterDescribeAlways(all8);
            throw th;
        }
    }

    private void updateAfterDescribeAlways(T4C8Oall all8) throws SQLException {
        this.rowsProcessed = all8.rowsProcessed;
        this.validRows = all8.getNumRows();
        if (this.connection.checksumMode.needToCalculateFetchChecksum()) {
            if (this.validRows > 0) {
                calculateCheckSum();
            } else if (this.rowsProcessed > 0) {
                long _checkSum = CRC64.updateChecksum(this.checkSum, this.rowsProcessed);
                this.checkSum = _checkSum;
            }
        }
    }

    private void updateAfterDescribeWithoutError(T4C8Oall all8) throws SQLException {
        this.needToParse = false;
        if (this.definedColumnType == null) {
            this.implicitDefineForLobPrefetchDone = false;
        }
        this.aFetchWasDoneDuringDescribe = false;
        if (all8.aFetchWasDone) {
            this.aFetchWasDoneDuringDescribe = true;
            this.rowPrefetchInLastFetch = this.rowPrefetch;
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void executeForRows(boolean isDescribed) throws SQLException {
        try {
            T4C8Oall all8 = this.t4Connection.all8;
            try {
                boolean sendDefine = prepareForExecuteForRows();
                doOall8(all8, this.needToParse, !isDescribed, true, false, sendDefine);
                handleExecuteForRowsCompletion(sendDefine);
                handleExecuteForRowsCompletionAlways(all8);
            } catch (Throwable th) {
                handleExecuteForRowsCompletionAlways(all8);
                throw th;
            }
        } catch (IOException e) {
            ((T4CConnection) this.connection).handleIOException(e);
            throw ((SQLException) DatabaseError.createSqlException(e).fillInStackTrace());
        }
    }

    private boolean prepareForExecuteForRows() throws SQLException {
        if (this.columnsDefinedByUser) {
            this.needToPrepareDefineBuffer = false;
            return false;
        }
        return prepareLobDefinesForExecution();
    }

    private void handleExecuteForRowsCompletionAlways(T4C8Oall all8) throws SQLException {
        if (this.implicitResultSetStatements == null) {
            this.validRows = all8.getNumRows();
        } else {
            this.validRows = 0L;
        }
        calculateCheckSum();
    }

    private void handleExecuteForRowsCompletion(boolean sentDefine) {
        this.needToParse = false;
        if (sentDefine) {
            this.implicitDefineForLobPrefetchDone = true;
        }
    }

    private boolean prepareLobDefinesForExecution() throws SQLException {
        if ((!this.t4Connection.useLobPrefetch || this.accessors == null || this.defaultLobPrefetchSize == -1 || this.implicitDefineForLobPrefetchDone || this.aFetchWasDoneDuringDescribe || this.definedColumnType != null) && !this.isFetchingValueBasedLob) {
            return false;
        }
        boolean oneColumnIsALob = false;
        int[] tempDefinedColumnType = new int[this.accessors.length];
        int[] tempDefinedColumnSize = new int[this.accessors.length];
        int[] tempDefinedColumnFormOfUse = new int[this.accessors.length];
        for (int i = 0; i < this.accessors.length; i++) {
            if (this.accessors[i] != null) {
                tempDefinedColumnType[i] = getJDBCType(this.accessors[i].internalType);
                tempDefinedColumnFormOfUse[i] = this.accessors[i].formOfUse;
                if (this.accessors[i].internalType == 113 || this.accessors[i].internalType == 112 || this.accessors[i].internalType == 114) {
                    oneColumnIsALob = true;
                    this.accessors[i].setPrefetchLength(this.defaultLobPrefetchSize);
                    tempDefinedColumnSize[i] = this.defaultLobPrefetchSize;
                } else if (this.accessors[i].internalType == 119) {
                    oneColumnIsALob = true;
                    this.accessors[i].setPrefetchLength(OracleXAResource.TMSUSPEND);
                    tempDefinedColumnSize[i] = 33554432;
                } else if (this.accessors[i].internalType == 127) {
                    oneColumnIsALob = true;
                    this.accessors[i].setPrefetchLength(524308);
                    tempDefinedColumnSize[i] = 524308;
                }
            }
        }
        if (oneColumnIsALob) {
            this.definedColumnType = tempDefinedColumnType;
            this.definedColumnSize = tempDefinedColumnSize;
            this.definedColumnFormOfUse = tempDefinedColumnFormOfUse;
            return true;
        }
        return false;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected void fetch(int firstRow) throws SQLException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, this.CLASS_NAME, "fetch", "firstRow={0}, fetchMode={1}.", (String) null, (Throwable) null, Integer.valueOf(firstRow), this.fetchMode);
        prepareForFetch(firstRow);
        boolean sendDefine = prepareLobDefinesForExecution();
        try {
            T4C8Oall all8 = this.t4Connection.all8;
            doOall8(all8, false, false, true, false, sendDefine);
            handleFetchCompletion(all8, sendDefine, firstRow);
        } catch (IOException ex) {
            ((T4CConnection) this.connection).handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    private void prepareForFetch(int firstRow) throws SQLException {
        if (this.fetchMode == OracleStatement.FetchMode.APPEND) {
            prepareAccessorRowCountsForOALL8(firstRow);
        } else if (this.rowData != null) {
            this.beyondRowData = 0L;
        }
        releaseStreamsBeforeFetch();
    }

    private void releaseStreamsBeforeFetch() throws SQLException {
        if (this.streamList != null) {
            while (this.nextStream != null) {
                try {
                    this.nextStream.close();
                    this.nextStream = this.nextStream.nextStream;
                } catch (IOException exc) {
                    ((T4CConnection) this.connection).handleIOException(exc);
                    throw ((SQLException) DatabaseError.createSqlException(exc).fillInStackTrace());
                }
            }
        }
    }

    private void handleFetchCompletion(T4C8Oall all8, boolean sentDefine, int firstRow) throws SQLException {
        if (sentDefine) {
            this.implicitDefineForLobPrefetchDone = true;
        }
        this.validRows = all8.getNumRows();
        if (this.validRows != -2) {
            this.validRows -= firstRow;
        }
        this.beyondRowData = Math.max(this.beyondRowData, this.rowData.getPosition());
        calculateCheckSum();
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void continueReadRow(int start) throws SQLException {
        if (this.streamingOAll == null) {
            return;
        }
        try {
            this.streamingOAll.continueReadRow(start, this);
            this.beyondRowData = Math.max(this.beyondRowData, this.rowData.getPosition());
            updateStreamingOAll(this.streamingOAll);
        } catch (IOException ioException) {
            ((T4CConnection) this.connection).handleIOException(ioException);
            throw ((SQLException) DatabaseError.createSqlException(ioException).fillInStackTrace());
        } catch (SQLException sqlException) {
            if (DatabaseError.isInternallyHandledWarning(sqlException)) {
                this.sqlWarning = DatabaseError.addSqlWarning(this.sqlWarning, 110);
                return;
            }
            throw sqlException;
        }
    }

    private void updateStreamingOAll(T4C8Oall oall) {
        if (oall.getNumRows() == -2) {
            this.streamingOAll = oall;
        } else {
            this.streamingOAll = null;
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void doClose() throws SQLException {
        this.t4Connection.assertLoggedOn("oracle.jdbc.driver.T4CPreparedStatement.do_close");
        int cursorId = getCursorId();
        if (cursorId != 0) {
            this.t4Connection.closeCursor(cursorId);
        }
        this.tmpByteArray = null;
        this.tmpBindsByteArray = null;
        this.definedColumnType = null;
        this.definedColumnSize = null;
        this.definedColumnFormOfUse = null;
        this.oacdefSent = null;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void closeQuery() throws SQLException {
        this.connection.needLine();
        this.t4Connection.assertLoggedOn("oracle.jdbc.driver.T4CPreparedStatement.closeQuery");
        if (this.streamList != null) {
            while (this.nextStream != null) {
                try {
                    this.nextStream.close();
                    this.nextStream = this.nextStream.nextStream;
                } catch (IOException exc) {
                    ((T4CConnection) this.connection).handleIOException(exc);
                    throw ((SQLException) DatabaseError.createSqlException(exc).fillInStackTrace());
                }
            }
        }
        int cursorId = getCursorId();
        if (!this.isAllFetched && cursorId != 0) {
            this.t4Connection.closeQuery(cursorId);
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    Binder getRowidNullBinder(int index) throws SQLException {
        if (this.sqlKind == OracleStatement.SqlKind.CALL_BLOCK) {
            this.currentRowCharLens[index] = 1;
            return new VarcharNullBinder();
        }
        return createRowidNullBinder();
    }

    @Override // oracle.jdbc.driver.OracleStatement
    public byte[] getRuntimeKey() throws SQLException {
        if (this.md == null) {
            try {
                this.md = MessageDigest.getInstance(AnoServices.CHECKSUM_MD5);
            } catch (NoSuchAlgorithmException e) {
                return (byte[]) null;
            }
        } else {
            this.md.reset();
        }
        if (this.maxRows > 0) {
            return (byte[]) null;
        }
        for (String s : this.nlsStrings) {
            String property = (String) this.connection.sessionProperties.get(s);
            if (property != null) {
                this.md.update(property.getBytes(StandardCharsets.UTF_16));
            }
        }
        if (this.t4Connection.currentSchema != null) {
            this.md.update(this.t4Connection.currentSchema.getBytes(StandardCharsets.UTF_16));
        }
        if (this.currentRowBinders != null) {
            for (Binder b : this.lastBinders) {
                switch (b.type) {
                    case 8:
                    case 24:
                    case 109:
                    case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                    case 112:
                    case 113:
                    case 114:
                        return (byte[]) null;
                    default:
                        this.md.update((byte) (b.type & 255));
                        this.md.update((byte) ((b.type >> 8) & 255));
                }
            }
            if (this.bindUseDBA) {
                if (this.numberOfBindPositions > 0 && this.bindDataOffsets != null && this.bindDataLengths != null && this.bindIndicators != null) {
                    int number_of_bound_rows = ((this.bindIndicators[this.bindIndicatorSubRange + 3] & 65535) << 16) + (this.bindIndicators[this.bindIndicatorSubRange + 4] & 65535);
                    for (int rowIndex = 0; rowIndex < number_of_bound_rows; rowIndex++) {
                        int rowStartPosition = rowIndex * this.numberOfBindPositions;
                        for (int position = 0; position < this.numberOfBindPositions; position++) {
                            int bindDataIndex = rowStartPosition + position;
                            this.bindData.updateDigest(this.md, this.bindDataOffsets[bindDataIndex], this.bindDataLengths[bindDataIndex]);
                        }
                    }
                }
            } else if (this.bindBytes != null) {
                this.md.update(this.bindBytes, 0, this.totalBindByteLength);
            } else if (this.bindDataLengths != null) {
                long offset = this.bindDataOffsets[0];
                int length = ((int) (offset - this.bindDataOffsets[this.bindDataOffsets.length - 1])) + this.bindDataLengths[this.bindDataLengths.length - 1];
                long localBindChecksum = this.bindData.updateChecksum(offset, length, PhysicalConnection.CHECKSUM, 0L);
                this.md.update(String.valueOf(localBindChecksum).getBytes(StandardCharsets.UTF_16));
            }
            int temp3 = Arrays.hashCode(this.bindIndicators);
            byte[] b2 = this.t4Connection.mare.tmpBuffer4;
            int i = 0;
            int exp = 8 * (b2.length - 1);
            while (i < b2.length) {
                b2[i] = (byte) ((temp3 & (255 << exp)) >> exp);
                i++;
                exp -= 8;
            }
            this.md.update(b2);
            if (this.bindChars != null) {
                long temp4 = CRC64.updateChecksum(0L, this.bindChars, 0, this.totalBindCharLength);
                byte[] b3 = this.t4Connection.mare.tmpBuffer8;
                int i2 = 0;
                int exp2 = 8 * (b3.length - 1);
                while (i2 < b3.length) {
                    long mask = 255 << exp2;
                    b3[i2] = (byte) ((temp4 & mask) >> exp2);
                    i2++;
                    exp2 -= 8;
                }
                this.md.update(b3);
            }
        }
        this.runtimeKey = this.md.digest();
        return this.runtimeKey;
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    Object[] handleOtherPlsqlTypes(int internalType, Object arrayData, int curLen, int[] eMaxLen) throws SQLException {
        Object[] returnValue = null;
        switch (internalType) {
            case 12:
                OracleTypeDATE typeObj = new OracleTypeDATE();
                returnValue = typeObj.toDatumArray(arrayData, this.connection, 1L, curLen);
                if (returnValue != null) {
                    eMaxLen[0] = 8;
                    break;
                }
                break;
            case 180:
                OracleTypeTIMESTAMP typeObj2 = new OracleTypeTIMESTAMP(this.connection);
                returnValue = typeObj2.toDatumArray(arrayData, this.connection, 1L, curLen);
                if (returnValue != null) {
                    eMaxLen[0] = 8;
                    break;
                }
                break;
        }
        return returnValue;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected final void prepareForExecuteWithDRCP() throws SQLException {
        int cursorId = getCursorId();
        if (cursorId != 0 && !this.t4Connection.canSendCursorIds()) {
            this.t4Connection.closeCursor(cursorId);
            clearCursorId();
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected void doBindValueConversion(int bindersOffset) throws SQLException {
        doBindValueConversion(bindersOffset, 1);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected void reallocBinds(int new_number_of_bind_rows_allocated) throws SQLException {
        if (this.bindIndicators == null) {
            allocBinds(1);
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected int getAllocBindsRowCount() {
        return 1;
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void doLocalInitialization() {
        super.doLocalInitialization();
        this.t4Connection.all8.bindChars = this.bindChars;
        this.t4Connection.all8.bindBytes = this.bindBytes;
    }
}
