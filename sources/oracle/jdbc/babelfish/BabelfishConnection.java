package oracle.jdbc.babelfish;

import java.lang.reflect.Method;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.EnumMap;
import java.util.Map;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleTranslatingConnection;
import oracle.jdbc.proxy.annotation.GetCreator;
import oracle.jdbc.proxy.annotation.GetDelegate;
import oracle.jdbc.proxy.annotation.GetProxy;
import oracle.jdbc.proxy.annotation.OnError;
import oracle.jdbc.proxy.annotation.Post;
import oracle.jdbc.proxy.annotation.ProxyFor;
import oracle.jdbc.proxy.annotation.ProxyLocale;

@ProxyFor({Connection.class, OracleConnection.class, oracle.jdbc.internal.OracleConnection.class})
@ProxyLocale
/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/BabelfishConnection.class */
public abstract class BabelfishConnection extends BabelfishGenericProxy implements OracleTranslatingConnection {
    @Override // oracle.jdbc.babelfish.BabelfishGenericProxy
    @GetCreator
    protected abstract Object getCreator();

    @Override // oracle.jdbc.babelfish.BabelfishGenericProxy
    @GetDelegate
    protected abstract Object getDelegate();

    @GetProxy
    protected abstract Object proxify(Object obj, Object obj2);

    @Override // oracle.jdbc.babelfish.BabelfishGenericProxy
    @OnError(SQLException.class)
    protected Object translateError(Method m, SQLException ex) throws SQLException {
        throw this.translator.translateError(ex);
    }

    @Override // oracle.jdbc.babelfish.BabelfishGenericProxy
    @Post
    protected Object post_Methods(Method m, Object result) {
        if (result instanceof BabelfishGenericProxy) {
            ((BabelfishGenericProxy) result).setTranslator(this.translator);
        }
        return result;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public PreparedStatement prepareStatement(String sql) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            PreparedStatement preparedStatement = (PreparedStatement) proxify(((Connection) getDelegate()).prepareStatement(translatedSql), this);
            ((BabelfishGenericProxy) preparedStatement).setTranslator(this.translator);
            return preparedStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public PreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            PreparedStatement preparedStatement = (PreparedStatement) proxify(((Connection) getDelegate()).prepareStatement(translatedSql, autoGeneratedKeys), this);
            ((BabelfishGenericProxy) preparedStatement).setTranslator(this.translator);
            return preparedStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public PreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            PreparedStatement preparedStatement = (PreparedStatement) proxify(((Connection) getDelegate()).prepareStatement(translatedSql, columnIndexes), this);
            ((BabelfishGenericProxy) preparedStatement).setTranslator(this.translator);
            return preparedStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public PreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            PreparedStatement preparedStatement = (PreparedStatement) proxify(((Connection) getDelegate()).prepareStatement(translatedSql, columnNames), this);
            ((BabelfishGenericProxy) preparedStatement).setTranslator(this.translator);
            return preparedStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            PreparedStatement preparedStatement = (PreparedStatement) proxify(((Connection) getDelegate()).prepareStatement(translatedSql, resultSetType, resultSetConcurrency), this);
            ((BabelfishGenericProxy) preparedStatement).setTranslator(this.translator);
            return preparedStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            PreparedStatement preparedStatement = (PreparedStatement) proxify(((Connection) getDelegate()).prepareStatement(translatedSql, resultSetType, resultSetConcurrency, resultSetHoldability), this);
            ((BabelfishGenericProxy) preparedStatement).setTranslator(this.translator);
            return preparedStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public CallableStatement prepareCall(String sql) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            CallableStatement callableStatement = (CallableStatement) proxify(((Connection) getDelegate()).prepareCall(translatedSql), this);
            ((BabelfishGenericProxy) callableStatement).setTranslator(this.translator);
            return callableStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            CallableStatement callableStatement = (CallableStatement) proxify(((Connection) getDelegate()).prepareCall(translatedSql, resultSetType, resultSetConcurrency), this);
            ((BabelfishGenericProxy) callableStatement).setTranslator(this.translator);
            return callableStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            CallableStatement callableStatement = (CallableStatement) proxify(((Connection) getDelegate()).prepareCall(translatedSql, resultSetType, resultSetConcurrency, resultSetHoldability), this);
            ((BabelfishGenericProxy) callableStatement).setTranslator(this.translator);
            return callableStatement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public String nativeSQL(String sql) throws SQLException {
        try {
            String translatedSql = this.translator.translateQuery(sql);
            return ((Connection) getDelegate()).nativeSQL(translatedSql);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void close() throws SQLException {
        this.translator.deactivateServerTranslation();
        ((Connection) getDelegate()).close();
    }

    /* JADX WARN: Multi-variable type inference failed */
    public Statement createStatement() throws SQLException {
        try {
            Statement statement = (Statement) proxify(((Connection) getDelegate()).createStatement(), this);
            ((BabelfishGenericProxy) statement).setTranslator(this.translator);
            return statement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public Statement createStatement(boolean translation) throws SQLException {
        Statement stmt;
        if (!translation) {
            stmt = ((Connection) getDelegate()).createStatement();
        } else {
            stmt = createStatement();
        }
        return stmt;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException {
        try {
            Statement statement = (Statement) proxify(((Connection) getDelegate()).createStatement(resultSetType, resultSetConcurrency), this);
            ((BabelfishGenericProxy) statement).setTranslator(this.translator);
            return statement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public Statement createStatement(int resultSetType, int resultSetConcurrency, boolean translation) throws SQLException {
        Statement stmt;
        if (!translation) {
            stmt = ((Connection) getDelegate()).createStatement(resultSetType, resultSetConcurrency);
        } else {
            stmt = createStatement(resultSetType, resultSetConcurrency);
        }
        return stmt;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        try {
            Statement statement = (Statement) proxify(((Connection) getDelegate()).createStatement(resultSetType, resultSetConcurrency, resultSetHoldability), this);
            ((BabelfishGenericProxy) statement).setTranslator(this.translator);
            return statement;
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability, boolean translation) throws SQLException {
        Statement stmt;
        if (!translation) {
            stmt = ((Connection) getDelegate()).createStatement(resultSetType, resultSetConcurrency, resultSetHoldability);
        } else {
            stmt = createStatement(resultSetType, resultSetConcurrency, resultSetHoldability);
        }
        return stmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public PreparedStatement prepareStatement(String sql, boolean translation) throws SQLException {
        PreparedStatement pstmt;
        if (!translation) {
            pstmt = ((Connection) getDelegate()).prepareStatement(sql);
        } else {
            pstmt = prepareStatement(sql);
        }
        return pstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public PreparedStatement prepareStatement(String sql, int autoGeneratedKeys, boolean translation) throws SQLException {
        PreparedStatement pstmt;
        if (!translation) {
            pstmt = ((Connection) getDelegate()).prepareStatement(sql, autoGeneratedKeys);
        } else {
            pstmt = prepareStatement(sql, autoGeneratedKeys);
        }
        return pstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public PreparedStatement prepareStatement(String sql, int[] columnIndexes, boolean translation) throws SQLException {
        PreparedStatement pstmt;
        if (!translation) {
            pstmt = ((Connection) getDelegate()).prepareStatement(sql, columnIndexes);
        } else {
            pstmt = prepareStatement(sql, columnIndexes);
        }
        return pstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public PreparedStatement prepareStatement(String sql, String[] columnNames, boolean translation) throws SQLException {
        PreparedStatement pstmt;
        if (!translation) {
            pstmt = ((Connection) getDelegate()).prepareStatement(sql, columnNames);
        } else {
            pstmt = prepareStatement(sql, columnNames);
        }
        return pstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, boolean translation) throws SQLException {
        PreparedStatement pstmt;
        if (!translation) {
            pstmt = ((Connection) getDelegate()).prepareStatement(sql, resultSetType, resultSetConcurrency);
        } else {
            pstmt = prepareStatement(sql, resultSetType, resultSetConcurrency);
        }
        return pstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability, boolean translation) throws SQLException {
        PreparedStatement pstmt;
        if (!translation) {
            pstmt = ((Connection) getDelegate()).prepareStatement(sql, resultSetType, resultSetConcurrency, resultSetHoldability);
        } else {
            pstmt = prepareStatement(sql, resultSetType, resultSetConcurrency, resultSetHoldability);
        }
        return pstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public CallableStatement prepareCall(String sql, boolean translation) throws SQLException {
        CallableStatement cstmt;
        if (!translation) {
            cstmt = ((Connection) getDelegate()).prepareCall(sql);
        } else {
            cstmt = prepareCall(sql);
        }
        return cstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, boolean translation) throws SQLException {
        CallableStatement cstmt;
        if (!translation) {
            cstmt = ((Connection) getDelegate()).prepareCall(sql, resultSetType, resultSetConcurrency);
        } else {
            cstmt = prepareCall(sql, resultSetType, resultSetConcurrency);
        }
        return cstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability, boolean translation) throws SQLException {
        CallableStatement cstmt;
        if (!translation) {
            cstmt = ((Connection) getDelegate()).prepareCall(sql, resultSetType, resultSetConcurrency, resultSetHoldability);
        } else {
            cstmt = prepareCall(sql, resultSetType, resultSetConcurrency, resultSetHoldability);
        }
        return cstmt;
    }

    @Override // oracle.jdbc.OracleTranslatingConnection
    public Map<OracleTranslatingConnection.SqlTranslationVersion, String> getSqlTranslationVersions(String sql, boolean suppressExceptions) throws SQLException {
        Map<OracleTranslatingConnection.SqlTranslationVersion, String> translationMap = new EnumMap<>(OracleTranslatingConnection.SqlTranslationVersion.class);
        translationMap.put(OracleTranslatingConnection.SqlTranslationVersion.ORIGINAL_SQL, sql);
        String tempStr = this.translator.convertParameterMarkersToOracleStyle(sql);
        translationMap.put(OracleTranslatingConnection.SqlTranslationVersion.JDBC_MARKER_CONVERTED, tempStr);
        try {
            String tempStr2 = this.translator.translateQuery(sql);
            translationMap.put(OracleTranslatingConnection.SqlTranslationVersion.TRANSLATED, tempStr2);
        } catch (SQLException ex) {
            if (suppressExceptions) {
                translationMap.put(OracleTranslatingConnection.SqlTranslationVersion.TRANSLATED, null);
            } else {
                throw ex;
            }
        }
        return translationMap;
    }
}
