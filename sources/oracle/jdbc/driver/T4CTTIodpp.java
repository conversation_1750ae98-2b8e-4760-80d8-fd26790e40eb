package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;
import java.util.Properties;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIodpp.class */
final class T4CTTIodpp extends T4CTTIfun {
    static final int DPPOPC_LOAD = 1;
    static final int DPPOPC_UNLOAD = 2;
    static final int DPPOPC_CONVERT = 3;
    static final int DPPDEF_KW_OBJ_NAME = 1;
    static final int DPPDEF_KW_SUB_NAME = 2;
    static final int DPPDEF_KW_SCHEMA_NAME = 3;
    static final int DPPDEF_KW_COLUMN_NAME = 4;
    static final int DPPDEF_KW_SORTED_INDEX = 5;
    static final int DPPDEF_KW_PARALLEL_FILE = 6;
    static final int DPPDEF_KW_ADTATTR_NAME = 7;
    static final int DPPDEF_KW_ADTATTR_EXPR = 8;
    static final int DPPDEF_KW_ADTATTR_OBJ_CNT = 9;
    static final int DPPDEF_KW_ADTATTR_OPQ_CNT = 10;
    static final int DPPDEF_KW_ADTATTR_REF_CNT = 11;
    static final int DPPDEF_KW_ADTATTR_TYPE = 12;
    static final int DPPDEF_KW_METADATA = 13;
    static final int DPPDEF_KW_UNLOAD_SCN_BASE = 14;
    static final int DPPDEF_KW_UNLOAD_SCN_WRAP = 15;
    static final int DPPDEF_KW_GRANULE_SIZE = 16;
    static final int DPPDEF_KW_SERVER_ROWS = 17;
    static final int DPPDEF_KW_COMMIT_EXPR = 18;
    static final int DPPDEF_KW_ADTATTR_EXPR_CNT = 19;
    static final int DPPDEF_KW_SUBTYPE_BITVECTORS = 20;
    static final int DPPDEF_KW_SUBTYPE_INDEX = 21;
    static final int DPPDEF_KW_SERVER_SLOT_SIZE = 22;
    static final int DPPDEF_KW_DROPCOL_BITVECTOR = 23;
    static final int DPPDEF_IN_IVRSN = 0;
    static final int DPPDEF_IN_STRVRSN = 1;
    static final int DPPDEF_IN_XFRSIZE = 2;
    static final int DPPDEF_IN_NOLOG = 3;
    static final int DPPDEF_IN_PARALLEL = 4;
    static final int DPPDEF_IN_SKIP_UNUSABLE_INDEX = 5;
    static final int DPPDEF_IN_SKIP_INDEX_MAINT = 6;
    static final int DPPDEF_IN_SINGLE_ROW_INDEX = 7;
    static final int DPPDEF_IN_STORAGE_INIT = 8;
    static final int DPPDEF_IN_STORAGE_NEXT = 9;
    static final int DPPDEF_IN_NESTED_TBL = 10;
    static final int DPPDEF_IN_NFOBJTBL_OIDPOS = 11;
    static final int DPPDEF_IN_SUBST_OBJTBL = 12;
    static final int DPPDEF_IN_DONT_SKIP_UNUSABLE_INDEX = 13;
    static final int DPPDEF_IN_LOCK_WAIT = 14;
    static final int DPPDEF_IN_VARRAY_TBL = 15;
    static final int DPPDEF_IN_NFOBJTBL_SIDPOS = 16;
    static final int DPPDEF_IN_NFOBJTBL_VAIPOS = 17;
    static final int DPPDEF_IN_PARTCONOPT = 18;
    static final int DPPDEF_IN_INTPARTUSED = 19;
    static final int DPPDEF_IN_INTSUBPARTUSED = 20;
    static final int DPPDEF_IN_RTNLEAVECASE = 21;
    static final int DPPDEF_IN_PARALLEL_LOB_LOAD = 22;
    static final int DPPDEF_IN_NO_INDEX_ERRORS = 23;
    static final int DPPDEF_IN_PARTITION_MEMORY = 24;
    static final int DPPDEF_IN_TABLE_PREEXISTS = 25;
    static final int DPPDEF_IN_USE_ACTIVE_TRANS = 26;
    static final int DPPDEF_IN_LONG_VARCHAR = 27;
    static final int DPPDEF_IN_ISLDRCLIENT = 28;
    static final int DPPDEF_IN_COLSEGCOL_LOBORDER = 29;
    static final int DPPDEF_IN_INTCOL_LOBORDER = 30;
    static final int DPPDEF_IN_COMPRESS_UNLOAD_BUFFS = 31;
    static final int DPPDEF_IN_DPFLAGS = 32;
    static final int DPPDEF_IN_DEFAULTS = 33;
    static final int DPPDEF_IN_DEFAULT_EXPR_CACHE_SIZE = 34;
    static final int DPPDEF_IN_CONTINUE_ON_ERRORS = 35;
    private static final int DPPDEF_IN_PARMCOUNT = 36;
    static final int DPPDEF_OUT_IVRSN = 0;
    static final int DPPDEF_OUT_STRVRSN = 1;
    static final int DPPDEF_OUT_XFRSIZE = 2;
    static final int DPPDEF_OUT_RETCURSOR = 3;
    static final int DPPDEF_OUT_SDBA_SAME = 4;
    static final int DPPDEF_OUT_SDBAOFBITS = 5;
    static final int DPPDEF_OUT_SDBANFBITS = 6;
    static final int DPPDEF_OUT_SDBABITS = 7;
    static final int DPPDEF_OUT_DBABBITS = 8;
    static final int DPPDEF_OUT_DBAFNEW = 9;
    static final int DPPDEF_OUT_DBAFOLD = 10;
    static final int DPPDEF_OUT_RTNLEAVECASE = 11;
    static final int DPPDEF_OUT_IN_WORKER_PROCESS = 12;
    static final int DPPDEF_OUT_LONG_VARCHAR = 13;
    private static final int DPPDEF_OUT_PARMCOUNT = 14;
    private static final long INTERFACE_VERSION = 400;
    private static final long STREAM_VERSION = 400;
    private int dppopc;
    private final List<KeywordValueI> dppiparm;
    private long[] dppi4;
    private int dppi4l;
    private KeywordValueI[] dppoparm;
    private long[] dppo4;
    private Accessor[] describedAccessors;

    T4CTTIodpp(T4CConnection _conn) throws SQLException {
        super(_conn, (byte) 3);
        this.dppopc = 1;
        setFunCode((short) 128);
        this.dppiparm = new LinkedList();
        clearState();
    }

    private void clearState() {
        this.dppopc = 0;
        this.dppiparm.clear();
        this.dppi4 = new long[36];
        this.dppi4[11] = 65535;
        this.dppi4[16] = 65535;
        this.dppi4[17] = 65535;
        this.dppi4l = 0;
        this.dppoparm = null;
        this.dppo4 = null;
        this.describedAccessors = null;
    }

    void doODPP(String schema, String dbObject, String[] columns, String dbSubOjbect, @Blind(PropertiesBlinder.class) Properties dpStmtProps) throws SQLException, IOException {
        clearState();
        this.dppopc = 1;
        if (schema != null && schema.length() != 0) {
            setKWValue(3, schema);
        }
        setKWValue(1, dbObject);
        if (dbSubOjbect != null && dbSubOjbect.length() != 0) {
            setKWValue(2, dbSubOjbect);
        }
        for (String col : columns) {
            setKWValue(4, col);
        }
        setI4Value(0, 400L);
        setI4Value(1, 400L);
        setI4Value(14, 1L);
        if (dpStmtProps != null) {
            boolean nolog = Boolean.parseBoolean(dpStmtProps.getProperty(oracle.jdbc.internal.OracleConnection.DPPDEF_IN_NOLOG_STMT_OPTION, "false"));
            boolean parallel = Boolean.parseBoolean(dpStmtProps.getProperty(oracle.jdbc.internal.OracleConnection.DPPDEF_IN_PARALLEL_STMT_OPTION, "false"));
            String paralleFile = dpStmtProps.getProperty(oracle.jdbc.internal.OracleConnection.DPPDEF_KW_PARALLEL_FILE_STMT_OPTION);
            int storageInit = Integer.parseInt(dpStmtProps.getProperty(oracle.jdbc.internal.OracleConnection.DPPDEF_IN_STORAGE_INIT_STMT_OPTION, "0"));
            int storageNext = Integer.parseInt(dpStmtProps.getProperty(oracle.jdbc.internal.OracleConnection.DPPDEF_IN_STORAGE_NEXT_STMT_OPTION, "0"));
            boolean skipUnusableIndex = Boolean.parseBoolean(dpStmtProps.getProperty(oracle.jdbc.internal.OracleConnection.DPPDEF_IN_SKIP_UNUSABLE_INDEX_STMT_OPTION, "false"));
            boolean skipIndexMaint = Boolean.parseBoolean(dpStmtProps.getProperty(oracle.jdbc.internal.OracleConnection.DPPDEF_IN_SKIP_INDEX_MAINT_STMT_OPTION, "false"));
            if (nolog) {
                setI4Value(3, 1L);
            }
            if (parallel) {
                setI4Value(4, 1L);
            }
            if (paralleFile != null && !paralleFile.isEmpty()) {
                setKWValue(6, paralleFile);
            }
            if (storageInit > 0) {
                setI4Value(8, storageInit);
            }
            if (storageNext > 0) {
                setI4Value(9, storageNext);
            }
            if (skipUnusableIndex) {
                setI4Value(5, 1L);
            }
            if (skipIndexMaint) {
                setI4Value(6, 1L);
            }
        }
        doRPC();
    }

    private void setKWValue(int key, String value) {
        if (0 != 0) {
            this.dppiparm.add(new KeywordValueI(key, value, null));
        } else {
            this.dppiparm.add(new KeywordValueI(key, null, value.getBytes()));
        }
    }

    private void setI4Value(int key, long value) {
        this.dppi4[key] = value;
        this.dppi4l = Math.max(this.dppi4l, key + 1);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.dppopc);
        if (this.dppiparm.size() > 0) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalSWORD(this.dppiparm.size());
        if (this.dppi4l > 0) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalUWORD(this.dppi4l);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        for (KeywordValueI kv : this.dppiparm) {
            kv.marshal(this.meg);
        }
        for (int i = 0; i < this.dppi4l; i++) {
            this.meg.marshalUB4(this.dppi4[i]);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        T4CTTIdcb dcb = new T4CTTIdcb(this.connection);
        dcb.init(this.connection.dppstmt, 0);
        this.describedAccessors = dcb.receiveCommon(null, true);
        int dppoparml = this.meg.unmarshalUB2();
        this.dppoparm = new KeywordValueI[dppoparml];
        for (int i = 0; i < dppoparml; i++) {
            this.dppoparm[i] = KeywordValueI.unmarshal(this.meg);
        }
        int dppo4l = this.meg.unmarshalUB2();
        this.dppo4 = new long[dppo4l];
        for (int i2 = 0; i2 < dppo4l; i2++) {
            this.dppo4[i2] = this.meg.unmarshalUB4();
        }
    }

    byte[] getBinaryKWValue(int key) throws SQLException {
        String charValue;
        byte[] value = null;
        if (this.dppoparm != null && key < this.dppoparm.length) {
            value = this.dppoparm[key].getBinaryValue();
            if (value == null && (charValue = this.dppoparm[key].getTextValue()) != null) {
                value = this.meg.conv.StringToCharBytes(charValue);
            }
        }
        return value;
    }

    String getCharacterKWValue(int key) throws SQLException {
        String value = null;
        if (this.dppoparm != null && key < this.dppoparm.length) {
            value = this.dppoparm[key].getTextValue();
            if (value == null) {
                byte[] binValue = this.dppoparm[key].getBinaryValue();
                value = this.meg.conv.CharBytesToString(binValue, binValue.length);
            }
        }
        return value;
    }

    long getO4Value(int key) {
        if (this.dppo4 == null || this.dppo4.length <= key) {
            return 0L;
        }
        return this.dppo4[key];
    }

    Accessor[] getDescribedAccessors() {
        return this.describedAccessors;
    }
}
