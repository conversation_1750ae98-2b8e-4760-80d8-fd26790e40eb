package oracle.jdbc.babelfish;

import java.io.InputStream;
import java.io.Reader;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Date;
import java.sql.NClob;
import java.sql.PreparedStatement;
import java.sql.Ref;
import java.sql.RowId;
import java.sql.SQLException;
import java.sql.SQLXML;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;
import oracle.jdbc.OraclePreparedStatement;
import oracle.jdbc.proxy.annotation.GetCreator;
import oracle.jdbc.proxy.annotation.GetDelegate;
import oracle.jdbc.proxy.annotation.GetProxy;
import oracle.jdbc.proxy.annotation.OnError;
import oracle.jdbc.proxy.annotation.Post;
import oracle.jdbc.proxy.annotation.ProxyFor;

@ProxyFor({OraclePreparedStatement.class, PreparedStatement.class})
/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/BabelfishPreparedStatement.class */
public abstract class BabelfishPreparedStatement extends BabelfishStatement {
    @Override // oracle.jdbc.babelfish.BabelfishStatement, oracle.jdbc.babelfish.BabelfishGenericProxy
    @GetCreator
    protected abstract Object getCreator();

    @Override // oracle.jdbc.babelfish.BabelfishStatement, oracle.jdbc.babelfish.BabelfishGenericProxy
    @GetDelegate
    protected abstract Object getDelegate();

    @Override // oracle.jdbc.babelfish.BabelfishStatement
    @GetProxy
    protected abstract Object proxify(Object obj, Object obj2);

    @Override // oracle.jdbc.babelfish.BabelfishStatement, oracle.jdbc.babelfish.BabelfishGenericProxy
    @OnError(SQLException.class)
    protected Object translateError(Method m, SQLException ex) throws SQLException {
        throw this.translator.translateError(ex);
    }

    @Override // oracle.jdbc.babelfish.BabelfishStatement, oracle.jdbc.babelfish.BabelfishGenericProxy
    @Post
    protected Object post_Methods(Method m, Object result) {
        if (result instanceof BabelfishGenericProxy) {
            ((BabelfishGenericProxy) result).setTranslator(this.translator);
        }
        return result;
    }

    public void setArray(int parameterIndex, Array x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setArrayAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setAsciiStream(int parameterIndex, InputStream x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setAsciiStreamAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setAsciiStream(int parameterIndex, InputStream x, int length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setAsciiStreamAtName(String.valueOf(parameterIndex), x, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setAsciiStream(int parameterIndex, InputStream x, long length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setAsciiStreamAtName(String.valueOf(parameterIndex), x, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBigDecimal(int parameterIndex, BigDecimal x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBigDecimalAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBinaryStream(int parameterIndex, InputStream x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBinaryStreamAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBinaryStream(int parameterIndex, InputStream x, int length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBinaryStreamAtName(String.valueOf(parameterIndex), x, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBinaryStream(int parameterIndex, InputStream x, long length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBinaryStreamAtName(String.valueOf(parameterIndex), x, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBlob(int parameterIndex, Blob x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBlobAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBlob(int parameterIndex, InputStream x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBlobAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBlob(int parameterIndex, InputStream x, long length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBlobAtName(String.valueOf(parameterIndex), x, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBoolean(int parameterIndex, boolean x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBooleanAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setByte(int parameterIndex, byte x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setByteAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setBytes(int parameterIndex, byte[] x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setBytesAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setCharacterStream(int parameterIndex, Reader reader) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setCharacterStreamAtName(String.valueOf(parameterIndex), reader);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setCharacterStream(int parameterIndex, Reader reader, int length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setCharacterStreamAtName(String.valueOf(parameterIndex), reader, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setCharacterStream(int parameterIndex, Reader reader, long length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setCharacterStreamAtName(String.valueOf(parameterIndex), reader, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setClob(int parameterIndex, Clob x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setClobAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setClob(int parameterIndex, Reader reader) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setClobAtName(String.valueOf(parameterIndex), reader);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setClob(int parameterIndex, Reader reader, long length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setClobAtName(String.valueOf(parameterIndex), reader, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setDate(int parameterIndex, Date x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setDateAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setDate(int parameterIndex, Date x, Calendar cal) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setDateAtName(String.valueOf(parameterIndex), x, cal);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setDouble(int parameterIndex, double x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setDoubleAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setFloat(int parameterIndex, float x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setFloatAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setInt(int parameterIndex, int x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setIntAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setLong(int parameterIndex, long x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setLongAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setNCharacterStream(int parameterIndex, Reader reader) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setNCharacterStreamAtName(String.valueOf(parameterIndex), reader);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setNCharacterStream(int parameterIndex, Reader reader, long length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setNCharacterStreamAtName(String.valueOf(parameterIndex), reader, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setNClob(int parameterIndex, NClob x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setNClobAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setNClob(int parameterIndex, Reader reader) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setNClobAtName(String.valueOf(parameterIndex), reader);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setNClob(int parameterIndex, Reader reader, long length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setNClobAtName(String.valueOf(parameterIndex), reader, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setNString(int parameterIndex, String x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setNStringAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setNull(int parameterIndex, int sqltype) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setNullAtName(String.valueOf(parameterIndex), sqltype);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setNull(int parameterIndex, int sqltype, String typeName) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setNullAtName(String.valueOf(parameterIndex), sqltype, typeName);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setObject(int parameterIndex, Object x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setObjectAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setObject(int parameterIndex, Object x, int targetSqlType) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setObjectAtName(String.valueOf(parameterIndex), x, targetSqlType);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setObject(int parameterIndex, Object x, int targetSqlType, int scaleOrLength) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setObjectAtName(String.valueOf(parameterIndex), x, targetSqlType, scaleOrLength);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setRef(int parameterIndex, Ref x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setRefAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setRowId(int parameterIndex, RowId x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setRowIdAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setShort(int parameterIndex, short x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setShortAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setSQLXML(int parameterIndex, SQLXML x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setSQLXMLAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setString(int parameterIndex, String x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setStringAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setTime(int parameterIndex, Time x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setTimeAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setTime(int parameterIndex, Time x, Calendar cal) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setTimeAtName(String.valueOf(parameterIndex), x, cal);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setTimestamp(int parameterIndex, Timestamp x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setTimestampAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setTimestamp(int parameterIndex, Timestamp x, Calendar cal) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setTimestampAtName(String.valueOf(parameterIndex), x, cal);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setUnicodeStream(int parameterIndex, InputStream x, int length) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setUnicodeStreamAtName(String.valueOf(parameterIndex), x, length);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void setURL(int parameterIndex, URL x) throws SQLException {
        try {
            ((OraclePreparedStatement) getDelegate()).setURLAtName(String.valueOf(parameterIndex), x);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }
}
