package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_th.class */
public class ErrorMessagesJson_th extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "เกิดข้อผิดพลาด I/O"}, new Object[]{"26302", "ไม่รองรับปี \"{0}\""}, new Object[]{"26303", "โอเวอร์โฟลว์ ค่ามากเกินไป: {0}"}, new Object[]{"26304", "ไม่รองรับตัวเลือก (ไม่ได้ใช้งาน)"}, new Object[]{"26305", "JSON ไบนารีไม่ถูกต้องหรือเสียหาย"}, new Object[]{"26306", "ไม่รองรับเวอร์ชันของ JSON ไบนารี: {0}"}, new Object[]{"26307", "คีย์ที่เข้ารหัส UTF-8 ต้องมีความยาวไม่เกิน 256 ไบต์ คีย์ต่อไปนี้เกินขีดจำกัดนี้: \"{0}\""}, new Object[]{"26308", "JSON ที่ระบุมีขนาดใหญ่เกินกว่าที่จะเข้ารหัสเป็น JSON ไบนารี  ขนาดรูปภาพที่เข้ารหัสต้องไม่เกิน 2GB"}, new Object[]{"26309", "JSON ไบนารีไม่ถูกต้องหรือเสียหาย รูปภาพที่ระบุมีเพียง {0} ไบต์"}, new Object[]{"26310", "java.time.Period ที่ระบุมีการตั้งค่าจำนวนวันไว้ แต่ช่วงเวลาจากต้นปีถึงเดือนของ Oracle ไม่รองรับจำนวนวัน"}, new Object[]{"26311", "ตัวสร้างปิดก่อนที่จะสิ้นสุด"}, new Object[]{"26312", "ต้องระบุคีย์ออบเจกต์ในคอนเท็กซ์นี้"}, new Object[]{"26313", "การเขียนไม่ถูกต้อง มีการเขียนค่าที่สมบูรณ์แล้ว"}, new Object[]{"26314", "ไม่สามารถใช้จุดสิ้นสุดในคอนเท็กซ์นี้"}, new Object[]{"26315", "ไม่สามารถใช้คีย์ในคอนเท็กซ์นี้"}, new Object[]{"26316", "ค่าที่ต้องการหลังจากคีย์"}, new Object[]{"26317", "สถานะของพาร์เซอร์ต้องเป็น {0}"}, new Object[]{"26318", "สถานะของพาร์เซอร์ต้องไม่เป็น {0}"}, new Object[]{"26319", "พาร์เซอร์ต้องทำงานกับค่า"}, new Object[]{"26320", "\"{0}\" ไม่ใช่ประเภทแรปเปอร์ที่รองรับ"}, new Object[]{"26321", "ไม่สามารถแก้ไขออบเจกต์นี้ หากต้องการสร้างสำเนาที่แก้ไขได้ ให้ใช้ OracleJsonFactory.createObject(OracleJsonObject)"}, new Object[]{"26322", "ไม่สามารถแก้ไขอาเรย์นี้ หากต้องการสร้างสำเนาที่แก้ไขได้ ให้ใช้ OracleJsonFactory.createArray(OracleJsonArray)"}, new Object[]{"26323", "ออบเจกต์ JSON มีคีย์ที่ซ้ำกัน: {0}"}, new Object[]{"26324", "ไม่สามารถตรวจหาการเข้ารหัสอัตโนมัติ เนื่องจากอักขระไม่เพียงพอ"}, new Object[]{"26325", "ต้องการโทเค็น EOF แต่ได้รับ {0}"}, new Object[]{"26326", "อักขระที่ไม่คาดหมาย {0} ที่บรรทัด {1}, คอลัมน์ {2}"}, new Object[]{"26327", "อักขระที่ไม่คาดหมาย {0} ที่บรรทัด {1}, คอลัมน์ {2} ต้องการ {3}"}, new Object[]{"26328", "โทเค็นไม่ถูกต้อง {0} ที่บรรทัด {1}, คอลัมน์ {2} คอลัมน์ที่ต้องการคือ: {3}"}, new Object[]{"26329", "JsonParser#getString() ใช้ได้เฉพาะ KEY_NAME, VALUE_STRING, สถานะพาร์เซอร์ VALUE_NUMBER แต่สถานะพาร์เซอร์ปัจจุบันคือ {0}"}, new Object[]{"26330", "JsonParser#isIntegralNumber() ใช้ได้เฉพาะสถานะพาร์เซอร์ VALUE_NUMBER แต่สถานะพาร์เซอร์ปัจจุบันคือ {0}"}, new Object[]{"26331", "JsonParser#getInt() ใช้ได้เฉพาะสถานะพาร์เซอร์ VALUE_NUMBER แต่สถานะพาร์เซอร์ปัจจุบันคือ {0}"}, new Object[]{"26332", "JsonParser#getLong() ใช้ได้เฉพาะสถานะพาร์เซอร์ VALUE_NUMBER แต่สถานะพาร์เซอร์ปัจจุบันคือ {0}"}, new Object[]{"26333", "JsonParser#getBigDecimal() ใช้ได้เฉพาะสถานะพาร์เซอร์ VALUE_NUMBER แต่สถานะพาร์เซอร์ปัจจุบันคือ {0}"}, new Object[]{"26334", "JsonParser#getArray() ใช้ได้เฉพาะสำหรับสถานะพาร์เซอร์ START_ARRAY แต่สถานะพาร์เซอร์ปัจจุบันคือ {0}"}, new Object[]{"26335", "JsonParser#getObject() ใช้ได้เฉพาะสำหรับสถานะพาร์เซอร์ START_OBJECT แต่สถานะพาร์เซอร์ปัจจุบันคือ {0}"}, new Object[]{"26336", "ไม่รองรับเวลาระบบที่มีพื้นที่ ระบบจะรองรับโซนเวลาออฟเซ็ตเท่านั้น"}, new Object[]{"26337", "ออบเจกต์และอาร์เรย์ในค่า JSON ต้องไม่ซ้อนกันลึกกว่า {0} ระดับ"}, new Object[]{"26338", "คีย์ของออบเจกต์ JSON ต้องไม่เกิน 65,535 ไบต์"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
