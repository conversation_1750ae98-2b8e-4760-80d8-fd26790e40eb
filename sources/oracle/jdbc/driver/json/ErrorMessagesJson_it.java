package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_it.class */
public class ErrorMessagesJson_it extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Si è verificata un'eccezione I/O"}, new Object[]{"26302", "L''anno \"{0}\" non è supportato"}, new Object[]{"26303", "Overflow, valore troppo grande: {0}."}, new Object[]{"26304", "Opzione non supportata (non implementata)."}, new Object[]{"26305", "Il file binario JSON non è valido o è danneggiato."}, new Object[]{"26306", "Versione del file binario JSON non supportata: {0}."}, new Object[]{"26307", "La lunghezza della chiave con codifica UTF-8 non deve essere maggiore di 256 byte. La chiave seguente supera questo limite: \"{0}\"."}, new Object[]{"26308", "Il file JSON specificato è troppo grande per essere codificato come file binario JSON. Le dimensioni delle immagini codificate non deve superare i 2 GB."}, new Object[]{"26309", "Il file binario JSON non è valido o è danneggiato. L''immagine specificata contiene solo {0} byte."}, new Object[]{"26310", "Per java.time.Period specificato sono stati impostati i giorni, ma l'intervallo Oracle in anni e mesi non supporta i giorni."}, new Object[]{"26311", "Generatore chiuso prima del termine."}, new Object[]{"26312", "È necessario specificare una chiave oggetto in questo contesto."}, new Object[]{"26313", "Scrittura non valida. Un valore completo è già stato scritto."}, new Object[]{"26314", "Fine non consentita in questo contesto."}, new Object[]{"26315", "Chiave non consentita in questo contesto."}, new Object[]{"26316", "È previsto un valore dopo la chiave."}, new Object[]{"26317", "Lo stato del parser deve essere {0}."}, new Object[]{"26318", "Lo stato del parser non deve essere {0}."}, new Object[]{"26319", "Il parser deve essere su un valore."}, new Object[]{"26320", "\"{0}\" non è un tipo di wrapper supportato."}, new Object[]{"26321", "Questo oggetto non può essere modificato. Per creare una copia modificabile, utilizzare OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Questo array non può essere modificato. Per creare una copia modificabile, utilizzare OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "L''oggetto JSON contiene una chiave duplicata: {0}."}, new Object[]{"26324", "Impossibile rilevare automaticamente la codifica, caratteri insufficienti."}, new Object[]{"26325", "Previsto token EOF, ma ottenuto {0}."}, new Object[]{"26326", "Carattere {0} imprevisto alla riga {1}, colonna {2}."}, new Object[]{"26327", "Carattere {0} imprevisto alla riga {1}, colonna {2}. Carattere previsto: {3}."}, new Object[]{"26328", "Token {0} non valido alla riga {1}, colonna {2}. Token previsti: {3}."}, new Object[]{"26329", "JsonParser#getString() è valido solo per gli stati del parser KEY_NAME, VALUE_STRING, VALUE_NUMBER. Tuttavia, lo stato corrente del parser è {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() è valido solo per lo stato del parser VALUE_NUMBER. Tuttavia, lo stato del parser corrente è {0}."}, new Object[]{"26331", "JsonParser#getInt() è valido solo per lo stato del parser VALUE_NUMBER. Tuttavia, lo stato del parser corrente è {0}."}, new Object[]{"26332", "JsonParser#getLong() è valido solo per lo stato del parser VALUE_NUMBER. Tuttavia, lo stato del parser corrente è {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() è valido solo per lo stato del parser VALUE_NUMBER. Tuttavia, lo stato del parser corrente è {0}."}, new Object[]{"26334", "JsonParser#getArray() è valido solo per lo stato del parser START_ARRAY, ma lo stato corrente del parser è {0}."}, new Object[]{"26335", "L''oggetto JsonParser-getObject() è valido solo per lo stato del parser START_OBJECT. Tuttavia, lo stato del parser corrente è {0}."}, new Object[]{"26336", "Indicatore orario con un'area non supportato. Sono supportati solo i fusi orari di offset."}, new Object[]{"26337", "Gli oggetti e gli array nel valore JSON potrebbero non essere nidificati più in profondità di {0} livelli"}, new Object[]{"26338", "Le chiavi di un oggetto JSON non possono superare 65.535 byte"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
