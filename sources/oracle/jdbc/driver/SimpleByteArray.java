package oracle.jdbc.driver;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.MessageDigest;
import java.sql.SQLException;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.driver.ByteArray;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/SimpleByteArray.class */
class SimpleByteArray extends ByteArray {
    protected byte[] bytes;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !SimpleByteArray.class.desiredAssertionStatus();
    }

    protected SimpleByteArray(Diagnosable diagnosable, byte[] _bytes) {
        super(diagnosable);
        setBytes(_bytes);
    }

    void setBytes(byte[] _bytes) {
        this.bytes = _bytes;
    }

    @Override // oracle.jdbc.driver.ByteArray
    long length() {
        return this.bytes.length;
    }

    @Override // oracle.jdbc.driver.ByteArray
    void put(long index, byte value) throws SQLException {
        if (!$assertionsDisabled && (index < 0 || index >= this.bytes.length)) {
            throw new AssertionError("index = " + index + " length = " + this.bytes.length);
        }
        this.bytes[(int) index] = value;
    }

    @Override // oracle.jdbc.driver.ByteArray
    byte get(long index) {
        if ($assertionsDisabled || (index >= 0 && index < this.bytes.length)) {
            return this.bytes[(int) index];
        }
        throw new AssertionError("index = " + index + " length = " + this.bytes.length);
    }

    @Override // oracle.jdbc.driver.ByteArray
    void put(long offset, byte[] src, int srcOffset, int length) throws SQLException {
        if (!$assertionsDisabled && (offset < 0 || offset + length > this.bytes.length)) {
            throw new AssertionError("offset = " + offset + " length = " + length + " bytes.length = " + this.bytes.length);
        }
        if (!$assertionsDisabled && (srcOffset < 0 || srcOffset + length > src.length)) {
            throw new AssertionError("srcOffset = " + srcOffset + " length = " + length + " src.length = " + src.length);
        }
        System.arraycopy(src, srcOffset, this.bytes, (int) offset, length);
    }

    @Override // oracle.jdbc.driver.ByteArray
    void get(long offset, byte[] dest, int destOffset, int length) {
        if (!$assertionsDisabled && (offset < 0 || offset + length > this.bytes.length || destOffset < 0 || destOffset + length > dest.length)) {
            throw new AssertionError(" offset: " + offset + " bytes.length: " + this.bytes.length + " destOffset: " + destOffset + " length: " + length);
        }
        System.arraycopy(this.bytes, (int) offset, dest, destOffset, length);
    }

    char[] getChars(long offset, int lengthInBytes, DBConversion conversion, int formOfUse, int[] out_lengthInChars) throws SQLException {
        if (!$assertionsDisabled && (offset < 0 || lengthInBytes < 0 || this.bytes.length < offset + lengthInBytes)) {
            throw new AssertionError("bytes.length: " + this.bytes.length + " offset: " + offset + " lengthInBytes: " + lengthInBytes);
        }
        if (!$assertionsDisabled && conversion == null) {
            throw new AssertionError("conversion is null");
        }
        if (!$assertionsDisabled && (out_lengthInChars == null || out_lengthInChars.length < 1)) {
            throw new AssertionError("out_lengthInChars: " + out_lengthInChars);
        }
        boolean isNchar = formOfUse == 2;
        char[] cBuf = new char[lengthInBytes * conversion.cMaxCharSize];
        int[] nbytes = {lengthInBytes};
        int charsConverted = conversion.CHARBytesToJavaChars(this.bytes, (int) offset, cBuf, 0, nbytes, cBuf.length, isNchar);
        out_lengthInChars[0] = charsConverted;
        return cBuf;
    }

    @Override // oracle.jdbc.driver.ByteArray
    char[] getChars(long offset, int lengthInBytes, CharacterSet charSet, int[] out_lengthInChars) throws SQLException {
        if (!$assertionsDisabled && (offset < 0 || lengthInBytes < 0)) {
            throw new AssertionError("offset: " + offset + " lengthInBytes: " + lengthInBytes);
        }
        if (!$assertionsDisabled && this.bytes.length < offset + lengthInBytes) {
            throw new AssertionError("bytes.length: " + this.bytes.length + " offset: " + offset + " lengthInBytes: " + lengthInBytes);
        }
        if (!$assertionsDisabled && (out_lengthInChars == null || out_lengthInChars.length <= 0)) {
            throw new AssertionError("out_lengthInChars: " + out_lengthInChars);
        }
        String s = charSet.toString(this.bytes, (int) offset, lengthInBytes);
        char[] c = s.toCharArray();
        out_lengthInChars[0] = c.length;
        return c;
    }

    @Override // oracle.jdbc.driver.ByteArray
    long updateChecksum(long offset, int length, CRC64 crc, long checksum) {
        return CRC64.updateChecksum(checksum, this.bytes, (int) offset, length);
    }

    @Override // oracle.jdbc.driver.ByteArray
    void updateDigest(MessageDigest md, long valOffset, int valLen) {
        if (!$assertionsDisabled && (valOffset < 0 || valOffset + valLen > this.bytes.length)) {
            throw new AssertionError(" valOffset: " + valOffset + " bytes.length: " + this.bytes.length + " valLen: " + valLen);
        }
        md.update(this.bytes, (int) valOffset, valLen);
    }

    @Override // oracle.jdbc.driver.ByteArray
    byte[] getBlockBasic(long offset, int[] initialByteIndex) {
        initialByteIndex[0] = (int) offset;
        if (offset < this.bytes.length) {
            return this.bytes;
        }
        return null;
    }

    @Override // oracle.jdbc.driver.ByteArray
    void free(boolean returnBlocksToSource) {
    }

    @Override // oracle.jdbc.driver.ByteArray
    long getCapacity() {
        return this.bytes.length;
    }

    @Override // oracle.jdbc.driver.ByteArray
    void putFloats(long offset, float[] floatArray, int arrayOffset, int length, ByteArray.NumberEncoding encoding) {
        ByteBuffer.wrap(this.bytes, (int) offset, this.bytes.length - ((int) offset)).order(encoding.byteOrder()).asFloatBuffer().put(floatArray, arrayOffset, length);
        if (encoding.isIeee()) {
            return;
        }
        int lastByteIndex = ((int) offset) + (length << 2);
        int msbOffset = encoding.byteOrder() == ByteOrder.LITTLE_ENDIAN ? 3 : 0;
        for (int i = (int) offset; i < lastByteIndex; i += 4) {
            if ((this.bytes[i + msbOffset] & 128) == 0) {
                byte[] bArr = this.bytes;
                int i2 = i + msbOffset;
                bArr[i2] = (byte) (bArr[i2] | 128);
            } else {
                this.bytes[i] = (byte) (this.bytes[i] ^ (-1));
                this.bytes[i + 1] = (byte) (this.bytes[i + 1] ^ (-1));
                this.bytes[i + 2] = (byte) (this.bytes[i + 2] ^ (-1));
                this.bytes[i + 3] = (byte) (this.bytes[i + 3] ^ (-1));
            }
        }
    }

    @Override // oracle.jdbc.driver.ByteArray
    void putDoubles(long offset, double[] doubleArray, int arrayOffset, int length, ByteArray.NumberEncoding encoding) {
        ByteBuffer.wrap(this.bytes, (int) offset, this.bytes.length - ((int) offset)).order(encoding.byteOrder()).asDoubleBuffer().put(doubleArray, arrayOffset, length);
        if (encoding.isIeee()) {
            return;
        }
        int lastByteIndex = ((int) offset) + (length << 3);
        int msbOffset = encoding.byteOrder() == ByteOrder.LITTLE_ENDIAN ? 7 : 0;
        for (int i = (int) offset; i < lastByteIndex; i += 8) {
            if ((this.bytes[i + msbOffset] & 128) == 0) {
                byte[] bArr = this.bytes;
                int i2 = i + msbOffset;
                bArr[i2] = (byte) (bArr[i2] | 128);
            } else {
                this.bytes[i] = (byte) (this.bytes[i] ^ (-1));
                this.bytes[i + 1] = (byte) (this.bytes[i + 1] ^ (-1));
                this.bytes[i + 2] = (byte) (this.bytes[i + 2] ^ (-1));
                this.bytes[i + 3] = (byte) (this.bytes[i + 3] ^ (-1));
                this.bytes[i + 4] = (byte) (this.bytes[i + 4] ^ (-1));
                this.bytes[i + 5] = (byte) (this.bytes[i + 5] ^ (-1));
                this.bytes[i + 6] = (byte) (this.bytes[i + 6] ^ (-1));
                this.bytes[i + 7] = (byte) (this.bytes[i + 7] ^ (-1));
            }
        }
    }

    @Override // oracle.jdbc.driver.ByteArray
    void getFloats(long offset, float[] floatArray, int arrayOffset, int length, ByteArray.NumberEncoding encoding) {
        if (!encoding.isIeee()) {
            super.getFloats(offset, floatArray, arrayOffset, length, encoding);
        } else {
            ByteBuffer.wrap(this.bytes, (int) offset, this.bytes.length - ((int) offset)).order(encoding.byteOrder()).asFloatBuffer().get(floatArray, arrayOffset, length);
        }
    }

    @Override // oracle.jdbc.driver.ByteArray
    void getDoubles(long offset, double[] doubleArray, int arrayOffset, int length, ByteArray.NumberEncoding encoding) {
        if (!encoding.isIeee()) {
            super.getDoubles(offset, doubleArray, arrayOffset, length, encoding);
        } else {
            ByteBuffer.wrap(this.bytes, (int) offset, this.bytes.length - ((int) offset)).order(encoding.byteOrder()).asDoubleBuffer().get(doubleArray, arrayOffset, length);
        }
    }
}
