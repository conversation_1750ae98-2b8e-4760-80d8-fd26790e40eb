package oracle.jdbc.diagnostics;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;
import oracle.jdbc.diagnostics.Metrics;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/MetricsImpl.class */
public class MetricsImpl extends Metrics {
    private final Map<String, long[]> connectionMetrics = new HashMap();
    private final Map<Metrics.ConnectionEvent, Byte> countMetrics = new EnumMap(Metrics.ConnectionEvent.class);
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !MetricsImpl.class.desiredAssertionStatus();
    }

    @Override // oracle.jdbc.diagnostics.Metrics
    public void begin(Metrics.ConnectionEvent event) {
        int retryIdentifier = this.countMetrics.getOrDefault(event, (byte) 0).byteValue();
        String eventName = retryIdentifier == 0 ? event.getName() : event.getName() + " " + retryIdentifier;
        this.connectionMetrics.computeIfAbsent(eventName, v -> {
            return new long[]{System.nanoTime(), 0};
        });
    }

    @Override // oracle.jdbc.diagnostics.Metrics
    public void end(Metrics.ConnectionEvent event) {
        Byte retryIdentifier = this.countMetrics.getOrDefault(event, (byte) 0);
        String eventName = retryIdentifier.byteValue() == 0 ? event.getName() : event.getName() + " " + retryIdentifier;
        long[] value = this.connectionMetrics.get(eventName);
        if (!$assertionsDisabled && value == null) {
            throw new AssertionError(String.format("end() called before begin for event %s.", event));
        }
        if (!$assertionsDisabled && value[1] != 0) {
            throw new AssertionError(String.format("end() already called for event %s.", event));
        }
        if (value != null && value[1] == 0) {
            value[1] = System.nanoTime();
        }
        this.countMetrics.put(event, Byte.valueOf((byte) (retryIdentifier.byteValue() + 1)));
    }

    @Override // oracle.jdbc.diagnostics.Metrics
    public void close() {
        add(this.connectionMetrics);
    }
}
