package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoclfeatures.class */
public class T4CTTIoclfeatures extends T4CTTIfun {
    private static final int TOTAL_UB8_BITMAPS_COUNT = 4;
    private static final int UB8_SIZE = 64;
    private long[] ub8BitmapBuckets;
    private int clientFeaturesCount;

    @Override // oracle.jdbc.driver.T4CTTIMsg, oracle.jdbc.diagnostics.Diagnosable
    public /* bridge */ /* synthetic */ Diagnosable getDiagnosable() {
        return super.getDiagnosable();
    }

    T4CTTIoclfeatures(T4CConnection _conn) {
        super(_conn, (byte) 17);
        this.ub8BitmapBuckets = new long[4];
        this.clientFeaturesCount = 0;
    }

    void add(OracleConnection.ClientFeature cf) {
        int ub8BucketIdx = cf.getFeatureId() / 64;
        long bitMask = 1 << (cf.getFeatureId() % 64);
        if ((this.ub8BitmapBuckets[ub8BucketIdx] & bitMask) != 0) {
            return;
        }
        long[] jArr = this.ub8BitmapBuckets;
        jArr[ub8BucketIdx] = jArr[ub8BucketIdx] | bitMask;
        this.clientFeaturesCount++;
    }

    void doOCLFEATURES() throws IOException {
        if (this.clientFeaturesCount > 0) {
            setFunCode((short) 191);
            doPigRPC();
            this.ub8BitmapBuckets = new long[4];
            this.clientFeaturesCount = 0;
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        try {
            StringBuilder ub8BitmapsStringBuilder = new StringBuilder();
            for (int i = 0; i < this.ub8BitmapBuckets.length - 1; i++) {
                ub8BitmapsStringBuilder.append(this.ub8BitmapBuckets[i] + ",");
            }
            ub8BitmapsStringBuilder.append(this.ub8BitmapBuckets[this.ub8BitmapBuckets.length - 1]);
            byte[] clientFeaturesAsByteArray = this.meg.conv.StringToCharBytes(ub8BitmapsStringBuilder.toString());
            this.meg.marshalPTR();
            this.meg.marshalSWORD(clientFeaturesAsByteArray.length);
            this.meg.marshalSB8(0L);
            this.meg.marshalCHR(clientFeaturesAsByteArray);
        } catch (SQLException sqlEx) {
            IOException ioEx = new IOException();
            ioEx.initCause(sqlEx.getCause());
            throw ioEx;
        }
    }
}
