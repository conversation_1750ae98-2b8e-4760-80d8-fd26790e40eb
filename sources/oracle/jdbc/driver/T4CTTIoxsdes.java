package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.XSSecureId;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxsdes.class */
final class T4CTTIoxsdes extends T4CTTIfun {
    private byte[] kpxsdesopsid;
    private XSSecureId kpxsdesopsidp;
    private byte[] kpxsdesopcookie;

    T4CTTIoxsdes(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 182);
    }

    void doOXSDES(byte[] kpxsdesopsid, XSSecureId kpxsdesopsidp, byte[] kpxsdesopcookie) throws SQLException, IOException {
        this.kpxsdesopsid = kpxsdesopsid;
        this.kpxsdesopsidp = kpxsdesopsidp;
        this.kpxsdesopcookie = kpxsdesopcookie;
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        boolean sendSid = false;
        if (this.kpxsdesopsid != null && this.kpxsdesopsid.length > 0) {
            sendSid = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.kpxsdesopsid.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendSidp = false;
        if (this.kpxsdesopsidp != null) {
            sendSidp = true;
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        boolean sendcookie = false;
        if (this.kpxsdesopcookie != null && this.kpxsdesopcookie.length > 0) {
            sendcookie = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.kpxsdesopcookie.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (sendSid) {
            this.meg.marshalB1Array(this.kpxsdesopsid);
        }
        if (sendSidp) {
            ((XSSecureIdI) this.kpxsdesopsidp).marshal(this.meg);
        }
        if (sendcookie) {
            this.meg.marshalB1Array(this.kpxsdesopcookie);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
