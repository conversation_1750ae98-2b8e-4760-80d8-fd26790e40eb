package oracle.jdbc.diagnostics;

import java.util.Map;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/DMSTraceId.class */
public class DMSTraceId {
    private final Map<String, Map<String, String>> desired;

    public static DMSTraceId create(Map<String, Map<String, String>> desired) {
        return new DMSTraceId(desired);
    }

    private DMSTraceId(Map<String, Map<String, String>> desired) {
        this.desired = desired;
    }

    public Map<String, Map<String, String>> getDmsContext() {
        return this.desired;
    }
}
