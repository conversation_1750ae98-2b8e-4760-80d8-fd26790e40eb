package oracle.jdbc.driver.json.tree;

import java.io.IOException;
import java.math.BigDecimal;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.NUMBER;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonStringNumberImpl.class */
public class OracleJsonStringNumberImpl extends OracleJsonNumberImpl implements OracleJsonDecimal {
    String value;

    public OracleJsonStringNumberImpl(String value) {
        this.value = value;
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public String getString() {
        return this.value;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.DECIMAL;
    }

    @Override // oracle.sql.json.OracleJsonDecimal
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonDecimal)) {
            return false;
        }
        OracleJsonDecimal othern = (OracleJsonDecimal) other;
        return bigDecimalValue().equals(othern.bigDecimalValue());
    }

    @Override // oracle.sql.json.OracleJsonDecimal
    public int hashCode() {
        return bigDecimalValue().hashCode();
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public BigDecimal bigDecimalValue() {
        return new BigDecimal(this.value);
    }

    public void serialize(Appendable out) throws IOException {
        try {
            out.append(this.value);
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
        }
    }

    @Override // oracle.sql.json.OracleJsonDecimal
    public NUMBER getNUMBER() {
        throw new UnsupportedOperationException();
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaStringNumberImpl(this.value));
        }
        return c.cast(new JsonpPrimitive.JsonpStringNumberImpl(this.value));
    }

    @Override // oracle.sql.json.OracleJsonDecimal
    public OracleJsonDecimal.TargetType getTargetType() {
        return null;
    }
}
