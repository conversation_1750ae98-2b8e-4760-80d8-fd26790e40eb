package oracle.jdbc.diagnostics;

import java.util.logging.Handler;
import java.util.logging.Level;
import java.util.logging.LogRecord;
import java.util.logging.MemoryHandler;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/TraceMemoryHandler.class */
public class Trace<PERSON><PERSON>oryHandler extends MemoryHandler {
    public TraceMemoryHandler(Handler target, int size, Level pushLevel) {
        super(target, size, pushLevel);
    }

    @Override // java.util.logging.MemoryHandler, java.util.logging.Handler
    public boolean isLoggable(LogRecord record) {
        return true;
    }
}
