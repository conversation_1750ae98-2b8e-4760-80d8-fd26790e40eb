package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_ar.class */
public class ErrorMessagesJson_ar extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "حدث استثناء في المدخلات/المخرجات"}, new Object[]{"26302", "السنة \"{0}\" غير مدعومة"}, new Object[]{"26303", "التجاوز، القيمة كبيرة للغاية: {0}."}, new Object[]{"26304", "خيار غير مدعوم (غير مُنفَّذ)."}, new Object[]{"26305", "يكون JSON الثنائي غير صالح أو تالفًا."}, new Object[]{"26306", "إصدار JSON الثنائي غير مدعوم: {0}."}, new Object[]{"26307", "يجب ألا يكون طول المفتاح بتشفير UTF-8 أكبر من 256 بايت. يتجاوز المفتاح التالي هذا الحد: \"{0}\"."}, new Object[]{"26308", "يكون JSON المُحدَّد كبيرًا للغاية ليتم تشفيره كـ JSON ثنائي. يجب ألا يتجاوز حجم الصور المُشفَّرة 2 جيجابايت."}, new Object[]{"26309", "يكون JSON الثنائي غير صالح أو تالفًا. تحتوي الصور المُحدَّدة على {0} من وحدات البايت فقط."}, new Object[]{"26310", "تم تعيين أيام في java.time.Period المُحدَّد، ولكن لا يدعم الفاصل الزمني \"سنة إلى شهر\" في Oracle الأيام."}, new Object[]{"26311", "تم إغلاق المُنشئ قبل النهاية."}, new Object[]{"26312", "يجب تحديد مفتاح الكائن في هذا السياق."}, new Object[]{"26313", "كتابة غير صالحة. تمت كتابة قيمة مكتملة."}, new Object[]{"26314", "النهاية غير مسموح بها في هذا السياق."}, new Object[]{"26315", "المفتاح غير مسموح به في هذا السياق."}, new Object[]{"26316", "القيمة المتوقعة بعد المتاح."}, new Object[]{"26317", "يجب أن تكون حالة المحلل {0}."}, new Object[]{"26318", "يجب ألا تكون حالة المحلل {0}."}, new Object[]{"26319", "يجب أن يكون المحلل على قيمة."}, new Object[]{"26320", "لا يكون \"{0}\" نوع الغلاف المدعوم."}, new Object[]{"26321", "لا يمكن تعديل هذا الكائن. وللحصول على نسخة معدلة، يلزم استخدام OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "لا يمكن تعديل هذا الصفيف. وللحصول على نسخة معدلة، يلزم استخدام OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "يشتمل كائن JSON على مفتاح مكرر: {0}."}, new Object[]{"26324", "لا يمكن اكتشاف الترميز آليًا، لا توجد أحرف كافية."}, new Object[]{"26325", "كان المتوقع هو مقطع نهاية الملف EOF، لكن تم الحصول على {0}."}, new Object[]{"26326", "حرف غير متوقع {0} في السطر {1}، العمود {2}."}, new Object[]{"26327", "حرف غير متوقع {0} في السطر {1}، العمود {2}. المتوقع {3}."}, new Object[]{"26328", "رمز غير صالح {0} في السطر {1}، العمود {2}. الرموز المتوقعة هي: {3}."}, new Object[]{"26329", "JsonParser#getString() صالحة فقط لحالات المحلل اللغوي KEY_NAME، وVALUE_STRING، وVALUE_NUMBER. لكن الحالة الحالية للمحلل اللغوي هي {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() صالحة فقط لحالة المحلل اللغوي VALUE_NUMBER. لكن الحالة الحالية للمحلل اللغوي هي {0}."}, new Object[]{"26331", "JsonParser#getInt() صالحة فقط لحالة المحلل اللغوي VALUE_NUMBER. لكن الحالة الحالية للمحلل اللغوي هي {0}."}, new Object[]{"26332", "JsonParser#getLong() صالحة فقط لحالة المحلل اللغوي VALUE_NUMBER. لكن الحالة الحالية للمحلل اللغوي هي {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() صالحة فقط لحالة المحلل اللغوي VALUE_NUMBER. لكن الحالة الحالية للمحلل اللغوي هي {0}."}, new Object[]{"26334", "JsonParser#getArray() صالحة فقط لحالة المحلل اللغوي. لكن الحالة الحالية للمحلل اللغوي هي {0}."}, new Object[]{"26335", "JsonParser#getObject() صالحة فقط لحالة المحلل اللغوي START_OBJECT. لكن الحالة الحالية للمحلل اللغوي هي {0}."}, new Object[]{"26336", "طابع زمني بمنطقة غير مدعوم. لا يتم دعم سوى المناطق الزمنية للمقاصة."}, new Object[]{"26337", "لا يجوز للكائنات والمصفوفات الموجودة في قيمة JSON أن تتداخل بشكل أعمق من {0} مستوى."}, new Object[]{"26338", "لا يجوز أن تتجاوز مفاتيح كائن JSON 65.535 بايت"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
