package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_pt.class */
public class ErrorMessagesJson_pt extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Ocorreu uma exceção de I/O"}, new Object[]{"26302", "O ano \"{0}\" não é suportado"}, new Object[]{"26303", "Excesso de dados, valor demasiado grande: {0}."}, new Object[]{"26304", "Opção não suportada (não implementada)."}, new Object[]{"26305", "O JSON binário é inválido ou está corrompido."}, new Object[]{"26306", "Versão de JSON binário não suportada: {0}."}, new Object[]{"26307", "O comprimento da chave codificada em UTF-8 não deve ser superior a 256 bytes. A chave seguinte excede este limite: \"{0}\"."}, new Object[]{"26308", "O JSON especificado é demasiado grande para ser codificado como JSON binário. O tamanho das imagens codificadas não deve exceder os 2 GB."}, new Object[]{"26309", "O JSON binário é inválido ou está corrompido. A imagem especificada contém apenas {0} bytes."}, new Object[]{"26310", "O java.time.Period especificado tem dias definidos, mas o intervalo de ano para mês da Oracle não suporta dias."}, new Object[]{"26311", "Gerador fechado antes do fim."}, new Object[]{"26312", "Deve ser especificada uma chave de objeto neste contexto."}, new Object[]{"26313", "Escrita inválida. Já foi escrito um valor completo."}, new Object[]{"26314", "Fim não permitido neste contexto."}, new Object[]{"26315", "Chave não permitida neste contexto."}, new Object[]{"26316", "Valor esperado após a chave."}, new Object[]{"26317", "O estado do analisador deve ser {0}."}, new Object[]{"26318", "O estado do analisador não deve ser {0}."}, new Object[]{"26319", "O analisador deve ser sobre um valor."}, new Object[]{"26320", "\"{0}\" não é um tipo de wrapper suportado."}, new Object[]{"26321", "Este objeto não pode ser modificado. Para criar uma cópia modificável, utilize OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Esta matriz não pode ser modificada. Para criar uma cópia modificável, utilize OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "o objeto de JSON contém uma chave em duplicado: {0}."}, new Object[]{"26324", "Não é possível detetar automaticamente a codificação, caracteres insuficientes."}, new Object[]{"26325", "Símbolo EOF esperado, mas foi recebido {0}."}, new Object[]{"26326", "Carácter {0} inesperado na linha {1}, coluna {2}."}, new Object[]{"26327", "Carácter {0} inesperado na linha {1}, coluna {2}. Esperado: {3}."}, new Object[]{"26328", "Símbolo {0} inválido na linha {1}, coluna {2}. Os símbolos esperados são: {3}."}, new Object[]{"26329", "JsonParser#getString() é válido apenas para estados do analisador KEY_NAME, VALUE_STRING, VALUE_NUMBER. No entanto, o atual estado do analisador é {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() é válido apenas para o estado do analisador VALUE_NUMBER. No entanto, o atual estado do analisador é {0}."}, new Object[]{"26331", "JsonParser#getInt() é válido apenas para o estado do analisador VALUE_NUMBER. No entanto, o atual estado do analisador é {0}."}, new Object[]{"26332", "JsonParser#getLong() é válido apenas para o estado do analisador VALUE_NUMBER. No entanto, o atual estado do analisador é {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() é válido apenas para o estado do analisador VALUE_NUMBER. No entanto, o atual estado do analisador é {0}."}, new Object[]{"26334", "JsonParser#getArray() é válido apenas para o estado do analisador START_ARRAY. No entanto, o estado atual do analisador é {0}."}, new Object[]{"26335", "JsonParser#getObject() é válido apenas para o estado do analisador START_OBJECT. No entanto, o atual estado do analisador é {0}."}, new Object[]{"26336", "Uma indicação de data/hora com uma região não é suportada. Só os fusos horários da diferença são suportados."}, new Object[]{"26337", "Os objetos e as matrizes no valor JSON não poderão encadear-se mais do que {0} níveis"}, new Object[]{"26338", "As chaves de um objeto JSON não poderão exceder 65.535 bytes"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
