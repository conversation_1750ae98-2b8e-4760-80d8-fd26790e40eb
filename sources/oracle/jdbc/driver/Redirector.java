package oracle.jdbc.driver;

import java.io.InputStream;
import java.io.Reader;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URL;
import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Date;
import java.sql.NClob;
import java.sql.Ref;
import java.sql.ResultSet;
import java.sql.RowId;
import java.sql.SQLException;
import java.sql.SQLXML;
import java.sql.Struct;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.Period;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.json.JsonArray;
import javax.json.JsonNumber;
import javax.json.JsonObject;
import javax.json.JsonString;
import javax.json.JsonStructure;
import javax.json.JsonValue;
import javax.json.stream.JsonParser;
import oracle.jdbc.OracleData;
import oracle.jdbc.OracleOpaque;
import oracle.sql.ARRAY;
import oracle.sql.BFILE;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.BLOB;
import oracle.sql.BOOLEAN;
import oracle.sql.CHAR;
import oracle.sql.CLOB;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.INTERVALDS;
import oracle.sql.INTERVALYM;
import oracle.sql.NCLOB;
import oracle.sql.NUMBER;
import oracle.sql.OPAQUE;
import oracle.sql.ORAData;
import oracle.sql.RAW;
import oracle.sql.REF;
import oracle.sql.ROWID;
import oracle.sql.STRUCT;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.VECTOR;
import oracle.sql.json.OracleJsonArray;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonDate;
import oracle.sql.json.OracleJsonDatum;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonDouble;
import oracle.sql.json.OracleJsonFloat;
import oracle.sql.json.OracleJsonIntervalDS;
import oracle.sql.json.OracleJsonIntervalYM;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonParser;
import oracle.sql.json.OracleJsonString;
import oracle.sql.json.OracleJsonStructure;
import oracle.sql.json.OracleJsonTimestamp;
import oracle.sql.json.OracleJsonValue;
import oracle.sql.json.OracleJsonVector;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/Redirector.class */
abstract class Redirector<T> {
    private final Class<T> type;
    private static final HashMap<Class<?>, Redirector<?>> CLASS_TO_REDIRECTOR;
    private static final HashMap<Class<?>, Redirector<?>> CLASS_TO_ERROR;
    static final /* synthetic */ boolean $assertionsDisabled;

    abstract T redirect(Accessor accessor, int i) throws SQLException;

    static {
        $assertionsDisabled = !Redirector.class.desiredAssertionStatus();
        CLASS_TO_REDIRECTOR = new HashMap<>();
        CLASS_TO_REDIRECTOR.put(Array.class, new Redirector<Array>(Array.class) { // from class: oracle.jdbc.driver.Redirector.4
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Array redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getARRAY(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(BigDecimal.class, new Redirector<BigDecimal>(BigDecimal.class) { // from class: oracle.jdbc.driver.Redirector.5
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BigDecimal redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBigDecimal(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Blob.class, new Redirector<Blob>(Blob.class) { // from class: oracle.jdbc.driver.Redirector.6
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Blob redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBLOB(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Boolean.class, new Redirector<Boolean>(Boolean.class) { // from class: oracle.jdbc.driver.Redirector.7
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Boolean redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return Boolean.valueOf(acc.getBoolean(currentRow));
            }
        });
        CLASS_TO_REDIRECTOR.put(Byte.class, new Redirector<Byte>(Byte.class) { // from class: oracle.jdbc.driver.Redirector.8
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Byte redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return Byte.valueOf(acc.getByte(currentRow));
            }
        });
        CLASS_TO_REDIRECTOR.put(byte[].class, new Redirector<byte[]>(byte[].class) { // from class: oracle.jdbc.driver.Redirector.9
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final byte[] redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBytes(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Clob.class, new Redirector<Clob>(Clob.class) { // from class: oracle.jdbc.driver.Redirector.10
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Clob redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getCLOB(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Date.class, new Redirector<Date>(Date.class) { // from class: oracle.jdbc.driver.Redirector.11
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Date redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getDate(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Double.class, new Redirector<Double>(Double.class) { // from class: oracle.jdbc.driver.Redirector.12
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Double redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return Double.valueOf(acc.getDouble(currentRow));
            }
        });
        CLASS_TO_REDIRECTOR.put(Float.class, new Redirector<Float>(Float.class) { // from class: oracle.jdbc.driver.Redirector.13
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Float redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return Float.valueOf(acc.getFloat(currentRow));
            }
        });
        CLASS_TO_REDIRECTOR.put(Integer.class, new Redirector<Integer>(Integer.class) { // from class: oracle.jdbc.driver.Redirector.14
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Integer redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return Integer.valueOf(acc.getInt(currentRow));
            }
        });
        CLASS_TO_REDIRECTOR.put(Long.class, new Redirector<Long>(Long.class) { // from class: oracle.jdbc.driver.Redirector.15
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Long redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return Long.valueOf(acc.getLong(currentRow));
            }
        });
        CLASS_TO_REDIRECTOR.put(NClob.class, new Redirector<NClob>(NClob.class) { // from class: oracle.jdbc.driver.Redirector.16
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final NClob redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getNClob(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Ref.class, new Redirector<Ref>(Ref.class) { // from class: oracle.jdbc.driver.Redirector.17
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Ref redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getREF(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(RowId.class, new Redirector<RowId>(RowId.class) { // from class: oracle.jdbc.driver.Redirector.18
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final RowId redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getROWID(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Short.class, new Redirector<Short>(Short.class) { // from class: oracle.jdbc.driver.Redirector.19
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Short redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return Short.valueOf(acc.getShort(currentRow));
            }
        });
        CLASS_TO_REDIRECTOR.put(SQLXML.class, new Redirector<SQLXML>(SQLXML.class) { // from class: oracle.jdbc.driver.Redirector.20
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final SQLXML redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getSQLXML(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(String.class, new Redirector<String>(String.class) { // from class: oracle.jdbc.driver.Redirector.21
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final String redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getString(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Struct.class, new Redirector<Struct>(Struct.class) { // from class: oracle.jdbc.driver.Redirector.22
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Struct redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getStruct(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Time.class, new Redirector<Time>(Time.class) { // from class: oracle.jdbc.driver.Redirector.23
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Time redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getTime(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Timestamp.class, new Redirector<Timestamp>(Timestamp.class) { // from class: oracle.jdbc.driver.Redirector.24
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Timestamp redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getTimestamp(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(URL.class, new Redirector<URL>(URL.class) { // from class: oracle.jdbc.driver.Redirector.25
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final URL redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getURL(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(BigInteger.class, new Redirector<BigInteger>(BigInteger.class) { // from class: oracle.jdbc.driver.Redirector.26
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BigInteger redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBigInteger(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(java.util.Date.class, new Redirector<java.util.Date>(java.util.Date.class) { // from class: oracle.jdbc.driver.Redirector.27
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final java.util.Date redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getJavaUtilDate(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Calendar.class, new Redirector<Calendar>(Calendar.class) { // from class: oracle.jdbc.driver.Redirector.28
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Calendar redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getCalendar(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Duration.class, new Redirector<Duration>(Duration.class) { // from class: oracle.jdbc.driver.Redirector.29
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Duration redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getDuration(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(LocalDate.class, new Redirector<LocalDate>(LocalDate.class) { // from class: oracle.jdbc.driver.Redirector.30
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final LocalDate redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getLocalDate(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(LocalDateTime.class, new Redirector<LocalDateTime>(LocalDateTime.class) { // from class: oracle.jdbc.driver.Redirector.31
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final LocalDateTime redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getLocalDateTime(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(LocalTime.class, new Redirector<LocalTime>(LocalTime.class) { // from class: oracle.jdbc.driver.Redirector.32
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final LocalTime redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getLocalTime(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OffsetDateTime.class, new Redirector<OffsetDateTime>(OffsetDateTime.class) { // from class: oracle.jdbc.driver.Redirector.33
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OffsetDateTime redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOffsetDateTime(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OffsetTime.class, new Redirector<OffsetTime>(OffsetTime.class) { // from class: oracle.jdbc.driver.Redirector.34
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OffsetTime redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOffsetTime(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Period.class, new Redirector<Period>(Period.class) { // from class: oracle.jdbc.driver.Redirector.35
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Period redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getPeriod(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(ZonedDateTime.class, new Redirector<ZonedDateTime>(ZonedDateTime.class) { // from class: oracle.jdbc.driver.Redirector.36
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ZonedDateTime redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getZonedDateTime(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(ARRAY.class, new Redirector<ARRAY>(ARRAY.class) { // from class: oracle.jdbc.driver.Redirector.37
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ARRAY redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getARRAY(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(BFILE.class, new Redirector<BFILE>(BFILE.class) { // from class: oracle.jdbc.driver.Redirector.38
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BFILE redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBFILE(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(BINARY_FLOAT.class, new Redirector<BINARY_FLOAT>(BINARY_FLOAT.class) { // from class: oracle.jdbc.driver.Redirector.39
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BINARY_FLOAT redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBINARY_FLOAT(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(BINARY_DOUBLE.class, new Redirector<BINARY_DOUBLE>(BINARY_DOUBLE.class) { // from class: oracle.jdbc.driver.Redirector.40
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BINARY_DOUBLE redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBINARY_DOUBLE(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(BLOB.class, new Redirector<BLOB>(BLOB.class) { // from class: oracle.jdbc.driver.Redirector.41
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BLOB redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBLOB(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(CHAR.class, new Redirector<CHAR>(CHAR.class) { // from class: oracle.jdbc.driver.Redirector.42
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final CHAR redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getCHAR(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(CLOB.class, new Redirector<CLOB>(CLOB.class) { // from class: oracle.jdbc.driver.Redirector.43
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final CLOB redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getCLOB(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(ResultSet.class, new Redirector<ResultSet>(ResultSet.class) { // from class: oracle.jdbc.driver.Redirector.44
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ResultSet redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getCursor(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(DATE.class, new Redirector<DATE>(DATE.class) { // from class: oracle.jdbc.driver.Redirector.45
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final DATE redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getDATE(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(INTERVALDS.class, new Redirector<INTERVALDS>(INTERVALDS.class) { // from class: oracle.jdbc.driver.Redirector.46
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final INTERVALDS redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getINTERVALDS(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(INTERVALYM.class, new Redirector<INTERVALYM>(INTERVALYM.class) { // from class: oracle.jdbc.driver.Redirector.47
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final INTERVALYM redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getINTERVALYM(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(NCLOB.class, new Redirector<NCLOB>(NCLOB.class) { // from class: oracle.jdbc.driver.Redirector.48
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final NCLOB redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getNCLOB(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(NUMBER.class, new Redirector<NUMBER>(NUMBER.class) { // from class: oracle.jdbc.driver.Redirector.49
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final NUMBER redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getNUMBER(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OPAQUE.class, new Redirector<OPAQUE>(OPAQUE.class) { // from class: oracle.jdbc.driver.Redirector.50
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OPAQUE redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOPAQUE(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(ORAData.class, new Redirector<ORAData>(ORAData.class) { // from class: oracle.jdbc.driver.Redirector.51
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ORAData redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getORAData(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleData.class, new Redirector<OracleData>(OracleData.class) { // from class: oracle.jdbc.driver.Redirector.52
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleData redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleData(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(RAW.class, new Redirector<RAW>(RAW.class) { // from class: oracle.jdbc.driver.Redirector.53
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final RAW redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getRAW(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(REF.class, new Redirector<REF>(REF.class) { // from class: oracle.jdbc.driver.Redirector.54
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final REF redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getREF(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(ROWID.class, new Redirector<ROWID>(ROWID.class) { // from class: oracle.jdbc.driver.Redirector.55
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ROWID redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getROWID(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(STRUCT.class, new Redirector<STRUCT>(STRUCT.class) { // from class: oracle.jdbc.driver.Redirector.56
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final STRUCT redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getSTRUCT(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(TIMESTAMPLTZ.class, new Redirector<TIMESTAMPLTZ>(TIMESTAMPLTZ.class) { // from class: oracle.jdbc.driver.Redirector.57
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final TIMESTAMPLTZ redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getTIMESTAMPLTZ(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(TIMESTAMPTZ.class, new Redirector<TIMESTAMPTZ>(TIMESTAMPTZ.class) { // from class: oracle.jdbc.driver.Redirector.58
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final TIMESTAMPTZ redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getTIMESTAMPTZ(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(TIMESTAMP.class, new Redirector<TIMESTAMP>(TIMESTAMP.class) { // from class: oracle.jdbc.driver.Redirector.59
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final TIMESTAMP redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getTIMESTAMP(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(oracle.jdbc.OracleArray.class, new Redirector<oracle.jdbc.OracleArray>(oracle.jdbc.OracleArray.class) { // from class: oracle.jdbc.driver.Redirector.60
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleArray redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getARRAY(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(oracle.jdbc.OracleBfile.class, new Redirector<oracle.jdbc.OracleBfile>(oracle.jdbc.OracleBfile.class) { // from class: oracle.jdbc.driver.Redirector.61
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleBfile redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBFILE(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(oracle.jdbc.OracleBlob.class, new Redirector<oracle.jdbc.OracleBlob>(oracle.jdbc.OracleBlob.class) { // from class: oracle.jdbc.driver.Redirector.62
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleBlob redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBLOB(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(oracle.jdbc.OracleClob.class, new Redirector<oracle.jdbc.OracleClob>(oracle.jdbc.OracleClob.class) { // from class: oracle.jdbc.driver.Redirector.63
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleClob redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getCLOB(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(oracle.jdbc.OracleNClob.class, new Redirector<oracle.jdbc.OracleNClob>(oracle.jdbc.OracleNClob.class) { // from class: oracle.jdbc.driver.Redirector.64
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleNClob redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getNCLOB(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleOpaque.class, new Redirector<OracleOpaque>(OracleOpaque.class) { // from class: oracle.jdbc.driver.Redirector.65
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleOpaque redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOPAQUE(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(oracle.jdbc.OracleRef.class, new Redirector<oracle.jdbc.OracleRef>(oracle.jdbc.OracleRef.class) { // from class: oracle.jdbc.driver.Redirector.66
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleRef redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getREF(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(oracle.jdbc.OracleStruct.class, new Redirector<oracle.jdbc.OracleStruct>(oracle.jdbc.OracleStruct.class) { // from class: oracle.jdbc.driver.Redirector.67
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleStruct redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getSTRUCT(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(BOOLEAN.class, new Redirector<BOOLEAN>(BOOLEAN.class) { // from class: oracle.jdbc.driver.Redirector.68
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BOOLEAN redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBOOLEAN(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(double[].class, new Redirector<double[]>(double[].class) { // from class: oracle.jdbc.driver.Redirector.69
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final double[] redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getDoubleArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(float[].class, new Redirector<float[]>(float[].class) { // from class: oracle.jdbc.driver.Redirector.70
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final float[] redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getFloatArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(short[].class, new Redirector<short[]>(short[].class) { // from class: oracle.jdbc.driver.Redirector.71
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final short[] redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getShortArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(int[].class, new Redirector<int[]>(int[].class) { // from class: oracle.jdbc.driver.Redirector.72
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final int[] redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getIntArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(long[].class, new Redirector<long[]>(long[].class) { // from class: oracle.jdbc.driver.Redirector.73
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final long[] redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getLongArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(boolean[].class, new Redirector<boolean[]>(boolean[].class) { // from class: oracle.jdbc.driver.Redirector.74
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final boolean[] redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBooleanArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(VECTOR.class, new Redirector<VECTOR>(VECTOR.class) { // from class: oracle.jdbc.driver.Redirector.75
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final VECTOR redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getVECTOR(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(VECTOR.SparseDoubleArray.class, new Redirector<VECTOR.SparseDoubleArray>(VECTOR.SparseDoubleArray.class) { // from class: oracle.jdbc.driver.Redirector.76
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final VECTOR.SparseDoubleArray redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getSparseDoubleArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(VECTOR.SparseFloatArray.class, new Redirector<VECTOR.SparseFloatArray>(VECTOR.SparseFloatArray.class) { // from class: oracle.jdbc.driver.Redirector.77
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final VECTOR.SparseFloatArray redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getSparseFloatArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(VECTOR.SparseByteArray.class, new Redirector<VECTOR.SparseByteArray>(VECTOR.SparseByteArray.class) { // from class: oracle.jdbc.driver.Redirector.78
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final VECTOR.SparseByteArray redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getSparseByteArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(InputStream.class, new Redirector<InputStream>(InputStream.class) { // from class: oracle.jdbc.driver.Redirector.79
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final InputStream redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getBinaryStream(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Reader.class, new Redirector<Reader>(Reader.class) { // from class: oracle.jdbc.driver.Redirector.80
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Reader redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getCharacterStream(currentRow);
            }
        });
        if (PhysicalConnection.isJsonJarPresent()) {
            CLASS_TO_REDIRECTOR.put(JsonValue.class, new Redirector<JsonValue>(JsonValue.class) { // from class: oracle.jdbc.driver.Redirector.81
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonValue redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJsonValue(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(JsonParser.class, new Redirector<JsonParser>(JsonParser.class) { // from class: oracle.jdbc.driver.Redirector.82
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonParser redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJsonParser(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(JsonStructure.class, new Redirector<JsonStructure>(JsonStructure.class) { // from class: oracle.jdbc.driver.Redirector.83
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonStructure redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJsonStructure(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(JsonObject.class, new Redirector<JsonObject>(JsonObject.class) { // from class: oracle.jdbc.driver.Redirector.84
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonObject redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJsonObject(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(JsonArray.class, new Redirector<JsonArray>(JsonArray.class) { // from class: oracle.jdbc.driver.Redirector.85
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonArray redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJsonArray(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(JsonString.class, new Redirector<JsonString>(JsonString.class) { // from class: oracle.jdbc.driver.Redirector.86
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonString redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJsonString(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(JsonNumber.class, new Redirector<JsonNumber>(JsonNumber.class) { // from class: oracle.jdbc.driver.Redirector.87
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonNumber redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJsonNumber(currentRow);
                }
            });
        }
        if (PhysicalConnection.isJakartaJarPresent()) {
            CLASS_TO_REDIRECTOR.put(jakarta.json.JsonValue.class, new Redirector<jakarta.json.JsonValue>(jakarta.json.JsonValue.class) { // from class: oracle.jdbc.driver.Redirector.88
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonValue redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJakartaJsonValue(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(jakarta.json.stream.JsonParser.class, new Redirector<jakarta.json.stream.JsonParser>(jakarta.json.stream.JsonParser.class) { // from class: oracle.jdbc.driver.Redirector.89
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.stream.JsonParser redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJakartaJsonParser(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(jakarta.json.JsonStructure.class, new Redirector<jakarta.json.JsonStructure>(jakarta.json.JsonStructure.class) { // from class: oracle.jdbc.driver.Redirector.90
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonStructure redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJakartaJsonStructure(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(jakarta.json.JsonObject.class, new Redirector<jakarta.json.JsonObject>(jakarta.json.JsonObject.class) { // from class: oracle.jdbc.driver.Redirector.91
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonObject redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJakartaJsonObject(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(jakarta.json.JsonArray.class, new Redirector<jakarta.json.JsonArray>(jakarta.json.JsonArray.class) { // from class: oracle.jdbc.driver.Redirector.92
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonArray redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJakartaJsonArray(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(jakarta.json.JsonString.class, new Redirector<jakarta.json.JsonString>(jakarta.json.JsonString.class) { // from class: oracle.jdbc.driver.Redirector.93
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonString redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJakartaJsonString(currentRow);
                }
            });
            CLASS_TO_REDIRECTOR.put(jakarta.json.JsonNumber.class, new Redirector<jakarta.json.JsonNumber>(jakarta.json.JsonNumber.class) { // from class: oracle.jdbc.driver.Redirector.94
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonNumber redirect(Accessor acc, int currentRow) throws SQLException {
                    if (acc.isNull(currentRow)) {
                        return null;
                    }
                    return acc.getJakartaJsonNumber(currentRow);
                }
            });
        }
        CLASS_TO_REDIRECTOR.put(OracleJsonValue.class, new Redirector<OracleJsonValue>(OracleJsonValue.class) { // from class: oracle.jdbc.driver.Redirector.95
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonValue redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonValue(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonStructure.class, new Redirector<OracleJsonStructure>(OracleJsonStructure.class) { // from class: oracle.jdbc.driver.Redirector.96
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonStructure redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonStructure(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonObject.class, new Redirector<OracleJsonObject>(OracleJsonObject.class) { // from class: oracle.jdbc.driver.Redirector.97
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonObject redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonObject(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonArray.class, new Redirector<OracleJsonArray>(OracleJsonArray.class) { // from class: oracle.jdbc.driver.Redirector.98
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonArray redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonArray(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonString.class, new Redirector<OracleJsonString>(OracleJsonString.class) { // from class: oracle.jdbc.driver.Redirector.99
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonString redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonString(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonNumber.class, new Redirector<OracleJsonNumber>(OracleJsonNumber.class) { // from class: oracle.jdbc.driver.Redirector.100
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonNumber redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonNumber(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonDecimal.class, new Redirector<OracleJsonDecimal>(OracleJsonDecimal.class) { // from class: oracle.jdbc.driver.Redirector.101
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonDecimal redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonDecimal(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonFloat.class, new Redirector<OracleJsonFloat>(OracleJsonFloat.class) { // from class: oracle.jdbc.driver.Redirector.102
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonFloat redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonFloat(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonDouble.class, new Redirector<OracleJsonDouble>(OracleJsonDouble.class) { // from class: oracle.jdbc.driver.Redirector.103
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonDouble redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonDouble(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonTimestamp.class, new Redirector<OracleJsonTimestamp>(OracleJsonTimestamp.class) { // from class: oracle.jdbc.driver.Redirector.104
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonTimestamp redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonTimestamp(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonDate.class, new Redirector<OracleJsonDate>(OracleJsonDate.class) { // from class: oracle.jdbc.driver.Redirector.105
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonDate redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonDate(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonBinary.class, new Redirector<OracleJsonBinary>(OracleJsonBinary.class) { // from class: oracle.jdbc.driver.Redirector.106
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonBinary redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonBinary(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonIntervalDS.class, new Redirector<OracleJsonIntervalDS>(OracleJsonIntervalDS.class) { // from class: oracle.jdbc.driver.Redirector.107
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonIntervalDS redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonIntervalDS(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonIntervalYM.class, new Redirector<OracleJsonIntervalYM>(OracleJsonIntervalYM.class) { // from class: oracle.jdbc.driver.Redirector.108
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonIntervalYM redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonIntervalYM(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonVector.class, new Redirector<OracleJsonVector>(OracleJsonVector.class) { // from class: oracle.jdbc.driver.Redirector.109
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonVector redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonVector(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonDatum.class, new Redirector<OracleJsonDatum>(OracleJsonDatum.class) { // from class: oracle.jdbc.driver.Redirector.110
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonDatum redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonDatum(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(Datum.class, new Redirector<Datum>(Datum.class) { // from class: oracle.jdbc.driver.Redirector.111
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Datum redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getDatum(currentRow);
            }
        });
        CLASS_TO_REDIRECTOR.put(OracleJsonParser.class, new Redirector<OracleJsonParser>(OracleJsonParser.class) { // from class: oracle.jdbc.driver.Redirector.112
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleJsonParser redirect(Accessor acc, int currentRow) throws SQLException {
                if (acc.isNull(currentRow)) {
                    return null;
                }
                return acc.getOracleJsonParser(currentRow);
            }
        });
        CLASS_TO_ERROR = new HashMap<>();
        CLASS_TO_ERROR.put(Array.class, new Redirector<Array>(Array.class) { // from class: oracle.jdbc.driver.Redirector.113
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Array redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(BigDecimal.class, new Redirector<BigDecimal>(BigDecimal.class) { // from class: oracle.jdbc.driver.Redirector.114
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BigDecimal redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Blob.class, new Redirector<Blob>(Blob.class) { // from class: oracle.jdbc.driver.Redirector.115
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Blob redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Boolean.class, new Redirector<Boolean>(Boolean.class) { // from class: oracle.jdbc.driver.Redirector.116
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Boolean redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Byte.class, new Redirector<Byte>(Byte.class) { // from class: oracle.jdbc.driver.Redirector.117
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Byte redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(byte[].class, new Redirector<byte[]>(byte[].class) { // from class: oracle.jdbc.driver.Redirector.118
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final byte[] redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Clob.class, new Redirector<Clob>(Clob.class) { // from class: oracle.jdbc.driver.Redirector.119
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Clob redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Date.class, new Redirector<Date>(Date.class) { // from class: oracle.jdbc.driver.Redirector.120
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Date redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Double.class, new Redirector<Double>(Double.class) { // from class: oracle.jdbc.driver.Redirector.121
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Double redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Float.class, new Redirector<Float>(Float.class) { // from class: oracle.jdbc.driver.Redirector.122
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Float redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Integer.class, new Redirector<Integer>(Integer.class) { // from class: oracle.jdbc.driver.Redirector.123
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Integer redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Long.class, new Redirector<Long>(Long.class) { // from class: oracle.jdbc.driver.Redirector.124
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Long redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(NClob.class, new Redirector<NClob>(NClob.class) { // from class: oracle.jdbc.driver.Redirector.125
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final NClob redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Ref.class, new Redirector<Ref>(Ref.class) { // from class: oracle.jdbc.driver.Redirector.126
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Ref redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(RowId.class, new Redirector<RowId>(RowId.class) { // from class: oracle.jdbc.driver.Redirector.127
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final RowId redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Short.class, new Redirector<Short>(Short.class) { // from class: oracle.jdbc.driver.Redirector.128
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Short redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(SQLXML.class, new Redirector<SQLXML>(SQLXML.class) { // from class: oracle.jdbc.driver.Redirector.129
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final SQLXML redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(String.class, new Redirector<String>(String.class) { // from class: oracle.jdbc.driver.Redirector.130
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final String redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Struct.class, new Redirector<Struct>(Struct.class) { // from class: oracle.jdbc.driver.Redirector.131
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Struct redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Time.class, new Redirector<Time>(Time.class) { // from class: oracle.jdbc.driver.Redirector.132
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Time redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Timestamp.class, new Redirector<Timestamp>(Timestamp.class) { // from class: oracle.jdbc.driver.Redirector.133
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Timestamp redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(URL.class, new Redirector<URL>(URL.class) { // from class: oracle.jdbc.driver.Redirector.134
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final URL redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(BigInteger.class, new Redirector<BigInteger>(BigInteger.class) { // from class: oracle.jdbc.driver.Redirector.135
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BigInteger redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(java.util.Date.class, new Redirector<java.util.Date>(java.util.Date.class) { // from class: oracle.jdbc.driver.Redirector.136
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final java.util.Date redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Calendar.class, new Redirector<Calendar>(Calendar.class) { // from class: oracle.jdbc.driver.Redirector.137
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Calendar redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Duration.class, new Redirector<Duration>(Duration.class) { // from class: oracle.jdbc.driver.Redirector.138
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Duration redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(LocalDate.class, new Redirector<LocalDate>(LocalDate.class) { // from class: oracle.jdbc.driver.Redirector.139
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final LocalDate redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(LocalDateTime.class, new Redirector<LocalDateTime>(LocalDateTime.class) { // from class: oracle.jdbc.driver.Redirector.140
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final LocalDateTime redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(LocalTime.class, new Redirector<LocalTime>(LocalTime.class) { // from class: oracle.jdbc.driver.Redirector.141
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final LocalTime redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(OffsetDateTime.class, new Redirector<OffsetDateTime>(OffsetDateTime.class) { // from class: oracle.jdbc.driver.Redirector.142
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OffsetDateTime redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(OffsetTime.class, new Redirector<OffsetTime>(OffsetTime.class) { // from class: oracle.jdbc.driver.Redirector.143
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OffsetTime redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Period.class, new Redirector<Period>(Period.class) { // from class: oracle.jdbc.driver.Redirector.144
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Period redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(ZonedDateTime.class, new Redirector<ZonedDateTime>(ZonedDateTime.class) { // from class: oracle.jdbc.driver.Redirector.145
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ZonedDateTime redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(ARRAY.class, new Redirector<ARRAY>(ARRAY.class) { // from class: oracle.jdbc.driver.Redirector.146
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ARRAY redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(BFILE.class, new Redirector<BFILE>(BFILE.class) { // from class: oracle.jdbc.driver.Redirector.147
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BFILE redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(BINARY_FLOAT.class, new Redirector<BINARY_FLOAT>(BINARY_FLOAT.class) { // from class: oracle.jdbc.driver.Redirector.148
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BINARY_FLOAT redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(BINARY_DOUBLE.class, new Redirector<BINARY_DOUBLE>(BINARY_DOUBLE.class) { // from class: oracle.jdbc.driver.Redirector.149
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BINARY_DOUBLE redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(BLOB.class, new Redirector<BLOB>(BLOB.class) { // from class: oracle.jdbc.driver.Redirector.150
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BLOB redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(CHAR.class, new Redirector<CHAR>(CHAR.class) { // from class: oracle.jdbc.driver.Redirector.151
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final CHAR redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(CLOB.class, new Redirector<CLOB>(CLOB.class) { // from class: oracle.jdbc.driver.Redirector.152
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final CLOB redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(ResultSet.class, new Redirector<ResultSet>(ResultSet.class) { // from class: oracle.jdbc.driver.Redirector.153
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ResultSet redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(DATE.class, new Redirector<DATE>(DATE.class) { // from class: oracle.jdbc.driver.Redirector.154
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final DATE redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(INTERVALDS.class, new Redirector<INTERVALDS>(INTERVALDS.class) { // from class: oracle.jdbc.driver.Redirector.155
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final INTERVALDS redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(INTERVALYM.class, new Redirector<INTERVALYM>(INTERVALYM.class) { // from class: oracle.jdbc.driver.Redirector.156
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final INTERVALYM redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(NCLOB.class, new Redirector<NCLOB>(NCLOB.class) { // from class: oracle.jdbc.driver.Redirector.157
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final NCLOB redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(NUMBER.class, new Redirector<NUMBER>(NUMBER.class) { // from class: oracle.jdbc.driver.Redirector.158
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final NUMBER redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(OPAQUE.class, new Redirector<OPAQUE>(OPAQUE.class) { // from class: oracle.jdbc.driver.Redirector.159
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OPAQUE redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(ORAData.class, new Redirector<ORAData>(ORAData.class) { // from class: oracle.jdbc.driver.Redirector.160
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ORAData redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(OracleData.class, new Redirector<OracleData>(OracleData.class) { // from class: oracle.jdbc.driver.Redirector.161
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleData redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(RAW.class, new Redirector<RAW>(RAW.class) { // from class: oracle.jdbc.driver.Redirector.162
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final RAW redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(REF.class, new Redirector<REF>(REF.class) { // from class: oracle.jdbc.driver.Redirector.163
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final REF redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(ROWID.class, new Redirector<ROWID>(ROWID.class) { // from class: oracle.jdbc.driver.Redirector.164
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final ROWID redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(STRUCT.class, new Redirector<STRUCT>(STRUCT.class) { // from class: oracle.jdbc.driver.Redirector.165
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final STRUCT redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(TIMESTAMPLTZ.class, new Redirector<TIMESTAMPLTZ>(TIMESTAMPLTZ.class) { // from class: oracle.jdbc.driver.Redirector.166
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final TIMESTAMPLTZ redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(TIMESTAMPTZ.class, new Redirector<TIMESTAMPTZ>(TIMESTAMPTZ.class) { // from class: oracle.jdbc.driver.Redirector.167
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final TIMESTAMPTZ redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(TIMESTAMP.class, new Redirector<TIMESTAMP>(TIMESTAMP.class) { // from class: oracle.jdbc.driver.Redirector.168
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final TIMESTAMP redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(oracle.jdbc.OracleArray.class, new Redirector<oracle.jdbc.OracleArray>(oracle.jdbc.OracleArray.class) { // from class: oracle.jdbc.driver.Redirector.169
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleArray redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(oracle.jdbc.OracleBfile.class, new Redirector<oracle.jdbc.OracleBfile>(oracle.jdbc.OracleBfile.class) { // from class: oracle.jdbc.driver.Redirector.170
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleBfile redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(oracle.jdbc.OracleBlob.class, new Redirector<oracle.jdbc.OracleBlob>(oracle.jdbc.OracleBlob.class) { // from class: oracle.jdbc.driver.Redirector.171
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleBlob redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(oracle.jdbc.OracleClob.class, new Redirector<oracle.jdbc.OracleClob>(oracle.jdbc.OracleClob.class) { // from class: oracle.jdbc.driver.Redirector.172
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleClob redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(oracle.jdbc.OracleNClob.class, new Redirector<oracle.jdbc.OracleNClob>(oracle.jdbc.OracleNClob.class) { // from class: oracle.jdbc.driver.Redirector.173
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleNClob redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(OracleOpaque.class, new Redirector<OracleOpaque>(OracleOpaque.class) { // from class: oracle.jdbc.driver.Redirector.174
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final OracleOpaque redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(oracle.jdbc.OracleRef.class, new Redirector<oracle.jdbc.OracleRef>(oracle.jdbc.OracleRef.class) { // from class: oracle.jdbc.driver.Redirector.175
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleRef redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(oracle.jdbc.OracleStruct.class, new Redirector<oracle.jdbc.OracleStruct>(oracle.jdbc.OracleStruct.class) { // from class: oracle.jdbc.driver.Redirector.176
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final oracle.jdbc.OracleStruct redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(BOOLEAN.class, new Redirector<BOOLEAN>(BOOLEAN.class) { // from class: oracle.jdbc.driver.Redirector.177
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final BOOLEAN redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(double[].class, new Redirector<double[]>(double[].class) { // from class: oracle.jdbc.driver.Redirector.178
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final double[] redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(float[].class, new Redirector<float[]>(float[].class) { // from class: oracle.jdbc.driver.Redirector.179
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final float[] redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(short[].class, new Redirector<short[]>(short[].class) { // from class: oracle.jdbc.driver.Redirector.180
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final short[] redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(int[].class, new Redirector<int[]>(int[].class) { // from class: oracle.jdbc.driver.Redirector.181
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final int[] redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(long[].class, new Redirector<long[]>(long[].class) { // from class: oracle.jdbc.driver.Redirector.182
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final long[] redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(boolean[].class, new Redirector<boolean[]>(boolean[].class) { // from class: oracle.jdbc.driver.Redirector.183
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final boolean[] redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(VECTOR.class, new Redirector<VECTOR>(VECTOR.class) { // from class: oracle.jdbc.driver.Redirector.184
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final VECTOR redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(VECTOR.SparseDoubleArray.class, new Redirector<VECTOR.SparseDoubleArray>(VECTOR.SparseDoubleArray.class) { // from class: oracle.jdbc.driver.Redirector.185
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final VECTOR.SparseDoubleArray redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(VECTOR.SparseFloatArray.class, new Redirector<VECTOR.SparseFloatArray>(VECTOR.SparseFloatArray.class) { // from class: oracle.jdbc.driver.Redirector.186
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final VECTOR.SparseFloatArray redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(VECTOR.SparseByteArray.class, new Redirector<VECTOR.SparseByteArray>(VECTOR.SparseByteArray.class) { // from class: oracle.jdbc.driver.Redirector.187
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final VECTOR.SparseByteArray redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(InputStream.class, new Redirector<InputStream>(InputStream.class) { // from class: oracle.jdbc.driver.Redirector.188
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final InputStream redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        CLASS_TO_ERROR.put(Reader.class, new Redirector<Reader>(Reader.class) { // from class: oracle.jdbc.driver.Redirector.189
            /* JADX INFO: Access modifiers changed from: package-private */
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // oracle.jdbc.driver.Redirector
            public final Reader redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        });
        if (PhysicalConnection.isJsonJarPresent()) {
            CLASS_TO_ERROR.put(JsonValue.class, new Redirector<JsonValue>(JsonValue.class) { // from class: oracle.jdbc.driver.Redirector.190
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonValue redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(JsonParser.class, new Redirector<JsonParser>(JsonParser.class) { // from class: oracle.jdbc.driver.Redirector.191
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonParser redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(JsonStructure.class, new Redirector<JsonStructure>(JsonStructure.class) { // from class: oracle.jdbc.driver.Redirector.192
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonStructure redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(JsonObject.class, new Redirector<JsonObject>(JsonObject.class) { // from class: oracle.jdbc.driver.Redirector.193
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonObject redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(JsonArray.class, new Redirector<JsonArray>(JsonArray.class) { // from class: oracle.jdbc.driver.Redirector.194
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonArray redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(JsonString.class, new Redirector<JsonString>(JsonString.class) { // from class: oracle.jdbc.driver.Redirector.195
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonString redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(JsonNumber.class, new Redirector<JsonNumber>(JsonNumber.class) { // from class: oracle.jdbc.driver.Redirector.196
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final JsonNumber redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
        }
        if (PhysicalConnection.isJakartaJarPresent()) {
            CLASS_TO_ERROR.put(jakarta.json.JsonValue.class, new Redirector<jakarta.json.JsonValue>(jakarta.json.JsonValue.class) { // from class: oracle.jdbc.driver.Redirector.197
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonValue redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(jakarta.json.stream.JsonParser.class, new Redirector<jakarta.json.stream.JsonParser>(jakarta.json.stream.JsonParser.class) { // from class: oracle.jdbc.driver.Redirector.198
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.stream.JsonParser redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(jakarta.json.JsonStructure.class, new Redirector<jakarta.json.JsonStructure>(jakarta.json.JsonStructure.class) { // from class: oracle.jdbc.driver.Redirector.199
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonStructure redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(jakarta.json.JsonObject.class, new Redirector<jakarta.json.JsonObject>(jakarta.json.JsonObject.class) { // from class: oracle.jdbc.driver.Redirector.200
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonObject redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(jakarta.json.JsonArray.class, new Redirector<jakarta.json.JsonArray>(jakarta.json.JsonArray.class) { // from class: oracle.jdbc.driver.Redirector.201
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonArray redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(jakarta.json.JsonString.class, new Redirector<jakarta.json.JsonString>(jakarta.json.JsonString.class) { // from class: oracle.jdbc.driver.Redirector.202
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonString redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
            CLASS_TO_ERROR.put(jakarta.json.JsonNumber.class, new Redirector<jakarta.json.JsonNumber>(jakarta.json.JsonNumber.class) { // from class: oracle.jdbc.driver.Redirector.203
                /* JADX INFO: Access modifiers changed from: package-private */
                /* JADX WARN: Can't rename method to resolve collision */
                @Override // oracle.jdbc.driver.Redirector
                public final jakarta.json.JsonNumber redirect(Accessor acc, int currentRow) throws SQLException {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            });
        }
    }

    private Redirector(Class<T> _type) {
        this.type = _type;
    }

    final Class<T> getTarget() {
        return this.type;
    }

    public String toString() {
        return super.toString() + "[" + this.type.getName() + "]";
    }

    static <V> Redirector<V> createObjectRedirector(Class<V> target) {
        return new Redirector<V>(target) { // from class: oracle.jdbc.driver.Redirector.1
            @Override // oracle.jdbc.driver.Redirector
            final V redirect(Accessor accessor, int i) throws SQLException {
                try {
                    V v = (V) accessor.getObject(i);
                    Class<V> target2 = getTarget();
                    if (v != null && !target2.isInstance(v)) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 49, target2.getName()).fillInStackTrace());
                    }
                    return v;
                } catch (ClassCastException e) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
                }
            }
        };
    }

    static <V> Redirector<V> createValueOfRedirector(Class<V> target, List<Class<?>> supportedTypes) throws SecurityException {
        if (Modifier.isPublic(target.getModifiers())) {
            Method[] methods = target.getDeclaredMethods();
            int desirable = Integer.MAX_VALUE;
            Method method = null;
            Class<?> type = null;
            for (Method m : methods) {
                if (Modifier.isStatic(m.getModifiers()) && Modifier.isPublic(m.getModifiers()) && m.getName().equals("valueOf") && m.getParameterTypes().length == 1 && target.isAssignableFrom(m.getReturnType())) {
                    int i = 0;
                    Iterator<Class<?>> it = supportedTypes.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            break;
                        }
                        Class<?> t = it.next();
                        if (m.getParameterTypes()[0].isAssignableFrom(t)) {
                            if (i < desirable) {
                                desirable = i;
                                method = m;
                                type = t;
                            }
                        } else {
                            i++;
                        }
                    }
                }
                if (desirable == 0) {
                    break;
                }
            }
            if (method != null) {
                return createValueOfRedirector(target, method, type);
            }
        }
        return new Redirector<V>(target) { // from class: oracle.jdbc.driver.Redirector.2
            @Override // oracle.jdbc.driver.Redirector
            final V redirect(Accessor acc, int currentRow) throws SQLException {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            }
        };
    }

    private static <V, W> Redirector<V> createValueOfRedirector(Class<V> target, final Method staticValueOf, Class<W> supportedType) {
        final Redirector<?> redirector = CLASS_TO_REDIRECTOR.get(supportedType);
        return new Redirector<V>(target) { // from class: oracle.jdbc.driver.Redirector.3
            @Override // oracle.jdbc.driver.Redirector
            final V redirect(Accessor accessor, int i) throws SQLException {
                try {
                    if (accessor.isNull(i)) {
                        return null;
                    }
                    return (V) staticValueOf.invoke(null, redirector.redirect(accessor, i));
                } catch (IllegalAccessException e) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, e.getMessage()).fillInStackTrace());
                } catch (IllegalArgumentException e2) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, e2.getMessage()).fillInStackTrace());
                } catch (InvocationTargetException e3) {
                    if (e3.getTargetException() instanceof SQLException) {
                        throw ((SQLException) e3.getTargetException());
                    }
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Got something other than a SQLException: " + e3.getTargetException()).fillInStackTrace());
                }
            }
        };
    }

    static Map<Class<?>, Redirector<?>> createRedirectorMap(Collection<Class<?>> supportedTypes) {
        Map<Class<?>, Redirector<?>> m = (Map) CLASS_TO_ERROR.clone();
        for (Class<?> t : supportedTypes) {
            if (!$assertionsDisabled && CLASS_TO_REDIRECTOR.get(t) == null) {
                throw new AssertionError(t);
            }
            m.put(t, CLASS_TO_REDIRECTOR.get(t));
        }
        return m;
    }

    static OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
