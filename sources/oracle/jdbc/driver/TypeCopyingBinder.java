package oracle.jdbc.driver;

import java.sql.SQLException;
import oracle.jdbc.oracore.OracleTypeADT;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TypeCopyingBinder.class */
abstract class TypeCopyingBinder extends ByteCopyingBinder {
    protected byte[] paramVal;
    protected OracleTypeADT paramOtype;

    TypeCopyingBinder(byte[] val, OracleTypeADT otype) {
        this.paramVal = val;
        this.paramOtype = otype;
    }

    @Override // oracle.jdbc.driver.ByteCopyingBinder, oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        return this;
    }

    @Override // oracle.jdbc.driver.ByteCopyingBinder, oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        if (bindUseDBA) {
            return super.bind(stmt, bindPosition, rankInBuffer, rank, bindBytes, bindChars, bindIndicators, bytePitch, charPitch, byteoffset, charoffset, lenoffset, indoffset, clearPriorBindValues, localCheckSum, bindData, bindDataOffsets, bindDataLengths, bindDataIndex, bindUseDBA, formOfUse);
        }
        byte[] value = this.paramVal;
        if (value == null) {
            bindIndicators[indoffset] = -1;
            if (bindUseDBA) {
                bindDataOffsets[bindDataIndex] = -1;
                bindDataLengths[bindDataIndex] = 0;
            }
        } else {
            bindIndicators[indoffset] = 0;
        }
        if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
            if (bindIndicators[indoffset] == -1) {
                localCheckSum = CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
            } else {
                localCheckSum = CRC64.updateChecksum(localCheckSum, value, 0, value.length);
            }
        }
        return localCheckSum;
    }
}
