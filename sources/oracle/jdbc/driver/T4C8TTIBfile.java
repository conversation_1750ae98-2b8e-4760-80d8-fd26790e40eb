package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTIBfile.class */
final class T4C8TTIBfile extends T4C8TTILob {
    T4C8TTIBfile(T4CConnection _conn) {
        super(_conn);
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    Datum createTemporaryLob(Connection conn, boolean cache, int duration) throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), "cannot create a temporary BFILE", -1).fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean openLob(byte[] lobLocator, int mode) throws SQLException, IOException {
        boolean wasOpened = openLob(lobLocator, 11, 256);
        return wasOpened;
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean closeLob(byte[] lobLocator) throws SQLException, IOException {
        boolean wasClosed = closeLob(lobLocator, 512);
        return wasClosed;
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean isOpenLob(byte[] lobLocator) throws SQLException, IOException {
        boolean open = isOpenLob(lobLocator, 1024);
        return open;
    }

    boolean doesExist(byte[] lobLocator) throws SQLException, IOException {
        initializeLobdef();
        this.sourceLobLocator = lobLocator;
        this.lobops = 2048L;
        this.nullO2U = true;
        doRPC();
        boolean exists = this.lobnull;
        return exists;
    }
}
