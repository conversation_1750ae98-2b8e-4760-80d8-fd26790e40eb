package oracle.jdbc.driver.utils;

import java.lang.Throwable;
import java.util.function.Supplier;

@FunctionalInterface
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/ThrowingSupplier.class */
public interface ThrowingSupplier<T, E extends Throwable> extends Supplier<T> {
    T getOrThrow() throws Throwable;

    @Override // java.util.function.Supplier
    default T get() {
        try {
            return getOrThrow();
        } catch (Throwable throwable) {
            throw CheckedExceptionHandler.toRuntimeException(throwable);
        }
    }
}
