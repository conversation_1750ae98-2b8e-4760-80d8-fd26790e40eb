package oracle.jdbc.driver;

import java.sql.SQLException;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CVarcharAccessor.class */
class T2CVarcharAccessor extends VarcharAccessor {
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T2CVarcharAccessor.class.desiredAssertionStatus();
    }

    T2CVarcharAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind) throws SQLException {
        super(stmt, max_len, form, external_type, isOutBind, isOutBind);
    }

    @Override // oracle.jdbc.driver.Accessor
    byte[] getBytesInternal(int currentRow) throws SQLException {
        if (this.formOfUse == 2) {
            if (!$assertionsDisabled && isNull(currentRow)) {
                throw new AssertionError();
            }
            CharacterSet cs = this.statement.connection.conversion.getCharacterSet(this.formOfUse);
            String str = this.rowData.getString(getOffset(currentRow), getLength(currentRow), cs);
            return DBConversion.stringToDriverCharBytes(str, (short) cs.getOracleId());
        }
        return super.getBytesInternal(currentRow);
    }
}
