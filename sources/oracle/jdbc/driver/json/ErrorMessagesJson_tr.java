package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_tr.class */
public class ErrorMessagesJson_tr extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Bir G/Ç istisnası oluştu"}, new Object[]{"26302", "\"{0}\" yılı desteklenmiyor"}, new Object[]{"26303", "Taş<PERSON>, değer çok büyük: {0}."}, new Object[]{"26304", "Desteklenmeyen seçenek (uygulanmadı)."}, new Object[]{"26305", "İkili JSON geçersiz veya hatalı."}, new Object[]{"26306", "Desteklenmeyen ikili JSON sürümü: {0}."}, new Object[]{"26307", "UTF-8 ile kodlanmış anahtar uzunluğu 256 bayt değerinden fazla olamaz. Şu anahtar bu sınırı aşıyor: \"{0}\"."}, new Object[]{"26308", "Belirtilen JSON, ikili JSON olarak kodlanmak için çok büyük. Kodlanmış resimler 2 GB'ı aşmamalıdır."}, new Object[]{"26309", "İkili JSON geçersiz veya hatalı. Belirtilen resim yalnızca {0} bayt içeriyor."}, new Object[]{"26310", "Belirtilen java.time.Period öğesinde gün ayarlı ancak Oracle yıldan aya aralığı günleri desteklemez."}, new Object[]{"26311", "Oluşturucu bitişten önce kapatıldı."}, new Object[]{"26312", "Bu bağlamda bir nesne anahtarı belirtilmelidir."}, new Object[]{"26313", "Geçersiz yazma işlemi. Tam bir değer zaten yazıldı."}, new Object[]{"26314", "Bu bağlamda bitişe izin verilmez."}, new Object[]{"26315", "Bu bağlamda anahtara izin verilmez."}, new Object[]{"26316", "Anahtardan sonra beklenen değer."}, new Object[]{"26317", "Ayrıştırıcı durumu {0} olmalıdır."}, new Object[]{"26318", "Ayrıştırıcı durumu {0} olmamalıdır."}, new Object[]{"26319", "Ayrıştırıcı bir değer üzerinde olmalıdır."}, new Object[]{"26320", "\"{0}\" desteklenen bir sarıcı türü değil."}, new Object[]{"26321", "Bu nesne değiştirilemiyor. Değiştirilebilir bir kopya oluşturmak için OracleJsonFactory.createObject(OracleJsonObject) kullanın."}, new Object[]{"26322", "Bu dizi değiştirilemiyor. Değiştirilebilir bir kopya oluşturmak için OracleJsonFactory.createArray(OracleJsonArray) kullanın."}, new Object[]{"26323", "JSON nesnesi tekrarlanan anahtar içeriyor: {0}."}, new Object[]{"26324", "Kodlama otomatik olarak algılanamıyor, yeterli karakter yok."}, new Object[]{"26325", "Beklenen Dosya Sonu belirteci, ancak {0} alındı."}, new Object[]{"26326", "{1} satırı ve {2} sütununda beklenmeyen karakter: {0}."}, new Object[]{"26327", "{1} satırı ve {2} sütununda beklenmeyen karakter: {0}. Beklenen: {3}."}, new Object[]{"26328", "{1} satırı ve {2} sütununda geçersiz belirteç: {0}. Beklenen belirteçler: {3}."}, new Object[]{"26329", "JsonParser#getString() sadece KEY_NAME, VALUE_STRING, VALUE_NUMBER ayrıştırıcı durumu için geçerlidir. Ancak geçerli ayrıştırıcı durumu {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() sadece VALUE_NUMBER ayrıştırıcı durumu için geçerlidir. Ancak geçerli ayrıştırıcı durumu {0}."}, new Object[]{"26331", "JsonParser#getInt() sadece VALUE_NUMBER ayrıştırıcı durumu için geçerlidir. Ancak geçerli ayrıştırıcı durumu {0}."}, new Object[]{"26332", "JsonParser#getLong() sadece VALUE_NUMBER ayrıştırıcı durumu için geçerlidir. Ancak geçerli ayrıştırıcı durumu {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() sadece VALUE_NUMBER ayrıştırıcı durumu için geçerlidir. Ancak geçerli ayrıştırıcı durumu {0}."}, new Object[]{"26334", "JsonParser#getArray() sadece START_ARRAY ayrıştırıcı durumu için geçerlidir. Ancak geçerli ayrıştırıcı durumu {0}."}, new Object[]{"26335", "JsonParser#getObject() sadece START_OBJECT ayrıştırıcı durumu için geçerlidir. Ancak geçerli ayrıştırıcı durumu {0}."}, new Object[]{"26336", "Bölgesi olan zaman damgası desteklenmiyor. Yalnızca konum saat dilimleri destekleniyor."}, new Object[]{"26337", "JSON değerindeki nesneler ve diziler en fazla {0} düzey iç içe geçebilir"}, new Object[]{"26338", "JSON nesnesinin anahtarları 65.535 bayt değerini aşamaz."}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
