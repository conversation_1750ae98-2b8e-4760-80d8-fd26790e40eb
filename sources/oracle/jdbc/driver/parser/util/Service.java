package oracle.jdbc.driver.parser.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/util/Service.class */
public class Service {
    public static String readFile(InputStream is) throws IOException {
        StringBuilder ret = new StringBuilder();
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        while (true) {
            String line = br.readLine();
            if (line != null) {
                ret.append(line + System.lineSeparator());
            } else {
                return ret.toString();
            }
        }
    }

    public static long lPair(int x, int y) {
        return (y << 32) | x;
    }

    public static int lY(long pair) {
        return (int) (pair >> 32);
    }

    public static int lX(long pair) {
        return (int) pair;
    }
}
