package oracle.jdbc.diagnostics;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/SecurityLabel.class */
public enum SecurityLabel {
    KEYS('K', "Keys", "Encryption keys, passwords"),
    DATA('D', "Data", "User Data, Block dumps"),
    UNKNOWN('U', "Unknown", "Unknown content"),
    SLIGHT_DATA('L', "Slight Data", "Slight amounts of User Data"),
    PROGRAM('P', "Program", "Written by customers may contain user data, SQL statements"),
    ALGORITHM('A', "Algorithm", "Written by customers that don't contain data"),
    CONFIG('C', "Config", "Host Names, IP Addresses, Directory Info, Config Usernames"),
    METADATA('M', "Metadata", "Table names, index names, user program names"),
    INTERNAL('I', "Internal", "Internal to Oracle code or running instance"),
    STATIC('S', "Static", "Fixed string with no args");

    private final char label;
    private final String labelName;
    private final String description;

    SecurityLabel(char lbl, String name, String desc) {
        this.label = lbl;
        this.labelName = name;
        this.description = desc;
    }

    public char getLabel() {
        return this.label;
    }

    public String getLabelName() {
        return this.labelName;
    }

    public String getDescription() {
        return this.description;
    }
}
