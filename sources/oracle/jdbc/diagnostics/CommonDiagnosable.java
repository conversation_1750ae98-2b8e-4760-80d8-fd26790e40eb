package oracle.jdbc.diagnostics;

import java.util.logging.Level;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.logging.annotations.PropertiesBlinder;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/CommonDiagnosable.class */
public final class CommonDiagnosable extends AbstractDiagnosable {
    private static final PropertiesBlinder PROPERTIES_BLINDER = new PropertiesBlinder();
    private static final String CLASS_NAME = CommonDiagnosable.class.getName();
    private static final CommonDiagnosable COMMON_DIAGNOSABLE_INSTANCE = new CommonDiagnosable();
    private final String diagnosticLoggerName = getSystemProperty(OracleConnection.CONNECTION_PROPERTY_DIAGNOSTIC_LOGGER_NAME, OracleConnection.CONNECTION_PROPERTY_DIAGNOSTIC_LOGGER_NAME_DEFAULT, IS_VALID_LOGGER_NAME);
    private final int diagnosticBufferSize = Integer.parseInt(getSystemProperty(OracleConnection.CONNECTION_PROPERTY_DIAGNOSTIC_BUFFER_SIZE, "1000", IS_VALID_BUFFER_SIZE));
    private volatile boolean isDiagnoseFirstFailureEnabled = Boolean.parseBoolean(getSystemProperty(OracleConnection.CONNECTION_PROPERTY_ENABLE_DIAGNOSE_FIRST_FAILURE, "true", IS_VALID_BOOLEAN_STRING));
    private volatile boolean isDebugEnabled = Boolean.parseBoolean(getSystemProperty(OracleConnection.CONNECTION_PROPERTY_ENABLE_LOGGING, "false", IS_VALID_BOOLEAN_STRING));
    private final boolean isSensitivePermitted = Boolean.parseBoolean(getSystemProperty(OracleConnection.CONNECTION_PROPERTY_PERMIT_SENSITIVE_DIAGNOSTICS, "false", IS_VALID_BOOLEAN_STRING));
    private final boolean isSensitiveEnabled = Boolean.parseBoolean(getSystemProperty(OracleConnection.CONNECTION_PROPERTY_ENABLE_SENSITIVE_DIAGNOSTICS, "false", IS_VALID_BOOLEAN_STRING));

    private CommonDiagnosable() {
        init();
    }

    public static Diagnosable getInstance() {
        return COMMON_DIAGNOSABLE_INSTANCE;
    }

    private void init() {
        this.diagnostic = Diagnostic.get(this.diagnosticLoggerName, this.diagnosticBufferSize);
        setDebugEnabled(this.isDebugEnabled || isGlobalDebugEnabled());
        setDiagnoseFirstFailureEnabled(this.isDiagnoseFirstFailureEnabled);
        setSensitiveEnabled(this.isSensitiveEnabled);
        OracleDiagnosticsMXBean.addPropertyChangeListener(this);
        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "init", "Properties : {0}", (String) null, (String) null, PROPERTIES_BLINDER.blind(Diagnostic.SYSTEM_CONFIG));
    }

    @Override // oracle.jdbc.diagnostics.AbstractDiagnosable
    public final void setDebugEnabled(boolean enabled) {
        this.isDebugEnabled = enabled;
    }

    @Override // oracle.jdbc.diagnostics.AbstractDiagnosable
    public final void setDiagnoseFirstFailureEnabled(boolean enabled) {
        this.isDiagnoseFirstFailureEnabled = enabled;
    }

    @Override // oracle.jdbc.diagnostics.AbstractDiagnosable
    protected boolean isSensitivePermitted() {
        return this.isSensitivePermitted;
    }

    @Override // oracle.jdbc.diagnostics.AbstractDiagnosable
    public String getDiagnosticLoggerName() {
        return this.diagnosticLoggerName;
    }

    @Override // oracle.jdbc.diagnostics.AbstractDiagnosable
    protected TraceAttributes getTraceAttributes() {
        return null;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable, oracle.jdbc.diagnostics.PropertyChangeListener
    public void propertyChange(PropertyChangeEvent event) {
        switch (event.getPropertyName()) {
            case "Event-Dump-DiagnoseFirstFailure":
                if (this.isDiagnoseFirstFailureEnabled) {
                    this.diagnostic.dumpDiagnoseFirstFailure(true);
                    break;
                }
                break;
            default:
                abstractPropertyChange(event);
                break;
        }
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final boolean isDebugEnabled() {
        return this.isDebugEnabled;
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public final boolean isDiagnoseFirstFailureEnabled() {
        return this.isDiagnoseFirstFailureEnabled;
    }
}
