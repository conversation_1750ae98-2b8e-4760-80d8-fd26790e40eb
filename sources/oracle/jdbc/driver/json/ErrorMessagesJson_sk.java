package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_sk.class */
public class ErrorMessagesJson_sk extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Vyskytla sa výnimka I/O"}, new Object[]{"26302", "Rok \"{0}\" nie je podporovaný"}, new Object[]{"26303", "Pretečenie, hodnota je príliš veľká: {0}."}, new Object[]{"26304", "Nepodporovaná voľba (neimplementovaná)."}, new Object[]{"26305", "Binárny súbor JSON je neplatný alebo poškodený."}, new Object[]{"26306", "Nepodporovaná verzia binárneho súboru JSON: {0}."}, new Object[]{"26307", "<PERSON><PERSON><PERSON><PERSON> s kódovaním UTF-8 nesmie byť väčší ako 256 bajtov. Tento limit je prekročený pri nasledujúcom kľúči: \"{0}\"."}, new Object[]{"26308", "Zadaný súbor JSON je príliš veľký na to, aby bol kódovaný ako binárny súbor JSON. Veľkosť kódovaných obrázkov nesmie prekročiť 2 GB."}, new Object[]{"26309", "Binárny súbor JSON je neplatný alebo poškodený. Zadaný obrázok obsahuje len {0} B."}, new Object[]{"26310", "Zadaná trieda java.time.Period má nastavené dni, ale interval rok až mesiac definovaný spoločnosťou Oracle dni nepodporuje."}, new Object[]{"26311", "Generátor sa zatvoril pred skončením."}, new Object[]{"26312", "V tomto kontexte musí byť zadaný kľúč objektu."}, new Object[]{"26313", "Neplatný zápis. Celá hodnota už bola zapísaná."}, new Object[]{"26314", "Ukončenie nie je v tomto kontexte povolené."}, new Object[]{"26315", "Kľúč nie je v tomto kontexte povolený."}, new Object[]{"26316", "Očakávaná hodnota za kľúčom."}, new Object[]{"26317", "Stav syntaktického analyzátora musí byť {0}."}, new Object[]{"26318", "Stav syntaktického analyzátora nesmie byť {0}."}, new Object[]{"26319", "Syntaktický analyzátor musí byť na hodnote."}, new Object[]{"26320", "\"{0}\" nie je podporovaný typ wrappera."}, new Object[]{"26321", "Tento objekt nie je možné modifikovať. Ak chcete vytvoriť kópiu, ktorú je možné modifikovať, použite príkaz OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Toto pole nie je možné modifikovať. Ak chcete vytvoriť kópiu, ktorú je možné modifikovať, použite príkaz OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "Objekt JSON obsahuje duplicitný kľúč: {0}."}, new Object[]{"26324", "Kódovanie nie je možné zistiť automaticky pre nedostatok znakov."}, new Object[]{"26325", "Očakával sa token EOF, ale prijaté bolo {0}."}, new Object[]{"26326", "Neočakávaný znak {0} v riadku {1} a stĺpci {2}."}, new Object[]{"26327", "Neočakávaný znak {0} v riadku {1} a stĺpci {2}. Očakával sa znak {3}."}, new Object[]{"26328", "Neplatný token {0} v riadku {1} a stĺpci {2}. Očakávané tokeny sú: {3}."}, new Object[]{"26329", "Metóda JsonParser#getString() je platná len pre tieto stavy syntaktického analyzátora: KEY_NAME, VALUE_STRING a VALUE_NUMBER. Ale aktuálny stav syntaktického analyzátora je {0}."}, new Object[]{"26330", "Metóda JsonParser#isIntegralNumber() je platná len pre tento stav syntaktického analyzátora: VALUE_NUMBER. Ale aktuálny stav syntaktického analyzátora je {0}."}, new Object[]{"26331", "Metóda JsonParser#getInt() je platná len pre tento stav syntaktického analyzátora: VALUE_NUMBER. Ale aktuálny stav syntaktického analyzátora je {0}."}, new Object[]{"26332", "Metóda JsonParser#getLong() je platná len pre tento stav syntaktického analyzátora: VALUE_NUMBER. Ale aktuálny stav syntaktického analyzátora je {0}."}, new Object[]{"26333", "Metóda JsonParser#getBigDecimal() je platná len pre tento stav syntaktického analyzátora: VALUE_NUMBER. Ale aktuálny stav syntaktického analyzátora je {0}."}, new Object[]{"26334", "Metóda JsonParser#getArray() je platná len pre stav syntaktického analyzátora START_ARRAY. Aktuálny stav syntaktického analyzátora však je {0}."}, new Object[]{"26335", "Metóda JsonParser#getObject() je platná len pre tento stav syntaktického analyzátora: START_OBJECT. Ale aktuálny stav syntaktického analyzátora je {0}."}, new Object[]{"26336", "Časová značka s oblasťou nie je podporovaná. Podporované sú len posunuté časové pásma."}, new Object[]{"26337", "Objekty a polia v hodnote JSON nemôžu byť vnorené hlbšie než do {0} úrovní"}, new Object[]{"26338", "Kľúče objektu JSON nesmú prekročiť 65 535 bajtov"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
