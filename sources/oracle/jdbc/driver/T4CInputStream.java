package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CInputStream.class */
class T4CInputStream extends OracleInputStream {
    T4CInputStream(OracleStatement stmt, int index, Accessor a) {
        super(stmt, index, a);
    }

    @Override // oracle.jdbc.driver.OracleInputStream, oracle.jdbc.driver.OracleBufferedStream
    public boolean isNull() throws IOException {
        if (!this.statement.isFetchStreams) {
            return super.isNull();
        }
        try {
            int currentRow = this.statement.currentResultSet.getRow();
            if (currentRow < 0) {
                currentRow = 0;
            }
            if (currentRow >= this.statement.validRows) {
                return true;
            }
            boolean result = this.statement.isNull(currentRow, this.columnIndex);
            return result;
        } catch (SQLException exc) {
            throw ((IOException) DatabaseError.createIOException(exc).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.OracleInputStream
    public int getBytes(int howMany, byte[] buffer) throws IOException {
        this.statement.connection.assertLockHeldByCurrentThread();
        int ret = -1;
        try {
            if (this.statement.connection.getLifecycle() == 1 || this.statement.connection.getLifecycle() == 2) {
                ret = this.accessor.readStream(buffer, howMany);
            }
            return ret;
        } catch (IOException ea) {
            try {
                ((T4CConnection) this.statement.connection).handleIOException(ea);
            } catch (SQLException e) {
            }
            throw ea;
        } catch (SQLException e2) {
            throw new IOException(e2.getMessage());
        }
    }
}
