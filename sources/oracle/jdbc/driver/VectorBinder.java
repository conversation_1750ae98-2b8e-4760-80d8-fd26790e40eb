package oracle.jdbc.driver;

import java.sql.SQLException;
import oracle.jdbc.driver.VectorData;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorBinder.class */
final class VectorBinder<T> extends Binder {
    private T values;
    private final VectorData.Encoder<? super T> encoder;
    private final boolean isComputingNorm;
    Binder copyingBinder;

    VectorBinder(T values, int vectorType, boolean isComputingNorm) throws SQLException {
        this.values = values;
        this.type = (short) 127;
        this.isComputingNorm = isComputingNorm;
        this.encoder = VectorData.getEncoder(values, vectorType);
        this.bytelen = this.encoder.getMaximumByteLength(values);
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement oraclePreparedStatement, int i, int i2, int i3, byte[] bArr, char[] cArr, short[] sArr, int i4, int i5, int i6, int i7, int i8, int i9, boolean z, long j, ByteArray byteArray, long[] jArr, int[] iArr, int i10, boolean z2, int i11) throws SQLException {
        long position;
        ByteArray simpleByteArray;
        if (z2) {
            position = byteArray.getPosition();
            oraclePreparedStatement.lastBoundDataOffsets[i] = position;
            jArr[i10] = position;
            simpleByteArray = byteArray;
        } else {
            position = 0;
            simpleByteArray = new SimpleByteArray(oraclePreparedStatement.getDiagnosable(), bArr);
            simpleByteArray.setPosition(i6);
        }
        this.encoder.encode(this.values, this.isComputingNorm, simpleByteArray);
        int position2 = (int) (simpleByteArray.getPosition() - position);
        sArr[i9] = 0;
        sArr[i8] = (short) Math.min(position2, 32767);
        if (z2) {
            iArr[i10] = position2;
            oraclePreparedStatement.lastBoundDataLengths[i] = position2;
        }
        if (z) {
            clearValue();
        }
        return j;
    }

    void clearValue() {
        this.values = null;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.copyingBinder == null) {
            this.copyingBinder = new VectorCopyingBinder(this);
        }
        return this.copyingBinder;
    }
}
