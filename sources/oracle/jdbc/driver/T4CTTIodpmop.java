package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.diagnostics.Diagnosable;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIodpmop.class */
public final class T4CTTIodpmop extends T4CTTIfun {
    public static final int DPMOPOPC_ABORT = 1;
    public static final int DPMOPOPC_FINISH = 2;
    public static final int DPMOPOPC_FLUSH = 3;
    public static final int DPMOPOPC_DATASAVE = 4;
    public static final int DPMOPOPC_SETIU = 5;
    public static final int DPMOPOPC_FINISH_ONLY = 6;
    public static final int DPMOPOPC_ABORT_ONLY = 7;
    public static final int DPMOPOPC_CALL_KDBLAI = 8;
    public static final int DPMOPDEF_DATASAVEOPT_SAVEONLY = 0;
    public static final int DPMOPDEF_DATASAVEOPT_FINISH = 1;
    public static final int DPMOPDEF_DATASAVEOPT_PARTIAL = 2;
    public static final int DPMOPDEF_IN_PARMCOUNT = 1;
    public static final int DPMOPDEF_OUT_PARMCOUNT = 0;
    private int dpmopopc;
    private int dpmopcsr;
    private long[] dpmopi4;
    private int dpmopi4l;
    private long[] dpmopo4;

    @Override // oracle.jdbc.driver.T4CTTIMsg, oracle.jdbc.diagnostics.Diagnosable
    public /* bridge */ /* synthetic */ Diagnosable getDiagnosable() {
        return super.getDiagnosable();
    }

    T4CTTIodpmop(T4CConnection _conn) throws SQLException {
        super(_conn, (byte) 3);
        setFunCode((short) 130);
        clearState();
    }

    private void clearState() {
        this.dpmopopc = 0;
        this.dpmopcsr = 0;
        this.dpmopi4 = new long[1];
        this.dpmopi4l = 0;
        this.dpmopo4 = null;
    }

    void doDPMOP(int opCode, int cursor, long[] mopi4) throws SQLException, IOException {
        this.dpmopopc = opCode;
        this.dpmopcsr = cursor;
        if (mopi4 != null) {
            this.dpmopi4l = mopi4.length;
            this.dpmopi4 = mopi4;
        } else {
            this.dpmopi4l = 0;
        }
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.dpmopopc);
        this.meg.marshalSWORD(this.dpmopcsr);
        if (this.dpmopi4l > 0) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalUWORD(this.dpmopi4l);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        for (int i = 0; i < this.dpmopi4l; i++) {
            this.meg.marshalUB4(this.dpmopi4[i]);
        }
        clearState();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int dpmopo4l = this.meg.unmarshalUB2();
        if (dpmopo4l > 0) {
            this.dpmopo4 = new long[dpmopo4l];
            for (int i = 0; i < dpmopo4l; i++) {
                this.dpmopo4[i] = this.meg.unmarshalUB4();
            }
            return;
        }
        this.dpmopo4 = null;
    }

    long getO4Value(int key) {
        if (this.dpmopo4 == null || this.dpmopo4.length <= key) {
            return 0L;
        }
        return this.dpmopo4[key];
    }

    long[] getO4Values() {
        return this.dpmopo4;
    }
}
