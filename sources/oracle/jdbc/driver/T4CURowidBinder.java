package oracle.jdbc.driver;

import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CURowidBinder.class */
class T4CURowidBinder extends RowidBinder {
    static void init(Binder x) {
        x.type = (short) 208;
        x.bytelen = 130;
    }

    T4CURowidBinder(byte[] val) {
        super(val);
        this.theRowidCopyingBinder = new T4CURowidCopyingBinder();
        init(this);
    }

    @Override // oracle.jdbc.driver.RowidBinder, oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        byte[] b;
        int offset;
        byte[] value = this.paramVal;
        if (clearPriorBindValues) {
            this.paramVal = null;
        }
        if (value == null) {
            bindIndicators[indoffset] = -1;
            if (bindUseDBA) {
                bindDataOffsets[bindDataIndex] = -1;
                bindDataLengths[bindDataIndex] = 0;
            }
        } else {
            bindIndicators[indoffset] = 0;
            if (bindUseDBA) {
                long pos = bindData.getPosition();
                bindDataOffsets[bindDataIndex] = pos;
                stmt.lastBoundDataOffsets[bindPosition] = pos;
                b = stmt.connection.methodTempLargeByteBuffer;
                offset = 0;
            } else {
                b = bindBytes;
                offset = byteoffset + 2;
            }
            int len = value.length;
            int convertedLength = T4CRowidAccessor.kgrdc2ub(value, 0, b, offset, len);
            if (bindUseDBA) {
                bindData.put(b, 0, convertedLength);
                bindDataLengths[bindDataIndex] = convertedLength;
                stmt.lastBoundDataLengths[bindPosition] = convertedLength;
            } else {
                bindBytes[byteoffset] = (byte) (convertedLength >> 8);
                bindBytes[byteoffset + 1] = (byte) (convertedLength & 255);
            }
            bindIndicators[lenoffset] = (short) (convertedLength + 2);
        }
        if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
            if (bindIndicators[indoffset] == -1) {
                localCheckSum = CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
            } else {
                localCheckSum = CRC64.updateChecksum(localCheckSum, value, 0, value.length);
            }
        }
        return localCheckSum;
    }
}
