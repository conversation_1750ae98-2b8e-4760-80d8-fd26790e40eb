package oracle.jdbc.driver.json.binary;

import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.OsonAbstractArray;
import oracle.jdbc.driver.json.tree.OracleJsonNumberImpl;
import oracle.sql.json.OracleJsonArray;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonDate;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonTimestamp;
import oracle.sql.json.OracleJsonTimestampTZ;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonArrayImpl.class */
public class OsonArrayImpl extends OsonAbstractArray implements OracleJsonArray {
    public OsonArrayImpl(OsonContext ctx, int pos) {
        super(ctx, pos);
    }

    @Override // java.util.List, java.util.Collection, java.lang.Iterable
    public Iterator<OracleJsonValue> iterator() {
        return new OsonAbstractArray.ValueIter();
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // java.util.List
    public OracleJsonValue get(int index) {
        return (OracleJsonValue) getInternal(index);
    }

    @Override // java.util.List
    public ListIterator<OracleJsonValue> listIterator() {
        return listIterator(0);
    }

    @Override // java.util.List
    public List<OracleJsonValue> subList(int fromIndex, int toIndex) {
        return super.sublist(fromIndex, toIndex);
    }

    @Override // java.util.List
    public ListIterator<OracleJsonValue> listIterator(int index) {
        if (index < 0 || index > this.size) {
            throw new IndexOutOfBoundsException();
        }
        return new OsonAbstractArray.ListIter(index);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        OsonContext newCtx;
        if (Jsonp.isJakartaJson(c)) {
            newCtx = new JakartaOsonContext(this.ctx);
        } else {
            newCtx = new JsonpOsonContext(this.ctx);
        }
        return c.cast(newCtx.valueFactory.createArray(newCtx, this.pos));
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        StringWriter writer = new StringWriter();
        JsonSerializerImpl ser = new JsonSerializerImpl(writer);
        ser.write(this);
        ser.close();
        return writer.toString();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public <T extends OracleJsonValue> List<T> getValuesAs(Class<T> c) {
        return this;
    }

    @Override // oracle.sql.json.OracleJsonArray
    public double getDouble(int index) {
        int childOffset = getOffsetWithError(index);
        return ((OracleJsonNumberImpl) getValueInternal(childOffset)).doubleValue();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public BigDecimal getBigDecimal(int index) {
        int childOffset = getOffsetWithError(index);
        return ((OracleJsonNumberImpl) getValueInternal(childOffset)).bigDecimalValue();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public long getLong(int index) {
        int childOffset = getOffsetWithError(index);
        return ((OracleJsonNumberImpl) getValueInternal(childOffset)).longValue();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public LocalDateTime getLocalDateTime(int index) {
        OracleJsonValue o = (OracleJsonValue) getValueInternal(getOffsetWithError(index));
        if (o.getOracleJsonType() == OracleJsonValue.OracleJsonType.DATE) {
            return ((OracleJsonDate) o).getLocalDateTime();
        }
        return ((OracleJsonTimestamp) o).getLocalDateTime();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OffsetDateTime getOffsetDateTime(int index) {
        OracleJsonValue o = (OracleJsonValue) getValueInternal(getOffsetWithError(index));
        return ((OracleJsonTimestampTZ) o).getOffsetDateTime();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public byte[] getBytes(int index) {
        int childOffset = getOffsetWithError(index);
        OracleJsonBinary bin = (OracleJsonBinary) getValueInternal(childOffset);
        return bin.getBytes();
    }

    @Override // java.util.List, java.util.Collection
    public boolean add(OracleJsonValue e) {
        throw createNotModifiable();
    }

    @Override // java.util.List, java.util.Collection
    public boolean addAll(Collection<? extends OracleJsonValue> c) {
        throw createNotModifiable();
    }

    @Override // java.util.List
    public boolean addAll(int index, Collection<? extends OracleJsonValue> c) {
        throw createNotModifiable();
    }

    @Override // java.util.List
    public OracleJsonValue set(int index, OracleJsonValue element) {
        throw createNotModifiable();
    }

    @Override // java.util.List
    public void add(int index, OracleJsonValue element) {
        throw createNotModifiable();
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // java.util.List
    public OracleJsonValue remove(int index) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, String value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(String value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, int value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(int value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, double value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(double value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, boolean value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(boolean value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue setNull(int index) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void addNull() {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, LocalDateTime value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, OffsetDateTime value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(LocalDateTime value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(OffsetDateTime value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, byte[] value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(byte[] value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, long value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, BigDecimal value) throws OracleJsonException {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(long value) {
        throw createNotModifiable();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(BigDecimal value) {
        throw createNotModifiable();
    }

    private UnsupportedOperationException createNotModifiable() {
        throw OracleJsonExceptions.ARR_NOT_MUTABLE.create(OracleJsonExceptions.ORACLE_FACTORY, new Object[0]);
    }
}
