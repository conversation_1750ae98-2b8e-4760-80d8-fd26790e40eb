package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.LinkedHashMap;
import java.util.Map;
import oracle.jdbc.VectorMetaData;
import oracle.jdbc.internal.KeywordValue;
import oracle.jdbc.util.RepConversion;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTIuds.class */
class T4C8TTIuds extends T4CTTIMsg {
    T4CTTIoac udsoac;
    boolean udsnull;
    short udscnl;
    byte optimizeOAC;
    byte[] udscolnm;
    short udscolnl;
    byte[] udssnm;
    long udssnl;
    int[] snnumchar;
    byte[] udstnm;
    long udstnl;
    int[] tnnumchar;
    int[] numBytes;
    short udskpos;
    int udsflg;
    String domainName;
    String domainSchema;
    private long udsvecdim;
    private byte udsvectyp;
    private byte udsvecflg;
    private byte UDSFCOL_VEC_IS_FLEX_DIM;
    private byte UDSFCOL_VEC_IS_SPARSE;
    private Map<String, String> annotations;
    static final int UDSFCOLSEC_ENABLED = 1;
    static final int UDSFCOLSEC_UNKNOWN = 2;
    static final int UDSFCOLSEC_UNAUTH_DATA_NULL = 4;
    static final int UDSFCOL_IS_INVISIBLE = 8;
    static final int UDSFCOL_IS_JSON = 256;
    static final int UDSFCOL_HAS_DOMAIN = 16384;
    static final int UDSFCOL_HAS_ANNOTATIONS = 32768;
    public static final boolean TRACE = false;
    private static final String CLASS_NAME = T4C8TTIuds.class.getName();
    private static final String _Copyright_2014_Oracle_All_Rights_Reserved_ = null;

    T4C8TTIuds(T4CConnection _connection) {
        super(_connection, (byte) 0);
        this.UDSFCOL_VEC_IS_FLEX_DIM = (byte) 1;
        this.UDSFCOL_VEC_IS_SPARSE = (byte) 2;
        this.udskpos = (short) -1;
        this.udsoac = new T4CTTIoac(_connection);
    }

    void unmarshal() throws SQLException, IOException {
        this.udsoac.unmarshal();
        short null_allowed = this.meg.unmarshalUB1();
        this.udsnull = null_allowed > 0;
        this.udscnl = this.meg.unmarshalUB1();
        this.numBytes = new int[1];
        this.udscolnm = this.meg.unmarshalDALC(this.numBytes);
        if (this.connection.getTTCVersion() >= 8) {
            this.udscnl = (short) this.udscolnm.length;
        }
        this.snnumchar = new int[1];
        this.udssnm = this.meg.unmarshalDALC(this.snnumchar);
        this.udssnl = this.udssnm.length;
        this.tnnumchar = new int[1];
        this.udstnm = this.meg.unmarshalDALC(this.tnnumchar);
        this.udstnl = this.udstnm.length;
        if (this.connection.getTTCVersion() >= 3) {
            this.udskpos = (short) this.meg.unmarshalUB2();
            if (this.connection.getTTCVersion() >= 6) {
                this.udsflg = (int) this.meg.unmarshalUB4();
            }
        } else {
            this.udskpos = (short) -1;
        }
        if (this.connection.getTTCVersion() >= 17) {
            int[] ret = {0};
            byte[] udsdsc = this.meg.unmarshalDALC(ret);
            int udsdsl = ret[0];
            this.domainSchema = udsdsl == 0 ? null : this.meg.conv.CharBytesToString(udsdsc, udsdsl);
            byte[] udsdnm = this.meg.unmarshalDALC(ret);
            int udsdnl = ret[0];
            this.domainName = udsdnl == 0 ? null : this.meg.conv.CharBytesToString(udsdnm, udsdnl);
        }
        this.annotations = null;
        if (this.connection.getTTCVersion() >= 20) {
            int kvArrlen = (int) this.meg.unmarshalUB4();
            if (kvArrlen > 0) {
                this.meg.unmarshalUB1();
                T4CTTIkvarr t4CTTIkvarr = new T4CTTIkvarr(this.connection);
                t4CTTIkvarr.unmarshal();
                fillAnnotations(t4CTTIkvarr.kpdkvarrptr);
            }
        }
        if (this.connection.getTTCVersion() >= 24) {
            this.udsvecdim = this.meg.unmarshalUB4();
            this.udsvectyp = (byte) this.meg.unmarshalUB1();
            this.udsvecflg = (byte) this.meg.unmarshalUB1();
        }
    }

    private void fillAnnotations(KeywordValue[] kvs) throws SQLException {
        this.annotations = new LinkedHashMap();
        if (kvs != null) {
            for (KeywordValue kv : kvs) {
                byte[] binValue = kv.getBinaryValue();
                this.annotations.put(kv.getTextValue(), binValue == null ? "" : this.meg.conv.CharBytesToString(binValue, binValue.length));
            }
        }
    }

    short getKernelPosition() {
        return this.udskpos;
    }

    byte[] getColumName() {
        return this.udscolnm;
    }

    byte[] getTypeName() {
        return this.udstnm;
    }

    byte[] getSchemaName() {
        return this.udssnm;
    }

    short getTypeCharLength() {
        return (short) this.tnnumchar[0];
    }

    short getColumNameByteLength() {
        return (short) this.numBytes[0];
    }

    short getSchemaCharLength() {
        return (short) this.snnumchar[0];
    }

    String getDomainSchema() {
        return this.domainSchema;
    }

    String getDomainName() {
        return this.domainName;
    }

    Map<String, String> getAnnotations() {
        return this.annotations;
    }

    private /* synthetic */ Object[] lambda$print$0() {
        return new Object[]{RepConversion.bArray2String(this.udscolnm)};
    }

    private /* synthetic */ Object[] lambda$print$1() {
        return new Object[]{RepConversion.bArray2String(this.udssnm)};
    }

    private /* synthetic */ Object[] lambda$print$2() {
        return new Object[]{RepConversion.bArray2String(this.udstnm)};
    }

    void print() {
    }

    VectorMetaData getVectorMetaData() throws SQLException {
        int length;
        if (this.udsoac.oacdty != 127) {
            throw DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 89, "Type code is not DTYVEC: " + ((int) this.udsoac.oacdty));
        }
        if ((this.udsvecflg & this.UDSFCOL_VEC_IS_FLEX_DIM) != 0) {
            length = -1;
        } else if (this.udsvecdim <= 2147483647L) {
            length = (int) this.udsvecdim;
        } else {
            throw DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 89, "Vector length is larger than Integer.MAX_VALUE: " + this.udsvecdim);
        }
        int typeCode = VectorData.toOracleType(this.udsvectyp).getVendorTypeNumber().intValue();
        boolean isSparse = (this.udsvecflg & this.UDSFCOL_VEC_IS_SPARSE) != 0;
        return VectorMetaDataImpl.create(length, typeCode, isSparse);
    }
}
