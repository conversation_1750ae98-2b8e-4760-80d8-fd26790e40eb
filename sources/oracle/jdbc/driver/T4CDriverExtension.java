package oracle.jdbc.driver;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import java.util.concurrent.CompletionStage;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CDriverExtension.class */
class T4CDriverExtension extends OracleDriverExtension implements Monitor {
    private static final String tcDriverExtensionClassName = "oracle.jdbc.driver.TrueCacheDriverExtension";
    private static String shardingDriverExtensionClassName = "oracle.jdbc.driver.ShardingDriverExtension";
    private static final Monitor DEFAULT_CONN_MONITOR = Monitor.newInstance();
    private OracleDriverExtension shardingDriverExtension = null;
    private OracleDriverExtension tcDriverExtension = null;
    private final Monitor.CloseableLock monitorLock = Monitor.newDefaultLock();

    T4CDriverExtension() {
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    final Connection getConnection(String url, @Blind(PropertiesBlinder.class) Properties info, AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        Monitor.CloseableLock lock;
        T4CConnection conn = new T4CConnection(url, info, this);
        Monitor.CloseableLock connLock = conn.acquireCloseableLock();
        Throwable th = null;
        try {
            if (conn.useTrueCacheDriverConnection() && !conn.isTrueCacheDriverMode()) {
                if (this.tcDriverExtension == null) {
                    try {
                        lock = acquireCloseableLock();
                        Throwable th2 = null;
                        try {
                            try {
                                if (this.tcDriverExtension == null) {
                                    this.tcDriverExtension = (OracleDriverExtension) Class.forName(tcDriverExtensionClassName).newInstance();
                                }
                                if (lock != null) {
                                    if (0 != 0) {
                                        try {
                                            lock.close();
                                        } catch (Throwable th3) {
                                            th2.addSuppressed(th3);
                                        }
                                    } else {
                                        lock.close();
                                    }
                                }
                            } finally {
                            }
                        } finally {
                        }
                    } catch (Exception ex) {
                        throw new SQLException(ex);
                    }
                }
                Connection connection = this.tcDriverExtension.getConnection(url, info, builder);
                if (connLock != null) {
                    if (0 != 0) {
                        try {
                            connLock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        connLock.close();
                    }
                }
                return connection;
            }
            if (!conn.useShardingDriverConnection() || conn.isShardingDriverMode()) {
                try {
                    conn.connect(builder);
                    if (connLock != null) {
                        if (0 != 0) {
                            try {
                                connLock.close();
                            } catch (Throwable th5) {
                                th.addSuppressed(th5);
                            }
                        } else {
                            connLock.close();
                        }
                    }
                    return conn;
                } catch (Exception e) {
                    throw e;
                }
            }
            if (this.shardingDriverExtension == null) {
                try {
                    lock = acquireCloseableLock();
                    Throwable th6 = null;
                    try {
                        try {
                            if (this.shardingDriverExtension == null) {
                                this.shardingDriverExtension = (OracleDriverExtension) Class.forName(shardingDriverExtensionClassName).newInstance();
                            }
                            if (lock != null) {
                                if (0 != 0) {
                                    try {
                                        lock.close();
                                    } catch (Throwable th7) {
                                        th6.addSuppressed(th7);
                                    }
                                } else {
                                    lock.close();
                                }
                            }
                        } finally {
                        }
                    } finally {
                    }
                } catch (Exception e2) {
                }
            }
            Connection connection2 = this.shardingDriverExtension.getConnection(url, info, builder);
            if (connLock != null) {
                if (0 != 0) {
                    try {
                        connLock.close();
                    } catch (Throwable th8) {
                        th.addSuppressed(th8);
                    }
                } else {
                    connLock.close();
                }
            }
            return connection2;
        } catch (Throwable th9) {
            if (connLock != null) {
                if (0 != 0) {
                    try {
                        connLock.close();
                    } catch (Throwable th10) {
                        th.addSuppressed(th10);
                    }
                } else {
                    connLock.close();
                }
            }
            throw th9;
        }
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    final CompletionStage<T4CConnection> getConnectionAsync(String url, @Blind(PropertiesBlinder.class) Properties info, AbstractConnectionBuilder<?, ?> builder) {
        try {
            T4CConnection conn = new T4CConnection(url, info, this);
            return conn.connectAsync(builder).thenApply(nil -> {
                return conn;
            });
        } catch (SQLException preConnectFailure) {
            return CompletionStageUtil.failedStage(preConnectFailure);
        }
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T4CStatement allocateStatement(oracle.jdbc.internal.OracleConnection connection, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        return new T4CStatement((PhysicalConnection) connection, resultSetType);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T4CPreparedStatement allocatePreparedStatement(oracle.jdbc.internal.OracleConnection connection, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        return new T4CPreparedStatement((PhysicalConnection) connection, sql, resultSetType);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T4CPreparedStatement allocatePreparedStatement(oracle.jdbc.internal.OracleConnection connection, String sql, AutoKeyInfo autoKeyInfo) throws SQLException {
        return new T4CPreparedStatement((PhysicalConnection) connection, sql, autoKeyInfo);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T4CCallableStatement allocateCallableStatement(oracle.jdbc.internal.OracleConnection connection, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        return new T4CCallableStatement((PhysicalConnection) connection, sql, resultSetType);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T4CInputStream createInputStream(OracleStatement stmt, int index, Accessor accessor) throws SQLException {
        return new T4CInputStream(stmt, index, accessor);
    }

    @Override // oracle.jdbc.internal.Monitor
    public final Monitor.CloseableLock getMonitorLock() {
        return this.monitorLock;
    }
}
