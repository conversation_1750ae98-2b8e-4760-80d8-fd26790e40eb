package oracle.jdbc.driver;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.sql.Date;
import java.sql.SQLData;
import java.sql.SQLException;
import java.sql.SQLType;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Hashtable;
import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.OracleData;
import oracle.jdbc.OracleDataFactory;
import oracle.jdbc.OracleType;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.oracore.OracleNamedType;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.jdbc.oracore.OracleTypeCOLLECTION;
import oracle.jdbc.oracore.OracleTypeOPAQUE;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.BFILE;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.BLOB;
import oracle.sql.CHAR;
import oracle.sql.CLOB;
import oracle.sql.CharacterSet;
import oracle.sql.CustomDatum;
import oracle.sql.CustomDatumFactory;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.INTERVALDS;
import oracle.sql.INTERVALYM;
import oracle.sql.JAVA_STRUCT;
import oracle.sql.NUMBER;
import oracle.sql.OPAQUE;
import oracle.sql.ORAData;
import oracle.sql.ORADataFactory;
import oracle.sql.OpaqueDescriptor;
import oracle.sql.RAW;
import oracle.sql.REF;
import oracle.sql.ROWID;
import oracle.sql.SQLName;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.TypeDescriptor;
import oracle.sql.converter.CharacterSetMetaData;
import oracle.xdb.XMLType;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/SQLUtil.class */
public class SQLUtil {
    private static final int CLASS_NOT_FOUND = -1;
    private static final int CLASS_STRING = 0;
    private static final int CLASS_BOOLEAN = 1;
    private static final int CLASS_INTEGER = 2;
    private static final int CLASS_LONG = 3;
    private static final int CLASS_FLOAT = 4;
    private static final int CLASS_DOUBLE = 5;
    private static final int CLASS_BIGDECIMAL = 6;
    private static final int CLASS_DATE = 7;
    private static final int CLASS_TIME = 8;
    private static final int CLASS_TIMESTAMP = 9;
    private static final int CLASS_SHORT = 10;
    private static final int CLASS_BYTE = 11;
    private static final int TOTAL_CLASSES = 12;
    private static final String CLASS_NAME = SQLUtil.class.getName();
    private static Hashtable<Class<?>, Integer> classTable = new Hashtable<>(12);

    static {
        try {
            classTable.put(Class.forName("java.lang.String"), 0);
            classTable.put(Class.forName("java.lang.Boolean"), 1);
            classTable.put(Class.forName("java.lang.Integer"), 2);
            classTable.put(Class.forName("java.lang.Long"), 3);
            classTable.put(Class.forName("java.lang.Float"), 4);
            classTable.put(Class.forName("java.lang.Double"), 5);
            classTable.put(Class.forName("java.math.BigDecimal"), 6);
            classTable.put(Class.forName("java.sql.Date"), 7);
            classTable.put(Class.forName("java.sql.Time"), 8);
            classTable.put(Class.forName("java.sql.Timestamp"), 9);
            classTable.put(Class.forName("java.lang.Short"), 10);
            classTable.put(Class.forName("java.lang.Byte"), 11);
        } catch (ClassNotFoundException e) {
            CommonDiagnosable.getInstance().debug(Level.SEVERE, SecurityLabel.UNKNOWN, CLASS_NAME, "<static_block>", "{0}\n", (String) null, (String) e, (Object) e.getMessage());
        }
    }

    public static Object SQLToJava(oracle.jdbc.internal.OracleConnection connection, byte[] sqlData, int sqlTypeCode, String sqlTypeName, Class<?> javaClass, Map<String, Class<?>> map) throws SQLException {
        Datum datum = makeDatum(connection, sqlData, sqlTypeCode, sqlTypeName, 0);
        Object ret = SQLToJava(connection, datum, javaClass, map);
        return ret;
    }

    public static CustomDatum SQLToJava(oracle.jdbc.internal.OracleConnection connection, byte[] sqlData, int sqlTypeCode, String sqlTypeName, CustomDatumFactory factory) throws SQLException {
        Datum datum = makeDatum(connection, sqlData, sqlTypeCode, sqlTypeName, 0);
        CustomDatum ret = factory.create(datum, sqlTypeCode);
        return ret;
    }

    public static ORAData SQLToJava(oracle.jdbc.internal.OracleConnection connection, byte[] sqlData, int sqlTypeCode, String sqlTypeName, ORADataFactory factory) throws SQLException {
        Datum datum = makeDatum(connection, sqlData, sqlTypeCode, sqlTypeName, 0);
        ORAData ret = factory.create(datum, sqlTypeCode);
        return ret;
    }

    public static OracleData SQLToJava(oracle.jdbc.internal.OracleConnection connection, byte[] sqlData, int sqlTypeCode, String sqlTypeName, OracleDataFactory factory) throws SQLException {
        Datum datum = makeDatum(connection, sqlData, sqlTypeCode, sqlTypeName, 0);
        OracleData ret = factory.create(datum.toJdbc(), sqlTypeCode);
        return ret;
    }

    public static Object SQLToJava(oracle.jdbc.internal.OracleConnection connection, Datum datum, Class<?> javaClass, Map<String, Class<?>> map) throws SQLException {
        Object ret_obj;
        if (datum instanceof STRUCT) {
            if (javaClass == null) {
                ret_obj = map != null ? ((STRUCT) datum).toJdbc(map) : datum.toJdbc();
            } else {
                ret_obj = map != null ? ((STRUCT) datum).toClass(javaClass, map) : ((STRUCT) datum).toClass(javaClass);
            }
        } else if (javaClass == null) {
            ret_obj = datum.toJdbc();
        } else {
            int class_num = classNumber(javaClass);
            switch (class_num) {
                case -1:
                default:
                    ret_obj = datum.toJdbc();
                    if (!javaClass.isInstance(ret_obj)) {
                        throw ((SQLException) DatabaseError.createSqlException(59, "invalid data conversion").fillInStackTrace());
                    }
                    break;
                case 0:
                    ret_obj = datum.stringValue();
                    break;
                case 1:
                    ret_obj = Boolean.valueOf(datum.longValue() != 0);
                    break;
                case 2:
                    ret_obj = Integer.valueOf((int) datum.longValue());
                    break;
                case 3:
                    ret_obj = Long.valueOf(datum.longValue());
                    break;
                case 4:
                    ret_obj = Float.valueOf(datum.bigDecimalValue().floatValue());
                    break;
                case 5:
                    ret_obj = Double.valueOf(datum.bigDecimalValue().doubleValue());
                    break;
                case 6:
                    ret_obj = datum.bigDecimalValue();
                    break;
                case 7:
                    ret_obj = datum.dateValue();
                    break;
                case 8:
                    ret_obj = datum.timeValue();
                    break;
                case 9:
                    ret_obj = datum.timestampValue();
                    break;
                case 10:
                    ret_obj = Short.valueOf((short) datum.longValue());
                    break;
                case 11:
                    ret_obj = Byte.valueOf((byte) datum.longValue());
                    break;
            }
        }
        return ret_obj;
    }

    public static byte[] JavaToSQL(oracle.jdbc.internal.OracleConnection connection, Object inObject, int sqlTypeCode, String sqlTypeName) throws SQLException {
        byte[] ret;
        if (inObject == null) {
            return null;
        }
        Datum datum = null;
        if (inObject instanceof Datum) {
            datum = (Datum) inObject;
        } else if (inObject instanceof ORAData) {
            datum = ((ORAData) inObject).toDatum(connection);
        } else if (inObject instanceof CustomDatum) {
            datum = connection.toDatum((CustomDatum) inObject);
        } else if (inObject instanceof SQLData) {
            datum = STRUCT.toSTRUCT(inObject, connection);
        }
        if (datum != null) {
            if (!checkDatumType(datum, sqlTypeCode, sqlTypeName)) {
                datum = null;
            }
        } else {
            datum = makeDatum(connection, inObject, sqlTypeCode, sqlTypeName);
        }
        if (datum != null) {
            if (datum instanceof STRUCT) {
                ret = ((STRUCT) datum).toBytes();
            } else if (datum instanceof ARRAY) {
                ret = ((ARRAY) datum).toBytes();
            } else if (datum instanceof OPAQUE) {
                ret = ((OPAQUE) datum).toBytes();
            } else {
                ret = datum.shareBytes();
            }
            return ret;
        }
        throw ((SQLException) DatabaseError.createSqlException(1, "attempt to convert a Datum to incompatible SQL type").fillInStackTrace());
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/SQLUtil$XMLFactory.class */
    private static class XMLFactory {
        private XMLFactory() {
        }

        static Datum createXML(OPAQUE opq) throws SQLException {
            return XMLType.createXML(opq);
        }

        static Datum createXML(oracle.jdbc.internal.OracleConnection conn, String xmlval) throws SQLException {
            return XMLType.createXML(conn, xmlval);
        }

        static Datum createXML(oracle.jdbc.internal.OracleConnection conn, InputStream is) throws SQLException {
            return XMLType.createXML(conn, is);
        }
    }

    public static Datum makeDatum(oracle.jdbc.internal.OracleConnection connection, byte[] sqlData, int sqlTypeCode, String sqlTypeName, int maxLen) throws SQLException {
        return makeDatum(connection, sqlData, sqlTypeCode, sqlTypeName, maxLen, (short) 0, (short) 0);
    }

    public static Datum makeDatum(oracle.jdbc.internal.OracleConnection connection, byte[] sqlData, int sqlTypeCode, String sqlTypeName, int maxLen, short useJdbcCharSet, short useDbCharSet) throws SQLException {
        Datum ret_datum = null;
        short dbCharSet = useDbCharSet == 0 ? connection.getDbCsId() : useDbCharSet;
        short jdbcCharSet = useJdbcCharSet == 0 ? connection.getJdbcCsId() : useJdbcCharSet;
        int nlsChrSetRatio = CharacterSetMetaData.getRatio(jdbcCharSet, dbCharSet);
        switch (sqlTypeCode) {
            case 1:
            case 8:
                ret_datum = new CHAR(sqlData, CharacterSet.make(jdbcCharSet));
                break;
            case 2:
            case 6:
                ret_datum = new NUMBER(sqlData);
                break;
            case 12:
                ret_datum = new DATE(sqlData);
                break;
            case 23:
            case 24:
                ret_datum = new RAW(sqlData);
                break;
            case 96:
                if (maxLen != 0 && maxLen < sqlData.length && nlsChrSetRatio == 1) {
                    ret_datum = new CHAR(sqlData, 0, maxLen, CharacterSet.make(jdbcCharSet));
                    break;
                } else {
                    ret_datum = new CHAR(sqlData, CharacterSet.make(jdbcCharSet));
                    break;
                }
            case 100:
                ret_datum = new BINARY_FLOAT(sqlData);
                break;
            case 101:
                ret_datum = new BINARY_DOUBLE(sqlData);
                break;
            case 102:
                throw ((SQLException) DatabaseError.createSqlException(1, "need resolution: do we want to handle ResultSet?").fillInStackTrace());
            case 104:
                ret_datum = new ROWID(sqlData);
                break;
            case 109:
                TypeDescriptor desc = TypeDescriptor.getTypeDescriptor(sqlTypeName, connection, sqlData, 0L);
                switch (desc.getTypeCode()) {
                    case 2002:
                        ret_datum = new STRUCT((StructDescriptor) desc, sqlData, connection);
                        break;
                    case 2003:
                        ret_datum = new ARRAY((ArrayDescriptor) desc, sqlData, connection);
                        break;
                    case oracle.jdbc.OracleTypes.OPAQUE /* 2007 */:
                        ret_datum = new OPAQUE((OpaqueDescriptor) desc, sqlData, connection);
                        break;
                    case 2008:
                        ret_datum = new JAVA_STRUCT((StructDescriptor) desc, sqlData, connection);
                        break;
                    case 2009:
                        ret_datum = XMLFactory.createXML(new OPAQUE((OpaqueDescriptor) desc, sqlData, connection));
                        break;
                }
            case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                Object desc2 = getTypeDescriptor(sqlTypeName, connection);
                if (desc2 instanceof StructDescriptor) {
                    ret_datum = new REF((StructDescriptor) desc2, connection, sqlData);
                    break;
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(1, "program error: REF points to a non-STRUCT").fillInStackTrace());
                }
            case 112:
                ret_datum = connection.createClob(sqlData);
                break;
            case 113:
                ret_datum = connection.createBlob(sqlData);
                break;
            case 114:
                ret_datum = connection.createBfile(sqlData);
                break;
            case 180:
                ret_datum = new TIMESTAMP(sqlData);
                break;
            case 181:
                ret_datum = new TIMESTAMPTZ(sqlData);
                break;
            case 182:
                ret_datum = new INTERVALYM(sqlData);
                break;
            case 183:
                ret_datum = new INTERVALDS(sqlData);
                break;
            case CharacterSet.WE8BS2000_CHARSET /* 231 */:
                ret_datum = new TIMESTAMPLTZ(sqlData);
                break;
            case DatabaseError.EOJ_INVALID_DRIVER_NAME_ATTR /* 257 */:
                ret_datum = XMLFactory.createXML(connection, new ByteArrayInputStream(sqlData));
                break;
            default:
                throw ((SQLException) DatabaseError.createSqlException(1, "program error: invalid SQL type code").fillInStackTrace());
        }
        return ret_datum;
    }

    public static Datum makeNDatum(oracle.jdbc.internal.OracleConnection connection, byte[] sqlData, int sqlTypeCode, String sqlTypeName, short form, int maxLen) throws SQLException {
        Datum ret_datum;
        switch (sqlTypeCode) {
            case 1:
            case 8:
                ret_datum = new CHAR(sqlData, CharacterSet.make(connection.getNCharSet()));
                break;
            case 96:
                int len = maxLen * CharacterSetMetaData.getRatio(connection.getNCharSet(), 1);
                if (maxLen != 0 && len < sqlData.length) {
                    ret_datum = new CHAR(sqlData, 0, maxLen, CharacterSet.make(connection.getNCharSet()));
                    break;
                } else {
                    ret_datum = new CHAR(sqlData, CharacterSet.make(connection.getNCharSet()));
                    break;
                }
                break;
            case 112:
                ret_datum = connection.createClob(sqlData, form);
                break;
            default:
                throw ((SQLException) DatabaseError.createSqlException(1, "program error: invalid SQL type code").fillInStackTrace());
        }
        return ret_datum;
    }

    public static Datum makeDatum(oracle.jdbc.internal.OracleConnection connection, Object inObject, int sqlTypeCode, String sqlTypeName) throws SQLException {
        return makeDatum(connection, inObject, sqlTypeCode, sqlTypeName, false);
    }

    public static Datum makeDatum(oracle.jdbc.internal.OracleConnection connection, Object inObject, int sqlTypeCode, String sqlTypeName, boolean isNChar) throws SQLException {
        Datum ret_datum = null;
        switch (sqlTypeCode) {
            case 1:
            case 8:
            case 96:
                ret_datum = new CHAR(inObject, CharacterSet.make(isNChar ? connection.getNCharSet() : connection.getJdbcCsId()));
                break;
            case 2:
            case 6:
                ret_datum = new NUMBER(inObject);
                break;
            case 12:
                ret_datum = (Datum) JavaToJavaConverter.convert(inObject, DATE.class, (OracleConnection) connection, null, null);
                break;
            case 23:
            case 24:
                ret_datum = new RAW(inObject);
                break;
            case 100:
                if (inObject instanceof String) {
                    ret_datum = new BINARY_FLOAT((String) inObject);
                    break;
                } else if (inObject instanceof Boolean) {
                    ret_datum = new BINARY_FLOAT((Boolean) inObject);
                    break;
                } else {
                    ret_datum = new BINARY_FLOAT((Float) inObject);
                    break;
                }
            case 101:
                if (inObject instanceof String) {
                    ret_datum = new BINARY_DOUBLE((String) inObject);
                    break;
                } else if (inObject instanceof Boolean) {
                    ret_datum = new BINARY_DOUBLE((Boolean) inObject);
                    break;
                } else {
                    ret_datum = new BINARY_DOUBLE((Double) inObject);
                    break;
                }
            case 102:
                throw ((SQLException) DatabaseError.createSqlException(1, "need resolution: do we want to handle ResultSet").fillInStackTrace());
            case 104:
                if (inObject instanceof String) {
                    ret_datum = new ROWID((String) inObject);
                    break;
                } else if (inObject instanceof byte[]) {
                    ret_datum = new ROWID((byte[]) inObject);
                    break;
                }
                break;
            case 109:
                if ((inObject instanceof STRUCT) || (inObject instanceof ARRAY) || (inObject instanceof OPAQUE)) {
                    ret_datum = (Datum) inObject;
                    break;
                }
                break;
            case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                if (inObject instanceof REF) {
                    ret_datum = (Datum) inObject;
                    break;
                }
                break;
            case 112:
                if (inObject instanceof CLOB) {
                    ret_datum = (Datum) inObject;
                }
                if (inObject instanceof String) {
                    CharacterSet chset = CharacterSet.make(isNChar ? connection.getNCharSet() : connection.getJdbcCsId());
                    ret_datum = new CHAR((String) inObject, chset);
                    break;
                }
                break;
            case 113:
                if (inObject instanceof BLOB) {
                    ret_datum = (Datum) inObject;
                }
                if (inObject instanceof byte[]) {
                    ret_datum = new RAW((byte[]) inObject);
                    break;
                }
                break;
            case 114:
                if (inObject instanceof BFILE) {
                    ret_datum = (Datum) inObject;
                    break;
                }
                break;
            case 180:
                if (inObject instanceof TIMESTAMP) {
                    ret_datum = (Datum) inObject;
                    break;
                } else if (inObject instanceof Timestamp) {
                    ret_datum = new TIMESTAMP((Timestamp) inObject);
                    break;
                } else if (inObject instanceof Date) {
                    ret_datum = new TIMESTAMP((Date) inObject);
                    break;
                } else if (inObject instanceof Time) {
                    ret_datum = new TIMESTAMP((Time) inObject);
                    break;
                } else if (inObject instanceof DATE) {
                    ret_datum = new TIMESTAMP((DATE) inObject);
                    break;
                } else if (inObject instanceof String) {
                    ret_datum = new TIMESTAMP((String) inObject);
                    break;
                } else if (inObject instanceof byte[]) {
                    ret_datum = new TIMESTAMP((byte[]) inObject);
                    break;
                } else {
                    ret_datum = (Datum) JavaToJavaConverter.convert(inObject, TIMESTAMP.class, (OracleConnection) connection, null, null);
                    break;
                }
            case 181:
                if (inObject instanceof TIMESTAMPTZ) {
                    ret_datum = (Datum) inObject;
                    break;
                } else if (inObject instanceof Timestamp) {
                    ret_datum = new TIMESTAMPTZ(connection, (Timestamp) inObject);
                    break;
                } else if (inObject instanceof Date) {
                    ret_datum = new TIMESTAMPTZ(connection, (Date) inObject);
                    break;
                } else if (inObject instanceof Time) {
                    ret_datum = new TIMESTAMPTZ(connection, (Time) inObject);
                    break;
                } else if (inObject instanceof DATE) {
                    ret_datum = new TIMESTAMPTZ(connection, (DATE) inObject);
                    break;
                } else if (inObject instanceof String) {
                    ret_datum = new TIMESTAMPTZ(connection, (String) inObject);
                    break;
                } else if (inObject instanceof byte[]) {
                    ret_datum = new TIMESTAMPTZ((byte[]) inObject);
                    break;
                } else {
                    ret_datum = (Datum) JavaToJavaConverter.convert(inObject, TIMESTAMPTZ.class, (OracleConnection) connection, null, null);
                    break;
                }
            case 182:
                ret_datum = (Datum) JavaToJavaConverter.convert(inObject, INTERVALYM.class, (OracleConnection) connection, null, null);
                break;
            case 183:
                ret_datum = (Datum) JavaToJavaConverter.convert(inObject, INTERVALDS.class, (OracleConnection) connection, null, null);
                break;
            case CharacterSet.WE8BS2000_CHARSET /* 231 */:
                if (inObject instanceof TIMESTAMPLTZ) {
                    ret_datum = (Datum) inObject;
                    break;
                } else if (inObject instanceof Timestamp) {
                    ret_datum = new TIMESTAMPLTZ(connection, (Timestamp) inObject);
                    break;
                } else if (inObject instanceof Date) {
                    ret_datum = new TIMESTAMPLTZ(connection, (Date) inObject);
                    break;
                } else if (inObject instanceof Time) {
                    ret_datum = new TIMESTAMPLTZ(connection, (Time) inObject);
                    break;
                } else if (inObject instanceof DATE) {
                    ret_datum = new TIMESTAMPLTZ(connection, (DATE) inObject);
                    break;
                } else if (inObject instanceof String) {
                    ret_datum = new TIMESTAMPLTZ(connection, (String) inObject);
                    break;
                } else if (inObject instanceof byte[]) {
                    ret_datum = new TIMESTAMPLTZ((byte[]) inObject);
                    break;
                } else {
                    ret_datum = (Datum) JavaToJavaConverter.convert(inObject, TIMESTAMPLTZ.class, (OracleConnection) connection, null, null);
                    break;
                }
            case DatabaseError.EOJ_INVALID_DRIVER_NAME_ATTR /* 257 */:
                if (inObject instanceof String) {
                    ret_datum = XMLFactory.createXML(connection, (String) inObject);
                    break;
                }
                break;
        }
        if (ret_datum == null) {
            throw ((SQLException) DatabaseError.createSqlException(1, "Unable to construct a Datum from the specified input").fillInStackTrace());
        }
        return ret_datum;
    }

    private static int classNumber(Class<?> inClass) {
        int ret = -1;
        Integer class_num = classTable.get(inClass);
        if (class_num != null) {
            ret = class_num.intValue();
        }
        return ret;
    }

    public static Object getTypeDescriptor(String name, oracle.jdbc.internal.OracleConnection conn) throws SQLException {
        Object descriptor;
        SQLName sqlName = new SQLName(name, conn);
        String qname = sqlName.getName();
        Object descriptor2 = conn.getDescriptor(qname);
        if (descriptor2 != null) {
            return descriptor2;
        }
        OracleTypeADT otype = new OracleTypeADT(qname, conn);
        otype.init(conn);
        OracleNamedType realType = otype.cleanup();
        switch (realType.getTypeCode()) {
            case 2002:
            case 2008:
                descriptor = new StructDescriptor(sqlName, (OracleTypeADT) realType, conn);
                break;
            case 2003:
                descriptor = new ArrayDescriptor(sqlName, (OracleTypeCOLLECTION) realType, conn);
                break;
            case oracle.jdbc.OracleTypes.BLOB /* 2004 */:
            case oracle.jdbc.OracleTypes.CLOB /* 2005 */:
            case 2006:
            default:
                throw ((SQLException) DatabaseError.createSqlException(1, "Unrecognized type code").fillInStackTrace());
            case oracle.jdbc.OracleTypes.OPAQUE /* 2007 */:
                descriptor = new OpaqueDescriptor(sqlName, (OracleTypeOPAQUE) realType, conn);
                break;
        }
        conn.putDescriptor(qname, descriptor);
        return descriptor;
    }

    public static boolean checkDatumType(Datum datum, int sqlType, String sqlTypeName) throws SQLException {
        boolean ret = false;
        switch (sqlType) {
            case 1:
            case 8:
            case 96:
                ret = datum instanceof CHAR;
                break;
            case 2:
            case 6:
                ret = datum instanceof NUMBER;
                break;
            case 12:
                ret = datum instanceof DATE;
                break;
            case 23:
            case 24:
                ret = datum instanceof RAW;
                break;
            case 100:
                ret = datum instanceof BINARY_FLOAT;
                break;
            case 101:
                ret = datum instanceof BINARY_DOUBLE;
                break;
            case 102:
            default:
                ret = false;
                break;
            case 104:
                ret = datum instanceof ROWID;
                break;
            case 109:
                if (datum instanceof STRUCT) {
                    ret = ((STRUCT) datum).isInHierarchyOf(sqlTypeName);
                    break;
                } else if (datum instanceof ARRAY) {
                    ret = ((ARRAY) datum).getSQLTypeName().equals(sqlTypeName);
                    break;
                } else if (datum instanceof OPAQUE) {
                    ret = ((OPAQUE) datum).getSQLTypeName().equals(sqlTypeName);
                    break;
                }
                break;
            case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                ret = (datum instanceof REF) && ((REF) datum).getBaseTypeName().equals(sqlTypeName);
                break;
            case 112:
                ret = datum instanceof CLOB;
                break;
            case 113:
                ret = datum instanceof BLOB;
                break;
            case 114:
                ret = datum instanceof BFILE;
                break;
            case 180:
                ret = datum instanceof TIMESTAMP;
                break;
            case 181:
                ret = datum instanceof TIMESTAMPTZ;
                break;
            case CharacterSet.WE8BS2000_CHARSET /* 231 */:
                ret = datum instanceof TIMESTAMPLTZ;
                break;
        }
        return ret;
    }

    public static boolean implementsInterface(Class<?> clazz, Class<?> interfaze) {
        if (clazz == null) {
            return false;
        }
        if (clazz == interfaze) {
            return true;
        }
        Class<?>[] interfazes = clazz.getInterfaces();
        for (Class<?> cls : interfazes) {
            if (implementsInterface(cls, interfaze)) {
                return true;
            }
        }
        return implementsInterface(clazz.getSuperclass(), interfaze);
    }

    public static Datum makeOracleDatum(oracle.jdbc.internal.OracleConnection connection, Object inObject, int typeCode, String sqlTypeName) throws SQLException {
        return makeOracleDatum(connection, inObject, typeCode, sqlTypeName, false);
    }

    public static Datum makeOracleDatum(oracle.jdbc.internal.OracleConnection connection, Object inObject, int typeCode, String sqlTypeName, boolean isNChar) throws SQLException {
        Datum ret = makeDatum(connection, inObject, getInternalType(typeCode), sqlTypeName, isNChar);
        return ret;
    }

    public static int getInternalType(int external_type) throws SQLException {
        int ret;
        switch (external_type) {
            case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
            case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
            case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
            case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                ret = 127;
                break;
            case oracle.jdbc.OracleTypes.INTERVALDS /* -104 */:
                ret = 183;
                break;
            case oracle.jdbc.OracleTypes.INTERVALYM /* -103 */:
                ret = 182;
                break;
            case oracle.jdbc.OracleTypes.TIMESTAMPLTZ /* -102 */:
                ret = 231;
                break;
            case oracle.jdbc.OracleTypes.TIMESTAMPTZ /* -101 */:
                ret = 181;
                break;
            case oracle.jdbc.OracleTypes.TIMESTAMPNS /* -100 */:
            case 93:
                ret = 180;
                break;
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
                ret = 96;
                break;
            case oracle.jdbc.OracleTypes.BFILE /* -13 */:
                ret = 114;
                break;
            case oracle.jdbc.OracleTypes.CURSOR /* -10 */:
            case oracle.jdbc.OracleTypes.REF_CURSOR /* 2012 */:
                ret = 102;
                break;
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
                ret = 1;
                break;
            case oracle.jdbc.OracleTypes.ROWID /* -8 */:
                ret = 104;
                break;
            case oracle.jdbc.OracleTypes.BIT /* -7 */:
            case oracle.jdbc.OracleTypes.TINYINT /* -6 */:
            case -5:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
                ret = 6;
                break;
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
                ret = 24;
                break;
            case -3:
            case -2:
                ret = 23;
                break;
            case -1:
                ret = 8;
                break;
            case 1:
                ret = 96;
                break;
            case 2:
                ret = 2;
                break;
            case 12:
                ret = 1;
                break;
            case 91:
            case 92:
                ret = 12;
                break;
            case 100:
                ret = 100;
                break;
            case 101:
                ret = 101;
                break;
            case oracle.jdbc.OracleTypes.FIXED_CHAR /* 999 */:
                ret = 999;
                break;
            case 2002:
            case 2003:
            case oracle.jdbc.OracleTypes.OPAQUE /* 2007 */:
            case 2008:
                ret = 109;
                break;
            case oracle.jdbc.OracleTypes.BLOB /* 2004 */:
                ret = 113;
                break;
            case oracle.jdbc.OracleTypes.CLOB /* 2005 */:
                ret = 112;
                break;
            case 2006:
                ret = 111;
                break;
            case 2009:
                ret = 257;
                break;
            case oracle.jdbc.OracleTypes.NCLOB /* 2011 */:
                ret = 112;
                break;
            case oracle.jdbc.OracleTypes.JSON /* 2016 */:
                ret = 119;
                break;
            default:
                throw ((SQLException) DatabaseError.createSqlException(4, "get_internal_type").fillInStackTrace());
        }
        return ret;
    }

    public static SQLType getExternalType(int internalTypeId) {
        OracleType ret;
        OracleType oracleType = OracleType.ANYTYPE;
        switch (internalTypeId) {
            case 1:
                ret = OracleType.VARCHAR2;
                break;
            case 2:
                ret = OracleType.NUMBER;
                break;
            case 12:
                ret = OracleType.DATE;
                break;
            case 23:
                ret = OracleType.RAW;
                break;
            case 96:
                ret = OracleType.CHAR;
                break;
            case 180:
                ret = OracleType.TIMESTAMP;
                break;
            case CharacterSet.WE8BS2000_CHARSET /* 231 */:
                ret = OracleType.TIMESTAMP_WITH_LOCAL_TIME_ZONE;
                break;
            default:
                ret = OracleType.ANYTYPE;
                break;
        }
        return ret;
    }

    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
