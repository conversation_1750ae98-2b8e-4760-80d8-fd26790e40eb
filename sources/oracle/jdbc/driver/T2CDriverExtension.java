package oracle.jdbc.driver;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import java.util.concurrent.CompletionStage;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.pool.OracleOCIConnectionPool;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CDriverExtension.class */
class T2CDriverExtension extends OracleDriverExtension {
    T2CDriverExtension() {
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    /* bridge */ /* synthetic */ Connection getConnection(String str, @Blind(PropertiesBlinder.class) Properties properties, AbstractConnectionBuilder abstractConnectionBuilder) throws SQLException {
        return getConnection(str, properties, (AbstractConnectionBuilder<?, ?>) abstractConnectionBuilder);
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    final T2CConnection getConnection(String url, @Blind(PropertiesBlinder.class) Properties info, AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        T2CConnection t2Conn;
        if (builder.getTokenSupplier() != null) {
            throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "Type 2 driver does not support token-based authentication").fillInStackTrace());
        }
        if (info.getProperty(OracleOCIConnectionPool.IS_CONNECTION_POOLING) == "true") {
            t2Conn = new oracle.jdbc.oci.OracleOCIConnection(url, info, this);
        } else {
            t2Conn = new T2CConnection(url, info, this);
        }
        Monitor.CloseableLock lock = t2Conn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                t2Conn.connect(builder);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return t2Conn;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    final CompletionStage<Connection> getConnectionAsync(String url, @Blind(PropertiesBlinder.class) Properties info, AbstractConnectionBuilder<?, ?> builder) {
        return CompletionStageUtil.failedStage(new UnsupportedOperationException("Asynchronous connection is not supported by the Type 2 OCI driver"));
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T2CStatement allocateStatement(oracle.jdbc.internal.OracleConnection connection, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        return new T2CStatement((T2CConnection) connection, resultSetType);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T2CPreparedStatement allocatePreparedStatement(oracle.jdbc.internal.OracleConnection connection, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        return new T2CPreparedStatement((T2CConnection) connection, sql, resultSetType);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T2CPreparedStatement allocatePreparedStatement(oracle.jdbc.internal.OracleConnection connection, String sql, AutoKeyInfo autoKeyInfo) throws SQLException {
        return new T2CPreparedStatement((T2CConnection) connection, sql, autoKeyInfo);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T2CCallableStatement allocateCallableStatement(oracle.jdbc.internal.OracleConnection conn, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        return new T2CCallableStatement((T2CConnection) conn, sql, resultSetType);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    public T2CInputStream createInputStream(OracleStatement stmt, int index, Accessor accessor) throws SQLException {
        return new T2CInputStream(stmt, index, accessor);
    }
}
