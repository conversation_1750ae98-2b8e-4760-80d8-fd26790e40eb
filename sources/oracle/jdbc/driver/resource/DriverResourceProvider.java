package oracle.jdbc.driver.resource;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.resource.ClearTextParameters;
import oracle.jdbc.driver.resource.ResourceProvider;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.spi.OracleResourceProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/DriverResourceProvider.class */
final class DriverResourceProvider<T extends OracleResourceProvider, U> implements ResourceProvider<T, U> {
    private final ResourceType<T, U> resourceType;
    private final T provider;
    private final Set<ParameterValue> parameterValues;

    private DriverResourceProvider(Builder<T, U> builder) throws SQLException {
        this.resourceType = ((Builder) builder).resourceType;
        this.provider = (T) ((Builder) builder).provider;
        this.parameterValues = Collections.unmodifiableSet(builder.copyParameterValues());
    }

    @Override // oracle.jdbc.driver.resource.ResourceProvider
    public U getResource() throws SQLException {
        try {
            ClearTextParameters clearTextParameters = getClearTextParameters();
            Throwable th = null;
            try {
                U resource = this.resourceType.getResource(this.provider, clearTextParameters.getParameterValues());
                if (clearTextParameters != null) {
                    if (0 != 0) {
                        try {
                            clearTextParameters.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        clearTextParameters.close();
                    }
                }
                if (resource == null) {
                    throw createSQLException("Invocation of getResource returned null with OracleResourceProvider: " + this.provider.getClass().getName());
                }
                return resource;
            } finally {
            }
        } catch (Exception exception) {
            throw createSQLException(null, exception);
        }
    }

    private ClearTextParameters getClearTextParameters() {
        ClearTextParameters.Builder builder = ClearTextParameters.builder();
        for (ParameterValue parameterValue : this.parameterValues) {
            parameterValue.addClearText(builder);
        }
        return builder.build();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static SQLException createSQLException(String message) {
        return createSQLException(message, null);
    }

    private static SQLException createSQLException(String message, Throwable cause) {
        return (SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_RESOURCE_PROVIDER_FAILURE, message, cause).fillInStackTrace();
    }

    static <T extends OracleResourceProvider, U> Builder<T, U> builder(String providerName, ResourceType<T, U> resourceType) throws SQLException {
        OracleResourceProvider oracleResourceProviderLoadInstalledProvider;
        if (providerName.equalsIgnoreCase(resourceType.getDefaultProviderName())) {
            oracleResourceProviderLoadInstalledProvider = loadDefaultProvider(resourceType);
        } else {
            oracleResourceProviderLoadInstalledProvider = loadInstalledProvider(providerName, resourceType);
        }
        OracleResourceProvider oracleResourceProvider = oracleResourceProviderLoadInstalledProvider;
        Map<String, OracleResourceProvider.Parameter> providerParameters = getProviderParameters(oracleResourceProvider);
        return new Builder<>(resourceType, oracleResourceProvider, providerParameters);
    }

    private static <T extends OracleResourceProvider> T loadInstalledProvider(String str, ResourceType<T, ?> resourceType) throws SQLException {
        InstalledProviders installedProvidersLoad = InstalledProviders.load(resourceType);
        T t = (T) installedProvidersLoad.get(str);
        if (t == null) {
            throw createSQLException(String.format("No implementation of %s with name: \"%s\" can be located. Implementations of %1$s that can be located have the following names : %s", resourceType.getProviderClass().getName(), str, installedProvidersLoad.getNames()));
        }
        return t;
    }

    private static <T extends OracleResourceProvider> T loadDefaultProvider(ResourceType<T, ?> resourceType) throws SQLException {
        try {
            return (T) resourceType.getDefaultProvider();
        } catch (Exception e) {
            throw createSQLException("Failed to load default provider for resource type: " + resourceType, e);
        }
    }

    private static Map<String, OracleResourceProvider.Parameter> getProviderParameters(OracleResourceProvider resourceProvider) throws SQLException {
        Collection<? extends OracleResourceProvider.Parameter> parameters = resourceProvider.getParameters();
        if (parameters == null) {
            throw createSQLException(String.format("Invocation of getParameters() on %s returned null", resourceProvider.getClass().getName()));
        }
        Map<String, OracleResourceProvider.Parameter> namedParameters = new TreeMap<>((Comparator<? super String>) String.CASE_INSENSITIVE_ORDER);
        for (OracleResourceProvider.Parameter parameter : parameters) {
            if (parameter == null) {
                throw createSQLException("Invocation of getParameters() on %s returns a collection containing a null value.");
            }
            String name = parameter.name();
            OracleResourceProvider.Parameter duplicate = namedParameters.put(name, parameter);
            if (duplicate != null) {
                throw createSQLException(String.format("Invocation of getParameters() on %s returns multiple parameters with the name \"%s\"", resourceProvider.getClass().getName(), name));
            }
        }
        return namedParameters;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/DriverResourceProvider$Builder.class */
    static final class Builder<T extends OracleResourceProvider, U> implements ResourceProvider.Builder<T, U> {
        private final ResourceType<T, U> resourceType;
        private final T provider;
        private final Map<String, OracleResourceProvider.Parameter> providerParameters;
        private final Map<OracleResourceProvider.Parameter, ParameterValue> parameterValues;

        private Builder(ResourceType<T, U> resourceType, T provider, Map<String, OracleResourceProvider.Parameter> providerParameters) {
            this.parameterValues = new HashMap();
            this.resourceType = resourceType;
            this.provider = provider;
            this.providerParameters = providerParameters;
        }

        @Override // oracle.jdbc.driver.resource.ResourceProvider.Builder
        public Builder<T, U> parameterValue(String name, CharSequence value) throws SQLException {
            Objects.requireNonNull(name);
            OracleResourceProvider.Parameter parameter = getParameter(name);
            addParameterValue(parameter, value);
            return this;
        }

        @Override // oracle.jdbc.driver.resource.ResourceProvider.Builder
        public Builder<T, U> parameterValue(String name, OpaqueString value) throws SQLException {
            Objects.requireNonNull(name);
            OracleResourceProvider.Parameter parameter = getParameter(name);
            if (OpaqueString.isNull(value)) {
                return this;
            }
            ParameterValue parameterValue = new SensitiveParameterValue(parameter, value);
            this.parameterValues.put(parameter, parameterValue);
            return this;
        }

        @Override // oracle.jdbc.driver.resource.ResourceProvider.Builder
        public DriverResourceProvider<T, U> build() throws SQLException {
            return new DriverResourceProvider<>(this);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public Set<ParameterValue> copyParameterValues() throws SQLException {
            Set<ParameterValue> copy = new HashSet<>(this.parameterValues.values());
            for (OracleResourceProvider.Parameter parameter : this.providerParameters.values()) {
                if (!this.parameterValues.containsKey(parameter)) {
                    CharSequence defaultValue = parameter.defaultValue();
                    if (defaultValue != null) {
                        copy.add(ParameterValue.create(parameter, defaultValue));
                    } else if (parameter.isRequired()) {
                        throw DriverResourceProvider.createSQLException(String.format("No value is configured for parameter \"%s\", which is a required parameter of %s", parameter.name(), this.provider.getClass().getName()));
                    }
                }
            }
            return copy;
        }

        private OracleResourceProvider.Parameter getParameter(String name) throws SQLException {
            OracleResourceProvider.Parameter parameter = this.providerParameters.get(name);
            if (parameter == null) {
                throw DriverResourceProvider.createSQLException(String.format("\"%s\" is not a parameter name recognized by %s. Recognized parameter names are: %s", name, this.provider.getClass().getName(), this.providerParameters.keySet()));
            }
            return parameter;
        }

        private void addParameterValue(OracleResourceProvider.Parameter parameter, CharSequence value) {
            if (value == null) {
                return;
            }
            ParameterValue parameterValue = ParameterValue.create(parameter, value);
            this.parameterValues.put(parameter, parameterValue);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/DriverResourceProvider$ParameterValue.class */
    private interface ParameterValue {
        void addClearText(ClearTextParameters.Builder builder);

        static ParameterValue create(OracleResourceProvider.Parameter parameter, CharSequence value) {
            return parameter.isSensitive() ? new SensitiveParameterValue(parameter, value) : new InsensitiveParameterValue(parameter, value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/DriverResourceProvider$InsensitiveParameterValue.class */
    private static final class InsensitiveParameterValue implements ParameterValue {
        private final OracleResourceProvider.Parameter parameter;
        private final CharSequence charSequence;

        private InsensitiveParameterValue(OracleResourceProvider.Parameter parameter, CharSequence charSequence) {
            this.parameter = parameter;
            this.charSequence = charSequence;
        }

        @Override // oracle.jdbc.driver.resource.DriverResourceProvider.ParameterValue
        public void addClearText(ClearTextParameters.Builder builder) {
            builder.addInsensitiveValue(this.parameter, this.charSequence);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/DriverResourceProvider$SensitiveParameterValue.class */
    private static final class SensitiveParameterValue implements ParameterValue {
        private final OracleResourceProvider.Parameter parameter;
        private final OpaqueString opaqueString;

        private SensitiveParameterValue(OracleResourceProvider.Parameter parameter, CharSequence value) {
            this(parameter, OpaqueString.newOpaqueString(value));
        }

        private SensitiveParameterValue(OracleResourceProvider.Parameter parameter, OpaqueString opaqueString) {
            this.parameter = parameter;
            this.opaqueString = opaqueString;
        }

        @Override // oracle.jdbc.driver.resource.DriverResourceProvider.ParameterValue
        public void addClearText(ClearTextParameters.Builder builder) {
            builder.addSensitiveValue(this.parameter, this.opaqueString);
        }
    }
}
