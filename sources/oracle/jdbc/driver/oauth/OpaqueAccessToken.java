package oracle.jdbc.driver.oauth;

import java.time.OffsetDateTime;
import java.util.Objects;
import oracle.jdbc.AccessToken;
import oracle.jdbc.internal.OpaquePrivateKey;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/oauth/OpaqueAccessToken.class */
public class OpaqueAccessToken implements AccessToken {
    private final OpaqueString token;
    private final OpaquePrivateKey privateKey;
    private final OffsetDateTime expiration;

    protected OpaqueAccessToken(@Blind OpaqueString token, OffsetDateTime expiration, @Blind OpaquePrivateKey privateKey) {
        this.token = token;
        this.expiration = expiration;
        this.privateKey = privateKey;
    }

    @Blind
    public static OpaqueAccessToken create(@Blind char[] token, OffsetDateTime expiration) {
        return new OpaqueAccessToken(OpaqueString.newOpaqueString((char[]) token.clone()), expiration, null);
    }

    @Blind
    public final OpaqueString token() {
        return this.token;
    }

    @Blind
    public final OpaquePrivateKey privateKey() {
        return this.privateKey;
    }

    public final OffsetDateTime expiration() {
        return this.expiration;
    }

    @Blind
    public final String toString() {
        return super.toString();
    }

    public boolean equals(Object object) {
        return object == this || ((object instanceof OpaqueAccessToken) && Objects.equals(token(), ((OpaqueAccessToken) object).token()) && Objects.equals(privateKey(), ((OpaqueAccessToken) object).privateKey()));
    }

    public int hashCode() {
        return Objects.hash(token(), this.privateKey);
    }
}
