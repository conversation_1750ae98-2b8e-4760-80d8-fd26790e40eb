package oracle.jdbc.driver.utils;

import java.lang.Throwable;

@FunctionalInterface
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/ThrowingRunnable.class */
public interface ThrowingRunnable<E extends Throwable> extends Runnable {
    void runOrThrow() throws Throwable;

    @Override // java.lang.Runnable
    default void run() {
        try {
            runOrThrow();
        } catch (Throwable throwable) {
            throw CheckedExceptionHandler.toRuntimeException(throwable);
        }
    }
}
