package oracle.jdbc.driver.json;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/Jsonp.class */
public class Jsonp {
    public static final Class<?> JAVAX_JSON_PARSER = forNameNoError("javax.json.stream.JsonParser");
    public static final Class<?> JAKARTA_JSON_PARSER = forNameNoError("jakarta.json.stream.JsonParser");

    public static boolean hasJakarta() {
        return JAKARTA_JSON_PARSER != null;
    }

    public static boolean isJakartaJson(Class<?> c) {
        String pkg = c.getPackage().getName();
        return "jakarta.json".equals(pkg);
    }

    public static boolean isJakartaJsonStream(Class<?> c) {
        String pkg = c.getPackage().getName();
        return "jakarta.json.stream".equals(pkg);
    }

    private static Class<?> forNameNoError(String clazz) {
        try {
            return Class.forName(clazz);
        } catch (ClassNotFoundException e) {
            return null;
        }
    }

    private Jsonp() {
    }
}
