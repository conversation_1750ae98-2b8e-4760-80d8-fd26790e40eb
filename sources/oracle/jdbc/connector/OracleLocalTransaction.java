package oracle.jdbc.connector;

import java.sql.Connection;
import java.sql.SQLException;
import javax.resource.ResourceException;
import javax.resource.spi.EISSystemException;
import javax.resource.spi.IllegalStateException;
import javax.resource.spi.LocalTransaction;
import javax.resource.spi.LocalTransactionException;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/connector/OracleLocalTransaction.class */
public class OracleLocalTransaction implements LocalTransaction {
    private OracleManagedConnection managedConnection;
    private Connection connection;
    boolean isBeginCalled;
    private static final String RAERR_LTXN_COMMIT = "commit without begin";
    private static final String RAERR_LTXN_ROLLBACK = "rollback without begin";

    OracleLocalTransaction(OracleManagedConnection omc) throws ResourceException {
        this.managedConnection = null;
        this.connection = null;
        this.isBeginCalled = false;
        this.managedConnection = omc;
        this.connection = omc.getPhysicalConnection();
        this.isBeginCalled = false;
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.IllegalStateException */
    public void begin() throws SQLException, ResourceException, IllegalStateException, EISSystemException {
        try {
            OracleConnection conn = (OracleConnection) this.connection;
            if (conn.getTxnMode() == 1 || conn.inSessionlessTxnMode()) {
                throw new IllegalStateException("Could not start a new transaction inside an active transaction");
            }
            if (this.connection.getAutoCommit()) {
                this.connection.setAutoCommit(false);
            }
            this.isBeginCalled = true;
            this.managedConnection.eventOccurred(2);
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.LocalTransactionException */
    public void commit() throws SQLException, ResourceException, LocalTransactionException, EISSystemException {
        if (!this.isBeginCalled) {
            throw new LocalTransactionException("begin() must be called before commit()", RAERR_LTXN_COMMIT);
        }
        try {
            this.connection.commit();
            this.isBeginCalled = false;
            this.managedConnection.eventOccurred(3);
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.LocalTransactionException */
    public void rollback() throws SQLException, ResourceException, LocalTransactionException, EISSystemException {
        if (!this.isBeginCalled) {
            throw new LocalTransactionException("begin() must be called before rollback()", RAERR_LTXN_ROLLBACK);
        }
        try {
            this.connection.rollback();
            this.isBeginCalled = false;
            this.managedConnection.eventOccurred(4);
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }
}
