package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.driver.DirectPathBufferMarshaler;
import oracle.jdbc.driver.T4CDirectPathPreparedStatement;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIodpls.class */
class T4CTTIodpls extends T4CTTIfun {
    private static final int DPLSDEF_IN_PARMCOUNT = 1;
    private static final int DPLSDEF_IN_FLAG = 0;
    private static final int DPLSDEF_IN_KLA_PARSE_RESET = 2;
    private static final int STREAM_VERSION = 400;
    private int dplscsr;
    private int dplsbufl;
    private DirectPathBufferMarshaler.BufferPlanner dplsbufPlan;
    private int dplsvrsn;
    private long[] dplsi4;
    private int dplsi4l;
    private long[] dplso4;
    int startErrorOffset;
    int endErrorOffset;

    T4CTTIodpls(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 129);
        clearState();
    }

    private void clearState() {
        this.dplscsr = 0;
        this.dplsbufPlan = null;
        this.dplsbufl = 0;
        this.dplsvrsn = 0;
        this.dplsi4 = new long[1];
        this.dplsi4l = 0;
        this.dplso4 = null;
    }

    void doODPLS(int cursorId, DirectPathBufferMarshaler.BufferPlanner bufferPlanner) throws SQLException, IOException {
        this.dplscsr = cursorId;
        this.dplsvrsn = 400;
        this.dplsbufPlan = bufferPlanner;
        do {
            try {
                this.dplsbufl = this.dplsbufPlan.preparePlan();
                doRPC();
            } catch (T4CDirectPathPreparedStatement.StreamLengthException streamLengthException) {
                throw ((SQLException) DatabaseError.createSqlException(72).initCause(streamLengthException).fillInStackTrace());
            } catch (IOException ioException) {
                throw ((SQLException) DatabaseError.createSqlException(ioException).fillInStackTrace());
            }
        } while (!this.dplsbufPlan.isComplete());
        clearState();
    }

    void setI4Value(int key, long value) {
        this.dplsi4[key] = value;
        this.dplsi4l = Math.max(this.dplsi4l, key + 1);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalSWORD(this.dplscsr);
        if (this.dplsbufl > 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.dplsbufl);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB4(this.dplsvrsn);
        if (this.dplsi4l > 0) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalUB4(this.dplsi4l);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        if (this.dplsbufl > 0) {
            DirectPathBufferMarshaler.marshal(this.dplsbufPlan, this.meg);
        }
        if (this.dplsi4l > 0) {
            for (int i = 0; i < this.dplsi4l; i++) {
                this.meg.marshalUB4(this.dplsi4[i]);
            }
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int dplso4l = this.meg.unmarshalUB2();
        this.dplso4 = new long[dplso4l];
        for (int i = 0; i < dplso4l; i++) {
            this.dplso4[i] = this.meg.unmarshalUB4();
        }
    }

    long getO4Value(int key) {
        if (this.dplso4 == null || key >= this.dplso4.length) {
            return 0L;
        }
        return this.dplso4[key];
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processError() throws SQLException {
        try {
            super.processError();
        } catch (SQLException sqe) {
            byte[] oerepa = this.connection.getT4CTTIoer().oerepa;
            if (oerepa != null) {
                int position = getErrorOffset(oerepa, 0);
                getErrorOffset(oerepa, position);
            }
            throw sqe;
        }
    }

    private int getErrorOffset(byte[] oerepa, int position) {
        int position2 = position + 1;
        int len = oerepa[position] & 255;
        int errorOffset = 0;
        if (len + position <= oerepa.length) {
            if (len == 0) {
                errorOffset = 0;
            } else if (len == 1) {
                position2++;
                errorOffset = oerepa[position2] & 255;
            } else if (len == 2) {
                int position3 = position2 + 1;
                int i = (oerepa[position2] & 255) << 8;
                position2 = position3 + 1;
                errorOffset = i | (oerepa[position3] & 255);
            } else if (len == 4) {
                int position4 = position2 + 1;
                int i2 = (oerepa[position2] & 255) << 24;
                int position5 = position4 + 1;
                int i3 = i2 | ((oerepa[position4] & 255) << 16);
                int position6 = position5 + 1;
                int i4 = i3 | ((oerepa[position5] & 255) << 8);
                position2 = position6 + 1;
                errorOffset = i4 | (oerepa[position6] & 255);
            }
        }
        if (position == 0) {
            this.startErrorOffset = errorOffset;
        } else {
            this.endErrorOffset = errorOffset;
        }
        return position2;
    }
}
