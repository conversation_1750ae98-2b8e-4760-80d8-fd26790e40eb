package oracle.jdbc.driver;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.concurrent.CompletionStage;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C7Oversion.class */
final class T4C7Oversion extends T4CTTIfun {
    private static final String CLASS_NAME = T4C7Oversion.class.getName();
    static final long VSN_BANNER_FORMAT_BASE = 0;
    static final long VSN_BANNER_FORMAT_FULL = 1;
    byte[] rdbmsVersion;
    private final boolean rdbmsVersionO2U = true;
    private final int bufLen = 256;
    private final boolean retVerLenO2U = true;
    int retVerLen;
    private final boolean retVerNumO2U = true;
    long retVerNum;

    T4C7Oversion(T4CConnection _connection) {
        super(_connection, (byte) 3);
        this.rdbmsVersion = new byte[]{78, 111, 116, 32, 100, 101, 116, 101, 114, 109, 105, 110, 101, 100, 32, 121, 101, 116};
        this.rdbmsVersionO2U = true;
        this.bufLen = 256;
        this.retVerLenO2U = true;
        this.retVerLen = 0;
        this.retVerNumO2U = true;
        this.retVerNum = VSN_BANNER_FORMAT_BASE;
        setFunCode((short) 59);
    }

    void doOVERSION() throws SQLException, IOException {
        begin(Metrics.ConnectionEvent.OVERSION);
        doRPC();
        end(Metrics.ConnectionEvent.OVERSION);
    }

    final CompletionStage<Void> doOVERSIONAsync() {
        return doRPCAsync();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.retVerLen = this.meg.unmarshalUB2();
        this.rdbmsVersion = this.meg.unmarshalCHR(this.retVerLen);
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "readRPA", "rdbms version num={0}", (String) null, (String) null, (Object) new String(this.rdbmsVersion, StandardCharsets.US_ASCII));
        this.retVerNum = this.meg.unmarshalUB4();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processRPA() throws SQLException {
        if (this.rdbmsVersion == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0210).fillInStackTrace());
        }
    }

    byte[] getVersion() {
        return this.rdbmsVersion;
    }

    protected static final int serverReleaseRel(long v) {
        return (int) ((v >>> 24) & 255);
    }

    protected static final int serverReleaseRelUpd(long v) {
        int ret;
        if (serverReleaseRel(v) < 18) {
            ret = (int) ((v >>> 20) & 15);
        } else {
            ret = (int) ((v >>> 16) & 255);
        }
        return ret;
    }

    private static final int serverReleaseRelUpdRev(long v) {
        int ret;
        if (serverReleaseRel(v) < 18) {
            ret = (int) ((v >>> 12) & 255);
        } else {
            ret = (int) ((v >>> 12) & 15);
        }
        return ret;
    }

    private static final int serverReleaseRelUpdInc(long v) {
        int ret;
        if (serverReleaseRel(v) < 18) {
            ret = (int) ((v >>> 8) & 15);
        } else {
            ret = (int) ((v >>> 4) & 255);
        }
        return ret;
    }

    short getVersionNumber() {
        int ver = 0 + (serverReleaseRel(this.retVerNum) * 1000);
        if (ver < 18000) {
            ver += serverReleaseRelUpd(this.retVerNum) * 100;
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getVersionNumber", "version num = {0}", (String) null, (String) null, (Object) Integer.valueOf(ver));
        return (short) ver;
    }

    long getVersionNumberasIs() {
        return this.retVerNum;
    }

    int getMajorVersionNumber() {
        return serverReleaseRel(this.retVerNum);
    }

    int getMinorVersionNumber() {
        return serverReleaseRelUpd(this.retVerNum);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalO2U(true);
        this.meg.marshalSWORD(256);
        this.meg.marshalO2U(true);
        this.meg.marshalO2U(true);
        if (this.connection.getTTCVersion() >= 11) {
            this.meg.marshalUB4(1L);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
