package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoer19.class */
class T4CTTIoer19 extends T4CTTIoer11 {
    long oercn2;
    long oerrcd2;
    public static final boolean TRACE = false;
    private static final String CLASS_NAME = T4CTTIoer19.class.getName();
    private static final String _Copyright_2014_Oracle_All_Rights_Reserved_ = null;

    T4CTTIoer19(T4CConnection conn) {
        super(conn);
    }

    @Override // oracle.jdbc.driver.T4CTTIoer11
    void init() {
        super.init();
        this.oerrcd2 = 0L;
        this.oercn2 = 0L;
    }

    @Override // oracle.jdbc.driver.T4CTTIoer11
    int unmarshal() throws SQLException, IOException {
        return unmarshal(false);
    }

    @Override // oracle.jdbc.driver.T4CTTIoer11
    int unmarshal(boolean ignoreORA1403) throws SQLException, IOException {
        unmarshalAttributes();
        if (this.oerrcd2 != 0) {
            if (this.oerrcd2 == 1403 && ignoreORA1403) {
                unmarshalErrorMessageAndIgnore();
            } else {
                unmarshalErrorMessage();
            }
        }
        return this.currCursorID;
    }

    @Override // oracle.jdbc.driver.T4CTTIoer11
    void unmarshalAttributes() throws SQLException, IOException {
        super.unmarshalAttributes();
        this.oerrcd2 = this.meg.unmarshalUB4();
        this.oercn2 = this.meg.unmarshalSB8();
    }

    @Override // oracle.jdbc.driver.T4CTTIoer11
    void print() throws SQLException {
        super.print();
        if (this.oerrcd2 != 0) {
            debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "print", "**** Error Message: {0}", (String) null, (Throwable) null, () -> {
                try {
                    return new Object[]{this.meg.conv.CharBytesToString(this.errorMsg, this.errorLength[0], true)};
                } catch (SQLException e) {
                    return new Object[]{"Got an exception generating the error message: " + e.getMessage()};
                }
            });
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIoer11
    long getCurRowNumber() throws SQLException {
        return this.oercn2;
    }

    @Override // oracle.jdbc.driver.T4CTTIoer11
    long getRetCode() {
        return this.oerrcd2;
    }

    @Override // oracle.jdbc.driver.T4CTTIoer11
    long updateChecksum(long localCheckSum) throws SQLException {
        return CRC64.updateChecksum(CRC64.updateChecksum(super.updateChecksum(localCheckSum), this.oerrcd2), this.oercn2);
    }
}
