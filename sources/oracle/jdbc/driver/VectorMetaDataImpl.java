package oracle.jdbc.driver;

import oracle.jdbc.OracleType;
import oracle.jdbc.VectorMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorMetaDataImpl.class */
final class VectorMetaDataImpl implements VectorMetaData {
    static final VectorMetaData UNKNOWN = new VectorMetaDataImpl(-1, OracleType.VECTOR, double[].class, false);
    private final int length;
    private final OracleType type;
    private final Class<?> arrayClass;
    private final boolean isSparse;

    private VectorMetaDataImpl(int length, OracleType type, Class<?> arrayClass, boolean isSparse) {
        this.length = length;
        this.type = type;
        this.arrayClass = arrayClass;
        this.isSparse = isSparse;
    }

    @Override // oracle.jdbc.VectorMetaData
    public int length() {
        return this.length;
    }

    @Override // oracle.jdbc.VectorMetaData
    public OracleType type() {
        return this.type;
    }

    @Override // oracle.jdbc.VectorMetaData
    public Class<?> arrayClass() {
        return this.arrayClass;
    }

    @Override // oracle.jdbc.VectorMetaData
    public boolean isSparse() {
        return this.isSparse;
    }

    static VectorMetaDataImpl create(int length, int typeCode, boolean isSparse) {
        switch (typeCode) {
            case oracle.jdbc.OracleTypes.VECTOR_BINARY /* -109 */:
                return new VectorMetaDataImpl(length, OracleType.VECTOR_BINARY, byte[].class, isSparse);
            case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                return new VectorMetaDataImpl(length, OracleType.VECTOR_FLOAT64, double[].class, isSparse);
            case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                return new VectorMetaDataImpl(length, OracleType.VECTOR_FLOAT32, float[].class, isSparse);
            case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                return new VectorMetaDataImpl(length, OracleType.VECTOR_INT8, byte[].class, isSparse);
            case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                return new VectorMetaDataImpl(length, OracleType.VECTOR, double[].class, isSparse);
            default:
                throw new IllegalStateException("Not a VECTOR: " + typeCode);
        }
    }
}
