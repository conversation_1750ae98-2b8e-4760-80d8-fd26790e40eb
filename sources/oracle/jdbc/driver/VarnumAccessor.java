package oracle.jdbc.driver;

import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VarnumAccessor.class */
class VarnumAccessor extends NumberCommonAccessor {
    static final int MAXLENGTH = 21;

    VarnumAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        super(stmt, 21, isStoredInBindData);
        init(stmt, max_len, form, external_type, isOutBind);
    }

    VarnumAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(stmt, 21, false);
        init(stmt, 6, max_len, nullable, flags, precision, scale, contflag, total_elems, form);
    }
}
