package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_ko.class */
public class ErrorMessagesJson_ko extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "I/O 예외사항이 발생했습니다."}, new Object[]{"26302", "\"{0}\" 연도는 지원되지 않습니다."}, new Object[]{"26303", "오버플로우, 값이 너무 큼: {0}."}, new Object[]{"26304", "지원되지 않는 옵션(구현되지 않음)."}, new Object[]{"26305", "바이너리 JSON이 부적합하거나 손상되었습니다."}, new Object[]{"26306", "지원되지 않는 바이너리 JSON 버전: {0}."}, new Object[]{"26307", "UTF-8 인코딩된 키 길이는 256바이트보다 크지 않아야 합니다. 다음 키가 이 제한을 초과함: \"{0}\"."}, new Object[]{"26308", "지정된 JSON이 너무 커서 바이너리 JSON으로 인코딩할 수 없습니다. 인코딩된 이미지 크기는 2GB를 초과하지 않아야 합니다."}, new Object[]{"26309", "바이너리 JSON이 부적합하거나 손상되었습니다. 지정된 이미지에는 {0}바이트만 포함됩니다."}, new Object[]{"26310", "지정된 java.time.Period에 일이 설정되었지만 오라클 연도-월 간격에서 일을 지원하지 않습니다."}, new Object[]{"26311", "생성기가 종료 전에 닫혔습니다."}, new Object[]{"26312", "이 컨텍스트에서는 객체 키가 지정되어야 합니다."}, new Object[]{"26313", "쓰기가 부적합합니다. 전체 값이 이미 쓰여졌습니다."}, new Object[]{"26314", "이 컨텍스트에서는 종료가 허용되지 않습니다."}, new Object[]{"26315", "이 컨텍스트에서는 키가 허용되지 않습니다."}, new Object[]{"26316", "키 뒤에 값이 필요합니다."}, new Object[]{"26317", "구문분석기는 {0} 상태여야 합니다."}, new Object[]{"26318", "구문분석기는 {0} 상태가 아니어야 합니다."}, new Object[]{"26319", "구문분석기는 값에 있어야 합니다."}, new Object[]{"26320", "\"{0}\"은(는) 지원되는 래퍼 유형이 아닙니다."}, new Object[]{"26321", "이 객체를 수정할 수 없습니다. 수정 가능한 복사본을 만들려면 OracleJsonFactory.createObject(OracleJsonObject)를 사용하십시오."}, new Object[]{"26322", "이 배열을 수정할 수 없습니다. 수정 가능한 복사본을 만들려면 OracleJsonFactory.createArray(OracleJsonArray)를 사용하십시오."}, new Object[]{"26323", "JSON 객체에 중복 키 있음: {0}."}, new Object[]{"26324", "인코딩을 자동 감지할 수 없음, 문자 수가 부족함."}, new Object[]{"26325", "EOF 토큰이 필요하지만 {0}을(를) 받았습니다."}, new Object[]{"26326", "예상치 않은 문자 {0}이(가) {1}행 {2}열에 있습니다."}, new Object[]{"26327", "예상치 않은 문자 {0}이(가) {1}행 {2}열에 있습니다. 필요한 값: {3}."}, new Object[]{"26328", "부적합한 토큰 {0}이(가) {1}행, {2}열에 있습니다. 필요한 토큰: {3}."}, new Object[]{"26329", "JsonParser#getString()은 KEY_NAME, VALUE_STRING, VALUE_NUMBER 구문분석기 상태만 적합합니다. 현재 구문분석기 상태는 {0}입니다."}, new Object[]{"26330", "JsonParser#isIntegralNumber()은 VALUE_NUMBER 구문분석기 상태만 적합합니다. 현재 구문분석기 상태는 {0}입니다."}, new Object[]{"26331", "JsonParser#getInt()은 VALUE_NUMBER 구문분석기 상태만 적합합니다. 현재 구문분석기 상태는 {0}입니다."}, new Object[]{"26332", "JsonParser#getLong()은 VALUE_NUMBER 구문분석기 상태만 적합합니다. 현재 구문분석기 상태는 {0}입니다."}, new Object[]{"26333", "JsonParser#getBigDecimal()은 VALUE_NUMBER 구문분석기 상태만 적합합니다. 현재 구문분석기 상태는 {0}입니다."}, new Object[]{"26334", "JsonParser#getArray()는 START_ARRAY 구문분석기 상태에만 적합합니다. 현재 구문분석기 상태는 {0}입니다."}, new Object[]{"26335", "JsonParser#getObject()은 START_OBJECT 구문분석기 상태에만 적합합니다. 현재 구문분석기 상태는 {0}입니다."}, new Object[]{"26336", "지역이 있는 시간기록은 지원되지 않습니다. 오프셋 시간대만 지원됩니다."}, new Object[]{"26337", "JSON 값의 객체 및 배열이 {0} 레벨 이상 중첩되지 않을 수 있습니다."}, new Object[]{"26338", "JSON 객체의 키는 65,535바이트를 초과할 수 없습니다."}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
