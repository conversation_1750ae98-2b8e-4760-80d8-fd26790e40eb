package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_nl.class */
public class ErrorMessagesJson_nl extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Er is een I/O-uitzondering opgetreden."}, new Object[]{"26302", "Het jaar \"{0}\" wordt niet ondersteund."}, new Object[]{"26303", "Overloop, waarde te groot: {0}."}, new Object[]{"26304", "Niet-ondersteunde optie (niet geïmplementeerd)."}, new Object[]{"26305", "Binaire JSON is ongeldig of beschadigd."}, new Object[]{"26306", "Niet-ondersteunde versie van binaire JSON: {0}."}, new Object[]{"26307", "De met UTF-8 gecodeerde sleutel mag niet langer zijn dan 256 bytes. De volgende sleutel overschrijdt deze limiet: \"{0}\"."}, new Object[]{"26308", "De opgegeven JSON is te groot om als binaire JSON te worden gecodeerd. De gecodeerde afbeeldingen mogen niet groter zijn dan 2 GB."}, new Object[]{"26309", "Binaire JSON is ongeldig of beschadigd. Opgegeven afbeelding bevat slechts {0} bytes."}, new Object[]{"26310", "Voor de opgegeven java.time.Period zijn dagen ingesteld, maar het jaar-naar-maand-interval van Oracle ondersteunt geen dagen."}, new Object[]{"26311", "Generator gesloten voor einde."}, new Object[]{"26312", "In deze context moet een objectsleutel worden opgegeven."}, new Object[]{"26313", "Ongeldige schrijfbewerking. Er is al een waarde 'Voltooid' geschreven."}, new Object[]{"26314", "Einde is niet toegestaan in deze context."}, new Object[]{"26315", "Sleutel is niet toegestaan in deze context."}, new Object[]{"26316", "Er wordt een waarde verwacht na de sleutel."}, new Object[]{"26317", "Parserstatus moet {0} zijn."}, new Object[]{"26318", "Parserstatus mag niet {0} zijn."}, new Object[]{"26319", "Parser moet betrekking hebben op een waarde."}, new Object[]{"26320", "\"{0}\" is geen ondersteund wrappertype."}, new Object[]{"26321", "Dit object kan niet worden gewijzigd. Als u een kopie wilt maken die te wijzigen is, gebruikt u OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Deze array kan niet worden gewijzigd. Als u een kopie wilt maken die te wijzigen is, gebruikt u OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "JSON-object bevat dubbele sleutel: {0}."}, new Object[]{"26324", "Kan codering niet automatisch detecteren, onvoldoende tekens."}, new Object[]{"26325", "EOF-token verwacht, maar {0} ontvangen."}, new Object[]{"26326", "Onverwacht teken {0} op regel {1}, kolom {2}."}, new Object[]{"26327", "Onverwacht teken {0} op regel {1}, kolom {2}. {3} werd verwacht."}, new Object[]{"26328", "Ongeldig token {0} op regel {1}, kolom {2}. Verwachte tokens zijn: {3}."}, new Object[]{"26329", "JsonParser#getString() is alleen geldig voor parserstatuswaarden KEY_NAME, VALUE_STRING, VALUE_NUMBER, maar de huidige parserstatus is {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() is alleen geldig voor parserstatus VALUE_NUMBER, maar de huidige parserstatus is {0}."}, new Object[]{"26331", "JsonParser#getInt() is alleen geldig voor parserstatus VALUE_NUMBER, maar de huidige parserstatus is {0}."}, new Object[]{"26332", "JsonParser#getLong() is alleen geldig voor parserstatus VALUE_NUMBER, maar de huidige parserstatus is {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() is alleen geldig voor parserstatus VALUE_NUMBER, maar de huidige parserstatus is {0}."}, new Object[]{"26334", "JsonParser#getArray() is alleen geldig voor parserstatus START_ARRAY, maar de huidige parserstatus is {0}."}, new Object[]{"26335", "JsonParser#getObject() is alleen geldig voor parserstatus START_OBJECT, maar de huidige parserstatus is {0}."}, new Object[]{"26336", "Een tijdstempel met een regio wordt niet ondersteund. Alleen offsettijdzones worden ondersteund."}, new Object[]{"26337", "De objecten en arrays in de JSON-waarde mogen niet dieper genest zijn dan {0} niveaus."}, new Object[]{"26338", "De sleutels van een JSON-object mogen niet groter zijn dan 65.535 bytes."}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
