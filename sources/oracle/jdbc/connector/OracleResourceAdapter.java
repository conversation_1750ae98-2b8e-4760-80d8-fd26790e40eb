package oracle.jdbc.connector;

import javax.resource.NotSupportedException;
import javax.resource.ResourceException;
import javax.resource.spi.ActivationSpec;
import javax.resource.spi.BootstrapContext;
import javax.resource.spi.ResourceAdapter;
import javax.resource.spi.ResourceAdapterInternalException;
import javax.resource.spi.endpoint.MessageEndpointFactory;
import javax.transaction.xa.XAResource;

/* loaded from: ojdbc8.jar:oracle/jdbc/connector/OracleResourceAdapter.class */
public class OracleResourceAdapter implements ResourceAdapter {
    public void start(BootstrapContext bootstrapContext) throws ResourceAdapterInternalException {
    }

    public void stop() {
    }

    public void endpointActivation(MessageEndpointFactory mesgFactory, ActivationSpec activationSpec) throws NotSupportedException {
    }

    public void endpointDeactivation(MessageEndpointFactory mesgFactory, ActivationSpec activationSpec) {
    }

    public XAResource[] getXAResources(ActivationSpec[] activationSpecs) throws ResourceException {
        return new XAResource[0];
    }
}
