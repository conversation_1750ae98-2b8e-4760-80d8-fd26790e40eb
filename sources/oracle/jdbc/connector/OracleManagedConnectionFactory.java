package oracle.jdbc.connector;

import java.io.PrintWriter;
import java.sql.SQLException;
import java.util.Properties;
import java.util.Set;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.resource.ResourceException;
import javax.resource.spi.ConnectionManager;
import javax.resource.spi.ConnectionRequestInfo;
import javax.resource.spi.EISSystemException;
import javax.resource.spi.ManagedConnection;
import javax.resource.spi.ManagedConnectionFactory;
import javax.resource.spi.ResourceAdapterInternalException;
import javax.resource.spi.SecurityException;
import javax.resource.spi.security.PasswordCredential;
import javax.security.auth.Subject;
import javax.sql.XAConnection;
import javax.sql.XADataSource;

/* loaded from: ojdbc8.jar:oracle/jdbc/connector/OracleManagedConnectionFactory.class */
public class OracleManagedConnectionFactory implements ManagedConnectionFactory {
    private XADataSource xaDataSource;
    private String xaDataSourceName;
    private static final String RAERR_MCF_SET_XADS = "invalid xads";
    private static final String RAERR_MCF_GET_PCRED = "no password credential";

    public OracleManagedConnectionFactory() throws ResourceException {
        this.xaDataSource = null;
        this.xaDataSourceName = null;
    }

    public OracleManagedConnectionFactory(XADataSource xads) throws ResourceException {
        this.xaDataSource = null;
        this.xaDataSourceName = null;
        this.xaDataSource = xads;
        this.xaDataSourceName = "XADataSource";
    }

    public void setXADataSourceName(String xadsName) {
        this.xaDataSourceName = xadsName;
    }

    public String getXADataSourceName() {
        return this.xaDataSourceName;
    }

    public Object createConnectionFactory(ConnectionManager cxManager) throws ResourceAdapterInternalException, ResourceException {
        if (this.xaDataSource == null) {
            setupXADataSource();
        }
        return this.xaDataSource;
    }

    public Object createConnectionFactory() throws ResourceException {
        return createConnectionFactory(null);
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    public ManagedConnection createManagedConnection(Subject subject, ConnectionRequestInfo cxRequestInfo) throws ResourceAdapterInternalException, ResourceException, SecurityException, EISSystemException {
        XAConnection xaconn;
        try {
            if (this.xaDataSource == null) {
                setupXADataSource();
            }
            PasswordCredential pcred = getPasswordCredential(subject, cxRequestInfo);
            if (pcred == null) {
                xaconn = this.xaDataSource.getXAConnection();
            } else {
                xaconn = this.xaDataSource.getXAConnection(pcred.getUserName(), new String(pcred.getPassword()));
            }
            OracleManagedConnection omc = new OracleManagedConnection(xaconn);
            omc.setPasswordCredential(pcred);
            omc.setLogWriter(getLogWriter());
            return omc;
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    public ManagedConnection matchManagedConnections(Set connectionSet, Subject subject, ConnectionRequestInfo cxRequestInfo) throws ResourceException, SecurityException {
        PasswordCredential pcred = getPasswordCredential(subject, cxRequestInfo);
        for (Object obj : connectionSet) {
            if (obj instanceof OracleManagedConnection) {
                OracleManagedConnection omc = (OracleManagedConnection) obj;
                if (omc.getPasswordCredential().equals(pcred)) {
                    return omc;
                }
            }
        }
        return null;
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    public void setLogWriter(PrintWriter out) throws ResourceAdapterInternalException, ResourceException, EISSystemException {
        try {
            if (this.xaDataSource == null) {
                setupXADataSource();
            }
            this.xaDataSource.setLogWriter(out);
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.EISSystemException */
    public PrintWriter getLogWriter() throws ResourceAdapterInternalException, ResourceException, EISSystemException {
        try {
            if (this.xaDataSource == null) {
                setupXADataSource();
            }
            return this.xaDataSource.getLogWriter();
        } catch (SQLException exc) {
            throw new EISSystemException("SQLException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.ResourceException */
    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.ResourceAdapterInternalException */
    private void setupXADataSource() throws ResourceAdapterInternalException, ResourceException {
        InitialContext initialContext = null;
        try {
            try {
                Properties props = System.getProperties();
                initialContext = new InitialContext(props);
            } catch (SecurityException e) {
            }
            if (initialContext == null) {
                initialContext = new InitialContext();
            }
            XADataSource xads = (XADataSource) initialContext.lookup(this.xaDataSourceName);
            if (xads == null) {
                throw new ResourceAdapterInternalException("Invalid XADataSource object");
            }
            this.xaDataSource = xads;
        } catch (NamingException exc) {
            throw new ResourceException("NamingException: " + exc.getMessage(), exc);
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.resource.spi.SecurityException */
    private PasswordCredential getPasswordCredential(Subject subject, ConnectionRequestInfo cxRequestInfo) throws ResourceException, SecurityException {
        if (subject != null) {
            Set<PasswordCredential> creds = subject.getPrivateCredentials(PasswordCredential.class);
            for (PasswordCredential pcred : creds) {
                if (pcred.getManagedConnectionFactory().equals(this)) {
                    return pcred;
                }
            }
            throw new SecurityException("Can not find user/password information", RAERR_MCF_GET_PCRED);
        }
        if (cxRequestInfo == null) {
            return null;
        }
        OracleConnectionRequestInfo info = (OracleConnectionRequestInfo) cxRequestInfo;
        PasswordCredential pcred2 = new PasswordCredential(info.getUser(), info.getPassword().toCharArray());
        pcred2.setManagedConnectionFactory(this);
        return pcred2;
    }
}
