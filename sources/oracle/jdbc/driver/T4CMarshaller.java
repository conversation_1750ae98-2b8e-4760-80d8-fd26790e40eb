package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.net.ns.BreakNetException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CMarshaller.class */
abstract class T4CMarshaller {
    private static final T4CMarshaller BASIC = new BasicMarshaller();
    private static final T4CMarshaller STREAM = new StreamMarshaller();
    static final T4CMarshaller CHAR = BASIC;
    static final T4CMarshaller LONG_RAW = STREAM;
    static final T4CMarshaller RAW = BASIC;
    static final T4CMarshaller VARCHAR = BASIC;
    static final T4CMarshaller LONG = STREAM;

    abstract boolean unmarshalOneRow(Accessor accessor) throws SQLException, IOException;

    abstract int readStreamFromWire(byte[] bArr, int i, int i2, int[] iArr, boolean[] zArr, boolean[] zArr2, T4CMAREngine t4CMAREngine, T4CTTIoer11 t4CTTIoer11) throws SQLException, IOException;

    private T4CMarshaller() {
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CMarshaller$BasicMarshaller.class */
    private static final class BasicMarshaller extends T4CMarshaller {
        private BasicMarshaller() {
            super();
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // oracle.jdbc.driver.T4CMarshaller
        boolean unmarshalOneRow(Accessor accessor) throws SQLException, IOException {
            T4CAccessor t4cAcc = (T4CAccessor) accessor;
            boolean isStream = false;
            if (!accessor.isUseless()) {
                if (accessor.isUnexpected()) {
                    long pos = accessor.rowData.getPosition();
                    t4cAcc.unmarshalColumnMetadata();
                    unmarshalBytes(accessor);
                    accessor.rowData.setPosition(pos);
                    accessor.setNull(accessor.lastRowProcessed, true);
                } else if (accessor.isNullByDescribe()) {
                    accessor.setNull(accessor.lastRowProcessed, true);
                    t4cAcc.unmarshalColumnMetadata();
                    if (accessor.statement.connection.versionNumber < 9200) {
                        t4cAcc.processIndicator(0);
                    }
                } else {
                    t4cAcc.unmarshalColumnMetadata();
                    isStream = unmarshalBytes(accessor);
                }
            }
            accessor.previousRowProcessed = accessor.lastRowProcessed;
            accessor.lastRowProcessed++;
            return isStream;
        }

        /* JADX WARN: Multi-variable type inference failed */
        private boolean unmarshalBytes(Accessor accessor) throws SQLException, IOException {
            int len;
            T4CAccessor t4cAcc = (T4CAccessor) accessor;
            T4CMAREngine mare = t4cAcc.getMAREngine();
            accessor.setOffset(accessor.lastRowProcessed);
            if (accessor.statement.maxFieldSize > 0) {
                len = ((DynamicByteArray) accessor.rowData).unmarshalCLR(mare, accessor.statement.maxFieldSize);
            } else {
                len = ((DynamicByteArray) accessor.rowData).unmarshalCLR(mare);
            }
            t4cAcc.processIndicator(len);
            accessor.setLength(accessor.lastRowProcessed, len);
            accessor.setNull(accessor.lastRowProcessed, len == 0);
            return false;
        }

        @Override // oracle.jdbc.driver.T4CMarshaller
        int readStreamFromWire(byte[] buffer, int offset, int length, int[] escapeSequenceArr, boolean[] readHeaderArr, boolean[] readAsNonStreamArr, T4CMAREngine mare, T4CTTIoer11 oer) throws SQLException, IOException {
            return -1;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CMarshaller$StreamMarshaller.class */
    private static final class StreamMarshaller extends T4CMarshaller {
        private StreamMarshaller() {
            super();
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // oracle.jdbc.driver.T4CMarshaller
        boolean unmarshalOneRow(Accessor accessor) throws SQLException, IOException {
            T4CAccessor t4cAcc = (T4CAccessor) accessor;
            T4CMAREngine mare = t4cAcc.getMAREngine();
            if (accessor.isUseless()) {
                accessor.lastRowProcessed++;
                return false;
            }
            boolean isStream = false;
            accessor.escapeSequenceArr[0] = mare.unmarshalUB1();
            if (mare.escapeSequenceNull(accessor.escapeSequenceArr[0])) {
                accessor.setNull(accessor.lastRowProcessed, true);
                mare.processIndicator(false, 0);
                accessor.escapeSequenceArr[0] = 0;
                accessor.previousRowProcessed = accessor.lastRowProcessed;
                accessor.lastRowProcessed++;
            } else {
                accessor.setNull(accessor.lastRowProcessed, false);
                accessor.readHeaderArr[0] = true;
                accessor.readAsNonStreamArr[0] = false;
                if (accessor.statement.isFetchStreams || accessor.definedColumnType == -2 || accessor.definedColumnType == 12 || accessor.definedColumnType == 1) {
                    int nbBytesReadTemp = 0;
                    int nbBytesRead = 0;
                    byte[] buf = accessor.statement.connection.getByteBuffer(32768);
                    accessor.setOffset(accessor.lastRowProcessed);
                    while (nbBytesReadTemp != -1) {
                        nbBytesReadTemp = readStreamFromWire(buf, 0, 32768, accessor.escapeSequenceArr, accessor.readHeaderArr, accessor.readAsNonStreamArr, mare, ((T4CConnection) accessor.statement.connection).oer);
                        if (nbBytesReadTemp != -1) {
                            if (accessor.statement.connection.checksumMode.needToCalculateFetchChecksum()) {
                                long localCheckSum = CRC64.updateChecksum(accessor.statement.checkSum, buf, 0, nbBytesReadTemp);
                                accessor.statement.checkSum = localCheckSum;
                            }
                            accessor.rowData.put(buf, 0, nbBytesReadTemp);
                            nbBytesRead += nbBytesReadTemp;
                        }
                    }
                    accessor.setLength(accessor.lastRowProcessed, nbBytesRead);
                    accessor.previousRowProcessed = accessor.lastRowProcessed;
                    accessor.lastRowProcessed++;
                    accessor.isStream = false;
                    accessor.statement.connection.cacheBuffer(buf);
                } else {
                    isStream = true;
                }
            }
            return isStream;
        }

        @Override // oracle.jdbc.driver.T4CMarshaller
        int readStreamFromWire(byte[] buffer, int offset, int length, int[] escapeSequenceArr, boolean[] readHeaderArr, boolean[] readAsNonStreamArr, T4CMAREngine mare, T4CTTIoer11 oer) throws SQLException, IOException {
            int bytesToRead = -1;
            try {
                if (!readAsNonStreamArr[0]) {
                    if (length > 32768 || length < 0) {
                        throw ((SQLException) DatabaseError.createSqlException(DatabaseError.TTC0205).fillInStackTrace());
                    }
                    if (readHeaderArr[0]) {
                        if (escapeSequenceArr[0] == 254) {
                            if (mare.useCLRBigChunks) {
                                bytesToRead = mare.unmarshalSB4();
                            } else {
                                bytesToRead = mare.unmarshalUB1();
                            }
                        } else {
                            if (escapeSequenceArr[0] == 0) {
                                oer.connection.internalClose();
                                throw ((SQLException) DatabaseError.createSqlException(401).fillInStackTrace());
                            }
                            readAsNonStreamArr[0] = true;
                            bytesToRead = escapeSequenceArr[0];
                        }
                        readHeaderArr[0] = false;
                        escapeSequenceArr[0] = 0;
                    } else if (mare.useCLRBigChunks) {
                        bytesToRead = mare.unmarshalSB4();
                    } else {
                        bytesToRead = mare.unmarshalUB1();
                    }
                } else {
                    readAsNonStreamArr[0] = false;
                }
                if (bytesToRead > 0) {
                    mare.unmarshalNBytes(buffer, offset, bytesToRead);
                } else {
                    bytesToRead = -1;
                }
                if (bytesToRead == -1) {
                    readHeaderArr[0] = true;
                    mare.unmarshalUB2();
                    mare.unmarshalUB2();
                }
                return bytesToRead;
            } catch (BreakNetException e) {
                return -1;
            }
        }
    }
}
