package oracle.jdbc.driver;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrreq.class */
class T4CTTIkpdnrreq {
    byte[] clientIdBytes;
    short registrationOpCode;
    boolean acknowledgement = false;
    T4CTTIkpdnrack notificationAcknowledgment;
    T4CMAREngine mar;

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrreq$OpCode.class */
    enum OpCode {
        INIT_KPDNRREQ(1),
        COMMIT_KPDNRREQ(2),
        ROLLBACK_KPDNRREQ(3),
        REINIT_KPDNRREQ(4);

        private final short mode;

        OpCode(short _mode) {
            this.mode = _mode;
        }

        public final short getCode() {
            return this.mode;
        }
    }

    T4CTTIkpdnrreq(T4CConnection connection) {
        this.mar = connection.mare;
    }

    void send(byte[] clientId, OpCode opCode) throws IOException {
        this.clientIdBytes = clientId;
        this.registrationOpCode = opCode.getCode();
        marshal();
    }

    void marshal() throws IOException {
        if (this.clientIdBytes != null && this.clientIdBytes.length != 0) {
            this.mar.marshalUB2(this.clientIdBytes.length);
            this.mar.marshalCLR(this.clientIdBytes, 0, this.clientIdBytes.length);
        } else {
            this.mar.marshalSWORD(0);
        }
        this.mar.marshalUB1(this.registrationOpCode);
        if (this.acknowledgement) {
            this.notificationAcknowledgment.send(null, 0L, null);
        } else {
            this.mar.marshalSWORD(0);
        }
    }
}
