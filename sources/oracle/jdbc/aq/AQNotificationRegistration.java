package oracle.jdbc.aq;

import java.sql.SQLException;
import java.util.concurrent.Executor;
import oracle.jdbc.NotificationRegistration;

/* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQNotificationRegistration.class */
public interface AQNotificationRegistration extends NotificationRegistration {
    void addListener(AQNotificationListener aQNotificationListener) throws SQLException;

    void addListener(AQNotificationListener aQNotificationListener, Executor executor) throws SQLException;

    void removeListener(AQNotificationListener aQNotificationListener) throws SQLException;

    String getQueueName();
}
