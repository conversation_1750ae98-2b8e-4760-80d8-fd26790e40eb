package oracle.jdbc.driver;

import java.sql.SQLException;
import oracle.sql.Datum;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/SetCHARBinder.class */
class SetCHARBinder extends Binder {
    byte[] paramVal;
    Binder theSetCHARCopyingBinder = null;

    static void init(Binder x) {
        x.type = (short) 996;
        x.bytelen = 0;
    }

    SetCHARBinder(byte[] val) {
        init(this);
        this.paramVal = val;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theSetCHARCopyingBinder == null) {
            this.theSetCHARCopyingBinder = new SetCHARCopyingBinder();
        }
        return this.theSetCHARCopyingBinder;
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        byte[] value = this.paramVal;
        if (clearPriorBindValues) {
            this.paramVal = null;
        }
        if (value == null) {
            bindIndicators[indoffset] = -1;
            if (bindUseDBA) {
                bindDataOffsets[bindDataIndex] = -1;
                bindDataLengths[bindDataIndex] = 0;
            }
        } else {
            bindIndicators[indoffset] = 0;
            int len = value.length;
            if (len > 65532) {
                bindIndicators[lenoffset] = -2;
            } else {
                bindIndicators[lenoffset] = (short) (len + 2);
            }
            if (bindUseDBA) {
                long pos = bindData.getPosition();
                bindDataOffsets[bindDataIndex] = pos;
                stmt.lastBoundDataOffsets[bindPosition] = pos;
                bindDataLengths[bindDataIndex] = len;
                stmt.lastBoundDataLengths[bindPosition] = len;
                bindData.put(value, 0, len);
            } else {
                bindChars[charoffset] = (char) len;
                int cpos = charoffset + (len >> 1);
                if (len % 2 == 1) {
                    len--;
                    bindChars[cpos + 1] = (char) (value[len] << 8);
                }
                while (len > 0) {
                    len -= 2;
                    int i = cpos;
                    cpos--;
                    bindChars[i] = (char) ((value[len] << 8) | (value[len + 1] & 255));
                }
            }
        }
        if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
            if (bindIndicators[indoffset] == -1) {
                localCheckSum = CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
            } else {
                localCheckSum = CRC64.updateChecksum(localCheckSum, value, 0, value.length);
            }
        }
        return localCheckSum;
    }

    @Override // oracle.jdbc.driver.Binder
    short updateInoutIndicatorValue(short inout) {
        return (short) (inout | 4);
    }

    @Override // oracle.jdbc.driver.Binder
    Datum getDatum(OraclePreparedStatement stmt, int bindPosition, int formOfUse, int internalType) throws SQLException {
        return SQLUtil.makeDatum(stmt.connection, this.paramVal, internalType, (String) null, 0);
    }
}
