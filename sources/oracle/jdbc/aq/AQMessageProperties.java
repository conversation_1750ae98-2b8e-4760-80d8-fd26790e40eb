package oracle.jdbc.aq;

import java.sql.SQLException;
import java.sql.Timestamp;

/* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQMessageProperties.class */
public interface AQMessageProperties {
    public static final int MESSAGE_NO_DELAY = 0;
    public static final int MESSAGE_NO_EXPIRATION = -1;
    public static final int INVALID_SHARD_ID = -1;

    int getDequeueAttemptsCount();

    void setCorrelation(String str) throws SQLException;

    String getCorrelation();

    void setDelay(int i) throws SQLException;

    int getDelay();

    Timestamp getEnqueueTime();

    void setExceptionQueue(String str) throws SQLException;

    String getExceptionQueue();

    void setExpiration(int i) throws SQLException;

    int getExpiration();

    MessageState getState();

    void setPriority(int i) throws SQLException;

    int getPriority();

    void setShardNum(int i) throws SQLException;

    int getShardNum();

    void setRecipientList(AQAgent[] aQAgentArr) throws SQLException;

    AQAgent[] getRecipientList();

    void setSender(AQAgent aQAgent) throws SQLException;

    AQAgent getSender();

    String getTransactionGroup();

    byte[] getPreviousQueueMessageId();

    DeliveryMode getDeliveryMode();

    String toString();

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQMessageProperties$MessageState.class */
    public enum MessageState {
        WAITING(1),
        READY(0),
        PROCESSED(2),
        EXPIRED(3);

        private final int code;

        MessageState(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }

        public static final MessageState getMessageState(int code) {
            if (code == WAITING.getCode()) {
                return WAITING;
            }
            if (code == READY.getCode()) {
                return READY;
            }
            if (code == PROCESSED.getCode()) {
                return PROCESSED;
            }
            return EXPIRED;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQMessageProperties$DeliveryMode.class */
    public enum DeliveryMode {
        PERSISTENT(1),
        BUFFERED(2);

        private final int code;

        DeliveryMode(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }

        public static final DeliveryMode getDeliveryMode(int code) {
            if (code == BUFFERED.getCode()) {
                return BUFFERED;
            }
            return PERSISTENT;
        }
    }
}
