package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIosessrls.class */
class T4CTTIosessrls extends T4CTTIfun {
    private static final String CLASS_NAME = T4CTTIosessrls.class.getName();
    String sessrlstag;
    long sessrlsmode;
    static final int SESSRLS_DROPSESS = 1;
    static final int SESSRLS_DEAUTHENTICATE = 2;
    static final int SESSRLS_RETAG = 4;
    static final int SESSRLS_MULTIPROPERTY_TAG = 8;

    T4CTTIosessrls(T4CConnection _conn) {
        super(_conn, (byte) 26);
        setFunCode((short) 163);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        try {
            byte[] b = null;
            this.sessrlsmode = 0L;
            if (this.connection.drcpTagName != null) {
                b = this.meg.conv.StringToCharBytes(this.connection.drcpTagName);
                this.meg.marshalPTR();
                this.meg.marshalSWORD(b.length);
                this.sessrlsmode |= 4;
                if (this.connection.getTTCVersion() >= 8 && this.connection.useDRCPMultipletag) {
                    this.sessrlsmode |= 8;
                }
            } else {
                this.meg.marshalSWORD(0);
                this.meg.marshalNULLPTR();
            }
            this.meg.marshalUB4(this.sessrlsmode);
            if (b != null) {
                this.meg.marshalCHR(b);
            }
        } catch (SQLException ea) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "marshal", "exception : {0}", (String) null, (String) null, (Object) ea.getMessage());
        }
    }

    void receive() throws SQLException, IOException {
        int sessrlstaglen = this.meg.unmarshalSWORD();
        if (sessrlstaglen > 0) {
            byte[] x = this.meg.unmarshalCHR(sessrlstaglen);
            this.sessrlstag = new String(x);
        }
        this.sessrlsmode = this.meg.unmarshalUB4();
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "receive", "sessrlstag = {0}, sessrlsmode = {1}", (String) null, (Throwable) null, this.sessrlstag, Long.valueOf(this.sessrlsmode));
    }
}
