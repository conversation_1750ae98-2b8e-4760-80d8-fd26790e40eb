package oracle.jdbc.babelfish;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/TranslationCache.class */
class TranslationCache {
    private Map<String, String> queryCache = new ConcurrentHashMap();
    private Map<Integer, TranslatedErrorInfo> errorCache = new ConcurrentHashMap();
    private Map<Integer, TranslatedErrorInfo> localErrorCache;

    TranslationCache(File localErrorFile) throws SQLException, ParserConfigurationException, SAXException, IOException, NumberFormatException {
        if (localErrorFile != null) {
            this.localErrorCache = new ConcurrentHashMap();
            readLocalErrorFile(localErrorFile);
        }
    }

    private void readLocalErrorFile(File localErrorFile) throws SQLException, ParserConfigurationException, SAXException, IOException, NumberFormatException {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setValidating(true);
            factory.setFeature("http://javax.xml.XMLConstants/feature/secure-processing", true);
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            DocumentBuilder docBuilder = factory.newDocumentBuilder();
            Document errorXMLDoc = docBuilder.parse(localErrorFile);
            int ORAError = 0;
            NodeList exceptionList = errorXMLDoc.getElementsByTagName("Exception");
            for (int i = 0; i < exceptionList.getLength(); i++) {
                TranslatedErrorInfo translatedErrorInfo = new TranslatedErrorInfo();
                Node exceptionNode = exceptionList.item(i);
                NodeList exceptionProps = exceptionNode.getChildNodes();
                for (int j = 0; j < exceptionProps.getLength(); j++) {
                    if (exceptionProps.item(j).getNodeType() == 1) {
                        Element propsElement = (Element) exceptionProps.item(j);
                        if (propsElement.getTagName().equals("ORAError")) {
                            ORAError = Integer.parseInt(propsElement.getFirstChild().getNodeValue());
                        } else if (propsElement.getTagName().equals("ErrorCode")) {
                            translatedErrorInfo.setErrorCode(Integer.parseInt(propsElement.getFirstChild().getNodeValue()));
                        } else if (propsElement.getTagName().equals("SQLState")) {
                            translatedErrorInfo.setSqlState(propsElement.getFirstChild().getNodeValue());
                        }
                    }
                }
                this.localErrorCache.put(Integer.valueOf(ORAError), translatedErrorInfo);
                ORAError = 0;
            }
        } catch (IOException e) {
            throw ((SQLException) DatabaseError.createSqlException(277).fillInStackTrace());
        } catch (ParserConfigurationException e2) {
            throw ((SQLException) DatabaseError.createSqlException(278).fillInStackTrace());
        } catch (SAXException e3) {
            throw ((SQLException) DatabaseError.createSqlException(278).fillInStackTrace());
        }
    }

    Map<String, String> getQueryCache() {
        return this.queryCache;
    }

    Map<Integer, TranslatedErrorInfo> getErrorCache() {
        return this.errorCache;
    }

    Map<Integer, TranslatedErrorInfo> getLocalErrorCache() {
        return this.localErrorCache;
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
