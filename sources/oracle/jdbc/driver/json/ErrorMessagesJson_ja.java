package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_ja.class */
public class ErrorMessagesJson_ja extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "I/O例外が発生しました"}, new Object[]{"26302", "年\"{0}\"はサポートされていません"}, new Object[]{"26303", "オーバーフロー。値が大きすぎます: {0}。"}, new Object[]{"26304", "サポートされていないオプション(実装されていません)。"}, new Object[]{"26305", "バイナリJSONが無効であるか、破損しています。"}, new Object[]{"26306", "サポートされていないバイナリJSONバージョン: {0}。"}, new Object[]{"26307", "UTF-8でエンコードされたキーの長さは256バイト以下にする必要があります。次のキーはこの制限を超えています: \"{0}\"。"}, new Object[]{"26308", "指定したJSONは大きすぎるため、バイナリJSONとしてエンコードできません。エンコードされたイメージ・サイズは2GBを超えることはできません。"}, new Object[]{"26309", "バイナリJSONが無効であるか、破損しています。指定したイメージには{0}バイトのみが含まれます。"}, new Object[]{"26310", "指定したjava.time.Periodに日数が設定されていますが、Oracleのyear to month intervalで日数はサポートされていません。"}, new Object[]{"26311", "ジェネレータは終了前にクローズしました。"}, new Object[]{"26312", "このコンテキストにオブジェクト・キーを指定する必要があります。"}, new Object[]{"26313", "書込みが無効です。完全な値がすでに書き込まれています。"}, new Object[]{"26314", "終了はこのコンテキストでは使用できません。"}, new Object[]{"26315", "キーはこのコンテキストでは使用できません。"}, new Object[]{"26316", "キーの後に必要な値。"}, new Object[]{"26317", "パーサー状態を{0}にする必要があります。"}, new Object[]{"26318", "パーサー状態を{0}にすることはできません。"}, new Object[]{"26319", "パーサーは値に存在する必要があります。"}, new Object[]{"26320", "\"{0}\"はサポートされているラッパー・タイプではありません"}, new Object[]{"26321", "このオブジェクトは変更できません。変更可能なコピーを作成するには、OracleJsonFactory.createObject(OracleJsonObject)を使用します。"}, new Object[]{"26322", "この配列は変更できません。変更可能なコピーを作成するには、OracleJsonFactory.createArray(OracleJsonArray)を使用します。"}, new Object[]{"26323", "JSONオブジェクトには重複するキーが含まれています: {0}"}, new Object[]{"26324", "エンコーディングを自動検出できません。文字が不足しています"}, new Object[]{"26325", "予期されるEOFトークンですが、{0}を取得しました"}, new Object[]{"26326", "予期しない文字{0}が行{1}、列{2}にあります"}, new Object[]{"26327", "予期しない文字{0}が行{1}、列{2}にあります。{3}が必要です。"}, new Object[]{"26328", "無効なトークン{0}が行{1}、列{2}にあります。必要なトークンは{3}です"}, new Object[]{"26329", "JsonParser#getString()はKEY_NAME、VALUE_STRING、VALUE_NUMBERパーサー状態の場合にのみ有効ですが、現在のパーサー状態は{0}です"}, new Object[]{"26330", "JsonParser#isIntegralNumber()はVALUE_NUMBERパーサー状態の場合にのみ有効ですが、現在のパーサー状態は{0}です"}, new Object[]{"26331", "JsonParser#getInt()はVALUE_NUMBERパーサー状態の場合にのみ有効ですが、現在のパーサー状態は{0}です"}, new Object[]{"26332", "JsonParser#getLong()はVALUE_NUMBERパーサー状態の場合にのみ有効ですが、現在のパーサー状態は{0}です"}, new Object[]{"26333", "JsonParser#getBigDecimal()はVALUE_NUMBERパーサー状態の場合にのみ有効ですが、現在のパーサー状態は{0}です"}, new Object[]{"26334", "JsonParser#getArray()はSTART_ARRAYパーサー状態の場合にのみ有効ですが、現在のパーサー状態は{0}です"}, new Object[]{"26335", "JsonParser#getObject()はSTART_OBJECTパーサー状態の場合にのみ有効ですが、現在のパーサー状態は{0}です"}, new Object[]{"26336", "リージョンがあるタイムスタンプはサポートされません。オフセット・タイムゾーンのみサポートされます。"}, new Object[]{"26337", "JSON値内のオブジェクトと配列は、{0}レベルより深くネストできません"}, new Object[]{"26338", "JSONオブジェクトのキーは、65,535バイトを超えないようにする必要があります"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
