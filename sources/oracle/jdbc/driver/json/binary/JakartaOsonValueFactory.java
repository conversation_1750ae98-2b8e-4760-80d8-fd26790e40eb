package oracle.jdbc.driver.json.binary;

import jakarta.json.JsonValue;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.sql.json.OracleJsonDecimal;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JakartaOsonValueFactory.class */
public class JakartaOsonValueFactory extends OsonValueFactory {
    public static JakartaOsonValueFactory INSTANCE = new JakartaOsonValueFactory();

    private JakartaOsonValueFactory() {
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    Object createBinary(OsonContext ctx, int pos, int len, boolean isId) {
        byte[] raw = new byte[len];
        ctx.b.position(pos);
        ctx.b.get(raw);
        return new JakartaPrimitive.JakartaBinaryImpl(raw, isId);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OsonAbstractArray createArray(OsonContext ctx, int pos) {
        return new JakartaOsonArray(ctx, pos);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OsonAbstractObject createObject(OsonContext ctx, int pos) {
        return new JakartaOsonObject(ctx, pos);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaStringImpl createString(OsonContext ctx, int pos, int len) {
        ctx.b.position(pos);
        return new JakartaPrimitive.JakartaStringImpl(ctx.b.readString(len));
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaNumberImpl createNumber(byte[] raw, OracleJsonDecimal.TargetType type) {
        return new JakartaPrimitive.JakartaNumberImpl(raw, type);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaStringNumberImpl createStringNumber(String value) {
        return new JakartaPrimitive.JakartaStringNumberImpl(value);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaDoubleImpl createDouble(double value) {
        return new JakartaPrimitive.JakartaDoubleImpl(value);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaTimestampImpl createTimestamp(byte[] raw) {
        return new JakartaPrimitive.JakartaTimestampImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createTimestampTZ(byte[] raw) {
        return new JakartaPrimitive.JakartaTimestampTZImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaFloatImpl createFloat(float flt) {
        return new JakartaPrimitive.JakartaFloatImpl(flt);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaDateImpl createDate(byte[] raw) {
        return new JakartaPrimitive.JakartaDateImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaIntervalYMImpl createIntervalYM(byte[] raw) {
        return new JakartaPrimitive.JakartaIntervalYMImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JakartaPrimitive.JakartaIntervalDSImpl createIntervalDS(byte[] raw) {
        return new JakartaPrimitive.JakartaIntervalDSImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createTrue() {
        return JsonValue.TRUE;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createFalse() {
        return JsonValue.FALSE;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createNull() {
        return JsonValue.NULL;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    Object createVector(OsonContext ctx, int pos, int len) {
        ctx.b.position(pos);
        byte[] res = new byte[len];
        ctx.b.get(res);
        return new JakartaPrimitive.JakartaVectorImpl(res);
    }
}
