package oracle.jdbc.babelfish;

/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/TranslatedErrorInfo.class */
class TranslatedErrorInfo {
    private int errorCode;
    private String sqlState;

    TranslatedErrorInfo() {
    }

    TranslatedErrorInfo(int errorCode, String sqlState) {
        this();
        this.errorCode = errorCode;
        this.sqlState = sqlState;
    }

    int getErrorCode() {
        return this.errorCode;
    }

    String getSqlState() {
        return this.sqlState;
    }

    void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    void setSqlState(String sqlState) {
        this.sqlState = sqlState;
    }
}
