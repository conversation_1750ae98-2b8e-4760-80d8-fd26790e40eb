package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoscid.class */
final class T4CTTIoscid extends T4CTTIfun {
    private static final String CLASS_NAME = T4CTTIoscid.class.getName();
    static final int KPDUSR_CID_RESET = 1;
    static final int KPDUSR_PROXY_RESET = 2;
    static final int KPDUSR_PROXY_TKTSENT = 4;
    static final int KPDUSR_MODULE_RESET = 8;
    static final int KPDUSR_ACTION_RESET = 16;
    static final int KPDUSR_EXECID_RESET = 32;
    static final int KPDUSR_EXECSQ_RESET = 64;
    static final int KPDUSR_COLLCT_RESET = 128;
    static final int KPDUSR_CLINFO_RESET = 256;
    static final int KPDUSR_DBOP_RESET = 512;
    private byte[] cidcid;
    private byte[] cidmod;
    private byte[] cidact;
    private byte[] cideci;
    private byte[] ciddbop;
    private byte[] cidcin;
    private boolean[] endToEndHasChanged;
    private String[] endToEndValues;
    private int endToEndECIDSequenceNumber;

    T4CTTIoscid(T4CConnection _conn) {
        super(_conn, (byte) 17);
        this.cidcid = null;
        this.cidmod = null;
        this.cidact = null;
        this.cideci = null;
        this.ciddbop = null;
        this.cidcin = null;
        this.endToEndHasChanged = null;
        this.endToEndValues = null;
        setFunCode((short) 135);
    }

    void doOSCID(boolean[] _endToEndHasChanged, String[] _endToEndValues, int _endToEndECIDSequenceNumber) throws SQLException, IOException {
        this.endToEndHasChanged = _endToEndHasChanged;
        this.endToEndValues = _endToEndValues;
        this.endToEndECIDSequenceNumber = _endToEndECIDSequenceNumber;
        if (this.endToEndValues[1] != null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOSCID", "CLIENT ID: {0}", (String) null, (String) null, (Object) this.endToEndValues[1]);
            this.cidcid = this.meg.conv.StringToCharBytes(this.endToEndValues[1]);
        } else {
            this.cidcid = null;
        }
        if (this.endToEndValues[3] != null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOSCID", "MODULE NAME: {0}", (String) null, (String) null, (Object) this.endToEndValues[3]);
            this.cidmod = this.meg.conv.StringToCharBytes(this.endToEndValues[3]);
        } else {
            this.cidmod = null;
        }
        if (this.endToEndValues[0] != null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOSCID", "ACTION NAME: {0}", (String) null, (String) null, (Object) this.endToEndValues[0]);
            this.cidact = this.meg.conv.StringToCharBytes(this.endToEndValues[0]);
        } else {
            this.cidact = null;
        }
        if (this.endToEndValues[2] != null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOSCID", "ECID: {0}", (String) null, (String) null, (Object) this.endToEndValues[2]);
            this.cideci = this.meg.conv.StringToCharBytes(this.endToEndValues[2]);
        } else {
            this.cideci = null;
        }
        if (this.endToEndValues[4] != null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOSCID", "DBOP NAME: {0}", (String) null, (String) null, (Object) this.endToEndValues[4]);
            this.ciddbop = this.meg.conv.StringToCharBytes(this.endToEndValues[4]);
        } else {
            this.ciddbop = null;
        }
        if (this.endToEndValues[5] != null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "doOSCID", "DBOP NAME: {0}", (String) null, (String) null, (Object) this.endToEndValues[5]);
            this.cidcin = this.meg.conv.StringToCharBytes(this.endToEndValues[5]);
        } else {
            this.cidcin = null;
        }
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        int cidflag = 64;
        if (this.endToEndHasChanged[0]) {
            cidflag = 64 | 16;
        }
        if (this.endToEndHasChanged[1]) {
            cidflag |= 1;
        }
        if (this.endToEndHasChanged[2]) {
            cidflag |= 32;
        }
        if (this.endToEndHasChanged[3]) {
            cidflag |= 8;
        }
        if (this.endToEndHasChanged[4]) {
            cidflag |= 512;
        }
        if (this.endToEndHasChanged[5]) {
            cidflag |= 256;
        }
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(cidflag);
        boolean sendClientId = false;
        boolean sendModule = false;
        boolean sendAction = false;
        boolean sendEcid = false;
        boolean sendDbop = false;
        boolean sendClientInfo = false;
        if (this.endToEndHasChanged[1]) {
            this.meg.marshalPTR();
            if (this.cidcid != null) {
                this.meg.marshalUB4(this.cidcid.length);
            } else {
                this.meg.marshalUB4(0L);
            }
            sendClientId = true;
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.endToEndHasChanged[3]) {
            this.meg.marshalPTR();
            if (this.cidmod != null) {
                this.meg.marshalUB4(this.cidmod.length);
            } else {
                this.meg.marshalUB4(0L);
            }
            sendModule = true;
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.endToEndHasChanged[0]) {
            this.meg.marshalPTR();
            if (this.cidact != null) {
                this.meg.marshalUB4(this.cidact.length);
            } else {
                this.meg.marshalUB4(0L);
            }
            sendAction = true;
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.endToEndHasChanged[2]) {
            this.meg.marshalPTR();
            if (this.cideci != null) {
                this.meg.marshalUB4(this.cideci.length);
            } else {
                this.meg.marshalUB4(0L);
            }
            sendEcid = true;
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB2(0);
        this.meg.marshalUB2(this.endToEndECIDSequenceNumber);
        if (this.endToEndHasChanged[5]) {
            this.meg.marshalPTR();
            if (this.cidcin != null) {
                this.meg.marshalUB4(this.cidcin.length);
            } else {
                this.meg.marshalUB4(0L);
            }
            sendClientInfo = true;
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(0L);
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(0L);
        if (this.connection.getTTCVersion() >= 7) {
            if (this.endToEndHasChanged[4]) {
                this.meg.marshalPTR();
                if (this.ciddbop != null) {
                    this.meg.marshalUB4(this.ciddbop.length);
                } else {
                    this.meg.marshalUB4(0L);
                }
                sendDbop = true;
            } else {
                this.meg.marshalNULLPTR();
                this.meg.marshalUB4(0L);
            }
        }
        if (sendClientId && this.cidcid != null) {
            this.meg.marshalCHR(this.cidcid);
        }
        if (sendModule && this.cidmod != null) {
            this.meg.marshalCHR(this.cidmod);
        }
        if (sendAction && this.cidact != null) {
            this.meg.marshalCHR(this.cidact);
        }
        if (sendEcid && this.cideci != null) {
            this.meg.marshalCHR(this.cideci);
        }
        if (sendClientInfo && this.cidcin != null) {
            this.meg.marshalCHR(this.cidcin);
        }
        if (sendDbop && this.ciddbop != null) {
            this.meg.marshalCHR(this.ciddbop);
        }
    }
}
