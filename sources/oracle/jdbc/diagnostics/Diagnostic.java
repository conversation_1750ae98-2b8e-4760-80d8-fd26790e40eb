package oracle.jdbc.diagnostics;

import java.security.AccessController;
import java.security.PrivilegedActionException;
import java.security.PrivilegedExceptionAction;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Properties;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.logging.Handler;
import java.util.logging.Level;
import java.util.logging.LogRecord;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import oracle.jdbc.driver.BuildInfo;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.logging.annotations.PropertiesBlinder;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Diagnostic.class */
public class Diagnostic {
    private static final OracleDiagnosticPermission ENABLE_SENSITIVE_PERMISSION;
    private static final AtomicLong MILLIS;
    private static final Timer CLOCK;
    private static final String CLASS_NAME;
    private static final Set<Integer> DEFAULT_ERROR_CODES;
    private static final Set<String> DEFAULT_ERROR_CODES_AS_STRINGS;
    private static final Set<Integer> errorCodesWatchedForDiagnoseFirstFailureDump;
    private static final Set<String> keywordsToSearchInException;
    private static final String ORA_ERROR_CODE_PREFIX = "ORA-";
    private static final PropertiesBlinder PROPERTIES_BLINDER;
    static final Properties SYSTEM_CONFIG;
    private static final Function<String, Boolean> IS_EXCEPTION_CONTAINS_KEYWORDS;
    private final Logger debugLogger;
    private final String loggerName;
    private Handler diagnoseFirstFailureTargetHandler;
    private AtomicReference<TraceMemoryHandler> diagnoseFirstFailureHandler;
    private static final Level DEFAULT_PUSH_LEVEL;
    static final /* synthetic */ boolean $assertionsDisabled;
    private final Properties additionalConfig = new Properties();
    private final AtomicBoolean isSensitiveEnabled = new AtomicBoolean(false);
    private boolean isAutomaticDiagnoseFirstFailureDumpEnabled = true;
    private ResourceBundle resourceBundle = null;

    static {
        $assertionsDisabled = !Diagnostic.class.desiredAssertionStatus();
        ENABLE_SENSITIVE_PERMISSION = new OracleDiagnosticPermission("enable_sensitive");
        MILLIS = new AtomicLong(System.currentTimeMillis());
        CLOCK = new Timer("oracle.jdbc.diagnostics.Diagnostic.CLOCK", true);
        CLOCK.scheduleAtFixedRate(new TimerTask() { // from class: oracle.jdbc.diagnostics.Diagnostic.1
            @Override // java.util.TimerTask, java.lang.Runnable
            public void run() {
                Diagnostic.MILLIS.set(System.currentTimeMillis());
            }
        }, 1000L, 1000L);
        CLASS_NAME = Diagnostic.class.getName();
        DEFAULT_ERROR_CODES = new HashSet();
        DEFAULT_ERROR_CODES_AS_STRINGS = new HashSet();
        errorCodesWatchedForDiagnoseFirstFailureDump = Collections.synchronizedSet(new HashSet());
        keywordsToSearchInException = Collections.synchronizedSet(new HashSet());
        PROPERTIES_BLINDER = new PropertiesBlinder();
        SYSTEM_CONFIG = new Properties();
        try {
            AccessController.doPrivileged(new PrivilegedExceptionAction<Object>() { // from class: oracle.jdbc.diagnostics.Diagnostic.2
                @Override // java.security.PrivilegedExceptionAction
                public Object run() {
                    Diagnostic.SYSTEM_CONFIG.setProperty("java.version: ", System.getProperty("java.version"));
                    Diagnostic.SYSTEM_CONFIG.setProperty("java.class.path: ", System.getProperty("java.class.path"));
                    Diagnostic.SYSTEM_CONFIG.setProperty("java.library.path: ", System.getProperty("java.library.path"));
                    return null;
                }
            });
        } catch (PrivilegedActionException e) {
        }
        SYSTEM_CONFIG.setProperty("LOCALE", Locale.getDefault().toString());
        DEFAULT_ERROR_CODES.addAll(Arrays.asList(Integer.valueOf(DatabaseError.ORAERROR_PROTOCOL_VIOLATION), Integer.valueOf(DatabaseError.ORAERROR_EXCEEDING_MAXIMUM_BUFFER_LENGTH), Integer.valueOf(DatabaseError.ORAERROR_EXCEEDING_INVALID_TYPE_REPRESENTATION_SETREP), Integer.valueOf(DatabaseError.ORAERROR_EXCEEDING_INVALID_TYPE_REPRESENTATION_GETREP), Integer.valueOf(DatabaseError.ORAERROR_INVALID_BUFFER_LENGTH), Integer.valueOf(DatabaseError.ORAERROR_DATA_TYPE_REPRESENTATION_MISMATCH), Integer.valueOf(DatabaseError.ORAERROR_OAUTH_MARSHALING_FAILURE)));
        List<Integer> errorCodesExcludeList = new ArrayList<Integer>() { // from class: oracle.jdbc.diagnostics.Diagnostic.3
            {
                add(Integer.valueOf(DatabaseError.ORAERROR_INIT_SHUTDOWN_IN_PROGRESS));
            }
        };
        Set<Integer> set = DEFAULT_ERROR_CODES;
        set.getClass();
        errorCodesExcludeList.forEach((v1) -> {
            r1.remove(v1);
        });
        DEFAULT_ERROR_CODES.stream().forEach(value -> {
            DEFAULT_ERROR_CODES_AS_STRINGS.add(ORA_ERROR_CODE_PREFIX + value);
        });
        errorCodesWatchedForDiagnoseFirstFailureDump.addAll(DEFAULT_ERROR_CODES);
        IS_EXCEPTION_CONTAINS_KEYWORDS = exceptionMessage -> {
            for (String keyword : keywordsToSearchInException) {
                if (exceptionMessage.toLowerCase().contains(keyword)) {
                    return true;
                }
            }
            return false;
        };
        DEFAULT_PUSH_LEVEL = Level.WARNING;
    }

    public static Diagnostic get(String loggerName, int size) {
        return new Diagnostic(loggerName, size);
    }

    protected Diagnostic(String loggerName, int recordsCount) {
        Logger l;
        if (!$assertionsDisabled && (loggerName == null || recordsCount <= 0)) {
            throw new AssertionError();
        }
        this.loggerName = loggerName;
        this.debugLogger = Logger.getLogger(loggerName);
        Logger parent = this.debugLogger;
        while (true) {
            l = parent;
            if (l == null || l.getHandlers().length != 0) {
                break;
            } else {
                parent = l.getParent();
            }
        }
        Handler h = l == null ? null : l.getHandlers()[0];
        if (h == null) {
            return;
        }
        this.diagnoseFirstFailureTargetHandler = h;
        this.diagnoseFirstFailureHandler = new AtomicReference<>(new TraceMemoryHandler(this.diagnoseFirstFailureTargetHandler, recordsCount, DEFAULT_PUSH_LEVEL));
    }

    public void addConfig(Properties properties) {
        properties.forEach((k, v) -> {
            this.additionalConfig.setProperty((String) k, (String) v);
        });
    }

    public final void enableSensitive(boolean enabled) {
        SecurityManager securityManager;
        if (enabled && (securityManager = System.getSecurityManager()) != null) {
            securityManager.checkPermission(ENABLE_SENSITIVE_PERMISSION);
        }
        this.isSensitiveEnabled.set(enabled);
    }

    public boolean isSensitiveEnabled() {
        return this.isSensitiveEnabled.get();
    }

    public void enableDiagnoseFirstFailureDump(boolean enableDump) {
        this.isAutomaticDiagnoseFirstFailureDumpEnabled = enableDump;
    }

    public <T extends Throwable> T trace(Level level, SecurityLabel label, String sourceClass, String sourceMethod, TraceAttributes attributes, T thrown, String publicMessage, String sensitiveMessage, Object... params) {
        if (this.diagnoseFirstFailureHandler == null) {
            return null;
        }
        if (level.intValue() >= DEFAULT_PUSH_LEVEL.intValue()) {
            Properties properties = (Properties) SYSTEM_CONFIG.clone();
            properties.put("DriverVersion", BuildInfo.getDriverVersion());
            this.additionalConfig.forEach((k, v) -> {
                properties.setProperty((String) k, (String) v);
            });
            LogRecord configRecord = buildAndGetLogRecord(Level.CONFIG, label, CLASS_NAME, "trace", null, null, "properties={0}. ", null, PROPERTIES_BLINDER.blind(properties));
            this.diagnoseFirstFailureHandler.get().publish(configRecord);
        }
        LogRecord logRecord = buildAndGetLogRecord(level, label, sourceClass, sourceMethod, attributes, thrown, publicMessage, sensitiveMessage, params);
        this.diagnoseFirstFailureHandler.get().publish(logRecord);
        if (level.intValue() < DEFAULT_PUSH_LEVEL.intValue() && (thrown instanceof SQLException) && ((errorCodesWatchedForDiagnoseFirstFailureDump.contains(Integer.valueOf(((SQLException) thrown).getErrorCode())) || IS_EXCEPTION_CONTAINS_KEYWORDS.apply(thrown.getMessage()).booleanValue()) && this.isAutomaticDiagnoseFirstFailureDumpEnabled)) {
            dumpDiagnoseFirstFailure(true);
        }
        return thrown;
    }

    public <T extends Throwable> T debug(Level level, SecurityLabel label, String sourceClass, String sourceMethod, TraceAttributes attributes, T thrown, String publicMessage, String sensitiveMessage, Object... params) {
        LogRecord logRecord = buildAndGetLogRecord(level, label, sourceClass, sourceMethod, attributes, thrown, publicMessage, sensitiveMessage, params);
        this.debugLogger.log(logRecord);
        return thrown;
    }

    private OracleLogRecord buildAndGetLogRecord(Level level, SecurityLabel label, String sourceClass, String sourceMethod, TraceAttributes attributes, Throwable thrown, String publicMessage, String sensitiveMessage, Object... params) {
        ImmutableTraceAttributes immutableTraceAttributes = attributes != null ? attributes.toReadOnly() : null;
        String message = (!this.isSensitiveEnabled.get() || sensitiveMessage == null) ? publicMessage : sensitiveMessage;
        OracleLogRecord record = new OracleLogRecord(level, label, immutableTraceAttributes, message);
        record.setMillis(MILLIS.getAndIncrement());
        record.setLoggerName(this.loggerName);
        if (message != null) {
            record.setParameters(params);
        }
        record.setResourceBundle(this.resourceBundle);
        record.setSourceClassName(sourceClass);
        record.setSourceMethodName(sourceMethod);
        record.setThreadID((int) Thread.currentThread().getId());
        record.setThrown(thrown);
        return record;
    }

    public void setLoggingLevel(String newLevel) {
        this.debugLogger.setLevel(Level.parse(newLevel));
    }

    boolean isLoggable(Level level) {
        return this.debugLogger.isLoggable(level);
    }

    protected static void addErrorCodeToWatchList(String errorCode) {
        String errCode = errorCode.trim().toUpperCase();
        if (errCode.startsWith(ORA_ERROR_CODE_PREFIX)) {
            String errNum = errCode.substring(ORA_ERROR_CODE_PREFIX.length());
            errorCodesWatchedForDiagnoseFirstFailureDump.add(Integer.valueOf(Integer.parseInt(errNum)));
        }
    }

    protected static void removeErrorCodeFromWatchList(String errorCode) {
        String errCode = errorCode.trim().toUpperCase();
        if (errCode.startsWith(ORA_ERROR_CODE_PREFIX)) {
            String errNum = errCode.substring(ORA_ERROR_CODE_PREFIX.length());
            errorCodesWatchedForDiagnoseFirstFailureDump.remove(Integer.valueOf(Integer.parseInt(errNum)));
        }
    }

    protected static String getErrorCodesWatchList() {
        return (String) errorCodesWatchedForDiagnoseFirstFailureDump.stream().map(value -> {
            return ORA_ERROR_CODE_PREFIX + value;
        }).collect(Collectors.joining(","));
    }

    protected static void resetErrorCodeWatchList() {
        errorCodesWatchedForDiagnoseFirstFailureDump.clear();
        errorCodesWatchedForDiagnoseFirstFailureDump.addAll(DEFAULT_ERROR_CODES);
    }

    public void setDiagnoseFirstFailureBufferSize(int bufferSize) {
        if (this.diagnoseFirstFailureHandler == null) {
            return;
        }
        this.diagnoseFirstFailureHandler.get().push();
        TraceMemoryHandler previous = this.diagnoseFirstFailureHandler.get();
        this.diagnoseFirstFailureHandler.set(new TraceMemoryHandler(this.diagnoseFirstFailureTargetHandler, bufferSize, Level.WARNING));
        previous.flush();
    }

    protected static void dumpDiagnoseFirstFailureWhenNextExceptionContains(String commaSeparatedKeywords) {
        keywordsToSearchInException.addAll((Collection) Stream.of((Object[]) commaSeparatedKeywords.split(",")).map((v0) -> {
            return v0.trim();
        }).map((v0) -> {
            return v0.toLowerCase();
        }).collect(Collectors.toSet()));
    }

    protected static String getExceptionKeywords() {
        return String.join(",", keywordsToSearchInException);
    }

    protected static void clearExceptionKeywords() {
        keywordsToSearchInException.clear();
    }

    public void dumpDiagnoseFirstFailure(boolean isDumpSystemConfig) {
        if (this.diagnoseFirstFailureHandler != null) {
            Properties properties = new Properties();
            if (!this.additionalConfig.isEmpty()) {
                this.additionalConfig.forEach((k, v) -> {
                    properties.setProperty((String) k, (String) v);
                });
            }
            if (isDumpSystemConfig) {
                Properties systemProperties = (Properties) SYSTEM_CONFIG.clone();
                systemProperties.forEach((k2, v2) -> {
                    properties.setProperty((String) k2, (String) v2);
                });
                properties.put("DriverVersion", BuildInfo.getDriverVersion());
            }
            if (!properties.isEmpty()) {
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "dumpDiagnoseFirstFailure", null, null, "properties={0}. ", null, PROPERTIES_BLINDER.blind(properties));
            }
            this.diagnoseFirstFailureHandler.get().push();
        }
    }

    public static Set<Integer> getErrorCodeWatchList() {
        return errorCodesWatchedForDiagnoseFirstFailureDump;
    }

    public static void stopClockTimer() {
        CLOCK.cancel();
    }
}
