package oracle.jdbc.datasource;

import java.sql.SQLException;
import javax.sql.ConnectionPoolDataSource;
import oracle.jdbc.OraclePooledConnectionBuilder;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/OracleConnectionPoolDataSource.class */
public interface OracleConnectionPoolDataSource extends ConnectionPoolDataSource, OracleCommonDataSource {
    OraclePooledConnectionBuilder createPooledConnectionBuilder() throws SQLException;
}
