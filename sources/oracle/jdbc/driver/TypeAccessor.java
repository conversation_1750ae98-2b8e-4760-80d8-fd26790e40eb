package oracle.jdbc.driver;

import java.sql.SQLException;
import oracle.jdbc.oracore.OracleType;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TypeAccessor.class */
abstract class TypeAccessor extends Accessor {
    abstract OracleType otypeFromName(String str) throws SQLException;

    TypeAccessor(Representation _representation, OracleStatement _statement, int _representationLengthMax, boolean isStoredInBindData) {
        super(_representation, _statement, _representationLengthMax, isStoredInBindData);
    }

    @Override // oracle.jdbc.driver.Accessor
    void initForDescribe(int type, int max_length, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, String typeName) throws SQLException {
        this.describeTypeName = typeName;
        initForDescribe(type, max_length, nullable, precision, scale, flags, contflag, total_elems, form);
    }

    byte[] pickledBytes(int currentRow) throws SQLException {
        return getBytesInternal(currentRow);
    }

    @Override // oracle.jdbc.driver.Accessor
    void initForDataAccess(int external_type, int max_len, String typeName) throws SQLException {
        if (external_type != 0) {
            this.externalType = external_type;
        }
        this.internalTypeName = typeName;
    }

    @Override // oracle.jdbc.driver.Accessor
    void initMetadata() throws SQLException {
        if (this.describeOtype == null && this.describeTypeName != null) {
            this.describeOtype = otypeFromName(this.describeTypeName);
        }
        if (this.internalOtype == null && this.internalTypeName != null) {
            this.internalOtype = otypeFromName(this.internalTypeName);
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    long updateChecksum(long _checkSum, int currentRow) throws SQLException {
        long _checkSum2;
        byte[] data = pickledBytes(currentRow);
        if (data == null || data.length == 0) {
            _checkSum2 = CRC64.updateChecksum(_checkSum, NULL_DATA_BYTES, 0, NULL_DATA_BYTES.length);
        } else {
            _checkSum2 = CRC64.updateChecksum(_checkSum, data, 0, data.length);
        }
        return _checkSum2;
    }
}
