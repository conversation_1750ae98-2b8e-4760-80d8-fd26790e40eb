package oracle.jdbc.driver.json.binary;

import java.lang.reflect.Array;
import java.util.AbstractList;
import java.util.Collection;
import java.util.List;
import java.util.ListIterator;
import java.util.NoSuchElementException;
import oracle.jdbc.driver.json.binary.OsonStructureImpl;
import oracle.jdbc.driver.json.tree.OracleJsonNumberImpl;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonAbstractArray.class */
public abstract class OsonAbstractArray extends OsonStructureImpl {

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonAbstractArray$ValueIter.class */
    protected class ValueIter<V> extends OsonStructureImpl.PositionIter<V> {
        protected ValueIter() {
            super();
        }

        @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl.PositionIter
        public V getValue(int i) {
            int i2 = i + 1;
            return (V) OsonAbstractArray.this.getValueInternal(OsonAbstractArray.this.getChildOffset(i));
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonAbstractArray$ListIter.class */
    public class ListIter<T> extends ValueIter<T> implements ListIterator<T> {
        @Override // oracle.jdbc.driver.json.binary.OsonAbstractArray.ValueIter, oracle.jdbc.driver.json.binary.OsonStructureImpl.PositionIter
        public /* bridge */ /* synthetic */ Object getValue(int i) {
            return super.getValue(i);
        }

        public ListIter(int i) {
            super();
            this.ipos = i;
        }

        @Override // java.util.ListIterator
        public boolean hasPrevious() {
            return this.ipos != 0;
        }

        @Override // java.util.ListIterator
        public T previous() {
            if (!hasPrevious()) {
                throw new NoSuchElementException();
            }
            OsonAbstractArray osonAbstractArray = OsonAbstractArray.this;
            int i = this.ipos - 1;
            this.ipos = i;
            return (T) osonAbstractArray.getInternal(i);
        }

        @Override // java.util.ListIterator
        public int nextIndex() {
            return this.ipos;
        }

        @Override // java.util.ListIterator
        public int previousIndex() {
            return this.ipos - 1;
        }

        @Override // java.util.Iterator, java.util.ListIterator
        public void remove() {
            throw new UnsupportedOperationException();
        }

        @Override // java.util.ListIterator
        public void set(T e) {
            throw new UnsupportedOperationException();
        }

        @Override // java.util.ListIterator
        public void add(T e) {
            throw new UnsupportedOperationException();
        }
    }

    public OsonAbstractArray(OsonContext ctx, int pos) {
        super(ctx);
        init(pos);
    }

    protected OsonAbstractArray(OsonContext ctx) {
        super(ctx);
    }

    public String getString(int i) {
        int childOffset = getOffsetWithError(i);
        String result = getStringInternal(childOffset);
        if (result == null) {
            throw new ClassCastException();
        }
        return result;
    }

    public boolean getBoolean(int i) {
        int childOffset = getOffsetWithError(i);
        Boolean result = getBooleanInternal(childOffset);
        if (result == null) {
            throw new ClassCastException();
        }
        return result.booleanValue();
    }

    public boolean getBoolean(int i, boolean d) {
        int childOffset = getChildOffset(i);
        if (childOffset == -1) {
            return d;
        }
        Boolean result = getBooleanInternal(childOffset);
        if (result == null) {
            return d;
        }
        return result.booleanValue();
    }

    public int getInt(int i) {
        int childOffset = getOffsetWithError(i);
        return ((OracleJsonNumberImpl) getValueInternal(childOffset)).intValue();
    }

    public int getInt(int i, int d) {
        int childOffset = getChildOffset(i);
        if (childOffset == -1) {
            return d;
        }
        Object o = getValueInternal(childOffset);
        if (!(o instanceof OracleJsonNumberImpl)) {
            return d;
        }
        OracleJsonNumberImpl n = (OracleJsonNumberImpl) o;
        return n.intValue();
    }

    public boolean isNull(int i) {
        int childOffset = getOffsetWithError(i);
        return isNullInternal(childOffset);
    }

    public boolean contains(Object value) {
        for (int i = 0; i < this.size; i++) {
            Object c = getValueInternal(getChildOffset(i));
            if (c.equals(value)) {
                return true;
            }
        }
        return false;
    }

    public Object[] toArray() {
        Object[] result = new Object[this.size];
        copyToArray(result);
        return result;
    }

    public <T> T[] toArray(T[] tArr) {
        T[] tArr2 = (T[]) (tArr.length >= this.size ? tArr : (Object[]) Array.newInstance(tArr.getClass().getComponentType(), this.size));
        copyToArray(tArr2);
        if (tArr2.length >= this.size + 1) {
            tArr2[this.size] = null;
        }
        return tArr2;
    }

    public boolean remove(Object o) {
        throw new UnsupportedOperationException();
    }

    public boolean removeAll(Collection<?> c) {
        throw new UnsupportedOperationException();
    }

    public boolean retainAll(Collection<?> c) {
        throw new UnsupportedOperationException();
    }

    public boolean containsAll(Collection<?> c) {
        for (Object o : c) {
            if (!contains(o)) {
                return false;
            }
        }
        return true;
    }

    public void clear() {
        throw new UnsupportedOperationException();
    }

    public boolean equals(Object o) {
        if (!(o instanceof List)) {
            return false;
        }
        List<?> otherList = (List) o;
        if (otherList.size() != this.size) {
            return false;
        }
        for (int i = 0; i < this.size; i++) {
            Object value = getValueInternal(getChildOffset(i));
            if (!value.equals(otherList.get(i))) {
                return false;
            }
        }
        return true;
    }

    public int hashCode() {
        int hashCode = 1;
        for (int i = 0; i < this.size; i++) {
            Object value = getValueInternal(getChildOffset(i));
            hashCode = (31 * hashCode) + value.hashCode();
        }
        return hashCode;
    }

    public Object getInternal(int i) {
        return getValueInternal(getOffsetWithError(i));
    }

    public int indexOf(Object o) {
        for (int i = 0; i < this.size; i++) {
            if (getInternal(i).equals(o)) {
                return i;
            }
        }
        return -1;
    }

    public int lastIndexOf(Object o) {
        int result = -1;
        for (int i = 0; i < this.size; i++) {
            if (getInternal(i).equals(o)) {
                result = i;
            }
        }
        return result;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.ARRAY;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl
    protected void init(int pos) {
        super.init(pos);
        int op = this.ctx.b.getUB1(pos);
        initChildOffseUb(op);
        int childSizeBits = op & 24;
        this.childArrayOffset = pos + 1;
        if (childSizeBits == 0) {
            this.childArrayOffset++;
            this.size = this.ctx.b.getUB1(pos + 1);
        } else if (childSizeBits == 8) {
            this.childArrayOffset += 2;
            this.size = this.ctx.b.getUB2(pos + 1);
        } else if (childSizeBits == 16) {
            this.childArrayOffset += 4;
            this.size = this.ctx.b.getUB4int(pos + 1);
        }
    }

    @Override // oracle.jdbc.driver.json.binary.OsonStructureImpl
    protected int getChildOffset(int i) {
        if (i < 0 || i >= this.size) {
            return -1;
        }
        OsonHeader header = this.ctx.getHeader();
        if (header.relativeOffsets()) {
            if (this.childOffsetUb == 2) {
                short relative = this.ctx.b.getShort(this.childArrayOffset + (i * 2));
                return (((short) (relative + (this.pos - header.getTreeSegmentOffset()))) & 65535) + header.getTreeSegmentOffset();
            }
            int relative2 = this.ctx.b.getInt(this.childArrayOffset + (i * 4));
            return ((relative2 + (this.pos - header.getTreeSegmentOffset())) & (-1)) + header.getTreeSegmentOffset();
        }
        if (this.childOffsetUb == 2) {
            return this.ctx.b.getUB2(this.childArrayOffset + (i * 2)) + header.getTreeSegmentOffset();
        }
        return this.ctx.b.getUB4int(this.childArrayOffset + (i * 4)) + header.getTreeSegmentOffset();
    }

    private void copyToArray(Object[] result) {
        for (int i = 0; i < this.size; i++) {
            result[i] = getValueInternal(getChildOffset(i));
        }
    }

    protected int getOffsetWithError(int i) {
        int childOffset = getChildOffset(i);
        if (childOffset == -1) {
            throw new IndexOutOfBoundsException();
        }
        return childOffset;
    }

    protected <T> AbstractList<T> sublist(final int fromIndex, final int toIndex) {
        if (fromIndex < 0 || toIndex > this.size || fromIndex > toIndex) {
            throw new IndexOutOfBoundsException();
        }
        return new AbstractList<T>() { // from class: oracle.jdbc.driver.json.binary.OsonAbstractArray.1
            @Override // java.util.AbstractList, java.util.List
            public T get(int i) {
                return (T) OsonAbstractArray.this.getValueInternal(OsonAbstractArray.this.getOffsetWithError(i + fromIndex));
            }

            @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
            public int size() {
                return toIndex - fromIndex;
            }
        };
    }
}
