package oracle.jdbc.driver;

import oracle.jdbc.oracore.OracleTypeADT;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/RefTypeBinder.class */
class RefTypeBinder extends TypeBinder {
    Binder theRefTypeCopyingBinder;

    RefTypeBinder(byte[] val, OracleTypeADT otype) {
        super(val, otype);
        this.theRefTypeCopyingBinder = null;
        init(this);
    }

    static void init(Binder x) {
        x.type = (short) 111;
        x.bytelen = 24;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theRefTypeCopyingBinder == null) {
            this.theRefTypeCopyingBinder = new RefTypeCopyingBinder(this.paramVal, this.paramOtype);
        }
        return this.theRefTypeCopyingBinder;
    }
}
