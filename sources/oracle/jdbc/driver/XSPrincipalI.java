package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import oracle.jdbc.internal.XSPrincipal;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/XSPrincipalI.class */
final class XSPrincipalI extends XSPrincipal {
    long kpxsprindbid = 0;
    String kpxsprinname = null;
    byte[] kpxsprinnameBytes = null;
    byte[] kpxsprinuuid = null;
    XSPrincipal.Flag kpxsprinflg = XSPrincipal.Flag.KPXS_PRIN_EXT;

    XSPrincipalI() {
    }

    @Override // oracle.jdbc.internal.XSPrincipal
    public void setDatabaseId(long kpxsprindbid) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.kpxsprindbid = kpxsprindbid;
    }

    @Override // oracle.jdbc.internal.XSPrincipal
    public void setName(String kpxsprinname) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.kpxsprinname = kpxsprinname;
    }

    @Override // oracle.jdbc.internal.XSPrincipal
    public void setUUID(byte[] kpxsprinuuid) throws SQLException {
        InternalFactory.xsSecurityCheck();
        if (kpxsprinuuid != null) {
            this.kpxsprinuuid = Arrays.copyOf(kpxsprinuuid, kpxsprinuuid.length);
        } else {
            this.kpxsprinuuid = null;
        }
    }

    @Override // oracle.jdbc.internal.XSPrincipal
    public void setFlag(XSPrincipal.Flag kpxsprinflg) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.kpxsprinflg = kpxsprinflg;
    }

    @Override // oracle.jdbc.internal.XSPrincipal
    public long getDatabaseId() {
        InternalFactory.xsSecurityCheck();
        return this.kpxsprindbid;
    }

    @Override // oracle.jdbc.internal.XSPrincipal
    public String getName() {
        InternalFactory.xsSecurityCheck();
        return this.kpxsprinname;
    }

    @Override // oracle.jdbc.internal.XSPrincipal
    public byte[] getUUID() {
        InternalFactory.xsSecurityCheck();
        return this.kpxsprinuuid;
    }

    @Override // oracle.jdbc.internal.XSPrincipal
    public XSPrincipal.Flag getFlag() {
        InternalFactory.xsSecurityCheck();
        return this.kpxsprinflg;
    }

    void doCharConversion(DBConversion conv) throws SQLException {
        if (this.kpxsprinname != null) {
            this.kpxsprinnameBytes = conv.StringToCharBytes(this.kpxsprinname);
        } else {
            this.kpxsprinnameBytes = null;
        }
    }

    void marshal(T4CMAREngine mar) throws IOException {
        mar.marshalSB8(this.kpxsprindbid);
        if (this.kpxsprinnameBytes != null) {
            mar.marshalUB4(this.kpxsprinnameBytes.length);
            mar.marshalCLR(this.kpxsprinnameBytes, this.kpxsprinnameBytes.length);
        } else {
            mar.marshalUB4(0L);
        }
        if (this.kpxsprinuuid != null) {
            mar.marshalUB4(this.kpxsprinuuid.length);
            mar.marshalCLR(this.kpxsprinuuid, this.kpxsprinuuid.length);
        } else {
            mar.marshalUB4(0L);
        }
        mar.marshalUB4(this.kpxsprinflg.getMode());
    }

    static XSPrincipalI unmarshal(T4CMAREngine mar) throws SQLException, IOException {
        XSPrincipal.Flag kpxsprinflg;
        int[] intArr = new int[1];
        long kpxsprindbid = mar.unmarshalSB8();
        String kpxsprinname = null;
        int kpxsprinnameBytesLength = (int) mar.unmarshalUB4();
        if (kpxsprinnameBytesLength > 0) {
            byte[] kpxsprinnameBytes = new byte[kpxsprinnameBytesLength];
            mar.unmarshalCLR(kpxsprinnameBytes, 0, intArr);
            kpxsprinname = mar.conv.CharBytesToString(kpxsprinnameBytes, intArr[0]);
        }
        byte[] kpxsprinuuid = null;
        int kpxsprinuuidLength = (int) mar.unmarshalUB4();
        if (kpxsprinuuidLength > 0) {
            mar.unmarshalUB1();
            kpxsprinuuid = mar.unmarshalNBytes(kpxsprinuuidLength);
        }
        long _kpxsprinflg = mar.unmarshalUB4();
        if (_kpxsprinflg == XSPrincipal.Flag.KPXS_PRIN_EXT.getMode()) {
            kpxsprinflg = XSPrincipal.Flag.KPXS_PRIN_EXT;
        } else {
            kpxsprinflg = XSPrincipal.Flag.KPXS_PRIN_USEDBID;
        }
        XSPrincipalI principal = new XSPrincipalI();
        principal.setDatabaseId(kpxsprindbid);
        principal.setName(kpxsprinname);
        principal.setUUID(kpxsprinuuid);
        principal.setFlag(kpxsprinflg);
        return principal;
    }
}
