package oracle.jdbc.driver.json.tree;

import java.time.OffsetDateTime;
import java.util.Arrays;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.binary.OsonPrimitiveConversions;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.json.OracleJsonTimestampTZ;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonTimestampTZImpl.class */
public class OracleJsonTimestampTZImpl implements OracleJsonTimestampTZ {
    byte[] raw;

    public OracleJsonTimestampTZImpl(byte[] raw) {
        this.raw = raw;
        OsonPrimitiveConversions.assertNoRegionTimestampTZ(getExceptionFactory(), raw);
    }

    public OracleJsonTimestampTZImpl(OffsetDateTime i) {
        this.raw = OsonPrimitiveConversions.toOracleTimestampTZ(getExceptionFactory(), i);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.TIMESTAMPTZ;
    }

    @Override // oracle.sql.json.OracleJsonTimestampTZ
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonTimestampTZ)) {
            return false;
        }
        if (!(other instanceof OracleJsonTimestampTZImpl)) {
            throw new UnsupportedOperationException();
        }
        OracleJsonTimestampTZImpl otherd = (OracleJsonTimestampTZImpl) other;
        return Arrays.equals(otherd.raw, this.raw);
    }

    @Override // oracle.sql.json.OracleJsonTimestampTZ
    public int hashCode() {
        return Arrays.hashCode(this.raw);
    }

    @Override // oracle.sql.json.OracleJsonTimestampTZ
    public String getString() {
        return OsonPrimitiveConversions.timestampTZToString(getExceptionFactory(), this.raw);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        return JsonSerializerImpl.serializeString(getString());
    }

    public byte[] raw() {
        return this.raw;
    }

    @Override // oracle.sql.json.OracleJsonTimestampTZ
    public TIMESTAMPTZ getTIMESTAMPTZ() {
        return new TIMESTAMPTZ(raw());
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaTimestampTZImpl(this.raw));
        }
        return c.cast(new JsonpPrimitive.JsonpTimestampTZImpl(this.raw));
    }

    public OracleJsonExceptions.ExceptionFactory getExceptionFactory() {
        return OracleJsonExceptions.ORACLE_FACTORY;
    }

    @Override // oracle.sql.json.OracleJsonTimestampTZ
    public OffsetDateTime getOffsetDateTime() {
        return OsonPrimitiveConversions.timestamptzToOffsetDateTime(getExceptionFactory(), this.raw);
    }
}
