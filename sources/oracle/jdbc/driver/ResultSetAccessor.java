package oracle.jdbc.driver;

import java.sql.ResultSet;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ResultSetAccessor.class */
class ResultSetAccessor extends Accessor {
    static final int MAXLENGTH = 16;
    OracleStatement currentStmt;

    ResultSetAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        super(Representation.RESULT_SET, stmt, 16, isStoredInBindData);
        init(stmt, 102, DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN, form, isOutBind);
        initForDataAccess(external_type, max_len, null);
    }

    ResultSetAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(Representation.RESULT_SET, stmt, 16, false);
        init(stmt, 102, DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN, form, false);
        initForDescribe(102, max_len, nullable, flags, precision, scale, contflag, total_elems, form, null);
        initForDataAccess(0, max_len, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    ResultSet getCursor(int currentRow) throws SQLException {
        OracleResultSet rset = null;
        if (this.currentStmt != null && this.currentStmt.refCursorRowNumber == currentRow && !this.currentStmt.isClosed()) {
            rset = this.currentStmt.createResultSet();
        } else {
            byte[] bytes = getBytes(currentRow);
            if (bytes != null) {
                OracleStatement newstmt = this.statement.connection.RefCursorBytesToStatement(bytes, this.statement);
                newstmt.refCursorRowNumber = currentRow;
                newstmt.doDescribe(false);
                if (newstmt.numberOfDefinePositions > 0) {
                    newstmt.prepareAccessors();
                }
                newstmt.setPrefetchInternal(this.statement.getFetchSize(), false, false);
                newstmt.setQueryTimeout(this.statement.getQueryTimeout());
                newstmt.closeOnCompletion();
                rset = newstmt.createResultSet();
                newstmt.currentResultSet = rset;
                this.currentStmt = newstmt;
            }
        }
        return rset;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        return getCursor(currentRow);
    }
}
