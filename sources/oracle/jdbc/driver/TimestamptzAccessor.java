package oracle.jdbc.driver;

import java.sql.Date;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Map;
import java.util.TimeZone;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.TIMEZONETAB;
import oracle.sql.ZONEIDMAP;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TimestamptzAccessor.class */
class TimestamptzAccessor extends DateTimeCommonAccessor {
    static final int MAXLENGTH = 13;
    TimestampTzConverter tstzConverter;
    static int OFFSET_HOUR = 20;
    static int OFFSET_MINUTE = 60;
    static byte REGIONIDBIT = Byte.MIN_VALUE;

    TimestamptzAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        super(Representation.TIMESTAMPTZ, stmt, 13, isStoredInBindData);
        this.tstzConverter = null;
        init(stmt, 181, 181, form, isOutBind);
        initForDataAccess(external_type, max_len, null);
        if (this.statement.connection.timestamptzInGmt) {
            this.tstzConverter = new GmtTimestampTzConverter();
        } else {
            this.tstzConverter = new OldTimestampTzConverter();
        }
    }

    TimestamptzAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(Representation.TIMESTAMPTZ, stmt, 13, false);
        this.tstzConverter = null;
        init(stmt, 181, 181, form, false);
        initForDescribe(181, max_len, nullable, flags, precision, scale, contflag, total_elems, form, null);
        initForDataAccess(0, max_len, null);
        if (this.statement.connection.timestamptzInGmt) {
            this.tstzConverter = new GmtTimestampTzConverter();
        } else {
            this.tstzConverter = new OldTimestampTzConverter();
        }
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException, NullPointerException {
        String regname;
        if (this.isUseLess || isNull(currentRow)) {
            return null;
        }
        int regionID = 0;
        getBytesInternal(currentRow, this.tmpBytes);
        if ((oracleTZ1(this.tmpBytes) & REGIONIDBIT) != 0) {
            int regionID2 = getHighOrderbits(oracleTZ1(this.tmpBytes));
            regionID = regionID2 + getLowOrderbits(oracleTZ2(this.tmpBytes));
            TIMEZONETAB tzTab = this.statement.connection.getTIMEZONETAB();
            if (tzTab.checkID(regionID)) {
                tzTab.updateTable(this.statement.connection, regionID);
            }
            regname = ZONEIDMAP.getRegion(regionID);
        } else {
            int off_hour = oracleTZ1(this.tmpBytes) - OFFSET_HOUR;
            int off_minute = Math.abs(oracleTZ2(this.tmpBytes) - OFFSET_MINUTE);
            regname = "GMT" + (off_hour < 0 ? "" : "+") + off_hour + (off_minute < 10 ? ":0" : ":") + off_minute;
        }
        Calendar calUTC = (Calendar) this.statement.getGMTCalendar().clone();
        int year = oracleYear(this.tmpBytes);
        calUTC.set(1, year);
        calUTC.set(2, oracleMonth(this.tmpBytes));
        calUTC.set(5, oracleDay(this.tmpBytes));
        calUTC.set(11, oracleHour(this.tmpBytes));
        calUTC.set(12, oracleMin(this.tmpBytes));
        calUTC.set(13, oracleSec(this.tmpBytes));
        calUTC.set(14, 0);
        if ((oracleTZ1(this.tmpBytes) & REGIONIDBIT) != 0) {
            int offset_gmt = this.statement.connection.getTIMEZONETAB().getOffset(calUTC, regionID);
            calUTC.add(14, offset_gmt);
        } else {
            calUTC.add(10, oracleTZ1(this.tmpBytes) - OFFSET_HOUR);
            calUTC.add(12, oracleTZ2(this.tmpBytes) - OFFSET_MINUTE);
        }
        int year2 = calUTC.get(1);
        int month = calUTC.get(2) + 1;
        int date = calUTC.get(5);
        int hour = calUTC.get(11);
        int min = calUTC.get(12);
        int sec = calUTC.get(13);
        boolean am = hour < 12;
        if (regname.length() > 3 && regname.startsWith("GMT")) {
            regname = regname.substring(3);
        }
        int nanos = oracleNanos(this.tmpBytes);
        return toText(year2, month, date, hour, min, sec, nanos, am, regname);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Date getDate(int currentRow) throws SQLException {
        return this.tstzConverter.getDate(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Date getDate(int currentRow, Calendar cal) throws SQLException {
        return getDate(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Time getTime(int currentRow) throws SQLException {
        return this.tstzConverter.getTime(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Time getTime(int currentRow, Calendar cal) throws SQLException {
        return getTime(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Timestamp getTimestamp(int currentRow) throws SQLException {
        return this.tstzConverter.getTimestamp(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Timestamp getTimestamp(int currentRow, Calendar cal) throws SQLException {
        return getTimestamp(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        return this.tstzConverter.getObject(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Datum getOracleObject(int currentRow) throws SQLException {
        return this.tstzConverter.getOracleObject(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    DATE getDATE(int currentRow) throws SQLException {
        TIMESTAMPTZ tstz = this.tstzConverter.getTIMESTAMPTZ(currentRow);
        if (tstz == null) {
            return null;
        }
        return TIMESTAMPTZ.toDATE(this.statement.connection, tstz.getBytes());
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMP getTIMESTAMP(int currentRow) throws SQLException {
        TIMESTAMPTZ tstz = this.tstzConverter.getTIMESTAMPTZ(currentRow);
        if (tstz == null) {
            return null;
        }
        return TIMESTAMPTZ.toTIMESTAMP(this.statement.connection, tstz.getBytes());
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    TIMESTAMPTZ getTIMESTAMPTZ(int currentRow) throws SQLException {
        return this.tstzConverter.getTIMESTAMPTZ(currentRow);
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Calendar getCalendar(int currentRow) throws SQLException {
        return (Calendar) JavaToJavaConverter.convert(new TIMESTAMPTZ(getBytesInternal(currentRow)), Calendar.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalDate getLocalDate(int currentRow) throws SQLException {
        return (LocalDate) JavaToJavaConverter.convert(new TIMESTAMPTZ(getBytesInternal(currentRow)), LocalDate.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalDateTime getLocalDateTime(int currentRow) throws SQLException {
        return (LocalDateTime) JavaToJavaConverter.convert(new TIMESTAMPTZ(getBytesInternal(currentRow)), LocalDateTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    LocalTime getLocalTime(int currentRow) throws SQLException {
        return (LocalTime) JavaToJavaConverter.convert(new TIMESTAMPTZ(getBytesInternal(currentRow)), LocalTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    OffsetDateTime getOffsetDateTime(int currentRow) throws SQLException {
        return (OffsetDateTime) JavaToJavaConverter.convert(new TIMESTAMPTZ(getBytesInternal(currentRow)), OffsetDateTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    OffsetTime getOffsetTime(int currentRow) throws SQLException {
        return (OffsetTime) JavaToJavaConverter.convert(new TIMESTAMPTZ(getBytesInternal(currentRow)), OffsetTime.class, this.statement.connection, null, null);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    ZonedDateTime getZonedDateTime(int currentRow) throws SQLException {
        return (ZonedDateTime) JavaToJavaConverter.convert(new TIMESTAMPTZ(getBytesInternal(currentRow)), ZonedDateTime.class, this.statement.connection, null, null);
    }

    static int setHighOrderbits(int ID) {
        return (ID & 8128) >> 6;
    }

    static int setLowOrderbits(int ID) {
        return (ID & 63) << 2;
    }

    static int getHighOrderbits(int ID) {
        return (ID & 127) << 6;
    }

    static int getLowOrderbits(int ID) {
        return (ID & 252) >> 2;
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/TimestamptzAccessor$OldTimestampTzConverter.class */
    class OldTimestampTzConverter extends TimestampTzConverter {
        OldTimestampTzConverter() {
            super();
        }

        @Override // oracle.jdbc.driver.TimestamptzAccessor.TimestampTzConverter
        Date getDate(int currentRow) throws SQLException, NullPointerException {
            if (TimestamptzAccessor.this.isUseLess || TimestamptzAccessor.this.isNull(currentRow)) {
                return null;
            }
            TimeZone zone = TimestamptzAccessor.this.statement.getDefaultTimeZone();
            Calendar cal = Calendar.getInstance(zone);
            TimestamptzAccessor.this.getBytesInternal(currentRow, TimestamptzAccessor.this.tmpBytes);
            int year = DateTimeCommonAccessor.oracleYear(TimestamptzAccessor.this.tmpBytes);
            cal.set(1, year);
            cal.set(2, DateTimeCommonAccessor.oracleMonth(TimestamptzAccessor.this.tmpBytes));
            cal.set(5, DateTimeCommonAccessor.oracleDay(TimestamptzAccessor.this.tmpBytes));
            cal.set(11, DateTimeCommonAccessor.oracleHour(TimestamptzAccessor.this.tmpBytes));
            cal.set(12, DateTimeCommonAccessor.oracleMin(TimestamptzAccessor.this.tmpBytes));
            cal.set(13, DateTimeCommonAccessor.oracleSec(TimestamptzAccessor.this.tmpBytes));
            cal.set(14, 0);
            if ((DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes) & TimestamptzAccessor.REGIONIDBIT) != 0) {
                int regionID = TimestamptzAccessor.getHighOrderbits(DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes)) + TimestamptzAccessor.getLowOrderbits(DateTimeCommonAccessor.oracleTZ2(TimestamptzAccessor.this.tmpBytes));
                TIMEZONETAB tzTab = TimestamptzAccessor.this.statement.connection.getTIMEZONETAB();
                if (tzTab.checkID(regionID)) {
                    tzTab.updateTable(TimestamptzAccessor.this.statement.connection, regionID);
                }
                int offset = tzTab.getOffset(cal, regionID);
                boolean sourceTimeInDST = zone.inDaylightTime(cal.getTime());
                boolean destinationTimeInDST = zone.inDaylightTime(new java.util.Date(cal.getTimeInMillis() + offset));
                if (!sourceTimeInDST && destinationTimeInDST) {
                    cal.add(14, (-1) * zone.getDSTSavings());
                } else if (sourceTimeInDST && !destinationTimeInDST) {
                    cal.add(14, zone.getDSTSavings());
                }
                cal.add(10, offset / 3600000);
                cal.add(12, (offset % 3600000) / 60000);
            } else {
                cal.add(10, DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes) - TimestamptzAccessor.OFFSET_HOUR);
                cal.add(12, DateTimeCommonAccessor.oracleTZ2(TimestamptzAccessor.this.tmpBytes) - TimestamptzAccessor.OFFSET_MINUTE);
            }
            long millis = cal.getTimeInMillis();
            return new Date(millis);
        }

        @Override // oracle.jdbc.driver.TimestamptzAccessor.TimestampTzConverter
        Time getTime(int currentRow) throws SQLException, NullPointerException {
            if (TimestamptzAccessor.this.isUseLess || TimestamptzAccessor.this.isNull(currentRow)) {
                return null;
            }
            TimeZone zone = TimestamptzAccessor.this.statement.getDefaultTimeZone();
            Calendar cal = Calendar.getInstance(zone);
            TimestamptzAccessor.this.getBytesInternal(currentRow, TimestamptzAccessor.this.tmpBytes);
            int year = DateTimeCommonAccessor.oracleYear(TimestamptzAccessor.this.tmpBytes);
            cal.set(1, year);
            cal.set(2, DateTimeCommonAccessor.oracleMonth(TimestamptzAccessor.this.tmpBytes));
            cal.set(5, DateTimeCommonAccessor.oracleDay(TimestamptzAccessor.this.tmpBytes));
            cal.set(11, DateTimeCommonAccessor.oracleHour(TimestamptzAccessor.this.tmpBytes));
            cal.set(12, DateTimeCommonAccessor.oracleMin(TimestamptzAccessor.this.tmpBytes));
            cal.set(13, DateTimeCommonAccessor.oracleSec(TimestamptzAccessor.this.tmpBytes));
            cal.set(14, 0);
            if ((DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes) & TimestamptzAccessor.REGIONIDBIT) != 0) {
                int regionID = TimestamptzAccessor.getHighOrderbits(DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes)) + TimestamptzAccessor.getLowOrderbits(DateTimeCommonAccessor.oracleTZ2(TimestamptzAccessor.this.tmpBytes));
                TIMEZONETAB tzTab = TimestamptzAccessor.this.statement.connection.getTIMEZONETAB();
                if (tzTab.checkID(regionID)) {
                    tzTab.updateTable(TimestamptzAccessor.this.statement.connection, regionID);
                }
                int offset = tzTab.getOffset(cal, regionID);
                boolean sourceTimeInDST = zone.inDaylightTime(cal.getTime());
                boolean destinationTimeInDST = zone.inDaylightTime(new java.util.Date(cal.getTimeInMillis() + offset));
                if (!sourceTimeInDST && destinationTimeInDST) {
                    cal.add(14, (-1) * zone.getDSTSavings());
                } else if (sourceTimeInDST && !destinationTimeInDST) {
                    cal.add(14, zone.getDSTSavings());
                }
                cal.add(10, offset / 3600000);
                cal.add(12, (offset % 3600000) / 60000);
            } else {
                cal.add(10, DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes) - TimestamptzAccessor.OFFSET_HOUR);
                cal.add(12, DateTimeCommonAccessor.oracleTZ2(TimestamptzAccessor.this.tmpBytes) - TimestamptzAccessor.OFFSET_MINUTE);
            }
            long millis = cal.getTimeInMillis();
            return new Time(millis);
        }

        @Override // oracle.jdbc.driver.TimestamptzAccessor.TimestampTzConverter
        Timestamp getTimestamp(int currentRow) throws SQLException, NullPointerException {
            if (TimestamptzAccessor.this.isUseLess || TimestamptzAccessor.this.isNull(currentRow)) {
                return null;
            }
            TimeZone zone = TimestamptzAccessor.this.statement.getDefaultTimeZone();
            Calendar cal = Calendar.getInstance(zone);
            Calendar calUTC = (Calendar) TimestamptzAccessor.this.statement.getGMTCalendar().clone();
            TimestamptzAccessor.this.getBytesInternal(currentRow, TimestamptzAccessor.this.tmpBytes);
            int year = DateTimeCommonAccessor.oracleYear(TimestamptzAccessor.this.tmpBytes);
            cal.set(1, year);
            cal.set(2, DateTimeCommonAccessor.oracleMonth(TimestamptzAccessor.this.tmpBytes));
            cal.set(5, DateTimeCommonAccessor.oracleDay(TimestamptzAccessor.this.tmpBytes));
            cal.set(11, DateTimeCommonAccessor.oracleHour(TimestamptzAccessor.this.tmpBytes));
            cal.set(12, DateTimeCommonAccessor.oracleMin(TimestamptzAccessor.this.tmpBytes));
            cal.set(13, DateTimeCommonAccessor.oracleSec(TimestamptzAccessor.this.tmpBytes));
            cal.set(14, 0);
            calUTC.set(1, year);
            calUTC.set(2, DateTimeCommonAccessor.oracleMonth(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(5, DateTimeCommonAccessor.oracleDay(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(11, DateTimeCommonAccessor.oracleHour(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(12, DateTimeCommonAccessor.oracleMin(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(13, DateTimeCommonAccessor.oracleSec(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(14, 0);
            if ((DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes) & TimestamptzAccessor.REGIONIDBIT) != 0) {
                int regionID = TimestamptzAccessor.getHighOrderbits(DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes)) + TimestamptzAccessor.getLowOrderbits(DateTimeCommonAccessor.oracleTZ2(TimestamptzAccessor.this.tmpBytes));
                TIMEZONETAB tzTab = TimestamptzAccessor.this.statement.connection.getTIMEZONETAB();
                if (tzTab.checkID(regionID)) {
                    tzTab.updateTable(TimestamptzAccessor.this.statement.connection, regionID);
                }
                int offset = tzTab.getOffset(calUTC, regionID);
                boolean sourceTimeInDST = zone.inDaylightTime(cal.getTime());
                boolean destinationTimeInDST = zone.inDaylightTime(new java.util.Date(cal.getTimeInMillis() + offset));
                if (!sourceTimeInDST && destinationTimeInDST) {
                    cal.add(14, (-1) * zone.getDSTSavings());
                } else if (sourceTimeInDST && !destinationTimeInDST) {
                    cal.add(14, zone.getDSTSavings());
                }
                cal.add(10, offset / 3600000);
                cal.add(12, (offset % 3600000) / 60000);
            } else {
                cal.add(10, DateTimeCommonAccessor.oracleTZ1(TimestamptzAccessor.this.tmpBytes) - TimestamptzAccessor.OFFSET_HOUR);
                cal.add(12, DateTimeCommonAccessor.oracleTZ2(TimestamptzAccessor.this.tmpBytes) - TimestamptzAccessor.OFFSET_MINUTE);
            }
            long millis = cal.getTimeInMillis();
            Timestamp result = new Timestamp(millis);
            int nanos = DateTimeCommonAccessor.oracleNanos(TimestamptzAccessor.this.tmpBytes);
            result.setNanos(nanos);
            return result;
        }

        @Override // oracle.jdbc.driver.TimestamptzAccessor.TimestampTzConverter
        TIMESTAMPTZ getTIMESTAMPTZ(int currentRow) throws SQLException {
            if (TimestamptzAccessor.this.isUseLess || TimestamptzAccessor.this.isNull(currentRow)) {
                return null;
            }
            return new TIMESTAMPTZ(TimestamptzAccessor.this.getBytesInternal(currentRow));
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/TimestamptzAccessor$GmtTimestampTzConverter.class */
    class GmtTimestampTzConverter extends TimestampTzConverter {
        GmtTimestampTzConverter() {
            super();
        }

        @Override // oracle.jdbc.driver.TimestamptzAccessor.TimestampTzConverter
        Date getDate(int currentRow) throws SQLException {
            if (TimestamptzAccessor.this.isUseLess || TimestamptzAccessor.this.isNull(currentRow)) {
                return null;
            }
            Calendar calUTC = (Calendar) TimestamptzAccessor.this.statement.getGMTCalendar().clone();
            TimestamptzAccessor.this.getBytesInternal(currentRow, TimestamptzAccessor.this.tmpBytes);
            int year = DateTimeCommonAccessor.oracleYear(TimestamptzAccessor.this.tmpBytes);
            calUTC.set(1, year);
            calUTC.set(2, DateTimeCommonAccessor.oracleMonth(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(5, DateTimeCommonAccessor.oracleDay(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(11, DateTimeCommonAccessor.oracleHour(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(12, DateTimeCommonAccessor.oracleMin(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(13, DateTimeCommonAccessor.oracleSec(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(14, 0);
            long millis = calUTC.getTimeInMillis();
            return new Date(millis);
        }

        @Override // oracle.jdbc.driver.TimestamptzAccessor.TimestampTzConverter
        Time getTime(int currentRow) throws SQLException {
            if (TimestamptzAccessor.this.isUseLess || TimestamptzAccessor.this.isNull(currentRow)) {
                return null;
            }
            Calendar calUTC = (Calendar) TimestamptzAccessor.this.statement.getGMTCalendar().clone();
            TimestamptzAccessor.this.getBytesInternal(currentRow, TimestamptzAccessor.this.tmpBytes);
            int year = DateTimeCommonAccessor.oracleYear(TimestamptzAccessor.this.tmpBytes);
            calUTC.set(1, year);
            calUTC.set(2, DateTimeCommonAccessor.oracleMonth(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(5, DateTimeCommonAccessor.oracleDay(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(11, DateTimeCommonAccessor.oracleHour(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(12, DateTimeCommonAccessor.oracleMin(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(13, DateTimeCommonAccessor.oracleSec(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(14, 0);
            return new Time(calUTC.getTimeInMillis());
        }

        @Override // oracle.jdbc.driver.TimestamptzAccessor.TimestampTzConverter
        Timestamp getTimestamp(int currentRow) throws SQLException {
            if (TimestamptzAccessor.this.isUseLess || TimestamptzAccessor.this.isNull(currentRow)) {
                return null;
            }
            Calendar calUTC = (Calendar) TimestamptzAccessor.this.statement.getGMTCalendar().clone();
            TimestamptzAccessor.this.getBytesInternal(currentRow, TimestamptzAccessor.this.tmpBytes);
            int year = DateTimeCommonAccessor.oracleYear(TimestamptzAccessor.this.tmpBytes);
            calUTC.set(1, year);
            calUTC.set(2, DateTimeCommonAccessor.oracleMonth(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(5, DateTimeCommonAccessor.oracleDay(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(11, DateTimeCommonAccessor.oracleHour(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(12, DateTimeCommonAccessor.oracleMin(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(13, DateTimeCommonAccessor.oracleSec(TimestamptzAccessor.this.tmpBytes));
            calUTC.set(14, 0);
            long millis = calUTC.getTimeInMillis();
            Timestamp result = new Timestamp(millis);
            int nanos = DateTimeCommonAccessor.oracleNanos(TimestamptzAccessor.this.tmpBytes);
            result.setNanos(nanos);
            return result;
        }

        @Override // oracle.jdbc.driver.TimestamptzAccessor.TimestampTzConverter
        TIMESTAMPTZ getTIMESTAMPTZ(int currentRow) throws SQLException {
            if (TimestamptzAccessor.this.isUseLess || TimestamptzAccessor.this.isNull(currentRow)) {
                return null;
            }
            return new TIMESTAMPTZ(TimestamptzAccessor.this.getBytesInternal(currentRow));
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/TimestamptzAccessor$TimestampTzConverter.class */
    abstract class TimestampTzConverter {
        abstract Date getDate(int i) throws SQLException;

        abstract Time getTime(int i) throws SQLException;

        abstract Timestamp getTimestamp(int i) throws SQLException;

        abstract TIMESTAMPTZ getTIMESTAMPTZ(int i) throws SQLException;

        TimestampTzConverter() {
        }

        Object getObject(int currentRow) throws SQLException {
            return getTIMESTAMPTZ(currentRow);
        }

        Datum getOracleObject(int currentRow) throws SQLException {
            return getTIMESTAMPTZ(currentRow);
        }

        Object getObject(int currentRow, Map<String, Class<?>> map) throws SQLException {
            return getTIMESTAMPTZ(currentRow);
        }
    }
}
