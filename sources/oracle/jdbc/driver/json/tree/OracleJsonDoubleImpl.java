package oracle.jdbc.driver.json.tree;

import java.math.BigDecimal;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.json.OracleJsonDouble;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonDoubleImpl.class */
public class OracleJsonDoubleImpl extends OracleJsonNumberImpl implements OracleJsonDouble {
    public static String NAN = "\"Nan\"";
    double value;

    public OracleJsonDoubleImpl(double value) {
        this.value = value;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.DOUBLE;
    }

    @Override // oracle.sql.json.OracleJsonDouble
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonDouble)) {
            return false;
        }
        OracleJsonDouble otherd = (OracleJsonDouble) other;
        return Double.doubleToLongBits(this.value) == Double.doubleToLongBits(otherd.doubleValue());
    }

    @Override // oracle.sql.json.OracleJsonDouble
    public int hashCode() {
        return Double.hashCode(this.value);
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public BigDecimal bigDecimalValue() {
        return BigDecimal.valueOf(this.value);
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public double doubleValue() {
        return this.value;
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public String getString() {
        if (this.value == Double.POSITIVE_INFINITY) {
            return OracleJsonDecimalImpl.POSITIVE_INF;
        }
        if (this.value == Double.NEGATIVE_INFINITY) {
            return OracleJsonDecimalImpl.NEGATIVE_INF;
        }
        if (Double.isNaN(this.value)) {
            return NAN;
        }
        return Double.toString(this.value);
    }

    @Override // oracle.sql.json.OracleJsonDouble
    public BINARY_DOUBLE getDOUBLE() {
        return new BINARY_DOUBLE(this.value);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaDoubleImpl(this.value));
        }
        return c.cast(new JsonpPrimitive.JsonpDoubleImpl(this.value));
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl, oracle.sql.json.OracleJsonValue
    public String toString() {
        if (this.value == Double.POSITIVE_INFINITY) {
            return OracleJsonDecimalImpl.POSITIVE_INF;
        }
        if (this.value == Double.NEGATIVE_INFINITY) {
            return OracleJsonDecimalImpl.NEGATIVE_INF;
        }
        if (Double.isNaN(this.value)) {
            return NAN;
        }
        return BigDecimal.valueOf(this.value).toString();
    }
}
