package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_de.class */
public class ErrorMessagesJson_de extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Eine I/O-Ausnahme ist aufgetreten"}, new Object[]{"26302", "Das Jahr \"{0}\" wird nicht unterstützt"}, new Object[]{"26303", "<PERSON><PERSON>lauf, Wert zu groß: {0}."}, new Object[]{"26304", "Nicht unterstützte Option (nicht implementiert)."}, new Object[]{"26305", "Binäre JSON ist ungültig oder beschädigt."}, new Object[]{"26306", "Nicht unterstützte binäre JSON-Version: {0}."}, new Object[]{"26307", "Der mit UTF-8 codierte Schlüssel darf nicht länger als 256 Byte sein. Der folgende Schlüssel überschreitet diesen Grenzwert: \"{0}\"."}, new Object[]{"26308", "Die angegebene JSON ist zu groß, um als binäre JSON codiert zu werden. Die codierten Bilder dürfen nicht größer als 2 GB sein."}, new Object[]{"26309", "Die binäre JSON ist ungültig oder beschädigt. Das angegebene Bild enthält nur {0} Byte."}, new Object[]{"26310", "Für die angegebene java.time.Period wurden Tage festgelegt, aber das Jahr-Monat-Intervall von Oracle unterstützt keine Tage."}, new Object[]{"26311", "Generator vor Ende geschlossen."}, new Object[]{"26312", "Ein Objektschlüssel muss in diesem Kontext angegeben werden."}, new Object[]{"26313", "Ungültiger Schreibvorgang. Ein vollständiger Wert wurde bereits geschrieben."}, new Object[]{"26314", "Ende in diesem Kontext nicht zulässig."}, new Object[]{"26315", "Schlüssel in diesem Kontext nicht zulässig."}, new Object[]{"26316", "Wert nach Schlüssel erwartet."}, new Object[]{"26317", "Parserstatus muss {0} sein."}, new Object[]{"26318", "Parserstatus darf nicht {0} sein."}, new Object[]{"26319", "Parser muss auf einem Wert liegen."}, new Object[]{"26320", "\"{0}\" ist kein unterstützter Wrapper-Typ."}, new Object[]{"26321", "Dieses Objekt kann nicht geändert werden. Um eine änderbare Kopie zu erstellen, verwenden Sie OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Dieses Array kann nicht geändert werden. Um eine änderbare Kopie zu erstellen, verwenden Sie OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "JSON-Objekt enthält doppelten Schlüssel: {0}."}, new Object[]{"26324", "Codierung kann nicht automatisch erkannt werden. Zu wenige Zeichen vorhanden."}, new Object[]{"26325", "EOF-Token wurde erwartet, aber {0} wurde empfangen."}, new Object[]{"26326", "Unerwartetes Zeichen {0} bei Zeile {1}, Spalte {2}."}, new Object[]{"26327", "Unerwartetes Zeichen {0} bei Zeile {1}, Spalte {2}. Erwartet: {3}."}, new Object[]{"26328", "Ungültiges Token {0} bei Zeile {1}, Spalte {2}. Erwartete Token: {3}."}, new Object[]{"26329", "JsonParser#getString() ist nur für den Parserstatus KEY_NAME, VALUE_STRING, VALUE_NUMBER gültig. Der aktuelle Parserstatus ist aber {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() ist nur für den Parserstatus VALUE_NUMBER gültig. Der aktuelle Parserstatus ist aber {0}."}, new Object[]{"26331", "JsonParser#getInt() ist nur für den Parserstatus VALUE_NUMBER gültig. Der aktuelle Parserstatus ist aber {0}."}, new Object[]{"26332", "JsonParser#getLong() ist nur für den Parserstatus VALUE_NUMBER gültig. Der aktuelle Parserstatus ist aber {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() ist nur für den Parserstatus VALUE_NUMBER gültig. Der aktuelle Parserstatus ist aber {0}."}, new Object[]{"26334", "JsonParser#getArray() ist nur gültig für Parserstatus START_ARRAY. Aktueller Parserstatus ist jedoch {0}."}, new Object[]{"26335", "JsonParser#getObject() ist nur für den Parserstatus START_OBJECT gültig. Der aktuelle Parserstatus ist aber {0}."}, new Object[]{"26336", "Zeitstempel mit Regionen werden nicht unterstützt. Nur Offsetzeitzonen werden unterstützt."}, new Object[]{"26337", "Die Objekte und Arrays im JSON-Wert dürfen nicht tiefer als {0} Ebenen verschachtelt sein"}, new Object[]{"26338", "Die Schlüssel eines JSON-Objekts dürfen 65.535 Byte nicht überschreiten"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
