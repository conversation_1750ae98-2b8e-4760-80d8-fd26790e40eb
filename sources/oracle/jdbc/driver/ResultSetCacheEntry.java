package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicReference;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ResultSetCacheEntry.class */
final class ResultSetCacheEntry {
    private final AtomicReference<QueryResultState> queryResultState = new AtomicReference<>(QueryResultState.FETCHING);
    private int numRows = -1;
    private ByteArray rowData = null;
    private AccessorPrototype[] accessorPrototypes = null;
    String userName;
    ResultSetCacheEntryKey key;
    long queryId;
    long sizeInMemory;
    static final /* synthetic */ boolean $assertionsDisabled;

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/ResultSetCacheEntry$QueryResultState.class */
    enum QueryResultState {
        FETCHING,
        VALID,
        INVALID
    }

    static {
        $assertionsDisabled = !ResultSetCacheEntry.class.desiredAssertionStatus();
    }

    ResultSetCacheEntry(ResultSetCacheEntryKey key, long queryId) {
        if (!$assertionsDisabled && queryId == 0) {
            throw new AssertionError();
        }
        this.key = key;
        this.queryId = queryId;
    }

    ResultSetCacheEntryKey getResultSetCacheEntryKey() {
        return this.key;
    }

    void initialize(int _numRows, ByteArray _rowData, Accessor[] _accessors, long sizeInMemory) throws SQLException {
        if (!$assertionsDisabled && this.queryResultState.get() == QueryResultState.VALID) {
            throw new AssertionError("queryResultState: " + this.queryResultState);
        }
        if (!$assertionsDisabled && _numRows < 0) {
            throw new AssertionError("_numRows: " + _numRows);
        }
        if (!$assertionsDisabled && _rowData == null) {
            throw new AssertionError("null _rowData");
        }
        if (!$assertionsDisabled && _accessors == null) {
            throw new AssertionError("null _accessors");
        }
        if (this.queryResultState.get() == QueryResultState.INVALID) {
            return;
        }
        this.numRows = _numRows;
        this.rowData = _rowData;
        this.accessorPrototypes = new AccessorPrototype[_accessors.length];
        for (int i = 0; i < _accessors.length; i++) {
            if (!$assertionsDisabled && _accessors[i] == null) {
                throw new AssertionError("null _accessor: " + i);
            }
            this.accessorPrototypes[i] = _accessors[i].newPrototype(this.numRows);
        }
        this.sizeInMemory = sizeInMemory;
        this.queryResultState.compareAndSet(QueryResultState.FETCHING, QueryResultState.VALID);
    }

    boolean isFetching() {
        return this.queryResultState.get() == QueryResultState.FETCHING;
    }

    boolean isValid() {
        return this.queryResultState.get() == QueryResultState.VALID;
    }

    boolean isInvalid() {
        return this.queryResultState.get() == QueryResultState.INVALID;
    }

    void invalidate() {
        this.queryResultState.set(QueryResultState.INVALID);
    }

    int getNumberOfRows() {
        if ($assertionsDisabled || this.queryResultState.get() != QueryResultState.FETCHING) {
            return this.numRows;
        }
        throw new AssertionError("queryResultState: " + this.queryResultState);
    }

    ByteArray getRowData() {
        if ($assertionsDisabled || this.queryResultState.get() != QueryResultState.FETCHING) {
            return this.rowData;
        }
        throw new AssertionError("queryResultState: " + this.queryResultState);
    }

    long getQueryId() {
        return this.queryId;
    }

    long getSizeInMemory() {
        return this.sizeInMemory;
    }

    Accessor[] newAccessors(OracleStatement stmt) throws SQLException {
        if (!$assertionsDisabled && this.queryResultState.get() == QueryResultState.FETCHING) {
            throw new AssertionError("queryResultState: " + this.queryResultState);
        }
        if (!$assertionsDisabled && stmt == null) {
            throw new AssertionError("null stmt");
        }
        Accessor[] acc = new Accessor[this.accessorPrototypes.length];
        for (int i = 0; i < this.accessorPrototypes.length; i++) {
            acc[i] = this.accessorPrototypes[i].newAccessor(stmt);
        }
        return acc;
    }
}
