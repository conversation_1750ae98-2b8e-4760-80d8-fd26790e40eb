package oracle.jdbc.diagnostics;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/ImmutableTraceAttributes.class */
public final class ImmutableTraceAttributes extends TraceAttributes {
    protected ImmutableTraceAttributes(String[] values) {
        super(values);
    }

    @Override // oracle.jdbc.diagnostics.TraceAttributes
    public ImmutableTraceAttributes toReadOnly() {
        return this;
    }
}
