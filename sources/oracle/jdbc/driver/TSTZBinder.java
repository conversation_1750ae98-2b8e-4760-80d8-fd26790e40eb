package oracle.jdbc.driver;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TSTZBinder.class */
class TSTZBinder extends DatumBinder {
    Binder theTSTZCopyingBinder;

    static void init(Binder x) {
        x.type = (short) 181;
        x.bytelen = 13;
    }

    TSTZBinder(byte[] val) {
        super(val);
        this.theTSTZCopyingBinder = null;
        init(this);
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theTSTZCopyingBinder == null) {
            this.theTSTZCopyingBinder = new TSTZCopyingBinder();
        }
        return this.theTSTZCopyingBinder;
    }
}
