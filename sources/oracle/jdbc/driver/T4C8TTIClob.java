package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.sql.CLOB;
import oracle.sql.CharacterSet;
import oracle.sql.Datum;
import oracle.sql.NCLOB;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTIClob.class */
final class T4C8TTIClob extends T4C8TTILob {
    private static final String CLASS_NAME = T4C8TTIClob.class.getName();
    int[] nBytes;

    T4C8TTIClob(T4CConnection _conn) {
        super(_conn);
        this.nBytes = new int[1];
    }

    long read(byte[] lobLocator, long offset, long numChars, boolean isNCLOB, char[] charOutBuffer, int _offsetInOutBuffer) throws SQLException, IOException {
        byte[] binaryReadBuffer = null;
        try {
            initializeLobdef();
            boolean varWidthChar = isLobCharsetVariableWidth(lobLocator);
            int bufferSizeNeeded = getByteBufferSizeForConversion(varWidthChar, numChars);
            binaryReadBuffer = this.connection.getByteBuffer(bufferSizeNeeded);
            this.littleEndianClob = isLobCharsetLE(lobLocator);
            this.lobops = 2L;
            this.sourceLobLocator = lobLocator;
            this.sourceOffset = offset;
            this.lobamt = numChars;
            this.sendLobamt = true;
            this.outBuffer = binaryReadBuffer;
            doRPC();
            long charsRead = this.lobamt;
            decodeNetworkCharSet(charOutBuffer, _offsetInOutBuffer, varWidthChar, isNCLOB);
            this.outBuffer = null;
            this.connection.cacheBuffer(binaryReadBuffer);
            return charsRead;
        } catch (Throwable th) {
            this.outBuffer = null;
            this.connection.cacheBuffer(binaryReadBuffer);
            throw th;
        }
    }

    boolean isLobCharsetVariableWidth(byte[] lobLocator) {
        return (lobLocator[6] & Byte.MIN_VALUE) == -128;
    }

    int getByteBufferSizeForConversion(boolean isVariableWidthCharSet, long numberOfCharsToRead) {
        if (isVariableWidthCharSet) {
            return ((int) numberOfCharsToRead) * 2;
        }
        return ((int) numberOfCharsToRead) * 3;
    }

    private boolean isLobCharsetLE(byte[] lobLocator) {
        return 64 == (lobLocator[7] & 64);
    }

    private void decodeNetworkCharSet(char[] charOutBuffer, int _offsetInOutBuffer, boolean isVariableWidthCS, boolean isNCLOB) throws SQLException {
        if (isVariableWidthCS) {
            decodeVariableWidthCharSet(charOutBuffer, _offsetInOutBuffer);
        } else {
            decodeFixedWidthCharSet(charOutBuffer, _offsetInOutBuffer, isNCLOB);
        }
    }

    private void decodeVariableWidthCharSet(char[] charOutBuffer, int _offsetInOutBuffer) throws SQLException {
        if (this.connection.versionNumber < 10101) {
            DBConversion.ucs2BytesToJavaChars(this.outBuffer, (int) this.lobBytesRead, charOutBuffer);
        } else if (this.littleEndianClob) {
            CharacterSet.convertAL16UTF16LEBytesToJavaChars(this.outBuffer, 0, charOutBuffer, _offsetInOutBuffer, (int) this.lobBytesRead, true);
        } else {
            CharacterSet.convertAL16UTF16BytesToJavaChars(this.outBuffer, 0, charOutBuffer, _offsetInOutBuffer, (int) this.lobBytesRead, true);
        }
    }

    private void decodeFixedWidthCharSet(char[] charOutBuffer, int _offsetInOutBuffer, boolean isNCLOB) throws SQLException {
        if (!isNCLOB) {
            this.nBytes[0] = (int) this.lobBytesRead;
            this.meg.conv.CHARBytesToJavaChars(this.outBuffer, 0, charOutBuffer, _offsetInOutBuffer, this.nBytes, charOutBuffer.length);
        } else {
            this.nBytes[0] = (int) this.lobBytesRead;
            this.meg.conv.NCHARBytesToJavaChars(this.outBuffer, 0, charOutBuffer, _offsetInOutBuffer, this.nBytes, charOutBuffer.length);
        }
    }

    long write(byte[] lobLocator, long offset, boolean isNCLOB, char[] _inBuffer, long offsetInBuffer, long numChars) throws SQLException, IOException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "write", "offset={0}, isNCLOB={1}, offsetInBuffer={2}, numChars={3}", (String) null, (Throwable) null, Long.valueOf(offset), Boolean.valueOf(isNCLOB), Long.valueOf(offsetInBuffer), Long.valueOf(numChars));
        validateLobOperation(lobLocator, 64, "write()");
        prepareForWrite(lobLocator, offset, isNCLOB, _inBuffer, offsetInBuffer, numChars);
        doRPC();
        return this.lobamt;
    }

    private void prepareForWrite(byte[] lobLocator, long offset, boolean isNCLOB, char[] _inBuffer, long offsetInBuffer, long numChars) throws SQLException {
        boolean varWidthChar = isLobCharsetVariableWidth(lobLocator);
        this.littleEndianClob = isLobCharsetLE(lobLocator);
        int byteBufferSize = getByteBufferSizeForConversion(varWidthChar, numChars);
        byte[] binaryWriteBuffer = new byte[byteBufferSize];
        int[] amount = {-1};
        int bytesConverted = encodeNetworkCharSet(_inBuffer, (int) offsetInBuffer, (int) numChars, binaryWriteBuffer, varWidthChar, isNCLOB, amount);
        initializeLobdef();
        this.lobops = 64L;
        this.sourceLobLocator = lobLocator;
        this.sourceOffset = offset;
        this.lobamt = amount[0] == -1 ? 0L : amount[0];
        this.sendLobamt = true;
        this.inBuffer = binaryWriteBuffer;
        this.inBufferOffset = 0L;
        this.inBufferNumBytes = bytesConverted;
    }

    int encodeNetworkCharSet(char[] inChars, int inCharsOffset, int inCharsLength, byte[] outBytes, boolean isVariableWidthCS, boolean isNCLOB, int[] amount) throws SQLException {
        if (isVariableWidthCS) {
            return encodeVariableWidthCharSet(inChars, inCharsOffset, inCharsLength, outBytes, amount);
        }
        return encodeFixedWidthCharSet(inChars, inCharsOffset, inCharsLength, outBytes, isNCLOB, amount);
    }

    private int encodeVariableWidthCharSet(char[] inChars, int inCharsOffset, int inCharsLength, byte[] outBytes, int[] amount) throws SQLException {
        int byteAmount;
        if (this.connection.versionNumber < 10101) {
            DBConversion.javaCharsToUcs2Bytes(inChars, inCharsOffset, outBytes, 0, inCharsLength);
            byteAmount = inCharsLength;
        } else if (this.littleEndianClob) {
            CharacterSet.convertJavaCharsToAL16UTF16LEBytes(inChars, inCharsOffset, outBytes, 0, inCharsLength);
            byteAmount = inCharsLength * 2;
        } else {
            CharacterSet.convertJavaCharsToAL16UTF16Bytes(inChars, inCharsOffset, outBytes, 0, inCharsLength);
            byteAmount = inCharsLength * 2;
        }
        if (amount != null && amount.length > 0) {
            amount[0] = inCharsLength;
        }
        return byteAmount;
    }

    private int encodeFixedWidthCharSet(char[] inChars, int inCharsOffset, int inCharsLength, byte[] outBytes, boolean isNCLOB, int[] amount) throws SQLException {
        short encodedCharSet;
        short databaseCharSet;
        boolean isCountingCodePoints;
        if (isNCLOB) {
            short s = this.meg.conv.serverNCharSetId;
            databaseCharSet = s;
            encodedCharSet = s;
        } else {
            encodedCharSet = this.meg.conv.clientCharSetId;
            databaseCharSet = this.meg.conv.serverCharSetId;
        }
        LobAmtUnit lobAmtUnit = LobAmtUnit.get(encodedCharSet, databaseCharSet);
        if (amount == null || amount.length == 0) {
            isCountingCodePoints = false;
        } else if (lobAmtUnit == LobAmtUnit.CODE_POINT) {
            isCountingCodePoints = true;
        } else if (lobAmtUnit == LobAmtUnit.CODE_UNIT && encodedCharSet == 2000) {
            isCountingCodePoints = false;
            amount[0] = inCharsLength;
        } else {
            isCountingCodePoints = false;
            amount[0] = -1;
        }
        return this.meg.conv.javaCharsToCHARBytes(inChars, inCharsOffset, outBytes, 0, encodedCharSet, inCharsLength, isCountingCodePoints ? amount : null);
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    Datum createTemporaryLob(Connection conn, boolean cache, int duration) throws SQLException, IOException {
        return createTemporaryLob(conn, cache, duration, (short) 1);
    }

    Datum createTemporaryLob(Connection conn, boolean cache, int duration, short form_of_use) throws SQLException, IOException {
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "createTemporaryLob", "cache={0}, duration={1}, form_of_use={2}", (String) null, (Throwable) null, Boolean.valueOf(cache), Integer.valueOf(duration), Short.valueOf(form_of_use));
        if (duration == 12) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 158).fillInStackTrace());
        }
        CLOB clob = null;
        initializeLobdef();
        this.lobops = 272L;
        int tempLobSize = getTemporaryLobSize();
        this.sourceLobLocator = new byte[tempLobSize];
        this.sourceLobLocator[1] = (byte) (tempLobSize - 2);
        if (form_of_use == 1) {
            this.sourceOffset = 1L;
        } else {
            this.sourceOffset = 2L;
        }
        this.destinationOffset = 112L;
        this.destinationLength = duration;
        this.lobamt = duration;
        this.sendLobamt = true;
        this.nullO2U = true;
        this.characterSet = form_of_use == 2 ? this.meg.conv.getNCharSetId() : this.meg.conv.getServerCharSetId();
        if (this.connection.versionNumber >= 9000) {
            this.lobscn = new int[1];
            this.lobscn[0] = cache ? 1 : 0;
            this.lobscnl = 1;
        }
        doRPC();
        if (this.sourceLobLocator != null) {
            if (form_of_use == 1) {
                clob = new CLOB((oracle.jdbc.OracleConnection) conn, this.sourceLobLocator);
            } else {
                clob = new NCLOB((oracle.jdbc.OracleConnection) conn, this.sourceLobLocator);
            }
        }
        return clob;
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean openLob(byte[] lobLocator, int mode) throws SQLException, IOException {
        int kokl_mode = 2;
        if (mode == 0) {
            kokl_mode = 1;
        }
        boolean wasOpened = openLob(lobLocator, kokl_mode, 32768);
        return wasOpened;
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean closeLob(byte[] lobLocator) throws SQLException, IOException {
        boolean wasClosed = closeLob(lobLocator, 65536);
        return wasClosed;
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean isOpenLob(byte[] lobLocator) throws SQLException, IOException {
        return isOpenLob(lobLocator, 69632);
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTIClob$LobAmtUnit.class */
    private enum LobAmtUnit {
        CODE_UNIT,
        CODE_POINT,
        UNKNOWN;

        /* JADX INFO: Access modifiers changed from: private */
        public static LobAmtUnit get(short driverCharSet, short serverCharSet) {
            if (driverCharSet != serverCharSet) {
                return UNKNOWN;
            }
            switch (driverCharSet) {
                case 1:
                case 2:
                case 31:
                case 46:
                case 178:
                case 871:
                case 873:
                    return CODE_POINT;
                case 2000:
                    return CODE_UNIT;
                default:
                    return UNKNOWN;
            }
        }
    }
}
