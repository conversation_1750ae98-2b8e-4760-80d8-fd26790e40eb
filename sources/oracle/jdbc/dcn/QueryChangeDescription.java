package oracle.jdbc.dcn;

import oracle.jdbc.dcn.DatabaseChangeEvent;

/* loaded from: ojdbc8.jar:oracle/jdbc/dcn/QueryChangeDescription.class */
public interface QueryChangeDescription {
    long getQueryId();

    QueryChangeEventType getQueryChangeEventType();

    TableChangeDescription[] getTableChangeDescription();

    /* loaded from: ojdbc8.jar:oracle/jdbc/dcn/QueryChangeDescription$QueryChangeEventType.class */
    public enum QueryChangeEventType {
        DEREG(DatabaseChangeEvent.EventType.DEREG.getCode()),
        QUERYCHANGE(DatabaseChangeEvent.EventType.QUERYCHANGE.getCode());

        private final int code;

        QueryChangeEventType(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }

        public static final QueryChangeEventType getQueryChangeEventType(int eventType) {
            if (eventType == DEREG.getCode()) {
                return DEREG;
            }
            return QUERYCHANGE;
        }
    }
}
