package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.sql.CharacterSet;
import oracle.sql.Datum;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/StringBinder.class */
class StringBinder extends VarcharBinder {
    private static final String CLASS_NAME = StringBinder.class.getName();

    StringBinder(String val) {
        super(val);
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        int l;
        int convertedBytesLengthActual;
        String value = this.paramVal;
        if (clearPriorBindValues) {
            this.paramVal = null;
        }
        if (value == null) {
            bindIndicators[indoffset] = -1;
            if (bindUseDBA) {
                bindDataOffsets[bindDataIndex] = -1;
                bindDataLengths[bindDataIndex] = 0;
            }
        } else {
            bindIndicators[indoffset] = 0;
            int l2 = value.length();
            if (bindUseDBA) {
                long pos = bindData.getPosition();
                bindDataOffsets[bindDataIndex] = pos;
                stmt.lastBoundDataOffsets[bindPosition] = pos;
                CharacterSet targetCS = stmt.getCharacterSetForBind(bindPosition, (short) formOfUse);
                if (targetCS.getOracleId() == 1 && !stmt.connection.isStrictAsciiConversion) {
                    convertedBytesLengthActual = bindData.putAsciiString(value);
                } else {
                    convertedBytesLengthActual = bindData.putStringWithReplacement(value, targetCS);
                }
                bindDataLengths[bindDataIndex] = convertedBytesLengthActual;
                stmt.debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "bind", "lastBoundDataLengths={0}. ", (String) null, (String) null, (Object) Integer.valueOf(convertedBytesLengthActual));
                stmt.lastBoundDataLengths[bindPosition] = convertedBytesLengthActual;
                l = convertedBytesLengthActual;
            } else {
                value.getChars(0, l2, bindChars, charoffset + 1);
                l = l2 << 1;
                bindChars[charoffset] = (char) l;
            }
            if (l > 65532) {
                bindIndicators[lenoffset] = -2;
            } else {
                bindIndicators[lenoffset] = (short) (l + 2);
            }
        }
        return localCheckSum;
    }

    @Override // oracle.jdbc.driver.Binder
    Datum getDatum(OraclePreparedStatement stmt, int bindPosition, int formOfUse, int internalType) throws SQLException {
        String value = this.paramVal;
        CharacterSet targetCS = stmt.getCharacterSetForBind(bindPosition, (short) formOfUse);
        if (targetCS.getOracleId() == 1 && !stmt.connection.isStrictAsciiConversion) {
            if (value == null || value.length() == 0) {
                return null;
            }
            int length = value.length();
            byte[] asciiBytes = new byte[length];
            for (int i = 0; i < length; i++) {
                asciiBytes[i] = (byte) value.charAt(i);
            }
            return SQLUtil.makeDatum(stmt.connection, asciiBytes, internalType, (String) null, 0);
        }
        return SQLUtil.makeDatum(stmt.connection, targetCS.convertWithReplacement(value), internalType, (String) null, 0);
    }
}
