package oracle.jdbc.driver;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CLongAccessor.class */
class T2CLongAccessor extends LongAccessor {
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T2CLongAccessor.class.desiredAssertionStatus();
    }

    T2CLongAccessor(OracleStatement stmt, int column_pos, int max_len, short form, int external_type, boolean isOutBind) throws SQLException {
        super(stmt, column_pos, max_len, form, external_type, isOutBind, false);
    }

    T2CLongAccessor(OracleStatement stmt, int column_pos, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(stmt, column_pos, max_len, nullable, flags, precision, scale, contflag, total_elems, form);
    }

    @Override // oracle.jdbc.driver.LongAccessor, oracle.jdbc.driver.Accessor
    byte[] getBytesInternal(int currentRow) throws SQLException {
        if (this.statement.isFetchStreams) {
            if (!$assertionsDisabled && isNull(currentRow)) {
                throw new AssertionError();
            }
            int len = getLength(currentRow);
            long off = getOffset(currentRow);
            return this.rowData.get(off, len);
        }
        return super.getBytesInternal(currentRow);
    }

    @Override // oracle.jdbc.driver.LongAccessor, oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    InputStream getAsciiStream(int currentRow) throws SQLException, IOException {
        if (this.statement.isFetchStreams) {
            if (isNull(currentRow)) {
                return null;
            }
            InputStream is = new ByteArrayInputStream(this.rowData.get(getOffset(currentRow), getLength(currentRow)));
            try {
                return this.statement.connection.conversion.ConvertStream(is, 0, this.statement.connection);
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                    }
                }
            }
        }
        return super.getAsciiStream(currentRow);
    }

    @Override // oracle.jdbc.driver.LongAccessor, oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    InputStream getUnicodeStream(int currentRow) throws SQLException, IOException {
        if (this.statement.isFetchStreams) {
            if (isNull(currentRow)) {
                return null;
            }
            InputStream is = new ByteArrayInputStream(this.rowData.get(getOffset(currentRow), getLength(currentRow)));
            try {
                return this.statement.connection.conversion.ConvertStream(is, 1, this.statement.connection);
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                    }
                }
            }
        }
        return super.getUnicodeStream(currentRow);
    }

    @Override // oracle.jdbc.driver.LongAccessor, oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Reader getCharacterStream(int currentRow) throws SQLException, IOException {
        if (this.statement.isFetchStreams) {
            if (isNull(currentRow)) {
                return null;
            }
            InputStream is = new ByteArrayInputStream(this.rowData.get(getOffset(currentRow), getLength(currentRow)));
            try {
                return this.statement.connection.conversion.ConvertCharacterStream(is, 9, this.formOfUse, this.statement.connection);
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                    }
                }
            }
        }
        return super.getCharacterStream(currentRow);
    }

    @Override // oracle.jdbc.driver.LongAccessor, oracle.jdbc.driver.CharCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    InputStream getBinaryStream(int currentRow) throws SQLException, IOException {
        if (this.statement.isFetchStreams) {
            if (isNull(currentRow)) {
                return null;
            }
            InputStream is = new ByteArrayInputStream(this.rowData.get(getOffset(currentRow), getLength(currentRow)));
            try {
                return this.statement.connection.conversion.ConvertStream(is, 6, this.statement.connection);
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                    }
                }
            }
        }
        return super.getBinaryStream(currentRow);
    }

    void copyStreamDataIntoDBA(int currentRow) throws SQLException {
        if (this.stream.closed) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 27).fillInStackTrace());
        }
        ByteArrayOutputStream outs = null;
        try {
            outs = new ByteArrayOutputStream(1024);
            byte[] buffer = this.statement.connection.getByteBuffer(32768);
            while (true) {
                try {
                    int length = this.stream.read(buffer, 0, 32768);
                    if (length == -1) {
                        break;
                    } else {
                        outs.write(buffer, 0, length);
                    }
                } catch (IOException e) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), e).fillInStackTrace());
                }
            }
            this.statement.connection.cacheBuffer(buffer);
            byte[] b = outs.toByteArray();
            if (outs != null) {
                try {
                    outs.close();
                } catch (IOException e2) {
                }
            }
            if (b == null || b.length == 0) {
                setLengthAndNull(currentRow, 0);
                return;
            }
            setOffset(currentRow);
            setLengthAndNull(currentRow, b.length);
            this.rowData.put(b);
        } catch (Throwable th) {
            if (outs != null) {
                try {
                    outs.close();
                } catch (IOException e3) {
                    throw th;
                }
            }
            throw th;
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    Accessor copyForDefine(OracleStatement dest) {
        LongAccessor a = (LongAccessor) super.copyForDefine(dest);
        try {
            a.stream = dest.connection.driverExtension.createInputStream(dest, this.columnPosition, a);
        } catch (SQLException e) {
        }
        return a;
    }
}
