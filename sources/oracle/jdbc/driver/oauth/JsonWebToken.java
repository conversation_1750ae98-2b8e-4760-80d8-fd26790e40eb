package oracle.jdbc.driver.oauth;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.sql.SQLException;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.function.Supplier;
import oracle.jdbc.AccessToken;
import oracle.jdbc.internal.OpaquePrivateKey;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonFactory;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/oauth/JsonWebToken.class */
public final class JsonWebToken extends OpaqueAccessToken {
    private JsonWebToken(@Blind OpaqueString token, OffsetDateTime expiration, @Blind OpaquePrivateKey opaquePrivateKey) {
        super(token, expiration, opaquePrivateKey);
    }

    @Blind
    static JsonWebToken fromOciFile(Path path) throws SQLException, InvalidKeySpecException, NoSuchAlgorithmException, IOException {
        char[] tokenChars = readTokenFile(path.resolve("token"));
        try {
            OffsetDateTime expiration = parseExp(tokenChars);
            JsonWebToken jsonWebToken = new JsonWebToken(OpaqueString.newOpaqueString(tokenChars), expiration, OpaquePrivateKey.fromPemFile(path.resolve("oci_db_key.pem")));
            Arrays.fill(tokenChars, (char) 0);
            return jsonWebToken;
        } catch (Throwable th) {
            Arrays.fill(tokenChars, (char) 0);
            throw th;
        }
    }

    @Blind
    static JsonWebToken fromFile(Path path) throws IOException {
        char[] tokenFile;
        if (Files.isDirectory(path, new LinkOption[0])) {
            tokenFile = readTokenFile(path.resolve("token"));
        } else {
            tokenFile = readTokenFile(path);
        }
        char[] tokenChars = tokenFile;
        try {
            OffsetDateTime expiration = parseExp(tokenChars);
            JsonWebToken jsonWebToken = new JsonWebToken(OpaqueString.newOpaqueString(tokenChars), expiration, null);
            Arrays.fill(tokenChars, (char) 0);
            return jsonWebToken;
        } catch (Throwable th) {
            Arrays.fill(tokenChars, (char) 0);
            throw th;
        }
    }

    @Blind
    public static JsonWebToken createProofOfPossessionToken(@Blind char[] token, @Blind PrivateKey privateKey) throws InvalidKeySpecException, NoSuchAlgorithmException {
        return new JsonWebToken(OpaqueString.newOpaqueString((char[]) token.clone()), parseExp(token), OpaquePrivateKey.fromPrivateKey(privateKey));
    }

    @Blind
    public static JsonWebToken createBearerToken(@Blind char[] token) {
        return new JsonWebToken(OpaqueString.newOpaqueString((char[]) token.clone()), parseExp(token), null);
    }

    @Blind
    private static char[] readTokenFile(Path path) throws IOException {
        requireValidSize(Files.size(path));
        byte[] fileBytes = Files.readAllBytes(path);
        try {
            Charset charset = detectCharacterSet(fileBytes);
            CharBuffer fileBuffer = charset.decode(ByteBuffer.wrap(fileBytes));
            List<CharBuffer> lines = new ArrayList<>(1);
            int length = splitLines(fileBuffer, lines);
            try {
                char[] tokenChars = new char[length];
                CharBuffer tokenBuffer = CharBuffer.wrap(tokenChars);
                for (CharBuffer line : lines) {
                    tokenBuffer.put(line);
                }
                fileBuffer.clear();
                fileBuffer.put(new char[fileBuffer.remaining()]);
                Arrays.fill(fileBytes, (byte) 0);
                return tokenChars;
            } catch (Throwable th) {
                fileBuffer.clear();
                fileBuffer.put(new char[fileBuffer.remaining()]);
                throw th;
            }
        } catch (Throwable th2) {
            Arrays.fill(fileBytes, (byte) 0);
            throw th2;
        }
    }

    private static int splitLines(CharBuffer charBuffer, List<CharBuffer> lines) {
        int start = charBuffer.position();
        while (start < charBuffer.limit() && isNewLine(charBuffer, start)) {
            start++;
        }
        int length = 0;
        for (int i = start; i < charBuffer.limit(); i++) {
            if (isNewLine(charBuffer, i)) {
                CharBuffer line = CharBuffer.wrap(charBuffer, start, i);
                length += line.remaining();
                lines.add(line);
                start = i + 1;
            }
        }
        if (start < charBuffer.limit()) {
            CharBuffer line2 = CharBuffer.wrap(charBuffer, start, charBuffer.limit());
            length += line2.remaining();
            lines.add(line2);
        }
        return length;
    }

    private static boolean isNewLine(CharBuffer charBuffer, int position) {
        char charValue = charBuffer.get(position);
        return charValue == '\r' || charValue == '\n';
    }

    private static Charset detectCharacterSet(@Blind byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return StandardCharsets.UTF_8;
        }
        if (bytes.length % 2 != 0) {
            return StandardCharsets.UTF_8;
        }
        if (bytes[0] == -2 && bytes[1] == -1) {
            return StandardCharsets.UTF_16BE;
        }
        if (bytes[0] == -1 && bytes[1] == -2) {
            return StandardCharsets.UTF_16LE;
        }
        for (int i = 0; i < bytes.length && bytes[i] == 0; i += 2) {
            if (i == bytes.length - 2) {
                return StandardCharsets.UTF_16BE;
            }
        }
        for (int i2 = 0; i2 < bytes.length && bytes[i2 + 1] == 0; i2 += 2) {
            if (i2 == bytes.length - 2) {
                return StandardCharsets.UTF_16LE;
            }
        }
        return StandardCharsets.UTF_8;
    }

    private static OffsetDateTime parseExp(@Blind char[] jwt) {
        OracleJsonValue exp = parseClaims(jwt).get("exp");
        if (exp == null) {
            throw new IllegalArgumentException("JWT is missing an exp claim");
        }
        if (!(exp instanceof OracleJsonNumber)) {
            throw new IllegalArgumentException("JWT has an exp claim with a non-numeric value of type: " + exp.getOracleJsonType());
        }
        return Instant.ofEpochSecond(exp.asJsonNumber().longValue()).atOffset(ZoneOffset.UTC);
    }

    /* JADX WARN: Finally extract failed */
    /* JADX WARN: Multi-variable type inference failed */
    private static OracleJsonObject parseClaims(@Blind char[] jwt) {
        requireValidSize(jwt.length);
        int start = 0;
        while (start < jwt.length && jwt[start] != '.') {
            start++;
        }
        int start2 = start + 1;
        if (start2 > jwt.length) {
            throw new IllegalArgumentException("Failed to identify payload of JWT");
        }
        int end = start2;
        while (end < jwt.length && jwt[end] != '.') {
            end++;
        }
        if (end == jwt.length) {
            throw new IllegalArgumentException("Failed to identify payload of JWT");
        }
        byte[] base64Payload = new byte[end - start2];
        int i = 0;
        while (i < base64Payload.length) {
            try {
                base64Payload[i] = (byte) jwt[i + start2];
                i++;
            } catch (Throwable th) {
                Arrays.fill(base64Payload, (byte) 0);
                throw th;
            }
        }
        try {
            byte[] jsonPayload = Base64.getUrlDecoder().decode(base64Payload);
            try {
                try {
                    ByteArrayInputStream inputStream = new ByteArrayInputStream(jsonPayload);
                    Throwable th2 = null;
                    try {
                        OracleJsonObject oracleJsonObjectAsJsonObject = new OracleJsonFactory().createJsonTextValue(inputStream).asJsonObject();
                        if (inputStream != null) {
                            if (0 != 0) {
                                try {
                                    inputStream.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                inputStream.close();
                            }
                        }
                        Arrays.fill(jsonPayload, (byte) 0);
                        Arrays.fill(base64Payload, (byte) 0);
                        return oracleJsonObjectAsJsonObject;
                    } catch (Throwable th4) {
                        if (inputStream != null) {
                            if (0 != 0) {
                                try {
                                    inputStream.close();
                                } catch (Throwable th5) {
                                    th2.addSuppressed(th5);
                                }
                            } else {
                                inputStream.close();
                            }
                        }
                        throw th4;
                    }
                } catch (IOException ioException) {
                    throw new IllegalArgumentException("Failed to read JWT payload", ioException);
                }
            } catch (ClassCastException | OracleJsonException exception) {
                throw new IllegalArgumentException("JWT payload is not JSON", exception);
            }
        } catch (Throwable th6) {
            Arrays.fill((byte[]) i, (byte) 0);
            throw th6;
        }
    }

    private static void requireValidSize(long size) {
        if (size > 16000) {
            throw new IllegalArgumentException("JWT of size " + size + " bytes exceeds the maximum accepted length of 16kb");
        }
    }

    public static AccessTokenCache<JsonWebToken> createCache(Supplier<? extends AccessToken> tokenSupplier) {
        return AccessTokenCache.create(() -> {
            AccessToken accessToken = (AccessToken) tokenSupplier.get();
            if (!(accessToken instanceof JsonWebToken)) {
                throw new IllegalArgumentException("token supplier has output an unrecognized object type: " + accessToken.getClass());
            }
            return (JsonWebToken) accessToken;
        });
    }

    OracleJsonObject getClaims() {
        return (OracleJsonObject) token().map(JsonWebToken::parseClaims);
    }
}
