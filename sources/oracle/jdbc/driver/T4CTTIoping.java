package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoping.class */
final class T4CTTIoping extends T4CTTIfun {
    T4CTTIoping(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 147);
    }

    void doOPING() throws SQLException, IOException {
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
