package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIokeyval.class */
final class T4CTTIokeyval extends T4CTTIfun {
    static final byte KVASET_KPDUSR = 1;
    static final byte KVACLA_KPDUSR = 2;
    static final int KVALDF_KVALKYL_MAX = 128;
    static final int KVALDF_KVALVLL_MAX = 65536;
    private byte[] namespaceByteArr;
    private char[] charArr;
    private byte[][] attrArr;
    private int[] attrArrSize;
    private byte[][] valueArr;
    private int[] valueArrSize;
    private byte[] kvalflg;
    private int nbNamespaceBytes;
    private int nbKeyVal;
    private boolean clear;

    /* JADX WARN: Type inference failed for: r1v11, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v7, types: [byte[], byte[][]] */
    T4CTTIokeyval(T4CConnection _conn) {
        super(_conn, (byte) 17);
        setFunCode((short) 154);
        this.namespaceByteArr = new byte[100];
        this.charArr = new char[100];
        this.attrArr = new byte[10];
        this.attrArrSize = new int[10];
        this.valueArr = new byte[10];
        this.valueArrSize = new int[10];
        this.kvalflg = new byte[10];
    }

    /* JADX WARN: Type inference failed for: r1v66, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v72, types: [byte[], byte[][]] */
    void doOKEYVAL(Namespace namespace) throws SQLException, IOException {
        String namespaceStr = namespace.name;
        String[] keys = namespace.keys;
        String[] values = namespace.values;
        this.clear = namespace.clear;
        this.nbKeyVal = namespace.nbPairs;
        int namespaceByteArrSize = namespaceStr.length() * this.meg.conv.cMaxCharSize;
        if (namespaceByteArrSize > this.namespaceByteArr.length) {
            this.namespaceByteArr = new byte[namespaceByteArrSize];
        }
        if (namespaceStr.length() > this.charArr.length) {
            this.charArr = new char[namespaceStr.length()];
        }
        namespaceStr.getChars(0, namespaceStr.length(), this.charArr, 0);
        this.nbNamespaceBytes = this.meg.conv.javaCharsToCHARBytes(this.charArr, 0, this.namespaceByteArr, 0, namespaceStr.length());
        if (this.nbKeyVal > 0) {
            if (this.nbKeyVal > this.attrArr.length) {
                this.attrArr = new byte[this.nbKeyVal];
                this.attrArrSize = new int[this.nbKeyVal];
                this.valueArr = new byte[this.nbKeyVal];
                this.valueArrSize = new int[this.nbKeyVal];
                this.kvalflg = new byte[this.nbKeyVal];
            }
            for (int i = 0; i < this.nbKeyVal; i++) {
                String attr = keys[i];
                String val = values[i];
                int attrByteArrSize = attr.length() * this.meg.conv.cMaxCharSize;
                if (this.attrArr[i] == null || this.attrArr[i].length < attrByteArrSize) {
                    this.attrArr[i] = new byte[attrByteArrSize];
                }
                int valueByteArrSize = val.length() * this.meg.conv.cMaxCharSize;
                if (this.valueArr[i] == null || this.valueArr[i].length < valueByteArrSize) {
                    this.valueArr[i] = new byte[valueByteArrSize];
                }
                if (attr.length() > this.charArr.length) {
                    this.charArr = new char[attr.length()];
                }
                attr.getChars(0, attr.length(), this.charArr, 0);
                this.attrArrSize[i] = this.meg.conv.javaCharsToCHARBytes(this.charArr, 0, this.attrArr[i], 0, attr.length());
                if (this.attrArrSize[i] > 128) {
                    oracle.jdbc.internal.OracleConnection connectionDuringExceptionHandling = getConnectionDuringExceptionHandling();
                    Object[] objArr = new Object[4];
                    objArr[0] = attr.length() > 128 ? attr.substring(0, 128) : attr;
                    objArr[1] = Integer.valueOf(this.attrArrSize[i]);
                    objArr[2] = Integer.valueOf(this.meg.conv.cMaxCharSize);
                    objArr[3] = Integer.valueOf(attr.length());
                    throw ((SQLException) DatabaseError.createSqlException(connectionDuringExceptionHandling, DatabaseError.TTC0005, String.format("[%s] length %d, %d, %d", objArr)).fillInStackTrace());
                }
                if (val.length() > this.charArr.length) {
                    this.charArr = new char[val.length()];
                }
                val.getChars(0, val.length(), this.charArr, 0);
                this.valueArrSize[i] = this.meg.conv.javaCharsToCHARBytes(this.charArr, 0, this.valueArr[i], 0, val.length());
                if (this.valueArrSize[i] > 65536) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0005, String.format("[%s] value length %d, %d, %d", attr, Integer.valueOf(this.valueArrSize[i]), Integer.valueOf(this.meg.conv.cMaxCharSize), Integer.valueOf(val.length()))).fillInStackTrace());
                }
            }
        }
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalPTR();
        this.meg.marshalUB4(this.nbNamespaceBytes);
        if (this.nbKeyVal > 0) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalUB4(this.nbKeyVal);
        int flag = 0;
        if (this.nbKeyVal > 0) {
            flag = 1;
        }
        if (this.clear) {
            flag |= 2;
        }
        this.meg.marshalUB2(flag);
        this.meg.marshalNULLPTR();
        this.meg.marshalCHR(this.namespaceByteArr, 0, this.nbNamespaceBytes);
        if (this.nbKeyVal > 0) {
            this.meg.marshalKEYVAL(this.attrArr, this.attrArrSize, this.valueArr, this.valueArrSize, this.kvalflg, this.nbKeyVal);
        }
    }
}
