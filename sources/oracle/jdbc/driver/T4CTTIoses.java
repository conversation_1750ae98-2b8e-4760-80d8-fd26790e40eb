package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoses.class */
final class T4CTTIoses extends T4CTTIfun {
    static final int OSESSWS = 1;
    static final int OSESDET = 3;
    private int sididx;
    private int sidser;
    private int sidopc;

    T4CTTIoses(T4CConnection _conn) {
        super(_conn, (byte) 17);
        setFunCode((short) 107);
    }

    void doO80SES(int _sididx, int _sidser, int _sidopc) throws SQLException, IOException {
        this.sididx = _sididx;
        this.sidser = _sidser;
        this.sidopc = _sidopc;
        if (this.sidopc != 1 && this.sidopc != 3) {
            throw new SQLException("Wrong operation : can only do switch or detach");
        }
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.sididx);
        this.meg.marshalUB4(this.sidser);
        this.meg.marshalUB4(this.sidopc);
    }
}
