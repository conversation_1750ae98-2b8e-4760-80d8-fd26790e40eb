package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_zh_CN.class */
public class ErrorMessagesJson_zh_CN extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "出现 I/O 异常错误"}, new Object[]{"26302", "不支持年份 \"{0}\""}, new Object[]{"26303", "溢出，值太大：{0}。"}, new Object[]{"26304", "不支持此选项（未实施）"}, new Object[]{"26305", "二进制 JSON 无效或损坏。"}, new Object[]{"26306", "不支持此二进制 JSON 版本：{0}。"}, new Object[]{"26307", "UTF-8 编码键长度不能大于 256 字节。以下键超过了此限制：\"{0}\"。"}, new Object[]{"26308", "指定的 JSON 太大，无法编码为二进制 JSON。编码的图像大小不能超过 2GB。"}, new Object[]{"26309", "二进制 JSON 无效或损坏。指定的图像仅包含 {0} 个字节。"}, new Object[]{"26310", "指定的 java.time.Period 设置了天数，但是 Oracle 年到月时间间隔不支持天数。"}, new Object[]{"26311", "生成器在结束之前关闭。"}, new Object[]{"26312", "在此上下文中必须指定对象键。"}, new Object[]{"26313", "写入无效。已写入完整值。"}, new Object[]{"26314", "在此上下文中不允许结束。"}, new Object[]{"26315", "在此上下文中不允许使用键。"}, new Object[]{"26316", "键后需要有值。"}, new Object[]{"26317", "语法分析器状态必须为 {0}。"}, new Object[]{"26318", "语法分析器状态不能为 {0}。"}, new Object[]{"26319", "必须对值使用语法分析器。"}, new Object[]{"26320", "\"{0}\" 不是支持的包装类型。"}, new Object[]{"26321", "无法修改此对象。要创建可修改副本，请使用 OracleJsonFactory.createObject(OracleJsonObject)。"}, new Object[]{"26322", "无法修改此数组。要创建可修改副本，请使用 OracleJsonFactory.createArray(OracleJsonArray)。"}, new Object[]{"26323", "JSON 对象包含重复的键：{0}。"}, new Object[]{"26324", "无法自动检测编码，字符数不够。"}, new Object[]{"26325", "需要 EOF 标记，但得到的是 {0}。"}, new Object[]{"26326", "第 {1} 行、第 {2} 列包含意外的字符 {0}。"}, new Object[]{"26327", "第 {1} 行、第 {2} 列包含意外的字符 {0}。应为 {3}。"}, new Object[]{"26328", "第 {1} 行、第 {2} 列包含无效标记 {0}。标记应为：{3}。"}, new Object[]{"26329", "JsonParser#getString() 仅对 KEY_NAME、VALUE_STRING、VALUE_NUMBER 语法分析器状态有效。但当前语法分析器状态为 {0}。"}, new Object[]{"26330", "JsonParser#isIntegralNumber() 仅对 VALUE_NUMBER 语法分析器状态有效。但当前语法分析器状态为 {0}。"}, new Object[]{"26331", "JsonParser#getInt() 仅对 VALUE_NUMBER 语法分析器状态有效。但当前语法分析器状态为 {0}。"}, new Object[]{"26332", "JsonParser#getLong() 仅对 VALUE_NUMBER 语法分析器状态有效。但当前语法分析器状态为 {0}。"}, new Object[]{"26333", "JsonParser#getBigDecimal() 仅对 VALUE_NUMBER 语法分析器状态有效。但当前语法分析器状态为 {0}。"}, new Object[]{"26334", "JsonParser#getArray() 仅对 START_ARRAY 语法分析器状态有效。但当前语法分析器状态为 {0}。"}, new Object[]{"26335", "JsonParser#getObject() 仅对 START_OBJECT 语法分析器状态有效。但当前语法分析器状态为 {0}。"}, new Object[]{"26336", "不支持带区域的时间戳。仅支持含偏移量的时区。"}, new Object[]{"26337", "JSON 值中的对象和数组嵌套深度不能超过 {0} 级"}, new Object[]{"26338", "JSON 对象的键不能超过 65535 字节"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
