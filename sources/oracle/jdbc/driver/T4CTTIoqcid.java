package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoqcid.class */
final class T4CTTIoqcid extends T4CTTIfun {
    byte[] cacheId;
    long registrationId;

    T4CTTIoqcid(T4CConnection _conn) {
        super(_conn, (byte) 17);
        this.cacheId = null;
        this.registrationId = 0L;
        setFunCode((short) 168);
    }

    void doOQCID(byte[] cacheId, long registrationId) throws SQLException, IOException {
        this.cacheId = cacheId;
        this.registrationId = registrationId;
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalSB8(this.registrationId);
        this.meg.marshalPTR();
        this.meg.marshalUB4(this.cacheId.length);
        this.meg.marshalB1Array(this.cacheId);
    }
}
