package oracle.jdbc.driver;

import java.sql.SQLException;
import java.sql.Time;
import java.util.Arrays;
import oracle.sql.Datum;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TimeBinder.class */
class TimeBinder extends DateCommonBinder {
    Binder theTimeCopyingBinder = null;
    Time paramVal;

    static void init(Binder x) {
        x.type = (short) 12;
        x.bytelen = 7;
    }

    TimeBinder(Time x) {
        init(this);
        this.paramVal = x;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theTimeCopyingBinder == null) {
            this.theTimeCopyingBinder = new TimeCopyingBinder();
        }
        return this.theTimeCopyingBinder;
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        Time value = this.paramVal;
        byte[] b = null;
        int len = 7;
        if (clearPriorBindValues) {
            this.paramVal = null;
        }
        if (value == null) {
            bindIndicators[indoffset] = -1;
            if (bindUseDBA) {
                bindDataOffsets[bindDataIndex] = -1;
                bindDataLengths[bindDataIndex] = 0;
            }
        } else {
            if (bindUseDBA) {
                long pos = bindData.getPosition();
                bindDataOffsets[bindDataIndex] = pos;
                stmt.lastBoundDataOffsets[bindPosition] = pos;
                b = stmt.connection.methodTempLittleByteBuffer;
                byteoffset = 0;
            } else {
                b = bindBytes;
            }
            bindIndicators[indoffset] = 0;
            len = getDatumBytes(stmt, value, b, byteoffset);
        }
        if (bindUseDBA) {
            bindData.put(b, 0, len);
            bindDataLengths[bindDataIndex] = len;
            stmt.lastBoundDataLengths[bindPosition] = len;
            bindIndicators[lenoffset] = (short) len;
        } else {
            bindIndicators[lenoffset] = (short) bytePitch;
        }
        if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
            if (bindIndicators[indoffset] == -1) {
                localCheckSum = CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
            } else {
                localCheckSum = CRC64.updateChecksum(localCheckSum, value.getTime());
            }
        }
        return localCheckSum;
    }

    private int getDatumBytes(OraclePreparedStatement stmt, Time value, byte[] b, int byteoffset) throws SQLException {
        setOracleHMS(setOracleCYMD(value.getTime(), b, byteoffset, stmt), b, byteoffset);
        b[0 + byteoffset] = 119;
        b[2 + byteoffset] = 1;
        b[3 + byteoffset] = 1;
        if (stmt.connection.use1900AsYearForTime) {
            b[1 + byteoffset] = 100;
        } else {
            b[1 + byteoffset] = -86;
        }
        return 7;
    }

    @Override // oracle.jdbc.driver.Binder
    Datum getDatum(OraclePreparedStatement stmt, int bindPosition, int formOfUse, int internalType) throws SQLException {
        byte[] b = stmt.connection.methodTempLittleByteBuffer;
        int len = getDatumBytes(stmt, this.paramVal, b, 0);
        return SQLUtil.makeDatum(stmt.connection, Arrays.copyOf(b, len), internalType, (String) null, 0);
    }
}
