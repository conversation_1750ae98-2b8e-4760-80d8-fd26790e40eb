package oracle.jdbc.datasource.impl;

import javax.net.ssl.SSLContext;
import oracle.jdbc.OraclePooledConnectionBuilder;
import oracle.jdbc.OracleShardingKey;
import oracle.jdbc.datasource.OraclePooledConnection;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.logging.annotations.Blind;
import org.ietf.jgss.GSSCredential;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/impl/OraclePooledConnectionBuilderImpl.class */
public abstract class OraclePooledConnectionBuilderImpl extends AbstractConnectionBuilder<OraclePooledConnectionBuilderImpl, OraclePooledConnection> implements OraclePooledConnectionBuilder {
    @Override // oracle.jdbc.OraclePooledConnectionBuilder
    public /* bridge */ /* synthetic */ OraclePooledConnectionBuilder readOnlyInstanceAllowed(boolean z) {
        return (OraclePooledConnectionBuilder) super.readOnlyInstanceAllowed(z);
    }

    @Override // oracle.jdbc.OraclePooledConnectionBuilder
    public /* bridge */ /* synthetic */ OraclePooledConnectionBuilder sslContext(SSLContext sSLContext) {
        return (OraclePooledConnectionBuilder) super.sslContext(sSLContext);
    }

    @Override // oracle.jdbc.OraclePooledConnectionBuilder
    public /* bridge */ /* synthetic */ OraclePooledConnectionBuilder gssCredential(GSSCredential gSSCredential) {
        return (OraclePooledConnectionBuilder) super.gssCredential(gSSCredential);
    }

    @Override // oracle.jdbc.OraclePooledConnectionBuilder
    public /* bridge */ /* synthetic */ OraclePooledConnectionBuilder superShardingKey(OracleShardingKey oracleShardingKey) {
        return (OraclePooledConnectionBuilder) super.superShardingKey(oracleShardingKey);
    }

    @Override // oracle.jdbc.OraclePooledConnectionBuilder
    public /* bridge */ /* synthetic */ OraclePooledConnectionBuilder shardingKey(OracleShardingKey oracleShardingKey) {
        return (OraclePooledConnectionBuilder) super.shardingKey(oracleShardingKey);
    }

    @Override // oracle.jdbc.OraclePooledConnectionBuilder
    public /* bridge */ /* synthetic */ OraclePooledConnectionBuilder password(@Blind String str) {
        return (OraclePooledConnectionBuilder) super.password(str);
    }

    @Override // oracle.jdbc.OraclePooledConnectionBuilder
    public /* bridge */ /* synthetic */ OraclePooledConnectionBuilder user(String str) {
        return (OraclePooledConnectionBuilder) super.user(str);
    }
}
