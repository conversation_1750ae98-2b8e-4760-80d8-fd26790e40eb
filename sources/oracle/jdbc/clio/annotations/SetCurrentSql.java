package oracle.jdbc.clio.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.CLASS)
/* loaded from: ojdbc8.jar:oracle/jdbc/clio/annotations/SetCurrentSql.class */
public @interface SetCurrentSql {

    /* loaded from: ojdbc8.jar:oracle/jdbc/clio/annotations/SetCurrentSql$Source.class */
    public enum Source {
        PARAMETER,
        FIELD,
        METHOD
    }

    Source source() default Source.METHOD;

    String sourceName() default "getSql";
}
