package oracle.jdbc.driver;

import java.util.Map;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8Kpcdsc.class */
class T4C8Kpcdsc {
    int size_kpcdsc;
    int dty_kpcdsc;
    String name_kpcdsc;
    int precision_kpcdsc;
    int scale_kpcdsc;
    boolean isnull_kpcdsc;
    short charsetform_kpcdsc;
    String typnm_kpcdsc;
    String domname_kpcdsc;
    String domsch_kpcdsc;
    private Map<String, String> annotations;

    T4C8Kpcdsc(int _size_kpcdsc, int _dty_kpcdsc, String _name_kpcdsc, short _precision_kpcdsc, short _scale_kpcdsc, short _isnull_kpcdsc, short _charsetform_kpcdsc, String _typnm_kpcdsc, String domainName, String domainSchema, Map<String, String> annotations) {
        this.size_kpcdsc = _size_kpcdsc;
        this.dty_kpcdsc = _dty_kpcdsc;
        this.name_kpcdsc = _name_kpcdsc;
        this.precision_kpcdsc = _precision_kpcdsc;
        this.scale_kpcdsc = _scale_kpcdsc;
        this.isnull_kpcdsc = _isnull_kpcdsc == 1;
        this.charsetform_kpcdsc = _charsetform_kpcdsc;
        this.typnm_kpcdsc = _typnm_kpcdsc;
        this.domname_kpcdsc = domainName;
        this.domsch_kpcdsc = domainSchema;
        this.annotations = annotations;
    }

    Map<String, String> getAnnotations() {
        return this.annotations;
    }
}
