package oracle.jdbc.driver.parser;

import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;
import java.util.TreeMap;
import oracle.jdbc.driver.parser.Parser;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/Matrix.class */
public class Matrix {
    public Parser parser;
    public Queue<Long> completionQueue = new LinkedList();
    private Map<Integer, Parser.EarleyCell>[] cells = null;
    private int lastY = 0;
    public int[] allXs = null;
    public Integer LAsuspect = null;

    public void enqueue(long candidate) {
        this.completionQueue.add(Long.valueOf(candidate));
    }

    public long dequeue() {
        if (this.completionQueue.isEmpty()) {
            return -1L;
        }
        return this.completionQueue.remove().longValue();
    }

    public Matrix(Parser p) {
        this.parser = null;
        this.parser = p;
    }

    public Parser.EarleyCell get(int x, int y) {
        return this.cells[y].get(Integer.valueOf(x));
    }

    public void put(int x, int y, Parser.EarleyCell content) {
        if (this.lastY < y) {
            this.lastY = y;
        }
        this.cells[y].put(Integer.valueOf(x), content);
    }

    public void initCells(int length) {
        this.cells = new Map[length + 1];
        for (int i = 0; i < this.cells.length; i++) {
            this.cells[i] = new TreeMap();
        }
    }

    public int lastY() {
        return this.lastY;
    }

    public Map<Integer, Parser.EarleyCell> getXRange(int y) {
        return this.cells[y];
    }
}
