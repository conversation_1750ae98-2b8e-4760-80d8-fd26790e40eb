package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.XSSecureId;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxsset.class */
final class T4CTTIoxsset extends T4CTTIfun {
    OracleConnection.XSSessionSetOperationCode opCode;
    byte[] sessionId;
    XSSecureId sidp;
    XSSessionParametersI sessParam;

    T4CTTIoxsset(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 183);
    }

    void doOXSSET(OracleConnection.XSSessionSetOperationCode opCode, byte[] sessionId, XSSecureId sidp, XSSessionParametersI sessParam) throws SQLException, IOException {
        this.opCode = opCode;
        this.sessionId = sessionId;
        this.sidp = sidp;
        this.sessParam = sessParam;
        if (sessParam != null) {
            sessParam.doCharConversion(this.meg.conv);
        }
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.opCode.getCode());
        boolean sendSid = false;
        if (this.sessionId != null && this.sessionId.length > 0) {
            sendSid = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sessionId.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.sidp != null) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (this.sessParam != null && this.sessParam.binaryParam != null && this.sessParam.binaryParam.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sessParam.binaryParam.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.sessParam != null) {
            this.meg.marshalUB4(this.sessParam.intParam);
        } else {
            this.meg.marshalUB4(0L);
        }
        if (this.sessParam != null && this.sessParam.textParamBytes != null && this.sessParam.textParamBytes.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sessParam.textParamBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (sendSid) {
            this.meg.marshalB1Array(this.sessionId);
        }
        if (this.sidp != null) {
            ((XSSecureIdI) this.sidp).marshal(this.meg);
        }
        if (this.sessParam != null && this.sessParam.binaryParam != null && this.sessParam.binaryParam.length > 0) {
            this.meg.marshalB1Array(this.sessParam.binaryParam);
        }
        if (this.sessParam != null && this.sessParam.textParamBytes != null && this.sessParam.textParamBytes.length > 0) {
            for (int i = 0; i < this.sessParam.textParamBytes.length; i++) {
                if (this.sessParam.textParamBytes[i] == null) {
                    this.meg.marshalUB4(0L);
                } else {
                    this.meg.marshalUB4(this.sessParam.textParamBytes[i].length);
                    this.meg.marshalCLR(this.sessParam.textParamBytes[i], this.sessParam.textParamBytes[i].length);
                }
            }
        }
    }
}
