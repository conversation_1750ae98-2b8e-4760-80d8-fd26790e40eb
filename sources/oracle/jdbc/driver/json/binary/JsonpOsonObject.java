package oracle.jdbc.driver.json.binary;

import java.io.StringWriter;
import java.sql.SQLException;
import java.sql.Wrapper;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import javax.json.JsonArray;
import javax.json.JsonNumber;
import javax.json.JsonObject;
import javax.json.JsonString;
import javax.json.JsonValue;
import javax.json.stream.JsonGenerator;
import oracle.jdbc.driver.json.JsonpGeneratorWrapper;
import oracle.jdbc.driver.json.binary.OsonAbstractObject;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JsonpOsonObject.class */
public class JsonpOsonObject extends OsonAbstractObject implements JsonObject, Wrapper {
    public JsonpOsonObject(OsonContext ctx) {
        super(ctx);
    }

    public JsonpOsonObject(OsonContext ctx, int pos) {
        super(ctx, pos);
    }

    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> iface) throws SQLException {
        try {
            return iface.cast(new OsonObjectImpl(new OsonContext(this.ctx), this.pos));
        } catch (ClassCastException e) {
            throw new SQLException(e.getMessage(), e);
        }
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return iface.isAssignableFrom(OsonObjectImpl.class);
    }

    /* renamed from: get, reason: merged with bridge method [inline-methods] */
    public JsonValue m404get(Object key) {
        return (JsonValue) getInternal(key);
    }

    public JsonArray getJsonArray(String key) {
        return getJsonArrayInternal(key);
    }

    public JsonObject getJsonObject(String key) {
        return getJsonObjectInternal(key);
    }

    public JsonNumber getJsonNumber(String key) {
        int childOffset = getChildOffset(key);
        if (childOffset < 0) {
            return null;
        }
        return (JsonNumber) getValueInternal(childOffset);
    }

    public JsonString getJsonString(String key) {
        return m404get((Object) key);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonAbstractObject
    public String getString(String key) {
        return getJsonString(key).getString();
    }

    @Override // oracle.jdbc.driver.json.binary.OsonAbstractObject
    public String getString(String key, String defaultValue) {
        JsonString jsonStringM404get = m404get((Object) key);
        if (jsonStringM404get == null) {
            return defaultValue;
        }
        if (jsonStringM404get.getValueType() == JsonValue.ValueType.STRING) {
            return jsonStringM404get.getString();
        }
        return defaultValue;
    }

    public JsonValue.ValueType getValueType() {
        return JsonValue.ValueType.OBJECT;
    }

    public JsonValue put(String key, JsonValue value) {
        throw new UnsupportedOperationException();
    }

    /* renamed from: remove, reason: merged with bridge method [inline-methods] */
    public JsonValue m403remove(Object key) {
        throw new UnsupportedOperationException();
    }

    public void putAll(Map<? extends String, ? extends JsonValue> m) {
        throw new UnsupportedOperationException();
    }

    public void clear() {
        throw new UnsupportedOperationException();
    }

    public Collection<JsonValue> values() {
        return new OsonAbstractObject.OsonObjectValues();
    }

    public Set<Map.Entry<String, JsonValue>> entrySet() {
        return new OsonAbstractObject.OsonEntrySet();
    }

    @Override // oracle.jdbc.driver.json.binary.OsonAbstractObject
    public int hashCode() {
        int result = 0;
        for (Map.Entry<String, JsonValue> e : entrySet()) {
            result += e.hashCode();
        }
        return result;
    }

    public String toString() {
        StringWriter writer = new StringWriter();
        JsonGenerator ser = new JsonpGeneratorWrapper(new JsonSerializerImpl(writer));
        ser.write(this);
        ser.close();
        return writer.toString();
    }
}
