package oracle.jdbc.driver;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.xa.OracleXAResource;
import oracle.net.ns.SQLnetDef;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CRowidAccessor.class */
class T4CRowidAccessor extends RowidAccessor {
    private static final String CLASS_NAME;
    T4CMAREngine mare;
    final int[] meta;
    static final int KGRD_EXTENDED_OBJECT = 6;
    static final int KGRD_EXTENDED_BLOCK = 6;
    static final int KGRD_EXTENDED_FILE = 3;
    static final int KGRD_EXTENDED_SLOT = 3;
    static final int kd4_ubridtype_physical = 1;
    static final int kd4_ubridtype_logical = 2;
    static final int kd4_ubridtype_remote = 3;
    static final int kd4_ubridtype_exttab = 4;
    static final int kd4_ubridtype_future2 = 5;
    static final int kd4_ubridtype_max = 5;
    static final int kd4_ubridlen_typeind = 1;
    static final int kd4_ubridlen_physobjd = 4;
    static final int kd4_ubridlen_physfno = 2;
    static final int kd4_ubridlen_physbno = 4;
    static final int kd4_ubridlen_physsno = 2;
    static final byte[] kgrd_indbyte_char;
    static final byte[] kgrd_basis_64;
    static final byte[] kgrd_index_64;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CRowidAccessor.class.desiredAssertionStatus();
        CLASS_NAME = T4CRowidAccessor.class.getName();
        kgrd_indbyte_char = new byte[]{65, 42, 45, 40, 41};
        kgrd_basis_64 = new byte[]{65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 43, 47};
        kgrd_index_64 = new byte[]{-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1};
    }

    T4CRowidAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, form, external_type, isOutBind, false);
        this.meta = new int[1];
        this.mare = _mare;
        this.defineType = 104;
    }

    T4CRowidAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, nullable, flags, precision, scale, contflag, total_elems, form);
        this.meta = new int[1];
        this.mare = _mare;
        this.definedColumnType = _definedColumnType;
        this.definedColumnSize = _definedColumnSize;
        this.defineType = 104;
    }

    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        boolean isStream = false;
        if (!isUseless()) {
            if (isUnexpected()) {
                long pos = this.rowData.getPosition();
                unmarshalColumnMetadata();
                unmarshalBytes();
                this.rowData.setPosition(pos);
                setNull(this.lastRowProcessed, true);
            } else if (isNullByDescribe()) {
                setNull(this.lastRowProcessed, true);
                unmarshalColumnMetadata();
                if (this.statement.connection.versionNumber < 9200) {
                    processIndicator(0);
                }
            } else {
                unmarshalColumnMetadata();
                isStream = unmarshalBytes();
            }
        }
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
        return isStream;
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new AccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CRowidAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CRowidAccessor(stmt, T4CRowidAccessor.this.describeMaxLength, T4CRowidAccessor.this.nullable, -1, T4CRowidAccessor.this.precision, T4CRowidAccessor.this.scale, T4CRowidAccessor.this.contflag, -1, T4CRowidAccessor.this.formOfUse, T4CRowidAccessor.this.definedColumnType, T4CRowidAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }

    boolean unmarshalBytes() throws SQLException, IOException {
        int len;
        setOffset(this.lastRowProcessed);
        this.rowData.putShort((short) 0);
        if (this.describeType == 208) {
            len = (int) this.mare.unmarshalUB4();
            if (len > 0) {
                ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare, len);
            }
        } else {
            len = this.mare.unmarshalUB1();
            if (len > 0) {
                this.rowData.putInt((int) this.mare.unmarshalUB4());
                this.rowData.putShort((short) this.mare.unmarshalUB2());
                this.rowData.put((byte) this.mare.unmarshalUB1());
                this.rowData.putInt((int) this.mare.unmarshalUB4());
                this.rowData.putShort((short) this.mare.unmarshalUB2());
            }
        }
        processIndicator(len);
        this.rowData.putShort(getOffset(this.lastRowProcessed), (short) len);
        setLength(this.lastRowProcessed, (int) (this.rowData.getPosition() - getOffset(this.lastRowProcessed)));
        setNull(this.lastRowProcessed, len == 0);
        return false;
    }

    byte[] getDecodedBytes(int currentRow) throws SQLException {
        this.rowData.pushPosition(getOffset(currentRow));
        int meta = this.rowData.getShort();
        if (this.describeType == 208) {
            byte[] b0 = getBytesInternal(currentRow);
            byte[] b1 = new byte[4096];
            int meta2 = kgrdub2c(b0, meta, 2, b1, 2);
            b1[0] = (byte) ((meta2 >> 8) & 255);
            b1[1] = (byte) (meta2 & 255);
            byte[] b2 = new byte[meta2 + 2];
            System.arraycopy(b1, 0, b2, 0, b2.length);
            this.rowData.popPosition();
            return b2;
        }
        short len = (short) meta;
        long rba = 0;
        int partitionID = 0;
        short tableID = 0;
        long blockNumber = 0;
        int slotNumber = 0;
        if (len > 0) {
            rba = this.rowData.getInt() & SQLnetDef.NSPDDLSLMAX;
            partitionID = this.rowData.getShort() & 65535;
            tableID = (short) (this.rowData.get() & 255);
            blockNumber = this.rowData.getInt() & SQLnetDef.NSPDDLSLMAX;
            slotNumber = this.rowData.getShort() & 65535;
        }
        this.rowData.popPosition();
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "getDecodedBytes", "$1.unmarshalOneRow : len={0}, rba={1}, partition={2}, tableID={3}, blockNumber={4}, slotNumber={5}", (String) null, (Throwable) null, Short.valueOf(len), Long.valueOf(rba), Integer.valueOf(partitionID), Short.valueOf(tableID), Long.valueOf(blockNumber), Integer.valueOf(slotNumber));
        if (rba == 0 && partitionID == 0 && tableID == 0 && blockNumber == 0 && slotNumber == 0) {
            return null;
        }
        long[] args = {rba, partitionID, blockNumber, slotNumber};
        byte[] base64format = rowidToString(args);
        int nbBytesToKeep = 18;
        if (this.byteLength - 2 < 18) {
            nbBytesToKeep = this.byteLength - 2;
        }
        byte[] result = new byte[nbBytesToKeep + 2];
        System.arraycopy(base64format, 0, result, 2, nbBytesToKeep);
        result[0] = (byte) ((nbBytesToKeep & OracleXAResource.ORAISOLATIONMASK) >> 8);
        result[1] = (byte) (nbBytesToKeep & 255);
        return result;
    }

    @Override // oracle.jdbc.driver.RowidAccessor, oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException {
        byte[] b;
        if (isNull(currentRow) || (b = getDecodedBytes(currentRow)) == null) {
            return null;
        }
        long off = getOffset(currentRow);
        if (this.describeType == 208 && this.rowData.get(off + 2) != 1) {
            int len = ((b[0] & 255) << 8) | (b[1] & 255);
            return new String(b, 2, len, StandardCharsets.US_ASCII);
        }
        long[] l = stringToRowid(b, 2, b.length - 2);
        return new String(rowidToString(l), StandardCharsets.US_ASCII);
    }

    @Override // oracle.jdbc.driver.RowidAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getObject(currentRow);
        }
        if (isNull(currentRow)) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case -1:
            case 1:
            case 12:
                return getString(currentRow);
            case oracle.jdbc.OracleTypes.ROWID /* -8 */:
                return getROWID(currentRow);
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    byte[] getBytes(int currentRow) throws SQLException {
        byte[] b0;
        if (isNull(currentRow) || (b0 = getDecodedBytes(currentRow)) == null) {
            return null;
        }
        byte[] b1 = new byte[b0.length - 2];
        System.arraycopy(b0, 2, b1, 0, b1.length);
        return b1;
    }

    static final byte[] rowidToString(long[] rowid) {
        long rba = rowid[0];
        long tidpid = rowid[1];
        long ridrba = rowid[2];
        long ridsqn = rowid[3];
        byte[] ret = new byte[18];
        int offset = kgrd42b(ret, rba, 6, 0);
        kgrd42b(ret, ridsqn, 3, kgrd42b(ret, ridrba, 6, kgrd42b(ret, tidpid, 3, offset)));
        return ret;
    }

    static final long[] rcToRowid(byte[] data, int offset, int length) throws SQLException, NumberFormatException {
        if (length != 18) {
            throw new SQLException("Rowid size incorrect.");
        }
        String value = new String(data, offset, length, StandardCharsets.US_ASCII);
        long blockNumber = Long.parseLong(value.substring(0, 8), 16);
        long slotNumber = Long.parseLong(value.substring(9, 13), 16);
        long partitionID = Long.parseLong(value.substring(14, 18), 16);
        long[] ret = {0, partitionID, blockNumber, slotNumber};
        return ret;
    }

    static final void kgrdr2rc(int tidrba, int tidpid, int tidtbl, int ridrba, int ridsqn, byte[] dstBytes, int offset) throws SQLException {
        int offset2 = lmx42h(dstBytes, ridrba, 8, offset);
        dstBytes[offset2] = 46;
        int offset3 = lmx42h(dstBytes, ridsqn, 4, offset2 + 1);
        dstBytes[offset3] = 46;
        lmx42h(dstBytes, tidpid, 4, offset3 + 1);
    }

    static final int lmx42h(byte[] charsAsBytes, long value, int size, int offset) {
        String hexRep = Long.toHexString(value).toUpperCase();
        int hexRepOff = 0;
        do {
            if (hexRepOff < hexRep.length()) {
                charsAsBytes[(offset + size) - 1] = (byte) hexRep.charAt((hexRep.length() - hexRepOff) - 1);
                hexRepOff++;
            } else {
                charsAsBytes[(offset + size) - 1] = 48;
            }
            size--;
        } while (size > 0);
        return size + offset;
    }

    static final int kgrdc2ub(byte[] src, int src_offset, byte[] dst, int dst_offset, int length) throws SQLException {
        byte rowid_type = getRowidType(src, src_offset);
        int bytes_to_convert = length - 1;
        byte[] kgrd_char64_tosigned = kgrd_index_64;
        int byte_used_len = 1 + (3 * ((length - 1) / 4)) + ((length - 1) % 4 != 0 ? ((length - 1) % 4) - 1 : 0);
        if (bytes_to_convert == 0) {
            throw ((SQLException) DatabaseError.createSqlException(132).fillInStackTrace());
        }
        dst[dst_offset + 0] = rowid_type;
        int next_input_byte = src_offset + 1;
        int next_output_byte = 1;
        while (bytes_to_convert > 0) {
            if (bytes_to_convert == 1) {
                throw ((SQLException) DatabaseError.createSqlException(132).fillInStackTrace());
            }
            byte this_byte = kgrd_char64_tosigned[src[next_input_byte]];
            if (this_byte == -1) {
                throw ((SQLException) DatabaseError.createSqlException(132).fillInStackTrace());
            }
            int next_input_byte2 = next_input_byte + 1;
            byte byte_after_this = kgrd_char64_tosigned[src[next_input_byte2]];
            if (byte_after_this == -1) {
                throw ((SQLException) DatabaseError.createSqlException(132).fillInStackTrace());
            }
            dst[dst_offset + next_output_byte] = (byte) (((this_byte & 255) << 2) | ((byte_after_this & 48) >> 4));
            if (bytes_to_convert == 2) {
                break;
            }
            int next_output_byte2 = next_output_byte + 1;
            int next_input_byte3 = next_input_byte2 + 1;
            byte byte_after_this2 = kgrd_char64_tosigned[src[next_input_byte3]];
            if (byte_after_this2 == -1) {
                throw ((SQLException) DatabaseError.createSqlException(132).fillInStackTrace());
            }
            dst[dst_offset + next_output_byte2] = (byte) (((byte_after_this & 255) << 4) | ((byte_after_this2 & 60) >> 2));
            if (bytes_to_convert == 3) {
                break;
            }
            int next_output_byte3 = next_output_byte2 + 1;
            int next_input_byte4 = next_input_byte3 + 1;
            byte byte_after_this3 = kgrd_char64_tosigned[src[next_input_byte4]];
            if (byte_after_this3 == -1) {
                throw ((SQLException) DatabaseError.createSqlException(132).fillInStackTrace());
            }
            dst[dst_offset + next_output_byte3] = (byte) (((byte_after_this2 & 3) << 6) | byte_after_this3);
            bytes_to_convert -= 4;
            next_input_byte = next_input_byte4 + 1;
            next_output_byte = next_output_byte3 + 1;
        }
        return byte_used_len;
    }

    static final long[] stringToRowid(byte[] data, int offset, int length) throws SQLException {
        if (length != 18) {
            throw new SQLException("Rowid size incorrect.");
        }
        long[] ret = new long[4];
        try {
            ret[0] = kgrdb42(data, 6, offset);
            int offset2 = offset + 6;
            ret[1] = kgrdb42(data, 3, offset2);
            int offset3 = offset2 + 3;
            ret[2] = kgrdb42(data, 6, offset3);
            int offset4 = offset3 + 6;
            ret[3] = kgrdb42(data, 3, offset4);
            int i = offset4 + 3;
        } catch (Exception e) {
            ret[0] = 0;
            ret[1] = 0;
            ret[2] = 0;
            ret[3] = 0;
        }
        return ret;
    }

    static final int kgrd42b(byte[] charsAsBytes, long value, int size, int offset) {
        long val = value;
        while (size > 0) {
            charsAsBytes[(offset + size) - 1] = kgrd_basis_64[((int) val) & 63];
            val = (val >>> 6) & 67108863;
            size--;
        }
        return size + offset;
    }

    static final long kgrdb42(byte[] charsAsBytes, int size, int offset) throws SQLException {
        long ret = 0;
        for (int i = 0; i < size; i++) {
            byte value = kgrd_index_64[charsAsBytes[offset + i]];
            if (value == -1) {
                throw new SQLException("Char data to rowid conversion failed.");
            }
            ret = (ret << 6) | value;
        }
        return ret;
    }

    static final void kgrdr2ec(int tidrba, int tidpid, int tidtbl, int ridrba, int ridsqn, byte[] dstBytes, int dstOffset) throws SQLException {
        kgrd42b(ridsqn, dstBytes, kgrd42b(ridrba, dstBytes, kgrd42b(tidpid, dstBytes, kgrd42b(tidrba, dstBytes, dstOffset, 6), 3), 6), 3);
    }

    static final int kgrd42b(int value, byte[] dstBytes, int dstOffset, int digits) throws SQLException {
        while (digits > 0) {
            digits--;
            dstBytes[dstOffset + digits] = kgrd_basis_64[value & 63];
            value >>= 6;
        }
        return dstOffset + digits;
    }

    static final int kgrdub2c(byte[] bytes, int size, int offset, byte[] dstBytes, int dstOffset) throws SQLException {
        int rid_length;
        byte rid_type = bytes[offset];
        if (rid_type == 1) {
            int[] int_bytes = new int[bytes.length];
            for (int i = 0; i < bytes.length; i++) {
                int_bytes[i] = bytes[i] & 255;
            }
            int temp = offset + 1;
            int tidrba = (((((int_bytes[temp + 0] << 8) + int_bytes[temp + 1]) << 8) + int_bytes[temp + 2]) << 8) + int_bytes[temp + 3];
            int temp2 = offset + 5;
            int tidpid = (int_bytes[temp2 + 0] << 8) + int_bytes[temp2 + 1];
            int temp3 = offset + 7;
            int ridrba = (((((int_bytes[temp3 + 0] << 8) + int_bytes[temp3 + 1]) << 8) + int_bytes[temp3 + 2]) << 8) + int_bytes[temp3 + 3];
            int temp4 = offset + 11;
            int ridsqn = (int_bytes[temp4 + 0] << 8) + int_bytes[temp4 + 1];
            if (tidrba == 0) {
                kgrdr2rc(tidrba, tidpid, 0, ridrba, ridsqn, dstBytes, dstOffset);
            } else {
                kgrdr2ec(tidrba, tidpid, 0, ridrba, ridsqn, dstBytes, dstOffset);
            }
            rid_length = 18;
        } else {
            int next_output_byte = 0;
            int bytes_to_convert = size - 1;
            int body_length = (4 * (size / 3)) + (size % 3 == 0 ? (size % 3) + 1 : 0);
            int char_length = (1 + body_length) - 1;
            if (char_length != 0) {
                dstBytes[dstOffset + 0] = kgrd_indbyte_char[rid_type - 1];
                int next_input_byte = offset + 1;
                next_output_byte = 1;
                while (true) {
                    if (bytes_to_convert <= 0) {
                        break;
                    }
                    int i2 = next_output_byte;
                    int next_output_byte2 = next_output_byte + 1;
                    dstBytes[dstOffset + i2] = kgrd_basis_64[(bytes[next_input_byte] & 255) >> 2];
                    if (bytes_to_convert == 1) {
                        next_output_byte = next_output_byte2 + 1;
                        dstBytes[dstOffset + next_output_byte2] = kgrd_basis_64[(bytes[next_input_byte] & 3) << 4];
                        break;
                    }
                    byte second_byte = (byte) (bytes[next_input_byte + 1] & 255);
                    int next_output_byte3 = next_output_byte2 + 1;
                    dstBytes[dstOffset + next_output_byte2] = kgrd_basis_64[((bytes[next_input_byte] & 3) << 4) | ((second_byte & 240) >> 4)];
                    if (bytes_to_convert == 2) {
                        next_output_byte = next_output_byte3 + 1;
                        dstBytes[dstOffset + next_output_byte3] = kgrd_basis_64[(second_byte & 15) << 2];
                        break;
                    }
                    int next_input_byte2 = next_input_byte + 2;
                    int next_output_byte4 = next_output_byte3 + 1;
                    dstBytes[dstOffset + next_output_byte3] = kgrd_basis_64[((second_byte & 15) << 2) | ((bytes[next_input_byte2] & 192) >> 6)];
                    dstBytes[dstOffset + next_output_byte4] = kgrd_basis_64[bytes[next_input_byte2] & 63];
                    bytes_to_convert -= 3;
                    next_input_byte = next_input_byte2 + 1;
                    next_output_byte = next_output_byte4 + 1;
                }
            }
            rid_length = next_output_byte;
        }
        return rid_length;
    }

    static final byte[] rowidToDTYBURI(long[] rowid) {
        int tidrba = (int) rowid[0];
        int tidpid = (int) rowid[1];
        int ridrba = (int) rowid[2];
        int ridsqn = (int) rowid[3];
        int tidpidOffset = 1 + 4;
        int ridrbaOffset = tidpidOffset + 2;
        int ridsqnOffset = ridrbaOffset + 4;
        byte[] kd4ubrid = new byte[ridsqnOffset + 2];
        kd4ubrid[0] = 1;
        kd4ubrid[1] = (byte) ((tidrba >> 24) & 255);
        kd4ubrid[1 + 1] = (byte) ((tidrba >> 16) & 255);
        kd4ubrid[1 + 2] = (byte) ((tidrba >> 8) & 255);
        kd4ubrid[1 + 3] = (byte) (tidrba & 255);
        kd4ubrid[tidpidOffset] = (byte) ((tidpid >> 8) & 255);
        kd4ubrid[tidpidOffset + 1] = (byte) (tidpid & 255);
        kd4ubrid[ridrbaOffset] = (byte) ((ridrba >> 24) & 255);
        kd4ubrid[ridrbaOffset + 1] = (byte) ((ridrba >> 16) & 255);
        kd4ubrid[ridrbaOffset + 2] = (byte) ((ridrba >> 8) & 255);
        kd4ubrid[ridrbaOffset + 3] = (byte) (ridrba & 255);
        kd4ubrid[ridsqnOffset] = (byte) ((ridsqn >> 8) & 255);
        kd4ubrid[ridsqnOffset + 1] = (byte) (ridsqn & 255);
        return kd4ubrid;
    }

    static final byte[] rowidToDTYBRI(long[] rowid, int sdbaOfBits, int sdbaBits, int dbabBits) {
        int tidrba = (int) rowid[0];
        int tidpid = (int) rowid[1];
        int ridrba = (int) rowid[2];
        int ridsqn = (int) rowid[3];
        int ridsqnOffset = 4 + 4;
        byte[] kd4lbrid = new byte[ridsqnOffset + 2];
        kd4lbrid[0] = (byte) ((tidrba >> 24) & 255);
        kd4lbrid[0 + 1] = (byte) ((tidrba >> 16) & 255);
        kd4lbrid[0 + 2] = (byte) ((tidrba >> 8) & 255);
        kd4lbrid[0 + 3] = (byte) (tidrba & 255);
        int tidpidHi = tidpid >> sdbaOfBits;
        int tidpidLo = tidpid & ((1 << sdbaOfBits) - 1);
        int krdba = ridrba + ((tidpidHi << dbabBits) | (tidpidLo << (sdbaBits - sdbaOfBits)));
        kd4lbrid[4] = (byte) ((krdba >> 24) & 255);
        kd4lbrid[4 + 1] = (byte) ((krdba >> 16) & 255);
        kd4lbrid[4 + 2] = (byte) ((krdba >> 8) & 255);
        kd4lbrid[4 + 3] = (byte) (krdba & 255);
        kd4lbrid[ridsqnOffset] = (byte) ((ridsqn >> 8) & 255);
        kd4lbrid[ridsqnOffset + 1] = (byte) (ridsqn & 255);
        return kd4lbrid;
    }

    static final boolean isRestricted(byte[] rowidChars) {
        for (byte b : rowidChars) {
            if (46 == b) {
                return true;
            }
        }
        return false;
    }

    static final boolean isUROWID(byte[] data, int offset) {
        return getRowidType(data, offset) == 2;
    }

    static final byte getRowidType(byte[] data, int offset) {
        byte rowid_type = 5;
        switch (data[offset]) {
            case 40:
                rowid_type = 4;
                break;
            case 41:
                rowid_type = 5;
                break;
            case 42:
                rowid_type = 2;
                break;
            case 45:
                rowid_type = 3;
                break;
            case DatabaseError.EOJ_CONV_WAS_NULL /* 65 */:
                rowid_type = 1;
                break;
        }
        return rowid_type;
    }
}
