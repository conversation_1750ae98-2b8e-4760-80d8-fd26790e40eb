package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Consumer;
import java.util.logging.Level;
import oracle.jdbc.DatabaseFunction;
import oracle.jdbc.ErrorSet;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.diagnostics.Parameter;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.Pipeline;
import oracle.jdbc.driver.T4CConnection;
import oracle.jdbc.driver.utils.ThrowingRunnable;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.replay.ReplayableConnection;

/*  JADX ERROR: NullPointerException in pass: ClassModifier
    java.lang.NullPointerException: Cannot invoke "java.util.List.forEach(java.util.function.Consumer)" because "blocks" is null
    	at jadx.core.utils.BlockUtils.collectAllInsns(BlockUtils.java:1029)
    	at jadx.core.dex.visitors.ClassModifier.removeBridgeMethod(ClassModifier.java:245)
    	at jadx.core.dex.visitors.ClassModifier.removeSyntheticMethods(ClassModifier.java:160)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1604)
    	at jadx.core.dex.visitors.ClassModifier.visit(ClassModifier.java:65)
    */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIfun.class */
abstract class T4CTTIfun extends T4CTTIMsg {
    private static final String CLASS_NAME;
    private short funCode;
    protected byte sequenceNumber;
    private long tokenNumber;
    private SQLException marshallingException;
    private boolean isDrainingCancel;
    int receiveState;
    static final int IDLE_RECEIVE_STATE = 0;
    static final int ACTIVE_RECEIVE_STATE = 1;
    static final int READROW_RECEIVE_STATE = 2;
    static final int STREAM_RECEIVE_STATE = 3;
    boolean rpaProcessed;
    boolean rxhProcessed;
    boolean iovProcessed;
    private final short[] ttiList;
    private int ttiListEnd;
    ReplayContext replayContext;
    StateSignatures stateSignatures;
    TemplateOverflow templateOverflow;
    static final /* synthetic */ boolean $assertionsDisabled;

    abstract void marshal() throws IOException;

    /*  JADX ERROR: Failed to decode insn: 0x0002: MOVE_MULTI
        java.lang.ArrayIndexOutOfBoundsException: arraycopy: source index -1 out of bounds for object array[6]
        	at java.base/java.lang.System.arraycopy(Native Method)
        	at jadx.plugins.input.java.data.code.StackState.insert(StackState.java:52)
        	at jadx.plugins.input.java.data.code.CodeDecodeState.insert(CodeDecodeState.java:137)
        	at jadx.plugins.input.java.data.code.JavaInsnsRegister.dup2x1(JavaInsnsRegister.java:313)
        	at jadx.plugins.input.java.data.code.JavaInsnData.decode(JavaInsnData.java:46)
        	at jadx.core.dex.instructions.InsnDecoder.lambda$process$0(InsnDecoder.java:50)
        	at jadx.plugins.input.java.data.code.JavaCodeReader.visitInstructions(JavaCodeReader.java:85)
        	at jadx.core.dex.instructions.InsnDecoder.process(InsnDecoder.java:46)
        	at jadx.core.dex.nodes.MethodNode.load(MethodNode.java:158)
        	at jadx.core.dex.nodes.ClassNode.load(ClassNode.java:458)
        	at jadx.core.ProcessClass.process(ProcessClass.java:69)
        	at jadx.core.ProcessClass.generateCode(ProcessClass.java:109)
        	at jadx.core.dex.nodes.ClassNode.generateClassCode(ClassNode.java:401)
        	at jadx.core.dex.nodes.ClassNode.decompile(ClassNode.java:389)
        	at jadx.core.dex.nodes.ClassNode.getCode(ClassNode.java:339)
        */
    static /* synthetic */ long access$302(oracle.jdbc.driver.T4CTTIfun r6, long r7) {
        /*
            r0 = r6
            r1 = r7
            // decode failed: arraycopy: source index -1 out of bounds for object array[6]
            r0.tokenNumber = r1
            return r-1
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CTTIfun.access$302(oracle.jdbc.driver.T4CTTIfun, long):long");
    }

    static {
        $assertionsDisabled = !T4CTTIfun.class.desiredAssertionStatus();
        CLASS_NAME = T4CTTIfun.class.getName();
    }

    T4CTTIfun(T4CConnection _conn, byte _ttcCode) {
        super(_conn, _ttcCode);
        this.isDrainingCancel = false;
        this.receiveState = 0;
        this.rpaProcessed = false;
        this.rxhProcessed = false;
        this.iovProcessed = false;
        this.ttiListEnd = 0;
        this.replayContext = null;
        this.stateSignatures = null;
        this.templateOverflow = null;
        this.ttiList = _conn.ttiList;
    }

    final void setFunCode(short _funCode) {
        this.funCode = _funCode;
    }

    final short getFunCode() {
        return this.funCode;
    }

    private final void marshalFunHeader(long tokenNumber) throws IOException {
        marshalTTCcode();
        this.connection.lastExecutedFunCode(this.funCode);
        this.meg.marshalUB1(this.funCode);
        this.sequenceNumber = this.connection.getNextSeqNumber();
        this.meg.marshalUB1(this.sequenceNumber);
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "marshalFunHeader", "func={0} sequenceNumber={1} tokenNumber={2}", (String) null, (Throwable) null, Short.valueOf(this.funCode), Byte.valueOf(this.sequenceNumber), Long.valueOf(tokenNumber));
        if (this.connection.getTTCVersion() >= 18) {
            this.meg.marshalUB8(tokenNumber);
        }
    }

    final void doRPC() throws SQLException, IOException {
        beforeRoundTrip();
        this.connection.awaitPipeline();
        drainCancel();
        requireNonPiggyBackFunction();
        this.connection.setExecutingRPCFunctionCode(this.funCode);
        this.connection.checkEndReplayCallback();
        sendPiggyBackMessages();
        try {
            try {
                this.connection.pipeState = 1;
                send(0L);
                this.connection.pipeState = 2;
                receive();
                afterRoundTrip(false);
                handleRpcCompletionAlways();
            } catch (SQLException sqlEx) {
                SQLException rpcFailure = handleRpcFailure(sqlEx);
                afterRoundTrip(true);
                throw rpcFailure;
            }
        } catch (Throwable th) {
            handleRpcCompletionAlways();
            throw th;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void beforeRoundTrip() {
        if (!this.connection.isTraceEventListenerEnabled()) {
            return;
        }
        ConnectionTraceContext traceContext = new ConnectionTraceContext(this.connection, DatabaseFunction.valueOfFunctionCode(this.funCode), false, getStatement());
        this.connection.trace(TraceEventListener.Sequence.BEFORE, traceContext);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void afterRoundTrip(boolean isCompletedExceptionally) {
        if (!this.connection.isTraceEventListenerEnabled()) {
            return;
        }
        ConnectionTraceContext traceContext = new ConnectionTraceContext(this.connection, DatabaseFunction.valueOfFunctionCode(this.funCode), isCompletedExceptionally, getStatement());
        this.connection.trace(TraceEventListener.Sequence.AFTER, traceContext);
    }

    protected OracleStatement getStatement() {
        return null;
    }

    private void requireNonPiggyBackFunction() throws SQLException {
        if (getTTCCode() == 17) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void sendPiggyBackMessages() throws SQLException, IOException {
        if (this.connection.isResultSetCacheActive()) {
            sendTTIQC();
        }
        this.connection.sendPiggyBackedMessages(getFunCode() == 9);
    }

    /* JADX WARN: Finally extract failed */
    private void drainCancel() throws SQLException, IOException {
        if (this.isDrainingCancel) {
            return;
        }
        Monitor.CloseableLock lock = this.connection.cancelInProgressLockForThin.acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.connection.cancelInProgressFlag) {
                T4CTTIfun oping = this.connection.oping;
                oping.isDrainingCancel = true;
                try {
                    try {
                        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "drainCancel", "Sending OPING to drain ORA-01013 error", null, (Throwable) null);
                        oping.doRPC();
                        oping.isDrainingCancel = false;
                    } catch (SQLException sqlException) {
                        if (!isCanceledError(sqlException)) {
                            throw sqlException;
                        }
                        oping.isDrainingCancel = false;
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                } catch (Throwable th3) {
                    oping.isDrainingCancel = false;
                    throw th3;
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void send(long tokenNumber) throws IOException {
        init();
        this.connection.enterMarshalling();
        marshalFunHeader(tokenNumber);
        marshal();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public SQLException handleRpcFailure(SQLException sqlException) {
        redoCursorClose();
        if (isCanceledError(sqlException)) {
            Monitor.CloseableLock lock = this.connection.cancelInProgressLockForThin.acquireCloseableLock();
            Throwable th = null;
            try {
                try {
                    this.connection.cancelInProgressFlag = false;
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    if (this.marshallingException != null) {
                        SQLException marshallingException = this.marshallingException;
                        this.marshallingException = null;
                        return marshallingException;
                    }
                } finally {
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        return sqlException;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void handleRpcCompletionAlways() {
        this.connection.pipeState = -1;
        this.connection.lastPiggyBackCursorCloseSeqNumber = (byte) 0;
        this.connection.sessionlessTxn.resetQueueOfPiggyBackMessages();
    }

    final CompletionStage<Void> doRPCAsync() {
        return doRPCAsync(ErrorSet.ALL_ERRORS);
    }

    final CompletionStage<Void> doRPCAsync(ErrorSet continueOnErrorSet) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        doRPCAsync(continueOnErrorSet, error -> {
            if (error == null) {
                future.complete(null);
            } else {
                future.completeExceptionally(error);
            }
        });
        return future;
    }

    final void doRPCAsync(Consumer<Throwable> callback) {
        doRPCAsync(ErrorSet.ALL_ERRORS, callback);
    }

    final void doRPCAsync(ErrorSet continueOnErrorSet, Consumer<Throwable> callback) {
        doRPCAsync(continueOnErrorSet, () -> {
        }, callback);
    }

    final void doRPCAsync(ErrorSet continueOnErrorSet, ThrowingRunnable<? extends Exception> startCallback, Consumer<Throwable> completionCallback) {
        try {
            if (this.connection.acProxy != null) {
                ((ReplayableConnection) this.connection.acProxy).disableReplay();
            }
            requireNonPiggyBackFunction();
            drainCancelForPipeline();
            Pipeline.IoTask ioTask = createIoTask(continueOnErrorSet, startCallback, completionCallback);
            this.connection.pipeline().execute(ioTask);
        } catch (Throwable throwable) {
            completionCallback.accept(throwable);
        }
    }

    private void drainCancelForPipeline() {
        if (this.isDrainingCancel || this.funCode == 200) {
            return;
        }
        Pipeline pipeline = this.connection.pipeline();
        Monitor.CloseableLock lock = this.connection.cancelInProgressLockForThin.acquireCloseableLock();
        Throwable th = null;
        try {
            if (pipeline.isExecuting()) {
                if (lock != null) {
                    if (0 == 0) {
                        lock.close();
                        return;
                    }
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                return;
            }
            if (!this.connection.cancelInProgressFlag) {
                if (lock != null) {
                    if (0 == 0) {
                        lock.close();
                        return;
                    }
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                        return;
                    }
                }
                return;
            }
            if (pipeline.communicationMode() == Pipeline.CommunicationMode.FULL_DUPLEX) {
                pipeline.resume();
            } else {
                T4CTTIfun oping = new T4CTTIoping(this.connection);
                oping.isDrainingCancel = true;
                oping.doRPCAsync(ErrorSet.NO_ERRORS, error -> {
                    oping.isDrainingCancel = false;
                });
            }
            if (lock != null) {
                if (0 == 0) {
                    lock.close();
                    return;
                }
                try {
                    lock.close();
                } catch (Throwable th4) {
                    th.addSuppressed(th4);
                }
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    private Pipeline.IoTask createIoTask(ErrorSet continueOnErrorSet, ThrowingRunnable<? extends Exception> startCallback, Consumer<Throwable> completionCallback) {
        Pipeline.IoTask ioTask;
        Pipeline pipeline = this.connection.pipeline();
        if (pipeline.isPipelinable(this.funCode) && pipeline.communicationMode() == Pipeline.CommunicationMode.FULL_DUPLEX) {
            ioTask = new PipelinedIoTask(continueOnErrorSet, startCallback, completionCallback);
        } else {
            ioTask = new NonPipelinedIoTask(continueOnErrorSet, startCallback, completionCallback);
        }
        return decorateIoTask(ioTask);
    }

    protected Pipeline.IoTask decorateIoTask(Pipeline.IoTask ioTask) {
        return ioTask;
    }

    private final void sendTTIQC() throws SQLException, IOException {
        this.connection.enterMarshalling();
        this.meg.marshalUB1((short) 24);
        this.connection.kpdqidcscn.setSCN(this.connection.getResultSetCacheVisibleSCN());
        this.connection.kpdqidcscn.marshal();
        this.connection.exitMarshalling();
    }

    final void doPigRPC() throws IOException {
        init();
        this.connection.enterMarshalling();
        marshalFunHeader(0L);
        marshal();
        this.connection.exitMarshalling();
    }

    final void doOneWayRPC() throws SQLException, IOException {
        this.connection.awaitPipeline();
        this.connection.sendPiggyBackedMessages();
        init();
        this.connection.enterMarshalling();
        marshalFunHeader(0L);
        marshal();
        this.meg.flush();
        this.connection.exitMarshalling();
    }

    private void init() {
        this.rpaProcessed = false;
        this.rxhProcessed = false;
        this.iovProcessed = false;
        this.ttiListEnd = 0;
    }

    void resumeReceive() throws SQLException, IOException {
        receive();
    }

    private final String ttiListString() {
        String s = "[ ";
        for (int i = 0; i < this.ttiListEnd; i++) {
            s = s + ((int) this.ttiList[i]) + ", ";
        }
        return s + "]";
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Code restructure failed: missing block: B:106:0x03e9, code lost:
    
        r11.receiveState = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:107:0x03ef, code lost:
    
        if (r12 == null) goto L110;
     */
    /* JADX WARN: Code restructure failed: missing block: B:109:0x03f3, code lost:
    
        throw r12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:138:0x046a, code lost:
    
        if (r11.replayContext == null) goto L140;
     */
    /* JADX WARN: Code restructure failed: missing block: B:139:0x046d, code lost:
    
        handleReplayContext(r11.replayContext);
     */
    /* JADX WARN: Code restructure failed: missing block: B:141:0x0479, code lost:
    
        if (r11.stateSignatures == null) goto L143;
     */
    /* JADX WARN: Code restructure failed: missing block: B:142:0x047c, code lost:
    
        updateSessionState(r11.stateSignatures, r11.templateOverflow);
     */
    /* JADX WARN: Code restructure failed: missing block: B:143:0x0488, code lost:
    
        r11.connection.setExecutingRPCFunctionCode(0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:144:0x0496, code lost:
    
        if (r11.funCode == 94) goto L147;
     */
    /* JADX WARN: Code restructure failed: missing block: B:146:0x049f, code lost:
    
        if (r11.funCode != 78) goto L148;
     */
    /* JADX WARN: Code restructure failed: missing block: B:147:0x04a2, code lost:
    
        r11.connection.setExecutingRPCSQL(null);
     */
    /* JADX WARN: Code restructure failed: missing block: B:148:0x04aa, code lost:
    
        r11.connection.exitMarshalling();
     */
    /* JADX WARN: Code restructure failed: missing block: B:149:0x04b1, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x0378, code lost:
    
        throw ((java.sql.SQLException) debug(java.util.logging.Level.SEVERE, oracle.jdbc.diagnostics.SecurityLabel.UNKNOWN, oracle.jdbc.driver.T4CTTIfun.CLASS_NAME, "receive", "Received unexpected TTC message code {0}", (java.lang.String) null, (java.lang.String) oracle.jdbc.driver.DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401, ttiListString()).fillInStackTrace(), (java.lang.Object) java.lang.Short.valueOf(r0)));
     */
    /* JADX WARN: Finally extract failed */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void receive() throws java.sql.SQLException, java.io.IOException {
        /*
            Method dump skipped, instructions count: 1202
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CTTIfun.receive():void");
    }

    private final void handleReplayContext(ReplayContext replayContext) {
        if (this.connection.replayModes.contains(T4CConnection.ReplayMode.NONREQUEST)) {
            if ((replayContext.flags_kpdxcAppContCtl & 4) == 0 && this.connection.replayModes.contains(T4CConnection.ReplayMode.RUNTIME_REPLAY_ENABLED)) {
                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "handleReplayContext", "Received server DISABLE at non-request call with ORA-{0}, clearing ENABLE", (String) null, (String) null, (Object) Long.valueOf(replayContext.getErrorCode()));
                this.connection.replayModes.remove(T4CConnection.ReplayMode.RUNTIME_REPLAY_ENABLED);
                this.connection.nonRequestDisableReplayCxt = replayContext;
                return;
            }
            return;
        }
        if (!$assertionsDisabled && (replayContext.flags_kpdxcAppContCtl & 4) == 0 && replayContext.errcode_kpdxcAppContCtl == 41406 && this.connection.replayModes.contains(T4CConnection.ReplayMode.RUNTIME_REPLAY_ENABLED)) {
            throw new AssertionError("Server disabled replay with error " + replayContext.errcode_kpdxcAppContCtl + " but our replayModes=" + this.connection.replayModes);
        }
        if (this.connection.thinACReplayContextReceived.length == this.connection.thinACReplayContextReceivedCurrent) {
            ReplayContext[] tmp = new ReplayContext[this.connection.thinACReplayContextReceived.length * 2];
            System.arraycopy(this.connection.thinACReplayContextReceived, 0, tmp, 0, this.connection.thinACReplayContextReceived.length);
            this.connection.thinACReplayContextReceived = tmp;
        }
        ReplayContext[] replayContextArr = this.connection.thinACReplayContextReceived;
        T4CConnection t4CConnection = this.connection;
        int i = t4CConnection.thinACReplayContextReceivedCurrent;
        t4CConnection.thinACReplayContextReceivedCurrent = i + 1;
        replayContextArr[i] = replayContext;
        if ((replayContext.flags_kpdxcAppContCtl & 4) == 0 && this.connection.replayModes.contains(T4CConnection.ReplayMode.RUNTIME_REPLAY_ENABLED)) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "handleReplayContext", "Received a DISABLE during runtime, clearing STATIC", (String) null, (Throwable) null);
            this.connection.replayModes.remove(T4CConnection.ReplayMode.RUNTIME_REPLAY_ENABLED);
            this.connection.replayModes.remove(T4CConnection.ReplayMode.RUNTIME_OR_REPLAYING_STATIC);
        }
        if (replayContext.replayctx_kpdxcAppContCtl != null && replayContext.replayctx_kpdxcAppContCtl.length > 0) {
            if (this.connection.getExecutingRPCFunctionCode() != 115 || !this.connection.ignoreReplayContextFromAuthentication) {
                this.connection.thinACLastReplayContextReceived = replayContext;
            }
        }
    }

    private final void updateSessionState(StateSignatures stateSignatures, TemplateOverflow templateOverflow) {
        this.connection.updateSessionState(stateSignatures, templateOverflow);
    }

    private final void processEOCS() throws SQLException, IOException {
        if (this.connection.hasServerCompileTimeCapability(15, 1)) {
            int ucaeocs = (int) this.meg.unmarshalUB4();
            this.connection.eocs = ucaeocs;
            if ((ucaeocs & 8) != 0) {
                long elapsedTime = this.meg.unmarshalSB8();
                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "processEOCS", "elapsed time={0}", (String) null, (String) null, (Object) Long.valueOf(elapsedTime));
            }
            if ((ucaeocs & 2048) != 0) {
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "processEOCS", "got in-band planned down bit, mark connection for close={0}", (String) null, (Throwable) null, this.connection);
                this.connection.setNeedsToBeClosed(true);
            }
            if ((ucaeocs & 32768) != 0 && this.connection.statements != null) {
                this.connection.drcpState = OracleConnection.DRCPState.DETACHED;
                this.connection.statements.clearCursorId();
                this.connection.cleanStatementCache();
            }
        }
    }

    void processRPA() throws SQLException {
    }

    void readOAC() throws SQLException, IOException {
    }

    void readRSH() throws SQLException, IOException {
    }

    void readRPA() throws SQLException, IOException {
    }

    void readBVC() throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    void readLOBD() throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    void readIOV() throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    void readRXH() throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    boolean readRXD() throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    void readIMPLRES() throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    void readDCB() throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    void processSLG() throws SQLException, IOException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    void processOCSHRDKEY(byte[] buf) throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
    }

    protected boolean processRENEG() {
        return true;
    }

    private final void readSPF() throws SQLException, IOException {
        byte opCode = (byte) this.meg.unmarshalUB1();
        switch (opCode) {
            case 1:
                int nbOfDtys = this.meg.unmarshalUB2();
                for (int i = 0; i < nbOfDtys; i++) {
                    this.connection.kpdqidcscn.unmarshal();
                    int kpdqidccinvl = this.meg.unmarshalSWORD();
                    if (kpdqidccinvl > 0) {
                        T4CTTIqcinv[] kpdqidccinv = new T4CTTIqcinv[kpdqidccinvl];
                        for (int ii = 0; ii < kpdqidccinvl; ii++) {
                            kpdqidccinv[ii] = new T4CTTIqcinv(this.connection);
                            kpdqidccinv[ii].unmarshal();
                            this.connection.getResultSetCacheInternal().processCommittedInvalidation(kpdqidccinv[ii]);
                        }
                    }
                    this.connection.getResultSetCacheLocalInvalidations().clear();
                    int kpdqidcusrl = this.meg.unmarshalSWORD();
                    if (kpdqidcusrl > 0) {
                        T4CTTIqcinv[] kpdqidcusr = new T4CTTIqcinv[kpdqidcusrl];
                        for (int ii2 = 0; ii2 < kpdqidcusrl; ii2++) {
                            kpdqidcusr[ii2] = new T4CTTIqcinv(this.connection);
                            kpdqidcusr[ii2].unmarshal();
                            this.connection.getResultSetCacheLocalInvalidations().add(Long.valueOf(kpdqidcusr[ii2].kpdqcqid));
                        }
                    }
                    this.meg.unmarshalUB4();
                    if (this.connection.isResultSetCacheActive()) {
                        this.connection.setResultSetCacheVisibleSCN(this.connection.kpdqidcscn.getSCN());
                    }
                }
                return;
            case 2:
                int nbOfDtys2 = this.meg.unmarshalUB2();
                byte[] _ospid = this.meg.unmarshalNBytes(nbOfDtys2);
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readSPF", "  mtspid = {0}", (String) null, (String) null, (Object) Parameter.arg(Format.Style.BYTE_ARRAY, _ospid, new long[0]));
                return;
            case 3:
            case 6:
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401, ttiListString()).fillInStackTrace());
            case 4:
                this.connection.ocsessret.receive();
                debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readSPF", "dump obj = {0}", null, (Throwable) null, () -> {
                    return new Object[]{T4CConnection.dumpObject(this.connection.ocsessret, "  ")};
                });
                return;
            case 5:
                this.meg.unmarshalUB2();
                T4CTTIkvarr kvarr = new T4CTTIkvarr(this.connection);
                kvarr.unmarshal();
                if (kvarr.kpdkvarrptr != null) {
                    this.connection.updateSessionProperties(kvarr.kpdkvarrptr);
                    return;
                }
                return;
            case 7:
                if (!$assertionsDisabled && !this.connection.enableTGSupport && !this.connection.enableACSupport) {
                    throw new AssertionError("Driver TG/AC support is disabled but server still sent LTXID piggyback");
                }
                byte[] ltxidbytes = this.meg.unmarshalDALC();
                int ltxidHash = Arrays.hashCode(ltxidbytes);
                if (this.connection.thinACLastLtxidHash != ltxidHash) {
                    LogicalTransactionId ltxid = new LogicalTransactionId(ltxidbytes);
                    this.connection.thinACCurrentLTXID = ltxid;
                    NTFLTXIDEvent ltxidevent = new NTFLTXIDEvent(this.connection, ltxid);
                    this.connection.notify(ltxidevent);
                    this.connection.thinACLastLtxidHash = ltxidHash;
                    return;
                }
                return;
            case 8:
                this.meg.unmarshalUB2();
                long flags_kpdxcAppContCtl = this.meg.unmarshalUB4();
                long errcode_kpdxcAppContCtl = this.meg.unmarshalUB4();
                short queue_kpdxcAppContCtl = this.meg.unmarshalUB1();
                byte[] replayctx_kpdxcAppContCtl = this.meg.unmarshalDALC();
                this.replayContext = new ReplayContext(flags_kpdxcAppContCtl, queue_kpdxcAppContCtl, replayctx_kpdxcAppContCtl, errcode_kpdxcAppContCtl);
                if (this.connection.cancelInProgressFlag && this.replayContext.isDuplicate(this.connection.thinACLastReplayContextReceived)) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "readSPF", "cancel in progress, FILTERED OUT duplicate replay context: {0}", (String) null, (String) null, (Object) this.replayContext);
                    this.replayContext = null;
                    return;
                }
                return;
            case 9:
                int nbOfDtys3 = this.meg.unmarshalUB2();
                for (int i2 = 0; i2 < nbOfDtys3; i2++) {
                    NTFXSEvent xsevent = new NTFXSEvent(this.connection);
                    this.connection.notify(xsevent);
                }
                return;
            case 10:
                if (!$assertionsDisabled && !this.connection.enableACSupport) {
                    throw new AssertionError("Driver AC support is disabled but server still sent state-signatures piggyback");
                }
                this.meg.unmarshalUB2();
                long signatureFlags = this.meg.unmarshalSB8();
                long clientSignature = this.meg.unmarshalSB8();
                long serverSignature = this.meg.unmarshalSB8();
                if (this.connection.hasServerCompileTimeCapability(39, 15)) {
                    long signatureVer = this.meg.unmarshalUB4();
                    this.stateSignatures = new StateSignatures(signatureFlags, clientSignature, serverSignature, signatureVer);
                    long templateId = this.meg.unmarshalSB8();
                    byte[] overflow = this.meg.unmarshalDALC();
                    long overflowSig = this.meg.unmarshalSB8();
                    boolean isFullOverflow = (signatureFlags & 64) == 64;
                    this.templateOverflow = new TemplateOverflow(templateId, overflow, isFullOverflow, overflowSig);
                    return;
                }
                this.stateSignatures = new StateSignatures(signatureFlags, clientSignature, serverSignature);
                return;
            case 11:
                this.meg.unmarshalUB2();
                byte[] buf = this.meg.unmarshalDALC();
                processOCSHRDKEY(buf);
                return;
        }
    }

    void unmarshalError() throws SQLException, IOException {
        this.connection.getT4CTTIoer().unmarshal();
    }

    void processError() throws SQLException {
        this.connection.getT4CTTIoer().processError();
    }

    final long getErrorCode() throws SQLException {
        return this.connection.getT4CTTIoer().retCode;
    }

    @Override // oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    protected void processTKN(long tokenNumber) throws SQLException {
        if (this.tokenNumber != tokenNumber) {
            throw DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401, "Unexpected token number: " + tokenNumber + ". Expected token number: " + this.tokenNumber);
        }
    }

    private boolean redoCursorClose() {
        if (this.connection.lastPiggyBackCursorCloseSeqNumber != 0 && this.connection.getT4CTTIoer().callNumber != this.connection.currentTTCSeqNumber) {
            short s = this.connection.getT4CTTIoer().callNumber == 127 ? (short) 1 : (short) (this.connection.getT4CTTIoer().callNumber + 1);
            while (true) {
                short j = s;
                if (j != this.connection.currentTTCSeqNumber) {
                    if (this.connection.lastPiggyBackCursorCloseSeqNumber == j) {
                        return true;
                    }
                    if (j == 127) {
                        s = 1;
                    } else {
                        s = (short) (j + 1);
                    }
                } else {
                    return false;
                }
            }
        } else {
            return false;
        }
    }

    private boolean isCanceledError(SQLException sqlException) {
        return sqlException.getErrorCode() == 1013 || (this.connection.cancelInProgressFlag && sqlException.getMessage() != null && sqlException.getMessage().contains("ORA-01013"));
    }

    protected void setMarshallingException(SQLException marshallingException) {
        if (this.marshallingException != null && this.marshallingException != marshallingException) {
            marshallingException.addSuppressed(this.marshallingException);
        }
        this.marshallingException = marshallingException;
    }

    private void handleOutOfSequenceError(T4CTTIoer11 oer) throws SQLException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "handleOutOfSequenceError", "TTIOER call number {0} does not match TTIFUN sequence number {1}", (String) null, (Throwable) null, Short.valueOf(oer.callNumber), Byte.valueOf(this.sequenceNumber));
        if (this.connection.cancelInProgressFlag && this.replayContext != null) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "handleOutOfSequenceError", "cancel in progress, FILTERED OUT duplicate replay context at OER: {0}", (String) null, (String) null, (Object) this.replayContext);
            this.replayContext = null;
        }
        oer.processError(true);
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIfun$RpcIoTask.class */
    private class RpcIoTask implements Pipeline.IoTask {
        private final ErrorSet continueOnErrorSet;
        private final ThrowingRunnable<? extends Exception> startCallback;
        private boolean isFirstSend = true;
        private final Consumer<Throwable> completionCallback;

        RpcIoTask(ErrorSet continueOnErrorSet, ThrowingRunnable<? extends Exception> startCallback, Consumer<Throwable> completionCallback) {
            this.continueOnErrorSet = continueOnErrorSet;
            this.startCallback = startCallback;
            this.completionCallback = completionCallback;
        }

        @Override // oracle.jdbc.driver.Pipeline.IoTask
        public final short functionCode() {
            return T4CTTIfun.this.funCode;
        }

        @Override // oracle.jdbc.driver.Pipeline.IoTask
        public final ErrorSet continueOnErrorSet() {
            return this.continueOnErrorSet;
        }

        /* JADX WARN: Failed to check method for inline after forced processoracle.jdbc.driver.T4CTTIfun.access$302(oracle.jdbc.driver.T4CTTIfun, long):long */
        @Override // oracle.jdbc.driver.Pipeline.IoTask
        public Pipeline.IoStatus send() throws Exception {
            if (this.isFirstSend) {
                this.isFirstSend = false;
                T4CTTIfun.this.beforeRoundTrip();
                this.startCallback.runOrThrow();
            }
            T4CTTIfun.this.connection.needLineUnchecked();
            T4CTTIfun.this.sendPiggyBackMessages();
            T4CTTIfun.this.send(T4CTTIfun.access$302(T4CTTIfun.this, T4CTTIfun.this.connection.pipeline().getNextToken()));
            return Pipeline.IoStatus.PENDING_RECEIVE;
        }

        @Override // oracle.jdbc.driver.Pipeline.IoTask
        public Pipeline.IoStatus receive() throws Exception {
            T4CTTIfun.this.connection.needLineUnchecked();
            try {
                T4CTTIfun.this.receive();
                return Pipeline.IoStatus.COMPLETE;
            } catch (SQLException sqlException) {
                throw T4CTTIfun.this.handleRpcFailure(sqlException);
            }
        }

        @Override // oracle.jdbc.driver.Pipeline.IoTask
        public final void complete(Throwable error) {
            T4CTTIfun.this.afterRoundTrip(error != null);
            T4CTTIfun.this.handleRpcCompletionAlways();
            this.completionCallback.accept(error);
        }

        public String toString() {
            return "[function-code = " + ((int) functionCode()) + ", function-description = " + DatabaseFunction.valueOfFunctionCode(functionCode()) + ", continue-on-error = " + continueOnErrorSet() + "]";
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIfun$PipelinedIoTask.class */
    private final class PipelinedIoTask extends RpcIoTask {
        private volatile boolean isSendIncomplete;

        PipelinedIoTask(ErrorSet continueOnErrorSet, ThrowingRunnable<? extends Exception> startCallback, Consumer<Throwable> completionCallback) {
            super(continueOnErrorSet, startCallback, completionCallback);
            this.isSendIncomplete = false;
        }

        @Override // oracle.jdbc.driver.T4CTTIfun.RpcIoTask, oracle.jdbc.driver.Pipeline.IoTask
        public Pipeline.IoStatus send() throws Exception {
            Pipeline.IoStatus ioStatus;
            if (this.isSendIncomplete) {
                boolean z = !T4CTTIfun.this.meg.endPipelineRequest();
                this.isSendIncomplete = z;
                return z ? Pipeline.IoStatus.PENDING_SEND : Pipeline.IoStatus.PENDING_RECEIVE;
            }
            T4CTTIfun.this.meg.beginPipelineRequest();
            try {
                if (continueOnErrorSet() != ErrorSet.ALL_ERRORS) {
                    new T4CTTIoplopn(T4CTTIfun.this.connection).doOPLOPN(0, (short) 0, (short) 2);
                }
                super.send();
                return ioStatus;
            } finally {
                boolean z2 = !T4CTTIfun.this.meg.endPipelineRequest();
                this.isSendIncomplete = z2;
                ioStatus = z2 ? Pipeline.IoStatus.PENDING_SEND : Pipeline.IoStatus.PENDING_RECEIVE;
            }
        }

        @Override // oracle.jdbc.driver.T4CTTIfun.RpcIoTask, oracle.jdbc.driver.Pipeline.IoTask
        public Pipeline.IoStatus receive() throws Exception {
            T4CTTIfun.this.meg.beginPipelineResponse();
            try {
                return super.receive();
            } finally {
                T4CTTIfun.this.meg.endPipelineResponse();
            }
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIfun$NonPipelinedIoTask.class */
    private final class NonPipelinedIoTask extends RpcIoTask {
        NonPipelinedIoTask(ErrorSet continueOnErrorSet, ThrowingRunnable<? extends Exception> startCallback, Consumer<Throwable> completionCallback) {
            super(continueOnErrorSet, startCallback, completionCallback);
        }

        @Override // oracle.jdbc.driver.T4CTTIfun.RpcIoTask, oracle.jdbc.driver.Pipeline.IoTask
        public Pipeline.IoStatus send() throws Exception {
            Pipeline.IoStatus status = super.send();
            T4CTTIfun.this.meg.flush();
            return status;
        }
    }
}
