package oracle.jdbc.driver.json.binary;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.xa.OracleXAResource;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonHeader.class */
public class OsonHeader {
    private static byte[][] UTF8_MAPPING = new byte[2048];
    int version;
    short flags;
    short flags2;
    private int uniqueFields;
    private int[] hashIdArray;
    private String[] fieldNames;
    private int fieldHeapSize;
    private int uniqueFields2;
    private int[] hashIdArray2;
    private String[] fieldNames2;
    private int fieldHeapSize2;
    int ubFieldId;
    int treeSegmentSize;
    int treeSegmentOffset;
    int tinyNodeCount;
    int updateFlags;
    int extendedTreeSegmentOffset;
    Map<Integer, Integer> forwardingAddress;

    public OsonHeader(OsonBuffer b, OracleJsonExceptions.ExceptionFactory f) {
        readHeader(b, f);
        this.treeSegmentOffset = b.position();
        if (isScalar()) {
            return;
        }
        if (this.uniqueFields > 0) {
            readDictionary(b);
        }
        if (this.uniqueFields2 > 0) {
            readDictionary2(b);
        }
        if (!isSet(4) || !isSet(2)) {
            throw new UnsupportedOperationException();
        }
        int updateHeaderOffset = this.treeSegmentOffset + this.treeSegmentSize;
        if (updateHeaderOffset < b.buffer().limit()) {
            b.position(updateHeaderOffset);
            readUpdateHeader(b, f);
        }
    }

    public void readHeader(OsonBuffer b, OracleJsonExceptions.ExceptionFactory f) {
        if (b.buffer().remaining() <= 6) {
            throw OracleJsonExceptions.CORRUPT2.create(f, Integer.valueOf(b.buffer().remaining()));
        }
        int magicAndVersion = b.getInt();
        if ((magicAndVersion & (-256)) != -11904512) {
            throw OracleJsonExceptions.CORRUPT.create(f, new Object[0]);
        }
        this.version = magicAndVersion & 255;
        if (this.version < 1 || this.version > 4) {
            throw OracleJsonExceptions.UNSUPPORTED_VERSION.create(f, Integer.valueOf(this.version));
        }
        this.flags = (short) b.getUB2();
        if (!isSet(16)) {
            if (isSet(8)) {
                this.ubFieldId = 4;
                this.uniqueFields = b.getUB4int();
            } else if (isSet(1024)) {
                this.ubFieldId = 2;
                this.uniqueFields = b.getUB2();
            } else {
                this.ubFieldId = 1;
                this.uniqueFields = b.getUB1();
            }
            this.fieldHeapSize = isSet(2048) ? b.getUB4int() : b.getUB2();
            if (this.version >= 3) {
                this.flags2 = (short) b.getUB2();
                this.uniqueFields2 = b.getUB4int();
                this.fieldHeapSize2 = b.getUB4int();
            }
            this.treeSegmentSize = isSet(4096) ? b.getUB4int() : b.getUB2();
            this.tinyNodeCount = b.getUB2();
            return;
        }
        this.treeSegmentSize = isSet(4096) ? b.getUB4int() : b.getUB2();
    }

    private void readDictionary(OsonBuffer b) {
        int[] fieldNameOffsets = new int[this.uniqueFields];
        this.hashIdArray = new int[this.uniqueFields];
        this.fieldNames = new String[this.uniqueFields];
        ub1(b, this.hashIdArray);
        if (isSet(2048)) {
            ub4int(b, fieldNameOffsets);
        } else {
            ub2(b, fieldNameOffsets);
        }
        int offset = b.position();
        this.treeSegmentOffset = b.position() + this.fieldHeapSize;
        for (int i = 0; i < fieldNameOffsets.length; i++) {
            b.position(fieldNameOffsets[i] + offset);
            int len = b.getUB1();
            this.fieldNames[i] = b.readString(len);
        }
        b.position(this.treeSegmentOffset);
    }

    private void readDictionary2(OsonBuffer b) {
        int[] fieldNameOffsets2 = new int[this.uniqueFields2];
        this.hashIdArray2 = new int[this.uniqueFields2];
        this.fieldNames2 = new String[this.uniqueFields2];
        ub2(b, this.hashIdArray2);
        if (isSet2(256)) {
            ub2(b, fieldNameOffsets2);
        } else {
            ub4int(b, fieldNameOffsets2);
        }
        int offset = b.position();
        this.treeSegmentOffset = b.position() + this.fieldHeapSize2;
        for (int i = 0; i < fieldNameOffsets2.length; i++) {
            b.position(fieldNameOffsets2[i] + offset);
            int len = b.getUB2();
            this.fieldNames2[i] = b.readString(len);
        }
        b.position(this.treeSegmentOffset);
    }

    public String getFieldName(int fid) {
        return fid < this.uniqueFields ? this.fieldNames[fid] : this.fieldNames2[fid - this.uniqueFields];
    }

    private void readUpdateHeader(OsonBuffer b, OracleJsonExceptions.ExceptionFactory f) {
        this.updateFlags = b.getUB2();
        int numOverflowAddrSeg = b.getUB2();
        b.getUB4int();
        int overflowMappingSize = b.getUB4int();
        b.getUB4int();
        this.extendedTreeSegmentOffset = b.position() + overflowMappingSize;
        this.forwardingAddress = new HashMap();
        if (isSetUpd(256)) {
            for (int i = 0; i < numOverflowAddrSeg; i++) {
                this.forwardingAddress.put(Integer.valueOf(b.getUB2()), Integer.valueOf(b.getUB2()));
            }
            return;
        }
        for (int i2 = 0; i2 < numOverflowAddrSeg; i2++) {
            this.forwardingAddress.put(Integer.valueOf(b.getUB4int()), Integer.valueOf(b.getUB4int()));
        }
    }

    public boolean isScalar() {
        return isSet(16);
    }

    public boolean isTinyNodeCount() {
        return isSet(8192);
    }

    private void ub1(OsonBuffer s, int[] arr) {
        for (int i = 0; i < arr.length; i++) {
            arr[i] = s.getUB1();
        }
    }

    private void ub2(OsonBuffer s, int[] arr) {
        for (int i = 0; i < arr.length; i++) {
            arr[i] = s.getUB2();
        }
    }

    private void ub4int(OsonBuffer s, int[] arr) {
        for (int i = 0; i < arr.length; i++) {
            arr[i] = s.getUB4int();
        }
    }

    public int getTreeSegmentOffset() {
        return this.treeSegmentOffset;
    }

    public int getFieldHeapSize() {
        return this.fieldHeapSize;
    }

    public String[] getFields() {
        return this.fieldNames;
    }

    public int[] hashIds() {
        return this.hashIdArray;
    }

    private boolean isSet(int f) {
        return (this.flags & f) != 0;
    }

    private boolean isSet2(int f) {
        return (this.flags2 & f) != 0;
    }

    private boolean isSetUpd(int f) {
        return (this.updateFlags & f) != 0;
    }

    public boolean fieldsSorted() {
        return !isSet(32768);
    }

    public boolean relativeOffsets() {
        return isSet(1);
    }

    public int getFieldId(String key) {
        int utf8len = utf8len(key);
        if (utf8len <= OsonConstants.MAX_SMALL_KEY_LENGTH && this.uniqueFields > 0) {
            return getFieldId(key, this.hashIdArray, this.fieldNames);
        }
        if (utf8len <= OsonConstants.MAX_BIG_KEY_LENGTH && this.uniqueFields2 > 0) {
            return this.uniqueFields + getFieldId(key, this.hashIdArray2, this.fieldNames2);
        }
        return -1;
    }

    private int getFieldId(String key, int[] hashIdArray, String[] fieldNames) {
        int hash = ohash(key, null);
        int idx = Arrays.binarySearch(hashIdArray, hash);
        if (idx < 0) {
            return -1;
        }
        while (idx > 0 && hashIdArray[idx - 1] == hash) {
            idx--;
        }
        while (!fieldNames[idx].equals(key)) {
            idx++;
            if (idx >= hashIdArray.length || hashIdArray[idx] != hash) {
                return -1;
            }
        }
        return idx + 1;
    }

    public static int ohash(String key, AtomicInteger length) {
        int iHashUtf8;
        int hash = -2128831035;
        int charlen = key.length();
        int len = charlen;
        int i = 0;
        while (i < charlen) {
            char c = key.charAt(i);
            if (c <= 127) {
                iHashUtf8 = (hash ^ c) * 16777619;
            } else if (c <= 2047) {
                len++;
                iHashUtf8 = (((hash ^ (UTF8_MAPPING[c][0] & 255)) * 16777619) ^ (UTF8_MAPPING[c][1] & 255)) * 16777619;
            } else {
                int cp = key.codePointAt(i);
                if (Character.charCount(cp) == 2) {
                    i++;
                }
                String s = new String(Character.toChars(cp));
                byte[] bytes = s.getBytes(StandardCharsets.UTF_8);
                len += bytes.length - 1;
                iHashUtf8 = hashUtf8(hash, bytes);
            }
            hash = iHashUtf8;
            i++;
        }
        int result = len <= OsonConstants.MAX_SMALL_KEY_LENGTH ? hash & 255 : ((hash & 255) << 8) | ((hash & OracleXAResource.ORAISOLATIONMASK) >> 8);
        if (length != null) {
            length.set(len);
        }
        return result;
    }

    public static int ohash(byte[] key) {
        int res = hashUtf8(-2128831035, key);
        return key.length <= OsonConstants.MAX_SMALL_KEY_LENGTH ? res & 255 : ((res & 255) << 8) | ((res & OracleXAResource.ORAISOLATIONMASK) >> 8);
    }

    static int utf8len(String key) {
        int result = 0;
        for (int i = 0; i < key.length(); i++) {
            char c = key.charAt(i);
            if (c <= 127) {
                result++;
            } else if (c <= 2047) {
                result += 2;
            } else {
                return key.getBytes(StandardCharsets.UTF_8).length;
            }
        }
        return result;
    }

    private static int hashUtf8(int hash, byte[] bytes) {
        for (byte b : bytes) {
            hash = (hash ^ (b & 255)) * 16777619;
        }
        return hash;
    }

    public int numFieldIdBytes() {
        return this.ubFieldId;
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [byte[], byte[][]] */
    static {
        for (int i = 0; i < UTF8_MAPPING.length; i++) {
            UTF8_MAPPING[i] = new String(new char[]{(char) i}).getBytes(StandardCharsets.UTF_8);
        }
    }
}
