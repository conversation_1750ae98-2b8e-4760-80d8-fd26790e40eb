package oracle.jdbc.driver.json.tree;

import java.math.BigDecimal;
import java.math.BigInteger;
import oracle.jdbc.driver.json.OracleJsonExceptions;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonNumberImpl.class */
public abstract class OracleJsonNumberImpl {
    public abstract BigDecimal bigDecimalValue();

    public abstract String getString();

    public boolean isIntegral() {
        return bigDecimalValue().scale() == 0;
    }

    public int intValue() {
        return bigDecimalValue().intValue();
    }

    public int intValueExact() {
        return bigDecimalValue().intValueExact();
    }

    public long longValue() {
        return bigDecimalValue().longValue();
    }

    public long longValueExact() {
        return bigDecimalValue().longValueExact();
    }

    public BigInteger bigIntegerValue() {
        return bigDecimalValue().toBigInteger();
    }

    public BigInteger bigIntegerValueExact() {
        return bigDecimalValue().toBigIntegerExact();
    }

    public double doubleValue() {
        return bigDecimalValue().doubleValue();
    }

    public String toString() {
        return bigDecimalValue().toString();
    }

    public float floatValue() {
        return bigDecimalValue().floatValue();
    }

    protected OracleJsonExceptions.ExceptionFactory getExceptionFactory() {
        return OracleJsonExceptions.ORACLE_FACTORY;
    }
}
