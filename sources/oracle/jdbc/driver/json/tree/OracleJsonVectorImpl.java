package oracle.jdbc.driver.json.tree;

import java.io.StringWriter;
import java.sql.SQLException;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.sql.VECTOR;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonValue;
import oracle.sql.json.OracleJsonVector;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonVectorImpl.class */
public class OracleJsonVectorImpl implements OracleJsonVector {
    private byte[] raw;

    public OracleJsonVectorImpl(byte[] raw) {
        this.raw = raw;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.VECTOR;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        StringWriter writer = new StringWriter();
        JsonSerializerImpl ser = new JsonSerializerImpl(writer);
        ser.write(this);
        ser.close();
        return writer.toString();
    }

    @Override // oracle.sql.json.OracleJsonVector
    public VECTOR getVECTOR() {
        return VECTOR.fromData(this.raw);
    }

    @Override // oracle.sql.json.OracleJsonVector
    public double[] getDoubleArray() {
        try {
            return VECTOR.toDoubleArray(this.raw);
        } catch (SQLException e) {
            throw new OracleJsonException(e);
        }
    }

    @Override // oracle.sql.json.OracleJsonVector
    public float[] getFloatArray() {
        try {
            return VECTOR.toFloatArray(this.raw);
        } catch (SQLException e) {
            throw new OracleJsonException(e);
        }
    }

    @Override // oracle.sql.json.OracleJsonVector
    public byte[] getByteArray() {
        try {
            return VECTOR.toByteArray(this.raw);
        } catch (SQLException e) {
            throw new OracleJsonException(e);
        }
    }

    @Override // oracle.sql.json.OracleJsonVector
    public boolean equals(Object other) {
        return (other instanceof OracleJsonVector) && ((OracleJsonVector) other).getVECTOR().equals(VECTOR.fromData(this.raw));
    }

    @Override // oracle.sql.json.OracleJsonVector
    public int hashCode() {
        return VECTOR.fromData(this.raw).hashCode();
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> wrapper) {
        return wrapper.cast(new JakartaPrimitive.JakartaVectorImpl(this.raw));
    }

    public byte[] raw() {
        return this.raw;
    }
}
