package oracle.jdbc.diagnostics;

import java.util.Collections;
import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;
import java.util.WeakHashMap;
import java.util.concurrent.TimeUnit;
import javax.management.MBeanAttributeInfo;
import javax.management.MBeanConstructorInfo;
import javax.management.MBeanInfo;
import javax.management.MBeanNotificationInfo;
import javax.management.MBeanOperationInfo;
import javax.management.MBeanParameterInfo;
import javax.management.StandardMBean;
import oracle.jdbc.driver.DatabaseError;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/OracleDiagnosticsMXBean.class */
public class OracleDiagnosticsMXBean extends StandardMBean implements DiagnosticsMXBean {
    public static final String EVENT_ENABLE_DIAGNOSE_FIRST_FAILURE_BY_CONN_ID_PREFIX = "Event-Enable-DiagnoseFirstFailure-By-Conn-Prefix";
    public static final String EVENT_DISABLE_DIAGNOSE_FIRST_FAILURE_BY_CONN_ID_PREFIX = "Event-Disable-DiagnoseFirstFailure-By-Conn-Prefix";
    public static final String EVENT_ENABLE_DIAGNOSE_FIRST_FAILURE_BY_TENANT_NAME = "Event-Enable-DiagnoseFirstFailure-By-Tenant-Name";
    public static final String EVENT_DISABLE_DIAGNOSE_FIRST_FAILURE_BY_TENANT_NAME = "Event-Disable-DiagnoseFirstFailure-By-Tenant-Name";
    public static final String EVENT_ENABLE_DIAGNOSE_FIRST_FAILURE_BY_LOGGER_NAME = "Event-Enable-DiagnoseFirstFailure-By-Logger-Name";
    public static final String EVENT_DISABLE_DIAGNOSE_FIRST_FAILURE_BY_LOGGER_NAME = "Event-Disable-DiagnoseFirstFailure-By-Logger-Name";
    public static final String EVENT_ENABLE_DIAGNOSE_FIRST_FAILURE = "Event-Enable-DiagnoseFirstFailure";
    public static final String EVENT_DISABLE_DIAGNOSE_FIRST_FAILURE = "Event-Disable-DiagnoseFirstFailure";
    public static final String EVENT_ENABLE_LOGGING_BY_CONN_ID_PREFIX = "Event-Enable-Logging-By-Conn-Prefix";
    public static final String EVENT_DISABLE_LOGGING_BY_CONN_ID_PREFIX = "Event-Disable-Logging-By-Conn-Prefix";
    public static final String EVENT_ENABLE_LOGGING_BY_TENANT_NAME = "Event-Enable-Logging-By-Tenant-Name";
    public static final String EVENT_DISABLE_LOGGING_BY_TENANT_NAME = "Event-Disable-Logging-By-Tenant-Name";
    public static final String EVENT_ENABLE_LOGGING_BY_LOGGER_NAME = "Event-Enable-Logging-By-Logger-Name";
    public static final String EVENT_DISABLE_LOGGING_BY_LOGGER_NAME = "Event-Disable-Logging-By-Logger-Name";
    public static final String EVENT_ENABLE_LOGGING = "Event-Enable-Logging";
    public static final String EVENT_DISABLE_LOGGING = "Event-Disable-Logging";
    public static final String EVENT_ENABLE_SENSITIVE_DIAGNOSTICS_BY_CONN_ID_PREFIX = "Event-Enable-Sensitive-Diagnostics-By-Conn-Prefix";
    public static final String EVENT_DISABLE_SENSITIVE_DIAGNOSTICS_BY_CONN_ID_PREFIX = "Event-Disable-Sensitive-Diagnostics-By-Conn-Prefix";
    public static final String EVENT_ENABLE_SENSITIVE_DIAGNOSTICS_BY_TENANT_NAME = "Event-Enable-Sensitive-Diagnostics-By-Tenant-Name";
    public static final String EVENT_DISABLE_SENSITIVE_DIAGNOSTICS_BY_TENANT_NAME = "Event-Disable-Sensitive-Diagnostics-By-Tenant-Name";
    public static final String EVENT_ENABLE_SENSITIVE_DIAGNOSTICS_BY_LOGGER_NAME = "Event-Enable-Sensitive-Diagnostics-By-Logger-Name";
    public static final String EVENT_DISABLE_SENSITIVE_DIAGNOSTICS_BY_LOGGER_NAME = "Event-Disable-Sensitive-Diagnostics-By-Logger-Name";
    public static final String EVENT_ENABLE_SENSITIVE_DIAGNOSTICS = "Event-Enable-Sensitive-Diagnostics";
    public static final String EVENT_DISABLE_SENSITIVE_DIAGNOSTICS = "Event-Disable-Sensitive-Diagnostics";
    public static final String EVENT_UPDATE_DIAGNOSTIC_LEVEL_BY_CONN_ID_PREFIX = "Event-Update-Diagnostic-Level-By-Conn-Prefix";
    public static final String EVENT_UPDATE_DIAGNOSTIC_LEVEL_BY_TENANT_NAME = "Event-Update-Diagnostic-Level-By-Tenant-Name";
    public static final String EVENT_UPDATE_DIAGNOSTIC_LEVEL_BY_LOGGER_NAME = "Event-Update-Diagnostic-Level-By-Logger-Name";
    public static final String EVENT_UPDATE_DIAGNOSTIC_LEVEL = "Event-Update-Diagnostic-Level";
    public static final String EVENT_UPDATE_DIAGNOSE_FIRST_FAILURE_SIZE_BY_CONN_ID_PREFIX = "Event-Update-DiagnoseFirstFailure-Size-By-Conn-Prefix";
    public static final String EVENT_UPDATE_DIAGNOSE_FIRST_FAILURE_SIZE_BY_TENANT_NAME = "Event-Update-DiagnoseFirstFailure-Size-By-Tenant-Name";
    public static final String EVENT_UPDATE_DIAGNOSE_FIRST_FAILURE_SIZE_BY_LOGGER_NAME = "Event-Update-DiagnoseFirstFailure-Size-By-Logger-Name";
    public static final String EVENT_UPDATE_DIAGNOSE_FIRST_FAILURE_SIZE = "Event-Update-DiagnoseFirstFailure-Size";
    public static final String EVENT_DUMP_DIAGNOSE_FIRST_FAILURE = "Event-Dump-DiagnoseFirstFailure";
    private static final String DUMMY_STRING = "DUMMY";
    private static final int OPERATIONS_LOG_SIZE = 20;
    private static final OracleDiagnosticsMXBean INSTANCE = new OracleDiagnosticsMXBean();
    private static final String CLASS_NAME = OracleDiagnosticsMXBean.class.getName();
    private static final Map<PropertyChangeListener, String> WEAK_PROPERTY_CHANGE_SUPPORT = Collections.synchronizedMap(new WeakHashMap());
    private static final Object SOURCE_BEAN = new Object();
    private static final Queue<String> OPERATIONS_LOG = new LinkedList();

    private OracleDiagnosticsMXBean() {
        super(DiagnosticsMXBean.class, true);
    }

    public static OracleDiagnosticsMXBean getInstance() {
        return INSTANCE;
    }

    public static void addPropertyChangeListener(PropertyChangeListener propertyChangeListener) {
        WEAK_PROPERTY_CHANGE_SUPPORT.put(propertyChangeListener, DUMMY_STRING);
    }

    public static void removePropertyChangeListener(PropertyChangeListener propertyChangeListener) {
        WEAK_PROPERTY_CHANGE_SUPPORT.remove(propertyChangeListener);
    }

    private void firePropertyChange(String propertyName, Object oldValue, Object newValue) {
        WEAK_PROPERTY_CHANGE_SUPPORT.forEach((k, v) -> {
            k.propertyChange(new PropertyChangeEvent(SOURCE_BEAN, propertyName, oldValue, newValue));
        });
    }

    private void logOperation(String operationDesc) {
        OPERATIONS_LOG.add(operationDesc);
        if (OPERATIONS_LOG.size() > 20) {
            OPERATIONS_LOG.remove();
        }
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableDiagnoseFirstFailureByConnectionIdPrefix(String connectionIdPrefix) {
        firePropertyChange(EVENT_ENABLE_DIAGNOSE_FIRST_FAILURE_BY_CONN_ID_PREFIX, null, connectionIdPrefix);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableDiagnoseFirstFailureByConnectionIdPrefix(String connectionIdPrefix) {
        firePropertyChange(EVENT_DISABLE_DIAGNOSE_FIRST_FAILURE_BY_CONN_ID_PREFIX, null, connectionIdPrefix);
        logOperation("Disabled Diagnose First Failure by connection id prefix : " + connectionIdPrefix);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableDiagnoseFirstFailureByTenantName(String tenantName) {
        firePropertyChange(EVENT_ENABLE_DIAGNOSE_FIRST_FAILURE_BY_TENANT_NAME, null, tenantName);
        logOperation("Enabled Diagnose First Failure by tenant name : " + tenantName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableDiagnoseFirstFailureByTenantName(String tenantName) {
        firePropertyChange(EVENT_DISABLE_DIAGNOSE_FIRST_FAILURE_BY_TENANT_NAME, null, tenantName);
        logOperation("Disabled Diagnose First Failure by tenant name : " + tenantName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableDiagnoseFirstFailureByLoggerName(String loggerName) {
        firePropertyChange(EVENT_ENABLE_DIAGNOSE_FIRST_FAILURE_BY_LOGGER_NAME, null, loggerName);
        logOperation("Enabled Diagnose First Failure by logger name : " + loggerName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableDiagnoseFirstFailureByLoggerName(String loggerName) {
        firePropertyChange(EVENT_DISABLE_DIAGNOSE_FIRST_FAILURE_BY_LOGGER_NAME, null, loggerName);
        logOperation("Disabled Diagnose First Failure by logger name : " + loggerName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableDiagnoseFirstFailure() {
        firePropertyChange(EVENT_ENABLE_DIAGNOSE_FIRST_FAILURE, null, null);
        logOperation("Enabled Diagnose First Failure");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableDiagnoseFirstFailure() {
        firePropertyChange(EVENT_DISABLE_DIAGNOSE_FIRST_FAILURE, null, null);
        logOperation("Disabled Diagnose First Failure");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableLoggingByConnectionIdPrefix(String connectionIdPrefix) {
        firePropertyChange(EVENT_ENABLE_LOGGING_BY_CONN_ID_PREFIX, null, connectionIdPrefix);
        logOperation("Enabled logging by connection id prefix : " + connectionIdPrefix);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableLoggingByConnectionIdPrefix(String connectionIdPrefix) {
        firePropertyChange(EVENT_DISABLE_LOGGING_BY_CONN_ID_PREFIX, null, connectionIdPrefix);
        logOperation("Disabled logging by connection id prefix : " + connectionIdPrefix);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableLoggingByTenantName(String tenantName) {
        firePropertyChange(EVENT_ENABLE_LOGGING_BY_TENANT_NAME, null, tenantName);
        logOperation("Enabled logging by tenant name : " + tenantName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableLoggingByTenantName(String tenantName) {
        firePropertyChange(EVENT_DISABLE_LOGGING_BY_TENANT_NAME, null, tenantName);
        logOperation("Disabled logging by tenant name : " + tenantName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableLoggingByLoggerName(String loggerName) {
        firePropertyChange(EVENT_ENABLE_LOGGING_BY_LOGGER_NAME, null, loggerName);
        logOperation("Enabled logging by logger name : " + loggerName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableLoggingByLoggerName(String loggerName) {
        firePropertyChange(EVENT_DISABLE_LOGGING_BY_LOGGER_NAME, null, loggerName);
        logOperation("Disabled logging by logger name : " + loggerName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableLogging() {
        AbstractDiagnosable.enableGlobalDebug(true);
        firePropertyChange(EVENT_ENABLE_LOGGING, null, null);
        logOperation("Enabled logging");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableLogging() {
        AbstractDiagnosable.enableGlobalDebug(false);
        firePropertyChange(EVENT_DISABLE_LOGGING, null, null);
        logOperation("Disabled logging");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public boolean isLoggingEnabled() {
        return AbstractDiagnosable.isGlobalDebugEnabled();
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableSensitiveDiagnosticsByConnectionIdPrefix(String connectionIdPrefix) {
        firePropertyChange(EVENT_ENABLE_SENSITIVE_DIAGNOSTICS_BY_CONN_ID_PREFIX, null, connectionIdPrefix);
        logOperation("Enabled sensitive diagnostics by connection id prefix : " + connectionIdPrefix);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableSensitiveDiagnosticsByConnectionIdPrefix(String connectionIdPrefix) {
        firePropertyChange(EVENT_DISABLE_SENSITIVE_DIAGNOSTICS_BY_CONN_ID_PREFIX, null, connectionIdPrefix);
        logOperation("Disabled sensitive diagnostics by connection id prefix : " + connectionIdPrefix);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableSensitiveDiagnosticsByTenantName(String tenantName) {
        firePropertyChange(EVENT_ENABLE_SENSITIVE_DIAGNOSTICS_BY_TENANT_NAME, null, tenantName);
        logOperation("Enabled sensitive diagnostics by tenant name : " + tenantName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableSensitiveDiagnosticsByTenantName(String tenantName) {
        firePropertyChange(EVENT_DISABLE_SENSITIVE_DIAGNOSTICS_BY_TENANT_NAME, null, tenantName);
        logOperation("Disabled sensitive diagnostics by tenant name : " + tenantName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableSensitiveDiagnosticsByLoggerName(String loggerName) {
        firePropertyChange(EVENT_ENABLE_SENSITIVE_DIAGNOSTICS_BY_LOGGER_NAME, null, loggerName);
        logOperation("Enabled sensitive diagnostics by logger name : " + loggerName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableSensitiveDiagnosticsByLoggerName(String loggerName) {
        firePropertyChange(EVENT_DISABLE_SENSITIVE_DIAGNOSTICS_BY_LOGGER_NAME, null, loggerName);
        logOperation("Disabled sensitive diagnostics by logger name : " + loggerName);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableSensitiveDiagnostics() {
        firePropertyChange(EVENT_ENABLE_SENSITIVE_DIAGNOSTICS, null, null);
        logOperation("Enabled sensitive diagnostics");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableSensitiveDiagnostics() {
        firePropertyChange(EVENT_DISABLE_SENSITIVE_DIAGNOSTICS, null, null);
        logOperation("Disabled sensitive diagnostics");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void updateDiagnosticLevelByConnectionIdPrefix(String connectionIdPrefix, String loggingLevel) {
        firePropertyChange(EVENT_UPDATE_DIAGNOSTIC_LEVEL_BY_CONN_ID_PREFIX, null, new Object[]{connectionIdPrefix, loggingLevel});
        logOperation("Updated diagnostic level by connection id prefix : " + connectionIdPrefix + ", loggingLevel : " + loggingLevel);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void updateDiagnosticLevelByTenantName(String tenantName, String loggingLevel) {
        firePropertyChange(EVENT_UPDATE_DIAGNOSTIC_LEVEL_BY_TENANT_NAME, null, new Object[]{tenantName, loggingLevel});
        logOperation("Updated diagnostic level by tenant name : " + tenantName + ", loggingLevel : " + loggingLevel);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void updateDiagnosticLevelByLoggerName(String loggerName, String loggingLevel) {
        firePropertyChange(EVENT_UPDATE_DIAGNOSTIC_LEVEL_BY_LOGGER_NAME, null, new Object[]{loggerName, loggingLevel});
        logOperation("Updated diagnostic level by logger name : " + loggerName + ", loggingLevel : " + loggingLevel);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void updateDiagnosticLevel(String loggingLevel) {
        firePropertyChange(EVENT_UPDATE_DIAGNOSTIC_LEVEL, null, loggingLevel);
        logOperation("Updated diagnostic level");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void updateBufferSizeByConnectionIdPrefix(String connectionIdPrefix, Integer bufferSize) {
        firePropertyChange(EVENT_UPDATE_DIAGNOSE_FIRST_FAILURE_SIZE_BY_CONN_ID_PREFIX, null, new Object[]{connectionIdPrefix, bufferSize});
        logOperation("Updated buffer size by connection id prefix : " + connectionIdPrefix + ", buffer size : " + bufferSize);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void updateBufferSizeByTenantName(String tenantName, Integer bufferSize) {
        firePropertyChange(EVENT_UPDATE_DIAGNOSE_FIRST_FAILURE_SIZE_BY_TENANT_NAME, null, new Object[]{tenantName, bufferSize});
        logOperation("Updated buffer size by tenant name : " + tenantName + ", buffer size : " + bufferSize);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void updateBufferSizeByLoggerName(String loggerName, Integer bufferSize) {
        firePropertyChange(EVENT_UPDATE_DIAGNOSE_FIRST_FAILURE_SIZE_BY_LOGGER_NAME, null, new Object[]{loggerName, bufferSize});
        logOperation("Updated buffer size by logger name : " + loggerName + ", buffer size : " + bufferSize);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void updateBufferSize(Integer bufferSize) {
        firePropertyChange(EVENT_UPDATE_DIAGNOSE_FIRST_FAILURE_SIZE, null, bufferSize);
        logOperation("Updated buffer size");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void readLoggingConfigFile(String configFile) {
        AbstractDiagnosable.onConfigFileChange(configFile);
        logOperation("Read logging config file : " + configFile);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void addErrorCodeToWatchList(String errorCode) {
        Diagnostic.addErrorCodeToWatchList(errorCode);
        logOperation("Added error code to watch list : " + errorCode);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void removeErrorCodeFromWatchList(String errorCode) {
        Diagnostic.removeErrorCodeFromWatchList(errorCode);
        logOperation("Removed error code from watch list : " + errorCode);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public String showErrorCodesWatchList() {
        return Diagnostic.getErrorCodesWatchList();
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void resetErrorCodeWatchList() {
        Diagnostic.resetErrorCodeWatchList();
        logOperation("Reset error code watch list");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void dumpDiagnoseFirstFailure() {
        firePropertyChange(EVENT_DUMP_DIAGNOSE_FIRST_FAILURE, null, null);
        logOperation("Dumped Diagnose First Failure");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void dumpDiagnoseFirstFailureWhenFutureExceptionContains(String commaSeparatedKeywords) {
        Diagnostic.dumpDiagnoseFirstFailureWhenNextExceptionContains(commaSeparatedKeywords);
        logOperation("Dump Diagnose First Failure when future exception contains : " + commaSeparatedKeywords);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public String showExceptionKeywords() {
        return Diagnostic.getExceptionKeywords();
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public String showRecentOperations() {
        return String.join("\n", OPERATIONS_LOG);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void clearExceptionKeywords() {
        Diagnostic.clearExceptionKeywords();
        logOperation("Cleared exception keywords");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableWriteLogsToDiagnoseFirstFailure(Boolean enabled) {
        AbstractDiagnosable.enableWriteLogsToDiagnoseFirstFailure(enabled.booleanValue());
        logOperation("Enabled write logs to Diagnose First Failure");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public boolean isWriteLogsToDiagnoseFirstFailure() {
        return AbstractDiagnosable.isWriteLogsToDiagnoseFirstFailureEnabled();
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void enableMetrics() {
        AbstractDiagnosable.enableMetrics(true);
        logOperation("Enabled metrics");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void disableMetrics() {
        AbstractDiagnosable.enableMetrics(false);
        logOperation("Disabled metrics");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public String showMetrics() {
        return Metrics.getReport(TimeUnit.MILLISECONDS);
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public void clearMetrics() {
        Metrics.clear();
        logOperation("Cleared metrics");
    }

    @Override // oracle.jdbc.diagnostics.DiagnosticsMXBean
    public boolean isMetricsEnabled() {
        return AbstractDiagnosable.isMetricsEnabled();
    }

    public MBeanInfo getMBeanInfo() {
        MBeanAttributeInfo[] mBeanAttributeInfos = {new MBeanAttributeInfo("LoggingEnabled", Boolean.class.getName(), DatabaseError.findMessage("DiagnosticsMBeanLoggingEnabledAttributeDescription", (Object) null), true, false, true), new MBeanAttributeInfo("WriteLogsToDiagnoseFirstFailure", Boolean.class.getName(), DatabaseError.findMessage("DiagnosticsMBeanWriteLogsToDiagnoseFirstFailureAttributeDescription", (Object) null), true, false, true), new MBeanAttributeInfo("MetricsEnabled", Boolean.class.getName(), DatabaseError.findMessage("DiagnosticsMBeanMetricsEnabledAttributeDescription", (Object) null), true, false, true)};
        MBeanOperationInfo[] mBeanOperationInfos = {new MBeanOperationInfo("enableDiagnoseFirstFailureByConnectionIdPrefix", DatabaseError.findMessage("DiagnosticsMBeanEnableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("connectionIdPrefix", String.class.getName(), DatabaseError.findMessage("DiagnosticsConnectionIdPrefixParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableDiagnoseFirstFailureByConnectionIdPrefix", DatabaseError.findMessage("DiagnosticsMBeanDisableDiagnoseFirstFailureByConnectionIdPrefixOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("connectionIdPrefix", String.class.getName(), DatabaseError.findMessage("DiagnosticsConnectionIdPrefixParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableDiagnoseFirstFailureByTenantName", DatabaseError.findMessage("DiagnosticsMBeanEnableDiagnoseFirstFailureByTenantNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("tenantName", String.class.getName(), DatabaseError.findMessage("DiagnosticsTenantNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableDiagnoseFirstFailureByTenantName", DatabaseError.findMessage("DiagnosticsMBeanDisableDiagnoseFirstFailureByTenantNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("tenantName", String.class.getName(), DatabaseError.findMessage("DiagnosticsTenantNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableDiagnoseFirstFailureByLoggerName", DatabaseError.findMessage("DiagnosticsMBeanEnableDiagnoseFirstFailureByLoggerNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("loggerName", String.class.getName(), DatabaseError.findMessage("DiagnosticsLoggerNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableDiagnoseFirstFailureByLoggerName", DatabaseError.findMessage("DiagnosticsMBeanDisableDiagnoseFirstFailureByLoggerNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("loggerName", String.class.getName(), DatabaseError.findMessage("DiagnosticsLoggerNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableDiagnoseFirstFailure", DatabaseError.findMessage("DiagnosticsMBeanEnableDiagnoseFirstFailureOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("disableDiagnoseFirstFailure", DatabaseError.findMessage("DiagnosticsMBeanDisableDiagnoseFirstFailureOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("enableLoggingByConnectionIdPrefix", DatabaseError.findMessage("DiagnosticsEnableLoggingByConnectionIdPrefixOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("connectionIdPrefix", String.class.getName(), DatabaseError.findMessage("DiagnosticsConnectionIdPrefixParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableLoggingByConnectionIdPrefix", DatabaseError.findMessage("DiagnosticsDisableLoggingByConnectionIdPrefixOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("connectionIdPrefix", String.class.getName(), DatabaseError.findMessage("DiagnosticsConnectionIdPrefixParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableMetrics", DatabaseError.findMessage("DiagnosticsEnableMetricsOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("disableMetrics", DatabaseError.findMessage("DiagnosticsDisableMetricsOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("showMetrics", DatabaseError.findMessage("DiagnosticsShowMetricsOperationDescription", (Object) null), (MBeanParameterInfo[]) null, String.class.getName(), 1), new MBeanOperationInfo("clearMetrics", DatabaseError.findMessage("DiagnosticsClearMetricsOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("enableLoggingByTenantName", DatabaseError.findMessage("DiagnosticsEnableLoggingByTenantNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("tenantName", String.class.getName(), DatabaseError.findMessage("DiagnosticsTenantNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableLoggingByTenantName", DatabaseError.findMessage("DiagnosticsDisableLoggingByTenantNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("tenantName", String.class.getName(), DatabaseError.findMessage("DiagnosticsTenantNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableLoggingByLoggerName", DatabaseError.findMessage("DiagnosticsEnableLoggingByLoggerNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("loggerName", String.class.getName(), DatabaseError.findMessage("DiagnosticsLoggerNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableLoggingByLoggerName", DatabaseError.findMessage("DiagnosticsDisableLoggingByLoggerNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("loggerName", String.class.getName(), DatabaseError.findMessage("DiagnosticsLoggerNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableLogging", DatabaseError.findMessage("DiagnosticsEnableLoggingOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("disableLogging", DatabaseError.findMessage("DiagnosticsDisableLoggingOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("enableSensitiveDiagnosticsByConnectionIdPrefix", DatabaseError.findMessage("DiagnosticsEnableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("connectionIdPrefix", String.class.getName(), DatabaseError.findMessage("DiagnosticsConnectionIdPrefixParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableSensitiveDiagnosticsByConnectionIdPrefix", DatabaseError.findMessage("DiagnosticsDisableSensitiveDiagnosticsByConnectionIdPrefixOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("connectionIdPrefix", String.class.getName(), DatabaseError.findMessage("DiagnosticsConnectionIdPrefixParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableSensitiveDiagnosticsByTenantName", DatabaseError.findMessage("DiagnosticsEnableSensitiveDiagnosticsByTenantNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("tenantName", String.class.getName(), DatabaseError.findMessage("DiagnosticsTenantNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableSensitiveDiagnosticsByTenantName", DatabaseError.findMessage("DiagnosticsDisableSensitiveDiagnosticsByTenantNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("tenantName", String.class.getName(), DatabaseError.findMessage("DiagnosticsTenantNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableSensitiveDiagnosticsByLoggerName", DatabaseError.findMessage("DiagnosticsEnableSensitiveDiagnosticsByLoggerNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("loggerName", String.class.getName(), DatabaseError.findMessage("DiagnosticsLoggerNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("disableSensitiveDiagnosticsByLoggerName", DatabaseError.findMessage("DiagnosticsDisableSensitiveDiagnosticsByLoggerNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("loggerName", String.class.getName(), DatabaseError.findMessage("DiagnosticsLoggerNameParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("enableSensitiveDiagnostics", DatabaseError.findMessage("DiagnosticsEnableSensitiveDiagnosticsOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("disableSensitiveDiagnostics", DatabaseError.findMessage("DiagnosticsDisableSensitiveDiagnosticsOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("updateDiagnosticLevelByConnectionIdPrefix", DatabaseError.findMessage("DiagnosticsUpdateDiagnosticLevelByConnectionIdPrefixOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("connectionIdPrefix", String.class.getName(), DatabaseError.findMessage("DiagnosticsConnectionIdPrefixParameterDescription", (Object) null)), new MBeanParameterInfo("level", String.class.getName(), DatabaseError.findMessage("DiagnosticsLevelParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("updateDiagnosticLevelByTenantName", DatabaseError.findMessage("DiagnosticsUpdateDiagnosticLevelByTenantNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("tenantName", String.class.getName(), DatabaseError.findMessage("DiagnosticsTenantNameParameterDescription", (Object) null)), new MBeanParameterInfo("level", String.class.getName(), DatabaseError.findMessage("DiagnosticsLevelParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("updateDiagnosticLevelByLoggerName", DatabaseError.findMessage("DiagnosticsUpdateDiagnosticLevelByLoggerNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("loggerName", String.class.getName(), DatabaseError.findMessage("DiagnosticsLoggerNameParameterDescription", (Object) null)), new MBeanParameterInfo("level", String.class.getName(), DatabaseError.findMessage("DiagnosticsLevelParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("updateDiagnosticLevel", DatabaseError.findMessage("DiagnosticsUpdateDiagnosticLevelOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("level", String.class.getName(), DatabaseError.findMessage("DiagnosticsLevelParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("updateBufferSizeByConnectionIdPrefix", DatabaseError.findMessage("DiagnosticsUpdateBufferSizeByConnectionIdPrefixOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("connectionIdPrefix", String.class.getName(), DatabaseError.findMessage("DiagnosticsConnectionIdPrefixParameterDescription", (Object) null)), new MBeanParameterInfo("bufferSize", Integer.class.getName(), DatabaseError.findMessage("DiagnosticsBufferSizeParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("updateBufferSizeByTenantName", DatabaseError.findMessage("DiagnosticsUpdateBufferSizeByTenantNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("tenantName", String.class.getName(), DatabaseError.findMessage("DiagnosticsTenantNameParameterDescription", (Object) null)), new MBeanParameterInfo("bufferSize", Integer.class.getName(), DatabaseError.findMessage("DiagnosticsBufferSizeParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("updateBufferSizeByLoggerName", DatabaseError.findMessage("DiagnosticsUpdateBufferSizeByLoggerNameOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("loggerName", String.class.getName(), DatabaseError.findMessage("DiagnosticsLoggerNameParameterDescription", (Object) null)), new MBeanParameterInfo("bufferSize", Integer.class.getName(), DatabaseError.findMessage("DiagnosticsBufferSizeParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("updateBufferSize", DatabaseError.findMessage("DiagnosticsUpdateBufferSizeOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("bufferSize", Integer.class.getName(), DatabaseError.findMessage("DiagnosticsBufferSizeParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("readLoggingConfigFile", DatabaseError.findMessage("DiagnosticsReadLoggingConfigFileOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("configFile", String.class.getName(), DatabaseError.findMessage("DiagnosticsConfigFileParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("addErrorCodeToWatchList", DatabaseError.findMessage("DiagnosticsAddErrorCodeToWatchListOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("errorCode", String.class.getName(), DatabaseError.findMessage("DiagnosticsErrorCodesParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("removeErrorCodeFromWatchList", DatabaseError.findMessage("DiagnosticsRemoveErrorCodeFromWatchListOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("errorCode", String.class.getName(), DatabaseError.findMessage("DiagnosticsErrorCodesParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("showErrorCodesWatchList", DatabaseError.findMessage("DiagnosticsShowErrorCodesWatchListOperationDescription", (Object) null), (MBeanParameterInfo[]) null, String.class.getName(), 0), new MBeanOperationInfo("resetErrorCodeWatchList", DatabaseError.findMessage("DiagnosticsResetErrorCodesWatchListOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("dumpDiagnoseFirstFailure", DatabaseError.findMessage("DiagnosticsDumpDiagnoseFirstFailureOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("dumpDiagnoseFirstFailureWhenFutureExceptionContains", DatabaseError.findMessage("DiagnosticsDumpDiagnoseFirstFailureWhenFutureExceptionContainsOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("commaSeparatedKeywords", String.class.getName(), DatabaseError.findMessage("DiagnosticsCommaSeparatedKeywordsParameterDescription", (Object) null))}, Void.class.getName(), 1), new MBeanOperationInfo("showExceptionKeywords", DatabaseError.findMessage("DiagnosticsShowExceptionKeywords", (Object) null), (MBeanParameterInfo[]) null, String.class.getName(), 1), new MBeanOperationInfo("showRecentOperations", DatabaseError.findMessage("DiagnosticsShowRecentOperations", (Object) null), (MBeanParameterInfo[]) null, String.class.getName(), 1), new MBeanOperationInfo("clearExceptionKeywords", DatabaseError.findMessage("DiagnosticsClearExceptionKeywordsOperationDescription", (Object) null), (MBeanParameterInfo[]) null, Void.class.getName(), 1), new MBeanOperationInfo("enableWriteLogsToDiagnoseFirstFailure", DatabaseError.findMessage("DiagnosticsEnableWriteLogsToDiagnoseFirstFailureOperationDescription", (Object) null), new MBeanParameterInfo[]{new MBeanParameterInfo("enabled", Boolean.class.getName(), DatabaseError.findMessage("DiagnosticsEnabledParameterDescription", (Object) null))}, Void.class.getName(), 1)};
        return new MBeanInfo(CLASS_NAME, DatabaseError.findMessage("DiagnosticsMBeanDescription", (Object) null), mBeanAttributeInfos, (MBeanConstructorInfo[]) null, mBeanOperationInfos, (MBeanNotificationInfo[]) null);
    }
}
