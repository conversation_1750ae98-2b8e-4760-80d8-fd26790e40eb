package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.function.Consumer;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoplend.class */
final class T4CTTIoplend extends T4CTTIfun {
    private Long pipelineId;

    T4CTTIoplend(T4CConnection t4cConnection) {
        super(t4cConnection, (byte) 3);
        setFunCode((short) 200);
    }

    void doOPLEND(Long pipelineId) throws SQLException, IOException {
        this.pipelineId = pipelineId;
        doRPC();
    }

    void doOPLENDAsync(Long pipelineId, Consumer<Throwable> callback) {
        this.pipelineId = pipelineId;
        doRPCAsync(callback);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (this.pipelineId != null) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.pipelineId.longValue());
        } else {
            this.meg.marshalNULLPTR();
        }
    }
}
