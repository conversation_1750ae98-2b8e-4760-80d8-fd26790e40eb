package oracle.jdbc.driver.json.tree;

import java.sql.SQLException;
import java.sql.Wrapper;
import java.util.AbstractList;
import java.util.List;
import javax.json.JsonArray;
import javax.json.JsonNumber;
import javax.json.JsonObject;
import javax.json.JsonString;
import javax.json.JsonValue;
import oracle.sql.json.OracleJsonArray;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpArrayImpl.class */
public class JsonpArrayImpl extends AbstractList<JsonValue> implements JsonArray, Wrapper {
    OracleJsonArrayImpl wrapped;

    JsonpArrayImpl(OracleJsonArrayImpl wrapped) {
        this.wrapped = wrapped;
    }

    public JsonValue.ValueType getValueType() {
        return JsonValue.ValueType.ARRAY;
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public int size() {
        return this.wrapped.size();
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
    public boolean isEmpty() {
        return this.wrapped.isEmpty();
    }

    @Override // java.util.AbstractList, java.util.List
    public JsonValue get(int index) {
        OracleJsonValue v = this.wrapped.get(index);
        return (JsonValue) v.wrap(JsonValue.class);
    }

    /* JADX WARN: Type inference failed for: r0v2, types: [oracle.sql.json.OracleJsonArray] */
    @Override // java.util.AbstractList, java.util.List
    public List<JsonValue> subList(int fromIndex, int toIndex) {
        return (List) this.wrapped.subList2(fromIndex, toIndex).wrap(JsonArray.class);
    }

    public boolean getBoolean(int i) {
        return this.wrapped.getBoolean(i);
    }

    public boolean getBoolean(int i, boolean d) {
        if (i < 0 || i >= this.wrapped.size()) {
            return d;
        }
        OracleJsonValue v = this.wrapped.get(i);
        if (OracleJsonValue.TRUE.equals(v)) {
            return true;
        }
        if (OracleJsonValue.FALSE.equals(v)) {
            return false;
        }
        return d;
    }

    public int getInt(int i) {
        return this.wrapped.getInt(i);
    }

    public int getInt(int i, int d) {
        if (i < 0 || i >= this.wrapped.size()) {
            return d;
        }
        OracleJsonValue v = this.wrapped.get(i);
        if (v instanceof OracleJsonNumber) {
            return ((OracleJsonNumber) v).intValue();
        }
        return d;
    }

    public JsonArray getJsonArray(int i) {
        return (JsonArray) this.wrapped.get(i).asJsonArray().wrap(JsonArray.class);
    }

    public JsonNumber getJsonNumber(int i) {
        return (JsonNumber) ((OracleJsonNumber) this.wrapped.get(i)).wrap(JsonNumber.class);
    }

    public JsonObject getJsonObject(int i) {
        return (JsonObject) this.wrapped.get(i).asJsonObject().wrap(JsonObject.class);
    }

    public JsonString getJsonString(int i) {
        return (JsonString) this.wrapped.get(i).wrap(JsonString.class);
    }

    public <T extends JsonValue> List<T> getValuesAs(Class<T> cls) {
        return new AbstractList<T>() { // from class: oracle.jdbc.driver.json.tree.JsonpArrayImpl.1
            /* JADX WARN: Incorrect return type in method signature: (I)TT; */
            @Override // java.util.AbstractList, java.util.List
            public JsonValue get(int index) {
                return JsonpArrayImpl.this.get(index);
            }

            @Override // java.util.AbstractCollection, java.util.Collection, java.util.List
            public int size() {
                return JsonpArrayImpl.this.wrapped.size();
            }
        };
    }

    public String getString(int i) {
        return get(i).getString();
    }

    public String getString(int i, String d) {
        if (i < 0 || i >= this.wrapped.size()) {
            return d;
        }
        OracleJsonValue v = this.wrapped.get(i);
        switch (v.getOracleJsonType()) {
            case BINARY:
            case DATE:
            case INTERVALDS:
            case INTERVALYM:
            case STRING:
            case TIMESTAMP:
                return ((JsonString) v.wrap(JsonString.class)).getString();
            default:
                return d;
        }
    }

    public boolean isNull(int index) {
        return this.wrapped.isNull(index);
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        return iface.isAssignableFrom(OracleJsonArray.class);
    }

    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> iface) throws SQLException {
        try {
            return iface.cast(this.wrapped);
        } catch (ClassCastException e) {
            throw new SQLException(e.getMessage(), e);
        }
    }
}
