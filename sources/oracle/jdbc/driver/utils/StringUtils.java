package oracle.jdbc.driver.utils;

import java.sql.SQLException;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/StringUtils.class */
public class StringUtils {
    public static String byteArrayToHexUnicode(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder(src.length);
        for (byte byteChar : src) {
            stringBuilder.append(String.format("%02X", Byte.valueOf(byteChar)));
        }
        return stringBuilder.toString();
    }

    public static String hexUnicode(int c) throws SQLException {
        String hex = Integer.toHexString(c);
        switch (hex.length()) {
            case 0:
                return "\\0000";
            case 1:
                return "\\000" + hex;
            case 2:
                return "\\00" + hex;
            case 3:
                return "\\0" + hex;
            case 4:
                return "\\" + hex;
            default:
                throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, 89, "Unexpected case in StringUtils.hexUnicode: " + c).fillInStackTrace());
        }
    }

    public static String[] splitString(String str, int start, int numberOfChars) {
        if (str == null) {
            return null;
        }
        if (str.isEmpty()) {
            return new String[0];
        }
        String[] result = new String[(str.length() / numberOfChars) + (str.length() % numberOfChars == 0 ? 0 : 1)];
        StringBuilder lastChars = new StringBuilder(numberOfChars);
        int j = 0;
        for (int i = 0; i < str.length() - start; i++) {
            lastChars.append(str.charAt(start + i));
            j++;
            if (j == numberOfChars) {
                result[i / numberOfChars] = lastChars.toString();
                if (i != str.length() - 1) {
                    lastChars = new StringBuilder(numberOfChars);
                    j = 0;
                }
            } else if (i == str.length() - 1) {
                result[result.length - 1] = lastChars.toString();
            }
        }
        return result;
    }

    public static char getQTickClosingDelimiter(char openingDelimiter) {
        switch (openingDelimiter) {
            case '(':
                return ')';
            case '<':
                return '>';
            case '[':
                return ']';
            case '{':
                return '}';
            default:
                return openingDelimiter;
        }
    }

    public static boolean isWhiteSpace(char c) {
        switch (c) {
            case '\t':
            case '\n':
            case '\r':
            case ' ':
                return true;
            default:
                return false;
        }
    }
}
