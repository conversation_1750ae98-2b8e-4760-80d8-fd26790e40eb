package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_es.class */
public class ErrorMessagesJson_es extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Se ha producido una excepción de E/S"}, new Object[]{"26302", "El año \"{0}\" no está soportado"}, new Object[]{"26303", "Desbordamiento. El valor es demasiado grande: {0}."}, new Object[]{"26304", "Opción no soportada (no implantada)."}, new Object[]{"26305", "El archivo JSON binario no es válido o está corrupto."}, new Object[]{"26306", "Versión del archivo JSON binario no soportada: {0}."}, new Object[]{"26307", "La clave codificada en UTF-8 no debe superar los 256 bytes. La siguiente clave excede este límite: \"{0}\"."}, new Object[]{"26308", "El archivo JSON especificado es demasiado grande para codificarse como archivo JSON binario. El tamaño de las imágenes codificadas no debe exceder los 2 GB."}, new Object[]{"26309", "El archivo JSON binario no es válido o está corrupto. La imagen especificada solo contiene {0} bytes."}, new Object[]{"26310", "El período java.time.Period especificado tiene días configurados, pero el intervalo de año a mes de Oracle no soporta los días."}, new Object[]{"26311", "Generador cerrado antes de finalizar."}, new Object[]{"26312", "Se debe especificar una clave de objeto en este contexto."}, new Object[]{"26313", "Escritura no válida. Ya se ha escrito un valor completo."}, new Object[]{"26314", "Finalización no permitida en este contexto."}, new Object[]{"26315", "Clave no permitida en este contexto."}, new Object[]{"26316", "Valor esperado tras la clave."}, new Object[]{"26317", "El estado del analizador debe ser {0}."}, new Object[]{"26318", "El estado del analizador no debe ser {0}."}, new Object[]{"26319", "El analizador debe tener un valor."}, new Object[]{"26320", "\"{0}\" no es un tipo de envoltorio soportado."}, new Object[]{"26321", "Este objeto no se puede modificar. Para realizar una copia modificable, utilice OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Esta matriz no se puede modificar. Para realizar una copia modificable, utilice OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "El objeto JSON contiene una clave duplicada: {0}."}, new Object[]{"26324", "No se puede detectar automáticamente la codificación. No hay suficientes caracteres."}, new Object[]{"26325", "Se esperaba el token EOF, pero se ha obtenido {0}."}, new Object[]{"26326", "Carácter {0} inesperado en la línea {1} y la columna {2}."}, new Object[]{"26327", "Carácter inesperado {0} en la línea {1} y la columna {2}. Valor esperado: {3}."}, new Object[]{"26328", "Token no válido {0} en la línea {1} y la columna {2}. Los tokens esperados son: {3}."}, new Object[]{"26329", "JsonParser#getString() solo es válido para los estados del analizador KEY_NAME, VALUE_STRING, VALUE_NUMBER, pero el estado actual del analizador es {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() solo es válido para el estado del analizador VALUE_NUMBER, pero el estado actual del analizador es {0}."}, new Object[]{"26331", "JsonParser#getInt() solo es válido para el estado del analizador VALUE_NUMBER, pero el estado actual del analizador es {0}."}, new Object[]{"26332", "JsonParser#getLong() solo es válido para el estado del analizador VALUE_NUMBER, pero el estado actual del analizador es {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() solo es válido para el estado del analizador VALUE_NUMBER, pero el estado actual del analizador es {0}."}, new Object[]{"26334", "JsonParser#getArray() solo es válido para el estado de analizador START_ARRAY, pero el estado actual del analizador es {0}."}, new Object[]{"26335", "JsonParser#getObject() solo es válido para el estado de analizador START_OBJECT, pero el estado actual del analizador es {0}."}, new Object[]{"26336", "No está soportado un registro de hora con una región. Solo están soportadas las zonas horarias con desfase."}, new Object[]{"26337", "Puede que los objetos y matrices en el valor JSON no aniden a niveles más profundos que {0}."}, new Object[]{"26338", "Las claves de un objeto JSON no pueden exceder los 65 535 bytes"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
