package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.OracleResultSetMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CLongAccessor.class */
class T4CLongAccessor extends LongAccessor implements T4CAccessor {
    T4CMAREngine mare;
    static final int PLSQL_MAXLENGTH = 32760;
    byte[][] data;
    int[] nbBytesRead;
    int[] bytesReadSoFar;
    private T4CMarshaller marshaller;

    /* JADX WARN: Type inference failed for: r1v9, types: [byte[], byte[][]] */
    T4CLongAccessor(OracleStatement stmt, int column_pos, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, column_pos, max_len, form, external_type, isOutBind, false);
        this.data = (byte[][]) null;
        this.nbBytesRead = null;
        this.bytesReadSoFar = null;
        this.marshaller = null;
        this.mare = _mare;
        if (stmt.isFetchStreams) {
            this.data = new byte[stmt.rowPrefetch];
            for (int i = 0; i < stmt.rowPrefetch; i++) {
                this.data[i] = new byte[4080];
            }
            this.nbBytesRead = new int[stmt.rowPrefetch];
            this.bytesReadSoFar = new int[stmt.rowPrefetch];
        }
    }

    /* JADX WARN: Type inference failed for: r1v11, types: [byte[], byte[][]] */
    T4CLongAccessor(OracleStatement stmt, int column_pos, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, column_pos, max_len, nullable, flags, precision, scale, contflag, total_elems, form);
        this.data = (byte[][]) null;
        this.nbBytesRead = null;
        this.bytesReadSoFar = null;
        this.marshaller = null;
        this.mare = _mare;
        if (stmt != null && stmt.implicitDefineForLobPrefetchDone) {
            this.definedColumnType = 0;
            this.definedColumnSize = 0;
        } else {
            this.definedColumnType = _definedColumnType;
            this.definedColumnSize = _definedColumnSize;
        }
        if (stmt.isFetchStreams) {
            this.data = new byte[stmt.rowPrefetch];
            for (int i = 0; i < stmt.rowPrefetch; i++) {
                this.data[i] = new byte[4080];
            }
            this.nbBytesRead = new int[stmt.rowPrefetch];
            this.bytesReadSoFar = new int[stmt.rowPrefetch];
        }
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        return getMarshaller().unmarshalOneRow(this);
    }

    int readStreamFromWire(byte[] buffer, int offset, int length, int[] escapeSequenceArr, boolean[] readHeaderArr, boolean[] readAsNonStreamArr, T4CMAREngine mare, T4CTTIoer11 oer) throws SQLException, IOException {
        return getMarshaller().readStreamFromWire(buffer, offset, length, escapeSequenceArr, readHeaderArr, readAsNonStreamArr, mare, oer);
    }

    @Override // oracle.jdbc.driver.Accessor
    void fetchNextColumns() throws SQLException {
        this.statement.continueReadRow(this.columnPosition);
    }

    @Override // oracle.jdbc.driver.Accessor
    int readStream(byte[] buffer, int length) throws SQLException, IOException {
        int currentRow = this.lastRowProcessed;
        if (this.statement.isFetchStreams) {
            int totalBytes = getLength(currentRow);
            int bytesRead = this.bytesReadSoFar[currentRow];
            if (bytesRead == totalBytes) {
                return -1;
            }
            int len = length <= totalBytes - bytesRead ? length : totalBytes - bytesRead;
            this.rowData.setPosition(getOffset(currentRow) + bytesRead);
            this.rowData.getBytes(buffer, 0, len);
            int[] iArr = this.bytesReadSoFar;
            iArr[currentRow] = iArr[currentRow] + len;
            return len;
        }
        int len2 = readStreamFromWire(buffer, 0, length, this.escapeSequenceArr, this.readHeaderArr, this.readAsNonStreamArr, this.mare, ((T4CConnection) this.statement.connection).oer);
        if (this.statement.connection.checksumMode.needToCalculateFetchChecksum() && len2 != -1) {
            long localCheckSum = CRC64.updateChecksum(this.statement.checkSum, buffer, 0, len2);
            this.statement.checkSum = localCheckSum;
        }
        return len2;
    }

    private final T4CMarshaller getMarshaller() {
        if (this.marshaller == null) {
            this.marshaller = (this.describeType == 8 || this.describeType == 112) ? T4CMarshaller.LONG : T4CMarshaller.VARCHAR;
        }
        return this.marshaller;
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean isNullByDescribe() {
        return false;
    }

    @Override // oracle.jdbc.driver.Accessor
    long updateChecksum(long _checkSum, int currentRow) throws SQLException {
        if (isNull(currentRow)) {
            _checkSum = CRC64.updateChecksum(_checkSum, NULL_DATA_BYTES, 0, NULL_DATA_BYTES.length);
        }
        return _checkSum;
    }
}
