package oracle.jdbc.driver;

import java.sql.SQLClientInfoException;
import java.sql.SQLException;
import oracle.jdbc.DatabaseFunction;
import oracle.jdbc.TraceEventListener;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/SimpleTraceContext.class */
class SimpleTraceContext implements TraceEventListener.TraceContext {
    private String connectionId;
    private DatabaseFunction databaseFunction;

    private SimpleTraceContext(Builder builder) {
        this.connectionId = builder.connectionId;
        this.databaseFunction = builder.function;
    }

    public static Builder builder() {
        return new Builder();
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public String getConnectionId() {
        return this.connectionId;
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public String databaseOperation() {
        return this.databaseFunction.getDescription();
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public DatabaseFunction databaseFunction() {
        return this.databaseFunction;
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public String originalSqlText() {
        return null;
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public String actualSqlText() {
        return null;
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public String user() {
        return null;
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public String tenant() {
        return null;
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public String getSqlId() {
        return null;
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public Boolean isCompletedExceptionally() {
        return null;
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public void setClientInfo(String name, String value) throws SQLClientInfoException {
    }

    @Override // oracle.jdbc.TraceEventListener.TraceContext
    public String getClientInfo(String name) throws SQLException {
        return null;
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/SimpleTraceContext$Builder.class */
    public static final class Builder {
        private String connectionId;
        private DatabaseFunction function;

        private Builder() {
        }

        public Builder connectionId(String connectionId) {
            this.connectionId = connectionId;
            return this;
        }

        public Builder function(DatabaseFunction function) {
            this.function = function;
            return this;
        }

        public SimpleTraceContext build() {
            return new SimpleTraceContext(this);
        }
    }
}
