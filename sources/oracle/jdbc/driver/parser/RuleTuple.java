package oracle.jdbc.driver.parser;

import java.io.Serializable;
import java.util.List;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/RuleTuple.class */
public class RuleTuple implements Comparable, Serializable {
    private static final long serialVersionUID = 1;
    public String head;
    public String[] rhs;

    public RuleTuple(String h, List<String> r) {
        this.head = h;
        this.rhs = new String[r.size()];
        int i = 0;
        for (String t : r) {
            int i2 = i;
            i++;
            this.rhs[i2] = t;
        }
    }

    public RuleTuple(String h, String[] r) {
        this.head = h;
        this.rhs = r;
    }

    @Override // java.lang.Comparable
    public int compareTo(Object obj) {
        RuleTuple src = (RuleTuple) obj;
        int cmp = this.head == null ? 0 : this.head.compareTo(src.head);
        if (cmp != 0) {
            return cmp;
        }
        for (int i = 0; i < this.rhs.length && i < src.rhs.length; i++) {
            if (this.rhs[i].compareTo(src.rhs[i]) != 0) {
                return this.rhs[i].compareTo(src.rhs[i]);
            }
        }
        return this.rhs.length - src.rhs.length;
    }

    public String toString() {
        StringBuffer b = new StringBuffer();
        if (this.head != null) {
            b.append(this.head + ":");
        }
        for (String t : this.rhs) {
            b.append(" " + t);
        }
        return b.toString();
    }
}
