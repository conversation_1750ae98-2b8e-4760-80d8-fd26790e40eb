package oracle.jdbc.driver.json;

import java.text.MessageFormat;
import java.util.ResourceBundle;
import oracle.jdbc.driver.DatabaseError;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonGenerationException;
import oracle.sql.json.OracleJsonParsingException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/OracleJsonExceptions.class */
public abstract class OracleJsonExceptions {
    private static final int JSON_ERROR_BASE = 26300;
    private String key;
    public static final ExceptionFactory ORACLE_FACTORY = new OracleExceptionFactory();
    public static final OracleJsonExceptions IO = jzn(getKey(1));
    public static final OracleJsonExceptions BAD_YEAR = jzn(getKey(2));
    public static final OracleJsonExceptions NOT_IMPLEMENTED = jzn(getKey(4));
    public static final OracleJsonExceptions CORRUPT = jzn(getKey(5));
    public static final OracleJsonExceptions UNSUPPORTED_VERSION = jzn(getKey(6));
    public static final OracleJsonExceptions LONG_KEY = jzn(getKey(7));
    public static final OracleJsonExceptions IMAGE_TOO_BIG = jzn(getKey(8));
    public static final OracleJsonExceptions CORRUPT2 = jzn(getKey(9));
    public static final OracleJsonExceptions NO_DAYS_ALLOWED = jzn(getKey(10));
    public static final OracleJsonExceptions BAD_WRAP = jzn(getKey(20));
    public static final OracleJsonExceptions PARSER_ENC_DETECT_FAIL = jzn(getKey(24));
    public static final OracleJsonExceptions BAD_TIMESTAMP_TZ = jzn(getKey(36));
    public static final OracleJsonExceptions GENERATION_INCOMPLETE = gen(getKey(11));
    public static final OracleJsonExceptions MISSING_KEY = gen(getKey(12));
    public static final OracleJsonExceptions EXTRA_EVENTS = gen(getKey(13));
    public static final OracleJsonExceptions BAD_END = gen(getKey(14));
    public static final OracleJsonExceptions BAD_KEY = gen(getKey(15));
    public static final OracleJsonExceptions EXPECTED_VALUE = gen(getKey(16));
    public static final OracleJsonExceptions DUPLICATE_KEY = gen(getKey(23));
    public static final OracleJsonExceptions NEST_DEPTH_EXCEEDED = gen(getKey(37));
    public static final OracleJsonExceptions KEY_TOO_LONG = gen(getKey(38));
    public static final OracleJsonExceptions PARSER_EXPECTED_EOF = par(getKey(25));
    public static final OracleJsonExceptions TOKEN_UNEXPECTED_CHAR = par(getKey(26));
    public static final OracleJsonExceptions TOKEN_EXPECTED_CHAR = par(getKey(27));
    public static final OracleJsonExceptions PARSER_INVALID_TOKEN = par(getKey(28));
    public static final OracleJsonExceptions OVERFLOW = ill(getKey(3));
    public static final OracleJsonExceptions BAD_PARSER_STATE = ill(getKey(17));
    public static final OracleJsonExceptions BAD_PARSER_STATE3 = ill(getKey(18));
    public static final OracleJsonExceptions BAD_PARSER_STATE_VALUE = ill(getKey(19));
    public static final OracleJsonExceptions PARSER_GETSTRING_ERR = ill(getKey(29));
    public static final OracleJsonExceptions PARSER_ISINTEGRAL_ERR = ill(getKey(30));
    public static final OracleJsonExceptions PARSER_GETINT_ERR = ill(getKey(31));
    public static final OracleJsonExceptions PARSER_GETLONG_ERR = ill(getKey(32));
    public static final OracleJsonExceptions PARSER_GETBIGDECIMAL_ERR = ill(getKey(33));
    public static final OracleJsonExceptions PARSER_GETARRAY_ERR = ill(getKey(34));
    public static final OracleJsonExceptions PARSER_GETOBJECT_ERR = ill(getKey(35));
    public static final OracleJsonExceptions OBJ_NOT_MUTABLE = uso(getKey(21));
    public static final OracleJsonExceptions ARR_NOT_MUTABLE = uso(getKey(22));
    private static final ResourceBundle MESSAGES = ResourceBundle.getBundle("oracle.jdbc.driver.json.ErrorMessagesJson");

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/OracleJsonExceptions$ExceptionFactory.class */
    public interface ExceptionFactory {
        RuntimeException createJsonException(String str, Throwable th);

        RuntimeException createJsonException(String str);

        RuntimeException createGenerationException(String str);

        RuntimeException createGenerationException(String str, Throwable th);

        RuntimeException createParsingException(String str);

        RuntimeException createParsingException(String str, Throwable th);
    }

    public abstract RuntimeException create(ExceptionFactory exceptionFactory, Throwable th, Object... objArr);

    public abstract RuntimeException create(ExceptionFactory exceptionFactory, Object... objArr);

    private static String getKey(int errorNumber) {
        return String.format("%05d", Integer.valueOf(JSON_ERROR_BASE + errorNumber));
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/OracleJsonExceptions$OracleExceptionFactory.class */
    private static final class OracleExceptionFactory implements ExceptionFactory {
        private OracleExceptionFactory() {
        }

        @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
        public RuntimeException createJsonException(String message, Throwable cause) {
            return new OracleJsonException(message, cause);
        }

        @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
        public RuntimeException createJsonException(String message) {
            return new OracleJsonException(message);
        }

        @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
        public RuntimeException createGenerationException(String message, Throwable cause) {
            return new OracleJsonGenerationException(message, cause);
        }

        @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
        public RuntimeException createGenerationException(String message) {
            return new OracleJsonGenerationException(message);
        }

        @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
        public RuntimeException createParsingException(String message) {
            return new OracleJsonParsingException(message);
        }

        @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
        public RuntimeException createParsingException(String message, Throwable cause) {
            return new OracleJsonParsingException(message, cause);
        }
    }

    private OracleJsonExceptions(String key) {
        this.key = key;
    }

    public String getMessage(Object... params) {
        String url = "";
        if (DatabaseError.isErrorUrlEnabled()) {
            url = System.lineSeparator() + DatabaseError.ERROR_URL_PREFIX + this.key + "/";
        }
        return "ORA-" + this.key + ": " + MessageFormat.format(MESSAGES.getString(this.key), params) + url;
    }

    private static OracleJsonExceptions jzn(String key) {
        return new OracleJsonExceptions(key) { // from class: oracle.jdbc.driver.json.OracleJsonExceptions.1
            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public RuntimeException create(ExceptionFactory f, Throwable cause, Object... params) {
                return f.createJsonException(getMessage(params), cause);
            }

            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public RuntimeException create(ExceptionFactory f, Object... params) {
                return f.createJsonException(getMessage(params));
            }
        };
    }

    private static OracleJsonExceptions par(String key) {
        return new OracleJsonExceptions(key) { // from class: oracle.jdbc.driver.json.OracleJsonExceptions.2
            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public RuntimeException create(ExceptionFactory f, Throwable cause, Object... params) {
                return f.createJsonException(getMessage(params), cause);
            }

            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public RuntimeException create(ExceptionFactory f, Object... params) {
                return f.createJsonException(getMessage(params));
            }
        };
    }

    private static OracleJsonExceptions gen(String key) {
        return new OracleJsonExceptions(key) { // from class: oracle.jdbc.driver.json.OracleJsonExceptions.3
            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public RuntimeException create(ExceptionFactory f, Throwable cause, Object... params) {
                return f.createGenerationException(getMessage(params), cause);
            }

            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public RuntimeException create(ExceptionFactory f, Object... params) {
                return f.createGenerationException(getMessage(params));
            }
        };
    }

    private static OracleJsonExceptions ill(String key) {
        return new OracleJsonExceptions(key) { // from class: oracle.jdbc.driver.json.OracleJsonExceptions.4
            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public IllegalStateException create(ExceptionFactory f, Throwable cause, Object... params) {
                return new IllegalStateException(getMessage(params), cause);
            }

            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public IllegalStateException create(ExceptionFactory f, Object... params) {
                return new IllegalStateException(getMessage(params));
            }
        };
    }

    private static OracleJsonExceptions uso(String key) {
        return new OracleJsonExceptions(key) { // from class: oracle.jdbc.driver.json.OracleJsonExceptions.5
            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public UnsupportedOperationException create(ExceptionFactory f, Throwable cause, Object... params) {
                return new UnsupportedOperationException(getMessage(params), cause);
            }

            @Override // oracle.jdbc.driver.json.OracleJsonExceptions
            public UnsupportedOperationException create(ExceptionFactory f, Object... params) {
                return new UnsupportedOperationException(getMessage(params));
            }
        };
    }
}
