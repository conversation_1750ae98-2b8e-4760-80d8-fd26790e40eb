package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedList;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;
import oracle.sql.CLOB;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CClobAccessor.class */
class T4CClobAccessor extends ClobAccessor {
    T4CMAREngine mare;
    short[] prefetchedClobCharset;
    boolean[] prefetchedClobDBVary;
    final int[] meta;
    ArrayList<LinkedList<CLOB>> registeredCLOBs;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CClobAccessor.class.desiredAssertionStatus();
    }

    private T4CClobAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare, short[] _prefetchedClobCharset, boolean[] _prefetchedClobDBVary) throws SQLException {
        this(stmt, max_len, nullable, flags, precision, scale, contflag, total_elems, form, _definedColumnType, _definedColumnSize, _mare);
        this.prefetchedClobCharset = _prefetchedClobCharset;
        this.prefetchedClobDBVary = _prefetchedClobDBVary;
    }

    T4CClobAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, 4000, form, external_type, isOutBind, false);
        this.prefetchedClobCharset = null;
        this.prefetchedClobDBVary = null;
        this.meta = new int[1];
        this.registeredCLOBs = new ArrayList<>(10);
        this.mare = _mare;
    }

    T4CClobAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, 4000, nullable, flags, precision, scale, contflag, total_elems, form);
        this.prefetchedClobCharset = null;
        this.prefetchedClobDBVary = null;
        this.meta = new int[1];
        this.registeredCLOBs = new ArrayList<>(10);
        this.mare = _mare;
        this.definedColumnType = _definedColumnType;
        this.definedColumnSize = _definedColumnSize;
    }

    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        boolean isStream = false;
        if (!isUseless()) {
            if (isUnexpected()) {
                long pos = this.rowData.getPosition();
                unmarshalColumnMetadata();
                unmarshalBytes();
                this.rowData.setPosition(pos);
                setNull(this.lastRowProcessed, true);
            } else if (isNullByDescribe()) {
                setNull(this.lastRowProcessed, true);
                unmarshalColumnMetadata();
                if (this.statement.connection.versionNumber < 9200) {
                    processIndicator(0);
                }
            } else {
                unmarshalColumnMetadata();
                isStream = unmarshalBytes();
            }
        }
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
        return isStream;
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        throw new NoSuchMethodError("oracle.jdbc.driver.T4C_lobAccessor.copyRow");
    }

    boolean unmarshalBytes() throws SQLException, IOException {
        int len = (int) this.mare.unmarshalUB4();
        if (len == 0) {
            setNull(this.lastRowProcessed, true);
            processIndicator(0);
            return false;
        }
        if (isPrefetched()) {
            unmarshalPrefetchData();
        }
        setOffset(this.lastRowProcessed);
        int actualLength = ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare);
        setNull(this.lastRowProcessed, actualLength == 0);
        setLength(this.lastRowProcessed, actualLength);
        processIndicator(actualLength);
        return false;
    }

    void unmarshalPrefetchData() throws SQLException, IOException {
        setPrefetchedLength(this.lastRowProcessed, this.mare.unmarshalSB8());
        setPrefetchedChunkSize(this.lastRowProcessed, (int) this.mare.unmarshalUB4());
        if (this.lobPrefetchSizeForThisColumn > 0) {
            boolean dbVary = ((byte) this.mare.unmarshalUB1()) == 1;
            if (dbVary) {
                setPrefetchedDataCharset(this.lastRowProcessed, this.mare.unmarshalUB2());
            } else {
                setPrefetchedDataCharset(this.lastRowProcessed, 0);
            }
            setPrefetchedDataFormOfUse(this.lastRowProcessed, this.mare.unmarshalUB1());
            setPrefetchedDataOffset(this.lastRowProcessed);
            setPrefetchedDataLength(this.lastRowProcessed, ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare));
            return;
        }
        setPrefetchedDataOffset(this.lastRowProcessed);
        setPrefetchedDataLength(this.lastRowProcessed, 0);
    }

    private void saveCLOBReference(int rowPosition, CLOB clob) {
        LinkedList<CLOB> clobsAtThisRow;
        if (this.registeredCLOBs.size() > rowPosition) {
            clobsAtThisRow = this.registeredCLOBs.get(rowPosition);
        } else {
            clobsAtThisRow = new LinkedList<>();
            while (this.registeredCLOBs.size() < rowPosition) {
                this.registeredCLOBs.add(new LinkedList<>());
            }
            this.registeredCLOBs.add(rowPosition, clobsAtThisRow);
        }
        if (clobsAtThisRow == null) {
            clobsAtThisRow = new LinkedList<>();
        }
        clobsAtThisRow.add(clob);
    }

    @Override // oracle.jdbc.driver.ClobAccessor, oracle.jdbc.driver.LobCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getObject(currentRow);
        }
        if (isNull(currentRow)) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
            case -3:
            case -2:
                return getBytes(currentRow);
            case -1:
            case 1:
            case 12:
                return getString(currentRow);
            case oracle.jdbc.OracleTypes.CLOB /* 2005 */:
                return getCLOB(currentRow);
            case oracle.jdbc.OracleTypes.NCLOB /* 2011 */:
                return getNCLOB(currentRow);
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final short[] prefetchedClobCharset = this.prefetchedClobCharset;
        final boolean[] prefetchedClobDBVary = this.prefetchedClobDBVary;
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new LobCommonAccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CClobAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CClobAccessor(stmt, T4CClobAccessor.this.describeMaxLength, T4CClobAccessor.this.nullable, -1, T4CClobAccessor.this.precision, T4CClobAccessor.this.scale, T4CClobAccessor.this.contflag, -1, T4CClobAccessor.this.formOfUse, T4CClobAccessor.this.definedColumnType, T4CClobAccessor.this.definedColumnSize, null, prefetchedClobCharset, prefetchedClobDBVary);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
