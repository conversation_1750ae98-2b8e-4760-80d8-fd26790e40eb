package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.Map;
import oracle.jdbc.oracore.OracleType;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.sql.Datum;
import oracle.sql.REF;
import oracle.sql.StructDescriptor;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/RefTypeAccessor.class */
class RefTypeAccessor extends TypeAccessor {
    static final int MAXLENGTH = -1;

    RefTypeAccessor(OracleStatement stmt, String typeName, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        super(Representation.REF_TYPE, stmt, -1, isStoredInBindData);
        init(stmt, DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT, DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT, form, isOutBind);
        initForDataAccess(external_type, 0, typeName);
    }

    RefTypeAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, String typeName) throws SQLException {
        super(Representation.REF_TYPE, stmt, -1, false);
        init(stmt, DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT, DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT, form, false);
        initForDescribe(DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT, max_len, nullable, flags, precision, scale, contflag, total_elems, form, typeName);
        initForDataAccess(0, max_len, typeName);
    }

    RefTypeAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, String typeName, OracleType otype) throws SQLException {
        super(Representation.REF_TYPE, stmt, -1, false);
        init(stmt, DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT, DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT, form, false);
        this.describeOtype = otype;
        initForDescribe(DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT, max_len, nullable, flags, precision, scale, contflag, total_elems, form, typeName);
        this.internalOtype = otype;
        initForDataAccess(0, max_len, typeName);
    }

    @Override // oracle.jdbc.driver.TypeAccessor
    final OracleType otypeFromName(String typeName) throws SQLException {
        if (!this.outBind) {
            return TypeDescriptor.getTypeDescriptor(typeName, this.statement.connection).getPickler();
        }
        return StructDescriptor.createDescriptor(typeName, this.statement.connection).getOracleTypeADT();
    }

    @Override // oracle.jdbc.driver.TypeAccessor, oracle.jdbc.driver.Accessor
    void initForDataAccess(int external_type, int max_len, String typeName) throws SQLException {
        super.initForDataAccess(external_type, max_len, typeName);
        this.byteLength = this.statement.connection.refTypeAccessorByteLen;
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    REF getREF(int currentRow) throws SQLException {
        if (isNull(currentRow)) {
            return null;
        }
        byte[] data = pickledBytes(currentRow);
        OracleTypeADT otype = (OracleTypeADT) this.internalOtype;
        return new REF(otype.getFullName(), this.statement.connection, data);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        return getObject(currentRow, this.statement.connection.getTypeMap());
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Datum getOracleObject(int currentRow) throws SQLException {
        return getREF(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow, Map<String, Class<?>> map) throws SQLException {
        REF ref = getREF(currentRow);
        if (ref == null) {
            return null;
        }
        return ref.toJdbc(map);
    }
}
