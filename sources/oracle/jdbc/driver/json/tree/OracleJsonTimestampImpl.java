package oracle.jdbc.driver.json.tree;

import java.time.LocalDateTime;
import java.util.Arrays;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.binary.OsonPrimitiveConversions;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.TIMESTAMP;
import oracle.sql.json.OracleJsonTimestamp;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonTimestampImpl.class */
public class OracleJsonTimestampImpl implements OracleJsonTimestamp {
    byte[] raw;

    public OracleJsonTimestampImpl(byte[] raw) {
        this.raw = raw;
    }

    public OracleJsonTimestampImpl(LocalDateTime value) {
        this.raw = OsonPrimitiveConversions.toOracleTimestamp(getExceptionFactory(), value);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.TIMESTAMP;
    }

    @Override // oracle.sql.json.OracleJsonTimestamp
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonTimestamp)) {
            return false;
        }
        if (!(other instanceof OracleJsonTimestampImpl)) {
            throw new UnsupportedOperationException();
        }
        OracleJsonTimestampImpl otherd = (OracleJsonTimestampImpl) other;
        return Arrays.equals(otherd.raw, this.raw);
    }

    @Override // oracle.sql.json.OracleJsonTimestamp
    public int hashCode() {
        return Arrays.hashCode(this.raw);
    }

    @Override // oracle.sql.json.OracleJsonTimestamp
    public String getString() {
        return OsonPrimitiveConversions.timestampToString(getExceptionFactory(), this.raw);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        return JsonSerializerImpl.serializeString(getString());
    }

    @Override // oracle.sql.json.OracleJsonTimestamp
    public LocalDateTime getLocalDateTime() {
        byte[] raw = raw();
        return OsonPrimitiveConversions.timestampToLocalDateTime(getExceptionFactory(), raw);
    }

    public byte[] raw() {
        return this.raw;
    }

    @Override // oracle.sql.json.OracleJsonTimestamp
    public TIMESTAMP getTIMESTAMP() {
        return new TIMESTAMP(raw());
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaTimestampImpl(this.raw));
        }
        return c.cast(new JsonpPrimitive.JsonpTimestampImpl(this.raw));
    }

    public OracleJsonExceptions.ExceptionFactory getExceptionFactory() {
        return OracleJsonExceptions.ORACLE_FACTORY;
    }
}
