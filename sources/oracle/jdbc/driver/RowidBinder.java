package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/RowidBinder.class */
public class RowidBinder extends Binder {
    private static final String CLASS_NAME = RowidBinder.class.getName();
    byte[] paramVal;
    Binder theRowidCopyingBinder = null;

    @Override // oracle.jdbc.driver.Binder
    public /* bridge */ /* synthetic */ String toString() {
        return super.toString();
    }

    static void init(Binder x) {
        x.type = (short) 9;
        x.bytelen = 4000;
    }

    RowidBinder(byte[] val) {
        this.paramVal = val;
        init(this);
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        byte[] value = this.paramVal;
        if (clearPriorBindValues) {
            this.paramVal = null;
        }
        if (value == null) {
            bindIndicators[indoffset] = -1;
            if (bindUseDBA) {
                bindDataOffsets[bindDataIndex] = -1;
                bindDataLengths[bindDataIndex] = 0;
            }
        } else {
            bindIndicators[indoffset] = 0;
            int l = value.length;
            bindIndicators[lenoffset] = (short) (l + 2);
            if (bindUseDBA) {
                long pos = bindData.getPosition();
                bindDataOffsets[bindDataIndex] = pos;
                stmt.lastBoundDataOffsets[bindPosition] = pos;
                bindDataLengths[bindDataIndex] = l + 2;
                stmt.lastBoundDataLengths[bindPosition] = l + 2;
                stmt.debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "bind", "first len byte: {0} second len byte: {1}", (String) null, (Throwable) null, () -> {
                    return new Object[]{Integer.toHexString(l >> 8), Integer.toHexString(l & 255)};
                });
                bindData.put((byte) (l >> 8));
                bindData.put((byte) (l & 255));
                bindData.put(value);
            } else {
                bindBytes[byteoffset] = (byte) (l >> 8);
                bindBytes[byteoffset + 1] = (byte) (l & 255);
                System.arraycopy(value, 0, bindBytes, byteoffset + 2, l);
            }
        }
        if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
            if (bindIndicators[indoffset] == -1) {
                localCheckSum = CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
            } else {
                localCheckSum = CRC64.updateChecksum(localCheckSum, value, 0, value.length);
            }
        }
        return localCheckSum;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theRowidCopyingBinder == null) {
            this.theRowidCopyingBinder = new RowidCopyingBinder();
        }
        return this.theRowidCopyingBinder;
    }
}
