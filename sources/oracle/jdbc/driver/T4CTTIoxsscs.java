package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.KeywordValueLong;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxsscs.class */
final class T4CTTIoxsscs extends T4CTTIfun {
    private String userName;
    private KeywordValueLong[] inKV;
    private int inFlags;
    private byte[] userNameArr;
    private byte[] sessionId;
    private KeywordValueLong[] outKV;
    private int outFlags;

    T4CTTIoxsscs(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.userName = null;
        this.inKV = null;
        this.userNameArr = null;
        this.sessionId = null;
        this.outKV = null;
        this.outFlags = -1;
        setFunCode((short) 155);
    }

    void doOXSSCS(String _userName, KeywordValueLong[] _inKV, int _inFlags) throws SQLException, IOException {
        this.userName = _userName;
        this.inKV = _inKV;
        this.inFlags = _inFlags;
        if (this.userName != null && this.userName.length() > 0) {
            this.userNameArr = this.meg.conv.StringToCharBytes(this.userName);
        } else {
            this.userNameArr = null;
        }
        this.sessionId = null;
        this.outKV = null;
        this.outFlags = -1;
        if (this.inKV != null) {
            for (int i = 0; i < this.inKV.length; i++) {
                ((KeywordValueLongI) this.inKV[i]).doCharConversion(this.meg.conv);
            }
        }
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        if (this.userNameArr != null) {
            this.meg.marshalPTR();
            this.meg.marshalSB4(this.userNameArr.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSB4(0);
        }
        boolean sendInKV = false;
        if (this.inKV != null && this.inKV.length > 0) {
            sendInKV = true;
            this.meg.marshalPTR();
            this.meg.marshalSB4(this.inKV.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSB4(0);
        }
        this.meg.marshalUB4(this.inFlags);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        if (this.userNameArr != null) {
            this.meg.marshalCHR(this.userNameArr);
        }
        if (sendInKV) {
            for (int i = 0; i < this.inKV.length; i++) {
                ((KeywordValueLongI) this.inKV[i]).marshal(this.meg);
            }
        }
    }

    byte[] getSessionId() {
        return this.sessionId;
    }

    KeywordValueLong[] getOutKV() {
        return this.outKV;
    }

    int getOutFlags() {
        return this.outFlags;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int sessionIdLength = (int) this.meg.unmarshalUB4();
        this.sessionId = this.meg.unmarshalNBytes(sessionIdLength);
        int xsscsovn = (int) this.meg.unmarshalUB4();
        this.outKV = new KeywordValueLong[xsscsovn];
        for (int i = 0; i < xsscsovn; i++) {
            this.outKV[i] = KeywordValueLongI.unmarshal(this.meg);
        }
        this.outFlags = (int) this.meg.unmarshalUB4();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
