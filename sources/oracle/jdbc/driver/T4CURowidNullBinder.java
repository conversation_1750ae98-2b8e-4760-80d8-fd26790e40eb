package oracle.jdbc.driver;

import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CURowidNullBinder.class */
class T4CURowidNullBinder extends T4CURowidBinder {
    T4CURowidNullBinder() {
        super(null);
    }

    @Override // oracle.jdbc.driver.T4CURowidBinder, oracle.jdbc.driver.RowidBinder, oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        bindIndicators[indoffset] = -1;
        if (bindUseDBA) {
            bindDataOffsets[bindDataIndex] = -1;
            bindDataLengths[bindDataIndex] = 0;
        }
        if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
            localCheckSum = CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
        }
        return localCheckSum;
    }

    @Override // oracle.jdbc.driver.RowidBinder, oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        return this;
    }
}
