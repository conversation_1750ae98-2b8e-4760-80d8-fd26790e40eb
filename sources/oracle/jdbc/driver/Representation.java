package oracle.jdbc.driver;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.NClob;
import java.sql.Ref;
import java.sql.ResultSet;
import java.sql.RowId;
import java.sql.SQLData;
import java.sql.SQLException;
import java.sql.SQLXML;
import java.sql.Struct;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.Period;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.json.JsonArray;
import javax.json.JsonNumber;
import javax.json.JsonObject;
import javax.json.JsonString;
import javax.json.JsonStructure;
import javax.json.JsonValue;
import javax.json.stream.JsonParser;
import oracle.jdbc.OracleData;
import oracle.jdbc.OracleOpaque;
import oracle.sql.ARRAY;
import oracle.sql.BFILE;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.BLOB;
import oracle.sql.BOOLEAN;
import oracle.sql.CHAR;
import oracle.sql.CLOB;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.INTERVALDS;
import oracle.sql.INTERVALYM;
import oracle.sql.NCLOB;
import oracle.sql.NUMBER;
import oracle.sql.OPAQUE;
import oracle.sql.ORAData;
import oracle.sql.RAW;
import oracle.sql.REF;
import oracle.sql.ROWID;
import oracle.sql.STRUCT;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.VECTOR;
import oracle.sql.json.OracleJsonArray;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonDate;
import oracle.sql.json.OracleJsonDatum;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonDouble;
import oracle.sql.json.OracleJsonFloat;
import oracle.sql.json.OracleJsonIntervalDS;
import oracle.sql.json.OracleJsonIntervalYM;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonParser;
import oracle.sql.json.OracleJsonString;
import oracle.sql.json.OracleJsonStructure;
import oracle.sql.json.OracleJsonTimestamp;
import oracle.sql.json.OracleJsonValue;
import oracle.sql.json.OracleJsonVector;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/Representation.class */
public class Representation {
    public static final Representation VARCHAR;
    public static final Representation FIXED_CHAR;
    public static final Representation CHAR;
    public static final Representation VCS;
    public static final Representation LONG;
    public static final Representation NUMBER;
    public static final Representation VARNUM;
    public static final Representation BINARY_FLOAT;
    public static final Representation BINARY_DOUBLE;
    public static final Representation RAW;
    public static final Representation VBI;
    public static final Representation LONG_RAW;
    public static final Representation ROWID;
    public static final Representation RESULT_SET;
    public static final Representation RSET;
    public static final Representation DATE;
    public static final Representation BLOB;
    public static Representation JSON;
    public static final Representation CLOB;
    public static final Representation BFILE;
    public static final Representation NAMED_TYPE;
    public static final Representation REF_TYPE;
    public static final Representation TIMESTAMP;
    public static final Representation TIMESTAMPTZ;
    public static final Representation OLD_TIMESTAMPTZ;
    public static final Representation TIMESTAMPLTZ;
    public static final Representation INTERVALYM;
    public static final Representation INTERVALDS;
    public static final Representation UROWID;
    public static final Representation PLSQL_INDEX_TABLE;
    public static final Representation T2S_OVERLONG_RAW;
    public static final Representation SET_CHAR_BYTES;
    public static final Representation NULL_TYPE;
    public static final Representation DML_RETURN_PARAM;
    public static final Representation NVARCHAR;
    public static final Representation FIXED_NCHAR;
    public static final Representation NCHAR;
    public static final Representation NVCS;
    public static final Representation NCLOB;
    public static final Representation VECTOR;
    protected final List<Class<?>> tableB3Classes;
    protected final Map<Class<?>, Redirector<?>> redirectorCache = new ConcurrentHashMap(1024);
    protected final String identifier;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !Representation.class.desiredAssertionStatus();
        VARCHAR = new Representation("VARCHAR", String.class, Reader.class, CHAR.class, InputStream.class, RowId.class, ROWID.class, BigDecimal.class, Double.class, Float.class, BigInteger.class, Long.class, Integer.class, Short.class, Byte.class, LocalDate.class, LocalDateTime.class, LocalTime.class, OffsetDateTime.class, OffsetTime.class, ZonedDateTime.class, Timestamp.class, Date.class, Calendar.class, java.sql.Date.class, Time.class, Boolean.class);
        FIXED_CHAR = VARCHAR;
        CHAR = VARCHAR;
        VCS = VARCHAR;
        LONG = new Representation("LONG", String.class, Reader.class, InputStream.class);
        NUMBER = new Representation("NUMBER", BigDecimal.class, NUMBER.class, String.class, CHAR.class, Double.class, Float.class, BigInteger.class, Long.class, Integer.class, Short.class, Byte.class, Boolean.class, BOOLEAN.class);
        VARNUM = NUMBER;
        BINARY_FLOAT = new Representation("BINARY_FLOAT", Float.class, Double.class, BigDecimal.class, BINARY_FLOAT.class, NUMBER.class, String.class, BigInteger.class, Long.class, Integer.class, Short.class, Byte.class);
        BINARY_DOUBLE = new Representation("BINARY_DOUBLE", Double.class, BigDecimal.class, BINARY_DOUBLE.class, NUMBER.class, String.class, Float.class, BigInteger.class, Long.class, Integer.class, Short.class, Byte.class);
        RAW = new Representation("RAW", byte[].class, RAW.class, String.class, InputStream.class, Reader.class);
        VBI = null;
        LONG_RAW = RAW;
        ROWID = new Representation("ROWID", RowId.class, ROWID.class, String.class);
        RESULT_SET = new Representation("RESULT_SET", ResultSet.class);
        RSET = RESULT_SET;
        DATE = new Representation("DATE", Timestamp.class, Date.class, LocalDate.class, LocalDateTime.class, LocalTime.class, OffsetDateTime.class, OffsetTime.class, ZonedDateTime.class, Calendar.class, DATE.class, TIMESTAMP.class, String.class, java.sql.Date.class, Time.class);
        BLOB = new Representation("BLOB", Blob.class, BLOB.class, oracle.jdbc.OracleBlob.class, InputStream.class, byte[].class);
        List<Class<?>> classes = new ArrayList<>();
        classes.add(byte[].class);
        classes.add(String.class);
        classes.add(Reader.class);
        classes.add(InputStream.class);
        classes.add(OracleJsonValue.class);
        classes.add(OracleJsonStructure.class);
        classes.add(OracleJsonObject.class);
        classes.add(OracleJsonArray.class);
        classes.add(OracleJsonString.class);
        classes.add(OracleJsonNumber.class);
        classes.add(OracleJsonDecimal.class);
        classes.add(OracleJsonFloat.class);
        classes.add(OracleJsonDouble.class);
        classes.add(OracleJsonBinary.class);
        classes.add(OracleJsonTimestamp.class);
        classes.add(OracleJsonDate.class);
        classes.add(OracleJsonIntervalDS.class);
        classes.add(OracleJsonIntervalYM.class);
        classes.add(OracleJsonVector.class);
        classes.add(OracleJsonParser.class);
        classes.add(OracleJsonDatum.class);
        classes.add(Datum.class);
        if (PhysicalConnection.isJsonJarPresent()) {
            classes.add(JsonValue.class);
            classes.add(JsonParser.class);
            classes.add(JsonStructure.class);
            classes.add(JsonObject.class);
            classes.add(JsonArray.class);
            classes.add(JsonString.class);
            classes.add(JsonNumber.class);
        }
        if (PhysicalConnection.isJakartaJarPresent()) {
            classes.add(jakarta.json.JsonValue.class);
            classes.add(jakarta.json.stream.JsonParser.class);
            classes.add(jakarta.json.JsonStructure.class);
            classes.add(jakarta.json.JsonObject.class);
            classes.add(jakarta.json.JsonArray.class);
            classes.add(jakarta.json.JsonString.class);
            classes.add(jakarta.json.JsonNumber.class);
        }
        JSON = new Representation("JSON", (Class[]) classes.toArray(new Class[classes.size()]));
        CLOB = new Representation("CLOB", Clob.class, CLOB.class, oracle.jdbc.OracleClob.class, Reader.class, String.class, InputStream.class);
        BFILE = new Representation("BFILE", BFILE.class, oracle.jdbc.OracleBfile.class, InputStream.class, byte[].class);
        NAMED_TYPE = new Representation("NAMED_TYPE", SQLXML.class, OracleData.class, ORAData.class, OPAQUE.class, OracleOpaque.class, Struct.class, STRUCT.class, oracle.jdbc.OracleStruct.class, Array.class, ARRAY.class, oracle.jdbc.OracleArray.class);
        REF_TYPE = new Representation("REF_TYPE", Ref.class, REF.class, oracle.jdbc.OracleRef.class);
        TIMESTAMP = new Representation("TIMESTAMP", Timestamp.class, TIMESTAMP.class, LocalDate.class, LocalDateTime.class, LocalTime.class, OffsetDateTime.class, OffsetTime.class, ZonedDateTime.class, Calendar.class, Date.class, DATE.class, String.class, java.sql.Date.class, Time.class, byte[].class);
        TIMESTAMPTZ = new Representation("TIMESTAMPTZ", TIMESTAMPTZ.class, LocalDate.class, LocalDateTime.class, LocalTime.class, OffsetDateTime.class, OffsetTime.class, ZonedDateTime.class, Timestamp.class, TIMESTAMP.class, Calendar.class, Date.class, DATE.class, String.class, java.sql.Date.class, Time.class, byte[].class);
        OLD_TIMESTAMPTZ = new Representation("OLD_TIMESTAMPTZ", TIMESTAMPTZ.class, Timestamp.class, LocalDate.class, LocalDateTime.class, LocalTime.class, OffsetDateTime.class, OffsetTime.class, ZonedDateTime.class, TIMESTAMP.class, Calendar.class, Date.class, String.class, java.sql.Date.class, Time.class);
        TIMESTAMPLTZ = new Representation("TIMESTAMPLTZ", TIMESTAMPLTZ.class, LocalDate.class, LocalDateTime.class, LocalTime.class, OffsetDateTime.class, OffsetTime.class, ZonedDateTime.class, Timestamp.class, TIMESTAMP.class, Calendar.class, Date.class, DATE.class, String.class, java.sql.Date.class, Time.class, byte[].class);
        INTERVALYM = new Representation("INTERVALYM", INTERVALYM.class, Period.class, String.class);
        INTERVALDS = new Representation("INTERVALDS", INTERVALDS.class, Duration.class, String.class, BigDecimal.class);
        UROWID = null;
        PLSQL_INDEX_TABLE = null;
        T2S_OVERLONG_RAW = null;
        SET_CHAR_BYTES = null;
        NULL_TYPE = null;
        DML_RETURN_PARAM = null;
        NVARCHAR = new Representation("NVARCHAR", String.class, Reader.class, CHAR.class, InputStream.class, BigDecimal.class, Double.class, Float.class, BigInteger.class, Long.class, Integer.class, Short.class, Byte.class, Timestamp.class, Date.class, LocalDate.class, LocalDateTime.class, LocalTime.class, OffsetDateTime.class, OffsetTime.class, ZonedDateTime.class, Calendar.class, java.sql.Date.class, Time.class, Boolean.class);
        FIXED_NCHAR = NVARCHAR;
        NCHAR = NVARCHAR;
        NVCS = NVARCHAR;
        NCLOB = new Representation("NCLOB", NClob.class, NCLOB.class, oracle.jdbc.OracleNClob.class, Reader.class, String.class, InputStream.class);
        VECTOR = new Representation("VECTOR", double[].class, float[].class, byte[].class, short[].class, int[].class, long[].class, boolean[].class, String.class, Clob.class, CLOB.class, oracle.jdbc.OracleClob.class, VECTOR.class, VECTOR.SparseDoubleArray.class, VECTOR.SparseFloatArray.class, VECTOR.SparseByteArray.class);
    }

    protected Representation(String name, Class<?>... types) {
        this.identifier = name;
        if (!$assertionsDisabled && types.length <= 0) {
            throw new AssertionError(types.length);
        }
        this.tableB3Classes = Collections.unmodifiableList(Arrays.asList(types));
        if (!$assertionsDisabled && this.tableB3Classes.size() <= 0) {
            throw new AssertionError(this.tableB3Classes.size());
        }
        this.redirectorCache.putAll(Redirector.createRedirectorMap(this.tableB3Classes));
    }

    public String toString() {
        return "oracle.jdbc.driver.Representation[" + this.identifier + "]";
    }

    <T> T getObject(Accessor acc, int currentRow, Class<T> type) throws SQLException {
        if (type == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NULL_CLASS).fillInStackTrace());
        }
        Redirector<T> r = getRedirector(type);
        if (r == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4, type).fillInStackTrace());
        }
        return r.redirect(acc, currentRow);
    }

    final <T> Redirector<T> getRedirector(Class<T> type) {
        Redirector<T> r = (Redirector) this.redirectorCache.get(type);
        if (r == null) {
            r = createRedirector(type);
            this.redirectorCache.put(type, r);
        }
        return r;
    }

    private final <T> Redirector<T> createRedirector(Class<T> type) {
        if (SQLData.class.isAssignableFrom(type) || OracleData.class.isAssignableFrom(type) || ORAData.class.isAssignableFrom(type)) {
            return Redirector.createObjectRedirector(type);
        }
        return Redirector.createValueOfRedirector(type, this.tableB3Classes);
    }

    static OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
