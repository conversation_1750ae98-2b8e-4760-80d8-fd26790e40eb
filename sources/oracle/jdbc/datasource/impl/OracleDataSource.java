package oracle.jdbc.datasource.impl;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.lang.management.ManagementFactory;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.security.PrivilegedActionException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.sql.SQLWarning;
import java.util.Enumeration;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.management.InstanceAlreadyExistsException;
import javax.management.JMException;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import javax.naming.NamingException;
import javax.naming.Reference;
import javax.naming.Referenceable;
import javax.naming.StringRefAddr;
import javax.net.ssl.SSLContext;
import oracle.jdbc.AccessToken;
import oracle.jdbc.OracleHostnameResolver;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.AbstractShardingConnection;
import oracle.jdbc.driver.AbstractTrueCacheConnection;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.OracleDriver;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.pool.OraclePooledConnection;
import oracle.jdbc.proxy.ProxyFactory;
import oracle.jdbc.replay.ReplayStatistics;
import oracle.jdbc.replay.driver.NonTxnReplayableArray;
import oracle.jdbc.replay.driver.NonTxnReplayableBase;
import oracle.jdbc.replay.driver.NonTxnReplayableBfile;
import oracle.jdbc.replay.driver.NonTxnReplayableBlob;
import oracle.jdbc.replay.driver.NonTxnReplayableClob;
import oracle.jdbc.replay.driver.NonTxnReplayableConnection;
import oracle.jdbc.replay.driver.NonTxnReplayableNClob;
import oracle.jdbc.replay.driver.NonTxnReplayableOpaque;
import oracle.jdbc.replay.driver.NonTxnReplayableOthers;
import oracle.jdbc.replay.driver.NonTxnReplayableRef;
import oracle.jdbc.replay.driver.NonTxnReplayableResultSet;
import oracle.jdbc.replay.driver.NonTxnReplayableStatement;
import oracle.jdbc.replay.driver.NonTxnReplayableStruct;
import oracle.jdbc.replay.driver.ReplayStatisticsMBeanImpl;
import oracle.jdbc.replay.driver.StatisticsTracker;
import oracle.jdbc.replay.driver.TxnReplayableArray;
import oracle.jdbc.replay.driver.TxnReplayableBase;
import oracle.jdbc.replay.driver.TxnReplayableBfile;
import oracle.jdbc.replay.driver.TxnReplayableBlob;
import oracle.jdbc.replay.driver.TxnReplayableClob;
import oracle.jdbc.replay.driver.TxnReplayableConnection;
import oracle.jdbc.replay.driver.TxnReplayableNClob;
import oracle.jdbc.replay.driver.TxnReplayableOpaque;
import oracle.jdbc.replay.driver.TxnReplayableOthers;
import oracle.jdbc.replay.driver.TxnReplayableRef;
import oracle.jdbc.replay.driver.TxnReplayableResultSet;
import oracle.jdbc.replay.driver.TxnReplayableSqlxml;
import oracle.jdbc.replay.driver.TxnReplayableStatement;
import oracle.jdbc.replay.driver.TxnReplayableStruct;
import oracle.jdbc.replay.internal.ConnectionInitializationCallback;
import oracle.jdbc.replay.internal.ReplayableConnection;
import oracle.net.resolver.NavSchemaObject;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/impl/OracleDataSource.class */
public class OracleDataSource implements oracle.jdbc.datasource.OracleDataSource, Diagnosable, oracle.jdbc.replay.internal.OracleDataSource, Serializable, Referenceable, Monitor {
    static final long serialVersionUID = 3349652938965166731L;
    protected static final String DMS_ROOT_NAME = "JDBC";
    protected static final String DMS_DEFAULT_DATASOURCE_NAME = "OracleDataSource";
    protected static final String DEFAULT_SERVICE_NAME = "SYS$USERS";
    private SSLContext sslContext;
    private boolean allowSingleShardTransaction;
    private OracleHostnameResolver hostnameResolver;
    private Supplier<? extends AccessToken> tokenSupplier;
    private static ProxyFactory PROXY_FACTORY;
    private static ProxyFactory NON_TXN_PROXY_FACTORY;
    protected static final String RECONNECT_DELAY_PROPERTY = "AUTH_FAILOVER_DELAY";
    protected static final String RECONNECT_RETRIES_PROPERTY = "AUTH_FAILOVER_RETRIES";
    protected static final String FAILOVER_TYPE_PROPERTY = "AUTH_FAILOVER_TYPE";
    protected static final int FAILOVER_TYPE_TRANSACTION = 8;
    protected static final int SESSION_STATE_CONSISTENCY_STATIC = 16;
    protected static final int FAILOVER_TYPE_AUTO = 32;
    protected static final int SESSION_STATE_CONSISTENCY_HYBRID = 256;
    protected static final int FAILOVER_TYPE_ON_REMOTE = 512;
    protected static final String SESSION_STATE_PROPERTY = "AUTH_SESSION_STATE_CONSISTENCY";
    protected static final String FAILOVER_RESTORE_PROPERTY = "AUTH_FAILOVER_RESTORE";
    protected static final int FAILOVER_RESTORE_NONE = 0;
    protected static final int FAILOVER_RESTORE_LEVEL1 = 1;
    protected static final int FAILOVER_RESTORE_LEVEL2 = 2;
    protected static final int FAILOVER_RESTORE_AUTO = 3;
    protected static final String INITIATION_TIMEOUT_PROPERTY = "AUTH_FAILOVER_REPLAYTIMEOUT";
    protected static final String CHECKSUM_PROPERTY = "oracle.jdbc.calculateChecksum";
    protected static final String IGNORE_AC_CONTEXT_PROPERTY = "oracle.jdbc.ignoreReplayContextFromAuthentication";
    private static final String AC_11203_COMPATIBLE_SYSTEM_PROPERTY = "oracle.jdbc.AC11203Compatible";
    private static final String IMPLICIT_BEGIN_REQUEST_SYSTEM_PROPERTY = "oracle.jdbc.beginRequestAtConnectionCreation";
    protected static final String ENABLE_AC_SUPPORT_PROPERTY = "oracle.jdbc.enableACSupport";
    protected static final String REQUEST_SIZE_LIMIT_PROPERTY = "oracle.jdbc.replay.protectedRequestSizeLimit";
    protected static final String ENABLE_SSS_CURSOR_SUPPORT_PROPERTY = "oracle.jdbc.enableSSSCursor";
    private static final String registeredName = "com.oracle.jdbc:type=ReplayStatistics,name=";
    private static ObjectName mbeanName;
    private static final String _Copyright_2014_Oracle_All_Rights_Reserved_;
    public static final boolean TRACE = false;
    protected static int unnamedInstanceCount = 0;
    private static final String CLASS_NAME = OracleDataSource.class.getName();
    private static final Monitor proxyFactoryLock = Monitor.newInstance();
    protected PrintWriter logWriter = null;
    protected int loginTimeout = 0;
    protected String databaseName = null;
    protected String serviceName = null;
    protected String dataSourceName = DMS_DEFAULT_DATASOURCE_NAME;
    protected String description = null;
    protected String networkProtocol = "tcp";
    protected int portNumber = 0;
    protected String user = null;
    protected OpaqueString password = null;
    protected String serverName = null;
    protected String url = null;
    protected String driverType = null;
    protected String tnsEntry = null;
    protected int maxStatements = 0;
    protected boolean implicitCachingEnabled = false;
    protected boolean explicitCachingEnabled = false;
    protected boolean maxStatementsSet = false;
    protected boolean implicitCachingEnabledSet = false;
    protected boolean explicitCachingEnabledSet = false;
    protected Properties connectionProperties = null;
    public boolean isOracleDataSource = true;
    private String roleName = null;
    private boolean urlExplicit = false;
    private boolean useDefaultConnection = false;
    protected transient OracleDriver driver = new OracleDriver();
    private final Monitor.CloseableLock monitorLock = Monitor.newDefaultLock();
    private transient ConnectionInitializationCallback connectionInitializationCallback = null;
    protected AtomicBoolean isFirstConnection = new AtomicBoolean(true);
    protected int reconnectDelay = 10;
    protected int reconnectRetries = 30;
    protected boolean isTransactionReplayEnabled = false;
    protected boolean isAutoACEnabled = false;
    protected boolean isReplayInDynamicMode = true;
    protected boolean isHybrid = false;
    protected ReplayableConnection.StateRestorationType stateRestorationType = ReplayableConnection.StateRestorationType.NONE;
    protected boolean isStateRestorationAuto = false;
    protected int replayInitiationTimeout = 300;
    protected final String clientChecksum12x = OracleConnection.ChecksumMode.CALCULATE_CHECKSUM_BINDS.toString();
    protected final String clientChecksum11203x = OracleConnection.ChecksumMode.CALCULATE_CHECKSUM_ALL.toString();
    protected boolean isSSSCursorEnabled = false;
    protected final AtomicBoolean trackerInitialized = new AtomicBoolean(false);
    private StatisticsTracker tracker = null;
    protected AtomicBoolean doneDumpOnMemoryPressure = new AtomicBoolean(false);

    static {
        PROXY_FACTORY = null;
        NON_TXN_PROXY_FACTORY = null;
        Monitor.CloseableLock lock = proxyFactoryLock.acquireCloseableLock();
        Throwable th = null;
        try {
            if (PROXY_FACTORY == null) {
                NON_TXN_PROXY_FACTORY = ProxyFactory.createProxyFactory(NonTxnReplayableBase.class, NonTxnReplayableConnection.class, NonTxnReplayableStatement.class, NonTxnReplayableResultSet.class, NonTxnReplayableArray.class, NonTxnReplayableBfile.class, NonTxnReplayableBlob.class, NonTxnReplayableClob.class, NonTxnReplayableNClob.class, NonTxnReplayableOpaque.class, NonTxnReplayableRef.class, NonTxnReplayableStruct.class, NonTxnReplayableOthers.class);
                PROXY_FACTORY = ProxyFactory.createProxyFactory(TxnReplayableBase.class, TxnReplayableConnection.class, TxnReplayableStatement.class, TxnReplayableResultSet.class, TxnReplayableArray.class, TxnReplayableBfile.class, TxnReplayableBlob.class, TxnReplayableClob.class, TxnReplayableNClob.class, TxnReplayableOpaque.class, TxnReplayableRef.class, TxnReplayableSqlxml.class, TxnReplayableStruct.class, TxnReplayableOthers.class);
            }
            mbeanName = null;
            AccessController.doPrivileged(new PrivilegedAction<Object>() { // from class: oracle.jdbc.datasource.impl.OracleDataSource.3
                @Override // java.security.PrivilegedAction
                public Object run() {
                    OracleDataSource.registerMBean();
                    return null;
                }
            });
            _Copyright_2014_Oracle_All_Rights_Reserved_ = null;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    protected static String dms_data_source_type() {
        return "JDBC_DataSource";
    }

    @Override // javax.sql.DataSource
    public Connection getConnection() throws SQLException {
        OpaqueString opaqueString = OpaqueString.NULL;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                String localUser = this.user;
                OpaqueString localPassword = this.password;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return getConnection(localUser, localPassword);
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // javax.sql.DataSource
    public Connection getConnection(String user, @Blind String password) throws SQLException {
        return getConnection(user, OpaqueString.newOpaqueString(password));
    }

    protected Connection getConnection(String _user, OpaqueString _passwd) throws SQLException {
        OracleConnectionBuilderImpl connectionBuilder = ((OracleConnectionBuilderImpl) createConnectionBuilder().user(_user)).password(_passwd);
        boolean useProxy = isACSupportPropertySet();
        return getConnectionInternal(connectionBuilder, useProxy);
    }

    protected Connection getConnection(OracleConnectionBuilderImpl builder) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            validateGSSCredentialConfiguration(builder);
            makeURL();
            Properties prop = this.connectionProperties == null ? new Properties() : (Properties) this.connectionProperties.clone();
            applyDataSourcePropertiesForGetConnectionWithBuilder(prop);
            applyBuilderProperties(builder, prop);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            Connection conn = getPhysicalConnection(prop, builder);
            if (conn == null) {
                throw ((SQLException) DatabaseError.createSqlException(67).fillInStackTrace());
            }
            return conn;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final CompletionStage<oracle.jdbc.OracleConnection> getConnectionAsync(OracleConnectionBuilderImpl builder) {
        try {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                validateGSSCredentialConfiguration(builder);
                makeURL();
                Properties prop = this.connectionProperties == null ? new Properties() : (Properties) this.connectionProperties.clone();
                applyDataSourcePropertiesForGetConnectionWithBuilder(prop);
                applyBuilderProperties(builder, prop);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                try {
                    return getPhysicalConnectionAsync(prop, builder).thenApply(conn -> {
                        if (conn == null) {
                            throw new CompletionException(DatabaseError.createSqlException(67).fillInStackTrace());
                        }
                        return conn;
                    });
                } catch (Exception exception) {
                    return CompletionStageUtil.failedStage(exception);
                }
            } finally {
            }
        } catch (SQLException preConnectFailure) {
            return CompletionStageUtil.failedStage(preConnectFailure);
        }
    }

    private static final void validateGSSCredentialConfiguration(OracleConnectionBuilderImpl builder) throws SQLException {
        if (builder.getGSSCredential() != null) {
            if (builder.getUser() != null || (builder.getPassword() != null && builder.getPassword() != OpaqueString.NULL)) {
                throw ((SQLException) DatabaseError.createSqlException(68, "GSSCredential and user/password cannot both be set in a connection builder.").fillInStackTrace());
            }
        }
    }

    private final void applyDataSourcePropertiesForGetConnectionWithBuilder(@Blind(PropertiesBlinder.class) Properties prop) {
        if (this.url != null) {
            prop.setProperty(OraclePooledConnection.url_string, this.url);
        }
        if (this.loginTimeout != 0) {
            prop.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_LOGIN_TIMEOUT, String.valueOf(this.loginTimeout));
        }
        if (this.maxStatementsSet) {
            prop.setProperty(OraclePooledConnection.statement_cache_size, String.valueOf(this.maxStatements));
        }
    }

    private static final void applyBuilderProperties(OracleConnectionBuilderImpl builder, @Blind(PropertiesBlinder.class) Properties connectionProperties) throws SQLException {
        if (builder.getUser() != null) {
            connectionProperties.setProperty("user", builder.getUser());
        }
        if (!OpaqueString.isNull(builder.getPassword())) {
            connectionProperties.setProperty("password", builder.getPassword().get());
        }
        if (builder.getInstanceName() != null) {
            connectionProperties.setProperty("oracle.jdbc.targetInstanceName", builder.getInstanceName());
        }
        if (builder.getServiceName() != null && !builder.getServiceName().equalsIgnoreCase(DEFAULT_SERVICE_NAME)) {
            connectionProperties.setProperty("oracle.jdbc.targetServiceName", builder.getServiceName());
        }
        if (builder.getShardingKey() != null) {
            String b64EncodedShardingKey = builder.getShardingKey().encodeKeyinB64Format();
            connectionProperties.setProperty("oracle.jdbc.targetShardingKey", b64EncodedShardingKey);
        }
        if (builder.getSuperShardingKey() != null) {
            String b64EncodedSuperKey = builder.getSuperShardingKey().encodeKeyinB64Format();
            connectionProperties.setProperty("oracle.jdbc.targetSuperShardingKey", b64EncodedSuperKey);
        }
        if (builder.getReadOnlyInstanceAllowed()) {
            connectionProperties.setProperty("oracle.jdbc.readOnlyInstanceAllowed", String.valueOf(builder.getReadOnlyInstanceAllowed()));
        }
    }

    private void applyDataSourcePropertiesToBuilder(AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        if (builder == null) {
            return;
        }
        if (this.sslContext != null && builder.getSSLContext() == null) {
            builder.sslContext(this.sslContext);
        }
        boolean allowSingleShardTxn = this.allowSingleShardTransaction || (this.connectionProperties != null && "true".equalsIgnoreCase(this.connectionProperties.getProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_ALLOW_SINGLE_SHARD_TRANSACTION_SUPPORT, "false")));
        if (allowSingleShardTxn && !builder.getAllowSingleShardTransaction()) {
            builder.singleShardTransactionSupport(allowSingleShardTxn);
        }
        if (this.hostnameResolver != null && builder.getHostnameResolver() == null) {
            builder.hostnameResolver(this.hostnameResolver);
        }
        configureTokenSupplier(builder);
    }

    private void configureTokenSupplier(AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        Supplier<? extends AccessToken> tokenSupplier = this.tokenSupplier;
        if (tokenSupplier == null) {
            return;
        }
        if (isUserOrPasswordConfigured()) {
            throw ((SQLException) DatabaseError.createSqlException((OracleConnection) null, DatabaseError.EOJ_TOKEN_CONFIGURATION_FAILURE, "DataSource configured with setTokenSupplier(Supplier) is also configured with a user name or password").fillInStackTrace());
        }
        if (builder != null && builder.getTokenSupplier() == null && builder.getUser() == null) {
            if (builder.getPassword() != null && !builder.getPassword().isNull()) {
                return;
            }
            builder.setTokenSupplier(tokenSupplier);
        }
    }

    private boolean isUserOrPasswordConfigured() {
        boolean z;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.user == null && (this.password == null || this.password.isNull())) {
                if (this.connectionProperties != null) {
                    if (!this.connectionProperties.containsKey("user") && !this.connectionProperties.containsKey("oracle.jdbc.user") && !this.connectionProperties.containsKey("password")) {
                        if (this.connectionProperties.containsKey("oracle.jdbc.password")) {
                        }
                    }
                    z = true;
                }
                z = false;
            } else {
                z = true;
            }
            return z;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    protected Connection getPhysicalConnection(@Blind(PropertiesBlinder.class) Properties prop, AbstractConnectionBuilder builder) throws SQLException, PrivilegedActionException {
        String string;
        Connection conn;
        String localUrl = prop.getProperty(OraclePooledConnection.url_string, this.url);
        String localUser = prop.getProperty("user");
        boolean hasGSSCredential = (builder == null || builder.getGSSCredential() == null) ? false : true;
        Properties localProps = createPropertiesForPhysicalConnection(prop, hasGSSCredential);
        debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "getPhysicalConnection", "(point 1) url={0}, user={1}. ", (String) null, (Throwable) null, localUrl, localUser);
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean localUseDefaultConnection = this.useDefaultConnection;
            if (this.driver == null) {
                this.driver = new OracleDriver();
            }
            if (this.dataSourceName == null || this.dataSourceName.length() == 0) {
                StringBuilder sbAppend = new StringBuilder().append("OracleDataSource_");
                int i = unnamedInstanceCount;
                unnamedInstanceCount = i + 1;
                string = sbAppend.append(i).toString();
            } else {
                string = this.dataSourceName;
            }
            String dmsName = string;
            localProps.setProperty("DMSName", dmsName);
            localProps.setProperty("DMSType", dms_data_source_type());
            applyDataSourcePropertiesToBuilder(builder);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            if (localUseDefaultConnection) {
                conn = this.driver.defaultConnection();
            } else {
                conn = this.driver.connect(localUrl, localProps, builder);
            }
            if (conn == null) {
                throw ((SQLException) DatabaseError.createSqlException(67).fillInStackTrace());
            }
            initializeStatementCacheForPhysicalConnection((OracleConnection) conn, prop);
            return conn;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private final CompletionStage<oracle.jdbc.OracleConnection> getPhysicalConnectionAsync(@Blind(PropertiesBlinder.class) Properties prop, AbstractConnectionBuilder builder) {
        String string;
        String localUrl = prop.getProperty(OraclePooledConnection.url_string, this.url);
        String localUser = prop.getProperty("user");
        boolean hasGSSCredential = (builder == null || builder.getGSSCredential() == null) ? false : true;
        Properties localProps = createPropertiesForPhysicalConnection(prop, hasGSSCredential);
        debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "getPhysicalConnection", "(point 2) url={0}, user={1}. ", (String) null, (Throwable) null, localUrl, localUser);
        if (this.useDefaultConnection) {
            return CompletionStageUtil.failedStage(new UnsupportedOperationException("Asynchronous connections are not supported by the server-side internal driver"));
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.driver == null) {
                    this.driver = new OracleDriver();
                }
                if (this.dataSourceName == null || this.dataSourceName.length() == 0) {
                    StringBuilder sbAppend = new StringBuilder().append("OracleDataSource_");
                    int i = unnamedInstanceCount;
                    unnamedInstanceCount = i + 1;
                    string = sbAppend.append(i).toString();
                } else {
                    string = this.dataSourceName;
                }
                String dmsName = string;
                localProps.setProperty("DMSName", dmsName);
                localProps.setProperty("DMSType", dms_data_source_type());
                localProps.setProperty("DMSName", dmsName);
                localProps.setProperty("DMSType", dms_data_source_type());
                try {
                    applyDataSourcePropertiesToBuilder(builder);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return this.driver._INTERNAL_ORACLE_connectAsync(localUrl, localProps, builder).thenApply(CompletionStageUtil.normalCompletionHandler(conn -> {
                        if (conn == null) {
                            throw ((SQLException) DatabaseError.createSqlException(67).fillInStackTrace());
                        }
                        OracleConnection oraConn = (OracleConnection) conn;
                        initializeStatementCacheForPhysicalConnection(oraConn, prop);
                        return oraConn;
                    }));
                } catch (SQLException sqlException) {
                    CompletionStage<oracle.jdbc.OracleConnection> completionStageFailedStage = CompletionStageUtil.failedStage(sqlException);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th3) {
                                th.addSuppressed(th3);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return completionStageFailedStage;
                }
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Blind(PropertiesBlinder.class)
    private final Properties createPropertiesForPhysicalConnection(@Blind(PropertiesBlinder.class) Properties internalProperties, boolean hasGSSCredential) {
        String localUser = internalProperties.getProperty("user");
        String localPassword = internalProperties.getProperty("password");
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (!hasGSSCredential) {
            if (localUser == null) {
                try {
                    try {
                        if (this.user != null) {
                            String str = this.user;
                            localUser = str;
                            internalProperties.put("user", str);
                        }
                    } finally {
                    }
                } catch (Throwable th2) {
                    if (lock != null) {
                        if (th != null) {
                            try {
                                lock.close();
                            } catch (Throwable th3) {
                                th.addSuppressed(th3);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    throw th2;
                }
            }
            if (localPassword == null && this.password != null && this.password != OpaqueString.NULL) {
                String str2 = this.password.get();
                localPassword = str2;
                internalProperties.put("password", str2);
            }
        }
        if (this.connectionProperties != null) {
            Properties physicalConnectionProperties = (Properties) this.connectionProperties.clone();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            if (localUser != null) {
                physicalConnectionProperties.setProperty("user", localUser);
            }
            if (localPassword != null) {
                physicalConnectionProperties.put("password", localPassword);
            }
            String localInstanceName = internalProperties.getProperty("oracle.jdbc.targetInstanceName");
            if (localInstanceName != null) {
                physicalConnectionProperties.put("oracle.jdbc.targetInstanceName", localInstanceName);
            }
            String localServiceName = internalProperties.getProperty("oracle.jdbc.targetServiceName");
            if (localServiceName != null) {
                physicalConnectionProperties.put("oracle.jdbc.targetServiceName", localServiceName);
            }
            String shardingKeyStr = internalProperties.getProperty("oracle.jdbc.targetShardingKey");
            if (shardingKeyStr != null) {
                physicalConnectionProperties.put("oracle.jdbc.targetShardingKey", shardingKeyStr);
            }
            String superShardingKeyStr = internalProperties.getProperty("oracle.jdbc.targetSuperShardingKey");
            if (superShardingKeyStr != null) {
                physicalConnectionProperties.put("oracle.jdbc.targetSuperShardingKey", superShardingKeyStr);
            }
            String isReadOnlyInstanceAllowed = internalProperties.getProperty("oracle.jdbc.readOnlyInstanceAllowed");
            if ("true".equalsIgnoreCase(isReadOnlyInstanceAllowed)) {
                physicalConnectionProperties.put("oracle.jdbc.readOnlyInstanceAllowed", isReadOnlyInstanceAllowed);
            }
            String localLoginTimeout = internalProperties.getProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_LOGIN_TIMEOUT);
            if (localLoginTimeout == null) {
                localLoginTimeout = physicalConnectionProperties.getProperty(OraclePooledConnection.LoginTimeout);
            }
            if (localLoginTimeout != null) {
                physicalConnectionProperties.setProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_LOGIN_TIMEOUT, localLoginTimeout);
            }
            return physicalConnectionProperties;
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                }
            } else {
                lock.close();
            }
        }
        return internalProperties;
    }

    private final void initializeStatementCacheForPhysicalConnection(OracleConnection physicalConnection, Properties internalProperties) throws SQLException {
        String cacheSizeProperty = internalProperties.getProperty(OraclePooledConnection.statement_cache_size);
        int cacheSizePropertyValue = cacheSizeProperty == null ? 0 : Integer.parseInt(cacheSizeProperty);
        if (cacheSizeProperty != null) {
            physicalConnection.setStatementCacheSize(cacheSizePropertyValue);
        }
        String enableExplicitProperty = internalProperties.getProperty(OraclePooledConnection.ExplicitStatementCachingEnabled);
        boolean enableExplicitPropertyValue = "true".equals(enableExplicitProperty);
        if (enableExplicitProperty != null) {
            physicalConnection.setExplicitCachingEnabled(enableExplicitPropertyValue);
        } else if (this.explicitCachingEnabled) {
            physicalConnection.setExplicitCachingEnabled(true);
        }
        String enableImplicitProperty = internalProperties.getProperty(OraclePooledConnection.ImplicitStatementCachingEnabled);
        boolean enableImplicitPropertyValue = "true".equals(enableImplicitProperty);
        if (enableImplicitProperty != null) {
            physicalConnection.setImplicitCachingEnabled(enableImplicitPropertyValue);
        } else if (this.implicitCachingEnabled) {
            physicalConnection.setImplicitCachingEnabled(true);
        }
        if (cacheSizePropertyValue > 0 && !enableExplicitPropertyValue && !enableImplicitPropertyValue) {
            physicalConnection.setImplicitCachingEnabled(true);
            physicalConnection.setExplicitCachingEnabled(true);
        }
    }

    @Override // javax.sql.CommonDataSource
    public int getLoginTimeout() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int i = this.loginTimeout;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return i;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // javax.sql.CommonDataSource
    public void setLoginTimeout(int timeout) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.loginTimeout = timeout;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // javax.sql.CommonDataSource
    public void setLogWriter(PrintWriter pw) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.logWriter = pw;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // javax.sql.CommonDataSource
    public PrintWriter getLogWriter() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            PrintWriter printWriter = this.logWriter;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return printWriter;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public void setTNSEntryName(String dbname) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.tnsEntry = dbname;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public String getTNSEntryName() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.tnsEntry;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setDataSourceName(String dsname) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.dataSourceName = dsname;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public String getDataSourceName() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.dataSourceName;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public String getDatabaseName() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.databaseName;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setDatabaseName(String dbname) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.databaseName = dbname;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public void setServiceName(String svcname) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.serviceName = svcname;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public String getServiceName() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.serviceName;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setServerName(String sn) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.serverName = sn;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public String getServerName() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.serverName;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setURL(String url) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.url = url;
            if (this.url != null) {
                this.urlExplicit = true;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public String getURL() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (!this.urlExplicit) {
                makeURL();
            }
            String str = this.url;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setUser(String userName) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.user = userName;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public String getUser() {
        return this.user;
    }

    private void setPassword(OpaqueString os) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.password = os;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setPassword(@Blind String pd) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                setPassword(OpaqueString.newOpaqueString(pd));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    protected OpaqueString getPassword() {
        return this.password;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public String getDescription() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.description;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setDescription(String des) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.description = des;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public String getDriverType() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.driverType;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public void setDriverType(String dt) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.driverType = dt;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public String getNetworkProtocol() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.networkProtocol;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setNetworkProtocol(String np) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.networkProtocol = np;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setPortNumber(int pn) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.portNumber = pn;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public int getPortNumber() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int i = this.portNumber;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return i;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public Reference getReference() throws NamingException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Reference ref = new Reference(getClass().getName(), "oracle.jdbc.datasource.impl.OracleDataSourceFactory", (String) null);
            addRefProperties(ref);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return ref;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    protected void addRefProperties(Reference ref) {
        if (this.url != null) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.URL, this.url));
        }
        if (this.user != null) {
            ref.add(new StringRefAddr("userName", this.user));
        }
        if (this.password != null && this.password != OpaqueString.NULL) {
            ref.add(new StringRefAddr("passWord", this.password.get()));
        }
        if (this.description != null) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.DESCRIPTION, this.description));
        }
        if (this.driverType != null) {
            ref.add(new StringRefAddr("driverType", this.driverType));
        }
        if (this.serverName != null) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.SERVER_NAME, this.serverName));
        }
        if (this.databaseName != null) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.DATABASE_NAME, this.databaseName));
        }
        if (this.serviceName != null) {
            ref.add(new StringRefAddr("serviceName", this.serviceName));
        }
        if (this.networkProtocol != null) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.NETWORK_PROTOCOL, this.networkProtocol));
        }
        if (this.portNumber != 0) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.PORT_NUMBER, Integer.toString(this.portNumber)));
        }
        if (this.tnsEntry != null) {
            ref.add(new StringRefAddr("tnsentryname", this.tnsEntry));
        }
        if (this.connectionProperties != null && this.connectionProperties.size() > 0) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.CONNECTION_PROPERTIES, this.connectionProperties.toString()));
        }
        if (this.maxStatementsSet) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.MAX_STATEMENTS, Integer.toString(this.maxStatements)));
        }
        if (this.implicitCachingEnabledSet) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.IMPLICIT_CACHING_ENABLED, this.implicitCachingEnabled ? "true" : "false"));
        }
        if (this.explicitCachingEnabledSet) {
            ref.add(new StringRefAddr(oracle.jdbc.replay.OracleDataSource.EXPLICIT_CACHING_ENABLED, this.explicitCachingEnabled ? "true" : "false"));
        }
    }

    protected void makeURL() throws SQLException {
        if (this.urlExplicit) {
            return;
        }
        if (this.driverType == null || (!this.driverType.equals("oci8") && !this.driverType.equals("oci") && !this.driverType.equals("thin") && !this.driverType.equals("kprb"))) {
            throw ((SQLException) DatabaseError.createSqlException(67, "OracleDataSource.makeURL").fillInStackTrace());
        }
        if (this.driverType.equals("kprb")) {
            this.useDefaultConnection = true;
            this.url = "jdbc:oracle:kprb:@";
            debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "makeURL", "useDefaultConnection={0}, url={1}. ", (String) null, (Throwable) null, Boolean.valueOf(this.useDefaultConnection), this.url);
            return;
        }
        if ((this.driverType.equals("oci8") || this.driverType.equals("oci")) && this.networkProtocol != null && this.networkProtocol.equals("ipc")) {
            this.url = "jdbc:oracle:oci:@";
            debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "makeURL", "url={0}. ", (String) null, (String) null, (Object) this.url);
        } else {
            if (this.tnsEntry != null) {
                this.url = "jdbc:oracle:" + this.driverType + ":@" + this.tnsEntry;
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "makeURL", "url={0}. ", (String) null, (String) null, (Object) this.url);
                return;
            }
            if (this.serviceName != null) {
                this.url = "jdbc:oracle:" + this.driverType + ":@(DESCRIPTION=(ADDRESS=(PROTOCOL=" + this.networkProtocol + ")(PORT=" + this.portNumber + NavSchemaObject.CID2v2 + this.serverName + "))(CONNECT_DATA=(SERVICE_NAME=" + this.serviceName + ")))";
            } else {
                this.url = "jdbc:oracle:" + this.driverType + ":@(DESCRIPTION=(ADDRESS=(PROTOCOL=" + this.networkProtocol + ")(PORT=" + this.portNumber + NavSchemaObject.CID2v2 + this.serverName + "))(CONNECT_DATA=(SID=" + this.databaseName + ")))";
                DatabaseError.addSqlWarning((SQLWarning) null, new SQLWarning("URL with SID jdbc:subprotocol:@host:port:sid will be deprecated in 10i\nPlease use URL with SERVICE_NAME as jdbc:subprotocol:@//host:port/service_name"));
            }
            debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "makeURL", "url={0}. ", (String) null, (String) null, (Object) this.url);
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource, oracle.jdbc.replay.OracleDataSource
    public void setMaxStatements(int max) throws SQLException {
        if (max < 0) {
            throw ((SQLException) DatabaseError.createSqlException(68).fillInStackTrace());
        }
        this.maxStatementsSet = true;
        this.maxStatements = max;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource, oracle.jdbc.replay.OracleDataSource
    public int getMaxStatements() throws SQLException {
        return this.maxStatements;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource, oracle.jdbc.replay.OracleDataSource
    public void setImplicitCachingEnabled(boolean cache) throws SQLException {
        this.implicitCachingEnabledSet = true;
        this.implicitCachingEnabled = cache;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource, oracle.jdbc.replay.OracleDataSource
    public boolean getImplicitCachingEnabled() throws SQLException {
        return this.implicitCachingEnabled;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource, oracle.jdbc.replay.OracleDataSource
    public void setExplicitCachingEnabled(boolean cache) throws SQLException {
        this.explicitCachingEnabledSet = true;
        this.explicitCachingEnabled = cache;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource, oracle.jdbc.replay.OracleDataSource
    public boolean getExplicitCachingEnabled() throws SQLException {
        return this.explicitCachingEnabled;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setConnectionProperties(@Blind(PropertiesBlinder.class) Properties properties) throws SQLException {
        if (properties == null || properties.size() == 0) {
            return;
        }
        if (this.connectionProperties == null || this.connectionProperties.size() == 0) {
            this.connectionProperties = (Properties) properties.clone();
            return;
        }
        Enumeration keys = properties.propertyNames();
        while (keys.hasMoreElements()) {
            Object key = keys.nextElement();
            this.connectionProperties.put(key, properties.get(key));
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource, oracle.jdbc.replay.OracleDataSource
    public void setRoleName(String roleName) throws SQLException {
        this.roleName = roleName;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource, oracle.jdbc.replay.OracleDataSource
    public String getRoleName() {
        return this.roleName;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    @Blind(PropertiesBlinder.class)
    public Properties getConnectionProperties() throws SQLException {
        return filterConnectionProperties(this.connectionProperties);
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public String getConnectionProperty(String propertyName) throws SQLException {
        if (isSensitiveProperty(propertyName) || this.connectionProperties == null) {
            return null;
        }
        return this.connectionProperties.getProperty(propertyName);
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setConnectionProperty(String name, String value) throws SQLException {
        if (value == null || value.equals("")) {
            throw new IllegalArgumentException("Connection property value can not be null or empty");
        }
        if (this.connectionProperties == null) {
            this.connectionProperties = new Properties();
        }
        this.connectionProperties.setProperty(name, value);
    }

    @Blind(PropertiesBlinder.class)
    public static final Properties filterConnectionProperties(@Blind(PropertiesBlinder.class) Properties prop) {
        Properties result = null;
        if (prop != null) {
            result = (Properties) prop.clone();
            Enumeration enu = result.propertyNames();
            while (enu.hasMoreElements()) {
                String key = (String) enu.nextElement();
                if (isSensitiveProperty(key)) {
                    result.remove(key);
                }
            }
        }
        return result;
    }

    private static boolean isSensitiveProperty(String name) {
        if (name == null) {
            return false;
        }
        switch (name) {
            case "oracle.jdbc.passwordAuthentication":
                return false;
            case "oracle.jdbc.accessToken":
                return true;
            case "oracle.jdbc.clientSecret":
                return true;
            default:
                return name.matches(".*[Pp][Aa][Ss][Ss][Ww][Oo][Rr][Dd].*");
        }
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.defaultWriteObject();
    }

    private void readObject(ObjectInputStream in) throws SQLException, ClassNotFoundException, IOException {
        in.defaultReadObject();
    }

    @Override // java.sql.Wrapper
    public boolean isWrapperFor(Class<?> iface) throws SQLException {
        if (iface.isInterface()) {
            return iface.isInstance(this);
        }
        throw ((SQLException) DatabaseError.createSqlException(177).fillInStackTrace());
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // java.sql.Wrapper
    public <T> T unwrap(Class<T> iface) throws SQLException {
        if (iface.isInterface() && iface.isInstance(this)) {
            return this;
        }
        throw ((SQLException) DatabaseError.createSqlException(177).fillInStackTrace());
    }

    @Override // javax.sql.CommonDataSource
    public Logger getParentLogger() throws SQLFeatureNotSupportedException {
        return Logger.getLogger(OracleDriver.oracle_string);
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public final void setSSLContext(SSLContext sslContext) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.sslContext = sslContext;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setSingleShardTransactionSupport(boolean allow) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.allowSingleShardTransaction = allow;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public void setHostnameResolver(OracleHostnameResolver hostnameResolver) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.hostnameResolver = hostnameResolver;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }

    @Override // oracle.jdbc.datasource.OracleDataSource
    public OracleConnectionBuilderImpl createConnectionBuilder() {
        return new OracleConnectionBuilderImpl() { // from class: oracle.jdbc.datasource.impl.OracleDataSource.1
            @Override // oracle.jdbc.OracleConnectionBuilder
            public CompletionStage<oracle.jdbc.OracleConnection> buildAsyncOracle() throws SQLException {
                ensureMutableState();
                verifyBuildConfiguration();
                CompletableFuture<oracle.jdbc.OracleConnection> connectionFuture = new CompletableFuture<>();
                OracleDataSource.this.getConnectionAsync(this).whenComplete((connection, error) -> {
                    if (error == null) {
                        connectionFuture.complete(connection);
                    } else {
                        Throwable errorCause = CompletionStageUtil.unwrapCompletionException(error);
                        connectionFuture.completeExceptionally(errorCause);
                    }
                });
                return connectionFuture;
            }

            @Override // oracle.jdbc.internal.AbstractConnectionBuilder, oracle.jdbc.OraclePooledConnectionBuilder
            public OracleConnection build() throws SQLException {
                OracleDataSource.this.debug(Level.CONFIG, SecurityLabel.UNKNOWN, OracleDataSource.CLASS_NAME + ".OracleConnectionBuilder", "build", "url={0}. ", (String) null, (String) null, (Object) OracleDataSource.this.url);
                ensureMutableState();
                verifyBuildConfiguration();
                boolean useProxy = OracleDataSource.this.isACSupportPropertySet();
                return (OracleConnection) OracleDataSource.this.getConnectionInternal(this, useProxy);
            }
        };
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final boolean isACSupportPropertySet() throws SQLException {
        String tempval = getSystemProperty("oracle.jdbc.enableACSupport", null);
        if (tempval == null) {
            tempval = getConnectionProperty("oracle.jdbc.enableACSupport");
        }
        if (tempval == null) {
            tempval = "true";
        }
        return tempval != null && tempval.equalsIgnoreCase("true");
    }

    @Override // oracle.jdbc.replay.internal.OracleDataSource
    public Connection getConnectionNoProxy(OracleConnectionBuilderImpl connBuilder) throws SQLException, InterruptedException {
        int retries = 1;
        Connection conn = null;
        Exception exc = null;
        do {
            try {
                try {
                    debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnectionNoProxy", "Reconnecting. RETRY={0}. ", (String) null, (String) null, (Object) Integer.valueOf(retries));
                    exc = null;
                    connBuilder.instanceName(null);
                    conn = getConnectionInternal(connBuilder, false);
                    OracleConnection oconn = (OracleConnection) conn;
                    if (oconn.isDRCPEnabled()) {
                        oconn.attachServerConnection();
                        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnectionNoProxy", "DRCP attach completed. ", (String) null, (Throwable) null);
                    }
                } catch (Exception e) {
                    conn = null;
                    exc = e;
                    debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnectionNoProxy", "Reconnect FAILED. ", (String) null, e);
                    if (0 != 0 && exc == null) {
                        return null;
                    }
                    retries++;
                }
                if (conn != null && 0 == 0) {
                    return conn;
                }
                retries++;
                try {
                    if (this.reconnectDelay > 0) {
                        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnectionNoProxy", "Reconnecting. delay={0}sec. ", (String) null, (String) null, (Object) Integer.valueOf(this.reconnectDelay));
                        Thread.sleep(this.reconnectDelay * 1000);
                    }
                } catch (InterruptedException sleepExc) {
                    debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnectionNoProxy", null, (String) null, sleepExc);
                }
            } catch (Throwable th) {
                if (conn != null && exc == null) {
                    return conn;
                }
                int i = retries + 1;
                throw th;
            }
        } while (retries <= this.reconnectRetries);
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public Connection getConnectionInternal(OracleConnectionBuilderImpl builder, boolean useProxy) throws SQLException, NumberFormatException {
        if (this.isFirstConnection.get()) {
            setupACSpecificProperties(useProxy);
        }
        Connection conn = getConnection(builder);
        if (useProxy) {
            conn = enableACAndProxifyIfNecessary(conn, builder);
        }
        if (conn instanceof AbstractTrueCacheConnection) {
            ((AbstractTrueCacheConnection) conn).makePrimaryConnectionUnavailable();
        }
        this.isFirstConnection.set(false);
        return conn;
    }

    protected void setupACSpecificProperties(boolean useProxy) throws SQLException {
        if (useProxy) {
            setConnectionProperty("oracle.jdbc.calculateChecksum", this.clientChecksum12x);
        } else {
            setConnectionProperty("oracle.jdbc.ignoreReplayContextFromAuthentication", "true");
        }
    }

    protected Connection enableACAndProxifyIfNecessary(Connection conn, OracleConnectionBuilderImpl connBuilder) throws SQLException, NumberFormatException {
        int wiredValue;
        if (conn instanceof AbstractTrueCacheConnection) {
            return conn;
        }
        if (conn instanceof AbstractShardingConnection) {
            return conn;
        }
        boolean useProxy = true;
        OracleConnection oconn = (OracleConnection) conn;
        Properties sessionProperties = oconn.getServerSessionInfo();
        String failoverDelay = sessionProperties.getProperty(RECONNECT_DELAY_PROPERTY);
        if (failoverDelay != null && !"".equals(failoverDelay)) {
            int _delay = Integer.parseInt(failoverDelay);
            if (_delay > 0) {
                this.reconnectDelay = _delay;
            } else {
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "enableACAndProxifyIfNecessary", "Server FAILOVER_DELAY={0}, using driver default. ", (String) null, (String) null, (Object) Integer.valueOf(_delay));
            }
        }
        String failoverRetries = sessionProperties.getProperty(RECONNECT_RETRIES_PROPERTY);
        if (failoverRetries != null && !"".equals(failoverRetries)) {
            int _retries = Integer.parseInt(failoverRetries);
            if (_retries > 0) {
                this.reconnectRetries = _retries;
            } else {
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "enableACAndProxifyIfNecessary", "Server FAILOVER_RETRIES={0}, using driver default. ", (String) null, (String) null, (Object) Integer.valueOf(_retries));
            }
        }
        String failoverType = sessionProperties.getProperty(FAILOVER_TYPE_PROPERTY);
        if (failoverType != null && !"".equals(failoverType)) {
            try {
                wiredValue = Integer.parseInt(failoverType);
            } catch (NumberFormatException e) {
                wiredValue = 0;
            }
            boolean isFailoverOnRemote = (wiredValue & 512) == 512;
            this.isAutoACEnabled = !isFailoverOnRemote && (wiredValue & 32) == 32;
            this.isTransactionReplayEnabled = this.isAutoACEnabled || (!isFailoverOnRemote && (wiredValue & 8) == 8);
            this.isReplayInDynamicMode = (wiredValue & 16) == 0;
            this.isHybrid = (wiredValue & 256) == 256;
            this.isSSSCursorEnabled = getSSSCursorProperty();
        } else {
            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "enableACAndProxifyIfNecessary", "AC is disabled because service does not return FAILOVER_TYPE attribute. conn={0}. ", (String) null, (String) null, (Object) conn);
            useProxy = false;
        }
        String failoverRestore = sessionProperties.getProperty(FAILOVER_RESTORE_PROPERTY);
        if (failoverRestore != null && !"".equals(failoverRestore) && "thin".equals(oconn.getProtocolType())) {
            int _restore = Integer.parseInt(failoverRestore);
            this.stateRestorationType = ReplayableConnection.StateRestorationType.values()[_restore];
            if (this.stateRestorationType.compareTo(ReplayableConnection.StateRestorationType.NONE) > 0) {
                this.isStateRestorationAuto = this.stateRestorationType.compareTo(ReplayableConnection.StateRestorationType.LEVEL2) >= 0;
                if (this.isAutoACEnabled && !this.isStateRestorationAuto) {
                    debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "enableACAndProxifyIfNecessary", "AC is disabled because FAILOVER_TYPE at server is AUTO, while FAILOVER_RESTORE is not. conn={0}. ", (String) null, (String) null, (Object) conn);
                    useProxy = false;
                }
            } else {
                debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "enableACAndProxifyIfNecessary", "Server FAILOVER_RESTORE={0}, disabling driver AC state restoration.", (String) null, (String) null, (Object) Integer.valueOf(_restore));
            }
        }
        String initiationTimeout = sessionProperties.getProperty(INITIATION_TIMEOUT_PROPERTY);
        if (initiationTimeout != null && !"".equals(initiationTimeout)) {
            this.replayInitiationTimeout = Integer.parseInt(initiationTimeout);
        }
        short dbVersion = oconn.getVersionNumber();
        if (dbVersion < 11203) {
            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "enableACAndProxifyIfNecessary", "AC is disabled because server version does not support AC. conn={0}.", (String) null, (String) null, (Object) conn);
            useProxy = false;
        } else if (dbVersion >= 12100 && !this.isTransactionReplayEnabled) {
            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "enableACAndProxifyIfNecessary", "AC is disabled because FAILOVER_TYPE service attribute is not set to TRANSACTION in server. conn={0}. ", (String) null, (String) null, (Object) conn);
            useProxy = false;
        } else if (dbVersion >= 11203 && dbVersion < 12100) {
            boolean enableAC11203 = "true".equalsIgnoreCase(getSystemProperty(AC_11203_COMPATIBLE_SYSTEM_PROPERTY, "false"));
            if (enableAC11203) {
                conn.close();
                setConnectionProperty("oracle.jdbc.calculateChecksum", this.clientChecksum11203x);
                conn = getConnection(connBuilder);
                Monitor.CloseableLock lock = proxyFactoryLock.acquireCloseableLock();
                Throwable th = null;
                try {
                    try {
                        PROXY_FACTORY = NON_TXN_PROXY_FACTORY;
                        if (lock != null) {
                            if (0 != 0) {
                                try {
                                    lock.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                lock.close();
                            }
                        }
                    } finally {
                    }
                } catch (Throwable th3) {
                    if (lock != null) {
                        if (th != null) {
                            try {
                                lock.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    throw th3;
                }
            } else {
                debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "enableACAndProxifyIfNecessary", "AC is disabled because server version does not support AC. conn={0}. ", (String) null, (String) null, (Object) conn);
                useProxy = false;
            }
        }
        if (useProxy) {
            oconn.enableDiagnoseFirstFailureDump(false);
            if (this.trackerInitialized.compareAndSet(false, true)) {
                this.tracker = new StatisticsTracker();
                ReplayStatisticsMBeanImpl.SOLE_INSTANCE.addTrackerForDS(this.tracker);
            }
            Connection connProxy = (Connection) PROXY_FACTORY.proxyFor(conn);
            ReplayableConnection rconn = (ReplayableConnection) connProxy;
            rconn.initialize(this, connBuilder);
            if (dbVersion >= 12100) {
                rconn.setReplayInitiationTimeout(this.replayInitiationTimeout);
                rconn.setAutoAC(this.isAutoACEnabled);
                rconn.setSessionStateConsistency(!this.isReplayInDynamicMode);
                rconn.setSessionStateRestoration(this.stateRestorationType);
                rconn.setSSSCursorEnabled(this.isSSSCursorEnabled);
                rconn.setHybrid(this.isSSSCursorEnabled && this.isHybrid);
            }
            ((OracleConnection) conn).getReplayContext();
            boolean enableImplicitBeginRequest = "true".equalsIgnoreCase(getSystemProperty(IMPLICIT_BEGIN_REQUEST_SYSTEM_PROPERTY, "true"));
            if (this.isAutoACEnabled && enableImplicitBeginRequest && !oconn.isDRCPEnabled()) {
                rconn.beginRequest();
            }
            return connProxy;
        }
        return conn;
    }

    @Override // oracle.jdbc.replay.OracleDataSource
    public void registerConnectionInitializationCallback(ConnectionInitializationCallback cbk) throws SQLException {
        if (cbk == null) {
            throw DatabaseError.createSqlException(68);
        }
        this.connectionInitializationCallback = cbk;
        debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "registerConnectionInitializationCallback", "cbk={0}. ", (String) null, (String) null, (Object) cbk);
    }

    @Override // oracle.jdbc.replay.OracleDataSource
    public void unregisterConnectionInitializationCallback(ConnectionInitializationCallback cbk) throws SQLException {
        if (cbk == null || this.connectionInitializationCallback != cbk) {
            throw DatabaseError.createSqlException(68);
        }
        this.connectionInitializationCallback = null;
        debug(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "unregisterConnectionInitializationCallback", "cbk={0}. ", (String) null, (String) null, (Object) cbk);
    }

    @Override // oracle.jdbc.replay.OracleDataSource
    public ConnectionInitializationCallback getConnectionInitializationCallback() {
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnectionInitializationCallback", "cbk={0}. ", (String) null, (String) null, (Object) this.connectionInitializationCallback);
        return this.connectionInitializationCallback;
    }

    @Override // oracle.jdbc.replay.OracleDataSource
    public ReplayStatistics getReplayStatistics() {
        return this.tracker.getReplayStatistics();
    }

    @Override // oracle.jdbc.replay.internal.OracleDataSource
    public void clearDoneDumpOnMemoryPressure() {
        this.doneDumpOnMemoryPressure.set(false);
    }

    @Override // oracle.jdbc.replay.internal.OracleDataSource
    public String getReplayStatisticsString() {
        if (this.doneDumpOnMemoryPressure.compareAndSet(false, true)) {
            return null;
        }
        return this.tracker.getReplayStatisticsString();
    }

    @Override // oracle.jdbc.replay.OracleDataSource
    public void clearReplayStatistics() {
        this.tracker.clearReplayStatistics();
    }

    @Override // oracle.jdbc.replay.internal.OracleDataSource
    public void updateReplayStatistics(oracle.jdbc.replay.internal.ReplayStatistics newStats) {
        this.tracker.updateReplayStatistics(getDataSourceName(), newStats);
    }

    @Override // oracle.jdbc.replay.internal.OracleDataSource
    public void removeReplayStatistics(oracle.jdbc.replay.internal.ReplayStatistics stats) {
        this.tracker.removeReplayStatistics(stats);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    public static String getSystemProperty(final String property, final String defaultValue) {
        if (property != null) {
            final String[] rets = {defaultValue};
            AccessController.doPrivileged(new PrivilegedAction() { // from class: oracle.jdbc.datasource.impl.OracleDataSource.2
                @Override // java.security.PrivilegedAction
                public Object run() {
                    rets[0] = System.getProperty(property, defaultValue);
                    return null;
                }
            });
            return rets[0];
        }
        return defaultValue;
    }

    @Override // oracle.jdbc.replay.internal.OracleDataSource
    public int getRequestSizeLimit() throws SQLException, NumberFormatException {
        int limit;
        String tempval = getSystemProperty("oracle.jdbc.replay.protectedRequestSizeLimit", null);
        if (tempval == null) {
            tempval = getConnectionProperty("oracle.jdbc.replay.protectedRequestSizeLimit");
        }
        if (tempval == null) {
            tempval = Integer.toString(Integer.MAX_VALUE);
        }
        try {
            limit = Integer.parseInt(tempval);
        } catch (NumberFormatException e) {
            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "getRequestSizeLimit", "Request-size-limit property is set to an incorrect value with number format problem. ", (String) null, (Throwable) null);
            limit = Integer.MAX_VALUE;
        }
        return limit;
    }

    protected final boolean getSSSCursorProperty() throws SQLException {
        String tempval = getSystemProperty("oracle.jdbc.enableSSSCursor", null);
        if (tempval == null) {
            tempval = getConnectionProperty("oracle.jdbc.enableSSSCursor");
        }
        if (tempval == null) {
            tempval = "false";
        }
        return tempval != null && tempval.equalsIgnoreCase("true");
    }

    @Override // oracle.jdbc.replay.internal.OracleDataSource
    public ProxyFactory getProxyFactory() throws SQLException {
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "getProxyFactory", "PROXY_FACTORY={0}. ", (String) null, (String) null, (Object) PROXY_FACTORY);
        return PROXY_FACTORY;
    }

    public static void registerMBean() {
        MBeanServer mbs;
        try {
            try {
                try {
                    Class<?> cls = Class.forName("oracle.as.jmx.framework.PortableMBeanFactory");
                    Object factory = cls.newInstance();
                    Method mthd = cls.getMethod("getMBeanServer", new Class[0]);
                    mbs = (MBeanServer) mthd.invoke(factory, new Object[0]);
                } catch (NoClassDefFoundError e) {
                    mbs = ManagementFactory.getPlatformMBeanServer();
                } catch (InvocationTargetException ex) {
                    CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "registerMBean", "Found Oracle Apps MBeanServer but the getMBeanServer method threw an exception. ", (String) null, ex);
                    mbs = ManagementFactory.getPlatformMBeanServer();
                }
            } catch (ClassNotFoundException e2) {
                mbs = ManagementFactory.getPlatformMBeanServer();
            } catch (IllegalAccessException ex2) {
                CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "registerMBean", "Found Oracle Apps MBeanServer but could not access the getMBeanServer method. ", (String) null, ex2);
                mbs = ManagementFactory.getPlatformMBeanServer();
            } catch (InstantiationException ex3) {
                CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "registerMBean", "Found Oracle Apps MBeanServer but could not create an instance. ", (String) null, ex3);
                mbs = ManagementFactory.getPlatformMBeanServer();
            } catch (NoSuchMethodException ex4) {
                CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "registerMBean", "Found Oracle Apps MBeanServer but not the getMBeanServer method. ", (String) null, ex4);
                mbs = ManagementFactory.getPlatformMBeanServer();
            }
            if (mbs != null) {
                Object loader = ReplayStatisticsMBeanImpl.class.getClassLoader();
                String loaderName = loader == null ? "nullLoader" : loader.getClass().getName();
                int count = 0;
                while (true) {
                    int i = count;
                    count++;
                    String nameSuffix = loaderName + "@" + Integer.toHexString((loader == null ? 0 : loader.hashCode()) + i);
                    mbeanName = new ObjectName(registeredName + nameSuffix);
                    try {
                        mbs.registerMBean(ReplayStatisticsMBeanImpl.SOLE_INSTANCE, mbeanName);
                        break;
                    } catch (InstanceAlreadyExistsException e3) {
                        CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "registerMBean", "AC statistics MBean with the same name already registered. ", (String) null, (Throwable) null);
                    }
                }
            } else {
                CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "registerMBean", "Unable to find an MBeanServer so no MBears are registered. ", (String) null, (Throwable) null);
            }
        } catch (JMException ex5) {
            CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "registerMBean", "Error while registering Oracle JDBC AC statistics MBean. ", (String) null, ex5);
        } catch (Throwable ex6) {
            CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "registerMBean", "Error while registering Oracle JDBC AC statistics MBean. ", (String) null, ex6);
        }
    }

    public static void unregisterMBean() {
        MBeanServer mbs;
        try {
            try {
                Class<?> cls = Class.forName("oracle.as.jmx.framework.PortableMBeanFactory");
                Object factory = cls.newInstance();
                Method mthd = cls.getMethod("getMBeanServer", new Class[0]);
                mbs = (MBeanServer) mthd.invoke(factory, new Object[0]);
            } catch (Throwable th) {
                mbs = ManagementFactory.getPlatformMBeanServer();
            }
            if (mbs != null) {
                try {
                    Object loader = ReplayStatisticsMBeanImpl.class.getClassLoader();
                    String loaderName = loader == null ? "nullLoader" : loader.getClass().getName();
                    int i = 0 + 1;
                    String nameSuffix = loaderName + "@" + Integer.toHexString((loader == null ? 0 : loader.hashCode()) + 0);
                    mbeanName = new ObjectName(registeredName + nameSuffix);
                    mbs.unregisterMBean(mbeanName);
                } catch (Throwable ex) {
                    CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "unregisterMBean", "Unable to unregister Oracle JDBC AC statistics MBean.", (String) null, ex);
                }
            } else {
                CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "unregisterMBean", "Unable to find an MBeanServer to unregister Oracle JDBC AC statistics MBean. ", (String) null, (Throwable) null);
            }
        } catch (Throwable ex2) {
            CommonDiagnosable.getInstance().debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "unregisterMBean", "Error while unregistering Oracle JDBC AC statistics MBean. ", (String) null, ex2);
        }
    }

    public static void cleanup() {
        unregisterMBean();
    }

    protected void finalize() throws Throwable {
        try {
            if (this.tracker != null) {
                ReplayStatisticsMBeanImpl.SOLE_INSTANCE.removeTrackerForDS(this.tracker);
                this.tracker = null;
            }
        } catch (Throwable th) {
        }
        super.finalize();
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return CommonDiagnosable.getInstance();
    }

    @Override // oracle.jdbc.internal.Monitor
    public final Monitor.CloseableLock getMonitorLock() {
        return this.monitorLock;
    }

    @Override // oracle.jdbc.datasource.OracleCommonDataSource
    public final void setTokenSupplier(Supplier<? extends AccessToken> tokenSupplier) {
        this.tokenSupplier = (Supplier) Objects.requireNonNull(tokenSupplier, "tokenSuplier is null");
    }
}
