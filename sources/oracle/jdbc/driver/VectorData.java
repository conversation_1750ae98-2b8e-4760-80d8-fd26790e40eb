package oracle.jdbc.driver;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.ByteOrder;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Arrays;
import java.util.Locale;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.ToIntFunction;
import oracle.jdbc.OracleType;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.driver.ByteArray;
import oracle.net.ns.SQLnetDef;
import oracle.sql.VECTOR;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData.class */
public final class VectorData {
    private static final int MAGIC_OFFSET = 0;
    private static final int VERSION_OFFSET = 1;
    private static final int FLAG1_OFFSET = 2;
    private static final int TYPE_OFFSET = 4;
    private static final int LENGTH_OFFSET = 5;
    private static final int NORM_OFFSET = 9;
    private static final byte LVECTOR_H_MAGIC = -37;
    private static final short LVECTOR_H_FLAG1_OPTIONAL = Short.MIN_VALUE;
    private static final short LVECTOR_H_FLAG1_LITENDIAN = 1;
    private static final short LVECTOR_H_FLAG1_NORM = 2;
    private static final short LVECTOR_H_FLAG1_UKDIM = 4;
    private static final short LVECTOR_H_FLAG1_IEEETYP = 8;
    private static final short LVECTOR_H_FLAG1_NORMSRC = 16;
    private static final short LVECTOR_H_FLAG1_SPARSE = 32;
    private static final byte LVECTOR_FLEX = 0;
    private static final byte LVECTOR_FLOAT16 = 1;
    private static final byte LVECTOR_FLOAT32 = 2;
    private static final byte LVECTOR_DOUBLE = 3;
    private static final byte LVECTOR_INT8 = 4;
    private static final byte LVECTOR_BINARY = 5;
    private static final int BUFFER_SIZE = 16384;

    private VectorData() {
    }

    public static <T> byte[] encode(T values, OracleType vectorType, boolean isComputingNorm) throws SQLException {
        Encoder<? super T> encoder = getEncoder(values, vectorType.getVendorTypeNumber().intValue());
        int maximumByteLength = encoder.getMaximumByteLength(values);
        byte[] data = new byte[maximumByteLength];
        ByteArray byteArray = wrapBytes(data);
        encoder.encode(values, isComputingNorm, byteArray);
        int encodedLength = (int) byteArray.getPosition();
        return encodedLength == data.length ? data : Arrays.copyOf(data, encodedLength);
    }

    static <T> Encoder<? super T> getEncoder(Object object, int vectorType) throws SQLException {
        if (!(object instanceof double[])) {
            if (!(object instanceof float[])) {
                if (!(object instanceof byte[])) {
                    if (!(object instanceof long[])) {
                        if (!(object instanceof int[])) {
                            if (!(object instanceof short[])) {
                                if (!(object instanceof boolean[])) {
                                    if (!(object instanceof VECTOR.SparseDoubleArray)) {
                                        if (!(object instanceof VECTOR.SparseFloatArray)) {
                                            if (object instanceof VECTOR.SparseByteArray) {
                                                switch (vectorType) {
                                                    case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                                                        return SparseEncoder.BYTE_TO_FLOAT64;
                                                    case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                                                        return SparseEncoder.BYTE_TO_FLOAT32;
                                                    case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                                                    case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                                                        return SparseEncoder.BYTE_TO_INT8;
                                                }
                                            }
                                        } else {
                                            switch (vectorType) {
                                                case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                                                    return SparseEncoder.FLOAT_TO_FLOAT64;
                                                case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                                                case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                                                    return SparseEncoder.FLOAT_TO_FLOAT32;
                                                case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                                                    return NarrowingSparseEncoder.FLOAT_TO_INT8;
                                            }
                                        }
                                    } else {
                                        switch (vectorType) {
                                            case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                                            case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                                                return SparseEncoder.DOUBLE_TO_FLOAT64;
                                            case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                                                return NarrowingSparseEncoder.DOUBLE_TO_FLOAT32;
                                            case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                                                return NarrowingSparseEncoder.DOUBLE_TO_INT8;
                                        }
                                    }
                                } else {
                                    switch (vectorType) {
                                        case oracle.jdbc.OracleTypes.VECTOR_BINARY /* -109 */:
                                        case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                                            return BinaryConversionEncoder.BOOLEAN_ENCODER;
                                        case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                                            return Float64ConversionEncoder.BOOLEAN_ENCODER;
                                        case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                                            return Float32ConversionEncoder.BOOLEAN_ENCODER;
                                        case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                                            return Int8ConversionEncoder.BOOLEAN_ENCODER;
                                    }
                                }
                            } else {
                                switch (vectorType) {
                                    case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                                        return Float64ConversionEncoder.SHORT_ENCODER;
                                    case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                                        return Float32ConversionEncoder.SHORT_ENCODER;
                                    case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                                        return Int8ConversionEncoder.SHORT_ENCODER;
                                }
                            }
                        } else {
                            switch (vectorType) {
                                case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                                    return Float64ConversionEncoder.INT_ENCODER;
                                case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                                    return Float32ConversionEncoder.INT_ENCODER;
                                case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                                    return Int8ConversionEncoder.INT_ENCODER;
                            }
                        }
                    } else {
                        switch (vectorType) {
                            case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                                return Float64ConversionEncoder.LONG_ENCODER;
                            case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                                return Float32ConversionEncoder.LONG_ENCODER;
                            case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                                return Int8ConversionEncoder.LONG_ENCODER;
                        }
                    }
                } else {
                    switch (vectorType) {
                        case oracle.jdbc.OracleTypes.VECTOR_BINARY /* -109 */:
                            return BinaryEncoder.INSTANCE;
                        case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                            return Float64ConversionEncoder.BYTE_ENCODER;
                        case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                            return Float32ConversionEncoder.BYTE_ENCODER;
                        case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                        case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                            return Int8Encoder.INSTANCE;
                    }
                }
            } else {
                switch (vectorType) {
                    case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                        return Float64ConversionEncoder.FLOAT_ENCODER;
                    case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                    case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                        return Float32Encoder.INSTANCE;
                    case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                        return Int8ConversionEncoder.FLOAT_ENCODER;
                }
            }
        } else {
            switch (vectorType) {
                case oracle.jdbc.OracleTypes.VECTOR_FLOAT64 /* -108 */:
                case oracle.jdbc.OracleTypes.VECTOR /* -105 */:
                    return Float64Encoder.INSTANCE;
                case oracle.jdbc.OracleTypes.VECTOR_FLOAT32 /* -107 */:
                    return Float32ConversionEncoder.DOUBLE_ENCODER;
                case oracle.jdbc.OracleTypes.VECTOR_INT8 /* -106 */:
                    return Int8ConversionEncoder.DOUBLE_ENCODER;
            }
        }
        throw unsupportedConversion(object.getClass(), OracleType.toOracleType(vectorType));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static SQLException unsupportedConversion(OracleType fromSqlType, Class<?> toJavaClass) {
        return (SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 4, "Conversions of " + fromSqlType.toString() + " to " + toJavaClass.getSimpleName() + " are not supported. Supported conversions are specified in the SQL to Java Conversions section of the JavaDoc for oracle.jdbc.OracleType." + fromSqlType).fillInStackTrace();
    }

    private static SQLException unsupportedConversion(Class<?> fromJavaClass, OracleType toSqlType) {
        return (SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 4, "Conversions of " + fromJavaClass.getSimpleName() + " to " + toSqlType.toString() + " are not supported. Supported conversions are specified in the Java to SQL Conversions section of the JavaDoc for oracle.jdbc.OracleType." + toSqlType).fillInStackTrace();
    }

    public static OracleType decodeType(byte[] data) throws SQLException {
        validateMagicNumber(data[0]);
        try {
            return toOracleType(data[4]);
        } catch (RuntimeException runtimeException) {
            throw unrecognizedData(runtimeException);
        }
    }

    public static OracleType toOracleType(byte typeCode) throws SQLException {
        switch (typeCode) {
            case 0:
                return OracleType.VECTOR;
            case 1:
            default:
                throw DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 89, "Unrecognized vector type code: " + ((int) typeCode));
            case 2:
                return OracleType.VECTOR_FLOAT32;
            case 3:
                return OracleType.VECTOR_FLOAT64;
            case 4:
                return OracleType.VECTOR_INT8;
            case 5:
                return OracleType.VECTOR_BINARY;
        }
    }

    public static int decodeLength(byte[] data) throws SQLException {
        validateMagicNumber(data[0]);
        try {
            return ((data[5] & 255) << 24) | ((data[6] & 255) << 16) | ((data[7] & 255) << 8) | (data[8] & 255);
        } catch (RuntimeException runtimeException) {
            throw unrecognizedData(runtimeException);
        }
    }

    public static boolean isSparse(byte[] data) throws SQLException {
        try {
            return isSparseFlagSet(decodeFlag1(data));
        } catch (RuntimeException runtimeException) {
            throw unrecognizedData(runtimeException);
        }
    }

    private static boolean isSparseFlagSet(short flag1) {
        return 0 != (flag1 & 32);
    }

    private static short decodeFlag1(byte[] data) {
        return (short) ((data[2] << 8) | (data[3] & 255));
    }

    private static ByteArray wrapBytes(byte[] data) {
        return new SimpleByteArray(CommonDiagnosable.getInstance(), data);
    }

    public static <T> T decode(byte[] bArr, Class<T> cls, boolean z) throws SQLException {
        return (T) decode(new SimpleByteArray(CommonDiagnosable.getInstance(), bArr), cls, z);
    }

    static <T> T decode(ByteArray byteArray, Class<T> cls, boolean z) throws SQLException {
        validateMagicNumber(byteArray.get());
        validateVersion(byteArray.get());
        long position = byteArray.getPosition();
        short flag1 = readFlag1(byteArray);
        byte b = byteArray.get();
        int i = byteArray.getInt();
        if (isNormBeforeValues(flag1)) {
            byteArray.setPosition(byteArray.getPosition() + 8);
        }
        Decoder<?> decoder = getDecoder(flag1, b);
        if (z) {
            decoder = decoder.toIeee(byteArray, i);
            byteArray.putShort(position, (short) (flag1 | 8));
        }
        return (T) decoder.decode(byteArray, i, cls);
    }

    private static void validateMagicNumber(byte magicNumber) throws SQLException {
        if (magicNumber != LVECTOR_H_MAGIC) {
            throw unrecognizedData("Possible data corruption. Unrecognized vector magic number: 0x" + Integer.toHexString(magicNumber & 255));
        }
    }

    private static void validateVersion(byte version) throws SQLException {
        if (version < 0 || version > 2) {
            throw unrecognizedData("Unrecognized vector version: " + ((int) version));
        }
    }

    private static short readFlag1(ByteArray data) throws SQLException {
        int flag1 = data.getShort();
        if ((flag1 & LVECTOR_H_FLAG1_OPTIONAL) != 0) {
            throw unrecognizedData("Unrecognized VECTOR flag: " + Integer.toHexString(flag1));
        }
        return (short) flag1;
    }

    private static boolean isNormBeforeValues(short flag1) {
        return isNormFlagSet(flag1) || isNormPaddingFlagSet(flag1);
    }

    private static boolean isNormFlagSet(short flag1) {
        return (flag1 & 2) != 0;
    }

    private static boolean isNormPaddingFlagSet(short flag1) {
        return (flag1 & 16) != 0;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static SQLException unrecognizedData(String message) {
        return DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 89, message);
    }

    private static SQLException unrecognizedData(RuntimeException runtimeException) {
        SQLException sqlException = unrecognizedData("Unrecognized VECTOR encoding");
        sqlException.initCause(runtimeException);
        return sqlException;
    }

    private static Decoder<?> getDecoder(short flag1, byte typeCode) throws SQLException {
        Decoder<?> decoder;
        switch (typeCode) {
            case 2:
                decoder = Float32Decoder.getDecoder(flag1);
                break;
            case 3:
                decoder = Float64Decoder.getDecoder(flag1);
                break;
            case 4:
                decoder = Int8Decoder.getDecoder();
                break;
            case 5:
                decoder = BinaryDecoder.getDecoder();
                break;
            default:
                throw unrecognizedData("Unrecognized vector type: " + ((int) typeCode));
        }
        return isSparseFlagSet(flag1) ? decoder.getSparseDecoder() : decoder;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ByteArray.NumberEncoding getNumberEncoding(short flag1) {
        boolean isIeee = (flag1 & 8) != 0;
        boolean isLittleEndian = (flag1 & 1) != 0;
        return isIeee ? isLittleEndian ? ByteArray.NumberEncoding.IEEE_LITTLE_ENDIAN : ByteArray.NumberEncoding.IEEE_BIG_ENDIAN : isLittleEndian ? ByteArray.NumberEncoding.ORACLE_LITTLE_ENDIAN : ByteArray.NumberEncoding.ORACLE_BIG_ENDIAN;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static int getFlag1FormatBits(ByteArray.NumberEncoding numberEncoding) {
        int flag1 = 0;
        if (numberEncoding.isIeee()) {
            flag1 = 0 | 8;
        }
        if (numberEncoding.byteOrder() == ByteOrder.LITTLE_ENDIAN) {
            flag1 |= 1;
        }
        return flag1;
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Decoder.class */
    private static abstract class Decoder<T> {
        private final OracleType type;
        private final SparseDecoder<T> sparseDecoder = SparseDecoder.getDecoder(this);

        abstract T decodePreferredArrayClass(ByteArray byteArray, int i) throws SQLException;

        abstract ArrayConverter<T> preferredArrayConverter();

        Decoder(OracleType type) {
            this.type = type;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public <U> U decode(ByteArray data, int length, Class<U> decodedClass) throws SQLException {
            Object decodedObject;
            if (decodedClass == double[].class) {
                decodedObject = decodeDoubles(data, length);
            } else if (decodedClass == float[].class) {
                decodedObject = decodeFloats(data, length);
            } else if (decodedClass == byte[].class) {
                decodedObject = decodeBytes(data, length);
            } else if (decodedClass == short[].class) {
                decodedObject = decodeShorts(data, length);
            } else if (decodedClass == int[].class) {
                decodedObject = decodeInts(data, length);
            } else if (decodedClass == long[].class) {
                decodedObject = decodeLongs(data, length);
            } else if (decodedClass == boolean[].class) {
                decodedObject = decodeBooleans(data, length);
            } else if (decodedClass == String.class) {
                decodedObject = decodeString(data, length);
            } else if (decodedClass == VECTOR.SparseDoubleArray.class) {
                decodedObject = decodeSparseDoubleArray(data, length);
            } else if (decodedClass == VECTOR.SparseFloatArray.class) {
                decodedObject = decodeSparseFloatArray(data, length);
            } else if (decodedClass == VECTOR.SparseByteArray.class) {
                decodedObject = decodeSparseByteArray(data, length);
            } else {
                if (decodedClass != Object.class) {
                    throw VectorData.unsupportedConversion(this.type, (Class<?>) decodedClass);
                }
                decodedObject = decodedClass.cast(decodePreferredArrayClass(data, length));
            }
            return decodedClass.cast(decodedObject);
        }

        Decoder<T> toIeee(ByteArray data, int length) throws SQLException {
            return this;
        }

        boolean[] decodeBooleans(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(this.type, (Class<?>) boolean[].class);
        }

        byte[] decodeBytes(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(this.type, (Class<?>) byte[].class);
        }

        short[] decodeShorts(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(this.type, (Class<?>) short[].class);
        }

        int[] decodeInts(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(this.type, (Class<?>) int[].class);
        }

        long[] decodeLongs(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(this.type, (Class<?>) long[].class);
        }

        float[] decodeFloats(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(this.type, (Class<?>) float[].class);
        }

        double[] decodeDoubles(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(this.type, (Class<?>) double[].class);
        }

        String decodeString(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(this.type, (Class<?>) String.class);
        }

        VECTOR.SparseDoubleArray decodeSparseDoubleArray(ByteArray data, int length) throws SQLException {
            return DoubleArrayConverter.INSTANCE.toSparseDoubleArray(decodeDoubles(data, length));
        }

        VECTOR.SparseFloatArray decodeSparseFloatArray(ByteArray data, int length) throws SQLException {
            return FloatArrayConverter.INSTANCE.toSparseFloatArray(decodeFloats(data, length));
        }

        VECTOR.SparseByteArray decodeSparseByteArray(ByteArray data, int length) throws SQLException {
            return ByteArrayConverter.INSTANCE.toSparseByteArray(decodeBytes(data, length));
        }

        final SparseDecoder<T> getSparseDecoder() throws SQLException {
            if (this.sparseDecoder == null) {
                throw VectorData.unrecognizedData("Decoding of " + this.type.toString() + " SPARSE VECTOR data is not supported");
            }
            return this.sparseDecoder;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Int8Decoder.class */
    private static final class Int8Decoder extends Decoder<byte[]> {
        private static final Int8Decoder INSTANCE = new Int8Decoder();

        static Int8Decoder getDecoder() {
            return INSTANCE;
        }

        private Int8Decoder() {
            super(OracleType.VECTOR_INT8);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        boolean[] decodeBooleans(ByteArray data, int length) {
            boolean[] array = new boolean[length];
            for (int i = 0; i < length; i++) {
                array[i] = data.get() != 0;
            }
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        byte[] decodeBytes(ByteArray data, int length) {
            byte[] array = new byte[length];
            data.getBytes(array, 0, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        short[] decodeShorts(ByteArray data, int length) {
            short[] array = new short[length];
            for (int i = 0; i < length; i++) {
                array[i] = data.get();
            }
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        int[] decodeInts(ByteArray data, int length) {
            int[] array = new int[length];
            for (int i = 0; i < length; i++) {
                array[i] = data.get();
            }
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        long[] decodeLongs(ByteArray data, int length) {
            long[] array = new long[length];
            for (int i = 0; i < length; i++) {
                array[i] = data.get();
            }
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        float[] decodeFloats(ByteArray data, int length) {
            float[] array = new float[length];
            for (int i = 0; i < length; i++) {
                array[i] = data.get();
            }
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        double[] decodeDoubles(ByteArray data, int length) {
            double[] array = new double[length];
            for (int i = 0; i < length; i++) {
                array[i] = data.get();
            }
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        String decodeString(ByteArray data, int length) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append('[');
            for (int i = 0; i < length; i++) {
                byte value = data.get();
                if (i > 0) {
                    stringBuilder.append(',');
                }
                stringBuilder.append((int) value);
            }
            stringBuilder.append(']');
            return stringBuilder.toString();
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // oracle.jdbc.driver.VectorData.Decoder
        public byte[] decodePreferredArrayClass(ByteArray data, int length) {
            return decodeBytes(data, length);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        ArrayConverter<byte[]> preferredArrayConverter() {
            return ByteArrayConverter.INSTANCE;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Float32Decoder.class */
    private static abstract class Float32Decoder extends Decoder<float[]> {
        protected static final DecimalFormat POSITIVE_FORMAT;
        protected static final DecimalFormat NEGATIVE_FORMAT;
        protected final ByteArray.NumberEncoding numberEncoding;

        /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Float32Decoder$IntFloatConsumer.class */
        private interface IntFloatConsumer {
            void accept(int i, float f);
        }

        static Float32Decoder getDecoder(short flag1) {
            ByteArray.NumberEncoding numberEncoding = VectorData.getNumberEncoding(flag1);
            boolean isOracleEncoding = !numberEncoding.isIeee();
            ByteOrder byteOrder = numberEncoding.byteOrder();
            if (isOracleEncoding) {
                return OracleFloat32Decoder.getDecoder(byteOrder);
            }
            return IeeeFloat32Decoder.getDecoder(byteOrder);
        }

        static {
            DecimalFormatSymbols decimalFormatSymbols = DecimalFormatSymbols.getInstance(Locale.US);
            decimalFormatSymbols.setExponentSeparator("E+");
            POSITIVE_FORMAT = new DecimalFormat("0.0#######E000", decimalFormatSymbols);
            POSITIVE_FORMAT.setRoundingMode(RoundingMode.HALF_EVEN);
            NEGATIVE_FORMAT = new DecimalFormat("0.0#######E000", DecimalFormatSymbols.getInstance(Locale.US));
            NEGATIVE_FORMAT.setRoundingMode(RoundingMode.HALF_EVEN);
        }

        Float32Decoder(ByteArray.NumberEncoding numberEncoding) {
            super(OracleType.VECTOR_FLOAT32);
            this.numberEncoding = numberEncoding;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final float[] decodeFloats(ByteArray data, int length) {
            float[] array = new float[length];
            data.getFloats(array, 0, length, this.numberEncoding);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final boolean[] decodeBooleans(ByteArray data, int length) {
            boolean[] array = new boolean[length];
            streamFloats(data, (index, value) -> {
                array[index] = value != 0.0f;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final byte[] decodeBytes(ByteArray data, int length) {
            byte[] array = new byte[length];
            streamFloats(data, (index, value) -> {
                array[index] = (byte) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final short[] decodeShorts(ByteArray data, int length) {
            short[] array = new short[length];
            streamFloats(data, (index, value) -> {
                array[index] = (short) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final int[] decodeInts(ByteArray data, int length) {
            int[] array = new int[length];
            streamFloats(data, (index, value) -> {
                array[index] = (int) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final long[] decodeLongs(ByteArray data, int length) {
            long[] array = new long[length];
            streamFloats(data, (index, value) -> {
                array[index] = (long) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final double[] decodeDoubles(ByteArray data, int length) {
            double[] array = new double[length];
            streamFloats(data, (index, value) -> {
                array[index] = value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final String decodeString(ByteArray data, int length) {
            DecimalFormat positiveFormat = (DecimalFormat) POSITIVE_FORMAT.clone();
            DecimalFormat negativeFormat = (DecimalFormat) NEGATIVE_FORMAT.clone();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append('[');
            streamFloats(data, (index, value) -> {
                String stringValue;
                if (index > 0) {
                    stringBuilder.append(',');
                }
                if (value == 0.0f) {
                    stringValue = "0";
                } else if (value >= 1.0f || value <= -1.0f) {
                    stringValue = positiveFormat.format(value);
                } else {
                    stringValue = negativeFormat.format(value);
                }
                stringBuilder.append(stringValue);
            }, length);
            stringBuilder.append(']');
            return stringBuilder.toString();
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // oracle.jdbc.driver.VectorData.Decoder
        public float[] decodePreferredArrayClass(ByteArray data, int length) {
            return decodeFloats(data, length);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        ArrayConverter<float[]> preferredArrayConverter() {
            return FloatArrayConverter.INSTANCE;
        }

        private void streamFloats(ByteArray data, IntFloatConsumer consumer, int length) {
            int bufferSize = Math.min(length, 4096);
            float[] buffer = new float[bufferSize];
            int valueIndex = 0;
            while (valueIndex < length) {
                int getLength = Math.min(length - valueIndex, bufferSize);
                data.getFloats(buffer, 0, getLength, this.numberEncoding);
                for (int i = 0; i < getLength; i++) {
                    int i2 = valueIndex;
                    valueIndex++;
                    consumer.accept(i2, buffer[i]);
                }
            }
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$OracleFloat32Decoder.class */
    private static final class OracleFloat32Decoder extends Float32Decoder {
        private static final OracleFloat32Decoder BIG_ENDIAN = new OracleFloat32Decoder(ByteArray.NumberEncoding.ORACLE_BIG_ENDIAN);
        private static final OracleFloat32Decoder LITTLE_ENDIAN = new OracleFloat32Decoder(ByteArray.NumberEncoding.ORACLE_LITTLE_ENDIAN);

        static OracleFloat32Decoder getDecoder(ByteOrder byteOrder) {
            return byteOrder == ByteOrder.BIG_ENDIAN ? BIG_ENDIAN : LITTLE_ENDIAN;
        }

        private OracleFloat32Decoder(ByteArray.NumberEncoding numberEncoding) {
            super(numberEncoding);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        protected Decoder<float[]> toIeee(ByteArray data, int length) throws SQLException {
            if (this.numberEncoding.byteOrder() == ByteOrder.LITTLE_ENDIAN) {
                return littleEndianToIeee(data, length);
            }
            return bigEndianToIeee(data, length);
        }

        private Decoder<float[]> bigEndianToIeee(ByteArray data, int length) throws SQLException {
            long position = data.getPosition();
            long limit = position + (length << 2);
            while (position < limit) {
                int bits = data.getInt(position);
                if ((bits & SQLnetDef.NSPCNCON) != 0) {
                    data.put(position, (byte) ((bits >> 24) & 127));
                } else {
                    data.putInt(position, bits ^ (-1));
                }
                position += 4;
            }
            return IeeeFloat32Decoder.BIG_ENDIAN;
        }

        private Decoder<float[]> littleEndianToIeee(ByteArray data, int length) throws SQLException {
            long position = data.getPosition();
            long limit = position + (length << 2);
            while (position < limit) {
                int bits = data.getInt(position);
                if ((bits & 128) != 0) {
                    data.put(position + 3, (byte) (bits & 127));
                } else {
                    data.putInt(position, bits ^ (-1));
                }
                position += 4;
            }
            return IeeeFloat32Decoder.LITTLE_ENDIAN;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$IeeeFloat32Decoder.class */
    private static final class IeeeFloat32Decoder extends Float32Decoder {
        static final IeeeFloat32Decoder BIG_ENDIAN = new IeeeFloat32Decoder(ByteArray.NumberEncoding.IEEE_BIG_ENDIAN);
        static final IeeeFloat32Decoder LITTLE_ENDIAN = new IeeeFloat32Decoder(ByteArray.NumberEncoding.IEEE_LITTLE_ENDIAN);

        static IeeeFloat32Decoder getDecoder(ByteOrder byteOrder) {
            return byteOrder == ByteOrder.BIG_ENDIAN ? BIG_ENDIAN : LITTLE_ENDIAN;
        }

        private IeeeFloat32Decoder(ByteArray.NumberEncoding numberEncoding) {
            super(numberEncoding);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Float64Decoder.class */
    private static abstract class Float64Decoder extends Decoder<double[]> {
        protected static final DecimalFormat POSITIVE_FORMAT;
        protected static final DecimalFormat NEGATIVE_FORMAT;
        protected final ByteArray.NumberEncoding numberEncoding;

        /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Float64Decoder$IntDoubleConsumer.class */
        private interface IntDoubleConsumer {
            void accept(int i, double d);
        }

        static Float64Decoder getDecoder(short flag1) {
            ByteArray.NumberEncoding numberEncoding = VectorData.getNumberEncoding(flag1);
            boolean isOracleEncoding = !numberEncoding.isIeee();
            ByteOrder byteOrder = numberEncoding.byteOrder();
            if (isOracleEncoding) {
                return OracleFloat64Decoder.getDecoder(byteOrder);
            }
            return IeeeFloat64Decoder.getDecoder(byteOrder);
        }

        static {
            DecimalFormatSymbols decimalFormatSymbols = DecimalFormatSymbols.getInstance(Locale.US);
            decimalFormatSymbols.setExponentSeparator("E+");
            POSITIVE_FORMAT = new DecimalFormat("0.0###############E000", decimalFormatSymbols);
            POSITIVE_FORMAT.setRoundingMode(RoundingMode.HALF_EVEN);
            NEGATIVE_FORMAT = new DecimalFormat("0.0###############E000", DecimalFormatSymbols.getInstance(Locale.US));
            NEGATIVE_FORMAT.setRoundingMode(RoundingMode.HALF_EVEN);
        }

        Float64Decoder(ByteArray.NumberEncoding numberEncoding) {
            super(OracleType.VECTOR_FLOAT64);
            this.numberEncoding = numberEncoding;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final double[] decodeDoubles(ByteArray data, int length) {
            double[] array = new double[length];
            data.getDoubles(array, 0, length, this.numberEncoding);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final boolean[] decodeBooleans(ByteArray data, int length) {
            boolean[] array = new boolean[length];
            streamDoubles(data, (index, value) -> {
                array[index] = value != 0.0d;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final byte[] decodeBytes(ByteArray data, int length) {
            byte[] array = new byte[length];
            streamDoubles(data, (index, value) -> {
                array[index] = (byte) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final int[] decodeInts(ByteArray data, int length) {
            int[] array = new int[length];
            streamDoubles(data, (index, value) -> {
                array[index] = (int) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final short[] decodeShorts(ByteArray data, int length) {
            short[] array = new short[length];
            streamDoubles(data, (index, value) -> {
                array[index] = (short) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final long[] decodeLongs(ByteArray data, int length) {
            long[] array = new long[length];
            streamDoubles(data, (index, value) -> {
                array[index] = (long) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final float[] decodeFloats(ByteArray data, int length) {
            float[] array = new float[length];
            streamDoubles(data, (index, value) -> {
                array[index] = (float) value;
            }, length);
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final String decodeString(ByteArray data, int length) {
            DecimalFormat positiveFormat = (DecimalFormat) POSITIVE_FORMAT.clone();
            DecimalFormat negativeFormat = (DecimalFormat) NEGATIVE_FORMAT.clone();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append('[');
            streamDoubles(data, (index, value) -> {
                String stringValue;
                if (index > 0) {
                    stringBuilder.append(',');
                }
                if (value == 0.0d) {
                    stringValue = "0";
                } else if (value >= 1.0d || value <= -1.0d) {
                    stringValue = positiveFormat.format(new BigDecimal(value));
                } else {
                    stringValue = negativeFormat.format(new BigDecimal(value));
                }
                stringBuilder.append(stringValue);
            }, length);
            stringBuilder.append(']');
            return stringBuilder.toString();
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // oracle.jdbc.driver.VectorData.Decoder
        public double[] decodePreferredArrayClass(ByteArray data, int length) {
            return decodeDoubles(data, length);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        ArrayConverter<double[]> preferredArrayConverter() {
            return DoubleArrayConverter.INSTANCE;
        }

        private void streamDoubles(ByteArray data, IntDoubleConsumer consumer, int length) {
            int bufferSize = Math.min(length, 2048);
            double[] buffer = new double[bufferSize];
            int valueIndex = 0;
            while (valueIndex < length) {
                int getLength = Math.min(length - valueIndex, bufferSize);
                data.getDoubles(buffer, 0, getLength, this.numberEncoding);
                for (int i = 0; i < getLength; i++) {
                    int i2 = valueIndex;
                    valueIndex++;
                    consumer.accept(i2, buffer[i]);
                }
            }
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$OracleFloat64Decoder.class */
    private static final class OracleFloat64Decoder extends Float64Decoder {
        static final OracleFloat64Decoder BIG_ENDIAN = new OracleFloat64Decoder(ByteArray.NumberEncoding.ORACLE_BIG_ENDIAN);
        static final OracleFloat64Decoder LITTLE_ENDIAN = new OracleFloat64Decoder(ByteArray.NumberEncoding.ORACLE_LITTLE_ENDIAN);

        static OracleFloat64Decoder getDecoder(ByteOrder byteOrder) {
            return byteOrder == ByteOrder.BIG_ENDIAN ? BIG_ENDIAN : LITTLE_ENDIAN;
        }

        private OracleFloat64Decoder(ByteArray.NumberEncoding numberEncoding) {
            super(numberEncoding);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        public Decoder<double[]> toIeee(ByteArray data, int length) throws SQLException {
            if (this.numberEncoding.byteOrder() == ByteOrder.LITTLE_ENDIAN) {
                return littleEndianToIeee(data, length);
            }
            return bigEndianToIeee(data, length);
        }

        private Decoder<double[]> bigEndianToIeee(ByteArray data, int length) throws SQLException {
            long position = data.getPosition();
            long limit = position + (length << 3);
            while (position < limit) {
                long bits = data.getLong(position);
                if ((bits & Long.MIN_VALUE) != 0) {
                    data.put(position, (byte) ((bits >> 56) & 127));
                } else {
                    data.putLong(position, bits ^ (-1));
                }
                position += 8;
            }
            return IeeeFloat64Decoder.BIG_ENDIAN;
        }

        private Decoder<double[]> littleEndianToIeee(ByteArray data, int length) throws SQLException {
            long position = data.getPosition();
            long limit = position + (length << 3);
            while (position < limit) {
                long bits = data.getLong(position);
                if ((bits & 128) != 0) {
                    data.put(position + 7, (byte) (bits & 127));
                } else {
                    data.putLong(position, bits ^ (-1));
                }
                position += 8;
            }
            return IeeeFloat64Decoder.LITTLE_ENDIAN;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$IeeeFloat64Decoder.class */
    private static final class IeeeFloat64Decoder extends Float64Decoder {
        private static final IeeeFloat64Decoder BIG_ENDIAN = new IeeeFloat64Decoder(ByteArray.NumberEncoding.IEEE_BIG_ENDIAN);
        private static final IeeeFloat64Decoder LITTLE_ENDIAN = new IeeeFloat64Decoder(ByteArray.NumberEncoding.IEEE_LITTLE_ENDIAN);

        static IeeeFloat64Decoder getDecoder(ByteOrder byteOrder) {
            return byteOrder == ByteOrder.BIG_ENDIAN ? BIG_ENDIAN : LITTLE_ENDIAN;
        }

        private IeeeFloat64Decoder(ByteArray.NumberEncoding numberEncoding) {
            super(numberEncoding);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$BinaryDecoder.class */
    private static final class BinaryDecoder extends Decoder<byte[]> {
        private static final BinaryDecoder INSTANCE = new BinaryDecoder();

        static BinaryDecoder getDecoder() {
            return INSTANCE;
        }

        private BinaryDecoder() {
            super(OracleType.VECTOR_BINARY);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        byte[] decodeBytes(ByteArray data, int length) {
            return data.getBytes((length + 7) >> 3);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        boolean[] decodeBooleans(ByteArray data, int length) {
            boolean[] array = new boolean[length];
            int byteLength = length >> 3;
            int bitIndex = 0;
            for (int i = 0; i < byteLength; i++) {
                int bits = data.get() & 255;
                int i2 = bitIndex;
                int bitIndex2 = bitIndex + 1;
                array[i2] = 0 != (bits & 128);
                int bitIndex3 = bitIndex2 + 1;
                array[bitIndex2] = 0 != (bits & 64);
                int bitIndex4 = bitIndex3 + 1;
                array[bitIndex3] = 0 != (bits & 32);
                int bitIndex5 = bitIndex4 + 1;
                array[bitIndex4] = 0 != (bits & 16);
                int bitIndex6 = bitIndex5 + 1;
                array[bitIndex5] = 0 != (bits & 8);
                int bitIndex7 = bitIndex6 + 1;
                array[bitIndex6] = 0 != (bits & 4);
                int bitIndex8 = bitIndex7 + 1;
                array[bitIndex7] = 0 != (bits & 2);
                bitIndex = bitIndex8 + 1;
                array[bitIndex8] = 0 != (bits & 1);
            }
            if (bitIndex < length) {
                int bits2 = data.get() & 255;
                int mask = 128;
                do {
                    int i3 = bitIndex;
                    bitIndex++;
                    array[i3] = 0 != (bits2 & mask);
                    mask >>= 1;
                } while (bitIndex < length);
            }
            return array;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        String decodeString(ByteArray data, int length) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append('[');
            int int8Length = (length + 7) >> 3;
            for (int i = 0; i < int8Length; i++) {
                byte value = data.get();
                if (i > 0) {
                    stringBuilder.append(',');
                }
                stringBuilder.append(value & 255);
            }
            stringBuilder.append(']');
            return stringBuilder.toString();
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        /* JADX WARN: Can't rename method to resolve collision */
        @Override // oracle.jdbc.driver.VectorData.Decoder
        public byte[] decodePreferredArrayClass(ByteArray data, int length) {
            return decodeBytes(data, length);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        ArrayConverter<byte[]> preferredArrayConverter() {
            return ByteArrayConverter.INSTANCE;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        VECTOR.SparseDoubleArray decodeSparseDoubleArray(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(OracleType.VECTOR_BINARY, (Class<?>) VECTOR.SparseDoubleArray.class);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        VECTOR.SparseFloatArray decodeSparseFloatArray(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(OracleType.VECTOR_BINARY, (Class<?>) VECTOR.SparseFloatArray.class);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        VECTOR.SparseByteArray decodeSparseByteArray(ByteArray data, int length) throws SQLException {
            throw VectorData.unsupportedConversion(OracleType.VECTOR_BINARY, (Class<?>) VECTOR.SparseByteArray.class);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseDecoder.class */
    private static class SparseDecoder<T> extends Decoder<T> {
        private final Decoder<T> dimensionsDecoder;

        /* JADX INFO: Access modifiers changed from: private */
        public static <T> SparseDecoder<T> getDecoder(Decoder<T> dimensionsDecoder) {
            SparseDecoder<T> sparseInt8Decoder;
            if (dimensionsDecoder instanceof SparseDecoder) {
                return (SparseDecoder) dimensionsDecoder;
            }
            if (dimensionsDecoder instanceof Float64Decoder) {
                sparseInt8Decoder = new SparseFloat64Decoder((Float64Decoder) dimensionsDecoder);
            } else if (dimensionsDecoder instanceof Float32Decoder) {
                sparseInt8Decoder = new SparseFloat32Decoder((Float32Decoder) dimensionsDecoder);
            } else if (dimensionsDecoder instanceof Int8Decoder) {
                sparseInt8Decoder = new SparseInt8Decoder((Int8Decoder) dimensionsDecoder);
            } else if (dimensionsDecoder instanceof BinaryDecoder) {
                sparseInt8Decoder = null;
            } else {
                sparseInt8Decoder = null;
            }
            SparseDecoder<T> typedSparseDecoder = sparseInt8Decoder;
            return typedSparseDecoder;
        }

        SparseDecoder(Decoder<T> dimensionsDecoder) {
            super(((Decoder) dimensionsDecoder).type);
            this.dimensionsDecoder = dimensionsDecoder;
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final Decoder<T> toIeee(ByteArray data, int length) throws SQLException {
            long position = data.getPosition();
            try {
                int sparseLength = data.getShort();
                int indicesLength = sparseLength << 2;
                data.setPosition(position + 2 + indicesLength);
                Decoder<T> denseDecoder = this.dimensionsDecoder.toIeee(data, sparseLength);
                SparseDecoder<T> sparseDecoder = denseDecoder.getSparseDecoder();
                data.setPosition(position);
                return sparseDecoder;
            } catch (Throwable th) {
                data.setPosition(position);
                throw th;
            }
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final boolean[] decodeBooleans(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            return (boolean[]) BooleanArrayConverter.INSTANCE.toDenseArray(length, indices, this.dimensionsDecoder.decodeBooleans(data, indices.length));
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final byte[] decodeBytes(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            return (byte[]) ByteArrayConverter.INSTANCE.toDenseArray(length, indices, this.dimensionsDecoder.decodeBytes(data, indices.length));
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final short[] decodeShorts(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            return (short[]) ShortArrayConverter.INSTANCE.toDenseArray(length, indices, this.dimensionsDecoder.decodeShorts(data, indices.length));
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final int[] decodeInts(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            return (int[]) IntArrayConverter.INSTANCE.toDenseArray(length, indices, this.dimensionsDecoder.decodeInts(data, indices.length));
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final long[] decodeLongs(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            return (long[]) LongArrayConverter.INSTANCE.toDenseArray(length, indices, this.dimensionsDecoder.decodeLongs(data, indices.length));
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final float[] decodeFloats(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            return (float[]) FloatArrayConverter.INSTANCE.toDenseArray(length, indices, this.dimensionsDecoder.decodeFloats(data, indices.length));
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final double[] decodeDoubles(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            return (double[]) DoubleArrayConverter.INSTANCE.toDenseArray(length, indices, this.dimensionsDecoder.decodeDoubles(data, indices.length));
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        String decodeString(ByteArray data, int length) throws SQLException {
            StringBuilder stringBuilder = new StringBuilder("[").append(length).append(',');
            stringBuilder.append('[');
            int valuesLength = data.getShort();
            for (int i = 0; i < valuesLength; i++) {
                int value = data.getInt();
                if (i > 0) {
                    stringBuilder.append(',');
                }
                stringBuilder.append(value);
            }
            stringBuilder.append("],");
            return stringBuilder.append(this.dimensionsDecoder.decodeString(data, valuesLength)).append(']').toString();
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final T decodePreferredArrayClass(ByteArray byteArray, int i) throws SQLException {
            int[] iArrDecodeIndices = decodeIndices(byteArray);
            return (T) preferredArrayConverter().toDenseArray(i, iArrDecodeIndices, this.dimensionsDecoder.decodePreferredArrayClass(byteArray, iArrDecodeIndices.length));
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        final ArrayConverter<T> preferredArrayConverter() {
            return this.dimensionsDecoder.preferredArrayConverter();
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        protected VECTOR.SparseDoubleArray decodeSparseDoubleArray(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            double[] values = this.dimensionsDecoder.decodeDoubles(data, indices.length);
            return new SparseDoubleArrayImpl(length, indices, values);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        protected VECTOR.SparseFloatArray decodeSparseFloatArray(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            float[] values = this.dimensionsDecoder.decodeFloats(data, indices.length);
            return new SparseFloatArrayImpl(length, indices, values);
        }

        @Override // oracle.jdbc.driver.VectorData.Decoder
        protected VECTOR.SparseByteArray decodeSparseByteArray(ByteArray data, int length) throws SQLException {
            int[] indices = decodeIndices(data);
            byte[] values = this.dimensionsDecoder.decodeBytes(data, indices.length);
            return new SparseByteArrayImpl(length, indices, values);
        }

        static int[] decodeIndices(ByteArray data) {
            int[] indices = new int[data.getShort()];
            data.getInts(indices, 0, indices.length);
            return indices;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseFloat32Decoder.class */
    private static final class SparseFloat32Decoder extends SparseDecoder<float[]> {
        SparseFloat32Decoder(Float32Decoder dimensionsDecoder) {
            super(dimensionsDecoder);
        }

        @Override // oracle.jdbc.driver.VectorData.SparseDecoder, oracle.jdbc.driver.VectorData.Decoder
        protected VECTOR.SparseByteArray decodeSparseByteArray(ByteArray data, int length) throws SQLException {
            return VectorData.toSparseByteArray(decodeSparseFloatArray(data, length));
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseFloat64Decoder.class */
    private static final class SparseFloat64Decoder extends SparseDecoder<double[]> {
        SparseFloat64Decoder(Float64Decoder dimensionsDecoder) {
            super(dimensionsDecoder);
        }

        @Override // oracle.jdbc.driver.VectorData.SparseDecoder, oracle.jdbc.driver.VectorData.Decoder
        protected VECTOR.SparseFloatArray decodeSparseFloatArray(ByteArray data, int length) throws SQLException {
            return VectorData.toSparseFloatArray(decodeSparseDoubleArray(data, length));
        }

        @Override // oracle.jdbc.driver.VectorData.SparseDecoder, oracle.jdbc.driver.VectorData.Decoder
        protected VECTOR.SparseByteArray decodeSparseByteArray(ByteArray data, int length) throws SQLException {
            return VectorData.toSparseByteArray(decodeSparseDoubleArray(data, length));
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseInt8Decoder.class */
    private static final class SparseInt8Decoder extends SparseDecoder<byte[]> {
        SparseInt8Decoder(Int8Decoder dimensionsDecoder) {
            super(dimensionsDecoder);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Encoder.class */
    static abstract class Encoder<T> {
        protected static final ByteArray.NumberEncoding NUMBER_ENCODING = getNumberEncoding();
        private final byte version;
        private final byte dimensionType;

        abstract int getMaximumValuesByteLength(T t);

        abstract short getFlag1();

        abstract double computeNorm(T t);

        abstract void encodeValues(T t, ByteArray byteArray) throws SQLException;

        abstract int getDimensionCount(T t);

        private static ByteArray.NumberEncoding getNumberEncoding() {
            String configuredEncoding = System.getProperty("oracle.jdbc.vectorEncoding");
            if (configuredEncoding == null) {
                return ByteArray.NumberEncoding.ORACLE_BIG_ENDIAN;
            }
            try {
                return ByteArray.NumberEncoding.valueOf(configuredEncoding);
            } catch (IllegalArgumentException illegalArgumentException) {
                illegalArgumentException.printStackTrace();
                return ByteArray.NumberEncoding.ORACLE_BIG_ENDIAN;
            }
        }

        Encoder(byte version, byte dimensionType) {
            this.version = version;
            this.dimensionType = dimensionType;
        }

        final int getMaximumByteLength(T values) {
            return 17 + getMaximumValuesByteLength(values);
        }

        final void encode(T values, boolean isComputingNorm, ByteArray data) throws SQLException {
            boolean isComputingNorm2 = isComputingNorm & (this.dimensionType != 5);
            data.put((byte) -37);
            data.put(this.version);
            data.putShort(getFlag1());
            data.put(this.dimensionType);
            data.putInt(getDimensionCount(values));
            if (isComputingNorm2) {
                data.putDouble(computeNorm(values), NUMBER_ENCODING);
            } else {
                data.putLong(0L);
            }
            encodeValues(values, data);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$AbstractFloat64Encoder.class */
    private static abstract class AbstractFloat64Encoder<T> extends Encoder<T> {
        abstract ArrayConverter<T> getArrayConverter();

        AbstractFloat64Encoder() {
            super((byte) 0, (byte) 3);
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final int getMaximumValuesByteLength(T values) {
            return getDimensionCount(values) << 3;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final short getFlag1() {
            return (short) (18 | VectorData.getFlag1FormatBits(NUMBER_ENCODING));
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final double computeNorm(T values) {
            ArrayConverter<T> converter = getArrayConverter();
            int length = converter.getLength(values);
            double squareSum = 0.0d;
            for (int i = 0; i < length; i++) {
                double value = converter.getDouble(values, i);
                squareSum += value * value;
            }
            return Math.sqrt(squareSum);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Float64Encoder.class */
    private static final class Float64Encoder extends AbstractFloat64Encoder<double[]> {
        private static final Float64Encoder INSTANCE = new Float64Encoder();

        private Float64Encoder() {
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public int getDimensionCount(double[] values) {
            return values.length;
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public void encodeValues(double[] values, ByteArray data) throws SQLException {
            data.putDoubles(values, NUMBER_ENCODING);
        }

        @Override // oracle.jdbc.driver.VectorData.AbstractFloat64Encoder
        ArrayConverter<double[]> getArrayConverter() {
            return DoubleArrayConverter.INSTANCE;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Float64ConversionEncoder.class */
    private static final class Float64ConversionEncoder<T> extends AbstractFloat64Encoder<T> {
        private static final Float64ConversionEncoder<float[]> FLOAT_ENCODER = new Float64ConversionEncoder<>(FloatArrayConverter.INSTANCE);
        private static final Float64ConversionEncoder<long[]> LONG_ENCODER = new Float64ConversionEncoder<>(LongArrayConverter.INSTANCE);
        private static final Float64ConversionEncoder<int[]> INT_ENCODER = new Float64ConversionEncoder<>(IntArrayConverter.INSTANCE);
        private static final Float64ConversionEncoder<short[]> SHORT_ENCODER = new Float64ConversionEncoder<>(ShortArrayConverter.INSTANCE);
        private static final Float64ConversionEncoder<byte[]> BYTE_ENCODER = new Float64ConversionEncoder<>(ByteArrayConverter.INSTANCE);
        private static final Float64ConversionEncoder<boolean[]> BOOLEAN_ENCODER = new Float64ConversionEncoder<>(BooleanArrayConverter.INSTANCE);
        private final ArrayConverter<T> converter;

        Float64ConversionEncoder(ArrayConverter<T> converter) {
            this.converter = converter;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        int getDimensionCount(T values) {
            return this.converter.getLength(values);
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        void encodeValues(T values, ByteArray data) throws SQLException {
            int length = getDimensionCount(values);
            int bufferSize = Math.min(length, 2048);
            double[] buffer = new double[bufferSize];
            int valueIndex = 0;
            while (valueIndex < length) {
                int bufferIndex = 0;
                while (bufferIndex < buffer.length && valueIndex < length) {
                    int i = bufferIndex;
                    bufferIndex++;
                    int i2 = valueIndex;
                    valueIndex++;
                    buffer[i] = this.converter.getDouble(values, i2);
                }
                data.putDoubles(buffer, 0, bufferIndex, NUMBER_ENCODING);
            }
        }

        @Override // oracle.jdbc.driver.VectorData.AbstractFloat64Encoder
        ArrayConverter<T> getArrayConverter() {
            return this.converter;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$AbstractFloat32Encoder.class */
    private static abstract class AbstractFloat32Encoder<T> extends Encoder<T> {
        abstract ArrayConverter<T> getArrayConverter();

        AbstractFloat32Encoder() {
            super((byte) 0, (byte) 2);
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final int getMaximumValuesByteLength(T values) {
            return getDimensionCount(values) << 2;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final short getFlag1() {
            return (short) (18 | VectorData.getFlag1FormatBits(NUMBER_ENCODING));
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final double computeNorm(T values) {
            ArrayConverter<T> converter = getArrayConverter();
            int length = converter.getLength(values);
            double squareSum = 0.0d;
            for (int i = 0; i < length; i++) {
                double value = converter.getDouble(values, i);
                squareSum += value * value;
            }
            return Math.sqrt(squareSum);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Float32Encoder.class */
    private static final class Float32Encoder extends AbstractFloat32Encoder<float[]> {
        private static final Float32Encoder INSTANCE = new Float32Encoder();

        private Float32Encoder() {
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public int getDimensionCount(float[] values) {
            return values.length;
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public void encodeValues(float[] values, ByteArray data) throws SQLException {
            data.putFloats(values, NUMBER_ENCODING);
        }

        @Override // oracle.jdbc.driver.VectorData.AbstractFloat32Encoder
        ArrayConverter<float[]> getArrayConverter() {
            return FloatArrayConverter.INSTANCE;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Float32ConversionEncoder.class */
    static final class Float32ConversionEncoder<T> extends AbstractFloat32Encoder<T> {
        private static final Float32ConversionEncoder<double[]> DOUBLE_ENCODER = new Float32ConversionEncoder<>(DoubleArrayConverter.INSTANCE);
        private static final Float32ConversionEncoder<long[]> LONG_ENCODER = new Float32ConversionEncoder<>(LongArrayConverter.INSTANCE);
        private static final Float32ConversionEncoder<int[]> INT_ENCODER = new Float32ConversionEncoder<>(IntArrayConverter.INSTANCE);
        private static final Float32ConversionEncoder<short[]> SHORT_ENCODER = new Float32ConversionEncoder<>(ShortArrayConverter.INSTANCE);
        private static final Float32ConversionEncoder<byte[]> BYTE_ENCODER = new Float32ConversionEncoder<>(ByteArrayConverter.INSTANCE);
        private static final Float32ConversionEncoder<boolean[]> BOOLEAN_ENCODER = new Float32ConversionEncoder<>(BooleanArrayConverter.INSTANCE);
        private final ArrayConverter<T> converter;

        Float32ConversionEncoder(ArrayConverter<T> converter) {
            this.converter = converter;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        int getDimensionCount(T values) {
            return this.converter.getLength(values);
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        void encodeValues(T values, ByteArray data) throws SQLException {
            int length = getDimensionCount(values);
            int bufferSize = Math.min(length, 4096);
            float[] buffer = new float[bufferSize];
            int valueIndex = 0;
            while (valueIndex < length) {
                int bufferIndex = 0;
                while (bufferIndex < buffer.length && valueIndex < length) {
                    int i = bufferIndex;
                    bufferIndex++;
                    int i2 = valueIndex;
                    valueIndex++;
                    buffer[i] = this.converter.getFloat(values, i2);
                }
                data.putFloats(buffer, 0, bufferIndex, NUMBER_ENCODING);
            }
        }

        @Override // oracle.jdbc.driver.VectorData.AbstractFloat32Encoder
        ArrayConverter<T> getArrayConverter() {
            return this.converter;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$AbstractInt8Encoder.class */
    private static abstract class AbstractInt8Encoder<T> extends Encoder<T> {
        AbstractInt8Encoder() {
            super((byte) 0, (byte) 4);
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final int getMaximumValuesByteLength(T values) {
            return getDimensionCount(values);
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final short getFlag1() {
            return (short) 18;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Int8Encoder.class */
    private static final class Int8Encoder extends AbstractInt8Encoder<byte[]> {
        private static final Int8Encoder INSTANCE = new Int8Encoder();

        private Int8Encoder() {
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public int getDimensionCount(byte[] values) {
            return values.length;
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public void encodeValues(byte[] values, ByteArray data) throws SQLException {
            data.put(values);
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public double computeNorm(byte[] values) {
            long squareSum = 0;
            for (byte value : values) {
                squareSum += value * value;
            }
            return Math.sqrt(squareSum);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$Int8ConversionEncoder.class */
    static final class Int8ConversionEncoder<T> extends AbstractInt8Encoder<T> {
        private static final Int8ConversionEncoder<double[]> DOUBLE_ENCODER = new Int8ConversionEncoder<>(DoubleArrayConverter.INSTANCE);
        private static final Int8ConversionEncoder<float[]> FLOAT_ENCODER = new Int8ConversionEncoder<>(FloatArrayConverter.INSTANCE);
        private static final Int8ConversionEncoder<long[]> LONG_ENCODER = new Int8ConversionEncoder<>(LongArrayConverter.INSTANCE);
        private static final Int8ConversionEncoder<int[]> INT_ENCODER = new Int8ConversionEncoder<>(IntArrayConverter.INSTANCE);
        private static final Int8ConversionEncoder<short[]> SHORT_ENCODER = new Int8ConversionEncoder<>(ShortArrayConverter.INSTANCE);
        private static final Int8ConversionEncoder<boolean[]> BOOLEAN_ENCODER = new Int8ConversionEncoder<>(BooleanArrayConverter.INSTANCE);
        private final ArrayConverter<T> converter;

        Int8ConversionEncoder(ArrayConverter<T> converter) {
            this.converter = converter;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        void encodeValues(T values, ByteArray data) throws SQLException {
            int length = getDimensionCount(values);
            for (int i = 0; i < length; i++) {
                data.put(this.converter.getByte(values, i));
            }
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        double computeNorm(T values) {
            int length = getDimensionCount(values);
            long squareSum = 0;
            for (int i = 0; i < length; i++) {
                byte value = this.converter.getByte(values, i);
                squareSum += value * value;
            }
            return Math.sqrt(squareSum);
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        int getDimensionCount(T values) {
            return this.converter.getLength(values);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$AbstractBinaryEncoder.class */
    private static abstract class AbstractBinaryEncoder<T> extends Encoder<T> {
        AbstractBinaryEncoder() {
            super((byte) 1, (byte) 5);
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final short getFlag1() {
            return (short) 16;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final double computeNorm(T values) {
            return 0.0d;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final int getMaximumValuesByteLength(T values) {
            return (getDimensionCount(values) + 7) >> 3;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$BinaryEncoder.class */
    private static final class BinaryEncoder extends AbstractBinaryEncoder<byte[]> {
        private static final BinaryEncoder INSTANCE = new BinaryEncoder();

        private BinaryEncoder() {
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public void encodeValues(byte[] values, ByteArray data) throws SQLException {
            data.put(values);
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public int getDimensionCount(byte[] values) {
            return values.length << 3;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$BinaryConversionEncoder.class */
    static final class BinaryConversionEncoder<T> extends AbstractBinaryEncoder<T> {
        private static final BinaryConversionEncoder<boolean[]> BOOLEAN_ENCODER = new BinaryConversionEncoder<>(BooleanArrayConverter.INSTANCE);
        private final ArrayConverter<T> converter;

        BinaryConversionEncoder(ArrayConverter<T> converter) {
            this.converter = converter;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        void encodeValues(T values, ByteArray data) throws SQLException {
            int bufferIndex;
            int length = getDimensionCount(values);
            int byteLength = length >> 3;
            int bufferSize = Math.min(byteLength, 16384);
            byte[] buffer = new byte[bufferSize];
            int bitIndex = 0;
            for (int byteIndex = 0; byteIndex < byteLength; byteIndex += bufferIndex) {
                bufferIndex = 0;
                while (bufferIndex < buffer.length && bitIndex < length) {
                    int bits = 0;
                    int mask = 128;
                    for (int b = 0; b < 8; b++) {
                        int i = bitIndex;
                        bitIndex++;
                        if (this.converter.getBoolean(values, i)) {
                            bits |= mask;
                        }
                        mask >>= 1;
                    }
                    int i2 = bufferIndex;
                    bufferIndex++;
                    buffer[i2] = (byte) bits;
                }
                data.put(buffer);
            }
            if (bitIndex < length) {
                int bits2 = 0;
                int mask2 = 128;
                do {
                    int i3 = bitIndex;
                    bitIndex++;
                    if (this.converter.getBoolean(values, i3)) {
                        bits2 |= mask2;
                    }
                    mask2 >>= 1;
                } while (bitIndex < length);
                data.put((byte) bits2);
            }
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        int getDimensionCount(T values) {
            return this.converter.getLength(values);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseEncoder.class */
    private static class SparseEncoder<T extends VECTOR.SparseArray, U> extends Encoder<T> {
        private static final SparseEncoder<VECTOR.SparseDoubleArray, double[]> DOUBLE_TO_FLOAT64 = new SparseEncoder<>((v0) -> {
            return v0.values();
        }, Float64Encoder.INSTANCE);
        private static final SparseEncoder<VECTOR.SparseFloatArray, float[]> FLOAT_TO_FLOAT64 = new SparseEncoder<>((v0) -> {
            return v0.values();
        }, Float64ConversionEncoder.FLOAT_ENCODER);
        private static final SparseEncoder<VECTOR.SparseByteArray, byte[]> BYTE_TO_FLOAT64 = new SparseEncoder<>((v0) -> {
            return v0.values();
        }, Float64ConversionEncoder.BYTE_ENCODER);
        private static final SparseEncoder<VECTOR.SparseFloatArray, float[]> FLOAT_TO_FLOAT32 = new SparseEncoder<>((v0) -> {
            return v0.values();
        }, Float32Encoder.INSTANCE);
        private static final SparseEncoder<VECTOR.SparseByteArray, byte[]> BYTE_TO_FLOAT32 = new SparseEncoder<>((v0) -> {
            return v0.values();
        }, Float32ConversionEncoder.BYTE_ENCODER);
        private static final SparseEncoder<VECTOR.SparseByteArray, byte[]> BYTE_TO_INT8 = new SparseEncoder<>((v0) -> {
            return v0.values();
        }, Int8Encoder.INSTANCE);
        private final Function<T, U> getValuesFunction;
        protected final Encoder<U> dimensionsEncoder;

        private SparseEncoder(Function<T, U> getValuesFunction, Encoder<U> dimensionsEncoder) {
            super((byte) 2, ((Encoder) dimensionsEncoder).dimensionType);
            this.getValuesFunction = getValuesFunction;
            this.dimensionsEncoder = dimensionsEncoder;
        }

        @Override // oracle.jdbc.driver.VectorData.Encoder
        final short getFlag1() {
            short dimensionsFlag1 = this.dimensionsEncoder.getFlag1();
            return (short) (dimensionsFlag1 | 32);
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public final int getDimensionCount(T sparseArray) {
            return sparseArray.length();
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public final int getMaximumValuesByteLength(T sparseArray) {
            U values = this.getValuesFunction.apply(sparseArray);
            return 2 + (sparseArray.indices().length << 2) + this.dimensionsEncoder.getMaximumValuesByteLength(values);
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public final double computeNorm(T sparseArray) {
            U values = this.getValuesFunction.apply(sparseArray);
            return this.dimensionsEncoder.computeNorm(values);
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // oracle.jdbc.driver.VectorData.Encoder
        public void encodeValues(T sparseArray, ByteArray dataBuffer) throws SQLException {
            int[] indices = sparseArray.indices();
            dataBuffer.putShort((short) indices.length);
            dataBuffer.putInts(indices);
            U values = this.getValuesFunction.apply(sparseArray);
            this.dimensionsEncoder.encodeValues(values, dataBuffer);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$NarrowingSparseEncoder.class */
    private static final class NarrowingSparseEncoder<T extends VECTOR.SparseArray, U, R extends VECTOR.SparseArray> extends SparseEncoder<T, U> {
        private static final NarrowingSparseEncoder<VECTOR.SparseDoubleArray, double[], VECTOR.SparseFloatArray> DOUBLE_TO_FLOAT32 = new NarrowingSparseEncoder<>((v0) -> {
            return v0.values();
        }, Float32ConversionEncoder.DOUBLE_ENCODER, x$0 -> {
            return VectorData.toSparseFloatArray(x$0);
        }, SparseEncoder.FLOAT_TO_FLOAT32);
        private static final NarrowingSparseEncoder<VECTOR.SparseDoubleArray, double[], VECTOR.SparseByteArray> DOUBLE_TO_INT8 = new NarrowingSparseEncoder<>((v0) -> {
            return v0.values();
        }, Int8ConversionEncoder.DOUBLE_ENCODER, x$0 -> {
            return VectorData.toSparseByteArray(x$0);
        }, SparseEncoder.BYTE_TO_INT8);
        private static final NarrowingSparseEncoder<VECTOR.SparseFloatArray, float[], VECTOR.SparseByteArray> FLOAT_TO_INT8 = new NarrowingSparseEncoder<>((v0) -> {
            return v0.values();
        }, Int8ConversionEncoder.FLOAT_ENCODER, x$0 -> {
            return VectorData.toSparseByteArray(x$0);
        }, SparseEncoder.BYTE_TO_INT8);
        private final Function<T, R> narrowingConversion;
        private final SparseEncoder<R, ?> narrowedSparseArrayEncoder;

        private NarrowingSparseEncoder(Function<T, U> getWideValuesFunction, Encoder<U> wideDimensionsEncoder, Function<T, R> conversionFunction, SparseEncoder<R, ?> narrowedSparseArrayEncoder) {
            super(getWideValuesFunction, wideDimensionsEncoder);
            this.narrowingConversion = conversionFunction;
            this.narrowedSparseArrayEncoder = narrowedSparseArrayEncoder;
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // oracle.jdbc.driver.VectorData.SparseEncoder, oracle.jdbc.driver.VectorData.Encoder
        public void encodeValues(T sparseArray, ByteArray dataBuffer) throws SQLException {
            this.narrowedSparseArrayEncoder.encodeValues((SparseEncoder<R, ?>) this.narrowingConversion.apply(sparseArray), dataBuffer);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$ArrayConverter.class */
    private interface ArrayConverter<T> {
        int getLength(T t);

        double getDouble(T t, int i);

        float getFloat(T t, int i);

        byte getByte(T t, int i);

        boolean getBoolean(T t, int i);

        void swap(T t, int i, int i2);

        T copyOf(T t, int i);

        default T toDenseArray(int length, int[] indices, T values) {
            T denseArray = copyOf(values, length);
            for (int i = indices.length - 1; i >= 0; i--) {
                swap(denseArray, i, indices[i]);
            }
            return denseArray;
        }

        /* JADX WARN: Multi-variable type inference failed */
        default double[] toDoubleArray(T t) {
            if (t instanceof double[]) {
                return (double[]) t;
            }
            double[] doubleArray = new double[getLength(t)];
            for (int i = 0; i < doubleArray.length; i++) {
                doubleArray[i] = getDouble(t, i);
            }
            return doubleArray;
        }

        /* JADX WARN: Multi-variable type inference failed */
        default float[] toFloatArray(T t) {
            if (t instanceof float[]) {
                return (float[]) t;
            }
            float[] floatArray = new float[getLength(t)];
            for (int i = 0; i < floatArray.length; i++) {
                floatArray[i] = getFloat(t, i);
            }
            return floatArray;
        }

        /* JADX WARN: Multi-variable type inference failed */
        default byte[] toByteArray(T t) {
            if (t instanceof byte[]) {
                return (byte[]) t;
            }
            byte[] byteArray = new byte[getLength(t)];
            for (int i = 0; i < byteArray.length; i++) {
                byteArray[i] = getByte(t, i);
            }
            return byteArray;
        }

        default int[] compactNonZeroValues(T array) {
            int denseLength = getLength(array);
            int[] indices = new int[denseLength];
            int sparseLength = 0;
            for (int i = 0; i < denseLength; i++) {
                if (getBoolean(array, i)) {
                    indices[sparseLength] = i;
                    swap(array, i, sparseLength);
                    sparseLength++;
                }
            }
            return Arrays.copyOf(indices, sparseLength);
        }

        default VECTOR.SparseDoubleArray toSparseDoubleArray(T denseArray) {
            int[] indices = compactNonZeroValues(denseArray);
            double[] values = toDoubleArray(copyOf(denseArray, indices.length));
            return new SparseDoubleArrayImpl(getLength(denseArray), indices, values);
        }

        default VECTOR.SparseFloatArray toSparseFloatArray(T denseArray) {
            int length = getLength(denseArray);
            float[] values = toFloatArray(denseArray);
            int[] indices = FloatArrayConverter.INSTANCE.compactNonZeroValues(values);
            return new SparseFloatArrayImpl(length, indices, FloatArrayConverter.INSTANCE.copyOf(values, indices.length));
        }

        default VECTOR.SparseByteArray toSparseByteArray(T denseArray) {
            int length = getLength(denseArray);
            byte[] values = toByteArray(denseArray);
            int[] indices = ByteArrayConverter.INSTANCE.compactNonZeroValues(values);
            return new SparseByteArrayImpl(length, indices, ByteArrayConverter.INSTANCE.copyOf(values, indices.length));
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$DoubleArrayConverter.class */
    private static final class DoubleArrayConverter implements ArrayConverter<double[]> {
        private static final DoubleArrayConverter INSTANCE = new DoubleArrayConverter();

        private DoubleArrayConverter() {
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public int getLength(double[] array) {
            return array.length;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public double getDouble(double[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public float getFloat(double[] array, int index) {
            return (float) array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public byte getByte(double[] array, int index) {
            return (byte) array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public boolean getBoolean(double[] array, int index) {
            return array[index] != 0.0d;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public void swap(double[] array, int from, int to) {
            double toValue = array[to];
            array[to] = array[from];
            array[from] = toValue;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public double[] copyOf(double[] array, int length) {
            return Arrays.copyOf(array, length);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$FloatArrayConverter.class */
    private static final class FloatArrayConverter implements ArrayConverter<float[]> {
        private static final FloatArrayConverter INSTANCE = new FloatArrayConverter();

        private FloatArrayConverter() {
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public int getLength(float[] array) {
            return array.length;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public double getDouble(float[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public float getFloat(float[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public byte getByte(float[] array, int index) {
            return (byte) array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public boolean getBoolean(float[] array, int index) {
            return array[index] != 0.0f;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public void swap(float[] array, int from, int to) {
            float toValue = array[to];
            array[to] = array[from];
            array[from] = toValue;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public float[] copyOf(float[] array, int length) {
            return Arrays.copyOf(array, length);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$LongArrayConverter.class */
    private static final class LongArrayConverter implements ArrayConverter<long[]> {
        private static final LongArrayConverter INSTANCE = new LongArrayConverter();

        private LongArrayConverter() {
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public int getLength(long[] array) {
            return array.length;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public double getDouble(long[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public float getFloat(long[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public byte getByte(long[] array, int index) {
            return (byte) array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public boolean getBoolean(long[] array, int index) {
            return array[index] != 0;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public void swap(long[] array, int from, int to) {
            long toValue = array[to];
            array[to] = array[from];
            array[from] = toValue;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public long[] copyOf(long[] array, int length) {
            return Arrays.copyOf(array, length);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$IntArrayConverter.class */
    private static final class IntArrayConverter implements ArrayConverter<int[]> {
        private static final IntArrayConverter INSTANCE = new IntArrayConverter();

        private IntArrayConverter() {
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public int getLength(int[] array) {
            return array.length;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public double getDouble(int[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public float getFloat(int[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public byte getByte(int[] array, int index) {
            return (byte) array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public boolean getBoolean(int[] array, int index) {
            return array[index] != 0;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public void swap(int[] array, int from, int to) {
            int toValue = array[to];
            array[to] = array[from];
            array[from] = toValue;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public int[] copyOf(int[] array, int length) {
            return Arrays.copyOf(array, length);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$ShortArrayConverter.class */
    private static final class ShortArrayConverter implements ArrayConverter<short[]> {
        private static final ShortArrayConverter INSTANCE = new ShortArrayConverter();

        private ShortArrayConverter() {
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public int getLength(short[] array) {
            return array.length;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public double getDouble(short[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public float getFloat(short[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public byte getByte(short[] array, int index) {
            return (byte) array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public boolean getBoolean(short[] array, int index) {
            return array[index] != 0;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public void swap(short[] array, int from, int to) {
            short toValue = array[to];
            array[to] = array[from];
            array[from] = toValue;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public short[] copyOf(short[] array, int length) {
            return Arrays.copyOf(array, length);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$ByteArrayConverter.class */
    private static final class ByteArrayConverter implements ArrayConverter<byte[]> {
        private static final ByteArrayConverter INSTANCE = new ByteArrayConverter();

        private ByteArrayConverter() {
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public int getLength(byte[] array) {
            return array.length;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public double getDouble(byte[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public float getFloat(byte[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public byte getByte(byte[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public boolean getBoolean(byte[] array, int index) {
            return array[index] != 0;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public void swap(byte[] array, int from, int to) {
            byte toValue = array[to];
            array[to] = array[from];
            array[from] = toValue;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public byte[] copyOf(byte[] array, int length) {
            return Arrays.copyOf(array, length);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$BooleanArrayConverter.class */
    private static final class BooleanArrayConverter implements ArrayConverter<boolean[]> {
        private static final BooleanArrayConverter INSTANCE = new BooleanArrayConverter();

        private BooleanArrayConverter() {
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public int getLength(boolean[] array) {
            return array.length;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public double getDouble(boolean[] array, int index) {
            return array[index] ? 1.0d : 0.0d;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public float getFloat(boolean[] array, int index) {
            return array[index] ? 1.0f : 0.0f;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public byte getByte(boolean[] array, int index) {
            return array[index] ? (byte) 1 : (byte) 0;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public boolean getBoolean(boolean[] array, int index) {
            return array[index];
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public void swap(boolean[] array, int from, int to) {
            boolean toValue = array[to];
            array[to] = array[from];
            array[from] = toValue;
        }

        @Override // oracle.jdbc.driver.VectorData.ArrayConverter
        public boolean[] copyOf(boolean[] array, int length) {
            return Arrays.copyOf(array, length);
        }
    }

    public static VECTOR.SparseDoubleArray createSparseDoubleArray(int length, int[] indices, double[] values) {
        DoubleArrayConverter doubleArrayConverter = DoubleArrayConverter.INSTANCE;
        doubleArrayConverter.getClass();
        validateSparseArray(length, indices, values, doubleArrayConverter::getLength);
        return new SparseDoubleArrayImpl(length, indices, values);
    }

    public static VECTOR.SparseFloatArray createSparseFloatArray(int length, int[] indices, float[] values) {
        FloatArrayConverter floatArrayConverter = FloatArrayConverter.INSTANCE;
        floatArrayConverter.getClass();
        validateSparseArray(length, indices, values, floatArrayConverter::getLength);
        return new SparseFloatArrayImpl(length, indices, values);
    }

    public static VECTOR.SparseByteArray createSparseByteArray(int length, int[] indices, byte[] values) {
        ByteArrayConverter byteArrayConverter = ByteArrayConverter.INSTANCE;
        byteArrayConverter.getClass();
        validateSparseArray(length, indices, values, byteArrayConverter::getLength);
        return new SparseByteArrayImpl(length, indices, values);
    }

    public static VECTOR.SparseDoubleArray createSparseDoubleArray(double[] denseArray) {
        Objects.requireNonNull(denseArray, "denseArray is null");
        return DoubleArrayConverter.INSTANCE.toSparseDoubleArray(denseArray.clone());
    }

    public static VECTOR.SparseFloatArray createSparseFloatArray(float[] denseArray) {
        Objects.requireNonNull(denseArray, "denseArray is null");
        return FloatArrayConverter.INSTANCE.toSparseFloatArray(denseArray.clone());
    }

    public static VECTOR.SparseByteArray createSparseByteArray(byte[] denseArray) {
        Objects.requireNonNull(denseArray, "denseArray is null");
        return ByteArrayConverter.INSTANCE.toSparseByteArray(denseArray.clone());
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static VECTOR.SparseFloatArray toSparseFloatArray(VECTOR.SparseDoubleArray sparseDoubleArray) {
        return toSparseFloatArray(sparseDoubleArray, (v0) -> {
            return v0.values();
        }, DoubleArrayConverter.INSTANCE);
    }

    private static <T extends VECTOR.SparseArray, U> VECTOR.SparseFloatArray toSparseFloatArray(T sparseArray, Function<T, U> getValuesFunction, ArrayConverter<U> converter) {
        int[] indices = sparseArray.indices();
        float[] values = converter.toFloatArray(getValuesFunction.apply(sparseArray));
        int sparseLength = 0;
        for (int i = 0; i < values.length; i++) {
            float value = values[i];
            if (0.0f != value) {
                if (i != sparseLength) {
                    values[sparseLength] = value;
                    indices[sparseLength] = indices[i];
                }
                sparseLength++;
            }
        }
        if (sparseLength != indices.length) {
            indices = Arrays.copyOf(indices, sparseLength);
            values = Arrays.copyOf(values, sparseLength);
        }
        return new SparseFloatArrayImpl(sparseArray.length(), indices, values);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static VECTOR.SparseByteArray toSparseByteArray(VECTOR.SparseDoubleArray sparseDoubleArray) {
        return toSparseByteArray(sparseDoubleArray, (v0) -> {
            return v0.values();
        }, DoubleArrayConverter.INSTANCE);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static VECTOR.SparseByteArray toSparseByteArray(VECTOR.SparseFloatArray sparseFloatArray) {
        return toSparseByteArray(sparseFloatArray, (v0) -> {
            return v0.values();
        }, FloatArrayConverter.INSTANCE);
    }

    private static <T extends VECTOR.SparseArray, U> VECTOR.SparseByteArray toSparseByteArray(T sparseArray, Function<T, U> getValuesFunction, ArrayConverter<U> converter) {
        int[] indices = sparseArray.indices();
        byte[] values = converter.toByteArray(getValuesFunction.apply(sparseArray));
        int sparseLength = 0;
        for (int i = 0; i < values.length; i++) {
            byte value = values[i];
            if (0 != value) {
                if (i != sparseLength) {
                    values[sparseLength] = value;
                    indices[sparseLength] = indices[i];
                }
                sparseLength++;
            }
        }
        if (sparseLength != indices.length) {
            indices = Arrays.copyOf(indices, sparseLength);
            values = Arrays.copyOf(values, sparseLength);
        }
        return new SparseByteArrayImpl(sparseArray.length(), indices, values);
    }

    private static <T> void validateSparseArray(int length, int[] indices, T values, ToIntFunction<T> valuesLengthFunction) {
        if (length < 0) {
            throw new IllegalArgumentException("length " + length + " is less than 0");
        }
        Objects.requireNonNull(indices, "indices is null");
        if (indices.length > 65535) {
            throw new IllegalArgumentException("indices length " + indices.length + " is greater than 65535");
        }
        Objects.requireNonNull(values, "values is null");
        int valuesLength = valuesLengthFunction.applyAsInt(values);
        if (valuesLength != indices.length) {
            throw new IllegalArgumentException("Values length " + valuesLength + " is not equal to indices length " + indices.length);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseArrayImpl.class */
    private static abstract class SparseArrayImpl implements VECTOR.SparseArray {
        private final int length;
        private final int[] indices;

        /* JADX INFO: Access modifiers changed from: package-private */
        public abstract Object values();

        private SparseArrayImpl(int length, int[] indices) {
            this.length = length;
            this.indices = indices;
        }

        @Override // oracle.sql.VECTOR.SparseArray
        public int length() {
            return this.length;
        }

        @Override // oracle.sql.VECTOR.SparseArray
        public int[] indices() {
            return this.indices;
        }

        @Override // oracle.sql.VECTOR.SparseArray
        public String toString() {
            try {
                byte[] encoding = VectorData.encode(this, OracleType.VECTOR, false);
                return (String) VectorData.decode(encoding, String.class, true);
            } catch (SQLException sqlException) {
                return "INVALID SPARSE ARRAY: " + sqlException.getMessage();
            }
        }

        public int hashCode() {
            return Arrays.deepHashCode(new Object[]{Integer.valueOf(this.length), this.indices, values()});
        }

        public boolean equals(Object object) {
            Object values;
            if (!(object instanceof VECTOR.SparseArray)) {
                return false;
            }
            VECTOR.SparseArray sparseArray = (VECTOR.SparseArray) object;
            if (this.length != sparseArray.length() || !Arrays.equals(this.indices, sparseArray.indices())) {
                return false;
            }
            if (sparseArray instanceof SparseArrayImpl) {
                values = ((SparseArrayImpl) sparseArray).values();
            } else if (sparseArray instanceof VECTOR.SparseDoubleArray) {
                values = ((VECTOR.SparseDoubleArray) sparseArray).values();
            } else if (sparseArray instanceof VECTOR.SparseFloatArray) {
                values = ((VECTOR.SparseFloatArray) sparseArray).values();
            } else if (sparseArray instanceof VECTOR.SparseByteArray) {
                values = ((VECTOR.SparseByteArray) sparseArray).values();
            } else {
                return false;
            }
            return Objects.deepEquals(values(), values);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseDoubleArrayImpl.class */
    private static final class SparseDoubleArrayImpl extends SparseArrayImpl implements VECTOR.SparseDoubleArray {
        private final double[] values;

        private SparseDoubleArrayImpl(int length, int[] indices, double[] values) {
            super(length, indices);
            this.values = values;
        }

        @Override // oracle.jdbc.driver.VectorData.SparseArrayImpl, oracle.sql.VECTOR.SparseByteArray
        public double[] values() {
            return this.values;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseFloatArrayImpl.class */
    private static final class SparseFloatArrayImpl extends SparseArrayImpl implements VECTOR.SparseFloatArray {
        private final float[] values;

        private SparseFloatArrayImpl(int length, int[] indices, float[] values) {
            super(length, indices);
            this.values = values;
        }

        @Override // oracle.jdbc.driver.VectorData.SparseArrayImpl, oracle.sql.VECTOR.SparseByteArray
        public float[] values() {
            return this.values;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorData$SparseByteArrayImpl.class */
    private static final class SparseByteArrayImpl extends SparseArrayImpl implements VECTOR.SparseByteArray {
        private final byte[] values;

        private SparseByteArrayImpl(int length, int[] indices, byte[] values) {
            super(length, indices);
            this.values = values;
        }

        @Override // oracle.jdbc.driver.VectorData.SparseArrayImpl, oracle.sql.VECTOR.SparseByteArray
        public byte[] values() {
            return this.values;
        }
    }
}
