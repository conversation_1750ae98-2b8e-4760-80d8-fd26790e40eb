package oracle.jdbc.driver.resource;

import java.sql.DriverPropertyInfo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.spi.OracleResourceProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ProviderProperties.class */
public final class ProviderProperties {
    private final Map<String, String> propertyValues;

    private ProviderProperties(Map<String, String> propertyValues) {
        this.propertyValues = Collections.unmodifiableMap(new HashMap(propertyValues));
    }

    public static ProviderProperties create(Hashtable<?, ?>... connectionProperties) {
        Map<String, String> propertyValues = new HashMap<>();
        for (Hashtable<?, ?> properties : connectionProperties) {
            if (properties != null) {
                for (Map.Entry<?, ?> entry : properties.entrySet()) {
                    Object key = entry.getKey();
                    Object value = entry.getValue();
                    if ((key instanceof String) && (value instanceof String) && isProviderProperty((String) key)) {
                        propertyValues.putIfAbsent((String) key, (String) value);
                    }
                }
            }
        }
        return new ProviderProperties(propertyValues);
    }

    String getProviderName(ResourceType<?, ?> resourceType) {
        return this.propertyValues.getOrDefault(resourceType.getProviderNameProperty(), resourceType.getDefaultProviderName());
    }

    Map<String, String> getParameterValues(ResourceType<?, ?> resource) {
        String nameSpacePrefix = resource.getProviderNameProperty() + '.';
        Map<String, String> parameterProperties = new HashMap<>();
        for (Map.Entry<String, String> propertyValue : this.propertyValues.entrySet()) {
            String propertyName = propertyValue.getKey();
            if (propertyName.startsWith(nameSpacePrefix)) {
                String parameterName = propertyName.substring(nameSpacePrefix.length());
                parameterProperties.put(parameterName, propertyValue.getValue());
            }
        }
        return parameterProperties;
    }

    public Collection<DriverPropertyInfo> generatePropertyInfo() {
        Collection<ResourceType<?, ?>> resourceTypes = ResourceType.allTypes();
        ArrayList<DriverPropertyInfo> info = new ArrayList<>(resourceTypes.size());
        for (ResourceType<?, ?> resourceType : resourceTypes) {
            InstalledProviders<?> installedProviders = InstalledProviders.load(resourceType);
            DriverPropertyInfo nameInfo = generateProviderNamePropertyInfo(resourceType, installedProviders);
            info.add(nameInfo);
            Collection<DriverPropertyInfo> parameterInfo = generateParameterPropertyInfo(resourceType, installedProviders);
            info.addAll(parameterInfo);
        }
        return info;
    }

    private DriverPropertyInfo generateProviderNamePropertyInfo(ResourceType<?, ?> resourceType, InstalledProviders<?> installedProviders) {
        DriverPropertyInfo info = new DriverPropertyInfo(resourceType.getProviderNameProperty(), getProviderName(resourceType));
        info.choices = (String[]) installedProviders.getNames().toArray(new String[0]);
        return info;
    }

    private Collection<DriverPropertyInfo> generateParameterPropertyInfo(ResourceType<?, ?> resourceType, InstalledProviders<?> installedProviders) {
        String providerName = getProviderName(resourceType);
        if (providerName == null) {
            return Collections.emptyList();
        }
        OracleResourceProvider provider = installedProviders.get(providerName);
        if (provider == null) {
            return Collections.emptyList();
        }
        Collection<? extends OracleResourceProvider.Parameter> parameters = provider.getParameters();
        if (parameters == null) {
            return Collections.emptyList();
        }
        ArrayList<DriverPropertyInfo> info = new ArrayList<>(parameters.size());
        for (OracleResourceProvider.Parameter parameter : parameters) {
            if (parameter != null) {
                String propertyName = resourceType.getProviderNameProperty() + OracleConnection.CLIENT_INFO_KEY_SEPARATOR + parameter.name();
                CharSequence defaultValue = parameter.isSensitive() ? null : parameter.defaultValue();
                String propertyValue = this.propertyValues.getOrDefault(propertyName, defaultValue == null ? null : defaultValue.toString());
                DriverPropertyInfo parameterInfo = new DriverPropertyInfo(propertyName, propertyValue);
                parameterInfo.required = parameter.isRequired();
                parameterInfo.description = parameter.description();
                if ("".equals(parameterInfo.description)) {
                    parameterInfo.description = null;
                }
                info.add(parameterInfo);
            }
        }
        return info;
    }

    public static boolean isProviderProperty(String name) {
        if (name == null) {
            return false;
        }
        for (ResourceType<?, ?> resourceType : ResourceType.allTypes()) {
            String propertyName = resourceType.getProviderNameProperty();
            if (name.startsWith(propertyName)) {
                if (propertyName.length() == name.length()) {
                    return true;
                }
                if (name.length() >= propertyName.length() + 2 && name.charAt(propertyName.length()) == '.' && name.charAt(propertyName.length() + 1) != '.') {
                    return true;
                }
            }
        }
        return false;
    }
}
