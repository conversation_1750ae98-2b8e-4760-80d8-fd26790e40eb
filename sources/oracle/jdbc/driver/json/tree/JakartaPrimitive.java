package oracle.jdbc.driver.json.tree;

import jakarta.json.JsonArray;
import jakarta.json.JsonNumber;
import jakarta.json.JsonString;
import jakarta.json.JsonValue;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Wrapper;
import oracle.jdbc.driver.json.JakartaExceptionFactory;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.binary.OsonPrimitiveConversions;
import oracle.sql.VECTOR;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive.class */
public final class JakartaPrimitive {

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaNumberImpl.class */
    public static class JakartaNumberImpl extends DefaultJsonNumber {
        byte[] raw;
        OracleJsonDecimal.TargetType type;

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public /* bridge */ /* synthetic */ String getString() {
            return super.getString();
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ boolean isWrapperFor(Class cls) throws SQLException {
            return super.isWrapperFor(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ Object unwrap(Class cls) throws SQLException {
            return super.unwrap(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber
        public /* bridge */ /* synthetic */ JsonValue.ValueType getValueType() {
            return super.getValueType();
        }

        public JakartaNumberImpl(byte[] raw, OracleJsonDecimal.TargetType type) {
            super();
            this.raw = raw;
            this.type = type;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public final BigDecimal bigDecimalValue() {
            return OsonPrimitiveConversions.toBigDecimal(this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonDecimalImpl(this.raw, this.type);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaFloatImpl.class */
    public static class JakartaFloatImpl extends DefaultJsonNumber {
        private float value;

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public /* bridge */ /* synthetic */ String getString() {
            return super.getString();
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ boolean isWrapperFor(Class cls) throws SQLException {
            return super.isWrapperFor(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ Object unwrap(Class cls) throws SQLException {
            return super.unwrap(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber
        public /* bridge */ /* synthetic */ JsonValue.ValueType getValueType() {
            return super.getValueType();
        }

        public JakartaFloatImpl(float value) {
            super();
            this.value = value;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public BigDecimal bigDecimalValue() {
            return BigDecimal.valueOf(this.value);
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public float floatValue() {
            return this.value;
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonFloatImpl(this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaDoubleImpl.class */
    public static class JakartaDoubleImpl extends DefaultJsonNumber {
        double value;

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public /* bridge */ /* synthetic */ String getString() {
            return super.getString();
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ boolean isWrapperFor(Class cls) throws SQLException {
            return super.isWrapperFor(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ Object unwrap(Class cls) throws SQLException {
            return super.unwrap(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber
        public /* bridge */ /* synthetic */ JsonValue.ValueType getValueType() {
            return super.getValueType();
        }

        public JakartaDoubleImpl(double value) {
            super();
            this.value = value;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public double doubleValue() {
            return this.value;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public BigDecimal bigDecimalValue() {
            return BigDecimal.valueOf(this.value);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonDoubleImpl(this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaStringImpl.class */
    public static class JakartaStringImpl implements DefaultJsonString {
        String value;

        public JakartaStringImpl(String value) {
            this.value = value;
        }

        public boolean equals(Object other) {
            return JakartaPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String getString() {
            return this.value;
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonStringImpl(this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaBinaryImpl.class */
    public static class JakartaBinaryImpl implements DefaultJsonString {
        byte[] bytes;
        boolean isId;

        public JakartaBinaryImpl(byte[] bytes, boolean isId) {
            this.bytes = bytes;
            this.isId = isId;
        }

        public boolean equals(Object other) {
            return JakartaPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OracleJsonBinaryImpl.getString(this.bytes, this.isId);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonBinaryImpl(this.bytes, this.isId);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaDateImpl.class */
    public static class JakartaDateImpl implements DefaultJsonString {
        byte[] raw;

        public JakartaDateImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JakartaPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.dateToString(JakartaExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonDateImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaTimestampImpl.class */
    public static class JakartaTimestampImpl implements DefaultJsonString {
        private byte[] raw;

        public JakartaTimestampImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JakartaPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.timestampToString(JakartaExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonTimestampImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaTimestampTZImpl.class */
    public static class JakartaTimestampTZImpl implements DefaultJsonString {
        private byte[] raw;

        public JakartaTimestampTZImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JakartaPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.timestampTZToString(JakartaExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonTimestampTZImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaIntervalDSImpl.class */
    public static class JakartaIntervalDSImpl implements DefaultJsonString {
        private byte[] raw;

        public JakartaIntervalDSImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JakartaPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.serializeIntervalDS(JakartaExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonIntervalDSImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaIntervalYMImpl.class */
    public static class JakartaIntervalYMImpl implements DefaultJsonString {
        byte[] raw;

        public JakartaIntervalYMImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JakartaPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.serializeIntervalYM(JakartaExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonIntervalYMImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaStringNumberImpl.class */
    public static class JakartaStringNumberImpl extends DefaultJsonNumber {
        private String value;

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public /* bridge */ /* synthetic */ String getString() {
            return super.getString();
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ boolean isWrapperFor(Class cls) throws SQLException {
            return super.isWrapperFor(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ Object unwrap(Class cls) throws SQLException {
            return super.unwrap(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber
        public /* bridge */ /* synthetic */ JsonValue.ValueType getValueType() {
            return super.getValueType();
        }

        public JakartaStringNumberImpl(String value) {
            super();
            this.value = value;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public BigDecimal bigDecimalValue() {
            return new BigDecimal(this.value);
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaPrimitive.DefaultJsonNumber
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonStringNumberImpl(this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$DefaultJsonNumber.class */
    private static abstract class DefaultJsonNumber extends OracleJsonNumberImpl implements JsonNumber, Wrapper {
        abstract OracleJsonValue getUnwrapped();

        private DefaultJsonNumber() {
        }

        public JsonValue.ValueType getValueType() {
            return JsonValue.ValueType.NUMBER;
        }

        @Override // java.sql.Wrapper
        public <T> T unwrap(Class<T> cls) throws SQLException {
            T t = (T) getUnwrapped();
            if (cls.isInstance(t)) {
                return t;
            }
            throw new SQLException(OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, cls.getName()).getMessage());
        }

        @Override // java.sql.Wrapper
        public boolean isWrapperFor(Class<?> iface) throws SQLException {
            return iface.isInstance(getUnwrapped());
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public String getString() {
            try {
                return ((OracleJsonNumberImpl) unwrap(OracleJsonNumber.class)).getString();
            } catch (SQLException e) {
                throw new IllegalStateException();
            }
        }

        public final boolean equals(Object other) {
            if (!(other instanceof JsonNumber)) {
                return false;
            }
            return bigDecimalValue().equals(((JsonNumber) other).bigDecimalValue());
        }

        public final int hashCode() {
            return bigDecimalValue().hashCode();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$JakartaVectorImpl.class */
    public static class JakartaVectorImpl extends JakartaArrayImpl implements JsonArray, Wrapper {
        byte[] raw;

        public JakartaVectorImpl(byte[] raw) {
            super(new OracleJsonArrayImpl());
            this.raw = raw;
            try {
                double[] arr = VECTOR.toDoubleArray(raw);
                for (double d : arr) {
                    this.wrapped.add(d);
                }
            } catch (SQLException e) {
                throw new OracleJsonException(e);
            }
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaArrayImpl, java.sql.Wrapper
        public <T> T unwrap(Class<T> cls) throws SQLException {
            T t = (T) new OracleJsonVectorImpl(this.raw);
            if (cls.isInstance(t)) {
                return t;
            }
            throw new SQLException(OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, cls.getName()).getMessage());
        }

        @Override // oracle.jdbc.driver.json.tree.JakartaArrayImpl, java.sql.Wrapper
        public boolean isWrapperFor(Class<?> iface) throws SQLException {
            return iface.isInstance(new OracleJsonVectorImpl(this.raw));
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JakartaPrimitive$DefaultJsonString.class */
    private interface DefaultJsonString extends JsonString, Wrapper {
        OracleJsonValue getUnwrapped();

        default JsonValue.ValueType getValueType() {
            return JsonValue.ValueType.STRING;
        }

        @Override // java.sql.Wrapper
        default <T> T unwrap(Class<T> cls) throws SQLException {
            T t = (T) getUnwrapped();
            if (cls.isInstance(t)) {
                return t;
            }
            throw new SQLException(OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, cls.getName()).getMessage());
        }

        @Override // java.sql.Wrapper
        default boolean isWrapperFor(Class<?> iface) throws SQLException {
            return iface.isInstance(getUnwrapped());
        }

        default CharSequence getChars() {
            return getString();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static boolean stringsEqual(JsonString ths, Object other) {
        if (other instanceof JsonString) {
            return ths == other || ths.getString().equals(((JsonString) other).getString());
        }
        return false;
    }

    private JakartaPrimitive() {
    }
}
