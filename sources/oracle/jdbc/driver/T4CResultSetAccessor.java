package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CResultSetAccessor.class */
class T4CResultSetAccessor extends ResultSetAccessor {
    T4CMAREngine mare;
    OracleStatement[] newstmt;
    byte[] empty;
    boolean underlyingLongRaw;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CResultSetAccessor.class.desiredAssertionStatus();
    }

    T4CResultSetAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, form, external_type, isOutBind, false);
        this.newstmt = null;
        this.empty = new byte[]{0};
        this.underlyingLongRaw = false;
        this.mare = _mare;
    }

    T4CResultSetAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len == -1 ? _definedColumnSize : max_len, nullable, flags, precision, scale, contflag, total_elems, form);
        this.newstmt = null;
        this.empty = new byte[]{0};
        this.underlyingLongRaw = false;
        this.mare = _mare;
        if (stmt != null && stmt.implicitDefineForLobPrefetchDone) {
            this.definedColumnType = 0;
            this.definedColumnSize = 0;
        } else {
            this.definedColumnType = _definedColumnType;
            this.definedColumnSize = _definedColumnSize;
        }
        if (max_len == -1) {
            this.underlyingLongRaw = true;
        }
    }

    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        boolean isStream = false;
        if (!isUseless()) {
            if (isUnexpected()) {
                long pos = this.rowData.getPosition();
                unmarshalColumnMetadata();
                unmarshalBytes();
                this.rowData.setPosition(pos);
                setNull(this.lastRowProcessed, true);
            } else if (isNullByDescribe()) {
                setNull(this.lastRowProcessed, true);
                unmarshalColumnMetadata();
                if (this.statement.connection.versionNumber < 9200) {
                    processIndicator(0);
                }
            } else {
                unmarshalColumnMetadata();
                isStream = unmarshalBytes();
            }
        }
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
        return isStream;
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.Accessor
    void setCapacity(int capacity) {
        super.setCapacity(capacity);
        if (this.newstmt == null || this.newstmt.length < capacity) {
            OracleStatement[] tmp = new OracleStatement[capacity];
            if (this.newstmt != null) {
                System.arraycopy(this.newstmt, 0, tmp, 0, this.newstmt.length);
            }
            this.newstmt = tmp;
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    void insertNull(int rowIndex) throws SQLException {
        System.arraycopy(this.newstmt, rowIndex, this.newstmt, rowIndex + 1, (this.newstmt.length - rowIndex) - 1);
        super.insertNull(rowIndex);
    }

    @Override // oracle.jdbc.driver.Accessor
    Accessor copyForDefine(OracleStatement dest) {
        T4CResultSetAccessor acc = (T4CResultSetAccessor) super.copyForDefine(dest);
        acc.newstmt = null;
        return acc;
    }

    @Override // oracle.jdbc.driver.Accessor
    protected void copyFromInternal(Accessor srcAcc, int srcRow, int destRow) throws SQLException {
        super.copyFromInternal(srcAcc, srcRow, destRow);
        this.newstmt[destRow] = ((T4CResultSetAccessor) srcAcc).newstmt[srcRow];
    }

    @Override // oracle.jdbc.driver.Accessor
    void deleteRow(int row) throws SQLException {
        super.deleteRow(row);
        if (this.newstmt[row] != null) {
            this.newstmt[row].close();
        }
        delete(this.newstmt, row);
    }

    boolean unmarshalBytes() throws SQLException, IOException {
        this.newstmt[this.lastRowProcessed] = this.statement.connection.RefCursorBytesToStatement(this.empty, this.statement);
        this.newstmt[this.lastRowProcessed].needToSendOalToFetch = true;
        T4CTTIdcb dcb = new T4CTTIdcb((T4CConnection) this.statement.connection);
        dcb.init(this.newstmt[this.lastRowProcessed], 0);
        this.newstmt[this.lastRowProcessed].accessors = dcb.receiveFromRefCursor(this.newstmt[this.lastRowProcessed].accessors);
        this.newstmt[this.lastRowProcessed].numberOfDefinePositions = this.newstmt[this.lastRowProcessed].accessors.length;
        this.newstmt[this.lastRowProcessed].describedWithNames = true;
        this.newstmt[this.lastRowProcessed].described = true;
        int cursorId = (int) this.mare.unmarshalUB4();
        this.newstmt[this.lastRowProcessed].setCursorId(cursorId);
        if (cursorId > 0) {
            this.rowData.putShort((short) cursorId);
            setLength(this.lastRowProcessed, 2);
            processIndicator(2);
            return false;
        }
        this.newstmt[this.lastRowProcessed].closeOrCache(null);
        this.newstmt[this.lastRowProcessed] = null;
        setNull(this.lastRowProcessed, true);
        processIndicator(0);
        return false;
    }

    @Override // oracle.jdbc.driver.ResultSetAccessor, oracle.jdbc.driver.GeneratedAccessor
    ResultSet getCursor(int currentRow) throws SQLException {
        ResultSet rset = null;
        if (this.newstmt[currentRow] != null) {
            for (int i = 0; i < this.newstmt[currentRow].numberOfDefinePositions; i++) {
                this.newstmt[currentRow].accessors[i].initMetadata();
            }
            this.newstmt[currentRow].prepareAccessors();
            this.newstmt[currentRow].setPrefetchInternal(this.statement.getFetchSize(), false, false);
            this.newstmt[currentRow].doSetQueryTimeout(this.statement.doGetQueryTimeout());
            this.newstmt[currentRow].closeOnCompletion();
            OracleResultSet rsetimpl = this.newstmt[currentRow].createResultSet();
            this.newstmt[currentRow].currentResultSet = rsetimpl;
            rset = rsetimpl;
        }
        return rset;
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new AccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CResultSetAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CResultSetAccessor(stmt, T4CResultSetAccessor.this.describeMaxLength, T4CResultSetAccessor.this.nullable, -1, T4CResultSetAccessor.this.precision, T4CResultSetAccessor.this.scale, T4CResultSetAccessor.this.contflag, -1, T4CResultSetAccessor.this.formOfUse, T4CResultSetAccessor.this.definedColumnType, T4CResultSetAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
