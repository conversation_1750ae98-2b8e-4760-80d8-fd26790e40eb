package oracle.jdbc.aq;

import java.sql.SQLException;
import oracle.jdbc.driver.InternalFactory;

/* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQFactory.class */
public abstract class AQFactory {
    public static AQMessage createAQMessage(AQMessageProperties prop) throws SQLException {
        return InternalFactory.createAQMessage(prop);
    }

    public static AQMessageProperties createAQMessageProperties() throws SQLException {
        return InternalFactory.createAQMessageProperties();
    }

    public static AQAgent createAQAgent() throws SQLException {
        return InternalFactory.createAQAgent();
    }
}
