package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.Arrays;
import oracle.jdbc.internal.XSSessionParameters;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/XSSessionParametersI.class */
final class XSSessionParametersI extends XSSessionParameters {
    byte[][] textParamBytes;
    int intParam = 0;
    byte[] binaryParam = null;
    String[] textParam = null;

    XSSessionParametersI() {
    }

    /* JADX WARN: Type inference failed for: r1v5, types: [byte[], byte[][]] */
    void doCharConversion(DBConversion conv) throws SQLException {
        if (this.textParam != null && this.textParam.length > 0) {
            this.textParamBytes = new byte[this.textParam.length];
            for (int i = 0; i < this.textParam.length; i++) {
                this.textParamBytes[i] = conv.StringToCharBytes(this.textParam[i]);
            }
            return;
        }
        this.textParamBytes = (byte[][]) null;
    }

    @Override // oracle.jdbc.internal.XSSessionParameters
    public void setBinary(byte[] binaryParam) throws SQLException {
        InternalFactory.xsSecurityCheck();
        if (binaryParam != null) {
            this.binaryParam = Arrays.copyOf(binaryParam, binaryParam.length);
        } else {
            this.binaryParam = null;
        }
    }

    @Override // oracle.jdbc.internal.XSSessionParameters
    public void setInt(int intParam) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.intParam = intParam;
    }

    @Override // oracle.jdbc.internal.XSSessionParameters
    public void setText(String[] textParam) throws SQLException {
        InternalFactory.xsSecurityCheck();
        if (textParam != null) {
            this.textParam = (String[]) Arrays.copyOf(textParam, textParam.length);
        } else {
            this.textParam = null;
        }
    }
}
