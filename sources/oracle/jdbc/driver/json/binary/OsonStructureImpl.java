package oracle.jdbc.driver.json.binary;

import java.nio.ByteBuffer;
import java.util.Iterator;
import java.util.NoSuchElementException;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.ShardingKeyInfo;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonStructureImpl.class */
public abstract class OsonStructureImpl {
    OsonContext ctx;
    int pos;
    public int size;
    byte childOffsetUb;
    int childArrayOffset;

    protected abstract int getChildOffset(int i);

    public abstract OracleJsonValue.OracleJsonType getOracleJsonType();

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonStructureImpl$PositionIter.class */
    public abstract class PositionIter<T> implements Iterator<T> {
        protected int ipos = 0;

        public abstract T getValue(int i);

        public PositionIter() {
        }

        @Override // java.util.Iterator
        public boolean hasNext() {
            return this.ipos < OsonStructureImpl.this.size;
        }

        @Override // java.util.Iterator
        public T next() {
            if (this.ipos >= OsonStructureImpl.this.size) {
                throw new NoSuchElementException();
            }
            int i = this.ipos;
            this.ipos = i + 1;
            return getValue(i);
        }
    }

    public OsonStructureImpl(OsonContext ctx) {
        this.ctx = ctx;
    }

    public int size() {
        return this.size;
    }

    public boolean isEmpty() {
        return size() == 0;
    }

    void init(int pos) {
        this.pos = pos;
    }

    void initChildOffseUb(int op) {
        if ((op & 32) != 0) {
            this.childOffsetUb = (byte) 4;
        } else {
            this.childOffsetUb = (byte) 2;
        }
    }

    Boolean getBooleanInternal(int offset) {
        int op = this.ctx.b.getUB1(offset);
        if (op < 0) {
            return null;
        }
        if (op == 49) {
            return true;
        }
        if (op == 50) {
            return false;
        }
        return null;
    }

    String getStringInternal(int offset) {
        int op = this.ctx.b.getUB1(offset);
        if (op < 0) {
            return null;
        }
        if (op <= 31) {
            this.ctx.b.position(offset + 1);
            return this.ctx.b.readString(op);
        }
        if (op == 51) {
            int len = this.ctx.b.getUB1(offset + 1);
            this.ctx.b.position(offset + 2);
            return this.ctx.b.readString(len);
        }
        if (op == 55) {
            int len2 = this.ctx.b.getUB2(offset + 1);
            this.ctx.b.position(offset + 3);
            return this.ctx.b.readString(len2);
        }
        if (op == 56) {
            int len3 = this.ctx.b.getUB4int(offset + 1);
            this.ctx.b.position(offset + 5);
            return this.ctx.b.readString(len3);
        }
        return null;
    }

    boolean isNullInternal(int childOffset) {
        int op = this.ctx.b.getUB1(childOffset);
        return op >= 0 && op == 48;
    }

    public Object getValueInternal(int offset) {
        return getValueInternal(offset, this.ctx.getFactory(), this.ctx);
    }

    public static Object getValueInternal(int offset, OsonValueFactory factory, OsonContext ctx) {
        int op = ctx.b.getUB1(offset);
        if ((op & 192) == 192) {
            return factory.createArray(ctx, offset);
        }
        if ((op & 192) == 128) {
            if ((op & DatabaseError.EOJ_QUERY_TIMEOUT_INVALID_STATE) == 131) {
                return getOverflowValue(offset, factory, ctx);
            }
            return factory.createObject(ctx, offset);
        }
        if (op <= 31) {
            return factory.createString(ctx, offset + 1, op);
        }
        if (OsonConstants.isSB4(op)) {
            return factory.createNumber(readRaw(offset + 1, op & 7, ctx), OracleJsonDecimal.TargetType.INT);
        }
        if (OsonConstants.isSB8(op)) {
            return factory.createNumber(readRaw(offset + 1, op & 15, ctx), OracleJsonDecimal.TargetType.LONG);
        }
        if (OsonConstants.isOraNum16(op)) {
            return factory.createNumber(readRaw(offset + 1, (op & 15) + 1, ctx), null);
        }
        if (OsonConstants.isDec_16(op)) {
            return factory.createNumber(readRaw(offset + 1, (op & 15) + 1, ctx), OracleJsonDecimal.TargetType.DECIMAL);
        }
        switch (op) {
            case 48:
                return factory.createNull();
            case 49:
                return factory.createTrue();
            case 50:
                return factory.createFalse();
            case 51:
                int len = ctx.b.getUB1(offset + 1);
                return factory.createString(ctx, offset + 2, len);
            case 52:
                byte[] raw = readRaw(offset + 2, ctx.b.getUB1(offset + 1), ctx);
                return factory.createNumber(raw, null);
            case DatabaseError.EOJ_INVALID_SIZE /* 53 */:
                int len2 = ctx.b.getUB1(offset + 1);
                ctx.b.position(offset + 2);
                String num = ctx.b.readString(len2);
                return factory.createStringNumber(num);
            case 54:
                ctx.b.position(offset + 1);
                return factory.createDouble(ctx.b.readDtyDouble());
            case DatabaseError.EOJ_FAIL_CONVERSION_CHARACTER /* 55 */:
                int len3 = ctx.b.getUB2(offset + 1);
                return factory.createString(ctx, offset + 3, len3);
            case 56:
                int len4 = ctx.b.getUB4int(offset + 1);
                ctx.b.position(offset + 5);
                return factory.createString(ctx, offset + 5, len4);
            case 57:
            case 125:
                return factory.createTimestamp(readTimestamp(ctx.b, offset));
            case 58:
                int len5 = ctx.b.getUB2(offset + 1);
                return factory.createBinary(ctx, offset + 3, len5, false);
            case 59:
                int len6 = ctx.b.getUB4int(offset + 1);
                return factory.createBinary(ctx, offset + 5, len6, false);
            case 60:
                return factory.createDate(readRaw(offset + 1, 7, ctx));
            case 61:
                return factory.createIntervalYM(readRaw(offset + 1, 5, ctx));
            case 62:
                return factory.createIntervalDS(readRaw(offset + 1, 11, ctx));
            case 63:
            case 64:
            case DatabaseError.EOJ_CONV_WAS_NULL /* 65 */:
            case 66:
            case 67:
            case DatabaseError.EOJ_INVALID_ARGUMENTS /* 68 */:
            case DatabaseError.EOJ_USE_XA_EXPLICIT /* 69 */:
            case 70:
            case 71:
            case 72:
            case ShardingKeyInfo.GWS_KEY_PUSH_BIND_INDEX_20_1 /* 73 */:
            case 74:
            case DatabaseError.EOJ_INVALID_FORWARD_RSET_OP /* 75 */:
            case 76:
            case DatabaseError.EOJ_FAIL_REF_SETVALUE /* 77 */:
            case 78:
            case DatabaseError.EOJ_USER_CREDENTIALS_FAIL /* 79 */:
            case 80:
            case 81:
            case 82:
            case 83:
            case 84:
            case DatabaseError.EOJ_UPDATE_CONFLICTS /* 85 */:
            case 86:
            case DatabaseError.WARN_IGNORE_FETCH_DIRECTION /* 87 */:
            case DatabaseError.EOJ_UNSUPPORTED_SYNTAX /* 88 */:
            case DatabaseError.EOJ_INTERNAL_ERROR /* 89 */:
            case 90:
            case 91:
            case 92:
            case 93:
            case 94:
            case 95:
            case 96:
            case 97:
            case 98:
            case 99:
            case 100:
            case 101:
            case 102:
            case 103:
            case 104:
            case 105:
            case 106:
            case 107:
            case 108:
            case 109:
            case 110:
            case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
            case 112:
            case 113:
            case 114:
            case 115:
            case 120:
            case 121:
            case 122:
            default:
                throw new UnsupportedOperationException(String.valueOf(op));
            case DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN /* 116 */:
                byte[] raw2 = readRaw(offset + 2, ctx.b.getUB1(offset + 1), ctx);
                return factory.createNumber(raw2, OracleJsonDecimal.TargetType.DECIMAL);
            case DatabaseError.EOJ_SETSVPT_IN_GLOBAL_TXN /* 117 */:
                return getOverflowValue(offset, factory, ctx);
            case 118:
                return getForwardedValue(ctx.b.getUB2(offset + 1), factory, ctx);
            case 119:
                return getForwardedValue(ctx.b.getUB4int(offset + 1), factory, ctx);
            case 123:
                int op2 = ctx.b.getUB1(offset + 1);
                if (op2 == 1) {
                    int len7 = ctx.b.getUB4int(offset + 2);
                    return factory.createVector(ctx, offset + 6, len7);
                }
                throw new UnsupportedOperationException(String.valueOf((op << 8) | op2));
            case DatabaseError.EOJ_WARN_CACHE_INACTIVITY_TIMEOUT /* 124 */:
                return factory.createTimestampTZ(readTimestampTZ(ctx.b, offset));
            case 126:
                int len8 = ctx.b.getUB1(offset + 1);
                if (len8 > 127 || len8 < 0) {
                    throw new UnsupportedOperationException(String.valueOf(op));
                }
                return factory.createBinary(ctx, offset + 2, len8, true);
            case 127:
                ctx.b.position(offset + 1);
                return factory.createFloat(ctx.b.readDtyFloat());
        }
    }

    private static Object getForwardedValue(int relativeOffset, OsonValueFactory factory, OsonContext ctx) {
        int fwd = relativeOffset + ctx.header.extendedTreeSegmentOffset;
        return getValueInternal(fwd, factory, ctx);
    }

    private static Object getOverflowValue(int absoluteOffset, OsonValueFactory factory, OsonContext ctx) {
        int relativeOffset = absoluteOffset - ctx.header.treeSegmentOffset;
        OsonHeader header = ctx.header;
        int fwd = header.forwardingAddress.get(Integer.valueOf(relativeOffset)).intValue() + header.extendedTreeSegmentOffset;
        return getValueInternal(fwd, factory, ctx);
    }

    private static byte[] readRaw(int offset, int len, OsonContext ctx) {
        ctx.b.position(offset);
        byte[] res = new byte[len];
        ctx.b.get(res);
        return res;
    }

    protected static byte[] readTimestamp(OsonBuffer b, int offset) {
        int len;
        int op = b.getUB1(offset);
        if (op == 125) {
            len = OsonPrimitiveConversions.SIZE_TIMESTAMP_NOFRAC;
        } else if (op == 57) {
            len = OsonPrimitiveConversions.SIZE_TIMESTAMP;
        } else {
            throw new ClassCastException();
        }
        byte[] raw = new byte[len];
        b.position(offset + 1);
        b.get(raw);
        return raw;
    }

    protected static byte[] readTimestampTZ(OsonBuffer b, int offset) {
        byte[] raw = new byte[OsonPrimitiveConversions.SIZE_TIMESTAMPTZ];
        b.position(offset + 1);
        b.get(raw);
        return raw;
    }

    protected OsonAbstractArray getArrayInternal(int childOffset) {
        int op = this.ctx.b.getUB1(childOffset);
        if ((op & 192) == 192) {
            return this.ctx.getFactory().createArray(this.ctx, childOffset);
        }
        throw new ClassCastException();
    }

    protected OsonAbstractObject getJsonObjectInternal(int childOffset) {
        int op = this.ctx.b.getUB1(childOffset);
        if ((op & 192) == 128) {
            return this.ctx.getFactory().createObject(this.ctx, childOffset);
        }
        throw new ClassCastException();
    }

    public ByteBuffer getBuffer() {
        ByteBuffer b = this.ctx.b.buffer;
        b.position(0);
        return b;
    }

    public boolean isRoot() {
        return this.pos == this.ctx.header.getTreeSegmentOffset();
    }
}
