package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.sql.BLOB;
import oracle.sql.Datum;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTIBlob.class */
final class T4C8TTIBlob extends T4C8TTILob {
    private static final String CLASS_NAME = T4C8TTIBlob.class.getName();

    T4C8TTIBlob(T4CConnection _conn) {
        super(_conn);
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    Datum createTemporaryLob(Connection conn, boolean cache, int duration) throws SQLException, IOException {
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "createTemporaryLob", "cache={0}, duration={1}", (String) null, (Throwable) null, Boolean.valueOf(cache), Integer.valueOf(duration));
        if (duration == 12) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 158).fillInStackTrace());
        }
        BLOB blob = null;
        initializeLobdef();
        this.lobops = 272L;
        int tempLobSize = getTemporaryLobSize();
        this.sourceLobLocator = new byte[tempLobSize];
        this.sourceLobLocator[1] = (byte) (tempLobSize - 2);
        this.characterSet = (short) 1;
        this.destinationOffset = 113L;
        this.destinationLength = duration;
        this.lobamt = duration;
        this.sendLobamt = true;
        this.nullO2U = true;
        if (this.connection.versionNumber >= 9000) {
            this.lobscn = new int[1];
            this.lobscn[0] = cache ? 1 : 0;
            this.lobscnl = 1;
        }
        doRPC();
        if (this.sourceLobLocator != null) {
            blob = new BLOB((oracle.jdbc.OracleConnection) conn, this.sourceLobLocator);
        }
        return blob;
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean openLob(byte[] lobLocator, int mode) throws SQLException, IOException {
        int kokl_mode = 2;
        if (mode == 0) {
            kokl_mode = 1;
        }
        boolean wasOpened = openLob(lobLocator, kokl_mode, 32768);
        return wasOpened;
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean closeLob(byte[] lobLocator) throws SQLException, IOException {
        boolean wasClosed = closeLob(lobLocator, 65536);
        return wasClosed;
    }

    @Override // oracle.jdbc.driver.T4C8TTILob
    boolean isOpenLob(byte[] lobLocator) throws SQLException, IOException {
        return isOpenLob(lobLocator, 69632);
    }
}
