package oracle.jdbc.connector;

import javax.resource.spi.ConnectionRequestInfo;

/* loaded from: ojdbc8.jar:oracle/jdbc/connector/OracleConnectionRequestInfo.class */
public class OracleConnectionRequestInfo implements ConnectionRequestInfo {
    private String user;
    private String password;

    public OracleConnectionRequestInfo(String user, String password) {
        this.user = null;
        this.password = null;
        this.user = user;
        this.password = password;
    }

    public String getUser() {
        return this.user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean equals(Object other) {
        if (!(other instanceof OracleConnectionRequestInfo)) {
            return false;
        }
        OracleConnectionRequestInfo info = (OracleConnectionRequestInfo) other;
        return this.user.equalsIgnoreCase(info.getUser()) && this.password.equals(info.getPassword());
    }
}
