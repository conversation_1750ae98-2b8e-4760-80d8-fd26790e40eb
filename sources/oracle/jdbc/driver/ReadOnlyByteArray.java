package oracle.jdbc.driver;

import java.security.MessageDigest;
import java.sql.SQLException;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ReadOnlyByteArray.class */
class ReadOnlyByteArray extends ByteArray {
    private ByteArray array;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !ReadOnlyByteArray.class.desiredAssertionStatus();
    }

    static ByteArray newReadOnlyByteArray(Diagnosable diagnosable, ByteArray a) {
        return a instanceof ReadOnlyByteArray ? a : new ReadOnlyByteArray(diagnosable, a);
    }

    private ReadOnlyByteArray(Diagnosable diagnosable, ByteArray a) {
        super(diagnosable);
        this.array = a;
    }

    @Override // oracle.jdbc.driver.ByteArray
    long getCapacity() {
        return this.array.getCapacity();
    }

    @Override // oracle.jdbc.driver.ByteArray
    long length() {
        return this.array.length();
    }

    @Override // oracle.jdbc.driver.ByteArray
    void put(long index, byte value) {
        if (!$assertionsDisabled) {
            throw new AssertionError("attempt to modify a read-only byte array");
        }
    }

    @Override // oracle.jdbc.driver.ByteArray
    byte get(long index) {
        return this.array.get(index);
    }

    @Override // oracle.jdbc.driver.ByteArray
    void put(long offset, byte[] src, int srcOffset, int length) {
        if (!$assertionsDisabled) {
            throw new AssertionError("attempt to modify a read-only byte array");
        }
    }

    @Override // oracle.jdbc.driver.ByteArray
    void get(long offset, byte[] dest, int destOffset, int length) {
        this.array.get(offset, dest, destOffset, length);
    }

    @Override // oracle.jdbc.driver.ByteArray
    char[] getChars(long offset, int lengthInBytes, CharacterSet charSet, int[] out_lengthInChars) throws SQLException {
        return this.array.getChars(offset, lengthInBytes, charSet, out_lengthInChars);
    }

    @Override // oracle.jdbc.driver.ByteArray
    long updateChecksum(long offset, int length, CRC64 crc, long checksum) {
        return this.array.updateChecksum(offset, length, crc, checksum);
    }

    @Override // oracle.jdbc.driver.ByteArray
    void updateDigest(MessageDigest md, long valOffset, int valLen) {
        this.array.updateDigest(md, valOffset, valLen);
    }

    @Override // oracle.jdbc.driver.ByteArray
    void free(boolean returnBlocksToSource) {
    }

    void deepFree() {
        this.array.free();
        this.array = null;
    }
}
