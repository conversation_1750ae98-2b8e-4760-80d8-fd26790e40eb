package oracle.jdbc.driver.utils;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Stack;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/SQLTokenizer.class */
public class SQLTokenizer {
    private static final int UNINITIALIZED = -1;
    private String sql;
    private static final int TOKEN_ARRAY_INITIAL_SIZE = 50;
    protected TokenType[] tokens = new TokenType[50];
    protected int[] tokensBeginIndex = new int[50];
    protected int nbTokens = 0;
    private List<OracleConvertedSqlSequence> ncharLiterals = null;
    private static final int BASE = 0;
    private static final int DOUBLE_QUOTED_STRING = 1;
    private static final int SINGLE_QUOTED_STRING = 2;
    private static final int DASH_COMMENT_BEGIN = 3;
    private static final int DASH_COMMENT = 4;
    private static final int SLASH_COMMENT_BEGIN = 5;
    private static final int SLASH_COMMENT = 6;
    private static final int SLASH_COMMENT_END = 7;
    private static final int BIND_1_BEGIN = 8;
    private static final int BIND_1 = 9;
    private static final int BIND_2 = 10;
    private static final int NTICK = 11;
    private static final int NTICK_2 = 12;
    private static final int NTICK_END = 13;
    private static final int QTICK = 14;
    private static final int QTICK_2 = 15;
    private static final int QTICK_DELIMITER = 16;
    private static final int QTICK_3 = 17;
    private static final int QTICK_4 = 18;
    private static final int QTICK_END = 19;
    private static final int UTICK = 20;
    private static final int UTICK_2 = 21;
    private static final int UTICK_END = 22;
    private static final int J_SON = 23;
    private static final int JS_ON = 24;
    private static final int JSO_N = 25;
    private static final int JSON_ = 26;
    private static final int JSON__OBJECT = 27;
    private static final int JSON_O_BJECT = 28;
    private static final int JSON_OB_JECT = 29;
    private static final int JSON_OBJ_ECT = 30;
    private static final int JSON_OBJE_CT = 31;
    private static final int JSON_OBJEC_T = 32;
    private static final int JSON_OBJECT_ = 33;
    private static final int JSON_KEY = 34;
    private static final int JSON_KEY_END = 35;
    private static final int JSON_VALUE = 36;
    private static final int JSON_KEY_BIND_BEGIN = 37;
    private static final int JSON_VALUE_BIND_BEGIN = 38;
    private static final int JSON_KEY_BIND = 39;
    private static final int JSON_VALUE_BIND = 40;
    private static final int DOUBLE_QUOTED_STRING_JSON_KEY = 41;
    private static final int SINGLE_QUOTED_STRING_JSON_KEY = 42;
    private static final int DOUBLE_QUOTED_STRING_JSON_VALUE = 43;
    private static final int SINGLE_QUOTED_STRING_JSON_VALUE = 44;
    private static final int OPEN_BRACKET = 45;
    private static final int BLOCK_ESCAPE_START = 46;
    private static final int BLOCK_ESCAPE_ESCAPING = 47;
    private static final int BLOCK_ESCAPE_END = 48;
    private static final int TOKEN = 49;
    private static final int OTHER = 50;
    private static final int NB_STATES = 51;
    private static final int ACTION_NONE = 0;
    private static final int ACTION_RECORD_TOKEN = 1;
    private static final int ACTION_RECORD_OTHER = 2;
    private static final int ACTION_RECORD_BIND = 3;
    private static final int ACTION_RECORD_QTICK = 4;
    private static final int ACTION_RECORD_QTICK_DELIMITER = 5;
    private static final int ACTION_RECORD_NTICK = 6;
    private static final int ACTION_RECORD_UTICK = 7;
    private static final int ACTION_RECORD_COMMENT = 8;
    private static final int ACTION_RECORD_SINGLE_QUOTED_STRING = 9;
    private static final int ACTION_RECORD_DOUBLE_QUOTED_STRING = 10;
    private static final int ACTION_RECORD_OPEN_BRACKET = 11;
    private static final int ACTION_RECORD_BLOCK_ESCAPE = 12;
    public static final String[] PARSER_STATE_NAME = new String[51];
    static final int[][] TRANSITION = new int[51];
    static final int[][] ACTION = new int[51];
    static final int[] basic = {50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 49, 49, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 50, 50, 50, 50, 50, 50, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 50, 50, 50, 50, 49, 50, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 49, 50, 50, 50, 50, 50};

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/SQLTokenizer$TokenType.class */
    public enum TokenType {
        COMMENT,
        WHITESPACES,
        NTICK,
        QTICK,
        UTICK,
        BIND,
        TOKEN,
        SINGLE_QUOTED_STRING,
        DOUBLE_QUOTED_STRING,
        OPEN_PARENTHESE,
        CLOSE_PARENTHESE,
        OPEN_BRACKET,
        CLOSE_BRACKET,
        OPEN_SQUARE_BRACKET,
        CLOSE_SQUARE_BRACKET,
        COMMA,
        COLON,
        BLOCK_ESCAPE,
        OTHER
    }

    public SQLTokenizer(String sql) throws SQLException {
        this.sql = sql;
        tokenize();
    }

    private void tokenize() throws SQLException {
        if (this.sql != null && !this.sql.isEmpty()) {
            tokenize(0, -1);
        }
    }

    private int tokenize(int beginIndex, int tokenLastIndex) throws SQLException {
        char currentChar;
        int currentState;
        int lastState = 0;
        int nbWhitespaces = 0;
        Character qtick_delimiter = null;
        Stack<Character> jsonStack = new Stack<>();
        int parenthesesTracker = 0;
        boolean isFirstToken = true;
        Boolean isDDLStatement = null;
        int i = beginIndex;
        while (i <= this.sql.length()) {
            if (i != this.sql.length()) {
                currentChar = this.sql.charAt(i);
                if (currentChar > 127) {
                    if (Character.isLetterOrDigit(currentChar)) {
                        currentChar = 'X';
                    } else {
                        currentChar = ' ';
                    }
                }
                if ((lastState == 17 || lastState == 18) && qtick_delimiter.charValue() == currentChar) {
                    currentState = 18;
                } else {
                    currentState = TRANSITION[lastState][currentChar];
                    if (currentState == 0 && !jsonStack.isEmpty()) {
                        currentState = jsonStack.peek().charValue() == '{' ? 34 : 36;
                    }
                }
            } else {
                currentChar = '\n';
                currentState = 0;
            }
            int action = ACTION[lastState][currentState];
            if (action != 0) {
                if (nbWhitespaces > 0) {
                    addToken(TokenType.WHITESPACES, tokenLastIndex + 1);
                    tokenLastIndex += nbWhitespaces;
                    nbWhitespaces = 0;
                }
                switch (action) {
                    case 1:
                        if (isFirstToken) {
                            isDDLStatement = Boolean.valueOf(isDDLStatementStartToken(this.sql.substring(tokenLastIndex + 1, i)));
                            isFirstToken = false;
                        }
                        addToken(TokenType.TOKEN, tokenLastIndex + 1);
                        tokenLastIndex = i - 1;
                        break;
                    case 2:
                        addToken(TokenType.OTHER, tokenLastIndex + 1);
                        tokenLastIndex = i - 1;
                        break;
                    case 3:
                        if (isDDLStatement == null || !isDDLStatement.booleanValue()) {
                            addToken(TokenType.BIND, tokenLastIndex + 1);
                            tokenLastIndex = i - 1;
                            break;
                        } else if ((i - 1) - (tokenLastIndex + 1) > 1) {
                            addToken(TokenType.COLON, tokenLastIndex + 1);
                            addToken(TokenType.TOKEN, tokenLastIndex + 1 + 1);
                            tokenLastIndex = i - 1;
                            break;
                        } else {
                            addToken(TokenType.OTHER, tokenLastIndex + 1);
                            tokenLastIndex++;
                            break;
                        }
                        break;
                    case 4:
                        addToken(TokenType.QTICK, tokenLastIndex + 1);
                        tokenLastIndex = i;
                        break;
                    case 5:
                        qtick_delimiter = Character.valueOf(StringUtils.getQTickClosingDelimiter(currentChar));
                        currentState = 17;
                        break;
                    case 6:
                        addToken(TokenType.NTICK, tokenLastIndex + 1);
                        StringBuilder convertedNcharLiteral = new StringBuilder((i - (tokenLastIndex + 1)) + 50);
                        convertedNcharLiteral.append("u'");
                        for (int j = tokenLastIndex + 3; j <= i - 1; j++) {
                            char c = this.sql.charAt(j);
                            if (c == '\\') {
                                convertedNcharLiteral.append("\\\\");
                            } else if (c < 128) {
                                convertedNcharLiteral.append(c);
                            } else {
                                convertedNcharLiteral.append(StringUtils.hexUnicode(c));
                            }
                        }
                        if (this.ncharLiterals == null) {
                            this.ncharLiterals = new ArrayList();
                        }
                        this.ncharLiterals.add(new OracleConvertedSqlSequence(this.nbTokens - 1, this.nbTokens - 1, convertedNcharLiteral.toString()));
                        tokenLastIndex = i - 1;
                        break;
                    case 7:
                        addToken(TokenType.UTICK, tokenLastIndex + 1);
                        tokenLastIndex = i - 1;
                        break;
                    case 8:
                        addToken(TokenType.COMMENT, tokenLastIndex + 1);
                        tokenLastIndex = i;
                        break;
                    case 9:
                        addToken(TokenType.SINGLE_QUOTED_STRING, tokenLastIndex + 1);
                        tokenLastIndex = i;
                        break;
                    case 10:
                        addToken(TokenType.DOUBLE_QUOTED_STRING, tokenLastIndex + 1);
                        tokenLastIndex = i;
                        break;
                    case 11:
                        addToken(TokenType.OPEN_BRACKET, tokenLastIndex + 1);
                        tokenLastIndex = i - 1;
                        break;
                    case 12:
                        addToken(TokenType.BLOCK_ESCAPE, tokenLastIndex + 1);
                        tokenLastIndex = i - 1;
                        if (currentState == 34 || currentState == 36) {
                            jsonStack.push('{');
                            currentState = 34;
                        }
                        if (isFirstToken) {
                            String token = this.sql.substring(this.tokensBeginIndex[this.nbTokens - 1] + 2, tokenLastIndex - 1).trim();
                            if (token.indexOf(" ") > 0) {
                                token = token.substring(0, token.indexOf(" "));
                            }
                            isDDLStatement = Boolean.valueOf(isDDLStatementStartToken(token.toUpperCase()));
                            isFirstToken = false;
                            break;
                        }
                        break;
                }
            }
            if (isBaseState(currentState)) {
                if (StringUtils.isWhiteSpace(currentChar) && lastState != 4) {
                    nbWhitespaces++;
                } else if (isSpecialChar(currentChar)) {
                    if (nbWhitespaces > 0) {
                        addToken(TokenType.WHITESPACES, tokenLastIndex + 1);
                        tokenLastIndex += nbWhitespaces;
                        nbWhitespaces = 0;
                    }
                    switch (currentChar) {
                        case '(':
                            tokenLastIndex++;
                            addToken(TokenType.OPEN_PARENTHESE, tokenLastIndex);
                            parenthesesTracker++;
                            if (currentState == 34) {
                                jsonStack.push('(');
                                break;
                            } else if (currentState == 36) {
                                i = tokenize(i + 1, tokenLastIndex);
                                tokenLastIndex = i;
                                break;
                            } else {
                                break;
                            }
                        case ')':
                            tokenLastIndex++;
                            addToken(TokenType.CLOSE_PARENTHESE, tokenLastIndex);
                            parenthesesTracker--;
                            if (parenthesesTracker == -1 && beginIndex != 0) {
                                return i;
                            }
                            if ((currentState != 34 && currentState != 35 && currentState != 36) || jsonStack.isEmpty() || jsonStack.peek().charValue() != '(') {
                                break;
                            } else {
                                jsonStack.pop();
                                break;
                            }
                        case ',':
                            tokenLastIndex++;
                            addToken(TokenType.COMMA, tokenLastIndex);
                            if (jsonStack.isEmpty()) {
                                continue;
                            } else {
                                switch (jsonStack.peek().charValue()) {
                                    case '(':
                                    case '{':
                                        currentState = 34;
                                        break;
                                    case '[':
                                        currentState = 36;
                                        break;
                                    default:
                                        throw new SQLException("An unknown character was inserted in the json stack");
                                }
                            }
                        case ':':
                            tokenLastIndex++;
                            addToken(TokenType.COLON, tokenLastIndex);
                            if (currentState != 34 && currentState != 35) {
                                break;
                            } else {
                                currentState = 36;
                                break;
                            }
                            break;
                        case '[':
                            tokenLastIndex++;
                            addToken(TokenType.OPEN_SQUARE_BRACKET, tokenLastIndex);
                            if (currentState != 34 && currentState != 36) {
                                break;
                            } else {
                                jsonStack.push('[');
                                currentState = 36;
                                break;
                            }
                            break;
                        case ']':
                            tokenLastIndex++;
                            addToken(TokenType.CLOSE_SQUARE_BRACKET, tokenLastIndex);
                            if ((currentState != 34 && currentState != 36) || jsonStack.isEmpty() || jsonStack.peek().charValue() != '[') {
                                break;
                            } else {
                                jsonStack.pop();
                                currentState = 36;
                                break;
                            }
                        case '{':
                            if (i + 1 < this.sql.length() && this.sql.charAt(i + 1) != '\\') {
                                tokenLastIndex++;
                                addToken(TokenType.OPEN_BRACKET, tokenLastIndex);
                                if (currentState != 34 && currentState != 36) {
                                    break;
                                } else {
                                    jsonStack.push('{');
                                    currentState = 34;
                                    break;
                                }
                            } else {
                                currentState = 45;
                                break;
                            }
                            break;
                        case '}':
                            tokenLastIndex++;
                            addToken(TokenType.CLOSE_BRACKET, tokenLastIndex);
                            if ((currentState != 34 && currentState != 36) || jsonStack.isEmpty() || jsonStack.peek().charValue() != '{') {
                                break;
                            } else {
                                jsonStack.pop();
                                currentState = 36;
                                break;
                            }
                        default:
                            throw new SQLException("A special character was added without its corresponding token type");
                    }
                } else {
                    continue;
                }
            }
            lastState = currentState;
            i++;
        }
        if (nbWhitespaces > 1) {
            addToken(TokenType.WHITESPACES, tokenLastIndex + 1);
        }
        return this.sql.length();
    }

    private void addToken(TokenType type, int tokenBeginIndex) {
        if (type != null && tokenBeginIndex >= 0) {
            if (this.nbTokens == this.tokens.length) {
                this.tokens = (TokenType[]) Arrays.copyOf(this.tokens, this.tokens.length * 2);
                this.tokensBeginIndex = Arrays.copyOf(this.tokensBeginIndex, this.tokensBeginIndex.length * 2);
            }
            this.tokens[this.nbTokens] = type;
            this.tokensBeginIndex[this.nbTokens] = tokenBeginIndex;
            this.nbTokens++;
        }
    }

    public int nextSignificantToken(int i) {
        while (i < this.nbTokens && (this.tokens[i] == TokenType.WHITESPACES || this.tokens[i] == TokenType.COMMENT)) {
            i++;
        }
        return i;
    }

    public int getNbTokens() {
        return this.nbTokens;
    }

    public TokenType getTokenType(int tokenIndex) {
        if (tokenIndex < 0 || tokenIndex >= this.nbTokens) {
            throw new IndexOutOfBoundsException();
        }
        return this.tokens[tokenIndex];
    }

    public String getToken(int tokenIndex) {
        int endIndex;
        if (tokenIndex < 0 || tokenIndex >= this.nbTokens) {
            throw new IndexOutOfBoundsException();
        }
        int offset = getTokenType(tokenIndex) == TokenType.BLOCK_ESCAPE ? 2 : 0;
        if (tokenIndex == this.nbTokens - 1) {
            endIndex = this.sql.length();
        } else {
            endIndex = this.tokensBeginIndex[tokenIndex + 1];
        }
        if (offset != 0) {
            return this.sql.substring(this.tokensBeginIndex[tokenIndex] + offset, endIndex - offset).replace("\\\\}", "\\}");
        }
        return this.sql.substring(this.tokensBeginIndex[tokenIndex], endIndex);
    }

    public int getTokenBeginIndex(int tokenIndex) {
        if (tokenIndex < 0 || tokenIndex >= this.nbTokens) {
            throw new IndexOutOfBoundsException();
        }
        return this.tokensBeginIndex[tokenIndex];
    }

    public String getTokens(int fromIndex, int toIndex) {
        if (fromIndex < 0 || fromIndex > this.nbTokens || toIndex < 0 || toIndex > this.nbTokens || toIndex <= fromIndex) {
            throw new IndexOutOfBoundsException();
        }
        StringBuilder builder = new StringBuilder();
        for (int i = fromIndex; i < toIndex; i++) {
            builder.append(getToken(i));
        }
        return builder.toString();
    }

    public int getTokenLength(int tokenIndex) {
        if (tokenIndex < 0 || tokenIndex >= this.nbTokens) {
            throw new IndexOutOfBoundsException();
        }
        if (tokenIndex == this.nbTokens - 1) {
            return this.sql.length() - this.tokensBeginIndex[tokenIndex];
        }
        return this.tokensBeginIndex[tokenIndex + 1] - this.tokensBeginIndex[tokenIndex];
    }

    public List<OracleConvertedSqlSequence> getNCharLiterals() {
        return this.ncharLiterals;
    }

    private static boolean isBaseState(int state) {
        switch (state) {
            case 0:
            case 34:
            case 35:
            case 36:
                return true;
            default:
                return false;
        }
    }

    private static boolean isSpecialChar(char c) {
        switch (c) {
            case '(':
            case ')':
            case ',':
            case ':':
            case '[':
            case ']':
            case '{':
            case '}':
                return true;
            default:
                return false;
        }
    }

    private static boolean isDDLStatementStartToken(String firstToken) {
        if (firstToken == null || firstToken.length() < 4 || firstToken.length() > 7) {
            return false;
        }
        switch (firstToken.toUpperCase()) {
            case "CREATE":
            case "ALTER":
            case "DROP":
            case "GRANT":
            case "REVOKE":
            case "ANALYZE":
            case "AUDIT":
            case "COMMENT":
                return true;
            default:
                return false;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/SQLTokenizer$OracleConvertedSqlSequence.class */
    public static class OracleConvertedSqlSequence {
        private int beginTokenIndex;
        private int endTokenIndex;
        private String convertedSqlSequence;

        public OracleConvertedSqlSequence(int beginTokenIndex, int endTokenIndex, String convertedSqlSequence) {
            this.beginTokenIndex = beginTokenIndex;
            this.endTokenIndex = endTokenIndex;
            this.convertedSqlSequence = convertedSqlSequence;
        }

        public String getConvertedSqlSequence() {
            return this.convertedSqlSequence;
        }

        public int getLength() {
            return this.endTokenIndex - this.beginTokenIndex;
        }

        public int getBeginTokenIndex() {
            return this.beginTokenIndex;
        }

        public int getEndTokenIndex() {
            return this.endTokenIndex;
        }
    }

    /* JADX WARN: Type inference failed for: r0v3, types: [int[], int[][]] */
    /* JADX WARN: Type inference failed for: r0v5, types: [int[], int[][]] */
    static {
        PARSER_STATE_NAME[0] = "BASE";
        PARSER_STATE_NAME[1] = "DOUBLE_QUOTED_STRING";
        PARSER_STATE_NAME[2] = "SINGLE_QUOTED_STRING";
        PARSER_STATE_NAME[3] = "DASH_COMMENT_BEGIN";
        PARSER_STATE_NAME[4] = "DASH_COMMENT";
        PARSER_STATE_NAME[5] = "SLASH_COMMENT_BEGIN";
        PARSER_STATE_NAME[6] = "SLASH_COMMENT";
        PARSER_STATE_NAME[7] = "SLASH_COMMENT_END";
        PARSER_STATE_NAME[8] = "BIND_1_BEGIN";
        PARSER_STATE_NAME[9] = "BIND_1";
        PARSER_STATE_NAME[10] = "BIND_2";
        PARSER_STATE_NAME[11] = "NTICK";
        PARSER_STATE_NAME[12] = "NTICK_2";
        PARSER_STATE_NAME[22] = "NTICK_END";
        PARSER_STATE_NAME[14] = "QTICK";
        PARSER_STATE_NAME[15] = "QTICK_2";
        PARSER_STATE_NAME[16] = "QTICK_DELIMITER";
        PARSER_STATE_NAME[17] = "QTICK_3";
        PARSER_STATE_NAME[18] = "QTICK_4";
        PARSER_STATE_NAME[19] = "QTICK_END";
        PARSER_STATE_NAME[20] = "UTICK";
        PARSER_STATE_NAME[21] = "UTICK_2";
        PARSER_STATE_NAME[22] = "UTICK_END";
        PARSER_STATE_NAME[23] = "J_SON";
        PARSER_STATE_NAME[24] = "JS_ON";
        PARSER_STATE_NAME[25] = "JSO_N";
        PARSER_STATE_NAME[26] = "JSON_";
        PARSER_STATE_NAME[27] = "JSON__OBJECT";
        PARSER_STATE_NAME[28] = "JSON_O_BJECT";
        PARSER_STATE_NAME[29] = "JSON_OB_JECT";
        PARSER_STATE_NAME[30] = "JSON_OBJ_ECT";
        PARSER_STATE_NAME[31] = "JSON_OBJE_CT";
        PARSER_STATE_NAME[32] = "JSON_OBJEC_T";
        PARSER_STATE_NAME[33] = "JSON_OBJECT_";
        PARSER_STATE_NAME[34] = "JSON_KEY";
        PARSER_STATE_NAME[35] = "JSON_KEY_END";
        PARSER_STATE_NAME[36] = "JSON_VALUE";
        PARSER_STATE_NAME[37] = "JSON_KEY_BIND_BEGIN";
        PARSER_STATE_NAME[38] = "JSON_VALUE_BIND_BEGIN";
        PARSER_STATE_NAME[39] = "JSON_KEY_BIND";
        PARSER_STATE_NAME[40] = "JSON_VALUE_BIND";
        PARSER_STATE_NAME[42] = "SINGLE_QUOTED_STRING_JSON_KEY";
        PARSER_STATE_NAME[44] = "SINGLE_QUOTED_STRING_JSON_VALUE";
        PARSER_STATE_NAME[41] = "DOUBLE_QUOTED_STRING_JSON_KEY";
        PARSER_STATE_NAME[43] = "DOUBLE_QUOTED_STRING_JSON_VALUE";
        PARSER_STATE_NAME[45] = "OPEN_BRACKET";
        PARSER_STATE_NAME[46] = "BLOCK_ESCAPE_START";
        PARSER_STATE_NAME[47] = "BLOCK_ESCAPE_ESCAPING";
        PARSER_STATE_NAME[48] = "BLOCK_ESCAPE_END";
        PARSER_STATE_NAME[49] = "TOKEN";
        PARSER_STATE_NAME[50] = "OTHER";
        int[] base = copy(basic);
        base[32] = 0;
        base[44] = 0;
        base[13] = 0;
        base[9] = 0;
        base[10] = 0;
        base[34] = 1;
        base[39] = 2;
        base[45] = 3;
        base[47] = 5;
        base[58] = 8;
        base[63] = 10;
        base[78] = 11;
        base[110] = 11;
        base[81] = 14;
        base[113] = 14;
        base[85] = 20;
        base[117] = 20;
        base[106] = 23;
        base[74] = 23;
        base[40] = 0;
        base[41] = 0;
        base[123] = 45;
        base[125] = 0;
        base[91] = 0;
        base[93] = 0;
        int[] openBracket = copy(base);
        openBracket[92] = 46;
        int[] blockEscapeStart = newArray(basic.length, 46);
        blockEscapeStart[92] = 47;
        int[] blockEscapeEscaping = newArray(basic.length, 46);
        blockEscapeEscaping[125] = 48;
        int[] blockEscapeEnd = copy(base);
        int[] token = copy(base);
        token[46] = 49;
        token[78] = 49;
        token[110] = 49;
        token[81] = 49;
        token[113] = 49;
        token[85] = 49;
        token[117] = 49;
        token[106] = 49;
        token[74] = 49;
        int[] other = copy(base);
        int[] doubleQuotedString = newArray(basic.length, 1);
        doubleQuotedString[34] = 0;
        int[] singleQuotedString = newArray(basic.length, 2);
        singleQuotedString[39] = 0;
        int[] dashComment = copy(base);
        dashComment[45] = 4;
        int[] dashCommentEnd = newArray(basic.length, 4);
        dashCommentEnd[10] = 0;
        int[] slashCommentBegin = copy(base);
        slashCommentBegin[42] = 6;
        int[] slashComment = newArray(basic.length, 6);
        slashComment[42] = 7;
        int[] slashCommentEnd = newArray(basic.length, 6);
        slashCommentEnd[42] = 7;
        slashCommentEnd[47] = 0;
        int[] bind1Begin = copyReplacing(basic, 49, 9);
        bind1Begin[32] = 8;
        bind1Begin[13] = 8;
        bind1Begin[9] = 8;
        bind1Begin[10] = 8;
        bind1Begin[123] = 45;
        bind1Begin[39] = 2;
        bind1Begin[34] = 1;
        int[] bind1 = copyReplacing(basic, 49, 9);
        bind1[32] = 0;
        bind1[44] = 0;
        bind1[41] = 0;
        int[] bind2 = copy(base);
        int[] ntick = copy(token);
        ntick[39] = 12;
        ntick[113] = 14;
        ntick[81] = 14;
        int[] ntick2 = newArray(basic.length, 12);
        ntick2[39] = 13;
        int[] ntickEnd = newArray(basic.length, 0);
        ntickEnd[39] = 12;
        int[] qtick = copy(token);
        qtick[39] = 15;
        int[] qtick2 = newArray(basic.length, 16);
        int[] qtick3 = newArray(basic.length, 17);
        int[] qtick4 = newArray(basic.length, 17);
        qtick4[39] = 19;
        int[] utick = copy(token);
        utick[39] = 21;
        int[] utick2 = newArray(basic.length, 21);
        utick2[39] = 22;
        int[] utickEnd = newArray(basic.length, 0);
        utickEnd[39] = 21;
        int[] j_son = copy(token);
        j_son[115] = 24;
        j_son[83] = 24;
        int[] js_on = copy(token);
        js_on[111] = 25;
        js_on[79] = 25;
        int[] jso_n = copy(token);
        jso_n[110] = 26;
        jso_n[78] = 26;
        int[] json_ = copyReplacing(base, 0, 34);
        json_[95] = 27;
        json_[123] = 34;
        int[] json__object = copy(token);
        json__object[111] = 28;
        json__object[79] = 28;
        int[] json_o_bject = copy(token);
        json_o_bject[98] = 29;
        json_o_bject[66] = 29;
        int[] json_ob_ject = copy(token);
        json_ob_ject[106] = 30;
        json_ob_ject[74] = 30;
        int[] json_obj_ect = copy(token);
        json_obj_ect[101] = 31;
        json_obj_ect[69] = 31;
        int[] json_obje_ct = copy(token);
        json_obje_ct[99] = 32;
        json_obje_ct[67] = 32;
        int[] json_objec_t = copy(token);
        json_objec_t[116] = 33;
        json_objec_t[84] = 33;
        int[] json_object_ = copyReplacing(base, 0, 34);
        int[] jsonKey = copyReplacing(base, 0, 34);
        jsonKey[58] = 37;
        jsonKey[63] = 39;
        jsonKey[34] = 41;
        jsonKey[39] = 42;
        jsonKey[45] = 3;
        jsonKey[47] = 5;
        jsonKey[123] = 34;
        int[] jsonKeyEnd = copyReplacing(jsonKey, 34, 35);
        jsonKeyEnd[58] = 36;
        int[] jsonValue = copyReplacing(jsonKey, 34, 36);
        jsonValue[58] = 38;
        jsonValue[63] = 40;
        int[] singleQuotedStringJsonKey = newArray(basic.length, 42);
        singleQuotedStringJsonKey[39] = 35;
        int[] singleQuotedStringJsonValue = newArray(basic.length, 44);
        singleQuotedStringJsonValue[39] = 36;
        int[] doubleQuotedStringJsonKey = newArray(basic.length, 41);
        doubleQuotedStringJsonKey[34] = 35;
        int[] doubleQuotedStringJsonValue = newArray(basic.length, 43);
        doubleQuotedStringJsonValue[34] = 36;
        int[] jsonKeyBindBegin = copyReplacing(basic, 49, 39);
        jsonKeyBindBegin[32] = 34;
        jsonKeyBindBegin[44] = 34;
        jsonKeyBindBegin[58] = 36;
        int[] jsonValueBindBegin = copyReplacing(basic, 49, 40);
        jsonValueBindBegin[32] = 36;
        jsonValueBindBegin[44] = 36;
        int[] jsonKeyBind = copyReplacing(jsonKey, 49, 39);
        jsonKeyBind[32] = 35;
        jsonKeyBind[44] = 34;
        jsonKeyBind[58] = 36;
        jsonKeyBind[78] = 39;
        jsonKeyBind[110] = 39;
        jsonKeyBind[81] = 39;
        jsonKeyBind[113] = 39;
        jsonKeyBind[85] = 39;
        jsonKeyBind[117] = 39;
        jsonKeyBind[106] = 39;
        jsonKeyBind[74] = 39;
        int[] jsonValueBind = copyReplacing(basic, 49, 40);
        jsonValueBind[32] = 36;
        jsonValueBind[44] = 34;
        jsonValueBind[93] = 36;
        jsonValueBind[125] = 36;
        TRANSITION[0] = base;
        TRANSITION[49] = token;
        TRANSITION[50] = other;
        TRANSITION[45] = openBracket;
        TRANSITION[46] = blockEscapeStart;
        TRANSITION[47] = blockEscapeEscaping;
        TRANSITION[48] = blockEscapeEnd;
        TRANSITION[1] = doubleQuotedString;
        TRANSITION[2] = singleQuotedString;
        TRANSITION[3] = dashComment;
        TRANSITION[4] = dashCommentEnd;
        TRANSITION[5] = slashCommentBegin;
        TRANSITION[6] = slashComment;
        TRANSITION[7] = slashCommentEnd;
        TRANSITION[8] = bind1Begin;
        TRANSITION[9] = bind1;
        TRANSITION[10] = bind2;
        TRANSITION[11] = ntick;
        TRANSITION[12] = ntick2;
        TRANSITION[13] = ntickEnd;
        TRANSITION[14] = qtick;
        TRANSITION[15] = qtick2;
        TRANSITION[16] = qtick3;
        TRANSITION[17] = qtick3;
        TRANSITION[18] = qtick4;
        TRANSITION[19] = base;
        TRANSITION[20] = utick;
        TRANSITION[21] = utick2;
        TRANSITION[22] = utickEnd;
        TRANSITION[23] = j_son;
        TRANSITION[24] = js_on;
        TRANSITION[25] = jso_n;
        TRANSITION[26] = json_;
        TRANSITION[27] = json__object;
        TRANSITION[28] = json_o_bject;
        TRANSITION[29] = json_ob_ject;
        TRANSITION[30] = json_obj_ect;
        TRANSITION[31] = json_obje_ct;
        TRANSITION[32] = json_objec_t;
        TRANSITION[33] = json_object_;
        TRANSITION[34] = jsonKey;
        TRANSITION[35] = jsonKeyEnd;
        TRANSITION[36] = jsonValue;
        TRANSITION[37] = jsonKeyBindBegin;
        TRANSITION[38] = jsonValueBindBegin;
        TRANSITION[39] = jsonKeyBind;
        TRANSITION[40] = jsonValueBind;
        TRANSITION[42] = singleQuotedStringJsonKey;
        TRANSITION[44] = singleQuotedStringJsonValue;
        TRANSITION[41] = doubleQuotedStringJsonKey;
        TRANSITION[43] = doubleQuotedStringJsonValue;
        int[] actionNone = newArray(51, 0);
        int[] actionDoubleQuotedString = copyReplacing(actionNone, 0, 10);
        actionDoubleQuotedString[1] = 0;
        int[] actionSingleQuotedString = copyReplacing(actionNone, 0, 9);
        actionSingleQuotedString[2] = 0;
        int[] actionDashCommentBegin = copyReplacing(actionNone, 0, 2);
        actionDashCommentBegin[50] = 0;
        actionDashCommentBegin[4] = 0;
        int[] actionDashComment = copy(actionNone);
        actionDashComment[0] = 8;
        int[] actionSlashCommentBegin = copyReplacing(actionNone, 0, 2);
        actionSlashCommentBegin[50] = 0;
        actionSlashCommentBegin[6] = 0;
        int[] actionSlashCommentEnd = copyReplacing(actionNone, 0, 8);
        int[] actionBind1Begin = copy(actionNone);
        actionBind1Begin[45] = 2;
        actionBind1Begin[2] = 2;
        actionBind1Begin[1] = 2;
        int[] actionBind1 = copyReplacing(actionNone, 0, 3);
        actionBind1[9] = 0;
        int[] actionBind2 = copyReplacing(actionNone, 0, 3);
        int[] actionNtick = copy(actionNone);
        actionNtick[0] = 1;
        actionNtick[34] = 1;
        actionNtick[35] = 1;
        actionNtick[36] = 1;
        actionNtick[50] = 1;
        int[] actionNtickEnd = copy(actionNone);
        actionNtickEnd[0] = 6;
        actionNtickEnd[34] = 6;
        actionNtickEnd[35] = 6;
        actionNtickEnd[36] = 6;
        int[] actionQtick = copy(actionNone);
        actionQtick[0] = 1;
        actionQtick[34] = 1;
        actionQtick[35] = 1;
        actionQtick[36] = 1;
        actionQtick[50] = 1;
        int[] actionQtick2 = copy(actionNone);
        actionQtick2[16] = 5;
        int[] actionQtick4 = copy(actionNone);
        actionQtick4[19] = 4;
        int[] actionUtick = copy(actionNone);
        actionUtick[0] = 1;
        actionUtick[34] = 1;
        actionUtick[35] = 1;
        actionUtick[36] = 1;
        actionUtick[50] = 1;
        int[] actionUtickEnd = copy(actionNone);
        actionUtickEnd[0] = 7;
        actionUtickEnd[34] = 7;
        actionUtickEnd[35] = 7;
        actionUtickEnd[36] = 7;
        int[] actionJ_son = copy(actionNone);
        actionJ_son[0] = 1;
        actionJ_son[50] = 1;
        actionJ_son[2] = 1;
        actionJ_son[1] = 1;
        actionJ_son[3] = 1;
        actionJ_son[5] = 1;
        actionJ_son[8] = 1;
        actionJ_son[10] = 1;
        int[] actionJs_on = copy(actionJ_son);
        int[] actionJso_n = copy(actionJ_son);
        int[] actionJson_ = copy(actionJ_son);
        actionJson_[34] = 1;
        actionJson_[36] = 1;
        int[] actionJson__object = copy(actionJ_son);
        int[] actionJson_o_bject = copy(actionJ_son);
        int[] actionJson_ob_ject = copy(actionJ_son);
        int[] actionJson_obj_ect = copy(actionJ_son);
        int[] actionJson_obje_ct = copy(actionJ_son);
        int[] actionJson_objec_t = copy(actionJ_son);
        int[] actionJson_object_ = copy(actionJ_son);
        actionJson_object_[34] = 1;
        actionJson_object_[36] = 1;
        int[] actionJsonKeyBind = copy(actionNone);
        actionJsonKeyBind[34] = 3;
        actionJsonKeyBind[35] = 3;
        int[] actionJsonValueBind = copyReplacing(actionNone, 0, 3);
        actionJsonValueBind[40] = 0;
        int[] actionDoubleQuotedStringJsonKey = copy(actionNone);
        actionDoubleQuotedStringJsonKey[35] = 10;
        int[] actionSingleQuotedStringJsonKey = copyReplacing(actionDoubleQuotedStringJsonKey, 10, 9);
        int[] actionDoubleQuotedStringJsonValue = copy(actionNone);
        actionDoubleQuotedStringJsonValue[34] = 10;
        actionDoubleQuotedStringJsonValue[36] = 10;
        int[] actionSingleQuotedStringJsonValue = copyReplacing(actionDoubleQuotedStringJsonValue, 10, 9);
        int[] actionToken = copyReplacing(actionNone, 0, 1);
        actionToken[49] = 0;
        int[] actionOther = copyReplacing(actionNone, 0, 2);
        actionToken[50] = 0;
        int[] actionOpenBracket = copyReplacing(actionNone, 0, 11);
        actionOpenBracket[46] = 0;
        int[] actionBlockEscapeStart = copy(actionNone);
        int[] actionOpenBracketEscapedEscaping = copy(actionNone);
        int[] actionBlockEscapeEnd = copyReplacing(actionNone, 0, 12);
        ACTION[0] = actionNone;
        ACTION[1] = actionDoubleQuotedString;
        ACTION[2] = actionSingleQuotedString;
        ACTION[3] = actionDashCommentBegin;
        ACTION[4] = actionDashComment;
        ACTION[5] = actionSlashCommentBegin;
        ACTION[6] = actionNone;
        ACTION[7] = actionSlashCommentEnd;
        ACTION[8] = actionBind1Begin;
        ACTION[9] = actionBind1;
        ACTION[10] = actionBind2;
        ACTION[11] = actionNtick;
        ACTION[12] = actionNone;
        ACTION[13] = actionNtickEnd;
        ACTION[14] = actionQtick;
        ACTION[15] = actionQtick2;
        ACTION[16] = actionNone;
        ACTION[17] = actionNone;
        ACTION[18] = actionQtick4;
        ACTION[19] = actionNone;
        ACTION[20] = actionUtick;
        ACTION[21] = actionNone;
        ACTION[22] = actionUtickEnd;
        ACTION[23] = actionJ_son;
        ACTION[24] = actionJs_on;
        ACTION[25] = actionJso_n;
        ACTION[26] = actionJson_;
        ACTION[27] = actionJson__object;
        ACTION[28] = actionJson_o_bject;
        ACTION[29] = actionJson_ob_ject;
        ACTION[30] = actionJson_obj_ect;
        ACTION[31] = actionJson_obje_ct;
        ACTION[32] = actionJson_objec_t;
        ACTION[33] = actionJson_object_;
        ACTION[34] = actionNone;
        ACTION[35] = actionNone;
        ACTION[36] = actionNone;
        ACTION[37] = actionNone;
        ACTION[38] = actionNone;
        ACTION[39] = actionJsonKeyBind;
        ACTION[40] = actionJsonValueBind;
        ACTION[41] = actionDoubleQuotedStringJsonKey;
        ACTION[42] = actionSingleQuotedStringJsonKey;
        ACTION[43] = actionDoubleQuotedStringJsonValue;
        ACTION[44] = actionSingleQuotedStringJsonValue;
        ACTION[49] = actionToken;
        ACTION[50] = actionOther;
        ACTION[45] = actionOpenBracket;
        ACTION[46] = actionBlockEscapeStart;
        ACTION[47] = actionOpenBracketEscapedEscaping;
        ACTION[48] = actionBlockEscapeEnd;
    }

    private static final int[] copy(int[] a) {
        int[] r = new int[a.length];
        System.arraycopy(a, 0, r, 0, a.length);
        return r;
    }

    private static final int[] copyReplacing(int[] a, int source, int target) {
        int[] r = new int[a.length];
        for (int i = 0; i < r.length; i++) {
            int t = a[i];
            if (t == source) {
                r[i] = target;
            } else {
                r[i] = t;
            }
        }
        return r;
    }

    private static final int[] newArray(int length, int value) {
        int[] r = new int[length];
        for (int i = 0; i < length; i++) {
            r[i] = value;
        }
        return r;
    }
}
