package oracle.jdbc.driver;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.nio.ByteOrder;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.DatabaseFunction;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.BreakNetException;
import oracle.net.ns.Communication;
import oracle.net.ns.NIONSDataChannel;
import oracle.net.ns.NetException;
import oracle.net.ns.SQLnetDef;
import oracle.net.ns.SessionAtts;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CMAREngineNIO.class */
class T4CMAREngineNIO extends T4CMAREngine {
    private static final String CLASS_NAME = T4CMAREngineNIO.class.getName();
    private NIONSDataChannel dataChannel;
    SessionAtts sAtts;
    private boolean bytesReadyToGo = false;
    private boolean isPipelineResponse = false;
    private T4CConnection conn;

    T4CMAREngineNIO(Communication net, T4CConnection conn) throws SQLException, IOException {
        if (net == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0205).fillInStackTrace());
        }
        this.net = net;
        this.sAtts = net.getSessionAttributes();
        this.conn = conn;
        this.dataChannel = this.sAtts.dataChannel;
        this.types = new T4CTypeRep(this, true);
        this.types.setRep((byte) 1, (byte) 0);
        this.sAtts.setByteOrder(ByteOrder.LITTLE_ENDIAN);
        this.sAtts.payloadDataBufferForRead.order(ByteOrder.LITTLE_ENDIAN);
        this.sAtts.payloadDataBufferForWrite.order(ByteOrder.LITTLE_ENDIAN);
    }

    private static void valueToUNV(long value, byte[] buffer) {
        buffer[0] = 0;
        if (value == 0) {
            return;
        }
        int indexInBuffer = 0;
        boolean zeros = true;
        boolean negative = value < 0;
        long absoluteValue = negative ? -value : value;
        for (int i = 0; i < 8; i++) {
            byte b = (byte) ((absoluteValue >>> (8 * (7 - i))) & 255);
            if (!zeros || b != 0) {
                zeros = false;
                indexInBuffer++;
                buffer[indexInBuffer] = b;
            }
        }
        buffer[0] = (byte) indexInBuffer;
        if (negative) {
            buffer[0] = (byte) (buffer[0] | Byte.MIN_VALUE);
        }
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalSB1(byte value) throws IOException {
        prepareForMarshall(1);
        this.sAtts.payloadDataBufferForWrite.put(value);
        this.bytesReadyToGo = true;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalUB1(short value) throws IOException {
        marshalSB1((byte) value);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalSB2(short value) throws IOException {
        prepareForMarshall(3);
        if (this.types.rep[1] != 1) {
            this.sAtts.payloadDataBufferForWrite.putShort(value);
            return;
        }
        if (value == 0) {
            this.sAtts.payloadDataBufferForWrite.put((byte) 0);
            return;
        }
        boolean negative = value < 0;
        short absoluteValue = (short) (negative ? -value : value);
        if (absoluteValue <= 255) {
            if (negative) {
                this.sAtts.payloadDataBufferForWrite.put((byte) -127);
            } else {
                this.sAtts.payloadDataBufferForWrite.put((byte) 1);
            }
            this.sAtts.payloadDataBufferForWrite.put((byte) absoluteValue);
            return;
        }
        if (negative) {
            this.sAtts.payloadDataBufferForWrite.put((byte) -126);
        } else {
            this.sAtts.payloadDataBufferForWrite.put((byte) 2);
        }
        this.sAtts.payloadDataBufferForWrite.putShort(absoluteValue);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalUB2(int value) throws IOException {
        prepareForMarshall(3);
        if (this.types.rep[1] != 1) {
            this.sAtts.payloadDataBufferForWrite.putShort((short) value);
        } else if (value == 0) {
            this.sAtts.payloadDataBufferForWrite.put((byte) 0);
        } else if (value <= 255) {
            this.sAtts.payloadDataBufferForWrite.put((byte) 1);
            this.sAtts.payloadDataBufferForWrite.put((byte) value);
        } else {
            this.sAtts.payloadDataBufferForWrite.put((byte) 2);
            this.sAtts.payloadDataBufferForWrite.putShort((short) value);
        }
        this.bytesReadyToGo = true;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    void marshalNativeUB2(short value, boolean isLSB) throws IOException {
        try {
            byte rep = this.types.getRep((byte) 1);
            byte newRep = (byte) (0 | (isLSB ? 2 : 0));
            this.types.setRep((byte) 1, newRep);
            marshalUB2(value);
            this.types.setRep((byte) 1, rep);
        } catch (SQLException e) {
        }
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalSB4(int value) throws IOException {
        prepareForMarshall(5);
        if (this.types.rep[2] != 1) {
            this.sAtts.payloadDataBufferForWrite.putInt(value);
        } else if (value == 0) {
            this.sAtts.payloadDataBufferForWrite.put((byte) 0);
        } else {
            boolean negative = value < 0;
            int absoluteValue = negative ? -value : value;
            if (absoluteValue <= 255) {
                if (!negative) {
                    this.sAtts.payloadDataBufferForWrite.put((byte) 1);
                } else {
                    this.sAtts.payloadDataBufferForWrite.put((byte) -127);
                }
                this.sAtts.payloadDataBufferForWrite.put((byte) absoluteValue);
            } else if (absoluteValue <= 65535) {
                if (!negative) {
                    this.sAtts.payloadDataBufferForWrite.put((byte) 2);
                } else {
                    this.sAtts.payloadDataBufferForWrite.put((byte) -126);
                }
                this.sAtts.payloadDataBufferForWrite.putShort((short) absoluteValue);
            } else if (absoluteValue < 26) {
                valueToUNV(value, this.tmpBuffer10);
                this.sAtts.payloadDataBufferForWrite.put(this.tmpBuffer10, 0, (this.tmpBuffer10[0] & Byte.MAX_VALUE) + 1);
            } else {
                if (!negative) {
                    this.sAtts.payloadDataBufferForWrite.put((byte) 4);
                } else {
                    this.sAtts.payloadDataBufferForWrite.put((byte) -124);
                }
                this.sAtts.payloadDataBufferForWrite.putInt(absoluteValue);
            }
        }
        this.bytesReadyToGo = true;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalUB4(long value) throws IOException {
        prepareForMarshall(5);
        if (this.types.rep[2] != 1) {
            this.sAtts.payloadDataBufferForWrite.putInt((int) value);
        } else {
            boolean negative = value < 0;
            long absoluteValue = negative ? -value : value;
            if (absoluteValue == 0) {
                this.sAtts.payloadDataBufferForWrite.put((byte) 0);
            } else if (absoluteValue <= 255) {
                this.sAtts.payloadDataBufferForWrite.put((byte) 1);
                this.sAtts.payloadDataBufferForWrite.put((byte) absoluteValue);
            } else if (absoluteValue <= 65535) {
                this.sAtts.payloadDataBufferForWrite.put((byte) 2);
                this.sAtts.payloadDataBufferForWrite.putShort((short) absoluteValue);
            } else if (absoluteValue <= 16777215) {
                valueToUNV(absoluteValue, this.tmpBuffer10);
                this.sAtts.payloadDataBufferForWrite.put(this.tmpBuffer10, 0, this.tmpBuffer10[0] + 1);
            } else {
                this.sAtts.payloadDataBufferForWrite.put((byte) 4);
                this.sAtts.payloadDataBufferForWrite.putInt((int) absoluteValue);
            }
        }
        this.bytesReadyToGo = true;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalSB8(long value) throws IOException {
        prepareForMarshall(9);
        if (this.types.rep[3] != 1) {
            throw new IOException("TODO: SB8 in UNV representation only");
        }
        valueToUNV(value, this.tmpBuffer10);
        this.sAtts.payloadDataBufferForWrite.put(this.tmpBuffer10, 0, (this.tmpBuffer10[0] & Byte.MAX_VALUE) + 1);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalUB8(long value) throws IOException {
        prepareForMarshall(9);
        if (this.types.rep[3] != 1) {
            this.sAtts.payloadDataBufferForWrite.putLong(value);
        } else if (value == 0) {
            this.sAtts.payloadDataBufferForWrite.put((byte) 0);
        } else {
            boolean zeros = true;
            int indexInBuffer = 0;
            for (int i = 0; i < 8; i++) {
                byte b = (byte) ((value >>> (8 * (7 - i))) & 255);
                if (!zeros || b != 0) {
                    zeros = false;
                    indexInBuffer++;
                    this.tmpBuffer10[indexInBuffer] = b;
                }
            }
            this.tmpBuffer10[0] = (byte) indexInBuffer;
            this.sAtts.payloadDataBufferForWrite.put(this.tmpBuffer10, 0, this.tmpBuffer10[0] + 1);
        }
        this.bytesReadyToGo = true;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalB1Array(byte[] value) throws IOException {
        marshalB1Array(value, 0, value.length);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final void marshalB1Array(byte[] value, int off, int len) throws IOException {
        prepareForMarshall(1);
        int bytesWrittenSoFar = 0;
        while (bytesWrittenSoFar < len) {
            prepareForMarshall(len - bytesWrittenSoFar);
            int bytesToWriteInNextCall = Math.min(this.sAtts.payloadDataBufferForWrite.remaining(), len - bytesWrittenSoFar);
            this.sAtts.payloadDataBufferForWrite.put(value, off + bytesWrittenSoFar, bytesToWriteInNextCall);
            this.bytesReadyToGo = true;
            bytesWrittenSoFar += bytesToWriteInNextCall;
        }
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final byte unmarshalSB1() throws SQLException, IOException {
        byte result = (byte) unmarshalSB2();
        return result;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final short unmarshalUB1() throws SQLException, IOException {
        prepareForUnmarshall();
        short value = (short) (this.sAtts.payloadDataBufferForRead.get() & 255);
        return value;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final short unmarshalSB2() throws SQLException, IOException {
        prepareForUnmarshall();
        if (this.types.rep[1] != 1 && this.sAtts.payloadDataBufferForRead.remaining() >= 2) {
            return this.sAtts.payloadDataBufferForRead.getShort();
        }
        short result = (short) unmarshalUB2();
        return result;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final int unmarshalUB2() throws SQLException, IOException {
        prepareForUnmarshall();
        int value = (int) buffer2Value((byte) 1);
        return value & 65535;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final int unmarshalSB4() throws SQLException, IOException {
        prepareForUnmarshall();
        if (this.types.rep[2] != 1 && this.sAtts.payloadDataBufferForRead.remaining() >= 4) {
            return this.sAtts.payloadDataBufferForRead.getInt();
        }
        return (int) buffer2Value((byte) 2);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final int unmarshalSB4(byte[] buffer) throws SQLException, IOException {
        prepareForUnmarshall();
        long value = buffer2Value((byte) 2, new ByteArrayInputStream(buffer));
        return (int) value;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final long unmarshalSB8() throws SQLException, IOException {
        prepareForUnmarshall();
        long value = buffer2Value((byte) 3);
        return value;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final long unmarshalUB4() throws SQLException, IOException {
        return unmarshalSB4() & SQLnetDef.NSPDDLSLMAX;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    byte[] unmarshalNBytes(int n) throws SQLException, IOException {
        byte[] tmpBuffer = new byte[n];
        getNBytes(tmpBuffer, 0, n);
        return tmpBuffer;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    int unmarshalNBytes(byte[] buf, int off, int n) throws SQLException, IOException {
        return getNBytes(buf, off, n);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    int getNBytes(byte[] buf, int off, int len) throws SQLException, IOException {
        if (off + len > buf.length) {
            len = buf.length - off;
        }
        int iPosition = 0;
        while (true) {
            int bytesReadSoFar = iPosition;
            if (bytesReadSoFar < len) {
                if (!this.sAtts.payloadDataBufferForRead.hasRemaining()) {
                    prepareForUnmarshall();
                }
                int pos = this.sAtts.payloadDataBufferForRead.position();
                this.sAtts.payloadDataBufferForRead.get(buf, off + bytesReadSoFar, Math.min(len - bytesReadSoFar, this.sAtts.payloadDataBufferForRead.remaining()));
                iPosition = bytesReadSoFar + (this.sAtts.payloadDataBufferForRead.position() - pos);
            } else {
                return bytesReadSoFar;
            }
        }
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    byte[] getNBytes(int n) throws SQLException, IOException {
        return unmarshalNBytes(n);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    void skipNBytes(int n) throws SQLException, IOException {
        int bytesReadSoFar = 0;
        this.sAtts.payloadDataBufferForRead.position();
        while (bytesReadSoFar < n) {
            if (this.sAtts.payloadDataBufferForRead.remaining() >= n - bytesReadSoFar) {
                this.sAtts.payloadDataBufferForRead.position(this.sAtts.payloadDataBufferForRead.position() + (n - bytesReadSoFar));
                bytesReadSoFar = n;
            } else {
                if (this.sAtts.payloadDataBufferForRead.remaining() > 0) {
                    bytesReadSoFar += this.sAtts.payloadDataBufferForRead.remaining();
                    this.sAtts.payloadDataBufferForRead.position(this.sAtts.payloadDataBufferForRead.position() + this.sAtts.payloadDataBufferForRead.remaining());
                }
                prepareForUnmarshall();
            }
        }
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    byte[] unmarshalTEXT(int bytes) throws SQLException, IOException {
        byte[] buffer;
        int offset = 0;
        byte[] tmpBuffer = new byte[bytes];
        while (offset < bytes) {
            prepareForUnmarshall();
            tmpBuffer[offset] = this.sAtts.payloadDataBufferForRead.get();
            int i = offset;
            offset++;
            if (tmpBuffer[i] == 0) {
                break;
            }
        }
        int offset2 = offset - 1;
        if (tmpBuffer.length == offset2) {
            buffer = tmpBuffer;
        } else {
            buffer = new byte[offset2];
            System.arraycopy(tmpBuffer, 0, buffer, 0, offset2);
        }
        return buffer;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    final long buffer2Value(byte repOffset) throws SQLException, IOException {
        long j;
        long j2;
        int i;
        int i2;
        long returnValue = 0;
        byte b = this.types.rep[repOffset];
        int numberOfBytes = 0;
        boolean negativeUNV = false;
        if (b == 1) {
            numberOfBytes = this.sAtts.payloadDataBufferForRead.get();
            if (numberOfBytes == 0) {
                return 0L;
            }
            if ((numberOfBytes & 128) > 0) {
                numberOfBytes &= 127;
                negativeUNV = true;
            }
        } else {
            switch (repOffset) {
                case 1:
                    numberOfBytes = 2;
                    break;
                case 2:
                    numberOfBytes = 4;
                    break;
                case 3:
                    numberOfBytes = 8;
                    break;
            }
        }
        if (this.sAtts.payloadDataBufferForRead.remaining() >= numberOfBytes && (numberOfBytes == 1 || numberOfBytes == 2 || numberOfBytes == 4 || numberOfBytes == 8)) {
            if (numberOfBytes == 1) {
                returnValue = this.sAtts.payloadDataBufferForRead.get() & 255;
            } else if (numberOfBytes == 2) {
                returnValue = this.sAtts.payloadDataBufferForRead.getShort() & 65535;
            } else if (numberOfBytes == 4) {
                returnValue = this.sAtts.payloadDataBufferForRead.getInt() & SQLnetDef.NSPDDLSLMAX;
            } else {
                returnValue = this.sAtts.payloadDataBufferForRead.getLong();
            }
        } else {
            unmarshalNBytes(this.tmpBuffer8, 0, numberOfBytes);
            for (int byteOffset = 0; byteOffset < numberOfBytes; byteOffset++) {
                if (b == 1 || b != 2) {
                    j = returnValue;
                    j2 = this.tmpBuffer8[byteOffset] & 255;
                    i = 8;
                    i2 = (numberOfBytes - byteOffset) - 1;
                } else {
                    j = returnValue;
                    j2 = this.tmpBuffer8[byteOffset] & 255;
                    i = 8;
                    i2 = byteOffset;
                }
                returnValue = j | (j2 << (i * i2));
            }
        }
        if (negativeUNV) {
            returnValue *= -1;
        }
        return returnValue;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    void setByteOrder(byte rep) {
        if (rep == 2) {
            this.sAtts.setByteOrder(ByteOrder.LITTLE_ENDIAN);
        } else {
            this.sAtts.setByteOrder(ByteOrder.BIG_ENDIAN);
        }
        if (this.sAtts.payloadDataBufferForRead != null) {
            this.sAtts.payloadDataBufferForRead.order(this.sAtts.getByteOrder());
        }
        if (this.sAtts.payloadDataBufferForWrite != null) {
            this.sAtts.payloadDataBufferForWrite.order(this.sAtts.getByteOrder());
        }
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    boolean sentCancel() {
        return this.conn.sentCancel;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    protected void flush() throws IOException {
        flush(0);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    protected void beginPipelineRequest() throws IOException {
        this.net.enqueueBlockedWrites(true);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    protected boolean endPipelineRequest() throws IOException {
        flush(2048);
        if (!this.net.completeBlockedWrites()) {
            return false;
        }
        this.net.enqueueBlockedWrites(false);
        return true;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    protected void beginPipelineResponse() {
        this.isPipelineResponse = true;
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    protected void endPipelineResponse() throws IOException {
        this.isPipelineResponse = false;
    }

    private void flush(int nsDataFlags) throws IOException {
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "flush", "flush flag={0}", (String) null, (String) null, (Object) Integer.valueOf(nsDataFlags));
        try {
            if (!this.sAtts.isConnected()) {
                throw new NetException(NetException.NOT_CONNECTED);
            }
            if (this.bytesReadyToGo) {
                this.dataChannel.writeDataToSocketChannel(nsDataFlags);
                this.bytesReadyToGo = false;
            }
        } catch (NetException ne) {
            ne.setNetConnectionId(this.sAtts.getNetConnectionId());
            throw ne;
        }
    }

    private void prepareForUnmarshall() throws SQLException, IOException {
        InetAddress localAddr;
        int localPort;
        if (this.bytesReadyToGo) {
            flush(2048);
        }
        if (this.sAtts.payloadDataBufferForRead.hasRemaining()) {
            return;
        }
        if (!this.sAtts.isConnected()) {
            throw new NetException(NetException.NOT_CONNECTED);
        }
        while (!this.sAtts.payloadDataBufferForRead.hasRemaining()) {
            try {
                this.dataChannel.readDataFromSocketChannel();
            } catch (BreakNetException e) {
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "prepareForUnmarshall", "Break received from server. Responding with reset...", null, null, new Object[0]);
                if (!this.isPipelineResponse) {
                    this.net.sendReset();
                }
                throw e;
            } catch (NetException ne) {
                ne.setNetConnectionId(this.sAtts.getNetConnectionId());
                if (ne.isNSControlCommandError()) {
                    clearWriteBuffer();
                }
                if ((ne.getErrorNumber() == 17902 || ne.getErrorNumber() == 17800 || ne.getErrorNumber() == 17909) && this.net.getSessionAttributes().getcOption() != null) {
                    String serverType = oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_VSESSION_TERMINAL_DEFAULT;
                    try {
                        NVPair nvp = new NVFactory().createNVPair(this.net.getSessionAttributes().getcOption().conn_data.toString());
                        NVNavigator nav = new NVNavigator();
                        NVPair connDataNVP = nav.findNVPair(nvp, "CONNECT_DATA");
                        NVPair servernvp = nav.findNVPair(connDataNVP, "SERVER");
                        if (servernvp != null) {
                            serverType = servernvp.getAtom();
                        }
                    } catch (NLException e2) {
                    }
                    try {
                        localAddr = this.net.getSessionAttributes().getNTAdapter().getSocketChannel().socket().getLocalAddress();
                        localPort = this.net.getSessionAttributes().getNTAdapter().getSocketChannel().socket().getLocalPort();
                    } catch (UnsupportedOperationException e3) {
                        localAddr = InetAddress.getLoopbackAddress();
                        localPort = 0;
                    }
                    Object[] objArr = new Object[17];
                    objArr[0] = "client";
                    objArr[1] = localAddr;
                    objArr[2] = String.valueOf(localPort);
                    objArr[3] = this.net.getSessionAttributes().getcOption().host;
                    objArr[4] = String.valueOf(this.net.getSessionAttributes().getcOption().port);
                    objArr[5] = this.net.getSessionAttributes().getcOption().protocol;
                    objArr[6] = this.net.getSessionAttributes().getcOption().service_name;
                    objArr[7] = "client";
                    objArr[8] = serverType;
                    objArr[9] = this.conn.thinVsessionProgram;
                    objArr[10] = this.conn.sessionProperties != null ? this.conn.getServerSessionInfo("AUTH_SERVER_PID") : null;
                    objArr[11] = this.conn.sessionProperties != null ? this.conn.getServerSessionInfo("AUTH_SESSION_ID") : null;
                    objArr[12] = this.conn.sessionProperties != null ? this.conn.getServerSessionInfo("AUTH_SERIAL_NUM") : null;
                    objArr[13] = this.conn.getUserName() != null ? this.conn.getUserName() : null;
                    objArr[14] = this.conn.lastExecutedFunCode != 0 ? DatabaseFunction.valueOfFunctionCode(this.conn.lastExecutedFunCode).getDescription() : null;
                    objArr[15] = this.conn.getNetConnectionId();
                    objArr[16] = this.conn.getSecurityInformation() != null ? ", nne_encryption=" + this.conn.getSecurityInformation().getEncryptionAlgorithm() + ", nne_checksumming=" + this.conn.getSecurityInformation().getChecksummingAlgorithm() + ", authentication=" + this.conn.getSecurityInformation().getAuthenticationAdaptor() : null;
                    throw ((NetException) new NetException(NetException.DATABASE_CONNECTION_LOST, null, false, objArr).initCause(ne).fillInStackTrace());
                }
                throw ne;
            }
        }
    }

    private void prepareForMarshall(int numberBytesToBeWritten) throws IOException {
        if (this.sAtts.payloadDataBufferForWrite.remaining() >= numberBytesToBeWritten) {
            return;
        }
        if (this.bytesReadyToGo) {
            flush();
        }
        this.sAtts.prepareWriteBuffer();
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    void writeZeroCopyIO(byte[] userBuffer, int offset, int length) throws IOException {
        try {
            flush();
            this.net.writeZeroCopyIO(userBuffer, offset, length);
        } catch (NetException ne) {
            ne.setNetConnectionId(this.sAtts.getNetConnectionId());
        }
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    void writeZeroCopyIOHeader(boolean flushBuffer, int lengthInDD, boolean isLastDD) throws IOException {
        if (flushBuffer) {
            try {
                flush();
            } catch (NetException ne) {
                ne.setNetConnectionId(this.sAtts.getNetConnectionId());
                return;
            }
        }
        this.net.writeZeroCopyIOHeader(flushBuffer, lengthInDD, isLastDD);
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    void writeZeroCopyIOData(byte[] userBuffer, int offset, int length) throws IOException {
        try {
            this.net.writeZeroCopyIOData(userBuffer, offset, length);
        } catch (NetException ne) {
            ne.setNetConnectionId(this.sAtts.getNetConnectionId());
        }
    }

    @Override // oracle.jdbc.driver.T4CMAREngine
    void clearWriteBuffer() {
        this.sAtts.prepareWriteBuffer();
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.sAtts.getDiagnosable();
    }
}
