package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_no.class */
public class ErrorMessagesJson_no extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Det oppstod et I/U-unntak"}, new Object[]{"26302", "Året {0} støttes ikke"}, new Object[]{"26303", "Overflyt, for stor verdi: {0}."}, new Object[]{"26304", "Valget støttes ikke (ikke implementert)."}, new Object[]{"26305", "Binær JSON er ugyldig eller skadet."}, new Object[]{"26306", "Binær JSON-versjon støttes ikke: {0}."}, new Object[]{"26307", "Lengden på den UTF-8-kodede nøkkelen kan ikke overskride 256 byte. Følgende nøkkel overskrider denne grensen: {0}."}, new Object[]{"26308", "Angitt JSON er for stor til å kodes som binær JSON. Størrelsen på de kodede bildene kan ikke overskride 2 GB."}, new Object[]{"26309", "Binær JSON er ugyldig eller skadet. Det angitte bildet inneholder bare {0} byte."}, new Object[]{"26310", "Angitt java.time.Period har dager angitt, men Oracle-intervallet for år til måned støtter ikke dager."}, new Object[]{"26311", "Generatoren ble lukket før slutten."}, new Object[]{"26312", "En objektnøkkel må være angitt i denne konteksten."}, new Object[]{"26313", "Ugyldig skriving. En fullstendig verdi er allerede skrevet."}, new Object[]{"26314", "Slutt er ikke tillatt i denne konteksten."}, new Object[]{"26315", "Nøkkel er ikke tillatt i denne konteksten."}, new Object[]{"26316", "Forventet verdi etter nøkkel."}, new Object[]{"26317", "Analysatortilstand må være {0}."}, new Object[]{"26318", "Analysatortilstand kan ikke være {0}."}, new Object[]{"26319", "Analysator må være på en verdi."}, new Object[]{"26320", "{0} er ikke en støttet innpakningstype."}, new Object[]{"26321", "Dette objektet kan ikke endres. Hvis du vil opprette en kopi som kan endres, kan du bruke OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Denne matrisen kan ikke endres. Hvis du vil opprette en kopi som kan endres, kan du bruke OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "JSON-objekt inneholder duplisert nøkkel: {0}."}, new Object[]{"26324", "Kan ikke oppdage koding automatisk, ikke nok tegn."}, new Object[]{"26325", "Forventet EOF-symbol, men fikk {0}."}, new Object[]{"26326", "Uventet tegn, {0}, på linje {1}, i kolonne {2}."}, new Object[]{"26327", "Tegnet {0} var uventet på linje {1}, kolonnen {2}. Forventet: {3}."}, new Object[]{"26328", "Ugyldig symbol, {0}, på linje {1}, i kolonne {2}. Forventede symboler: {3}."}, new Object[]{"26329", "JsonParser#getString() er bare gyldig for analysatortilstandene KEY_NAME, VALUE_STRING, VALUE_NUMBER, men gjeldende analysatortilstand er {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() er bare gyldig for analysatortilstanden VALUE_NUMBER, men gjeldende analysatortilstand er {0}."}, new Object[]{"26331", "JsonParser#getInt() er bare gyldig for analysatortilstanden VALUE_NUMBER, men gjeldende analysatortilstand er {0}."}, new Object[]{"26332", "JsonParser#getLong() er bare gyldig for analysatortilstanden VALUE_NUMBER, men gjeldende analysatortilstand er {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() er bare gyldig for analysatortilstanden VALUE_NUMBER, men gjeldende analysatortilstand er {0}."}, new Object[]{"26334", "JsonParser#getArray() er bare gyldig for analysatortilstanden START_ARRAY, men gjeldende analysatortilstand er {0}."}, new Object[]{"26335", "JsonParser#getObject() er bare gyldig for analysatortilstanden START_OBJECT, men gjeldende analysatortilstand er {0}."}, new Object[]{"26336", "Et tidsstempel med et område støttes ikke. Bare forskjøvne tidssoner støttes."}, new Object[]{"26337", "Objektene og matrisene i JSON-verdien kan ikke nøstes dypere enn {0} nivåer"}, new Object[]{"26338", "Nøklene for et JSON-objekt kan ikke overskride 65 535 byte"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
