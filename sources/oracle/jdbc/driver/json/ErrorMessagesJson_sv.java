package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_sv.class */
public class ErrorMessagesJson_sv extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Ett I/O-undantag inträffade"}, new Object[]{"26302", "Året \"{0}\" stöds inte"}, new Object[]{"26303", "Spill, för stort värde: {0}."}, new Object[]{"26304", "Alternativet stöds inte (inte implementerat)."}, new Object[]{"26305", "Den binära JSON är ogiltig eller skadad."}, new Object[]{"26306", "Den binära JSON-versionen stöds inte: {0}."}, new Object[]{"26307", "Den UTF-8-krypterade nyckeln får inte vara större än 256 byte. Följande nyckel överskrider den gränsen: \"{0}\"."}, new Object[]{"26308", "Den angivna JSON är för stor för att krypteras som en binär JSON. Den krypterade bildstorleken får inte överskrida 2 GB."}, new Object[]{"26309", "Den binära JSON är ogiltig eller skadad. Den angivna bilden innehåller endast {0} byte."}, new Object[]{"26310", "Den angivna java.time.Period har angivna dagar, men Oracles år-till-månad-intervall stöder inte dagar."}, new Object[]{"26311", "Generatorn stängdes före slutet."}, new Object[]{"26312", "En objektnyckel måste anges i den här kontexten."}, new Object[]{"26313", "Ogiltig skrivning. Ett fullständigt värde har redan skrivits."}, new Object[]{"26314", "Slutet är inte tillåtet i den här kontexten."}, new Object[]{"26315", "Nyckeln är inte tillåten i den här kontexten."}, new Object[]{"26316", "Förväntat värde efter nyckel."}, new Object[]{"26317", "Parsertillståndet måste vara {0}."}, new Object[]{"26318", "Parsertillståndet får inte vara {0}."}, new Object[]{"26319", "Parser måste ha ett värde."}, new Object[]{"26320", "\"{0}\" är en wrappertyp som inte stöds."}, new Object[]{"26321", "Det här objektet kan inte ändras. Om du vill skapa en kopia som kan ändras använder du OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Den här uppställningen kan inte ändras. Om du vill skapa en kopia som kan ändras använder du OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "JSON-objektet innehåller dubblettnyckel: {0}."}, new Object[]{"26324", "Kan inte autoavkänna kodning, otillräckligt antal tecken."}, new Object[]{"26325", "Förväntade EOF-token, men fick {0}."}, new Object[]{"26326", "Oväntat tecken ({0}) på raden {1} i kolumnen {2}."}, new Object[]{"26327", "Oväntat tecken ({0}) på raden {1} i kolumnen {2}. Förväntat: {3}."}, new Object[]{"26328", "Ogiltigt token ({0}) på raden {1} i kolumnen {2}. Förväntade token är: {3}."}, new Object[]{"26329", "JsonParser#getString() är endast giltigt för parsertillstånden KEY_NAME, VALUE_STRING, VALUE_NUMBER. Men det aktuella parsertillståndet är {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() är endast giltigt för parsertillståndet VALUE_NUMBER. Men det aktuella parsertillståndet är {0}."}, new Object[]{"26331", "JsonParser#getInt() är endast giltigt för parsertillståndet VALUE_NUMBER. Men det aktuella parsertillståndet är {0}."}, new Object[]{"26332", "JsonParser#getLong() är endast giltigt för parsertillståndet VALUE_NUMBER. Men det aktuella parsertillståndet är {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() är endast giltigt för parsertillståndet VALUE_NUMBER. Men det aktuella parsertillståndet är {0}."}, new Object[]{"26334", "JsonParser#getArray() är endast giltigt för parsertillståndet START_ARRAY. Men det aktuella parsertillståndet är {0}."}, new Object[]{"26335", "JsonParser#getObject() är endast giltigt för parsertillståndet START_OBJECT. Men det aktuella parsertillståndet är {0}."}, new Object[]{"26336", "En tidsstämpel med en region stöds inte. Endast förskjutna tidszoner stöds."}, new Object[]{"26337", "Objekten och uppställningarna i JSON-värdet får inte kapslas djupare än {0} nivåer"}, new Object[]{"26338", "Nycklarna till ett JSON-objekt får inte överstiga 65 535 byte"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
