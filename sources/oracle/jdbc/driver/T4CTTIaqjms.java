package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIaqjms.class */
class T4CTTIaqjms implements Diagnosable {
    private static final String CLASS_NAME = T4CTTIaqjms.class.getName();
    int aqjmshdrpcnt;
    byte[] aqjmshdrprop;
    int aqjmsusrprpcnt;
    byte[] aqjmsuserprop;
    int aqjmsflags = 1;
    T4CMAREngine mar;
    T4CConnection connection;

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.connection.getDiagnosable();
    }

    T4CTTIaqjms(T4CConnection _connection) {
        this.mar = _connection.mare;
        this.connection = _connection;
    }

    void marshal() throws IOException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshal", "aqjmshdrpcnt={0}, aqjmsusrprpcnt={1}, aqjmsflags={2}", (String) null, (Throwable) null, Integer.valueOf(this.aqjmshdrpcnt), Integer.valueOf(this.aqjmsusrprpcnt), Integer.valueOf(this.aqjmsflags));
        this.mar.marshalUB4(this.aqjmshdrpcnt);
        if (this.aqjmshdrprop != null && this.aqjmshdrprop.length != 0) {
            this.mar.marshalSWORD(this.aqjmshdrprop.length);
            this.mar.marshalCLR(this.aqjmshdrprop, 0, this.aqjmshdrprop.length);
        } else {
            this.mar.marshalSWORD(0);
        }
        this.mar.marshalUB4(this.aqjmsusrprpcnt);
        if (this.aqjmsuserprop != null && this.aqjmsuserprop.length != 0) {
            this.mar.marshalSWORD(this.aqjmsuserprop.length);
            this.mar.marshalCLR(this.aqjmsuserprop, 0, this.aqjmsuserprop.length);
        } else {
            this.mar.marshalSWORD(0);
        }
        this.mar.marshalUB4(this.aqjmsflags);
    }

    void receive() throws SQLException, IOException {
        this.aqjmshdrpcnt = (int) this.mar.unmarshalUB4();
        int headerPropLen = this.mar.unmarshalSWORD();
        if (headerPropLen > 0) {
            this.aqjmshdrprop = new byte[headerPropLen];
            int[] intArray = new int[1];
            this.mar.unmarshalCLR(this.aqjmshdrprop, 0, intArray, headerPropLen);
        } else {
            this.aqjmshdrprop = null;
        }
        this.aqjmsusrprpcnt = (int) this.mar.unmarshalUB4();
        int userPropLen = this.mar.unmarshalSWORD();
        if (userPropLen > 0) {
            this.aqjmsuserprop = new byte[userPropLen];
            int[] intArray2 = new int[1];
            this.mar.unmarshalCLR(this.aqjmsuserprop, 0, intArray2, userPropLen);
        } else {
            this.aqjmsuserprop = null;
        }
        this.aqjmsflags = (int) this.mar.unmarshalUB4();
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "receive", "aqjmshdrpcnt={0}, headerPropLen={1}, aqjmsusrprpcnt={2}, userPropLen={3}, aqjmsflags={4}", (String) null, (Throwable) null, Integer.valueOf(this.aqjmshdrpcnt), Integer.valueOf(headerPropLen), Integer.valueOf(this.aqjmsusrprpcnt), Integer.valueOf(userPropLen), Integer.valueOf(this.aqjmsflags));
    }
}
