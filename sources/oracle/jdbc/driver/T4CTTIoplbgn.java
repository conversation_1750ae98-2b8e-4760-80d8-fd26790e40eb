package oracle.jdbc.driver;

import java.io.IOException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoplbgn.class */
final class T4CTTIoplbgn extends T4CTTIfun {
    static final short OCI_PIPELINE_CONT_ON_ERROR = 1;
    static final short OCI_PIPELINE_ABORT_ON_ERROR = 2;
    private int errorSetId;
    private short errorSetMode;
    private short pipelineMode;

    T4CTTIoplbgn(T4CConnection t4cConnection) {
        super(t4cConnection, (byte) 17);
        setFunCode((short) 199);
    }

    void doOPLBGN(int errorSetId, short errorSetMode, short pipelineMode) throws IOException {
        this.errorSetId = errorSetId;
        this.errorSetMode = errorSetMode;
        this.pipelineMode = pipelineMode;
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB2(this.errorSetId);
        this.meg.marshalUB1(this.errorSetMode);
        this.meg.marshalUB1(this.pipelineMode);
    }
}
