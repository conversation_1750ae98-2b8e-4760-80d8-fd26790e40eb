package oracle.jdbc.driver;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VarcharBinder.class */
abstract class VarcharBinder extends Binder {
    String paramVal;
    Binder theVarcharCopyingBinder = null;

    static void init(Binder x) {
        x.type = (short) 9;
        x.bytelen = 0;
    }

    VarcharBinder(String val) {
        init(this);
        this.paramVal = val;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theVarcharCopyingBinder == null) {
            this.theVarcharCopyingBinder = new VarcharCopyingBinder(this.paramVal);
        }
        return this.theVarcharCopyingBinder;
    }
}
