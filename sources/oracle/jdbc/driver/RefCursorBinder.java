package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.Arrays;
import oracle.sql.Datum;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/RefCursorBinder.class */
class RefCursorBinder extends Binder {
    int paramVal;
    Binder theRefCursorCopyingBinder = null;

    RefCursorBinder(int x) {
        init(this);
        this.paramVal = x;
    }

    static void init(Binder x) {
        x.type = (short) 102;
        x.bytelen = 4;
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        byte[] b;
        int offset;
        int val = this.paramVal;
        if (bindUseDBA) {
            long pos = bindData.getPosition();
            bindDataOffsets[bindDataIndex] = pos;
            stmt.lastBoundDataOffsets[bindPosition] = pos;
            b = stmt.connection.methodTempLittleByteBuffer;
            offset = 0;
        } else {
            b = bindBytes;
            offset = byteoffset + 1;
        }
        int len = getDatumBytes(stmt, val, b, offset);
        bindIndicators[indoffset] = 0;
        bindIndicators[lenoffset] = (short) (len + 1);
        if (bindUseDBA) {
            bindData.put(b, 0, len);
            bindDataLengths[bindDataIndex] = len;
            stmt.lastBoundDataLengths[bindPosition] = len;
        } else {
            b[byteoffset] = (byte) len;
        }
        return localCheckSum;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theRefCursorCopyingBinder == null) {
            this.theRefCursorCopyingBinder = new RefCursorCopyingBinder(this.paramVal);
        }
        return this.theRefCursorCopyingBinder;
    }

    private int getDatumBytes(OraclePreparedStatement stmt, int val, byte[] b, int offset) throws SQLException {
        b[offset] = (byte) (val >>> 24);
        b[offset + 1] = (byte) (val >>> 16);
        b[offset + 2] = (byte) (val >>> 8);
        b[offset + 3] = (byte) val;
        return 4;
    }

    @Override // oracle.jdbc.driver.Binder
    Datum getDatum(OraclePreparedStatement stmt, int bindPosition, int formOfUse, int internalType) throws SQLException {
        byte[] b = stmt.connection.methodTempLittleByteBuffer;
        int len = getDatumBytes(stmt, this.paramVal, b, 0);
        return SQLUtil.makeDatum(stmt.connection, Arrays.copyOf(b, len), internalType, (String) null, 0);
    }
}
