package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIochunkinfo.class */
final class T4CTTIochunkinfo extends T4CTTIfun {
    static final int KPDXSHCHUNKINFOFLAGS_KEY = 1;
    static final int KPDXSHCHUNKINFOFLAGS_CHUNK = 2;
    static final int KPDXSHCHUNKINFOFLAGS_SGKEY = 4;
    private byte[] shardingKeyBytes;
    private byte[] superKeyBytes;
    private byte[] chunkNameBytes;
    int chunkInfoFlag;
    static final int INVALID_SHARDING_KEY_ERROR_CODE_PIGGYBACK = 45582;
    static final int INVALID_SHARDING_KEY_ERROR_CODE_FUNCTION = 5016;

    T4CTTIochunkinfo(T4CConnection _conn) {
        super(_conn, (byte) 17);
        this.shardingKeyBytes = null;
        this.superKeyBytes = null;
        this.chunkNameBytes = null;
        this.chunkInfoFlag = 0;
        setFunCode((short) 190);
    }

    void doOCHUNKINFO(String shardingKey, String superShardingKey, String chunkName, boolean isPiggyback) throws SQLException, IOException {
        prepareForRPC(shardingKey, superShardingKey, chunkName);
        if (isPiggyback) {
            setTTCCode((byte) 17);
            doPigRPC();
        } else {
            setTTCCode((byte) 3);
            doRPC();
        }
    }

    private void prepareForRPC(String shardingKey, String superShardingKey, String chunkName) throws SQLException {
        resetBeforeRPC();
        if (shardingKey != null) {
            this.shardingKeyBytes = this.meg.conv.StringToCharBytes(shardingKey);
            this.chunkInfoFlag |= 1;
        }
        if (superShardingKey != null) {
            this.superKeyBytes = this.meg.conv.StringToCharBytes(superShardingKey);
            this.chunkInfoFlag |= 4;
        }
        if (chunkName != null) {
            this.chunkNameBytes = this.meg.conv.StringToCharBytes(chunkName);
            this.chunkInfoFlag |= 2;
        }
    }

    private void resetBeforeRPC() {
        this.shardingKeyBytes = null;
        this.superKeyBytes = null;
        this.chunkInfoFlag = 0;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (this.shardingKeyBytes != null) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.shardingKeyBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.chunkNameBytes != null) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.chunkNameBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.superKeyBytes != null) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.superKeyBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB4(this.chunkInfoFlag);
        if (this.shardingKeyBytes != null) {
            this.meg.marshalCHR(this.shardingKeyBytes);
        }
        if (this.chunkNameBytes != null) {
            this.meg.marshalCHR(this.chunkNameBytes);
        }
        if (this.superKeyBytes != null) {
            this.meg.marshalCHR(this.superKeyBytes);
        }
    }
}
