package oracle.jdbc.driver.json.binary;

import oracle.jdbc.driver.DatabaseError;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonConstants.class */
public final class OsonConstants {
    static final int MAGIC = -11904512;
    static final int MAGIC_VERSION1 = -11904511;
    static final int MAGIC_VERSION3 = -11904509;
    static final int JZNOCT3_FLDNM2_SZ_UB2 = 256;
    static final int JZNOCT2_REL_OFFSET = 1;
    static final int JZNOCT2_INLINE_LEAF = 2;
    static final int JZNOCT2_SLEN_IN_PCODE = 4;
    static final int JZNOCT2_TOT_DISFNM_UB4 = 8;
    static final int JZNOCT2_SHR_SIMP_NODES = 32;
    static final int JZNOCT2_SHR_NODES = 64;
    static final int JZNOCT2_J_SCALAR = 16;
    static final int JZNOCT_HID_USEUB1 = 256;
    static final int JZNOCT_HID_USEUB2 = 512;
    static final int JZNOCT_TOT_DISFNM_UB2 = 1024;
    static final int JZNOCT_FLDNM_SZ_UB4 = 2048;
    static final int JZNOCT_TREE_SZ_UB4 = 4096;
    static final int JZNOCT_TINY_NODE_STAT = 8192;
    public static final int JZNOCT_FID_NO_SORT = 32768;
    static final int JZNOCTUPDHDR_OVFLW_SEG_UB2 = 256;
    static final int JZNOCT_UPD_OVFLW = 117;
    static final int JZNOCT_UPD2_FWA = 118;
    static final int JZNOCT_UPD4_FWA = 119;
    static final int JZNOCT_UPD_XSZ_RES = 120;
    static final int JZNOCT_UPD_OBJ_REF_BITMASK = 131;
    public static final int OPCODE_OFFSET_SIZE_BIT = 32;
    public static final int OPCODE_CHILD_SIZE_BITS = 24;
    public static final int OPCODE_CHILD_NO_SORT_BIT = 4;
    public static final int JZNOCT_OBJECT_TYP = 128;
    public static final int JZNOCT_ARRAY_TYP = 192;
    static final int JZNOCT_JNULL_C = 48;
    static final int JZNOCT_JBOOLT_C = 49;
    static final int JZNOCT_JBOOLF_C = 50;
    static final int JZNOCT_JSUB1L_C = 51;
    static final int JZNOCT_JORA_DTYNUM_C = 52;
    static final int JZNOCT_JORA_DTYNUM_DEC_C = 116;
    static final int JZNOCT_JDTYSTAMP7_C = 125;
    static final int JZNOCT_JDTYSTAMP_TZ_C = 124;
    static final int JZNOCT_JDTYGENID_C = 126;
    static final int JZNOCT_JDTYFLT_C = 127;
    static final int JZNOCT_JSNUM_C = 53;
    static final int JZNOCT_JDTYDB_C = 54;
    static final int JZNOCT_JSUB2L_C = 55;
    static final int JZNOCT_JSUB4L_C = 56;
    static final int JZNOCT_JDTYSTAMP_C = 57;
    static final int JZNOCT_JBINUB2L_C = 58;
    static final int JZNOCT_JBINUB4L_C = 59;
    static final int JZNOCT_JDTYDATE_C = 60;
    static final int JZNOCT_JDTYYM_C = 61;
    static final int JZNOCT_JDTYDS_C = 62;
    static final int JZNOCT_JEXT = 123;
    static final int JZNOCT_JVECTOR = 31489;
    public static final int UB1_MAXSZ = 256;
    public static final int UB2_MAXSZ = 65536;
    static final int JZNOCT_BIN_SRCH_TRIG_LIMIT = 10;
    static final int JZNOCT_STR_OK_5BITS = 31;
    static final int JZNOCT_ORANUM_OK_4BITS = 8;
    static final int JZNOCT_OBJ_FID_REFERRED = 2;
    static final int JZNOCT_UPD_UB4_FWA_SZ = 5;
    public static final byte[] MAGIC_BYTES = {-1, 74, 90};
    public static int MAX_SMALL_KEY_LENGTH = 255;
    public static int MAX_BIG_KEY_LENGTH = 65535;
    public static int MASK_SB4 = 64;
    public static int MASK_SB8 = 80;
    public static int MASK_ORANUM_16 = 32;
    public static int MASK_DEC_16 = 96;

    public static boolean isSB4(int op) {
        return (op & 248) == MASK_SB4;
    }

    public static boolean isSB8(int op) {
        return (op & DatabaseError.EOJ_NTF_UNKNOWN_LOCALHOST) == MASK_SB8;
    }

    public static boolean isOraNum16(int op) {
        return (op & DatabaseError.EOJ_NTF_UNKNOWN_LOCALHOST) == MASK_ORANUM_16;
    }

    public static boolean isDec_16(int op) {
        return (op & DatabaseError.EOJ_NTF_UNKNOWN_LOCALHOST) == MASK_DEC_16;
    }
}
