package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.aq.AQDequeueOptions;
import oracle.jdbc.aq.AQEnqueueOptions;
import oracle.jdbc.aq.AQMessage;
import oracle.jdbc.clio.annotations.Debug;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.JMSDequeueOptions;
import oracle.jdbc.internal.JMSEnqueueOptions;
import oracle.jdbc.internal.JMSFactory;
import oracle.jdbc.internal.JMSMessage;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTCaqa.class */
class T4CTTCaqa extends T4CTTIfun {
    private static final String CLASS_NAME = T4CTTCaqa.class.getName();
    static final int AQXDEF_ENQ = 1;
    static final int AQXDEF_DEQ = 2;
    static final int AQXDEF_ARR = 3;
    static final int AQXDEF_RETMID = 1;
    static final int AQXDEF_DYNDAT = 2;
    static final int AQXDEF_ORGMID = 4;
    T4CConnection connection;
    T4CTTIaqi[] aqiArray;
    T4CTTIaqo[] aqoArray;
    private long aqxrflg;
    private int aqxaqopt;
    private long aqxiters;
    private JMSEnqueueOptions jmsEnqueueOptions;
    private AQMessagePropertiesI[] messageProperties;
    private JMSMessage[] jmsMesgs;
    private boolean isAQMsgs;
    private AQMessage[] aqMesgs;
    private AQEnqueueOptions aqEnqueueOptions;
    private JMSDequeueOptions jmsDequeueOptions;
    private AQDequeueOptions aqDequeueOptions;

    T4CTTCaqa(T4CConnection _connection) throws SQLException, IOException {
        super(_connection, (byte) 3);
        this.aqxrflg = 0L;
        this.aqxaqopt = 0;
        this.aqxiters = 0L;
        this.isAQMsgs = false;
        this.jmsDequeueOptions = null;
        this.aqDequeueOptions = null;
        setFunCode((short) 145);
        this.connection = _connection;
    }

    @Debug(level = Debug.Level.FINER)
    int doJMSEnq(String _queueName, JMSEnqueueOptions _enqueueOptions, JMSMessage[] _mesgs, AQMessagePropertiesI[] _messageProperties) throws SQLException, IOException {
        try {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doJMSEnq", "entering args ({0}, {1}, {2}, {3})", (String) null, (String) null, _queueName, _enqueueOptions, _mesgs, _messageProperties);
            this.jmsEnqueueOptions = _enqueueOptions;
            this.messageProperties = _messageProperties;
            this.jmsMesgs = _mesgs;
            this.aqxaqopt = 1;
            this.isAQMsgs = false;
            this.aqxiters = _mesgs.length;
            this.aqEnqueueOptions = null;
            this.aqMesgs = null;
            this.aqiArray = new T4CTTIaqi[(int) this.aqxiters];
            for (int i = 0; i < this.aqxiters; i++) {
                this.aqiArray[i] = new T4CTTIaqi(this.connection, this.aqxaqopt, _queueName, _enqueueOptions, _mesgs[i], _messageProperties[i], _mesgs[i].getJMSMessageProperties(), null);
            }
            setFlag();
            doRPC();
            int i2 = (int) this.aqxiters;
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doJMSEnq", "returning {0}", (String) null, (String) null, Integer.valueOf(i2));
            return i2;
        } catch (Throwable th) {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doJMSEnq", "throwing", (String) null, (String) th, new Object[0]);
            throw th;
        }
    }

    @Debug(level = Debug.Level.FINER)
    JMSMessage[] doJMSDeq(String _queueName, JMSDequeueOptions _jmsDequeueOpt, int _size, AQMessagePropertiesI[] _messageProperties, JMSMessagePropertiesI _jmsProp) throws SQLException, IOException {
        try {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doJMSDeq", "entering args ({0}, {1}, {2}, {3}, {4})", (String) null, (String) null, _queueName, _jmsDequeueOpt, Integer.valueOf(_size), _messageProperties, _jmsProp);
            this.jmsEnqueueOptions = null;
            this.messageProperties = _messageProperties;
            JMSMessage[] mesgs = null;
            this.aqxaqopt = 2;
            this.isAQMsgs = false;
            this.aqxiters = _size;
            this.jmsDequeueOptions = _jmsDequeueOpt;
            this.aqEnqueueOptions = null;
            this.aqMesgs = null;
            this.aqDequeueOptions = null;
            this.aqiArray = new T4CTTIaqi[(int) this.aqxiters];
            for (int i = 0; i < this.aqxiters; i++) {
                this.aqiArray[i] = new T4CTTIaqi(this.connection, this.aqxaqopt, _queueName, null, null, _messageProperties[i], _jmsProp, _jmsDequeueOpt);
            }
            setFlag();
            doRPC();
            if (this.aqoArray != null && this.aqoArray.length > 0) {
                mesgs = new JMSMessage[this.aqoArray.length];
                for (int i2 = 0; i2 < this.aqoArray.length; i2++) {
                    JMSMessage msg = JMSFactory.createJMSMessage(_jmsProp);
                    msg.setPayload(this.aqoArray[i2].getPayload());
                    msg.setMessageId(this.aqoArray[i2].getMsgId());
                    msg.setJMSMessageProperties(_jmsProp);
                    msg.setAQMessageProperties(_messageProperties[i2]);
                    mesgs[i2] = msg;
                }
            }
            JMSMessage[] jMSMessageArr = mesgs;
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doJMSDeq", "returning {0}", (String) null, (String) null, jMSMessageArr);
            return jMSMessageArr;
        } catch (Throwable th) {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doJMSDeq", "throwing", (String) null, (String) th, new Object[0]);
            throw th;
        }
    }

    @Debug(level = Debug.Level.FINER)
    int doAQEnq(String _queueName, AQEnqueueOptions _aqEnqueueOptions, AQMessage[] _mesgs) throws SQLException, IOException {
        try {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doAQEnq", "entering args ({0}, {1}, {2})", (String) null, (String) null, _queueName, _aqEnqueueOptions, _mesgs);
            this.aqEnqueueOptions = _aqEnqueueOptions;
            this.aqMesgs = _mesgs;
            this.aqxaqopt = 1;
            this.isAQMsgs = true;
            this.aqxiters = _mesgs.length;
            this.jmsEnqueueOptions = null;
            this.jmsMesgs = null;
            this.aqDequeueOptions = null;
            this.jmsDequeueOptions = null;
            this.aqiArray = new T4CTTIaqi[(int) this.aqxiters];
            this.messageProperties = new AQMessagePropertiesI[(int) this.aqxiters];
            for (int i = 0; i < this.aqxiters; i++) {
                AQMessageI imesg = (AQMessageI) _mesgs[i];
                this.messageProperties[i] = imesg.getMessagePropertiesI();
                this.aqiArray[i] = new T4CTTIaqi(this.connection, this.aqxaqopt, _queueName, _aqEnqueueOptions, _mesgs[i], imesg.getMessagePropertiesI(), null, null, 0);
            }
            setFlag();
            doRPC();
            int i2 = (int) this.aqxiters;
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doAQEnq", "returning {0}", (String) null, (String) null, Integer.valueOf(i2));
            return i2;
        } catch (Throwable th) {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doAQEnq", "throwing", (String) null, (String) th, new Object[0]);
            throw th;
        }
    }

    @Debug(level = Debug.Level.FINER)
    AQMessage[] doAQDeq(String _queueName, AQDequeueOptions _aqDequeueOptions, byte[] _tdo, int _version, int _size, AQMessagePropertiesI[] _messageProperties) throws SQLException, IOException {
        try {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doAQDeq", "entering args ({0}, {1}, {2}, {3}, {4}, {5})", (String) null, (String) null, _queueName, _aqDequeueOptions, _tdo, Integer.valueOf(_version), Integer.valueOf(_size), _messageProperties);
            AQMessageI[] mesgs = null;
            this.aqEnqueueOptions = null;
            this.messageProperties = _messageProperties;
            this.aqxaqopt = 2;
            this.isAQMsgs = true;
            this.aqxiters = _size;
            this.aqDequeueOptions = _aqDequeueOptions;
            this.jmsEnqueueOptions = null;
            this.jmsMesgs = null;
            this.jmsDequeueOptions = null;
            this.aqiArray = new T4CTTIaqi[(int) this.aqxiters];
            for (int i = 0; i < this.aqxiters; i++) {
                this.aqiArray[i] = new T4CTTIaqi(this.connection, this.aqxaqopt, _queueName, null, null, _messageProperties[i], _aqDequeueOptions, _tdo, _version);
            }
            setFlag();
            doRPC();
            if (this.aqoArray != null && this.aqoArray.length > 0) {
                mesgs = new AQMessageI[this.aqoArray.length];
                for (int i2 = 0; i2 < this.aqoArray.length; i2++) {
                    AQMessageI msg = new AQMessageI(_messageProperties[i2], this.connection);
                    msg.setPayload(this.aqoArray[i2].getPayload(), _tdo);
                    msg.setMessageId(this.aqoArray[i2].getMsgId());
                    mesgs[i2] = msg;
                }
            }
            AQMessageI[] aQMessageIArr = mesgs;
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doAQDeq", "returning {0}", (String) null, (String) null, aQMessageIArr);
            return aQMessageIArr;
        } catch (Throwable th) {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T4CTTCaqa", "doAQDeq", "throwing", (String) null, (String) th, new Object[0]);
            throw th;
        }
    }

    void setFlag() {
        boolean retrieveMessageId;
        if (this.aqxaqopt == 1) {
            retrieveMessageId = (!this.isAQMsgs && this.jmsEnqueueOptions.isRetrieveMessageId()) || (this.isAQMsgs && this.aqEnqueueOptions.getRetrieveMessageId());
        } else {
            retrieveMessageId = (!this.isAQMsgs && this.jmsDequeueOptions.isRetrieveMessageId()) || (this.isAQMsgs && this.aqDequeueOptions.getRetrieveMessageId());
        }
        if (retrieveMessageId) {
            this.aqxrflg |= 1;
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshal", "aqxaqopt={0}, aqxrflg={1}, isAQMsgs={2}", (String) null, (Throwable) null, Integer.valueOf(this.aqxaqopt), Long.valueOf(this.aqxrflg), Boolean.valueOf(this.isAQMsgs));
        if (this.aqxaqopt != 1 && this.aqiArray != null && this.aqiArray.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.aqiArray.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalUB4(this.aqxrflg);
        if (this.aqxaqopt == 1) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(0L);
        } else {
            this.meg.marshalPTR();
            this.meg.marshalPTR();
        }
        if (!this.isAQMsgs && this.aqxaqopt == 1) {
            this.meg.marshalSB4(3);
        } else {
            this.meg.marshalSB4(this.aqxaqopt);
        }
        if (this.aqxaqopt == 1) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (this.connection.getTTCVersion() >= 16) {
            if (this.aqxaqopt == 1) {
                this.meg.marshalUB4(65535L);
            } else {
                this.meg.marshalUB4(65535L);
            }
        }
        if (this.aqxaqopt == 1) {
            this.meg.marshalUB4(this.aqxiters);
        }
        if (this.aqiArray != null && this.aqiArray.length != 0) {
            if (this.aqxaqopt == 1) {
                this.aqiArray[0].marshalPropagation();
                this.aqiArray[0].marshalHeader();
                for (int i = 0; i < this.aqiArray.length; i++) {
                    if (this.isAQMsgs) {
                        this.aqiArray[i].marshalData();
                    } else {
                        this.aqiArray[i].marshalJmsData();
                    }
                }
                this.aqiArray[0].marshalDone();
                return;
            }
            for (int i2 = 0; i2 < this.aqiArray.length; i2++) {
                this.aqiArray[i2].marshal();
            }
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        unmarshal();
    }

    void unmarshal() throws SQLException, IOException {
        byte[] outMsgId;
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshal", "aqxaqopt={0}, isAQMsgs={1}, aqxiters={2}", (String) null, (Throwable) null, Integer.valueOf(this.aqxaqopt), Boolean.valueOf(this.isAQMsgs), Long.valueOf(this.aqxiters));
        int aqxaqol = (int) this.meg.unmarshalUB4();
        if (aqxaqol > 0) {
            this.aqoArray = new T4CTTIaqo[aqxaqol];
            for (int i = 0; i < aqxaqol; i++) {
                if (this.isAQMsgs) {
                    this.aqoArray[i] = new T4CTTIaqo(this.connection, this.aqxaqopt, this.aqEnqueueOptions, this.messageProperties[i], this.aqDequeueOptions, this.aqiArray[i].isRawQueue(), this.aqiArray[i].isJsonQueue());
                } else {
                    this.aqoArray[i] = new T4CTTIaqo(this.connection, this.aqxaqopt, this.jmsEnqueueOptions, this.messageProperties[i], this.jmsDequeueOptions, this.aqiArray[i].isRawQueue());
                }
                this.aqoArray[i].unmarshal();
            }
            if (this.aqxaqopt == 1) {
                boolean retrieveMessageId = (!this.isAQMsgs && this.jmsEnqueueOptions.isRetrieveMessageId()) || (this.isAQMsgs && this.aqEnqueueOptions.getRetrieveMessageId());
                if (retrieveMessageId && (outMsgId = this.aqoArray[0].getMsgId()) != null && this.aqxiters * 16 == outMsgId.length) {
                    for (int i2 = 0; i2 < this.aqxiters; i2++) {
                        byte[] msgId = new byte[16];
                        System.arraycopy(outMsgId, i2 * 16, msgId, 0, 16);
                        if (this.isAQMsgs) {
                            ((AQMessageI) this.aqMesgs[i2]).setMessageId(msgId);
                        } else {
                            this.jmsMesgs[i2].setMessageId(msgId);
                        }
                    }
                }
            }
        } else {
            this.aqoArray = null;
        }
        if (this.aqxaqopt == 1) {
            this.aqxiters = this.meg.unmarshalUB4();
        } else {
            this.aqxiters = aqxaqol;
        }
    }
}
