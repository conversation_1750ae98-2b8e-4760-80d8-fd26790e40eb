package oracle.jdbc.driver;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.sql.SQLException;
import oracle.jdbc.internal.Monitor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CInputStream.class */
class T2CInputStream extends OracleInputStream {
    native int t2cGetBytes(long j, int i, byte[] bArr, int i2, Accessor[] accessorArr, Object[] objArr, Object[] objArr2, long j2);

    T2CInputStream(OracleStatement stmt, int index, Accessor a) {
        super(stmt, index, a);
    }

    private int getRowNumber() {
        int row = 0;
        if (this.statement.isFetchStreams) {
            if (this.statement instanceof T2CStatement) {
                if (((T2CStatement) this.statement).needToRetainRows) {
                    row = this.statement.storedRowCount;
                }
            } else if (this.statement instanceof T2CPreparedStatement) {
                if (((T2CPreparedStatement) this.statement).needToRetainRows) {
                    row = this.statement.storedRowCount;
                }
            } else if (((T2CCallableStatement) this.statement).needToRetainRows) {
                row = this.statement.storedRowCount;
            }
        }
        return row;
    }

    @Override // oracle.jdbc.driver.OracleInputStream
    public int getBytes(int howMany, byte[] buffer) throws SQLException, IOException {
        Monitor.CloseableLock lock = this.statement.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            long flags = this.statement.connection.useNio ? 1 : 0;
            if (this.statement.connection.useNio) {
                if (this.statement.nioBuffers[3] == null || this.statement.nioBuffers[3].capacity() < buffer.length) {
                    this.statement.nioBuffers[3] = ByteBuffer.allocateDirect(buffer.length);
                } else {
                    this.statement.nioBuffers[3].rewind();
                }
            }
            int dataSize = t2cGetBytes(this.statement.c_state, this.columnIndex, buffer, this.currentBufferSize, this.statement.accessors, this.statement.nioBuffers, this.statement.lobPrefetchMetaData, flags);
            boolean needToUpdateNioBuffers = false;
            try {
                int row = getRowNumber();
                if (dataSize == -1) {
                    ((T2CConnection) this.statement.connection).checkError(dataSize, this.statement.sqlWarning);
                } else if (dataSize == -2) {
                    needToUpdateNioBuffers = true;
                    this.accessor.setNull(row, true);
                    dataSize = 0;
                } else if (dataSize >= 0) {
                    this.accessor.setNull(row, false);
                }
                if (dataSize <= 0) {
                    dataSize = -1;
                    needToUpdateNioBuffers = true;
                }
                if (this.statement.connection.useNio) {
                    ByteBuffer b = this.statement.nioBuffers[3];
                    if (b != null && dataSize > 0) {
                        b.get(buffer, 0, dataSize);
                    }
                    if (needToUpdateNioBuffers) {
                        try {
                            this.statement.extractNioDefineBuffers(this.columnIndex);
                        } catch (SQLException e) {
                            throw new IOException(e.getMessage());
                        }
                    }
                }
                if (needToUpdateNioBuffers && this.statement.lobPrefetchMetaData != null) {
                    this.statement.processLobPrefetchMetaData(this.statement.lobPrefetchMetaData);
                }
                return dataSize;
            } catch (SQLException e2) {
                throw new IOException(e2.getMessage());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleInputStream, oracle.jdbc.driver.OracleBufferedStream
    public boolean isNull() throws IOException {
        if (!this.statement.isFetchStreams) {
            needBytes();
            return super.isNull();
        }
        return false;
    }
}
