package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_hu.class */
public class ErrorMessagesJson_hu extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "I/O kivétel történt"}, new Object[]{"26302", "A(z) \"{0}\" év nem támogatott"}, new Object[]{"26303", "Túlcsordulás, túl nagy érték: {0}."}, new Object[]{"26304", "Nem támogatott opció (nincs implementálva)."}, new Object[]{"26305", "A bináris JSON érvénytelen vagy sérült."}, new Object[]{"26306", "Nem támogatott bináris JSON verzió: {0}."}, new Object[]{"26307", "Az UTF-8 kódolt kulcs nem lehet hosszabb mint 256 bájt. A következő kulcs meghaladja ezt a maximális hosszt: \"{0}\"."}, new Object[]{"26308", "A megadott JSON túl nagy ahhoz, hogy kódolható legyen bináris JSON-ként. A kódolt kép mérete nem lehet nagyobb mint 2 GB."}, new Object[]{"26309", "A bináris JSON érvénytelen vagy sérült. A megadott kép csak {0} bájtot tartalmaz."}, new Object[]{"26310", "A megadott java.time.Period paraméterhez nap van beállítva, azonban az Oracle év-hónap időköze nem támogatja a napot."}, new Object[]{"26311", "A generátor lezárt a záró érték előtt."}, new Object[]{"26312", "Ebben a környezetben meg kell adni egy objektumkulcsot."}, new Object[]{"26313", "Érvénytelen írás. Már van beírva teljes érték."}, new Object[]{"26314", "Záró érték nem megengedett ebben a környezetben."}, new Object[]{"26315", "Kulcs nem megengedett ebben a környezetben."}, new Object[]{"26316", "A rendszer értéket vár a kulcs után."}, new Object[]{"26317", "Az elemző állapotának {0} értékűnek kell lennie."}, new Object[]{"26318", "Az elemző állapotának értéke nem lehet {0}."}, new Object[]{"26319", "Az elemzőnek értékkel kell rendelkeznie."}, new Object[]{"26320", "a(z) \"{0}\" nem támogatott leképezőtípus."}, new Object[]{"26321", "Ez az objektum nem módosítható. Módosítható példány létrehozásához használja a következőt: OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Ez a tömb nem módosítható. Módosítható példány létrehozásához használja a következőt: OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "A JSON objektum ismétlődő kulcsot tartalmaz: {0}."}, new Object[]{"26324", "Kódolás automatikus felismerése nem lehetséges, nincs elegendő karakter."}, new Object[]{"26325", "A várt EOF token helyett {0} érkezett."}, new Object[]{"26326", "Váratlan {0} karakter: {1}. sor, {2}. oszlop."}, new Object[]{"26327", "Váratlan {0} karakter: {1}. sor, {2}. oszlop. Várt karakter: {3}."}, new Object[]{"26328", "Váratlan {0} token: {1}. sor, {2}. oszlop. Várt tokenek: {3}."}, new Object[]{"26329", "A JsonParser#getString() csak az elemző KEY_NAME, VALUE_STRING, VALUE_NUMBER állapotában érvényes. Az elemző aktuális állapota {0}."}, new Object[]{"26330", "A JsonParser#isIntegralNumber() csak az elemző VALUE_NUMBER állapotában érvényes. Az elemző aktuális állapota {0}."}, new Object[]{"26331", "A JsonParser#getInt() csak az elemző VALUE_NUMBER állapotában érvényes. Az elemző aktuális állapota {0}."}, new Object[]{"26332", "A JsonParser#getLong() csak az elemző VALUE_NUMBER állapotában érvényes. Az elemző aktuális állapota {0}."}, new Object[]{"26333", "A JsonParser#getBigDecimal() csak az elemző VALUE_NUMBER állapotában érvényes. Az elemző aktuális állapota {0}."}, new Object[]{"26334", "A JsonParser#getArray() csak az elemző START_ARRAY állapotában érvényes. Az elemző aktuális állapota {0}."}, new Object[]{"26335", "A JsonParser#getObject() csak az elemző START_OBJECT állapotában érvényes. Az elemző aktuális állapota {0}."}, new Object[]{"26336", "A régióval megadott időbélyeg nem támogatott. Csak az eltolásos időzónák támogatottak."}, new Object[]{"26337", "A JSON-értékben található objektumok és tömbök nem ágyazhatók be {0} szintnél mélyebbre"}, new Object[]{"26338", "A JSON-objektum kulcsainak mérete nem haladhatja meg a 65535 bájtot."}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
