package oracle.jdbc.driver;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.CharBuffer;
import java.nio.ShortBuffer;
import java.sql.SQLException;
import java.util.ArrayDeque;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.driver.OracleStatement;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CStatement.class */
class T2CStatement extends OracleStatement {
    private final String CLASS_NAME;
    T2CConnection t2cConnection;
    static int T2C_EXTEND_BUFFER;
    long[] t2cOutput;
    long[] t2cOutputUpdateCountArray;
    int[] t2cOutputUpdateCountArraySize;
    static final int T2C_OUTPUT_USE_NIO = 5;
    static final int T2C_OUTPUT_STMT_LOB_PREFETCH_SIZE = 6;
    static final int T2C_OUTPUT_USE_OCI_DEFAULT_DEFINE_OFFSET = 7;
    static final boolean T2CDEBUG = false;
    int extractedCharOffset;
    int extractedByteOffset;
    int savedRowPrefetch;
    int OCIPrefetch;
    static final byte T2C_LOB_PREFETCH_SIZE_THIS_COLUMN_OFFSET = 0;
    static final byte T2C_LOB_PREFETCH_LOB_LENGTH_OFFSET = 1;
    static final byte T2C_LOB_PREFETCH_FORM_OFFSET = 2;
    static final byte T2C_LOB_PREFETCH_CHUNK_OFFSET = 3;
    static final byte T2C_LOB_PREFETCH_DATA_OFFSET = 4;
    byte[] lobPrefetchTempBytes;
    boolean needToRetainRows;
    byte[] returnParamBytes;
    char[] returnParamChars;
    int[] returnParamIndicators;
    int returnParamRowBytes;
    int returnParamRowChars;
    static int PREAMBLE_PER_POSITION;
    SQLException updateDataException;
    int lastProcessedCell;
    static final int PROCESS_DEFINE_DYNAMIC_COLUMNS = 16;
    static final int PROCESS_DEFINE_DEFAULT_COLUMNS = 32;
    static final int PROCESS_ADT_OUT_BINDS = 64;
    int lastProcessedAccessorIndex;
    int accessorsProcessed;
    int previousMode;
    static final /* synthetic */ boolean $assertionsDisabled;

    static native int t2cParseExecuteDescribe(OracleStatement oracleStatement, long j, int i, int i2, int i3, boolean z, boolean z2, boolean z3, boolean z4, byte[] bArr, int i4, byte b, int i5, int i6, short[] sArr, int i7, byte[] bArr2, char[] cArr, int i8, int i9, short[] sArr2, int i10, int i11, byte[] bArr3, char[] cArr2, int i12, int i13, int[] iArr, short[] sArr3, byte[] bArr4, int i14, int i15, int i16, int i17, boolean z5, boolean z6, Accessor[] accessorArr, Binder[][] binderArr, long[] jArr, long[] jArr2, int[] iArr2, boolean z7) throws IOException;

    static native long t2cDefineExecuteFetch(OracleStatement oracleStatement, long j, int i, int i2, int i3, int i4, boolean z, boolean z2, byte[] bArr, int i5, byte b, int i6, int i7, short[] sArr, int i8, byte[] bArr2, char[] cArr, int i9, int i10, short[] sArr2, byte[] bArr3, int i11, int i12, boolean z3, boolean z4, Accessor[] accessorArr, Binder[][] binderArr, long[] jArr, ByteBuffer[] byteBufferArr, Object[] objArr) throws IOException;

    static native int t2cDescribe(long j, short[] sArr, byte[] bArr, int i, int i2, int i3, int i4, byte[] bArr2, int i5, boolean z);

    static native long t2cDefineFetch(OracleStatement oracleStatement, long j, int i, int i2, short[] sArr, byte[] bArr, int i3, int i4, Accessor[] accessorArr, long[] jArr, ByteBuffer[] byteBufferArr, Object[] objArr);

    static native int t2cGetImplicitResultSetCount(OracleStatement oracleStatement, long j);

    static native long t2cFetch(OracleStatement oracleStatement, long j, boolean z, int i, int i2, Accessor[] accessorArr, long[] jArr, ByteBuffer[] byteBufferArr, Object[] objArr);

    static native int t2cCloseStatement(long j);

    static native int t2cEndToEndUpdate(long j, byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, int i3, byte[] bArr4, int i4, byte[] bArr5, int i5, int i6);

    static native int t2cGetRowsDmlReturned(long j);

    static native int t2cFetchDmlReturnParams(long j, OracleStatement oracleStatement, Accessor[] accessorArr, byte[] bArr, char[] cArr, int[] iArr);

    static {
        $assertionsDisabled = !T2CStatement.class.desiredAssertionStatus();
        T2C_EXTEND_BUFFER = -3;
        PREAMBLE_PER_POSITION = 5;
    }

    T2CStatement(T2CConnection conn, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        super(conn, resultSetType);
        this.CLASS_NAME = getClass().getName();
        this.t2cConnection = null;
        this.t2cOutput = new long[10];
        this.t2cOutputUpdateCountArray = null;
        this.t2cOutputUpdateCountArraySize = new int[1];
        this.savedRowPrefetch = 0;
        this.OCIPrefetch = 1;
        this.needToRetainRows = false;
        this.updateDataException = null;
        this.lastProcessedCell = 0;
        this.lastProcessedAccessorIndex = 0;
        this.accessorsProcessed = 0;
        this.previousMode = 0;
        this.t2cConnection = conn;
        if (this.t2cConnection.useOCIDefaultDefines) {
            this.savedRowPrefetch = this.rowPrefetch;
            this.OCIPrefetch = this.rowPrefetch;
            this.rowPrefetch = 1;
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    int getPrefetchInternal(boolean statement) {
        if (!this.t2cConnection.useOCIDefaultDefines) {
            return super.getPrefetchInternal(statement);
        }
        int ret_val = statement ? this.defaultRowPrefetch : this.savedRowPrefetch;
        return ret_val;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void setPrefetchInternal(int new_value, boolean setRowPrefetch, boolean statement) throws SQLException {
        int localRowPrefetch = this.rowPrefetch;
        super.setPrefetchInternal(new_value, setRowPrefetch, statement);
        if (this.t2cConnection.useOCIDefaultDefines && localRowPrefetch != this.rowPrefetch) {
            this.savedRowPrefetch = this.rowPrefetch;
            this.OCIPrefetch = this.rowPrefetch;
            this.rowPrefetch = 1;
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void prepareForNewResults(boolean resetPrefetch, boolean clearStreamList, boolean clearImplicitResults) throws SQLException {
        super.prepareForNewResults(resetPrefetch, clearStreamList, clearImplicitResults);
        if (this.t2cConnection.useOCIDefaultDefines && this.rowPrefetchChanged) {
            this.savedRowPrefetch = this.rowPrefetch;
            this.OCIPrefetch = this.rowPrefetch;
            this.rowPrefetch = 1;
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void prepareAccessors() throws SQLException {
        super.prepareAccessors();
        if (this.rowPrefetchChanged) {
            this.lobPrefetchMetaData = getLobPrefetchMetaData();
        }
        if (this.t2cConnection.useOCIDefaultDefines && this.hasStream) {
            this.savedRowPrefetch = 1;
        }
    }

    String bytes2String(byte[] bytes, int offset, int size) throws SQLException {
        byte[] tmp = new byte[size];
        System.arraycopy(bytes, offset, tmp, 0, size);
        return this.connection.conversion.CharBytesToString(tmp, size);
    }

    void processDescribeData() throws SQLException {
        this.described = true;
        this.describedWithNames = true;
        if (this.numberOfDefinePositions < 1) {
            return;
        }
        if (this.accessors == null || this.numberOfDefinePositions > this.accessors.length) {
            this.accessors = new Accessor[this.numberOfDefinePositions];
        }
        int currentShort = this.t2cConnection.queryMetaData1Offset;
        int currentChar = this.t2cConnection.queryMetaData2Offset;
        short[] s = this.t2cConnection.queryMetaData1;
        byte[] c = this.t2cConnection.queryMetaData2;
        int i = 0;
        while (i < this.numberOfDefinePositions) {
            short s2 = s[currentShort + 0];
            short s3 = s[currentShort + 1];
            short s4 = s[currentShort + 11];
            boolean nullable = s[currentShort + 2] != 0;
            short s5 = s[currentShort + 3];
            short s6 = s[currentShort + 4];
            short formOfUse = s[currentShort + 5];
            short columnNameLen = s[currentShort + 6];
            String columnName = bytes2String(c, currentChar, columnNameLen);
            short schemaNameLen = s[currentShort + 12];
            short typeNameLen = s[currentShort + 13];
            boolean columnInvisible = s[currentShort + 14] != 0;
            boolean columnJSON = s[currentShort + 15] != 0;
            String typeName = null;
            OracleTypeADT otype = null;
            currentChar += columnNameLen;
            if (typeNameLen > 0) {
                String schemaName = bytes2String(c, currentChar, schemaNameLen);
                int currentChar2 = currentChar + schemaNameLen;
                String typeName2 = bytes2String(c, currentChar2, typeNameLen);
                currentChar = currentChar2 + typeNameLen;
                if (PhysicalConnection.needToQuoteIdentifier(schemaName) || PhysicalConnection.needToQuoteIdentifier(typeName2)) {
                    typeName = String.format("\"%s\".\"%s\"", schemaName, typeName2);
                } else {
                    typeName = schemaName + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + typeName2;
                }
                otype = new OracleTypeADT(typeName, this.connection.getPhysicalConnection());
                otype.tdoCState = ((s[currentShort + 7] & 65535) << 48) | ((s[currentShort + 8] & 65535) << 32) | ((s[currentShort + 9] & 65535) << 16) | (s[currentShort + 10] & 65535);
            }
            Accessor accessor = this.accessors[i];
            if (accessor == null || accessor.defineType == 0 || (accessor.describeType != 0 && accessor.describeType != s2)) {
                accessor = allocateAccessorForDefines(i, s2, s3, nullable, 0, s5, s6, 0L, 0, formOfUse, s4, otype, typeName);
                if (this.accessors[i] != null) {
                    accessor.rowLength = this.accessors[i].rowLength;
                    accessor.rowOffset = this.accessors[i].rowOffset;
                    accessor.rowNull = this.accessors[i].rowNull;
                    accessor.rowMetadata = this.accessors[i].rowMetadata;
                }
            } else {
                accessor.initForDescribe(s2, s3, nullable, 0, s5, s6, 0L, 0, formOfUse, typeName);
            }
            accessor.describeOtype = otype;
            accessor.columnName = columnName;
            accessor.columnInvisible = columnInvisible;
            accessor.columnJSON = columnJSON;
            this.accessors[i] = accessor;
            i++;
            currentShort += 16;
        }
    }

    Accessor allocateAccessorForDefines(int index, int accessorType, int maxLength, boolean nullable, int flags, int precision, int scale, long contflag, int totalElems, short formOfUse, int maxCharLength, OracleTypeADT otype, String typeName) throws SQLException {
        Accessor accessor;
        switch (accessorType) {
            case 1:
                accessor = new VarcharAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse, maxCharLength);
                break;
            case 2:
                accessor = new NumberAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 8:
                accessor = new T2CLongAccessor(this, index + 1, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                this.rowPrefetch = 1;
                this.OCIPrefetch = 1;
                this.savedRowPrefetch = 1;
                break;
            case 12:
                accessor = new DateAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 23:
                accessor = new RawAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 24:
                accessor = new T2CLongRawAccessor(this, index + 1, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                this.rowPrefetch = 1;
                this.savedRowPrefetch = 1;
                this.OCIPrefetch = 1;
                break;
            case 96:
                accessor = new CharAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse, maxCharLength);
                break;
            case 100:
                accessor = new BinaryFloatAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 101:
                accessor = new BinaryDoubleAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 102:
            case DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN /* 116 */:
                if (this.sqlKind.isPlsqlOrCall()) {
                    accessor = new T2CResultSetAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                    break;
                } else {
                    accessor = new ResultSetAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                    break;
                }
            case 104:
                accessor = new RowidAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, (short) 1);
                break;
            case 109:
                accessor = new NamedTypeAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse, typeName, otype);
                break;
            case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                accessor = new RefTypeAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse, typeName, otype);
                break;
            case 112:
                accessor = new ClobAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 113:
                accessor = new BlobAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 114:
                accessor = new BfileAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 180:
                accessor = new TimestampAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 181:
                accessor = new TimestamptzAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 182:
                accessor = new IntervalymAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 183:
                accessor = new IntervaldsAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case CharacterSet.WE8BS2000_CHARSET /* 231 */:
                accessor = new TimestampltzAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            case 252:
                accessor = new BooleanAccessor(this, maxLength, nullable, flags, precision, scale, contflag, totalElems, formOfUse);
                break;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Unknown or unimplemented accessor type: " + accessorType).fillInStackTrace());
        }
        return accessor;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void executeForDescribe() throws SQLException {
        boolean try_again;
        this.t2cOutput[0] = 0;
        this.t2cOutput[2] = 0;
        this.t2cOutput[7] = this.t2cConnection.useOCIDefaultDefines ? 1 : 0;
        this.lobPrefetchMetaData = null;
        boolean need_to_describe = !this.described;
        boolean executed = false;
        int rowsToFetch = this.t2cConnection.useOCIDefaultDefines ? this.savedRowPrefetch : this.rowPrefetch;
        this.validRows = 0L;
        do {
            try_again = false;
            if (this.connection.endToEndAnyChanged) {
                pushEndToEndValues();
                this.connection.endToEndAnyChanged = false;
            }
            byte[] array_sql = this.sqlObject.getSqlBytes(this.processEscapes, this.convertNcharLiterals);
            try {
                try {
                    resetStateBeforeFetch();
                    this.t2cOutputUpdateCountArray = null;
                    this.t2cOutputUpdateCountArraySize[0] = 0;
                    int status = t2cParseExecuteDescribe(this, this.c_state, this.numberOfBindPositions, 0, 0, false, this.needToParse, need_to_describe, executed, array_sql, array_sql.length, convertSqlKindEnumToByte(this.sqlKind), rowsToFetch, this.OCIPrefetch, this.bindIndicators, this.bindIndicatorOffset, this.bindBytes, this.bindChars, this.bindByteOffset, this.bindCharOffset, this.ibtBindIndicators, this.ibtBindIndicatorOffset, this.ibtBindIndicatorSize, this.ibtBindBytes, this.ibtBindChars, this.ibtBindByteOffset, this.ibtBindCharOffset, this.returnParamMeta, this.t2cConnection.queryMetaData1, this.t2cConnection.queryMetaData2, this.t2cConnection.queryMetaData1Offset, this.t2cConnection.queryMetaData2Offset, this.t2cConnection.queryMetaData1Size, this.t2cConnection.queryMetaData2Size, true, true, this.accessors, (Binder[][]) null, this.t2cOutput, this.t2cOutputUpdateCountArray, this.t2cOutputUpdateCountArraySize, this.t2cConnection.plsqlCompilerWarnings);
                    if (this.t2cOutputUpdateCountArraySize[0] > 0) {
                        this.batchRowsUpdatedArray = new long[this.t2cOutputUpdateCountArraySize[0]];
                        System.arraycopy(this.t2cOutputUpdateCountArray, 0, this.batchRowsUpdatedArray, 0, this.t2cOutputUpdateCountArraySize[0]);
                    } else if (this.batchRowsUpdatedArray != null) {
                        this.batchRowsUpdatedArray = new long[0];
                    }
                    this.validRows = this.t2cOutput[1];
                    if (status == -1 || status == -4) {
                        this.t2cConnection.checkError(status, this.c_state, this.sqlObject);
                    } else if (status == T2C_EXTEND_BUFFER) {
                        status = this.t2cConnection.queryMetaData1Size * 2;
                    }
                    if (this.t2cOutput[3] != 0) {
                        foundPlsqlCompilerWarning();
                    } else if (this.t2cOutput[2] != 0) {
                        this.sqlWarning = this.t2cConnection.checkError(1, this.sqlWarning);
                    }
                    this.connection.endToEndECIDSequenceNumber = (short) this.t2cOutput[4];
                    this.needToParse = false;
                    executed = true;
                    if (this.sqlKind.isSELECT()) {
                        this.numberOfDefinePositions = status;
                        if (this.numberOfDefinePositions > this.t2cConnection.queryMetaData1Size) {
                            try_again = true;
                            executed = true;
                            this.t2cConnection.reallocateQueryMetaData(this.numberOfDefinePositions, this.numberOfDefinePositions * 8);
                        }
                    } else {
                        this.numberOfDefinePositions = 0;
                    }
                    if (this.sqlKind.isPlsqlOrCall()) {
                        checkForImplicitResultSets();
                    }
                } catch (IOException e) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 266).fillInStackTrace());
                }
            } catch (Throwable th) {
                if (this.t2cOutputUpdateCountArraySize[0] > 0) {
                    this.batchRowsUpdatedArray = new long[this.t2cOutputUpdateCountArraySize[0]];
                    System.arraycopy(this.t2cOutputUpdateCountArray, 0, this.batchRowsUpdatedArray, 0, this.t2cOutputUpdateCountArraySize[0]);
                } else if (this.batchRowsUpdatedArray != null) {
                    this.batchRowsUpdatedArray = new long[0];
                }
                throw th;
            }
        } while (try_again);
        this.isAllFetched = false;
        processDescribeData();
    }

    void checkForImplicitResultSets() throws SQLException {
        int status = t2cGetImplicitResultSetCount(this, this.c_state);
        if (status > 0) {
            int implicitResultSetCount = status;
            this.implicitResultSetStatements = new ArrayDeque<>(implicitResultSetCount);
            while (implicitResultSetCount != 0) {
                OracleStatement newstmt = this.connection.createImplicitResultSetStatement(this);
                ((T2CStatement) newstmt).doDescribe(true);
                ((T2CStatement) newstmt).prepareAccessors();
                implicitResultSetCount--;
            }
            this.implicitResultSetIterator = this.implicitResultSetStatements.iterator();
            return;
        }
        if (status != 0) {
            this.t2cConnection.checkError(status);
        }
    }

    void pushEndToEndValues() throws SQLException {
        T2CConnection c = this.t2cConnection;
        byte[] e2e_action = null;
        byte[] e2e_clientid = null;
        byte[] e2e_ecid = null;
        byte[] e2e_module = null;
        byte[] e2e_dbop = null;
        if (c.endToEndValues != null) {
            if (c.endToEndHasChanged[0]) {
                String action = c.endToEndValues[0];
                if (action != null) {
                    e2e_action = DBConversion.stringToDriverCharBytes(action, c.m_clientCharacterSet);
                } else {
                    e2e_action = PhysicalConnection.EMPTY_BYTE_ARRAY;
                }
                c.endToEndHasChanged[0] = false;
            }
            if (c.endToEndHasChanged[1]) {
                String clientid = c.endToEndValues[1];
                if (clientid != null) {
                    e2e_clientid = DBConversion.stringToDriverCharBytes(clientid, c.m_clientCharacterSet);
                } else {
                    e2e_clientid = PhysicalConnection.EMPTY_BYTE_ARRAY;
                }
                c.endToEndHasChanged[1] = false;
            }
            if (c.endToEndHasChanged[2]) {
                String ecid = c.endToEndValues[2];
                if (ecid != null) {
                    e2e_ecid = DBConversion.stringToDriverCharBytes(ecid, c.m_clientCharacterSet);
                } else {
                    e2e_ecid = PhysicalConnection.EMPTY_BYTE_ARRAY;
                }
                c.endToEndHasChanged[2] = false;
            }
            if (c.endToEndHasChanged[3]) {
                String module = c.endToEndValues[3];
                if (module != null) {
                    e2e_module = DBConversion.stringToDriverCharBytes(module, c.m_clientCharacterSet);
                } else {
                    e2e_module = PhysicalConnection.EMPTY_BYTE_ARRAY;
                }
                c.endToEndHasChanged[3] = false;
            }
            if (c.endToEndHasChanged[4]) {
                String dbop = c.endToEndValues[4];
                if (dbop != null) {
                    e2e_module = DBConversion.stringToDriverCharBytes(dbop, c.m_clientCharacterSet);
                } else {
                    e2e_module = PhysicalConnection.EMPTY_BYTE_ARRAY;
                }
                c.endToEndHasChanged[4] = false;
            }
            t2cEndToEndUpdate(this.c_state, e2e_action, e2e_action == null ? -1 : e2e_action.length, e2e_clientid, e2e_clientid == null ? -1 : e2e_clientid.length, e2e_ecid, e2e_ecid == null ? -1 : e2e_ecid.length, e2e_module, e2e_module == null ? -1 : e2e_module.length, null, 0 == 0 ? -1 : e2e_dbop.length, c.endToEndECIDSequenceNumber);
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void executeForRows(boolean executed_for_describe) throws SQLException {
        if (this.connection.endToEndAnyChanged) {
            pushEndToEndValues();
            this.connection.endToEndAnyChanged = false;
        }
        if (!executed_for_describe) {
            if (this.numberOfDefinePositions > 0) {
                doDefineExecuteFetch();
            } else {
                executeForDescribe();
            }
        } else if (this.numberOfDefinePositions > 0) {
            doDefineFetch();
        }
        if (this.returnParamMeta != null) {
            fetchDmlReturnParams();
        }
        this.needToPrepareDefineBuffer = false;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected boolean isDefineBufferPreparedForExecute() throws SQLException {
        if (this.numberOfDefinePositions > 0) {
            return false;
        }
        return super.isDefineBufferPreparedForExecute();
    }

    void setupForDefine() throws SQLException {
        if (this.numberOfDefinePositions > this.t2cConnection.queryMetaData1Size) {
            int n = (this.numberOfDefinePositions / 100) + 1;
            this.t2cConnection.reallocateQueryMetaData(this.t2cConnection.queryMetaData1Size * n, this.t2cConnection.queryMetaData2Size * n * 8);
        }
        short[] s = this.t2cConnection.queryMetaData1;
        int currentShort = this.t2cConnection.queryMetaData1Offset;
        int i = 0;
        while (i < this.numberOfDefinePositions) {
            Accessor currentAccessor = this.accessors[i];
            if (currentAccessor == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 21).fillInStackTrace());
            }
            s[currentShort + 0] = (short) currentAccessor.defineType;
            if (!this.described && currentAccessor.charLength > 0 && currentAccessor.formOfUse == 1) {
                int charLength = currentAccessor.charLength;
                int byteLength = charLength + 1;
                s[currentShort + 11] = 0;
                s[currentShort + 1] = (short) byteLength;
            } else {
                s[currentShort + 11] = (short) currentAccessor.charLength;
                s[currentShort + 1] = (short) currentAccessor.byteLength;
            }
            s[currentShort + 5] = currentAccessor.formOfUse;
            if (currentAccessor.internalOtype != null) {
                long tdo = ((OracleTypeADT) currentAccessor.internalOtype).getTdoCState();
                s[currentShort + 7] = (short) ((tdo & (-281474976710656L)) >> 48);
                s[currentShort + 8] = (short) ((tdo & 281470681743360L) >> 32);
                s[currentShort + 9] = (short) ((tdo & 4294901760L) >> 16);
                s[currentShort + 10] = (short) (tdo & 65535);
            }
            switch (currentAccessor.internalType) {
                case 112:
                case 113:
                    if (currentAccessor.lobPrefetchSizeForThisColumn == -1) {
                        currentAccessor.setPrefetchLength(this.defaultLobPrefetchSize);
                    }
                    s[currentShort + 7] = (short) currentAccessor.lobPrefetchSizeForThisColumn;
                    break;
            }
            i++;
            currentShort += 16;
        }
    }

    protected void configureBindData() throws SQLException {
        if (this.outBindAccessors == null) {
            return;
        }
        this.bindData = null;
    }

    void initializePlsqlIndexByTableAccessor(Accessor accessor, int indOffset) {
        ((T2CPlsqlIndexTableAccessor) accessor).ibtMetaIndex = indOffset - 8;
    }

    Object[] getLobPrefetchMetaData() {
        Object[] lobPrefetchMetaData = null;
        int[] lobPrefetchSizeForThisColumnArray = null;
        int noOfLobColumnsWithPrefetch = 0;
        int lastStreamColumnIndex = 0;
        if (this.accessors != null) {
            for (int i = 0; i < this.numberOfDefinePositions; i++) {
                switch (this.accessors[i].internalType) {
                    case 8:
                    case 24:
                        lastStreamColumnIndex = i;
                        break;
                    case 112:
                    case 113:
                        if (lobPrefetchSizeForThisColumnArray == null) {
                            lobPrefetchSizeForThisColumnArray = new int[this.accessors.length];
                        }
                        if (this.accessors[i].lobPrefetchSizeForThisColumn != -1) {
                            noOfLobColumnsWithPrefetch++;
                            lobPrefetchSizeForThisColumnArray[i] = this.accessors[i].lobPrefetchSizeForThisColumn;
                            break;
                        } else {
                            lobPrefetchSizeForThisColumnArray[i] = -1;
                            break;
                        }
                }
            }
            if (noOfLobColumnsWithPrefetch > 0) {
                if (0 == 0 || this.rowPrefetchChanged) {
                    lobPrefetchMetaData = new Object[]{null, new long[this.rowPrefetch * noOfLobColumnsWithPrefetch], new byte[this.accessors.length], new int[this.accessors.length], new Object[this.rowPrefetch * noOfLobColumnsWithPrefetch]};
                }
                for (int i2 = 0; i2 < lastStreamColumnIndex; i2++) {
                    switch (this.accessors[i2].internalType) {
                        case 112:
                        case 113:
                            this.accessors[i2].setPrefetchLength(-1);
                            lobPrefetchSizeForThisColumnArray[i2] = -1;
                            break;
                    }
                }
                lobPrefetchMetaData[0] = lobPrefetchSizeForThisColumnArray;
            }
        }
        return lobPrefetchMetaData;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void processLobPrefetchMetaData(Object[] lobPrefetchMetaData) throws SQLException {
        int lobColumnCount = 0;
        int noOfRowsFetched = ((int) this.validRows) == -2 ? 1 : (int) this.validRows;
        byte[] prefetchedDataFormOfUse = (byte[]) lobPrefetchMetaData[2];
        int[] prefetchedChunkSize = (int[]) lobPrefetchMetaData[3];
        long[] prefetchedLength = (long[]) lobPrefetchMetaData[1];
        Object[] prefetchedData = (Object[]) lobPrefetchMetaData[4];
        int[] lobPrefetchSizeForThisColumnArray = (int[]) lobPrefetchMetaData[0];
        if (this.accessors != null) {
            for (int col = 0; col < this.numberOfDefinePositions; col++) {
                switch (this.accessors[col].internalType) {
                    case 112:
                    case 113:
                        if (this.accessors[col].lobPrefetchSizeForThisColumn >= 0) {
                            LobCommonAccessor accessor = (LobCommonAccessor) this.accessors[col];
                            if (accessor.prefetchedDataLength == null || accessor.prefetchedDataLength.length < this.rowPrefetch) {
                                if (accessor.internalType == 112) {
                                    ((ClobAccessor) accessor).prefetchedDataFormOfUse = new int[this.rowPrefetch];
                                }
                                accessor.prefetchedChunkSize = new int[this.rowPrefetch];
                                accessor.prefetchedDataLength = new int[this.rowPrefetch];
                                accessor.prefetchedLength = new long[this.rowPrefetch];
                                accessor.prefetchedDataOffset = new long[this.rowPrefetch];
                            }
                            int offset = noOfRowsFetched * lobColumnCount;
                            int rowOffset = this.needToRetainRows ? this.storedRowCount : 0;
                            for (int row = 0; row < noOfRowsFetched; row++) {
                                accessor.prefetchedChunkSize[rowOffset + row] = prefetchedChunkSize[col];
                                accessor.prefetchedLength[rowOffset + row] = prefetchedLength[offset + row];
                                if (accessor.internalType == 112) {
                                    ((ClobAccessor) accessor).prefetchedDataFormOfUse[rowOffset + row] = prefetchedDataFormOfUse[col];
                                }
                                accessor.prefetchedDataLength[row] = 0;
                                accessor.prefetchedDataOffset[row] = 0;
                                if (lobPrefetchSizeForThisColumnArray[col] > 0 && prefetchedLength[offset + row] > 0) {
                                    byte[] b = (byte[]) prefetchedData[offset + row];
                                    int dataLength = b == null ? 0 : b.length;
                                    if (dataLength > 0) {
                                        accessor.setPrefetchedDataOffset(rowOffset + row);
                                        accessor.rowData.put(b, 0, dataLength);
                                    }
                                    accessor.prefetchedDataLength[rowOffset + row] = dataLength;
                                }
                            }
                            lobColumnCount++;
                            break;
                        } else {
                            break;
                        }
                        break;
                }
            }
        }
    }

    int getRowsToFetch() {
        int rowsToFetch;
        if (this.hasStream) {
            rowsToFetch = 1;
            if (this.t2cConnection.useOCIDefaultDefines) {
                this.savedRowPrefetch = 1;
            } else {
                this.rowPrefetch = 1;
            }
        } else {
            rowsToFetch = this.t2cConnection.useOCIDefaultDefines ? (this.maxRows <= 0 || this.maxRows != ((long) this.storedRowCount)) ? this.rowPrefetch : 0 : (this.maxRows <= 0 || this.maxRows >= ((long) (this.rowPrefetch + this.storedRowCount))) ? this.rowPrefetch : (this.storedRowCount >= 1 || this.maxRows >= ((long) this.rowPrefetch)) ? (int) Math.min(this.rowPrefetch, this.maxRows - this.storedRowCount) : (int) this.maxRows;
        }
        return rowsToFetch;
    }

    void doDefineFetch() throws SQLException {
        int rowsToFetch = getRowsToFetch();
        this.validRows = 0L;
        if (!this.needToPrepareDefineBuffer) {
            throw new Error("doDefineFetch called when needToPrepareDefineBuffer=false " + this.sqlObject.getSql(this.processEscapes, this.convertNcharLiterals));
        }
        if (rowsToFetch > 0) {
            setupForDefine();
            this.t2cOutput[2] = 0;
            this.t2cOutput[5] = this.connection.useNio ? 1 : 0;
            this.t2cOutput[6] = this.defaultLobPrefetchSize;
            if (this.connection.useNio) {
                resetNioAttributesBeforeFetch();
                allocateNioBuffersIfRequired(this.defineChars == null ? 0 : this.defineChars.length, this.defineBytes == null ? 0 : this.defineBytes.length, this.defineIndicators == null ? 0 : this.defineIndicators.length);
            }
            if (this.lobPrefetchMetaData == null) {
                this.lobPrefetchMetaData = getLobPrefetchMetaData();
            }
            resetStateBeforeFetch();
            this.validRows = t2cDefineFetch(this, this.c_state, rowsToFetch, this.OCIPrefetch, this.t2cConnection.queryMetaData1, this.t2cConnection.queryMetaData2, this.t2cConnection.queryMetaData1Offset, this.t2cConnection.queryMetaData2Offset, this.accessors, this.t2cOutput, this.nioBuffers, this.lobPrefetchMetaData);
            if (this.validRows == -1 || this.validRows == -4) {
                this.t2cConnection.checkError((int) this.validRows);
            }
            if (this.t2cOutput[2] != 0) {
                this.sqlWarning = this.t2cConnection.checkError(1, this.sqlWarning);
            }
            if (this.connection.useNio && (this.validRows > 0 || this.validRows == -2)) {
                extractNioDefineBuffers(0);
            }
            if (this.isFetchStreams && this.validRows == -2) {
                copyStreamDataIntoDBA(0);
            }
            if (this.lobPrefetchMetaData != null) {
                processLobPrefetchMetaData(this.lobPrefetchMetaData);
            }
        }
        this.isAllFetched = rowsToFetch < 1 || (this.validRows >= 0 && this.validRows < ((long) rowsToFetch));
    }

    void copyStreamDataIntoDBA(int row) throws SQLException {
        checkValidRowsStatus();
        if (this.accessors != null) {
            for (Accessor a : this.accessors) {
                if (a != null) {
                    switch (a.internalType) {
                        case 8:
                            ((T2CLongAccessor) a).copyStreamDataIntoDBA(row);
                            break;
                        case 24:
                            ((T2CLongRawAccessor) a).copyStreamDataIntoDBA(row);
                            break;
                    }
                }
            }
        }
    }

    void allocateNioBuffersIfRequired(int charSize, int byteSize, int indSize) throws SQLException {
        if (this.nioBuffers == null) {
            this.nioBuffers = new ByteBuffer[4];
        }
        if (byteSize > 0) {
            if (this.nioBuffers[0] == null || this.nioBuffers[0].capacity() < byteSize) {
                this.nioBuffers[0] = ByteBuffer.allocateDirect(byteSize);
            } else if (this.nioBuffers[0] != null) {
                this.nioBuffers[0].rewind();
            }
        }
        int charSize2 = charSize * 2;
        if (charSize2 > 0) {
            if (this.nioBuffers[1] == null || this.nioBuffers[1].capacity() < charSize2) {
                this.nioBuffers[1] = ByteBuffer.allocateDirect(charSize2);
            } else if (this.nioBuffers[1] != null) {
                this.nioBuffers[1].rewind();
            }
        }
        int indSize2 = indSize * 2;
        if (indSize2 > 0) {
            if (this.nioBuffers[2] == null || this.nioBuffers[2].capacity() < indSize2) {
                this.nioBuffers[2] = ByteBuffer.allocateDirect(indSize2);
            } else if (this.nioBuffers[2] != null) {
                this.nioBuffers[2].rewind();
            }
        }
    }

    void doDefineExecuteFetch() throws SQLException {
        short[] queryMetaData1 = null;
        if (this.needToPrepareDefineBuffer || this.needToParse) {
            setupForDefine();
            queryMetaData1 = this.t2cConnection.queryMetaData1;
        }
        this.t2cOutput[0] = 0;
        this.t2cOutput[2] = 0;
        byte[] array_sql = this.sqlObject.getSqlBytes(this.processEscapes, this.convertNcharLiterals);
        this.t2cOutput[5] = this.connection.useNio ? 1 : 0;
        this.t2cOutput[6] = this.defaultLobPrefetchSize;
        this.t2cOutput[7] = this.t2cConnection.useOCIDefaultDefines ? 1 : 0;
        if (this.connection.useNio) {
            resetNioAttributesBeforeFetch();
            allocateNioBuffersIfRequired(this.defineChars == null ? 0 : this.defineChars.length, this.defineBytes == null ? 0 : this.defineBytes.length, this.defineIndicators == null ? 0 : this.defineIndicators.length);
        }
        if (this.lobPrefetchMetaData == null) {
            this.lobPrefetchMetaData = getLobPrefetchMetaData();
        }
        int rowsToFetch = getRowsToFetch();
        this.validRows = 0L;
        try {
            resetStateBeforeFetch();
            this.validRows = t2cDefineExecuteFetch(this, this.c_state, this.numberOfDefinePositions, this.numberOfBindPositions, 0, 0, false, this.needToParse, array_sql, array_sql.length, convertSqlKindEnumToByte(this.sqlKind), rowsToFetch, this.OCIPrefetch, this.bindIndicators, this.bindIndicatorOffset, this.bindBytes, this.bindChars, this.bindByteOffset, this.bindCharOffset, queryMetaData1, this.t2cConnection.queryMetaData2, this.t2cConnection.queryMetaData1Offset, this.t2cConnection.queryMetaData2Offset, true, true, this.accessors, (Binder[][]) null, this.t2cOutput, this.nioBuffers, this.lobPrefetchMetaData);
            if (this.validRows == -1) {
                this.t2cConnection.checkError((int) this.validRows);
            }
            if (this.t2cOutput[2] != 0) {
                this.sqlWarning = this.t2cConnection.checkError(1, this.sqlWarning);
            }
            this.connection.endToEndECIDSequenceNumber = (short) this.t2cOutput[4];
            if (this.connection.useNio && (this.validRows > 0 || this.validRows == -2)) {
                extractNioDefineBuffers(0);
            }
            if (this.isFetchStreams && this.validRows == -2) {
                copyStreamDataIntoDBA(0);
            }
            if (this.lobPrefetchMetaData != null) {
                processLobPrefetchMetaData(this.lobPrefetchMetaData);
            }
            this.isAllFetched = rowsToFetch < 1 || (this.validRows >= 0 && this.validRows < ((long) rowsToFetch));
            this.needToParse = false;
        } catch (IOException iox) {
            this.validRows = 0L;
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), iox).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected void fetch(int firstRow) throws SQLException {
        this.needToRetainRows = this.fetchMode == OracleStatement.FetchMode.APPEND;
        int rowsToFetch = getRowsToFetch();
        if (!$assertionsDisabled && rowsToFetch <= 0) {
            throw new AssertionError("rowsToFetch < 1 (rowsToFetch=" + rowsToFetch + ", maxRows=" + this.maxRows + ", rowPrefetch=" + this.rowPrefetch + ", savedRowPrefetch=" + this.savedRowPrefetch + ")");
        }
        this.validRows = 0L;
        if (rowsToFetch > 0 && this.numberOfDefinePositions > 0) {
            if (this.needToPrepareDefineBuffer) {
                doDefineFetch();
                this.needToPrepareDefineBuffer = false;
            } else {
                this.t2cOutput[2] = 0;
                this.t2cOutput[5] = this.connection.useNio ? 1 : 0;
                this.t2cOutput[6] = this.defaultLobPrefetchSize;
                if (this.connection.useNio) {
                    resetNioAttributesBeforeFetch();
                    allocateNioBuffersIfRequired(this.defineChars == null ? 0 : this.defineChars.length, this.defineBytes == null ? 0 : this.defineBytes.length, this.defineIndicators == null ? 0 : this.defineIndicators.length);
                }
                if (this.lobPrefetchMetaData == null) {
                    this.lobPrefetchMetaData = getLobPrefetchMetaData();
                }
                resetStateBeforeFetch();
                this.validRows = t2cFetch(this, this.c_state, this.needToPrepareDefineBuffer, rowsToFetch, this.OCIPrefetch, this.accessors, this.t2cOutput, this.nioBuffers, this.lobPrefetchMetaData);
                if (this.validRows == -1 || this.validRows == -4) {
                    this.t2cConnection.checkError((int) this.validRows);
                }
                if (this.t2cOutput[2] != 0) {
                    this.sqlWarning = this.t2cConnection.checkError(1, this.sqlWarning);
                }
                if (this.lobPrefetchMetaData != null) {
                    processLobPrefetchMetaData(this.lobPrefetchMetaData);
                }
                if (this.connection.useNio && (this.validRows > 0 || this.validRows == -2)) {
                    extractNioDefineBuffers(0);
                }
                if (this.isFetchStreams && this.validRows == -2) {
                    copyStreamDataIntoDBA(firstRow);
                }
            }
        }
        this.isAllFetched = rowsToFetch < 1 || (this.validRows >= 0 && this.validRows < ((long) rowsToFetch));
        this.needToRetainRows = false;
    }

    void resetNioAttributesBeforeFetch() {
        this.extractedCharOffset = 0;
        this.extractedByteOffset = 0;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void extractNioDefineBuffers(int columnIndex) throws SQLException {
        if (this.accessors == null || this.defineIndicators == null || columnIndex == this.numberOfDefinePositions) {
            return;
        }
        int sizeToCopyBytes = 0;
        int sizeToCopyChars = 0;
        int sizeToCopyIndicators = 0;
        int lengthOffset = 0;
        int indOffset = 0;
        if (!this.hasStream) {
            sizeToCopyBytes = this.defineBytes != null ? this.defineBytes.length : 0;
            sizeToCopyChars = this.defineChars != null ? this.defineChars.length : 0;
            sizeToCopyIndicators = this.defineIndicators.length;
        } else {
            if (this.numberOfDefinePositions > columnIndex) {
                indOffset = this.accessors[columnIndex].indicatorIndex;
                lengthOffset = this.accessors[columnIndex].lengthIndex;
            }
            for (int i = columnIndex; i < this.numberOfDefinePositions; i++) {
                switch (this.accessors[i].internalType) {
                    case 8:
                    case 24:
                        break;
                    default:
                        sizeToCopyBytes += this.accessors[i].byteLength;
                        sizeToCopyChars += this.accessors[i].charLength;
                        sizeToCopyIndicators++;
                }
            }
        }
        ByteBuffer b = this.nioBuffers[0];
        if (b != null && this.defineBytes != null && sizeToCopyBytes > 0) {
            b.position(this.extractedByteOffset);
            b.get(this.defineBytes, this.extractedByteOffset, sizeToCopyBytes);
            this.extractedByteOffset += sizeToCopyBytes;
        }
        if (this.nioBuffers[1] != null && this.defineChars != null) {
            CharBuffer c = this.nioBuffers[1].order(ByteOrder.LITTLE_ENDIAN).asCharBuffer();
            if (sizeToCopyChars > 0) {
                c.position(this.extractedCharOffset);
                c.get(this.defineChars, this.extractedCharOffset, sizeToCopyChars);
                this.extractedCharOffset += sizeToCopyChars;
            }
        }
        if (this.nioBuffers[2] != null) {
            ShortBuffer s = this.nioBuffers[2].order(ByteOrder.LITTLE_ENDIAN).asShortBuffer();
            if (this.hasStream) {
                if (sizeToCopyIndicators > 0) {
                    s.position(indOffset);
                    s.get(this.defineIndicators, indOffset, sizeToCopyIndicators);
                    s.position(lengthOffset);
                    s.get(this.defineIndicators, lengthOffset, sizeToCopyIndicators);
                    return;
                }
                return;
            }
            s.get(this.defineIndicators);
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void doClose() throws SQLException {
        if (this.defineBytes != null) {
            this.defineBytes = null;
        }
        if (this.defineChars != null) {
            this.defineChars = null;
        }
        if (this.defineIndicators != null) {
            this.defineIndicators = null;
        }
        int returnCode = t2cCloseStatement(this.c_state);
        this.nioBuffers = null;
        if (returnCode != 0) {
            this.t2cConnection.checkError(returnCode);
        }
        this.t2cOutput = null;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void closeQuery() throws SQLException {
        this.connection.needLine();
        if (this.streamList != null) {
            while (this.nextStream != null) {
                try {
                    this.nextStream.close();
                    this.nextStream = this.nextStream.nextStream;
                } catch (IOException exc) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), exc).fillInStackTrace());
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement, oracle.jdbc.driver.GeneratedStatement
    void closeUsedStreams(int columnIndex) throws SQLException {
        while (this.nextStream != null && this.nextStream.columnIndex < 1 + this.offsetOfFirstUserColumn + columnIndex) {
            try {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, this.CLASS_NAME, "closeUsedStreams", "closeUsedStream({0}) closing {1} at index {2}", (String) null, (Throwable) null, Integer.valueOf(1 + this.offsetOfFirstUserColumn + columnIndex), this.nextStream, Integer.valueOf(this.nextStream.columnIndex));
                this.nextStream.close();
                this.nextStream = this.nextStream.nextStream;
            } catch (IOException exc) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), exc).fillInStackTrace());
            }
        }
        if (this.nextStream != null) {
            try {
                this.nextStream.needBytes();
            } catch (IOException e) {
                interalCloseOnIOException(e);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), e).fillInStackTrace());
            }
        }
    }

    void interalCloseOnIOException(IOException ea) throws SQLException {
        this.closed = true;
        if (this.currentResultSet != null) {
            this.currentResultSet.closed = true;
        }
        doClose();
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void fetchDmlReturnParams() throws SQLException {
        this.rowsDmlReturned = t2cGetRowsDmlReturned(this.c_state);
        if (this.rowsDmlReturned != 0) {
            allocateDmlReturnStorage();
            resetStateBeforeFetch();
            int status = t2cFetchDmlReturnParams(this.c_state, this, this.accessors, this.returnParamBytes, this.returnParamChars, this.returnParamIndicators);
            if (status == -1 || status == -4) {
                this.t2cConnection.checkError(status);
            }
            if (this.t2cOutput[2] != 0) {
                this.sqlWarning = this.t2cConnection.checkError(1, this.sqlWarning);
            }
            if (this.connection.useNio && (status > 0 || status == -2)) {
                extractNioDefineBuffers(0);
            }
        }
        AggregateByteArray _bindData = (AggregateByteArray) this.bindData;
        if (this.returnParamBytes != null) {
            _bindData.setBytes(this.returnParamBytes);
        }
        ((T2CCharByteArray) _bindData.extension).setChars(this.returnParamChars);
        ((T2CCharByteArray) _bindData.extension).setDBConversion(this.connection.conversion);
        int bindBytesLength = this.returnParamBytes == null ? 0 : this.returnParamBytes.length;
        int nullIndex = 0;
        int lenIndex = this.numReturnParams * this.rowsDmlReturned;
        int byteOffset = 0;
        int charOffset = bindBytesLength;
        int returnParamCount = 0;
        for (int aIndex = 0; aIndex < this.numberOfBindPositions; aIndex++) {
            Accessor accessor = this.accessors[aIndex];
            if (accessor != null) {
                accessor.setCapacity(this.rowsDmlReturned);
                for (int rowNumber = 0; rowNumber < this.rowsDmlReturned; rowNumber++) {
                    if (accessor.internalType == 109 || accessor.internalType == 111) {
                        returnParamCount++;
                    } else {
                        int i = lenIndex;
                        lenIndex++;
                        int len = this.returnParamIndicators[i];
                        int i2 = nullIndex;
                        nullIndex++;
                        boolean isNull = this.returnParamIndicators[i2] == -1;
                        int lenPrefix = 0;
                        if (accessor.internalType == 104) {
                            lenPrefix = 2;
                        } else if (accessor.defineType == 6 || accessor.defineType == 9 || accessor.defineType == 1) {
                            lenPrefix = 1;
                        }
                        if (accessor.charLength > 0) {
                            accessor.setOffset(rowNumber, charOffset + lenPrefix);
                            charOffset += accessor.charLength;
                        } else {
                            accessor.setOffset(rowNumber, byteOffset + lenPrefix);
                            byteOffset += accessor.byteLength;
                        }
                        if (isNull || len == 0) {
                            accessor.setLengthAndNull(rowNumber, 0);
                        } else {
                            if (accessor.internalType == 1) {
                                len /= 2;
                            }
                            accessor.setLengthAndNull(rowNumber, len);
                        }
                    }
                }
            }
        }
        this.returnParamsFetched = true;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void processDmlReturningBind() throws SQLException {
        super.processDmlReturningBind();
        this.returnParamRowBytes = 0;
        this.returnParamRowChars = 0;
        for (int i = 0; i < this.numberOfBindPositions; i++) {
            Accessor accessor = this.accessors[i];
            if (accessor != null) {
                if (accessor.charLength > 0) {
                    this.returnParamRowChars += accessor.charLength;
                } else {
                    this.returnParamRowBytes += accessor.byteLength;
                }
            }
        }
        this.returnParamMeta[1] = this.returnParamRowBytes;
        this.returnParamMeta[2] = this.returnParamRowChars;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void allocateDmlReturnStorage() {
        if (this.rowsDmlReturned == 0) {
            return;
        }
        int totalBytes = this.returnParamRowBytes * this.rowsDmlReturned;
        int totalChars = this.returnParamRowChars * this.rowsDmlReturned;
        int indicatorLength = 2 * this.numReturnParams * this.rowsDmlReturned;
        this.returnParamBytes = new byte[totalBytes];
        this.returnParamChars = new char[totalChars];
        this.returnParamIndicators = new int[indicatorLength];
        for (int i = 0; i < this.numberOfBindPositions; i++) {
            Accessor accessor = this.accessors[i];
            if (accessor != null) {
                accessor.setCapacity(this.rowsDmlReturned);
            }
        }
    }

    void cleanupReturnParameterBuffers() {
        this.returnParamBytes = null;
        this.returnParamChars = null;
        this.returnParamIndicators = null;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void initializeIndicatorSubRange() {
        this.bindIndicatorSubRange = this.numberOfBindPositions * PREAMBLE_PER_POSITION;
    }

    int calculateIndicatorSubRangeSize() {
        return this.numberOfBindPositions * PREAMBLE_PER_POSITION;
    }

    short getInoutIndicator(int bindPosition) {
        return this.bindIndicators[bindPosition * PREAMBLE_PER_POSITION];
    }

    void resetStateBeforeFetch() {
        this.lastProcessedCell = 0;
        this.lastProcessedAccessorIndex = 0;
        this.accessorsProcessed = 0;
        this.previousMode = 0;
        if (this.rowData != null) {
            if (this.needToRetainRows) {
                this.rowData.setPosition(this.rowData.length());
            } else {
                this.rowData.reset();
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:76:0x021a A[Catch: SQLException -> 0x02b5, TryCatch #0 {SQLException -> 0x02b5, blocks: (B:2:0x0000, B:4:0x0008, B:5:0x000d, B:6:0x0022, B:10:0x004d, B:14:0x0080, B:16:0x0094, B:18:0x009c, B:20:0x00a7, B:21:0x00ac, B:22:0x00e0, B:23:0x00e9, B:24:0x00ef, B:25:0x0103, B:27:0x010c, B:29:0x0114, B:31:0x0139, B:33:0x0146, B:37:0x0156, B:41:0x0166, B:45:0x0177, B:49:0x0188, B:67:0x01df, B:68:0x01e5, B:73:0x01ff, B:76:0x021a, B:80:0x024d, B:82:0x025f, B:84:0x0267, B:86:0x0284, B:88:0x028c, B:71:0x01f0, B:48:0x0183, B:44:0x0172, B:40:0x0161, B:36:0x0151, B:50:0x0194, B:54:0x01a4, B:58:0x01b4, B:62:0x01c5, B:66:0x01d6, B:65:0x01d1, B:61:0x01c0, B:57:0x01af, B:53:0x019f), top: B:95:0x0000 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int updateData(int r6, int r7, int r8, int[] r9, byte[] r10) {
        /*
            Method dump skipped, instructions count: 706
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CStatement.updateData(int, int, int, int[], byte[]):int");
    }

    final boolean bit(long value, long mask) {
        return (value & mask) == value;
    }

    public static String toHex(byte[] value, int length) {
        if (value == null) {
            return "null";
        }
        if (length > value.length) {
            return "byte array not long enough";
        }
        String result = "0:";
        for (int i = 0; i < length; i++) {
            if (i != 0 && i % 10 == 0) {
                result = result + "\n" + i + ": ";
            }
            result = result + OracleLog.toHex(value[i]) + " ";
        }
        return result;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void locationToPutBytes(Accessor acc, int row, int length) throws SQLException {
        acc.setOffset(row, allocateRowDataSpace(length));
    }

    @Override // oracle.jdbc.driver.OracleStatement
    long allocateRowDataSpace(int size) {
        return this.rowData.length();
    }

    @Override // oracle.jdbc.driver.OracleStatement, oracle.jdbc.OracleStatement
    public String getSqlId() throws SQLException {
        return super.getSqlId();
    }

    @Override // oracle.jdbc.driver.OracleStatement
    boolean areOutBindsStoredInBindData() {
        return true;
    }

    @Override // oracle.jdbc.driver.OracleStatement
    Accessor allocateAccessor(int internal_type, int external_type, int col_index, int max_len, short form, String typeName, boolean isOutBind) throws SQLException {
        switch (internal_type) {
            case 1:
                if (isOutBind) {
                    if (typeName != null) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 12, "sqlType=" + external_type).fillInStackTrace());
                    }
                    Accessor result = new T2CVarcharAccessor(this, max_len, form, external_type, isOutBind);
                    return result;
                }
                break;
            case 8:
                if (isOutBind && typeName != null) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 12, "sqlType=" + external_type).fillInStackTrace());
                }
                if (isOutBind) {
                    return new VarcharAccessor(this, max_len, form, external_type, isOutBind, isOutBind ? areOutBindsStoredInBindData() : false);
                }
                return new T2CLongAccessor(this, col_index, max_len, form, external_type, isOutBind);
            case 24:
                if (isOutBind && typeName != null) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 12, "sqlType=" + external_type).fillInStackTrace());
                }
                if (isOutBind) {
                    return new OutRawAccessor(this, max_len, form, external_type);
                }
                return new T2CLongRawAccessor(this, col_index, max_len, form, external_type, isOutBind);
            case 102:
            case DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN /* 116 */:
                if (isOutBind && typeName != null) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 12, "sqlType=" + external_type).fillInStackTrace());
                }
                Accessor result2 = new T2CResultSetAccessor(this, max_len, form, external_type, isOutBind);
                return result2;
        }
        return super.allocateAccessor(internal_type, external_type, col_index, max_len, form, typeName, isOutBind);
    }

    @Override // oracle.jdbc.driver.OracleStatement
    void doDescribe(boolean includeNames) throws SQLException {
        boolean try_again;
        if (this.closed) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 9).fillInStackTrace());
        }
        if (this.described) {
            return;
        }
        if (!this.isOpen) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_STMT_NOT_EXECUTED).fillInStackTrace());
        }
        do {
            try_again = false;
            boolean needToExecute = this.sqlKind.isSELECT() && this.needToParse && !(this.described && this.serverCursor);
            byte[] array_sql = needToExecute ? this.sqlObject.getSqlBytes(this.processEscapes, this.convertNcharLiterals) : PhysicalConnection.EMPTY_BYTE_ARRAY;
            this.numberOfDefinePositions = t2cDescribe(this.c_state, this.t2cConnection.queryMetaData1, this.t2cConnection.queryMetaData2, this.t2cConnection.queryMetaData1Offset, this.t2cConnection.queryMetaData2Offset, this.t2cConnection.queryMetaData1Size, this.t2cConnection.queryMetaData2Size, array_sql, array_sql.length, needToExecute);
            if (!this.described) {
                this.described = true;
            }
            if (this.numberOfDefinePositions == -1) {
                this.t2cConnection.checkError(this.numberOfDefinePositions);
            }
            if (this.numberOfDefinePositions == T2C_EXTEND_BUFFER) {
                try_again = true;
                this.t2cConnection.reallocateQueryMetaData(this.t2cConnection.queryMetaData1Size * 2, this.t2cConnection.queryMetaData2Size * 2);
            }
        } while (try_again);
        processDescribeData();
    }

    @Override // oracle.jdbc.driver.OracleStatement
    protected ByteArray createBindData() {
        ByteArray adtByteArray = DynamicByteArray.createDynamicByteArray(this.connection.getDiagnosable(), this.connection.getBlockSource());
        ByteArray ibtBindCharsArray = new T2CCharByteArray(this.connection.getDiagnosable(), PhysicalConnection.EMPTY_CHAR_ARRAY, adtByteArray);
        ByteArray ibtBindBytesArray = new AggregateByteArray(this.connection.getDiagnosable(), PhysicalConnection.EMPTY_BYTE_ARRAY, ibtBindCharsArray);
        ByteArray bindCharArray = new T2CCharByteArray(this.connection.getDiagnosable(), PhysicalConnection.EMPTY_CHAR_ARRAY, ibtBindBytesArray);
        return new AggregateByteArray(this.connection.getDiagnosable(), PhysicalConnection.EMPTY_BYTE_ARRAY, bindCharArray);
    }
}
