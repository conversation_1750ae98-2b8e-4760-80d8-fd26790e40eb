package oracle.jdbc.driver.configuration;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.sql.SQLException;
import java.util.Base64;
import javax.net.ssl.HttpsURLConnection;
import oracle.jdbc.util.OracleConfigurationCache;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/configuration/OracleConfigurationHttpsProvider.class */
public class OracleConfigurationHttpsProvider extends OracleConfigurationParsableProvider {
    private static final OracleConfigurationCache CACHE = OracleConfigurationCache.create(100);

    @Override // oracle.jdbc.spi.OracleConfigurationCachableProvider
    public OracleConfigurationCache getCache() {
        return CACHE;
    }

    @Override // oracle.jdbc.driver.configuration.OracleConfigurationParsableProvider
    public InputStream getInputStream(String location) throws ProtocolException, SQLException {
        try {
            HttpURLConnection connection = getHttpURLConnection(location);
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");
            if (this.options.containsKey("authentication") && this.options.get("authentication").equals("basic")) {
                String headerValue = Base64.getEncoder().encodeToString((this.options.get("user") + ":" + this.options.get("password")).getBytes());
                connection.setRequestProperty("Authorization", "Basic " + headerValue);
            }
            connection.setDoOutput(true);
            return connection.getInputStream();
        } catch (IOException ex) {
            throw new SQLException("Error retrieving configuration", ex);
        }
    }

    @Override // oracle.jdbc.spi.OracleConfigurationProvider
    public String getType() {
        return "https";
    }

    HttpURLConnection getHttpURLConnection(String location) throws IOException {
        if (!location.startsWith("https://")) {
            location = "https://" + location;
        }
        URL url = new URL(location);
        return (HttpsURLConnection) url.openConnection();
    }
}
