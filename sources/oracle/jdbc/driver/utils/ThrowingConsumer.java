package oracle.jdbc.driver.utils;

import java.lang.Throwable;
import java.util.function.Consumer;

@FunctionalInterface
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/ThrowingConsumer.class */
public interface ThrowingConsumer<T, E extends Throwable> extends Consumer<T> {
    void acceptOrThrow(T t) throws Throwable;

    @Override // java.util.function.Consumer
    default void accept(T input) {
        try {
            acceptOrThrow(input);
        } catch (Throwable throwable) {
            throw CheckedExceptionHandler.toRuntimeException(throwable);
        }
    }
}
