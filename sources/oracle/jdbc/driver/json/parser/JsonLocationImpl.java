package oracle.jdbc.driver.json.parser;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/parser/JsonLocationImpl.class */
public class JsonLocationImpl {
    static final JsonLocationImpl UNKNOWN = new JsonLocationImpl(-1, -1, -1);
    private final long columnNo;
    private final long lineNo;
    private final long offset;

    public JsonLocationImpl(long lineNo, long columnNo, long streamOffset) {
        this.lineNo = lineNo;
        this.columnNo = columnNo;
        this.offset = streamOffset;
    }

    public long getLineNumber() {
        return this.lineNo;
    }

    public long getColumnNumber() {
        return this.columnNo;
    }

    public long getStreamOffset() {
        return this.offset;
    }

    public String toString() {
        return "(line no=" + this.lineNo + ", column no=" + this.columnNo + ", offset=" + this.offset + ")";
    }
}
