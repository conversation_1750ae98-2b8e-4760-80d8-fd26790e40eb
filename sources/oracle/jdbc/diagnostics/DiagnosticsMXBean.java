package oracle.jdbc.diagnostics;

import javax.management.MXBean;

@MXBean
/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/DiagnosticsMXBean.class */
public interface DiagnosticsMXBean {
    void enableDiagnoseFirstFailureByConnectionIdPrefix(String str);

    void disableDiagnoseFirstFailureByConnectionIdPrefix(String str);

    void enableDiagnoseFirstFailureByTenantName(String str);

    void disableDiagnoseFirstFailureByTenantName(String str);

    void enableDiagnoseFirstFailureByLoggerName(String str);

    void disableDiagnoseFirstFailureByLoggerName(String str);

    void enableDiagnoseFirstFailure();

    void disableDiagnoseFirstFailure();

    void enableLoggingByConnectionIdPrefix(String str);

    void disableLoggingByConnectionIdPrefix(String str);

    void enableLoggingByTenantName(String str);

    void disableLoggingByTenantName(String str);

    void enableLoggingByLoggerName(String str);

    void disableLoggingByLoggerName(String str);

    void enableLogging();

    void disableLogging();

    boolean isLoggingEnabled();

    void enableSensitiveDiagnosticsByConnectionIdPrefix(String str);

    void disableSensitiveDiagnosticsByConnectionIdPrefix(String str);

    void enableSensitiveDiagnosticsByTenantName(String str);

    void disableSensitiveDiagnosticsByTenantName(String str);

    void enableSensitiveDiagnosticsByLoggerName(String str);

    void disableSensitiveDiagnosticsByLoggerName(String str);

    void enableSensitiveDiagnostics();

    void disableSensitiveDiagnostics();

    void updateDiagnosticLevelByConnectionIdPrefix(String str, String str2);

    void updateDiagnosticLevelByTenantName(String str, String str2);

    void updateDiagnosticLevelByLoggerName(String str, String str2);

    void updateDiagnosticLevel(String str);

    void updateBufferSizeByConnectionIdPrefix(String str, Integer num);

    void updateBufferSizeByTenantName(String str, Integer num);

    void updateBufferSizeByLoggerName(String str, Integer num);

    void updateBufferSize(Integer num);

    void readLoggingConfigFile(String str);

    void addErrorCodeToWatchList(String str);

    void removeErrorCodeFromWatchList(String str);

    String showErrorCodesWatchList();

    void resetErrorCodeWatchList();

    void dumpDiagnoseFirstFailure();

    void dumpDiagnoseFirstFailureWhenFutureExceptionContains(String str);

    String showExceptionKeywords();

    String showRecentOperations();

    void clearExceptionKeywords();

    void enableWriteLogsToDiagnoseFirstFailure(Boolean bool);

    boolean isWriteLogsToDiagnoseFirstFailure();

    void enableMetrics();

    void disableMetrics();

    String showMetrics();

    void clearMetrics();

    boolean isMetricsEnabled();
}
