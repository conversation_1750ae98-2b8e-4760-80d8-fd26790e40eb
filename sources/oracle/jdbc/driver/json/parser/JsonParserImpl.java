package oracle.jdbc.driver.json.parser;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.ArrayDeque;
import java.util.NoSuchElementException;
import oracle.jdbc.driver.json.BufferPoolImpl;
import oracle.jdbc.driver.json.JakartaParserWrapper;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.JsonpParserWrapper;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.parser.JsonTokenizer;
import oracle.jdbc.driver.json.tree.OracleJsonArrayImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDecimalImpl;
import oracle.jdbc.driver.json.tree.OracleJsonObjectImpl;
import oracle.jdbc.driver.json.tree.OracleJsonStringImpl;
import oracle.sql.json.OracleJsonArray;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonParser;
import oracle.sql.json.OracleJsonStructure;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/parser/JsonParserImpl.class */
public class JsonParserImpl implements OracleJsonParser {
    private OracleJsonParser.Event currentEvent;
    private final JsonTokenizer tokenizer;
    private Context currentContext = new NoneContext();
    private final Stack stack = new Stack();

    public JsonParserImpl(Reader reader, BufferPoolImpl bufferPool) {
        this.tokenizer = new JsonTokenizer(reader, bufferPool);
    }

    public JsonParserImpl(InputStream in, BufferPoolImpl bufferPool) {
        UnicodeDetectingInputStream uin = new UnicodeDetectingInputStream(in);
        this.tokenizer = new JsonTokenizer(new InputStreamReader(uin, uin.getCharset()), bufferPool);
    }

    private OracleJsonExceptions.ExceptionFactory factory() {
        return OracleJsonExceptions.ORACLE_FACTORY;
    }

    @Override // oracle.sql.json.OracleJsonParser
    public String getString() {
        if (this.currentEvent == OracleJsonParser.Event.KEY_NAME || this.currentEvent == OracleJsonParser.Event.VALUE_STRING || this.currentEvent == OracleJsonParser.Event.VALUE_DECIMAL) {
            return this.tokenizer.getValue();
        }
        throw OracleJsonExceptions.PARSER_GETSTRING_ERR.create(factory(), this.currentEvent);
    }

    @Override // oracle.sql.json.OracleJsonParser
    public boolean isIntegralNumber() {
        if (this.currentEvent != OracleJsonParser.Event.VALUE_DECIMAL) {
            throw OracleJsonExceptions.PARSER_ISINTEGRAL_ERR.create(factory(), this.currentEvent);
        }
        return this.tokenizer.isIntegral();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public int getInt() {
        if (this.currentEvent != OracleJsonParser.Event.VALUE_DECIMAL) {
            throw OracleJsonExceptions.PARSER_GETBIGDECIMAL_ERR.create(factory(), this.currentEvent);
        }
        return this.tokenizer.getInt();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public double getDouble() {
        return getBigDecimal().doubleValue();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public float getFloat() {
        return getBigDecimal().floatValue();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public BigInteger getBigInteger() {
        return getBigDecimal().toBigInteger();
    }

    boolean isDefinitelyInt() {
        return this.tokenizer.isDefinitelyInt();
    }

    boolean isDefinitelyLong() {
        return this.tokenizer.isDefinitelyLong();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public long getLong() {
        if (this.currentEvent != OracleJsonParser.Event.VALUE_DECIMAL) {
            throw OracleJsonExceptions.PARSER_GETLONG_ERR.create(factory(), this.currentEvent);
        }
        return this.tokenizer.getLong();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public BigDecimal getBigDecimal() {
        if (this.currentEvent != OracleJsonParser.Event.VALUE_DECIMAL) {
            throw OracleJsonExceptions.PARSER_GETBIGDECIMAL_ERR.create(factory(), this.currentEvent);
        }
        return this.tokenizer.getBigDecimal();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OracleJsonArray getArray() {
        if (this.currentEvent != OracleJsonParser.Event.START_ARRAY) {
            throw OracleJsonExceptions.PARSER_GETARRAY_ERR.create(factory(), this.currentEvent);
        }
        return getValue().asJsonArray();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OracleJsonObject getObject() {
        if (this.currentEvent != OracleJsonParser.Event.START_OBJECT) {
            throw OracleJsonExceptions.PARSER_GETOBJECT_ERR.create(factory(), this.currentEvent);
        }
        return getValue().asJsonObject();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OracleJsonValue getValue() {
        OracleJsonValue value;
        ArrayDeque<OracleJsonStructure> stack = new ArrayDeque<>(4);
        String currentKey = null;
        if (this.currentEvent == null || this.currentEvent == OracleJsonParser.Event.END_ARRAY || this.currentEvent == OracleJsonParser.Event.END_OBJECT) {
            throw OracleJsonExceptions.BAD_PARSER_STATE_VALUE.create(factory(), new Object[0]);
        }
        while (true) {
            switch (this.currentEvent) {
                case END_ARRAY:
                case END_OBJECT:
                    value = stack.pop();
                    break;
                case KEY_NAME:
                    currentKey = getString();
                    next();
                    continue;
                case START_ARRAY:
                    value = new OracleJsonArrayImpl();
                    addValue(value, stack, currentKey);
                    stack.push(value.asJsonArray());
                    break;
                case START_OBJECT:
                    value = new OracleJsonObjectImpl();
                    addValue(value, stack, currentKey);
                    stack.push(value.asJsonObject());
                    break;
                case VALUE_DECIMAL:
                    if (isDefinitelyInt()) {
                        value = new OracleJsonDecimalImpl(getInt(), (OracleJsonDecimal.TargetType) null);
                    } else if (isDefinitelyLong()) {
                        value = new OracleJsonDecimalImpl(getLong(), (OracleJsonDecimal.TargetType) null);
                    } else {
                        value = new OracleJsonDecimalImpl(getBigDecimal());
                    }
                    addValue(value, stack, currentKey);
                    break;
                case VALUE_STRING:
                    value = new OracleJsonStringImpl(getString());
                    addValue(value, stack, currentKey);
                    break;
                case VALUE_TRUE:
                    value = OracleJsonValue.TRUE;
                    addValue(value, stack, currentKey);
                    break;
                case VALUE_FALSE:
                    value = OracleJsonValue.FALSE;
                    addValue(value, stack, currentKey);
                    break;
                case VALUE_NULL:
                    value = OracleJsonValue.NULL;
                    addValue(value, stack, currentKey);
                    break;
                case VALUE_BINARY:
                case VALUE_DATE:
                case VALUE_DOUBLE:
                case VALUE_FLOAT:
                case VALUE_INTERVALDS:
                case VALUE_INTERVALYM:
                case VALUE_TIMESTAMP:
                case VALUE_VECTOR:
                default:
                    throw new IllegalStateException();
            }
            currentKey = null;
            if (!stack.isEmpty()) {
                next();
            } else {
                return value;
            }
        }
    }

    private static void addValue(OracleJsonValue v, ArrayDeque<OracleJsonStructure> stack, String currentKey) {
        if (stack.isEmpty()) {
            return;
        }
        OracleJsonStructure parent = stack.peek();
        if (parent.getOracleJsonType() == OracleJsonValue.OracleJsonType.OBJECT) {
            parent.asJsonObject().put((OracleJsonObject) currentKey, (String) v);
        } else {
            parent.asJsonArray().add((OracleJsonArray) v);
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public void skipArray() {
        if (this.currentEvent == OracleJsonParser.Event.START_ARRAY) {
            this.currentContext.skip();
            this.currentContext = this.stack.pop();
            this.currentEvent = OracleJsonParser.Event.END_ARRAY;
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public void skipObject() {
        if (this.currentEvent == OracleJsonParser.Event.START_OBJECT) {
            this.currentContext.skip();
            this.currentContext = this.stack.pop();
            this.currentEvent = OracleJsonParser.Event.END_OBJECT;
        }
    }

    public JsonLocationImpl getLocation() {
        return this.tokenizer.getLocation();
    }

    public JsonLocationImpl getLastCharLocation() {
        return this.tokenizer.getLastCharLocation();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public boolean hasNext() {
        if (this.stack.isEmpty() && this.currentEvent != null && this.currentEvent.compareTo(OracleJsonParser.Event.KEY_NAME) > 0) {
            JsonTokenizer.JsonToken token = this.tokenizer.nextToken();
            if (token != JsonTokenizer.JsonToken.EOF) {
                throw OracleJsonExceptions.PARSER_EXPECTED_EOF.create(factory(), this.currentEvent);
            }
            return false;
        }
        if (!this.stack.isEmpty() && !this.tokenizer.hasNextToken()) {
            this.currentEvent = this.currentContext.getNextEvent();
            return false;
        }
        return true;
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OracleJsonParser.Event next() {
        if (!hasNext()) {
            throw new NoSuchElementException();
        }
        OracleJsonParser.Event nextEvent = this.currentContext.getNextEvent();
        this.currentEvent = nextEvent;
        return nextEvent;
    }

    @Override // oracle.sql.json.OracleJsonParser, java.io.Closeable, java.lang.AutoCloseable
    public void close() {
        try {
            this.tokenizer.close();
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(factory(), this.currentEvent);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/parser/JsonParserImpl$Stack.class */
    private static final class Stack {
        private Context head;

        private Stack() {
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void push(Context context) {
            context.next = this.head;
            this.head = context;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public Context pop() {
            if (this.head == null) {
                throw new NoSuchElementException();
            }
            Context temp = this.head;
            this.head = this.head.next;
            return temp;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public boolean isEmpty() {
            return this.head == null;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/parser/JsonParserImpl$Context.class */
    private abstract class Context {
        Context next;

        abstract OracleJsonParser.Event getNextEvent();

        abstract void skip();

        private Context() {
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/parser/JsonParserImpl$NoneContext.class */
    private final class NoneContext extends Context {
        private NoneContext() {
            super();
        }

        @Override // oracle.jdbc.driver.json.parser.JsonParserImpl.Context
        public OracleJsonParser.Event getNextEvent() {
            JsonTokenizer.JsonToken token = JsonParserImpl.this.tokenizer.nextToken();
            if (token == JsonTokenizer.JsonToken.CURLYOPEN) {
                JsonParserImpl.this.stack.push(JsonParserImpl.this.currentContext);
                JsonParserImpl.this.currentContext = new ObjectContext();
                return OracleJsonParser.Event.START_OBJECT;
            }
            if (token == JsonTokenizer.JsonToken.SQUAREOPEN) {
                JsonParserImpl.this.stack.push(JsonParserImpl.this.currentContext);
                JsonParserImpl.this.currentContext = new ArrayContext();
                return OracleJsonParser.Event.START_ARRAY;
            }
            if (!token.isValue()) {
                throw JsonParserImpl.this.parsingException(token, "[CURLYOPEN, SQUAREOPEN, STRING, NUMBER, TRUE, FALSE, NULL]");
            }
            return token.getEvent();
        }

        @Override // oracle.jdbc.driver.json.parser.JsonParserImpl.Context
        void skip() {
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public RuntimeException parsingException(JsonTokenizer.JsonToken token, String expectedTokens) {
        JsonLocationImpl location = getLastCharLocation();
        return OracleJsonExceptions.PARSER_INVALID_TOKEN.create(factory(), token, Long.valueOf(location.getLineNumber()), Long.valueOf(location.getColumnNumber()), expectedTokens);
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/parser/JsonParserImpl$ObjectContext.class */
    private final class ObjectContext extends Context {
        private boolean firstValue;

        private ObjectContext() {
            super();
            this.firstValue = true;
        }

        @Override // oracle.jdbc.driver.json.parser.JsonParserImpl.Context
        public OracleJsonParser.Event getNextEvent() {
            JsonTokenizer.JsonToken token = JsonParserImpl.this.tokenizer.nextToken();
            if (token == JsonTokenizer.JsonToken.EOF) {
                switch (JsonParserImpl.this.currentEvent) {
                    case KEY_NAME:
                        throw JsonParserImpl.this.parsingException(token, "[COLON]");
                    case START_OBJECT:
                        throw JsonParserImpl.this.parsingException(token, "[STRING, CURLYCLOSE]");
                    default:
                        throw JsonParserImpl.this.parsingException(token, "[COMMA, CURLYCLOSE]");
                }
            }
            if (JsonParserImpl.this.currentEvent == OracleJsonParser.Event.KEY_NAME) {
                if (token != JsonTokenizer.JsonToken.COLON) {
                    throw JsonParserImpl.this.parsingException(token, "[COLON]");
                }
                JsonTokenizer.JsonToken token2 = JsonParserImpl.this.tokenizer.nextToken();
                if (token2.isValue()) {
                    return token2.getEvent();
                }
                if (token2 == JsonTokenizer.JsonToken.CURLYOPEN) {
                    JsonParserImpl.this.stack.push(JsonParserImpl.this.currentContext);
                    JsonParserImpl.this.currentContext = JsonParserImpl.this.new ObjectContext();
                    return OracleJsonParser.Event.START_OBJECT;
                }
                if (token2 != JsonTokenizer.JsonToken.SQUAREOPEN) {
                    throw JsonParserImpl.this.parsingException(token2, "[CURLYOPEN, SQUAREOPEN, STRING, NUMBER, TRUE, FALSE, NULL]");
                }
                JsonParserImpl.this.stack.push(JsonParserImpl.this.currentContext);
                JsonParserImpl.this.currentContext = new ArrayContext();
                return OracleJsonParser.Event.START_ARRAY;
            }
            if (token == JsonTokenizer.JsonToken.CURLYCLOSE) {
                JsonParserImpl.this.currentContext = JsonParserImpl.this.stack.pop();
                return OracleJsonParser.Event.END_OBJECT;
            }
            if (this.firstValue) {
                this.firstValue = false;
            } else {
                if (token != JsonTokenizer.JsonToken.COMMA) {
                    throw JsonParserImpl.this.parsingException(token, "[COMMA]");
                }
                token = JsonParserImpl.this.tokenizer.nextToken();
            }
            if (token != JsonTokenizer.JsonToken.STRING) {
                throw JsonParserImpl.this.parsingException(token, "[STRING]");
            }
            return OracleJsonParser.Event.KEY_NAME;
        }

        @Override // oracle.jdbc.driver.json.parser.JsonParserImpl.Context
        void skip() {
            int depth = 1;
            while (true) {
                JsonTokenizer.JsonToken token = JsonParserImpl.this.tokenizer.nextToken();
                switch (token) {
                    case CURLYCLOSE:
                        depth--;
                        break;
                    case CURLYOPEN:
                        depth++;
                        break;
                }
                if (token == JsonTokenizer.JsonToken.CURLYCLOSE && depth == 0) {
                    return;
                }
            }
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/parser/JsonParserImpl$ArrayContext.class */
    private final class ArrayContext extends Context {
        private boolean firstValue;

        private ArrayContext() {
            super();
            this.firstValue = true;
        }

        @Override // oracle.jdbc.driver.json.parser.JsonParserImpl.Context
        public OracleJsonParser.Event getNextEvent() {
            JsonTokenizer.JsonToken token = JsonParserImpl.this.tokenizer.nextToken();
            if (token == JsonTokenizer.JsonToken.EOF) {
                switch (JsonParserImpl.this.currentEvent) {
                    case START_ARRAY:
                        throw JsonParserImpl.this.parsingException(token, "[CURLYOPEN, SQUAREOPEN, STRING, NUMBER, TRUE, FALSE, NULL]");
                    default:
                        throw JsonParserImpl.this.parsingException(token, "[COMMA, CURLYCLOSE]");
                }
            }
            if (token == JsonTokenizer.JsonToken.SQUARECLOSE) {
                JsonParserImpl.this.currentContext = JsonParserImpl.this.stack.pop();
                return OracleJsonParser.Event.END_ARRAY;
            }
            if (this.firstValue) {
                this.firstValue = false;
            } else {
                if (token != JsonTokenizer.JsonToken.COMMA) {
                    throw JsonParserImpl.this.parsingException(token, "[COMMA]");
                }
                token = JsonParserImpl.this.tokenizer.nextToken();
            }
            if (token.isValue()) {
                return token.getEvent();
            }
            if (token == JsonTokenizer.JsonToken.CURLYOPEN) {
                JsonParserImpl.this.stack.push(JsonParserImpl.this.currentContext);
                JsonParserImpl.this.currentContext = new ObjectContext();
                return OracleJsonParser.Event.START_OBJECT;
            }
            if (token != JsonTokenizer.JsonToken.SQUAREOPEN) {
                throw JsonParserImpl.this.parsingException(token, "[CURLYOPEN, SQUAREOPEN, STRING, NUMBER, TRUE, FALSE, NULL]");
            }
            JsonParserImpl.this.stack.push(JsonParserImpl.this.currentContext);
            JsonParserImpl.this.currentContext = JsonParserImpl.this.new ArrayContext();
            return OracleJsonParser.Event.START_ARRAY;
        }

        @Override // oracle.jdbc.driver.json.parser.JsonParserImpl.Context
        void skip() {
            int depth = 1;
            while (true) {
                JsonTokenizer.JsonToken token = JsonParserImpl.this.tokenizer.nextToken();
                switch (token) {
                    case SQUARECLOSE:
                        depth--;
                        break;
                    case SQUAREOPEN:
                        depth++;
                        break;
                }
                if (token == JsonTokenizer.JsonToken.SQUARECLOSE && depth == 0) {
                    return;
                }
            }
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public <T> T wrap(Class<T> wrapper) {
        try {
            if (Jsonp.isJakartaJsonStream(wrapper)) {
                return wrapper.cast(new JakartaParserWrapper(this));
            }
            return wrapper.cast(new JsonpParserWrapper(this));
        } catch (ClassCastException e) {
            throw OracleJsonExceptions.BAD_WRAP.create(factory(), e, wrapper.getName());
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public Period getPeriod() {
        throw new UnsupportedOperationException();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public Duration getDuration() {
        throw new UnsupportedOperationException();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public byte[] getBytes() {
        throw new UnsupportedOperationException();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OffsetDateTime getOffsetDateTime() {
        throw new UnsupportedOperationException();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public LocalDateTime getLocalDateTime() {
        throw new UnsupportedOperationException();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public void getBytes(OutputStream out) {
        throw new UnsupportedOperationException();
    }
}
