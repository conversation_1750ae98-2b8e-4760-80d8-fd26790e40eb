package oracle.jdbc.diagnostics;

import java.nio.ByteBuffer;
import java.text.NumberFormat;
import java.util.Arrays;
import java.util.Objects;
import java.util.Properties;
import java.util.Random;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.clio.annotations.Format;
import oracle.jdbc.clio.annotations.Sensitive;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter.class */
public class Parameter<T> {
    protected T value;
    public static final Parameter BLINDED = BlindedParameter.INSTANCE;
    private static final char[] ASCII = {'.', '.', '.', '.', '.', '.', '.', '.', '.', '.', 9252, '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '!', '\"', '#', '$', '%', '&', '\'', '(', ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?', '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\', ']', '^', '_', '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}', '~', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.'};
    private static final String[] HEX = {"00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "0A", "0B", "0C", "0D", "0E", "0F", OracleConnection.CONNECTION_PROPERTY_DEFAULT_ROW_PREFETCH_DEFAULT, "11", "12", "13", "14", OracleConnection.CONNECTION_PROPERTY_THIN_SSL_CONTEXT_CACHE_SIZE_DEFAULT, "16", "17", "18", "19", "1A", "1B", "1C", "1D", "1E", "1F", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "2A", "2B", "2C", "2D", "2E", "2F", OracleConnection.CONNECTION_PROPERTY_MAX_CACHED_BUFFER_SIZE_DEFAULT, "31", "32", "33", "34", "35", "36", "37", "38", "39", "3A", "3B", "3C", "3D", "3E", "3F", oracle.jdbc.internal.OracleConnection.CONNECTION_PROPERTY_JAVANET_MSGQ_BUSYWAIT_DEFAULT, "41", "42", "43", "44", "45", "46", "47", "48", "49", "4A", "4B", "4C", "4D", "4E", "4F", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "5A", "5B", "5C", "5D", "5E", "5F", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "6A", "6B", "6C", "6D", "6E", "6F", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "7A", "7B", "7C", "7D", "7E", "7F", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "8A", "8B", "8C", "8D", "8E", "8F", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "9A", "9B", "9C", "9D", "9E", "9F", "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "AA", "AB", "AC", "AD", "AE", "AF", "B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "BA", "BB", "BC", "BD", "BE", "BF", "C0", "C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "CA", "CB", "CC", "CD", "CE", "CF", "D0", "D1", "D2", "D3", "D4", "D5", "D6", "D7", "D8", "D9", "DA", "DB", "DC", "DD", "DE", "DF", "E0", "E1", "E2", "E3", "E4", "E5", "E6", "E7", "E8", "E9", "EA", "EB", "EC", "ED", "EE", "EF", "F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "FA", "FB", "FC", "FD", "FE", "FF"};

    @Sensitive(Sensitive.Dependency.DEPENDENT)
    public static Parameter<?> arg(Format.Style f, Object value, long... params) {
        if (Objects.isNull(value)) {
            return null;
        }
        switch (f) {
            case STRING:
                if (params.length == 0) {
                    return new StringParameter((String) value);
                }
                return new StringParameter((String) value, params[0]);
            case BYTE_ARRAY:
                if (params.length == 0) {
                    return new ByteArrayParameter((byte[]) value);
                }
                return new ByteArrayParameter((byte[]) value, params[0], params[1], params[2]);
            case BYTE_ARRAY_CLONE:
                if (params.length == 0) {
                    return new ByteArrayParameter((byte[]) ((byte[]) value).clone());
                }
                return new ByteArrayParameter((byte[]) ((byte[]) value).clone(), params[0], params[1], params[2]);
            case INTEGER_ARRAY:
                if (params.length == 0) {
                    return new IntegerArrayParameter((int[]) value);
                }
                return new IntegerArrayParameter((int[]) value, params[0], params[1], params[2]);
            case LONG_ARRAY:
                if (params.length == 0) {
                    return new LongArrayParameter((long[]) value);
                }
                return new LongArrayParameter((long[]) value, params[0], params[1], params[2]);
            case BYTE_BUFFER:
                return new ByteBufferParameter((ByteBuffer) value, params[0], params[1], params[2], params[3]);
            case PACKET_DUMP:
                return new PacketDumpParameter((ByteBuffer) value, params[0], params[1]);
            case ISO_DATETIME:
                return null;
            case PROPERTIES:
                return new PropertiesParameter(value);
            default:
                return new Parameter<>(value);
        }
    }

    private Parameter() {
        this.value = null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    protected Parameter(Object obj) {
        this.value = obj;
    }

    public final String toString() {
        if (this.value == null) {
            return "<null>";
        }
        return basicFormat();
    }

    protected String basicFormat() {
        return this.value.toString();
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter$BlindedParameter.class */
    private static final class BlindedParameter<T> extends Parameter<T> {
        private static final BlindedParameter INSTANCE = new BlindedParameter();

        private BlindedParameter() {
            super();
        }

        @Override // oracle.jdbc.diagnostics.Parameter
        protected String basicFormat() {
            return "*****";
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter$StringParameter.class */
    private static class StringParameter extends Parameter<String> {
        private long maxChars;

        protected StringParameter(String value) {
            this(value, Long.MAX_VALUE);
        }

        protected StringParameter(String value, long maxChars) {
            super(value);
            this.maxChars = maxChars;
        }

        @Override // oracle.jdbc.diagnostics.Parameter
        protected String basicFormat() {
            StringBuilder sb;
            if (((String) this.value).length() > this.maxChars) {
                sb = new StringBuilder(((int) this.maxChars) + 6);
            } else {
                sb = new StringBuilder(((String) this.value).length() + 2);
            }
            sb.append('\"');
            int i = 1;
            char[] charArray = ((String) this.value).toCharArray();
            int length = charArray.length;
            int i2 = 0;
            while (true) {
                if (i2 >= length) {
                    break;
                }
                char c = charArray[i2];
                int i3 = i;
                i++;
                if (i3 > this.maxChars) {
                    sb.append(" ... + ");
                    sb.append(((String) this.value).length() - this.maxChars);
                    break;
                }
                if (c == '\n') {
                    sb.append((char) 9252);
                } else if (Character.isISOControl(c)) {
                    sb.append('.');
                } else {
                    sb.append(c);
                }
                i2++;
            }
            sb.append('\"');
            return sb.toString();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter$ByteArrayParameter.class */
    private static class ByteArrayParameter extends Parameter<byte[]> {
        protected int maxBytes;
        protected int offset;
        protected int length;

        protected ByteArrayParameter(byte[] value) {
            this(value, value.length, 0L, value.length);
        }

        protected ByteArrayParameter(byte[] value, long maxBytes, long offset, long length) {
            super(value);
            this.maxBytes = (int) maxBytes;
            this.offset = (int) offset;
            this.length = (int) length;
        }

        @Override // oracle.jdbc.diagnostics.Parameter
        protected String basicFormat() {
            StringBuilder sb;
            if (this.length > this.maxBytes) {
                sb = new StringBuilder((this.maxBytes * 3) + 5);
            } else {
                sb = new StringBuilder((this.length * 3) + 1);
            }
            sb.append('[');
            int iMin = Math.min(this.length, this.maxBytes);
            if (iMin > 0) {
                sb.append(Parameter.HEX[((byte[]) this.value)[this.offset] & 255]);
                for (int i = this.offset + 1; i < this.offset + iMin; i++) {
                    sb.append(' ');
                    sb.append(Parameter.HEX[((byte[]) this.value)[i] & 255]);
                }
            }
            if (this.length > this.maxBytes) {
                sb.append(" ... + ");
                sb.append(this.length - this.maxBytes);
            }
            sb.append(']');
            return sb.toString();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter$IntegerArrayParameter.class */
    private static class IntegerArrayParameter extends Parameter<int[]> {
        protected int maxBytes;
        protected int offset;
        protected int length;

        protected IntegerArrayParameter(int[] value) {
            this(value, value.length, 0L, value.length);
        }

        protected IntegerArrayParameter(int[] value, long maxBytes, long offset, long length) {
            super(value);
            this.maxBytes = (int) maxBytes;
            this.offset = (int) offset;
            this.length = (int) length;
        }

        @Override // oracle.jdbc.diagnostics.Parameter
        protected String basicFormat() {
            return Arrays.toString((int[]) this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter$LongArrayParameter.class */
    private static class LongArrayParameter extends Parameter<long[]> {
        protected int maxBytes;
        protected int offset;
        protected int length;

        protected LongArrayParameter(long[] value) {
            this(value, value.length, 0L, value.length);
        }

        protected LongArrayParameter(long[] value, long maxBytes, long offset, long length) {
            super(value);
            this.maxBytes = (int) maxBytes;
            this.offset = (int) offset;
            this.length = (int) length;
        }

        @Override // oracle.jdbc.diagnostics.Parameter
        protected String basicFormat() {
            return Arrays.toString((long[]) this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter$ByteBufferParameter.class */
    protected static final class ByteBufferParameter extends Parameter<ByteBuffer> {
        private static final int BYTES_PER_LINE = 10;
        private static final String DIVIDER = "     |";
        private static final String BLANK_SPACE = "  ";
        private static final ThreadLocal<NumberFormat> BYTE_COUNT_FORMAT = ThreadLocal.withInitial(() -> {
            NumberFormat f = NumberFormat.getInstance();
            f.setMinimumIntegerDigits(12);
            return f;
        });
        protected int maxBytes;
        protected int offset;
        protected int length;
        protected long initialCount;

        protected ByteBufferParameter(ByteBuffer value, long maxBytes, long offset, long length, long initialCount) {
            super(value);
            this.maxBytes = (int) maxBytes;
            this.offset = (int) offset;
            this.length = (int) length;
            this.initialCount = initialCount;
        }

        @Override // oracle.jdbc.diagnostics.Parameter
        protected final String basicFormat() {
            NumberFormat numberFormat = BYTE_COUNT_FORMAT.get();
            StringBuilder sb = new StringBuilder((this.length * 7) + 64);
            StringBuilder sb2 = new StringBuilder(64);
            int iPosition = ((ByteBuffer) this.value).position();
            int iLimit = ((ByteBuffer) this.value).limit();
            long j = this.initialCount;
            ((ByteBuffer) this.value).position(this.offset);
            ((ByteBuffer) this.value).limit(this.length);
            sb.delete(0, sb.length());
            sb2.delete(0, sb2.length());
            int i = (int) (j % 10);
            if (i > 0) {
                sb.append(numberFormat.format(j));
                sb.append(" : ");
                sb.append(repeat(BLANK_SPACE, i));
                sb2.append(repeat(" ", i));
            }
            while (((ByteBuffer) this.value).hasRemaining()) {
                if (i == 0) {
                    sb.append(numberFormat.format(j));
                    sb.append(" : ");
                }
                int i2 = ((ByteBuffer) this.value).get() & 255;
                sb.append(Parameter.HEX[i2]);
                sb2.append(Parameter.ASCII[i2]);
                j++;
                i++;
                if (i == 10) {
                    sb.append(DIVIDER);
                    sb.append(sb2.substring(0, sb2.length()));
                    sb.append("|\n");
                    sb2.delete(0, sb2.length());
                    i = 0;
                }
            }
            if (i > 0) {
                int i3 = (10 - i) - 1;
                sb.append(repeat(BLANK_SPACE, Math.max(0, i3 + 1)));
                sb.append(DIVIDER);
                sb.append(sb2.substring(0, sb2.length()));
                sb.append(repeat(" ", Math.max(0, i3 + 1)));
                sb.append("|\n");
                sb2.delete(0, sb2.length());
            }
            ((ByteBuffer) this.value).rewind();
            if (iLimit >= 0) {
                ((ByteBuffer) this.value).limit(iLimit);
            }
            if (iPosition >= 0 && iPosition <= iLimit) {
                ((ByteBuffer) this.value).position(iPosition);
            }
            return sb.substring(0, sb.length());
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter$PacketDumpParameter.class */
    protected static final class PacketDumpParameter extends Parameter<ByteBuffer> {
        long offset;
        long packetLength;

        PacketDumpParameter(ByteBuffer buffer, long offset, long packetLength) {
            super(buffer);
            this.offset = offset;
            this.packetLength = packetLength;
        }

        @Override // oracle.jdbc.diagnostics.Parameter
        protected final String basicFormat() {
            StringBuilder sb = new StringBuilder(16384);
            StringBuilder sb2 = new StringBuilder(80);
            if (this.value == null) {
                return "NULL";
            }
            int iPosition = ((ByteBuffer) this.value).position();
            int iLimit = ((ByteBuffer) this.value).limit();
            ((ByteBuffer) this.value).position((int) this.offset);
            ((ByteBuffer) this.value).limit((int) this.packetLength);
            sb.delete(0, sb.length());
            sb2.delete(0, sb2.length());
            int i = 0;
            while (((ByteBuffer) this.value).hasRemaining()) {
                int i2 = ((ByteBuffer) this.value).get() & 255;
                sb.append(" " + Parameter.HEX[i2]);
                sb2.append(Parameter.ASCII[i2]);
                i++;
                if (i == 8) {
                    sb.append("     |");
                    sb.append(sb2.substring(0, sb2.length()));
                    sb.append("|\n");
                    sb2.delete(0, sb2.length());
                    i = 0;
                }
            }
            if (i > 0) {
                int i3 = (8 - i) - 1;
                for (int i4 = 0; i4 <= i3; i4++) {
                    sb.append("   ");
                }
                sb.append("     |");
                sb.append(sb2.substring(0, sb2.length()));
                for (int i5 = 0; i5 <= i3; i5++) {
                    sb.append(" ");
                }
                sb.append("|\n");
                sb2.delete(0, sb2.length());
            }
            ((ByteBuffer) this.value).rewind();
            if (iLimit >= 0) {
                ((ByteBuffer) this.value).limit(iLimit);
            }
            if (iPosition >= 0 && iPosition <= iLimit) {
                ((ByteBuffer) this.value).position(iPosition);
            }
            return sb.substring(0, sb.length());
        }
    }

    protected static String repeat(String s, int count) {
        if (count == 0) {
            return "";
        }
        if (count == 1) {
            return s;
        }
        StringBuilder b = new StringBuilder(s.length() * count);
        while (count > 0) {
            b.append(s);
            count--;
        }
        return b.toString();
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/Parameter$PropertiesParameter.class */
    protected static final class PropertiesParameter extends Parameter<Properties> {
        protected PropertiesParameter(Object v) {
            super(v);
        }

        @Override // oracle.jdbc.diagnostics.Parameter
        protected String basicFormat() {
            StringBuilder sb = new StringBuilder();
            ((Properties) this.value).forEach((k, v) -> {
                sb.append("[" + k + "=" + v + "]\n");
            });
            return sb.toString();
        }
    }

    public static void main(String[] args) throws Throwable {
        byte[] a = new byte[1024];
        new Random().nextBytes(a);
        ByteBuffer b = ByteBuffer.wrap(a);
        System.out.println(arg(Format.Style.STRING, "How now\nbrown cow", new long[0]));
        System.out.println(arg(Format.Style.STRING, "The quick brown fox jumped over the lazy dog", 19));
        System.out.println(arg(Format.Style.BYTE_ARRAY, a, new long[0]));
        System.out.println(arg(Format.Style.BYTE_ARRAY, a, 10, 32, 30));
        System.out.println(arg(Format.Style.BYTE_BUFFER, b, 100, 32, 100, 4917));
    }
}
