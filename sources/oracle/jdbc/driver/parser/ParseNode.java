package oracle.jdbc.driver.parser;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import oracle.jdbc.driver.parser.util.Array;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/ParseNode.class */
public class ParseNode implements Comparable<ParseNode> {
    public int from;
    public int to;
    public ParseNode parent;
    ParseNode lft;
    ParseNode rgt;
    int[] symbols;
    public Set<ParseNode> topLevel;
    public Parser parser;
    public static String ignoreMarkers = null;

    public boolean contains(int symbol) {
        return this.symbols[Array.indexOf(this.symbols, symbol)] == symbol;
    }

    public List<ParseNode> descendants() {
        List<ParseNode> ret = new ArrayList<>();
        ret.add(this);
        for (ParseNode n : children()) {
            ret.addAll(n.descendants());
        }
        return ret;
    }

    @Override // java.lang.Comparable
    public int compareTo(ParseNode obj) {
        return this.from != obj.from ? this.from - obj.from : this.to - obj.to;
    }

    public ParseNode next() {
        if (this.lft != null) {
            if (this.lft.isAuxiliary()) {
                return this.lft.next();
            }
            return this.lft;
        }
        ParseNode parseNode = this.parent;
        while (true) {
            ParseNode prt = parseNode;
            if (prt != null) {
                ParseNode nextSibling = prt.rgt;
                if (nextSibling == null && prt.topLevel != null) {
                    for (ParseNode p : prt.topLevel) {
                        if (this.to <= p.from) {
                            if (p.isAuxiliary()) {
                                return p.next();
                            }
                            return p;
                        }
                    }
                }
                if (nextSibling != null && this.to == nextSibling.from) {
                    if (nextSibling.isAuxiliary()) {
                        return nextSibling.next();
                    }
                    return nextSibling;
                }
                parseNode = prt.parent;
            } else {
                if (this.topLevel != null) {
                    Iterator<ParseNode> it = this.topLevel.iterator();
                    if (it.hasNext()) {
                        ParseNode child = it.next();
                        return child;
                    }
                    return null;
                }
                return null;
            }
        }
    }

    public int[] content() {
        return this.symbols;
    }

    public void addContent(int symbol) {
        this.symbols = Array.insert(this.symbols, symbol);
    }

    public void deleteContent(int symbol) {
        this.symbols = Array.delete(this.symbols, symbol);
    }

    public void addTopLevel(ParseNode child) {
        if (this.topLevel == null) {
            this.topLevel = new TreeSet();
        }
        this.topLevel.add(child);
        child.parent = this;
    }

    public ParseNode coveredByOnTopLevel(int pos) {
        if (this.topLevel == null) {
            return null;
        }
        for (ParseNode node : this.topLevel) {
            if (node.from <= pos && pos < node.to) {
                return node;
            }
        }
        return null;
    }

    public ParseNode(int begin, int end, int symbol, int dummy, Parser p) {
        this(begin, end, symbol, p);
    }

    public ParseNode(int begin, int end, int symbol, Parser p) {
        this.lft = null;
        this.rgt = null;
        this.symbols = new int[0];
        this.topLevel = null;
        this.from = begin;
        this.to = end;
        addContent(symbol);
        this.parser = p;
    }

    public String toString() {
        return toString(0, false);
    }

    protected String toString(int depth, boolean isCut) {
        if (ignoreMarkers != null) {
            return toString(depth, ignoreMarkers, isCut);
        }
        return toString(depth, "", isCut);
    }

    protected String toString(int depth, String auxMarker, boolean isCut) {
        String symbol;
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < depth; i++) {
            sb.append("  ");
        }
        if (isCut) {
            sb.append("...");
            return sb.toString();
        }
        sb.append(interval() + " ");
        for (int i2 : content()) {
            Integer i3 = Integer.valueOf(i2);
            if (i3.intValue() != -1) {
                if (this.parser == null || this.parser.allSymbols == null || i3.intValue() < 0 || i3.intValue() > this.parser.allSymbols.length) {
                    symbol = "!" + i3;
                } else {
                    symbol = this.parser.allSymbols[i3.intValue()];
                }
                if (0 >= auxMarker.length() || 0 > symbol.indexOf(auxMarker)) {
                    sb.append("  " + symbol);
                }
            }
        }
        return sb.toString();
    }

    public String interval() {
        return "[" + this.from + "," + this.to + ")";
    }

    public boolean isAuxiliary() {
        if (this.symbols[0] == -1) {
            return true;
        }
        if (this.parser.isAuxNode(this.symbols[0]) && this.symbols.length == 1) {
            return true;
        }
        return false;
    }

    public SortedSet<ParseNode> children() {
        TreeSet<ParseNode> ret = new TreeSet<>();
        if (this.topLevel != null) {
            for (ParseNode child : this.topLevel) {
                if (child.isAuxiliary()) {
                    ret.addAll(child.children());
                } else {
                    ret.add(child);
                }
            }
            return ret;
        }
        if (this.lft == null) {
            return ret;
        }
        if (this.lft.isAuxiliary()) {
            ret.addAll(this.lft.children());
        } else {
            ret.add(this.lft);
        }
        if (this.rgt == null) {
            return ret;
        }
        if (this.rgt.isAuxiliary()) {
            ret.addAll(this.rgt.children());
        } else {
            ret.add(this.rgt);
        }
        return ret;
    }
}
