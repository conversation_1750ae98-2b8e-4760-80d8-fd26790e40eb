package oracle.jdbc.driver.json.tree;

import java.time.Period;
import java.util.Arrays;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.binary.OsonPrimitiveConversions;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.INTERVALYM;
import oracle.sql.json.OracleJsonIntervalYM;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonIntervalYMImpl.class */
public class OracleJsonIntervalYMImpl implements OracleJsonIntervalYM {
    public static final int INTERVALYM_LEN = 5;
    byte[] raw;

    public OracleJsonIntervalYMImpl(byte[] raw) {
        this.raw = raw;
    }

    public OracleJsonIntervalYMImpl(Period p) {
        this.raw = OsonPrimitiveConversions.periodToIntervalYM(OracleJsonExceptions.ORACLE_FACTORY, p);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.INTERVALYM;
    }

    @Override // oracle.sql.json.OracleJsonIntervalYM
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonIntervalYM)) {
            return false;
        }
        if (!(other instanceof OracleJsonIntervalYMImpl)) {
            throw new UnsupportedOperationException();
        }
        OracleJsonIntervalYMImpl otheri = (OracleJsonIntervalYMImpl) other;
        return Arrays.equals(otheri.raw, this.raw);
    }

    @Override // oracle.sql.json.OracleJsonIntervalYM
    public int hashCode() {
        return Arrays.hashCode(this.raw);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        return JsonSerializerImpl.serializeString(getString());
    }

    @Override // oracle.sql.json.OracleJsonIntervalYM
    public String getString() {
        return OsonPrimitiveConversions.serializeIntervalYM(OracleJsonExceptions.ORACLE_FACTORY, raw());
    }

    public byte[] raw() {
        return this.raw;
    }

    @Override // oracle.sql.json.OracleJsonIntervalYM
    public Period getPeriod() {
        return OsonPrimitiveConversions.intervalYMToPeriod(raw());
    }

    public static String serializePeriod(Period p, OracleJsonExceptions.ExceptionFactory f) {
        byte[] raw = OsonPrimitiveConversions.periodToIntervalYM(f, p);
        return OsonPrimitiveConversions.serializeIntervalYM(f, raw);
    }

    @Override // oracle.sql.json.OracleJsonIntervalYM
    public INTERVALYM getINTERVALYM() {
        return new INTERVALYM(raw());
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaIntervalYMImpl(this.raw));
        }
        return c.cast(new JsonpPrimitive.JsonpIntervalYMImpl(this.raw));
    }
}
