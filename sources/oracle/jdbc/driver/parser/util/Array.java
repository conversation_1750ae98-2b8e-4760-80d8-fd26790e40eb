package oracle.jdbc.driver.parser.util;

import java.util.Arrays;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/util/Array.class */
public class Array {
    public static int indexOf(int[] array, int value) {
        return indexOf(array, 0, array.length - 1, value);
    }

    private static int indexOf(int[] array, int xx, int yy, int value) {
        if (xx + 1 == yy || xx == yy) {
            return array[xx] < value ? yy : xx;
        }
        int mid = (xx + yy) / 2;
        if (value < array[mid]) {
            return indexOf(array, xx, mid, value);
        }
        return indexOf(array, mid, yy, value);
    }

    public static int indexOf(long[] array, int xx, int yy, long value) {
        if (xx + 1 == yy || xx == yy) {
            return array[xx] < value ? yy : xx;
        }
        int mid = (xx + yy) / 2;
        if (value < array[mid]) {
            return indexOf(array, xx, mid, value);
        }
        return indexOf(array, mid, yy, value);
    }

    public static int[] insert(int[] array, int value) {
        if (array == null || array.length == 0) {
            return new int[]{value};
        }
        int index = indexOf(array, 0, array.length, value);
        if (index < array.length && array[index] == value) {
            return array;
        }
        int[] ret = new int[array.length + 1];
        for (int i = 0; i < index; i++) {
            ret[i] = array[i];
        }
        ret[index] = value;
        for (int i2 = index + 1; i2 < ret.length; i2++) {
            ret[i2] = array[i2 - 1];
        }
        return ret;
    }

    public static long[] insert(long[] array, long value) {
        if (array == null || array.length == 0) {
            return new long[]{value};
        }
        int index = indexOf(array, 0, array.length, value);
        if (index < array.length && array[index] == value) {
            return array;
        }
        long[] ret = new long[array.length + 1];
        for (int i = 0; i < index; i++) {
            ret[i] = array[i];
        }
        ret[index] = value;
        for (int i2 = index + 1; i2 < ret.length; i2++) {
            ret[i2] = array[i2 - 1];
        }
        return ret;
    }

    public static int[] delete(int[] array, int value) {
        int index = indexOf(array, 0, array.length, value);
        if (index == array.length || array[index] != value) {
            return array;
        }
        int[] ret = new int[array.length - 1];
        for (int i = 0; i < index; i++) {
            ret[i] = array[i];
        }
        for (int i2 = index; i2 < ret.length; i2++) {
            ret[i2] = array[i2 + 1];
        }
        return ret;
    }

    public static int[] delete(int[] array, int minVal, int maxVal) {
        if (minVal + 1 == maxVal) {
            return delete(array, minVal);
        }
        int iMin = indexOf(array, 0, array.length, minVal);
        if (array.length <= iMin) {
            return array;
        }
        if (array[iMin] < minVal) {
            iMin++;
        }
        int iMax = indexOf(array, 0, array.length, maxVal);
        if (array[iMax] > maxVal) {
            iMax--;
        }
        if (iMin == iMax) {
            return array;
        }
        int[] ret = new int[array.length - (iMax - iMin)];
        for (int i = 0; i < iMin; i++) {
            ret[i] = array[i];
        }
        for (int i2 = iMax; i2 < array.length; i2++) {
            ret[i2 - (iMax - iMin)] = array[i2];
        }
        return ret;
    }

    public static int[] merge(int[] x, int[] y) {
        if (x == null) {
            return y;
        }
        if (y == null) {
            return x;
        }
        int m = x.length;
        int n = y.length;
        int[] tmp = new int[m + n];
        int i = 0;
        int j = 0;
        int k = 0;
        while (i < m && j < n) {
            if (x[i] == y[j]) {
                int i2 = k;
                k++;
                int i3 = i;
                i++;
                tmp[i2] = x[i3];
                j++;
            } else if (x[i] < y[j]) {
                int i4 = k;
                k++;
                int i5 = i;
                i++;
                tmp[i4] = x[i5];
            } else {
                int i6 = k;
                k++;
                int i7 = j;
                j++;
                tmp[i6] = y[i7];
            }
        }
        if (i < m) {
            for (int p = i; p < m; p++) {
                int i8 = k;
                k++;
                tmp[i8] = x[p];
            }
        } else {
            for (int p2 = j; p2 < n; p2++) {
                int i9 = k;
                k++;
                tmp[i9] = y[p2];
            }
        }
        int[] ret = new int[k];
        for (int ii = 0; ii < k; ii++) {
            ret[ii] = tmp[ii];
        }
        return ret;
    }

    public static long[] merge(long[] x, long[] y) {
        if (x == null) {
            return y;
        }
        if (y == null) {
            return x;
        }
        int m = x.length;
        int n = y.length;
        long[] tmp = new long[m + n];
        int i = 0;
        int j = 0;
        int k = 0;
        while (i < m && j < n) {
            if (x[i] == y[j]) {
                int i2 = k;
                k++;
                int i3 = i;
                i++;
                tmp[i2] = x[i3];
                j++;
            } else if (x[i] < y[j]) {
                int i4 = k;
                k++;
                int i5 = i;
                i++;
                tmp[i4] = x[i5];
            } else {
                int i6 = k;
                k++;
                int i7 = j;
                j++;
                tmp[i6] = y[i7];
            }
        }
        if (i < m) {
            for (int p = i; p < m; p++) {
                int i8 = k;
                k++;
                tmp[i8] = x[p];
            }
        } else {
            for (int p2 = j; p2 < n; p2++) {
                int i9 = k;
                k++;
                tmp[i9] = y[p2];
            }
        }
        return Arrays.copyOf(tmp, k);
    }
}
