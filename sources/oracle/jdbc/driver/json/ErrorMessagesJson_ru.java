package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_ru.class */
public class ErrorMessagesJson_ru extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Возникло исключение ввода/вывода"}, new Object[]{"26302", "Год \"{0}\" не поддерживается"}, new Object[]{"26303", "Переполнение, слишком большое значение: {0}."}, new Object[]{"26304", "Неподдерживаемый параметр (не реализован)."}, new Object[]{"26305", "Двоичный файл JSON недействителен или поврежден."}, new Object[]{"26306", "Неподдерживаемая версия двоичного файла JSON: {0}."}, new Object[]{"26307", "Длина ключа в кодировке UTF-8 не должна быть больше 256 символов. Это ограничение превышено в следующем ключе: \"{0}\"."}, new Object[]{"26308", "Указанный файл JSON имеет слишком большой размер для кодировки в качестве двоичного файла JSON. Размер кодированных изображений не должен превышать 2 ГБ."}, new Object[]{"26309", "Двоичный файл JSON недействителен или поврежден. Указанное изображение содержит всего {0} байт."}, new Object[]{"26310", "В указанном java.time.Period заданы дни, но интервал Oracle \"год – месяц\" не поддерживает дни."}, new Object[]{"26311", "Генератор закрыт до окончания."}, new Object[]{"26312", "В этом контексте необходимо указать ключ объекта."}, new Object[]{"26313", "Недопустимая запись. Полное значение уже записано."}, new Object[]{"26314", "Окончание в этом контексте не допускается."}, new Object[]{"26315", "Ключ в этом контексте не допускается."}, new Object[]{"26316", "Ожидаемое значение после ключа."}, new Object[]{"26317", "Анализатор должен находиться в состоянии {0}."}, new Object[]{"26318", "Анализатор не должен находиться в состоянии {0}."}, new Object[]{"26319", "Анализатор должен иметь значение."}, new Object[]{"26320", "тип обертки \"{0}\" не поддерживается."}, new Object[]{"26321", "Этот объект не может быть изменен. Для создания изменяемой копии используйте код OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Этот массив не может быть изменен. Для создания изменяемой копии используйте код OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "Объект JSON содержит повторяющийся ключ: {0}."}, new Object[]{"26324", "Невозможно автоматически определить кодировку, недостаточно символов."}, new Object[]{"26325", "Ожидался токен EOF, но получен {0}."}, new Object[]{"26326", "Непредвиденный символ {0} в строке {1}, столбец {2}."}, new Object[]{"26327", "Непредвиденный символ {0} в строке {1}, столбец {2}. Ожидалось: {3}."}, new Object[]{"26328", "Недопустимый символ {0} в строке {1}, столбец {2}. Ожидаемые токены: {3}."}, new Object[]{"26329", "JsonParser#getString() является допустимым только для состояний анализатора KEY_NAME, VALUE_STRING, VALUE_NUMBER. Но в текущий момент анализатор находится в состоянии {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() является допустимым только для состояния анализатора VALUE_NUMBER. Но в текущий момент анализатор находится в состоянии {0}."}, new Object[]{"26331", "JsonParser#getInt() является допустимым только для состояния анализатора VALUE_NUMBER. Но в текущий момент анализатор находится в состоянии {0}."}, new Object[]{"26332", "JsonParser#getLong() является допустимым только для состояния анализатора VALUE_NUMBER. Но в текущий момент анализатор находится в состоянии {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() является допустимым только для состояния анализатора VALUE_NUMBER. Но в текущий момент анализатор находится в состоянии {0}."}, new Object[]{"26334", "JsonParser#getArray() является допустимым только для состояния анализатора START_ARRAY. Но в текущий момент анализатор находится в состоянии {0}."}, new Object[]{"26335", "JsonParser#getObject() является допустимым только для состояния анализатора START_OBJECT. Но в текущий момент анализатор находится в состоянии {0}."}, new Object[]{"26336", "Метка времени с регионом не поддерживается. Поддерживаются только часовые пояса для смещения."}, new Object[]{"26337", "Максимальное число уровней вложения для объектов и массивов в значении JSON: {0}"}, new Object[]{"26338", "Размер ключей объекта JSON не должен превышать 65 535 байт"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
