package oracle.jdbc.driver;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.xa.OracleXAResource;
import oracle.net.ns.Communication;
import oracle.net.ns.SQLnetDef;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CMAREngine.class */
abstract class T4CMAREngine implements Diagnosable {
    private static final String CLASS_NAME;
    static final int TTCC_MXL = 252;
    static final int TTCC_ESC = 253;
    static final int TTCC_LNG = 254;
    static final int TTCC_ERR = 255;
    static final int TTCC_MXIN = 32767;
    static final int TTCC_MXIN_OLD = 64;
    static final byte TTCLXMULTI = 1;
    static final byte TTCLXMCONV = 2;
    T4CTypeRep types;
    Communication net;
    DBConversion conv;
    short proSvrVer;
    static final byte[] NO_BYTES;
    static final byte[] IGNORED;
    static final byte[] NULL_PTR;
    static final byte[] NOTNULL_PTR;
    static final /* synthetic */ boolean $assertionsDisabled;
    private int effectiveTTCC_MXIN = 64;
    boolean useCLRBigChunks = false;
    final byte[] tmpBuffer1 = new byte[1];
    final byte[] tmpBuffer2 = new byte[2];
    final byte[] tmpBuffer3 = new byte[3];
    final byte[] tmpBuffer4 = new byte[4];
    final byte[] tmpBuffer5 = new byte[5];
    final byte[] tmpBuffer6 = new byte[6];
    final byte[] tmpBuffer7 = new byte[7];
    final byte[] tmpBuffer8 = new byte[8];
    final byte[] tmpBuffer10 = new byte[10];
    final int[] retLen = new int[1];
    AtomicReference<oracle.jdbc.internal.OracleConnection> connForException = new AtomicReference<>();
    ArrayList<byte[]> refVector = null;
    private ArrayList<byte[]> clrList = null;

    abstract void marshalSB1(byte b) throws IOException;

    abstract void marshalUB1(short s) throws IOException;

    abstract void marshalSB2(short s) throws IOException;

    abstract void marshalUB2(int i) throws IOException;

    abstract void marshalNativeUB2(short s, boolean z) throws IOException;

    abstract void marshalSB4(int i) throws IOException;

    abstract void marshalUB4(long j) throws IOException;

    abstract void marshalUB8(long j) throws IOException;

    abstract void marshalSB8(long j) throws IOException;

    abstract void marshalB1Array(byte[] bArr) throws IOException;

    abstract void marshalB1Array(byte[] bArr, int i, int i2) throws IOException;

    abstract byte unmarshalSB1() throws SQLException, IOException;

    abstract short unmarshalUB1() throws SQLException, IOException;

    abstract short unmarshalSB2() throws SQLException, IOException;

    abstract int unmarshalUB2() throws SQLException, IOException;

    abstract int unmarshalSB4() throws SQLException, IOException;

    abstract long unmarshalUB4() throws SQLException, IOException;

    abstract int unmarshalSB4(byte[] bArr) throws SQLException, IOException;

    abstract long unmarshalSB8() throws SQLException, IOException;

    abstract byte[] unmarshalNBytes(int i) throws SQLException, IOException;

    abstract int unmarshalNBytes(byte[] bArr, int i, int i2) throws SQLException, IOException;

    abstract int getNBytes(byte[] bArr, int i, int i2) throws SQLException, IOException;

    abstract byte[] getNBytes(int i) throws SQLException, IOException;

    abstract void skipNBytes(int i) throws SQLException, IOException;

    abstract byte[] unmarshalTEXT(int i) throws SQLException, IOException;

    abstract long buffer2Value(byte b) throws SQLException, IOException;

    protected abstract void flush() throws IOException;

    abstract void setByteOrder(byte b) throws IOException;

    abstract boolean sentCancel();

    abstract void writeZeroCopyIO(byte[] bArr, int i, int i2) throws IOException;

    abstract void writeZeroCopyIOHeader(boolean z, int i, boolean z2) throws IOException;

    abstract void writeZeroCopyIOData(byte[] bArr, int i, int i2) throws IOException;

    abstract void clearWriteBuffer();

    T4CMAREngine() {
    }

    static {
        $assertionsDisabled = !T4CMAREngine.class.desiredAssertionStatus();
        CLASS_NAME = T4CMAREngine.class.getName();
        NO_BYTES = new byte[0];
        IGNORED = new byte[32767];
        NULL_PTR = new byte[]{0, 0, 0, 0};
        NOTNULL_PTR = new byte[]{Byte.MAX_VALUE, Byte.MAX_VALUE, Byte.MAX_VALUE, Byte.MAX_VALUE};
    }

    static String toHex(long value, int bytes) {
        String result;
        switch (bytes) {
            case 1:
                result = "00" + Long.toString(value & 255, 16);
                break;
            case 2:
                result = oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_RESOURCE_MANAGER_ID_DEFAULT + Long.toString(value & 65535, 16);
                break;
            case 3:
                result = "000000" + Long.toString(value & 16777215, 16);
                break;
            case 4:
                result = "00000000" + Long.toString(value & SQLnetDef.NSPDDLSLMAX, 16);
                break;
            case 5:
                result = "0000000000" + Long.toString(value & 1099511627775L, 16);
                break;
            case 6:
                result = "000000000000" + Long.toString(value & 281474976710655L, 16);
                break;
            case 7:
                result = "00000000000000" + Long.toString(value & 72057594037927935L, 16);
                break;
            case 8:
                return toHex(value >> 32, 4) + toHex(value, 4).substring(2);
            default:
                return "more than 8 bytes";
        }
        return "0x" + result.substring(result.length() - (2 * bytes));
    }

    static String toHex(byte value) {
        String result = "00" + Integer.toHexString(value & 255);
        return "0x" + result.substring(result.length() - 2);
    }

    static String toHex(short value) {
        return toHex(value, 2);
    }

    static String toHex(int value) {
        return toHex(value, 4);
    }

    static String toHex(byte[] value, int length) {
        if (value == null) {
            return "null";
        }
        if (length > value.length) {
            return "byte array not long enough";
        }
        String result = "[";
        int len = Math.min(64, length);
        for (int i = 0; i < len; i++) {
            result = result + toHex(value[i]) + " ";
        }
        if (len < length) {
            result = result + "...";
        }
        return result + "]";
    }

    static String toHex(byte[] value) {
        if (value == null) {
            return "null";
        }
        return toHex(value, value.length);
    }

    final void marshalSWORD(int value) throws IOException {
        marshalSB4(value);
    }

    final void marshalUWORD(long value) throws IOException {
        marshalSB4((int) (value & (-1)));
    }

    final void marshalUB4Array(long[] value) throws IOException {
        for (long j : value) {
            marshalSB4((int) (j & (-1)));
        }
    }

    final void marshalO2U(boolean notnull) throws IOException {
        if (notnull) {
            addPtr((byte) 1);
        } else {
            addPtr((byte) 0);
        }
    }

    final void marshalNULLPTR() throws IOException {
        addPtr((byte) 0);
    }

    final void marshalPTR() throws IOException {
        addPtr((byte) 1);
    }

    final void marshalCHR(byte[] value) throws IOException {
        marshalCHR(value, 0, value.length);
    }

    final void marshalCHR(byte[] value, int offset, int length) throws IOException {
        if (length > 0) {
            if (this.types.isConvNeeded()) {
                marshalCLR(value, offset, length);
            } else {
                marshalB1Array(value, offset, length);
            }
        }
    }

    final void marshalCLR(byte[] value, int valueLen) throws IOException {
        marshalCLR(value, 0, valueLen);
    }

    final void marshalCLR(byte[] value, int offset, int valueLen) throws IOException {
        if (valueLen > 252) {
            int nbBytesWritten = 0;
            marshalUB1((short) -2);
            do {
                int bytesLeft = valueLen - nbBytesWritten;
                int len = bytesLeft > this.effectiveTTCC_MXIN ? this.effectiveTTCC_MXIN : bytesLeft;
                if (this.useCLRBigChunks) {
                    marshalSB4(len);
                } else {
                    marshalUB1((byte) (len & 255));
                }
                marshalB1Array(value, offset + nbBytesWritten, len);
                nbBytesWritten += len;
            } while (nbBytesWritten < valueLen);
            marshalUB1((short) 0);
            return;
        }
        marshalUB1((byte) (valueLen & 255));
        if (value.length != 0) {
            marshalB1Array(value, offset, valueLen);
        }
    }

    final void marshalCLR(DynamicByteArray value, long offset, int valueLen) throws IOException {
        if (valueLen > 252) {
            int nbBytesWritten = 0;
            marshalUB1((short) -2);
            do {
                int bytesLeft = valueLen - nbBytesWritten;
                int chunkLen = bytesLeft > this.effectiveTTCC_MXIN ? this.effectiveTTCC_MXIN : bytesLeft;
                if (this.useCLRBigChunks) {
                    marshalSB4(chunkLen);
                } else {
                    marshalUB1((byte) (chunkLen & 255));
                }
                value.marshalB1Array(this, offset + nbBytesWritten, chunkLen);
                nbBytesWritten += chunkLen;
            } while (nbBytesWritten < valueLen);
            marshalUB1((short) 0);
            return;
        }
        marshalUB1((byte) (valueLen & 255));
        if (value.length != 0) {
            value.marshalB1Array(this, offset, valueLen);
        }
    }

    final void marshalCLR(InputStream currentStream, int offset) throws IOException {
        int bufferLength = this.effectiveTTCC_MXIN;
        byte[] buffer = new byte[bufferLength];
        boolean endOfStream = false;
        marshalUB1((short) 254);
        while (!endOfStream) {
            try {
                if (sentCancel()) {
                    break;
                }
                int bytesRead = currentStream.read(buffer, 0, bufferLength);
                if (bytesRead == -1) {
                    endOfStream = true;
                }
                if (bytesRead > 0) {
                    if (this.useCLRBigChunks) {
                        marshalSB4(bytesRead);
                    } else {
                        marshalUB1((byte) (bytesRead & 255));
                    }
                    marshalB1Array(buffer, 0, bytesRead);
                }
            } finally {
                marshalUB1((short) 0);
            }
        }
    }

    final void marshalKEYVAL(byte[][] keys, int[] keysSize, byte[][] values, int[] valuesSize, byte[] kvalflg, int nb) throws IOException {
        for (int i = 0; i < nb; i++) {
            if (keys[i] != null && keysSize[i] > 0) {
                marshalUB4(keysSize[i]);
                marshalCLR(keys[i], 0, keysSize[i]);
            } else {
                marshalUB4(0L);
            }
            if (values[i] != null && valuesSize[i] > 0) {
                marshalUB4(valuesSize[i]);
                marshalCLR(values[i], 0, valuesSize[i]);
            } else {
                marshalUB4(0L);
            }
            if (kvalflg[i] != 0) {
                marshalUB4(1L);
            } else {
                marshalUB4(0L);
            }
        }
    }

    final void marshalKEYVAL(byte[][] keys, byte[][] values, byte[] kvalflg, int nb) throws IOException {
        int[] keysSize = new int[nb];
        int[] valuesSize = new int[nb];
        for (int i = 0; i < nb; i++) {
            if (keys[i] != null) {
                keysSize[i] = keys[i].length;
            }
            if (values[i] != null) {
                valuesSize[i] = values[i].length;
            }
        }
        marshalKEYVAL(keys, keysSize, values, valuesSize, kvalflg, nb);
    }

    final void marshalDALC(byte[] buffer) throws IOException {
        if (buffer == null || buffer.length < 1) {
            marshalUB4(0L);
        } else {
            marshalUB4(buffer.length);
            marshalCLR(buffer, buffer.length);
        }
    }

    final void marshalKPDKV(byte[][] textValues, byte[][] binaryValues, int[] keywords) throws IOException {
        for (int i = 0; i < textValues.length; i++) {
            if (textValues[i] != null) {
                marshalUB4(textValues[i].length);
                marshalCLR(textValues[i], 0, textValues[i].length);
            } else {
                marshalUB4(0L);
            }
            if (binaryValues[i] != null) {
                marshalUB4(binaryValues[i].length);
                marshalCLR(binaryValues[i], 0, binaryValues[i].length);
            } else {
                marshalUB4(0L);
            }
            marshalUB2(keywords[i]);
        }
    }

    final void unmarshalKPDKV(byte[][] textValues, int[] textValuesLength, byte[][] binaryValues, int[] keywords) throws SQLException, IOException {
        int[] tempAr = new int[1];
        for (int i = 0; i < textValues.length; i++) {
            int tempLength = (int) unmarshalUB4();
            if (tempLength > 0) {
                textValues[i] = new byte[tempLength];
                unmarshalCLR(textValues[i], 0, tempAr, tempLength);
                textValuesLength[i] = tempAr[0];
            }
            int tempLength2 = (int) unmarshalUB4();
            if (tempLength2 > 0) {
                binaryValues[i] = new byte[tempLength2];
                unmarshalCLR(binaryValues[i], 0, tempAr, tempLength2);
            }
            keywords[i] = unmarshalUB2();
        }
    }

    final void addPtr(byte value) throws IOException {
        if (this.types.rep[4] == 1) {
            marshalUB1(value);
        } else if (value == 0) {
            marshalB1Array(NULL_PTR);
        } else {
            marshalB1Array(NOTNULL_PTR);
        }
    }

    int unmarshalNativeUB2(boolean isLsb) throws SQLException, IOException {
        int value;
        unmarshalNBytes(this.tmpBuffer2, 0, 2);
        if (isLsb) {
            value = ((this.tmpBuffer2[1] << 8) & OracleXAResource.ORAISOLATIONMASK) | (this.tmpBuffer2[0] & 255);
        } else {
            value = ((this.tmpBuffer2[0] << 8) & OracleXAResource.ORAISOLATIONMASK) | (this.tmpBuffer2[1] & 255);
        }
        return value;
    }

    final int unmarshalUCS2(byte[] ucs2Char, long offset) throws SQLException, IOException {
        int value = unmarshalUB2();
        this.tmpBuffer2[0] = (byte) ((value & OracleXAResource.ORAISOLATIONMASK) >> 8);
        this.tmpBuffer2[1] = (byte) (value & 255);
        if (offset + 1 < ucs2Char.length) {
            ucs2Char[(int) offset] = this.tmpBuffer2[0];
            ucs2Char[((int) offset) + 1] = this.tmpBuffer2[1];
        } else {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalUCS2", "ucs2Char buffer too small", null, null);
        }
        if (this.tmpBuffer2[0] == 0) {
            return this.tmpBuffer2[1] == 0 ? 1 : 2;
        }
        return 3;
    }

    final int unmarshalRefCursor(byte[] buffer) throws SQLException, IOException {
        int result = unmarshalSB4(buffer);
        return result;
    }

    int unmarshalSWORD() throws SQLException, IOException {
        int result = (int) unmarshalUB4();
        return result;
    }

    long unmarshalUWORD() throws SQLException, IOException {
        long result = unmarshalUB4();
        return result;
    }

    byte[] unmarshalCHR(int retLength) throws SQLException, IOException {
        byte[] resBuffer;
        if (this.types.isConvNeeded()) {
            resBuffer = unmarshalCLR(retLength, this.retLen);
            if (resBuffer.length != this.retLen[0]) {
                byte[] tmpBuf = new byte[this.retLen[0]];
                System.arraycopy(resBuffer, 0, tmpBuf, 0, this.retLen[0]);
                resBuffer = tmpBuf;
            }
        } else {
            resBuffer = getNBytes(retLength);
        }
        return resBuffer;
    }

    void unmarshalCLR(byte[] bytes, int offsetRow, int[] intArray) throws SQLException, IOException {
        unmarshalCLR(bytes, offsetRow, intArray, Integer.MAX_VALUE);
    }

    void unmarshalCLR(byte[] bytes, int offsetRow, int[] intArray, int maxSize) throws SQLException, IOException {
        unmarshalCLR(bytes, offsetRow, intArray, maxSize, 0);
    }

    void unmarshalCLR(byte[] bytes, int offsetRow, int[] intArray, int maxSize, int ignoreNBytes) throws SQLException, IOException {
        int offset = offsetRow;
        int nbBytesWritten = 0;
        int nbOfBytesIgnored = 0;
        int len = unmarshalUB1();
        if (len < 0) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalCLR", "length less than 0", null, null);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
        }
        if (len == 0) {
            intArray[0] = 0;
            return;
        }
        if (escapeSequenceNull(len)) {
            intArray[0] = 0;
            return;
        }
        if (len != 254) {
            if (ignoreNBytes - 0 >= len) {
                unmarshalBuffer(IGNORED, 0, len);
                int i = 0 + len;
                len = 0;
            } else if (ignoreNBytes - 0 > 0) {
                unmarshalBuffer(IGNORED, 0, ignoreNBytes - 0);
                len -= ignoreNBytes - 0;
                int i2 = 0 + (ignoreNBytes - 0);
            }
            if (len > 0) {
                int keepThem = Math.min(maxSize - 0, len);
                offset = unmarshalBuffer(bytes, offset, keepThem);
                nbBytesWritten = 0 + keepThem;
                int rest = len - keepThem;
                if (rest > 0) {
                    unmarshalBuffer(IGNORED, 0, rest);
                }
            }
        } else {
            while (true) {
                int len2 = this.useCLRBigChunks ? unmarshalSB4() : unmarshalUB1();
                if (len2 <= 0) {
                    break;
                }
                if (offset == -1) {
                    unmarshalBuffer(IGNORED, 0, len2);
                } else {
                    int locallen = len2;
                    if (ignoreNBytes - nbOfBytesIgnored >= locallen) {
                        unmarshalBuffer(IGNORED, 0, locallen);
                        nbOfBytesIgnored += locallen;
                        locallen = 0;
                    } else if (ignoreNBytes - nbOfBytesIgnored > 0) {
                        unmarshalBuffer(IGNORED, 0, ignoreNBytes - nbOfBytesIgnored);
                        locallen -= ignoreNBytes - nbOfBytesIgnored;
                        nbOfBytesIgnored += ignoreNBytes - nbOfBytesIgnored;
                    }
                    if (locallen > 0) {
                        int keepThem2 = Math.min(maxSize - nbBytesWritten, locallen);
                        offset = unmarshalBuffer(bytes, offset, keepThem2);
                        nbBytesWritten += keepThem2;
                        int rest2 = locallen - keepThem2;
                        if (rest2 > 0) {
                            unmarshalBuffer(IGNORED, 0, rest2);
                        }
                    }
                }
            }
        }
        if (intArray != null) {
            if (offset != -1) {
                intArray[0] = nbBytesWritten;
            } else {
                intArray[0] = bytes.length - offsetRow;
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalCLR", "exceeded the tmpBuffer length", null, null);
            }
        }
    }

    final byte[] unmarshalCLR(int buflen, int[] intArray) throws SQLException, IOException {
        byte[] tmpBuf = new byte[buflen * this.conv.c2sNlsRatio];
        unmarshalCLR(tmpBuf, 0, intArray, buflen);
        return tmpBuf;
    }

    final int[] unmarshalKEYVAL(byte[][] keys, byte[][] values, int nb) throws SQLException, IOException {
        byte[] buff = new byte[1000];
        int[] length = new int[1];
        int[] kvalflg = new int[nb];
        for (int i = 0; i < nb; i++) {
            int len = unmarshalSB4();
            if (len > 0) {
                unmarshalCLR(buff, 0, length);
                keys[i] = new byte[length[0]];
                System.arraycopy(buff, 0, keys[i], 0, length[0]);
            }
            int len2 = unmarshalSB4();
            if (len2 > 0) {
                unmarshalCLR(buff, 0, length);
                values[i] = new byte[length[0]];
                System.arraycopy(buff, 0, values[i], 0, length[0]);
            }
            kvalflg[i] = unmarshalSB4();
        }
        return kvalflg;
    }

    final int unmarshalBuffer(byte[] _byteValue, int offset, int len) throws SQLException, IOException {
        int offset2;
        if (len <= 0) {
            return offset;
        }
        if (_byteValue.length < offset + len) {
            unmarshalNBytes(_byteValue, offset, _byteValue.length - offset);
            unmarshalNBytes(IGNORED, 0, (offset + len) - _byteValue.length);
            offset2 = -1;
        } else {
            unmarshalNBytes(_byteValue, offset, len);
            offset2 = offset + len;
        }
        return offset2;
    }

    final byte[] unmarshalCLRforREFS() throws SQLException, IOException {
        byte[] finalBuffer;
        int i = 0;
        int iUnmarshalUB1 = unmarshalUB1();
        if (iUnmarshalUB1 < 0) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalCLRforREFS", "bytes < 0", null, null);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
        }
        if (iUnmarshalUB1 == 0) {
            return null;
        }
        if (!escapeSequenceNull(iUnmarshalUB1)) {
            if (this.refVector == null) {
                this.refVector = new ArrayList<>(10);
            } else {
                this.refVector.clear();
            }
            if (iUnmarshalUB1 == 254) {
                while (true) {
                    int iUnmarshalSB4 = this.useCLRBigChunks ? unmarshalSB4() : unmarshalUB1();
                    int len = iUnmarshalSB4;
                    if (iUnmarshalSB4 <= 0) {
                        break;
                    }
                    if (len != 254 || this.useCLRBigChunks || !this.types.isServerConversion()) {
                        i = (short) (i + len);
                        byte[] tmpBuf = new byte[len];
                        unmarshalBuffer(tmpBuf, 0, len);
                        this.refVector.add(tmpBuf);
                    }
                }
            } else {
                i = iUnmarshalUB1;
                byte[] tmpBuf2 = new byte[iUnmarshalUB1];
                unmarshalBuffer(tmpBuf2, 0, iUnmarshalUB1);
                this.refVector.add(tmpBuf2);
            }
            finalBuffer = new byte[i];
            int start = 0;
            while (this.refVector.size() > 0) {
                int arrayLen = this.refVector.get(0).length;
                System.arraycopy(this.refVector.get(0), 0, finalBuffer, start, arrayLen);
                start += arrayLen;
                this.refVector.remove(0);
            }
        } else {
            finalBuffer = null;
        }
        return finalBuffer;
    }

    final byte[] unmarshalCLR() throws SQLException, IOException {
        byte[] finalBuffer;
        int i = 0;
        int iUnmarshalUB1 = unmarshalUB1();
        if (iUnmarshalUB1 < 0) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalCLR", "bytes < 0", null, null);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
        }
        if (iUnmarshalUB1 == 0) {
            return null;
        }
        if (!escapeSequenceNull(iUnmarshalUB1)) {
            if (this.clrList == null) {
                this.clrList = new ArrayList<>(10);
            } else {
                this.clrList.clear();
            }
            if (iUnmarshalUB1 == 254) {
                while (true) {
                    int iUnmarshalSB4 = this.useCLRBigChunks ? unmarshalSB4() : unmarshalUB1();
                    int len = iUnmarshalSB4;
                    if (iUnmarshalSB4 <= 0) {
                        break;
                    }
                    i = (short) (i + len);
                    byte[] tmpBuf = new byte[len];
                    unmarshalBuffer(tmpBuf, 0, len);
                    this.clrList.add(tmpBuf);
                }
            } else {
                i = iUnmarshalUB1;
                byte[] tmpBuf2 = new byte[iUnmarshalUB1];
                unmarshalBuffer(tmpBuf2, 0, iUnmarshalUB1);
                this.clrList.add(tmpBuf2);
            }
            finalBuffer = new byte[i];
            int start = 0;
            while (this.clrList.size() > 0) {
                int arrayLen = this.clrList.get(0).length;
                System.arraycopy(this.clrList.get(0), 0, finalBuffer, start, arrayLen);
                start += arrayLen;
                this.clrList.remove(0);
            }
        } else {
            finalBuffer = null;
        }
        return finalBuffer;
    }

    final void unmarshalCLRAndIgnore() throws SQLException, IOException {
        short bytes = unmarshalUB1();
        if (bytes < 0) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalCLRAndIgnore", "bytes < 0", null, null);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
        }
        if (bytes != 0 && !escapeSequenceNull(bytes)) {
            if (bytes != 254) {
                skipNBytes(bytes);
                return;
            }
            while (true) {
                int iUnmarshalSB4 = this.useCLRBigChunks ? unmarshalSB4() : unmarshalUB1();
                int len = iUnmarshalSB4;
                if (iUnmarshalSB4 > 0) {
                    skipNBytes(len);
                } else {
                    return;
                }
            }
        }
    }

    final boolean escapeSequenceNull(int bytes) throws SQLException {
        boolean is_null = false;
        switch (bytes) {
            case 0:
                is_null = true;
                break;
            case 253:
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "escapeSequenceNull", "received an ESC", null, null);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
            case 255:
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "escapeSequenceNull", "received an ERROR", null, null);
                is_null = true;
                break;
        }
        return is_null;
    }

    final int processIndicator(boolean isNull, int dataSize) throws SQLException, IOException {
        short ind = unmarshalSB2();
        int res = 0;
        if (!isNull) {
            if (ind == 0) {
                res = dataSize;
            } else if (ind == -2 || ind > 0) {
                res = ind;
            } else {
                res = 65536 + ind;
            }
        }
        return res;
    }

    final int unmarshalDALC(byte[] buffer, int offset) throws SQLException, IOException {
        int len = (int) unmarshalUB4();
        if (len > 0) {
            unmarshalCLR(buffer, offset, this.retLen);
        }
        return len;
    }

    final byte[] unmarshalDALC() throws SQLException, IOException {
        byte[] buffer;
        int len = (int) unmarshalUB4();
        if (len > 0) {
            buffer = unmarshalCLR(len, this.retLen);
            if (buffer == null) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalDALC", "buffer == null", null, null);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
            }
        } else {
            this.retLen[0] = 0;
            buffer = NO_BYTES;
        }
        return buffer;
    }

    final byte[] unmarshalDALC(int[] CLRRetLen) throws SQLException, IOException {
        byte[] buffer;
        int len = (int) unmarshalUB4();
        if (len > 0) {
            buffer = unmarshalCLR(len, CLRRetLen);
            if (buffer == null) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshalDALC", "buffer == null", null, null);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 401).fillInStackTrace());
            }
        } else {
            CLRRetLen[0] = 0;
            buffer = NO_BYTES;
        }
        return buffer;
    }

    final long buffer2Value(byte repOffset, ByteArrayInputStream in) throws SQLException, IOException {
        byte b;
        int bufLength = 0;
        long value = 0;
        boolean negative = false;
        if ((this.types.rep[repOffset] & 1) > 0) {
            bufLength = in.read();
            if ((bufLength & 128) > 0) {
                bufLength &= 127;
                negative = true;
            }
            if (bufLength < 0) {
                trace(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "buffer2Value", "bufLength < 0", null, null, new Object[0]);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0103).fillInStackTrace());
            }
            if (bufLength == 0) {
                return 0L;
            }
            if ((repOffset == 1 && bufLength > 2) || (repOffset == 2 && bufLength > 4)) {
                trace(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "buffer2Value", "incorrect number of bytes", null, null, new Object[0]);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0105).fillInStackTrace());
            }
        } else if (repOffset == 1) {
            bufLength = 2;
        } else if (repOffset == 2) {
            bufLength = 4;
        }
        byte[] tmpBuffer = new byte[bufLength];
        if (in.read(tmpBuffer) < 0) {
            trace(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "buffer2Value", "end of buffer", null, null, new Object[0]);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.TTC0103).fillInStackTrace());
        }
        for (int i = 0; i < tmpBuffer.length; i++) {
            if ((this.types.rep[repOffset] & 2) > 0) {
                b = tmpBuffer[(tmpBuffer.length - 1) - i];
            } else {
                b = tmpBuffer[i];
            }
            short tmpByte = (short) (b & 255);
            value |= tmpByte << (8 * ((tmpBuffer.length - 1) - i));
        }
        long value2 = value & (-1);
        if (negative) {
            value2 = -value2;
        }
        return value2;
    }

    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connForException.get();
    }

    protected void setConnectionDuringExceptionHandling(oracle.jdbc.internal.OracleConnection conn) {
        this.connForException.set(conn);
    }

    void writeZeroCopyIO(DynamicByteArray dba, long offset, int length) throws IOException {
        dba.writeZeroCopyIO(this, offset, length);
    }

    boolean readZeroCopyIO(byte[] userBuffer, int offset, int[] bytesRead) throws IOException {
        boolean isMarked = this.net.readZeroCopyIO(userBuffer, offset, bytesRead);
        return isMarked;
    }

    void setUseCLRBigChunks(boolean useCLRBigChunks) {
        this.useCLRBigChunks = useCLRBigChunks;
        if (useCLRBigChunks) {
            this.effectiveTTCC_MXIN = 32767;
        } else {
            if (!$assertionsDisabled) {
                throw new AssertionError();
            }
            this.effectiveTTCC_MXIN = 64;
        }
    }

    void beginPipelineRequest() throws IOException {
        throw new UnsupportedOperationException();
    }

    boolean endPipelineRequest() throws IOException {
        throw new UnsupportedOperationException();
    }

    void beginPipelineResponse() {
        throw new UnsupportedOperationException();
    }

    void endPipelineResponse() throws IOException {
        throw new UnsupportedOperationException();
    }
}
