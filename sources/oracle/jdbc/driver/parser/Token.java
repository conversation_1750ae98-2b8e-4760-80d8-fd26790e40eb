package oracle.jdbc.driver.parser;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/Token.class */
public enum Token {
    COMMENT,
    LINE_COMMENT,
    QUOTED_STRING,
    DQUOTED_STRING,
    B<PERSON><PERSON>OTED_STRING,
    WS,
    DIGITS,
    OPERATION,
    ID<PERSON><PERSON><PERSON><PERSON>,
    AUXILIARY,
    REGEXP,
    MACRO_SKIP,
    SQLPLUSLINECONTINUE_SKIP,
    DBTOOLS_COMMAND,
    SQLPLUS_COMMAND,
    INCOMPLETE
}
