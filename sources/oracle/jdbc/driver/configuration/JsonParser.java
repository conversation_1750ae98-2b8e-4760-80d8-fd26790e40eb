package oracle.jdbc.driver.configuration;

import java.io.InputStream;
import java.sql.SQLException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import oracle.jdbc.driver.OracleDriver;
import oracle.jdbc.spi.OracleConfigurationParser;
import oracle.jdbc.spi.OracleConfigurationSecretProvider;
import oracle.sql.json.OracleJsonFactory;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/configuration/JsonParser.class */
public class JsonParser implements OracleConfigurationParser {
    private static final OracleJsonFactory JSON_FACTORY = new OracleJsonFactory();
    private static final Map<String, OracleConfigurationSecretProvider> secretProviders = new HashMap();
    private static final String WALLET_LOCATION_JSON_OBJECT_NAME = "wallet_location";
    private static final String CONFIG_TTL_JSON_OBJECT_NAME = "config_time_to_live";

    @Override // oracle.jdbc.spi.OracleConfigurationParser
    public Properties parse(InputStream source, Map<String, String> options) throws SQLException {
        OracleConfigurationSecretProvider provider;
        OracleConfigurationSecretProvider provider2;
        OracleJsonObject json = JSON_FACTORY.createJsonTextValue(source).asJsonObject();
        String key = options.get("key");
        if (key != null) {
            if (!json.containsKey(key)) {
                throw new IllegalArgumentException(key + " key appears in URL but is missing in JSON.");
            }
            json = json.get(key).asJsonObject();
        }
        Properties properties = new Properties();
        if (json.containsKey("user")) {
            properties.setProperty("user", json.getString("user"));
        }
        if (!json.containsKey("connect_descriptor")) {
            throw new SQLException("'connect_descriptor' attribute missing in JSON.");
        }
        properties.setProperty("URL", "jdbc:oracle:thin:@" + json.getString("connect_descriptor"));
        if (json.containsKey(OracleDriver.jdbc_string)) {
            OracleJsonObject jdbc = json.get(OracleDriver.jdbc_string).asJsonObject();
            jdbc.forEach((k, v) -> {
                if (v.getOracleJsonType().equals(OracleJsonValue.OracleJsonType.STRING)) {
                    properties.setProperty(k, v.asJsonString().getString());
                } else {
                    properties.setProperty(k, v.toString());
                }
            });
        }
        if (json.containsKey("password")) {
            OracleJsonObject passwordObject = json.get("password").asJsonObject();
            Map<String, String> passwordMap = convertToMap(passwordObject);
            String secretType = passwordObject.getString("type");
            if (secretProviders.containsKey(secretType)) {
                provider2 = secretProviders.get(secretType);
            } else {
                provider2 = OracleConfigurationSecretProvider.find(secretType);
                secretProviders.put(secretType, provider2);
            }
            properties.setProperty("password", new String(Base64.getDecoder().decode(new String(provider2.getSecret(passwordMap)))));
        }
        if (json.containsKey(WALLET_LOCATION_JSON_OBJECT_NAME)) {
            OracleJsonObject walletObject = json.get(WALLET_LOCATION_JSON_OBJECT_NAME).asJsonObject();
            Map<String, String> walletMap = convertToMap(walletObject);
            String secretType2 = walletObject.getString("type");
            if (secretProviders.containsKey(secretType2)) {
                provider = secretProviders.get(secretType2);
            } else {
                provider = OracleConfigurationSecretProvider.find(secretType2);
                secretProviders.put(secretType2, provider);
            }
            properties.setProperty("oracle.net.wallet_location", "data:;base64," + new String(provider.getSecret(walletMap)));
        }
        if (json.containsKey(CONFIG_TTL_JSON_OBJECT_NAME)) {
            properties.setProperty(CONFIG_TTL_JSON_OBJECT_NAME, String.valueOf(json.getLong(CONFIG_TTL_JSON_OBJECT_NAME)));
        }
        return properties;
    }

    @Override // oracle.jdbc.spi.OracleConfigurationParser
    public String getType() {
        return "json";
    }

    private Map<String, String> convertToMap(OracleJsonObject jsonObject) {
        Map<String, String> map = new HashMap<>();
        jsonObject.keySet().forEach(key -> {
            OracleJsonValue jsonValue = jsonObject.get(key);
            if (key.equals("authentication")) {
                OracleJsonObject authObject = jsonValue.asJsonObject();
                authObject.keySet().forEach(k -> {
                    String v = authObject.getString(k);
                    if (k.equals("method")) {
                        map.put("AUTHENTICATION", v);
                    } else {
                        map.put(k, v);
                    }
                });
            } else {
                map.put(key, jsonValue.asJsonString().getString());
            }
        });
        return map;
    }
}
