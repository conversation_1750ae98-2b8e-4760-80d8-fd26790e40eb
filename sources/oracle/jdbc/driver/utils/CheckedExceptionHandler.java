package oracle.jdbc.driver.utils;

/* compiled from: CheckedExceptionUtils.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/CheckedExceptionHandler.class */
final class CheckedExceptionHandler {
    private CheckedExceptionHandler() {
    }

    static RuntimeException toRuntimeException(Throwable throwable) {
        if (throwable instanceof Error) {
            throw ((Error) throwable);
        }
        if (throwable instanceof RuntimeException) {
            return (RuntimeException) throwable;
        }
        return new RuntimeException(throwable);
    }
}
