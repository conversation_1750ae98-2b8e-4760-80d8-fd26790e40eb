package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.XSAttribute;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/XSAttributeI.class */
final class XSAttributeI extends XSAttribute {
    byte[] attributeNameBytes;
    byte[] attributeValueBytes;
    byte[] attributeDefaultValueBytes;
    String attributeName = null;
    String attributeValue = null;
    String attributeDefaultValue = null;
    long flag = 0;

    XSAttributeI() {
    }

    @Override // oracle.jdbc.internal.XSAttribute
    public void setAttributeName(String _attributeName) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.attributeName = _attributeName;
    }

    void doCharConversion(DBConversion conv) throws SQLException {
        if (this.attributeName != null) {
            this.attributeNameBytes = conv.StringToCharBytes(this.attributeName);
        } else {
            this.attributeNameBytes = null;
        }
        if (this.attributeValue != null) {
            this.attributeValueBytes = conv.StringToCharBytes(this.attributeValue);
        } else {
            this.attributeValueBytes = null;
        }
        if (this.attributeDefaultValue != null) {
            this.attributeDefaultValueBytes = conv.StringToCharBytes(this.attributeDefaultValue);
        } else {
            this.attributeDefaultValueBytes = null;
        }
    }

    @Override // oracle.jdbc.internal.XSAttribute
    public void setAttributeValue(String _attributeValue) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.attributeValue = _attributeValue;
    }

    @Override // oracle.jdbc.internal.XSAttribute
    public void setAttributeDefaultValue(String _attributeDefaultValue) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.attributeDefaultValue = _attributeDefaultValue;
    }

    @Override // oracle.jdbc.internal.XSAttribute
    public void setFlag(long _flag) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.flag = _flag;
    }

    @Override // oracle.jdbc.internal.XSAttribute
    public String getAttributeName() {
        InternalFactory.xsSecurityCheck();
        return this.attributeName;
    }

    @Override // oracle.jdbc.internal.XSAttribute
    public String getAttributeValue() {
        InternalFactory.xsSecurityCheck();
        return this.attributeValue;
    }

    @Override // oracle.jdbc.internal.XSAttribute
    public String getAttributeDefaultValue() {
        InternalFactory.xsSecurityCheck();
        return this.attributeDefaultValue;
    }

    @Override // oracle.jdbc.internal.XSAttribute
    public long getFlag() {
        InternalFactory.xsSecurityCheck();
        return this.flag;
    }

    void marshal(T4CMAREngine mar) throws IOException {
        if (this.attributeNameBytes != null) {
            mar.marshalUB4(this.attributeNameBytes.length);
            mar.marshalCLR(this.attributeNameBytes, this.attributeNameBytes.length);
        } else {
            mar.marshalUB4(0L);
        }
        if (this.attributeValueBytes != null) {
            mar.marshalUB4(this.attributeValueBytes.length);
            mar.marshalCLR(this.attributeValueBytes, this.attributeValueBytes.length);
        } else {
            mar.marshalUB4(0L);
        }
        if (this.attributeDefaultValueBytes != null) {
            mar.marshalUB4(this.attributeDefaultValueBytes.length);
            mar.marshalCLR(this.attributeDefaultValueBytes, this.attributeDefaultValueBytes.length);
        } else {
            mar.marshalUB4(0L);
        }
        mar.marshalUB4(this.flag);
    }

    static XSAttributeI unmarshal(T4CMAREngine mar) throws SQLException, IOException {
        int[] intArr = new int[1];
        String attributeName = null;
        String attributeValue = null;
        String attributeDefaultValue = null;
        int attributeNameLength = (int) mar.unmarshalUB4();
        if (attributeNameLength > 0) {
            byte[] attributeNameBytesTemp = new byte[attributeNameLength];
            mar.unmarshalCLR(attributeNameBytesTemp, 0, intArr);
            attributeName = mar.conv.CharBytesToString(attributeNameBytesTemp, intArr[0]);
        }
        int attributeValueLength = (int) mar.unmarshalUB4();
        if (attributeValueLength > 0) {
            byte[] attributeValueBytesTemp = new byte[attributeValueLength];
            mar.unmarshalCLR(attributeValueBytesTemp, 0, intArr);
            attributeValue = mar.conv.CharBytesToString(attributeValueBytesTemp, intArr[0]);
        }
        int attributeDefaultValueLength = (int) mar.unmarshalUB4();
        if (attributeDefaultValueLength > 0) {
            byte[] attributeDefaultValueBytesTemp = new byte[attributeDefaultValueLength];
            mar.unmarshalCLR(attributeDefaultValueBytesTemp, 0, intArr);
            attributeDefaultValue = mar.conv.CharBytesToString(attributeDefaultValueBytesTemp, intArr[0]);
        }
        long flag = mar.unmarshalUB4();
        XSAttributeI attr = new XSAttributeI();
        attr.setAttributeName(attributeName);
        attr.setAttributeValue(attributeValue);
        attr.setAttributeDefaultValue(attributeDefaultValue);
        attr.setFlag(flag);
        return attr;
    }
}
