package oracle.jdbc.driver;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import oracle.jdbc.internal.JMSEnqueueOptions;
import oracle.jdbc.internal.JMSMessageProperties;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoaqenq.class */
class T4CTTIoaqenq extends T4CTTIfun {
    static final int AQTTC_ENQ_STREAMING_DISABLED = 0;
    static final int AQTTC_ENQ_STREAMING_ENABLED = 1;
    static final int AQTCC_OCI_ONE_PIECE = 0;
    static final int AQTCC_OCI_FIRST_PIECE = 1;
    static final int AQTCC_OCI_NEXT_PIECE = 2;
    static final int AQTCC_OCI_LAST_PIECE = 3;
    static final int AQENQVER_DEFAULT = 1;
    static final int AQENQVER_12_2 = 2;
    static final int AQENQVER_12_1 = 1;
    T4CTTIaqm aqm;
    T4Ctoh toh;
    T4CTTIaqjms aqjms;
    private JMSEnqueueOptions enqueueOptions;
    private AQMessagePropertiesI messageProperties;
    private JMSMessageProperties jmsProp;
    private byte[] aqmcorBytes;
    private byte[] aqmeqnBytes;
    private byte[] senderAgentName;
    private byte[] senderAgentAddress;
    private byte senderAgentProtocol;
    private byte[] messageData;
    private byte[] messageOid;
    private int aqenqver;
    private AQAgentI[] attrRecipientList;
    private byte[][] recipientTextValues;
    private byte[][] recipientBinaryValues;
    private int[] recipientKeywords;
    private byte[] queueNameBytes;
    private byte[] outMsgId;
    private int bitMappedEnqueueOption;
    private byte[] headerPropBytes;
    private byte[] userPropBytes;
    private boolean retrieveMessageId;
    private boolean bStreamingMode;
    private int blockSize;
    private InputStream payloadStream;

    T4CTTIoaqenq(T4CConnection _connection) {
        super(_connection, (byte) 3);
        this.enqueueOptions = null;
        this.messageProperties = null;
        this.jmsProp = null;
        this.senderAgentName = null;
        this.senderAgentAddress = null;
        this.senderAgentProtocol = (byte) 0;
        this.messageData = null;
        this.messageOid = null;
        this.aqenqver = 0;
        this.attrRecipientList = null;
        this.recipientTextValues = (byte[][]) null;
        this.recipientBinaryValues = (byte[][]) null;
        this.recipientKeywords = null;
        this.queueNameBytes = null;
        this.outMsgId = null;
        this.bitMappedEnqueueOption = 0;
        this.retrieveMessageId = false;
        this.bStreamingMode = false;
        this.blockSize = 8192;
        this.payloadStream = null;
        setFunCode((short) 184);
        this.toh = new T4Ctoh(_connection);
        this.aqm = new T4CTTIaqm(this.connection, this.toh);
        this.aqjms = new T4CTTIaqjms(this.connection);
    }

    void doJMSEnq(String _queueName, JMSEnqueueOptions _enqueueOptions, AQMessagePropertiesI _messageProperties, JMSMessageProperties _jmsProperties, byte[] _messageOid, byte[] _messageData) throws SQLException, IOException {
        setStreamingMode(false);
        setInputStream(null);
        doJMSEnqRPC(_queueName, _enqueueOptions, _messageProperties, _jmsProperties, _messageOid, _messageData);
    }

    /* JADX WARN: Type inference failed for: r1v49, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v54, types: [byte[], byte[][]] */
    private void doJMSEnqRPC(String _queueName, JMSEnqueueOptions _enqueueOptions, AQMessagePropertiesI _messageProperties, JMSMessageProperties jmsProperties, byte[] _messageOid, byte[] _messageData) throws SQLException, IOException {
        this.enqueueOptions = _enqueueOptions;
        this.messageProperties = _messageProperties;
        this.jmsProp = jmsProperties;
        this.messageData = _messageData;
        if (this.messageProperties != null) {
            String aqmcor = this.messageProperties.getCorrelation();
            if (aqmcor != null && aqmcor.length() != 0) {
                this.aqmcorBytes = this.meg.conv.StringToCharBytes(aqmcor);
            } else {
                this.aqmcorBytes = null;
            }
            String aqmeqn = this.messageProperties.getExceptionQueue();
            if (aqmeqn != null && aqmeqn.length() != 0) {
                this.aqmeqnBytes = this.meg.conv.StringToCharBytes(aqmeqn);
            } else {
                this.aqmeqnBytes = null;
            }
            AQAgentI senderAgent = (AQAgentI) this.messageProperties.getSender();
            if (senderAgent != null) {
                if (senderAgent.getName() != null) {
                    this.senderAgentName = this.meg.conv.StringToCharBytes(senderAgent.getName());
                } else {
                    this.senderAgentName = null;
                }
                if (senderAgent.getAddress() != null) {
                    this.senderAgentAddress = this.meg.conv.StringToCharBytes(senderAgent.getAddress());
                } else {
                    this.senderAgentAddress = null;
                }
                this.senderAgentProtocol = (byte) senderAgent.getProtocol();
            } else {
                this.senderAgentName = null;
                this.senderAgentAddress = null;
                this.senderAgentProtocol = (byte) 0;
            }
            this.attrRecipientList = (AQAgentI[]) this.messageProperties.getRecipientList();
            if (this.attrRecipientList != null && this.attrRecipientList.length > 0) {
                this.recipientTextValues = new byte[this.attrRecipientList.length * 3];
                this.recipientBinaryValues = new byte[this.attrRecipientList.length * 3];
                this.recipientKeywords = new int[this.attrRecipientList.length * 3];
                for (int i = 0; i < this.attrRecipientList.length; i++) {
                    if (this.attrRecipientList[i].getName() != null) {
                        this.recipientTextValues[3 * i] = this.meg.conv.StringToCharBytes(this.attrRecipientList[i].getName());
                    }
                    if (this.attrRecipientList[i].getAddress() != null) {
                        this.recipientTextValues[(3 * i) + 1] = this.meg.conv.StringToCharBytes(this.attrRecipientList[i].getAddress());
                    }
                    this.recipientBinaryValues[(3 * i) + 2] = new byte[1];
                    this.recipientBinaryValues[(3 * i) + 2][0] = (byte) this.attrRecipientList[i].getProtocol();
                    this.recipientKeywords[3 * i] = 3 * i;
                    this.recipientKeywords[(3 * i) + 1] = (3 * i) + 1;
                    this.recipientKeywords[(3 * i) + 2] = (3 * i) + 2;
                }
            }
        } else {
            this.aqmcorBytes = null;
            this.aqmeqnBytes = null;
            this.senderAgentName = null;
            this.senderAgentAddress = null;
            this.senderAgentProtocol = (byte) 0;
        }
        this.messageData = _messageData;
        this.messageOid = _messageOid;
        if (_queueName != null && _queueName.length() != 0) {
            this.queueNameBytes = this.meg.conv.StringToCharBytes(_queueName);
        } else {
            this.queueNameBytes = null;
        }
        this.bitMappedEnqueueOption = _enqueueOptions.getDeliveryMode().getCode() + _enqueueOptions.getVisibility().getCode();
        if (this.jmsProp != null) {
            this.headerPropBytes = this.meg.conv.StringToCharBytes(this.jmsProp.getHeaderProperties());
            this.userPropBytes = this.meg.conv.StringToCharBytes(this.jmsProp.getUserProperties());
        } else {
            this.headerPropBytes = null;
            this.userPropBytes = null;
        }
        this.outMsgId = null;
        try {
            if (this.connection.getVersionNumber() >= 12200 && TypeDescriptor.isV2available(_messageOid)) {
                this.aqenqver = 2;
            } else {
                this.aqenqver = 1;
            }
        } catch (Exception e) {
            this.aqenqver = 1;
        }
        doRPC();
    }

    private void setStreamingMode(boolean flag) {
        this.bStreamingMode = flag;
    }

    private void setBlockSize(int _blockSize) {
        if (_blockSize > 0) {
            this.blockSize = _blockSize;
        }
    }

    private void setInputStream(InputStream inputStream) {
        this.payloadStream = inputStream;
    }

    void doJMSEnq(String _queueName, JMSEnqueueOptions _enqueueOptions, AQMessagePropertiesI _messageProperties, JMSMessageProperties _jmsProperties, byte[] _messageOid, InputStream _inputStream, int blockSize) throws SQLException, IOException {
        setStreamingMode(true);
        setBlockSize(blockSize);
        setInputStream(_inputStream);
        doJMSEnqRPC(_queueName, _enqueueOptions, _messageProperties, _jmsProperties, _messageOid, null);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(this.queueNameBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.aqm.initToDefaultValues();
        if (this.messageProperties != null) {
            this.aqm.aqmpri = this.messageProperties.getPriority();
            this.aqm.aqmdel = this.messageProperties.getDelay();
            this.aqm.aqmexp = this.messageProperties.getExpiration();
            this.aqm.originalMsgId = this.messageProperties.getPreviousQueueMessageId();
            this.aqm.aqmshardNum = this.messageProperties.getShardNum();
        }
        this.aqm.aqmcorBytes = this.aqmcorBytes;
        this.aqm.aqmeqnBytes = this.aqmeqnBytes;
        this.aqm.senderAgentName = this.senderAgentName;
        this.aqm.senderAgentAddress = this.senderAgentAddress;
        this.aqm.senderAgentProtocol = this.senderAgentProtocol;
        this.aqm.marshal();
        this.meg.marshalSB4(this.bitMappedEnqueueOption);
        if (this.jmsProp != null) {
            this.aqjms.aqjmsflags = this.jmsProp.getJMSMessageType().getCode();
            this.aqjms.aqjmshdrpcnt = 0;
            this.aqjms.aqjmsusrprpcnt = 0;
        } else {
            this.aqjms.aqjmsflags = 0;
            this.aqjms.aqjmshdrpcnt = 0;
            this.aqjms.aqjmsusrprpcnt = 0;
        }
        this.aqjms.aqjmshdrprop = this.headerPropBytes;
        this.aqjms.aqjmsuserprop = this.userPropBytes;
        this.aqjms.marshal();
        if (this.messageOid != null) {
            this.meg.marshalPTR();
            this.meg.marshalSWORD(16);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalUB2(this.aqenqver);
        this.meg.marshalNULLPTR();
        if (this.messageData != null) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.messageData.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.bStreamingMode) {
            this.meg.marshalSB4(1);
        } else {
            this.meg.marshalSB4(0);
        }
        if (this.enqueueOptions.isRetrieveMessageId()) {
            this.retrieveMessageId = true;
            this.meg.marshalPTR();
            this.meg.marshalSWORD(16);
        } else {
            this.retrieveMessageId = false;
            this.meg.marshalNULLPTR();
            this.meg.marshalSWORD(0);
        }
        if (this.connection.getTTCVersion() >= 14) {
            this.meg.marshalNULLPTR();
        }
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalCHR(this.queueNameBytes);
        }
        if (this.messageOid != null) {
            this.meg.marshalB1Array(this.messageOid);
        }
        if (this.messageData != null) {
            this.meg.marshalB1Array(this.messageData);
        }
        if (this.bStreamingMode) {
            writeStreamingPayload();
        }
    }

    private void writeStreamingPayload() throws IOException {
        byte[] bytearray = new byte[this.blockSize];
        boolean firstTime = true;
        while (true) {
            int noOfBytesRead = this.payloadStream.read(bytearray);
            if (noOfBytesRead < this.blockSize) {
                writeLast(bytearray, noOfBytesRead);
                this.payloadStream.close();
                this.payloadStream = null;
                return;
            } else if (firstTime) {
                writeFirst(bytearray, noOfBytesRead);
                firstTime = false;
            } else {
                writeNext(bytearray, noOfBytesRead);
            }
        }
    }

    private void writeFirst(byte[] _byteArray, int length) throws IOException {
        this.meg.marshalUB1((short) 1);
        this.meg.marshalSB8(length);
        this.meg.marshalB1Array(_byteArray, 0, length);
    }

    private void writeNext(byte[] _byteArray, int length) throws IOException {
        this.meg.marshalUB1((short) 2);
        this.meg.marshalSB8(length);
        this.meg.marshalB1Array(_byteArray, 0, length);
    }

    private void writeLast(byte[] _byteArray, int length) throws IOException {
        if (length > 0) {
            this.meg.marshalUB1((short) 3);
            this.meg.marshalSB8(length);
            this.meg.marshalB1Array(_byteArray, 0, length);
        } else {
            this.meg.marshalUB1((short) 3);
            this.meg.marshalSB8(0L);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        if (this.retrieveMessageId) {
            this.outMsgId = new byte[16];
            this.meg.unmarshalBuffer(this.outMsgId, 0, 16);
        }
    }

    byte[] getMsgId() {
        return this.outMsgId;
    }
}
