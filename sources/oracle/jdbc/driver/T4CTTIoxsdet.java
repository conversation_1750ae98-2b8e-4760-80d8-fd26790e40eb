package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.XSSecureId;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxsdet.class */
final class T4CTTIoxsdet extends T4CTTIfun {
    private int opcode;
    private byte[] sessionId;
    private XSSecureId secureId;

    T4CTTIoxsdet(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 181);
    }

    void doOXSDET(int opcode, byte[] sessionId, XSSecureId secureId, boolean roundTripRPC) throws SQLException, IOException {
        if (roundTripRPC) {
            setTTCCode((byte) 3);
        } else {
            setTTCCode((byte) 17);
        }
        this.opcode = opcode;
        this.sessionId = sessionId;
        this.secureId = secureId;
        if (roundTripRPC) {
            doRPC();
        } else {
            doPigRPC();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.opcode);
        boolean sendSessionId = false;
        if (this.sessionId != null && this.sessionId.length > 0) {
            sendSessionId = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sessionId.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.secureId == null) {
            this.meg.marshalNULLPTR();
        } else {
            this.meg.marshalPTR();
        }
        if (sendSessionId) {
            this.meg.marshalB1Array(this.sessionId);
        }
        if (this.secureId != null) {
            ((XSSecureIdI) this.secureId).marshal(this.meg);
        }
    }
}
