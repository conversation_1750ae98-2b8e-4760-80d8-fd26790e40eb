package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.KeywordValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIocsessret.class */
class T4CTTIocsessret extends T4CTTIfun {
    int sessretokvn;
    KeywordValue[] sessretokv;
    long sessretflags;
    long sessretidx;
    int sessretser;

    T4CTTIocsessret(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 4);
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
    }

    void receive() throws SQLException, IOException {
        this.meg.unmarshalUB2();
        this.meg.unmarshalUB1();
        this.sessretokvn = this.meg.unmarshalUB2();
        if (this.sessretokvn > 0) {
            this.meg.unmarshalUB1();
            this.sessretokv = new KeywordValue[this.sessretokvn];
            for (int i = 0; i < this.sessretokvn; i++) {
                this.sessretokv[i] = KeywordValueI.unmarshal(this.meg);
            }
            this.connection.updateSessionProperties(this.sessretokv);
        }
        this.sessretflags = this.meg.unmarshalUB4();
        this.sessretidx = this.meg.unmarshalUB4();
        this.sessretser = this.meg.unmarshalUB2();
        this.connection.updateSessionProperties("AUTH_SESSION_ID", String.valueOf(this.sessretidx));
        this.connection.updateSessionProperties("AUTH_SERIAL_NUM", String.valueOf(this.sessretser));
        this.connection.resetAfterReusePooledConnection();
    }
}
