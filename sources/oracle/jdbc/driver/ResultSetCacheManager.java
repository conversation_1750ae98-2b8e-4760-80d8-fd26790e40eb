package oracle.jdbc.driver;

import java.util.Hashtable;
import oracle.jdbc.driver.ResultSetCache;
import oracle.jdbc.internal.Monitor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ResultSetCacheManager.class */
final class ResultSetCacheManager {
    private static final Hashtable<String, ResultSetCache> cacheTable = new Hashtable<>(10);
    private static final Monitor CACHE_TABLE_MONITOR = Monitor.newInstance();

    ResultSetCacheManager() {
    }

    static ResultSetCache getResultSetCache(String databaseUniqueName, long cacheMaxSize, int cacheLag) {
        Monitor.CloseableLock lock = CACHE_TABLE_MONITOR.acquireCloseableLock();
        Throwable th = null;
        try {
            ResultSetCache cache = cacheTable.get(databaseUniqueName);
            if (cache == null) {
                cache = new ResultSetCache(cacheMaxSize, cacheLag);
                cacheTable.put(databaseUniqueName, cache);
            } else if (cache.getState() == ResultSetCache.ResultSetCacheState.CLOSED) {
                cacheTable.remove(databaseUniqueName);
                cache = new ResultSetCache(cacheMaxSize, cacheLag);
                cacheTable.put(databaseUniqueName, cache);
            }
            return cache;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }
}
