package oracle.jdbc.diagnostics;

import java.io.Serializable;
import java.util.Iterator;
import oracle.jdbc.TraceKey;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/TraceAttributes.class */
public abstract class TraceAttributes implements Iterable<Entry>, Serializable {
    protected final String[] values;
    static final /* synthetic */ boolean $assertionsDisabled;

    public abstract ImmutableTraceAttributes toReadOnly();

    static {
        $assertionsDisabled = !TraceAttributes.class.desiredAssertionStatus();
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/TraceAttributes$Entry.class */
    public static final class Entry {
        public final TraceKey key;
        public final String value;

        private Entry(TraceKey key, String value) {
            this.key = key;
            this.value = value;
        }
    }

    protected TraceAttributes() {
        this.values = new String[TraceKey.maxIndex()];
    }

    protected TraceAttributes(String[] values) {
        this();
        if (values != null) {
            if (!$assertionsDisabled && values.length != this.values.length) {
                throw new AssertionError();
            }
            System.arraycopy(values, 0, this.values, 0, values.length);
        }
    }

    public String get(Object key) {
        return this.values[((TraceKey) key).index()];
    }

    @Override // java.lang.Iterable
    public Iterator<Entry> iterator() {
        return new Iterator<Entry>() { // from class: oracle.jdbc.diagnostics.TraceAttributes.1
            private Iterator<TraceKey> i = TraceKey.iterator();

            @Override // java.util.Iterator
            public boolean hasNext() {
                return this.i.hasNext();
            }

            /* JADX WARN: Can't rename method to resolve collision */
            @Override // java.util.Iterator
            public Entry next() {
                TraceKey k = this.i.next();
                return new Entry(k, TraceAttributes.this.values[k.index()]);
            }
        };
    }
}
