package oracle.jdbc.driver;

import java.sql.SQLException;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ReturnParamBinder.class */
class ReturnParamBinder extends Binder {
    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        return this;
    }

    ReturnParamBinder() {
        this.type = (short) 994;
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        return localCheckSum;
    }
}
