package oracle.jdbc.driver.parser;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import oracle.jdbc.driver.parser.Earley;
import oracle.jdbc.driver.parser.Parser;
import oracle.jdbc.driver.parser.util.Array;
import oracle.jdbc.driver.parser.util.Service;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/SqlEarley.class */
public class SqlEarley extends Earley implements Parseable {
    private static SqlEarley instance;
    private static RuleTuple[] origRules;
    private int as;
    private int aliased_dml_table_expression_clause;
    private int basic_decl_item;
    private int begin;
    private int body;
    private int boolean_primary;
    private int compound_expression;
    private int condition;
    public int CONNECT;
    private int dotted_name;
    public int decl_id;
    private int distinct;
    private int ELSE;
    private int expr;
    private int grouping_expression_list;
    private int json_object_arg_list;
    private int model_expression;
    private int arg_list;
    public int multiset_except;
    private int pkg_spec;
    private int pls_expr;
    public int query_block;
    public int REPLACE;
    public int select;
    public int simple_expression;
    public int sim_stmt;
    public int sql_statement;
    public int sql_statements;
    public int sqlplus_lexeme;
    private int start;
    public int stmt;
    public int subquery;
    public int table_reference;
    private int unlabeled_nonblock_stmt;
    private int user_defined_types;
    private int where_gby_hier;
    private int HEX_LITERAL;
    private int RegExpLiteral;
    private int StringLiteral;
    private int TemplateLiteral;
    private int[] whatToRecognize;
    int END;
    int RPAREN;
    int semi;
    private int[] notOptimizableHeadSymbols;
    boolean isAsc;
    public static String[] keywords = {"'WITH'", "'SELECT'", "'FROM'", "'WHERE'", "'AND'", "'OR'", "'NOT'", "'DISTINCT'", "'UNION'", "'ALL'", "'NATURAL'", "'ON'", "'INSERT'", "'UPDATE'", "'CREATE'", "'ALTER'", "'TABLE'", "'VALUES'", "'VARCHAR2'", "'INTEGER'", "'WHEN'"};
    private Set<Integer> _keywords;
    private Set<Integer> _JSkeywords;

    public static SqlEarley newPartialRecognizer() {
        return newPartialRecognizer(new String[]{"sql_statements", "subprg_body", "expr"});
    }

    public static SqlEarley newPartialRecognizer(String[] what2Recognize) {
        SqlEarley ret = new SqlEarley() { // from class: oracle.jdbc.driver.parser.SqlEarley.1
            @Override // oracle.jdbc.driver.parser.SqlEarley, oracle.jdbc.driver.parser.Earley
            protected boolean lookaheadOK(Parser.Tuple t, int pos, Matrix matrix) {
                return true;
            }

            @Override // oracle.jdbc.driver.parser.SqlEarley, oracle.jdbc.driver.parser.Earley
            protected void merge(Parser.EarleyCell cell, long[] ls, Matrix matrix) {
                cell.merge(ls);
            }

            @Override // oracle.jdbc.driver.parser.Earley
            protected boolean scan(Matrix matrix, List<LexerToken> src) {
                boolean ret2 = super.scan(matrix, src);
                matrix.LAsuspect = null;
                return ret2;
            }
        };
        for (String symbol : what2Recognize) {
            ret.addSymbol2Recognize(symbol);
        }
        return ret;
    }

    static {
        instance = null;
        origRules = null;
        try {
            origRules = UnifiedRules.getRules();
            instance = newPartialRecognizer();
        } catch (Exception e) {
            throw new RuntimeException("Could not initialize SQL parser", e);
        }
    }

    public static SqlEarley getInstance() {
        return instance;
    }

    static RuleTuple[] getRules() {
        return origRules;
    }

    private SqlEarley() {
        this(getRules());
    }

    private SqlEarley(RuleTuple[] rules) {
        super(rules, false);
        this.whatToRecognize = new int[0];
        this.END = getSymbol("'END'");
        this.RPAREN = getSymbol("')'");
        this.semi = getSymbol("';'");
        this.notOptimizableHeadSymbols = null;
        this.isAsc = false;
        this._keywords = new TreeSet();
        this._JSkeywords = new TreeSet();
        initKeywords();
        this.as = getSymbol("'AS'");
        this.aliased_dml_table_expression_clause = getSymbol("aliased_dml_table_expression_clause");
        this.basic_decl_item = getSymbol("basic_decl_item");
        this.begin = getSymbol("'BEGIN'");
        this.body = getSymbol("'BODY'");
        this.boolean_primary = getSymbol("boolean_primary");
        this.compound_expression = getSymbol("compound_expression");
        this.condition = getSymbol("condition");
        this.CONNECT = getSymbol("CONNECT");
        this.dotted_name = getSymbol("dotted_name");
        this.decl_id = getSymbol("decl_id");
        this.distinct = getSymbol("'DISTINCT'");
        this.ELSE = getSymbol("'ELSE'");
        this.expr = getSymbol("expr");
        this.grouping_expression_list = getSymbol("grouping_expression_list");
        this.json_object_arg_list = getSymbol("json_object_arg_list");
        this.model_expression = getSymbol("model_expression");
        this.arg_list = getSymbol("arg_list");
        this.multiset_except = getSymbol("multiset_except");
        this.pkg_spec = getSymbol("pkg_spec");
        this.pls_expr = getSymbol("pls_expr");
        this.query_block = getSymbol("query_block");
        this.REPLACE = getSymbol("'REPLACE'");
        this.select = getSymbol("select");
        this.simple_expression = getSymbol("simple_expression");
        this.sim_stmt = getSymbol("sim_stmt");
        this.sql_statement = getSymbol("sql_statement");
        this.sql_statements = getSymbol("sql_statements");
        this.sqlplus_lexeme = getSymbol("sqlplus_lexeme");
        this.start = getSymbol("'START'");
        this.stmt = getSymbol("stmt");
        this.subquery = getSymbol("subquery");
        this.table_reference = getSymbol("table_reference");
        this.unlabeled_nonblock_stmt = getSymbol("unlabeled_nonblock_stmt");
        this.user_defined_types = getSymbol("user_defined_types");
        this.where_gby_hier = getSymbol("\"where,gby,hier\"");
        this.HEX_LITERAL = getSymbol("HEX_LITERAL");
        this.RegExpLiteral = getSymbol("RegExpLiteral");
        this.StringLiteral = getSymbol("string_literal");
        this.TemplateLiteral = getSymbol("TemplateLiteral");
        prioritizeRules();
        precomputePredictions();
    }

    private void prioritizeRules() {
        prioritizeRules(getSymbol("adt_field"), new int[]{getSymbol("method_specification"), getSymbol("field")});
        prioritizeRules(getSymbol("\"alter_pluggable_database_clause\""), new int[]{getSymbol("pdb_datafile_clause"), getSymbol("pdb_change_state_from_root")});
        prioritizeRules(getSymbol("alter_table___0#"), new int[]{getSymbol("constraint_clauses"), getSymbol("column_clauses"), getSymbol("alter_external_table")});
        prioritizeRules(getSymbol("analytic_function"), new int[]{getSymbol("count"), getSymbol("nth_value"), getSymbol("first_last_value"), getSymbol("listagg"), getSymbol("lag"), getSymbol("lead"), getSymbol("sum"), getSymbol("min"), getSymbol("max"), getSymbol("a_f")});
        swapRules("assoc_arg:  assoc_name_list  '='  '>'  pls_expr;", "assoc_arg:  sim_expr  '='  '>'  expr;");
        prioritizeRules(getSymbol("basic_d"), new int[]{getSymbol("subprg_i"), getSymbol("object_d")});
        prioritizeRules(this.basic_decl_item, new int[]{getSymbol("pragma"), getSymbol("basic_d")});
        prioritizeRules(this.boolean_primary, new int[]{getSymbol("'TRUE'"), getSymbol("'FALSE'"), getSymbol("sim_expr"), this.condition, getSymbol("function_expression")});
        swapRules("boolean_primary:  sim_expr;", "boolean_primary:  condition;");
        prioritizeRules(getSymbol("cell_assignment___0"), new int[]{getSymbol("multi_column_for_loop"), getSymbol("cell_assignment___1")});
        prioritizeRules(getSymbol("col_properties"), new int[]{getSymbol("out_of_line_ref_constraint"), getSymbol("out_of_line_constraint"), getSymbol("column_definition")});
        prioritizeRules(getSymbol("column_definition___2"), new int[]{getSymbol("inline_ref_constraint"), getSymbol("inline_constraint")});
        swapRules("\"cond_or_expr\":  expr;", "\"cond_or_expr\":  model_condition;");
        swapRules("insert_into_clause:  'INTO'  aliased_dml_table_expression_clause;", "insert_into_clause:  'INTO'  aliased_dml_table_expression_clause  insert_into_clause___0;");
        prioritizeRules(getSymbol("comparison_condition"), new int[]{getSymbol("between_condition"), getSymbol("group_comparison_condition"), getSymbol("simple_comparison_condition")});
        prioritizeRules(this.compound_expression, new int[]{getSymbol("expr"), getSymbol("compound_expression___1")});
        prioritizeRules(this.condition, new int[]{getSymbol("compound_condition"), getSymbol("comparison_condition"), getSymbol("null_condition"), getSymbol("JSON_condition"), getSymbol("simple_expression"), getSymbol("function_expression")});
        prioritizeRules(getSymbol("datetime_expression___1"), new int[]{getSymbol("'DBTIMEZONE'"), getSymbol("string_literal"), this.expr});
        prioritizeRules(getSymbol("expr#"), new int[]{this.simple_expression, this.compound_expression, getSymbol("function_expression"), getSymbol("object_access_expression"), getSymbol("type_constructor_expression"), getSymbol("JSON_object_access_expr"), getSymbol("model_expression"), getSymbol("compound_condition")});
        prioritizeRules(getSymbol("function_expression"), new int[]{getSymbol("function"), getSymbol("function_call")});
        prioritizeRules(getSymbol("function"), new int[]{getSymbol("aggregate_function"), getSymbol("analytic_function"), getSymbol("single_row_function"), getSymbol("user_defined_function"), getSymbol("object_reference_function")});
        prioritizeRules(getSymbol("group_by_col"), new int[]{getSymbol("rollup_cube_clause"), getSymbol("expr")});
        prioritizeRules(getSymbol("modify_column_clauses___1"), new int[]{getSymbol("modify_col_visibility"), getSymbol("modify_col_properties"), getSymbol("virtual_column_definition")});
        prioritizeRules(getSymbol("pdb_change_state"), new int[]{getSymbol("pdb_change_state___0"), this.identifier});
        prioritizeRules(this.pls_expr, new int[]{getSymbol("pls_expr"), getSymbol("and_expr")});
        prioritizeRules(getSymbol("query_table_expression"), new int[]{getSymbol("xmltable"), getSymbol("table_collection_expression"), getSymbol("function_expression")});
        prioritizeRules(getSymbol("select_term"), new int[]{this.expr, getSymbol("\"aliased_expr\"")});
        prioritizeRules(this.simple_expression, new int[]{getSymbol("literal"), getSymbol("'NULL'"), getSymbol("'CONNECT_BY_ROOT'"), getSymbol("'ROWID'"), getSymbol("'ROWNUM'"), getSymbol("'CONNECT_BY_ISCYCLE'"), getSymbol("'CONNECT_BY_ISLEAF'"), getSymbol("identifier"), getSymbol("column")});
        prioritizeRules(this.sim_stmt, new int[]{getSymbol("null_stmt"), getSymbol("exit_stmt"), getSymbol("continue_stmt"), getSymbol("raise_stmt"), getSymbol("return_stmt"), getSymbol("procedure_call")});
        prioritizeRules(getSymbol("ty_def"), new int[]{getSymbol("array_ty_def"), getSymbol("tbl_ty_def")});
        prioritizeRules(getSymbol("unconstrained_type_wo_datetime_wo_national"), new int[]{getSymbol("pls_number_datatypes"), getSymbol("link_expanded_n")});
        prioritizeRules(this.unlabeled_nonblock_stmt, new int[]{getSymbol("sql_stmt"), this.sim_stmt});
        prioritizeRules(getSymbol("values_clause___0"), new int[]{getSymbol("par_expr_list"), this.expr});
        prioritizeRules(getSymbol("windowing_clause___3"), new int[]{getSymbol("'UNBOUNDED'"), this.expr});
        prioritizeRules(getSymbol("windowing_clause___6"), new int[]{getSymbol("'UNBOUNDED'"), this.expr});
    }

    private void prioritizeRules(int head, int[] symbols) {
        int[] ruleNumbers = new int[symbols.length];
        for (int i = 0; i < this.rules.length; i++) {
            Parser.Tuple t = this.rules[i];
            if (t.head == head && (t.rhs.length == 1 || t.head == this.boolean_primary || t.head == this.basic_decl_item || t.head == this.compound_expression || t.head == this.simple_expression || t.head == this.pls_expr || t.head == this.unlabeled_nonblock_stmt || t.head == this.sim_stmt)) {
                int j = 0;
                while (true) {
                    if (j >= symbols.length) {
                        break;
                    }
                    if (t.rhs[0] != symbols[j]) {
                        j++;
                    } else {
                        ruleNumbers[j] = i;
                        break;
                    }
                }
            }
        }
        Parser.Tuple[] tuples = new Parser.Tuple[symbols.length];
        for (int j2 = 0; j2 < symbols.length; j2++) {
            tuples[j2] = this.rules[ruleNumbers[j2]];
        }
        Arrays.sort(ruleNumbers);
        for (int i2 = 0; i2 < ruleNumbers.length; i2++) {
            this.rules[ruleNumbers[i2]] = tuples[i2];
        }
    }

    public void addSymbol2Recognize(String additionalSymbol) {
        addSymbol2Recognize(getSymbol(additionalSymbol));
    }

    public void addSymbol2Recognize(int additionalSymbol) {
        int[] tmp = new int[this.whatToRecognize.length + 1];
        for (int i = 0; i < this.whatToRecognize.length; i++) {
            if (this.whatToRecognize[i] == additionalSymbol) {
                return;
            }
            tmp[i] = this.whatToRecognize[i];
        }
        tmp[this.whatToRecognize.length] = additionalSymbol;
        this.whatToRecognize = tmp;
    }

    @Override // oracle.jdbc.driver.parser.Earley
    protected void initCell00(List<LexerToken> src, Matrix matrix) {
        matrix.initCells(src.size());
        if (src.size() == 0) {
            return;
        }
        initCell(matrix, this.whatToRecognize, 0);
        LexerToken LAtoken = src.get(0);
        matrix.LAsuspect = this.symbolIndexes.get("'" + LAtoken.content.toUpperCase() + "'");
    }

    protected boolean scan(Matrix matrix, List<LexerToken> src, long[] deletedXs) {
        int y = matrix.lastY();
        if (src.size() <= y) {
            return false;
        }
        if (deletedXs != null) {
            for (long range : deletedXs) {
                for (int x = Service.lX(range); x < Service.lY(range); x++) {
                    matrix.allXs = Array.delete(matrix.allXs, x);
                }
            }
            deletedXs = null;
        }
        LexerToken token = src.get(y);
        String tokUpper = token.content.toUpperCase();
        Integer suspect = this.symbolIndexes.get("'" + tokUpper + "'");
        matrix.LAsuspect = null;
        if (y + 1 < src.size()) {
            LexerToken LAtoken = src.get(y + 1);
            matrix.LAsuspect = this.symbolIndexes.get("'" + LAtoken.content.toUpperCase() + "'");
        }
        boolean ret = false;
        for (int i = matrix.allXs.length - 1; 0 <= i; i--) {
            int x2 = matrix.allXs[i];
            if (scan(matrix, y, src, x2, suspect, deletedXs)) {
                ret = true;
            }
        }
        if (scan(matrix, y, src, y, suspect, deletedXs)) {
            ret = true;
        }
        return ret;
    }

    private boolean scan(Matrix matrix, int y, List<LexerToken> src, int x, Integer suspect, long[] deletedXs) {
        long[] content = null;
        Cell candidateRules = matrix.get(x, y);
        if (candidateRules == null) {
            return false;
        }
        for (int j = 0; j < candidateRules.size(); j++) {
            int pos = candidateRules.getPosition(j);
            int ruleNo = candidateRules.getRule(j);
            Parser.Tuple t = this.rules[ruleNo];
            if (t.size() - 1 >= pos && isScannedSymbol(y, src, pos, t, suspect) && lookaheadOK(t, pos + 1, matrix)) {
                long cellElem = makeMatrixCellElem(ruleNo, pos + 1, t);
                content = Array.insert(content, cellElem);
                if (t.rhs.length == pos + 1) {
                    matrix.enqueue(Service.lPair(x, t.head));
                }
            }
        }
        if (content == null) {
            return false;
        }
        matrix.put(x, y + 1, new Parser.EarleyCell(content));
        matrix.allXs = Array.insert(matrix.allXs, x);
        return true;
    }

    @Override // oracle.jdbc.driver.parser.Earley
    protected boolean lookaheadOK(Parser.Tuple t, int pos, Matrix matrix) {
        if (pos > t.size() - 1) {
            return true;
        }
        int nextInTuple = t.content(pos);
        if (isTerminal(nextInTuple)) {
            return matrix.LAsuspect != null && nextInTuple == matrix.LAsuspect.intValue();
        }
        Earley.PredictedTerminals terminal = this.terminalPredictions[nextInTuple];
        if (terminal != null) {
            return terminal.matches(matrix.LAsuspect);
        }
        return true;
    }

    @Override // oracle.jdbc.driver.parser.Earley
    protected void merge(Parser.EarleyCell cell, long[] ls, Matrix matrix) {
        if (ls != null) {
            for (long l : ls) {
                Parser.Tuple t = this.rules[ruleFromEarleyCell(l)];
                if (lookaheadOK(t, 0, matrix)) {
                    cell.insertContent(l);
                }
            }
        }
    }

    protected boolean isIdentifier(int y, List<LexerToken> src, int symbol, Integer suspect, boolean isJsRule) {
        LexerToken token = src.get(y);
        if (symbol == this.RegExpLiteral && token.type == Token.REGEXP) {
            return true;
        }
        if (symbol != this.identifier) {
            return false;
        }
        LexerToken prior = null;
        if (!isJsRule && suspect != null && this._keywords.contains(suspect)) {
            if (0 < y) {
                prior = src.get(y - 1);
            }
            if (prior == null || !":".equals(prior.content)) {
                return false;
            }
        }
        if (isJsRule && suspect != null && this._JSkeywords.contains(suspect)) {
            return false;
        }
        if (!isJsRule && token.type == Token.DQUOTED_STRING) {
            return true;
        }
        if (token.type != Token.IDENTIFIER) {
            return false;
        }
        if (suspect == null) {
            return true;
        }
        if (prior == null && 0 < y) {
            prior = src.get(y - 1);
        }
        if ("TO".equalsIgnoreCase(token.content) && prior != null && ("YEAR".equalsIgnoreCase(prior.content) || "HOUR".equalsIgnoreCase(prior.content) || "MINUTE".equalsIgnoreCase(prior.content))) {
            return false;
        }
        if ("IF".equalsIgnoreCase(token.content) && prior != null && "TABLE".equalsIgnoreCase(prior.content)) {
            return false;
        }
        if ("BY".equalsIgnoreCase(token.content) && prior != null && "CONNECT".equalsIgnoreCase(prior.content)) {
            return false;
        }
        if ("CASE".equalsIgnoreCase(token.content) && y + 1 < src.size() && "WHEN".equalsIgnoreCase(src.get(y + 1).content)) {
            return false;
        }
        if (("LEFT".equalsIgnoreCase(token.content) || "CROSS".equalsIgnoreCase(token.content)) && y < src.size() - 1) {
            LexerToken next = src.get(y + 1);
            if ("JOIN".equalsIgnoreCase(next.content)) {
                return false;
            }
            return true;
        }
        if ("INNER".equalsIgnoreCase(token.content) && y < src.size() - 1) {
            LexerToken next2 = src.get(y + 1);
            if ("JOIN".equalsIgnoreCase(next2.content)) {
                return false;
            }
            if (prior != null && "NATURAL".equalsIgnoreCase(prior.content)) {
                return false;
            }
            return true;
        }
        if (prior != null && "FROM".equalsIgnoreCase(prior.content) && ("FIRST".equalsIgnoreCase(token.content) || "LAST".equalsIgnoreCase(token.content))) {
            LexerToken next3 = null;
            if (y + 1 < src.size()) {
                next3 = src.get(y + 1);
            }
            if (next3 != null && ("OVER".equalsIgnoreCase(next3.content) || "IGNORE".equalsIgnoreCase(next3.content) || "RESPECT".equalsIgnoreCase(next3.content))) {
                return false;
            }
        }
        if ("AS".equalsIgnoreCase(token.content) && prior != null && "@".equalsIgnoreCase(prior.content)) {
            return false;
        }
        if ("BETWEEN".equalsIgnoreCase(token.content)) {
            LexerToken next4 = null;
            if (y < src.size()) {
                next4 = src.get(y + 1);
            }
            if (next4 != null && "-".equalsIgnoreCase(next4.content)) {
                return false;
            }
            return true;
        }
        return true;
    }

    @Override // oracle.jdbc.driver.parser.Earley
    protected boolean isScannedSymbol(int y, List<LexerToken> src, int pos, Parser.Tuple t, Integer suspect) {
        LexerToken token = src.get(y);
        int symbol = t.content(pos);
        if (symbol == this.digits && token.type == Token.DIGITS) {
            return true;
        }
        if (symbol == this.string_literal && token.type == Token.BQUOTED_STRING) {
            return true;
        }
        if (symbol == this.string_literal && token.type == Token.QUOTED_STRING) {
            return true;
        }
        if (symbol == this.sqlplus_lexeme && token.type == Token.SQLPLUS_COMMAND) {
            return true;
        }
        String head = this.allSymbols[t.head];
        boolean isJsRule = 3 < head.length() && Character.isUpperCase(head.charAt(0)) && Character.isLowerCase(head.charAt(1));
        if (isJsRule && symbol == this.string_literal && token.type == Token.DQUOTED_STRING) {
            return true;
        }
        return (suspect != null && suspect.intValue() == symbol) || (isIdentifier(y, src, symbol, suspect, isJsRule) && (suspect == null || notConfusedAsId(suspect.intValue(), t.head, pos)));
    }

    @Override // oracle.jdbc.driver.parser.Earley
    protected boolean notConfusedAsId(int suspect, int head, int pos) {
        return ((suspect == this.begin && ((head == this.dotted_name || head == this.decl_id) && pos == 0)) || (suspect == this.start && head == this.table_reference && pos == 1) || ((suspect == this.distinct && head == this.multiset_except && pos == 3) || ((suspect == this.body && head == this.pkg_spec && pos == 1) || ((suspect == this.as && head == this.table_reference && pos == 1) || ((suspect == this.ELSE && head == this.aliased_dml_table_expression_clause && pos == 1) || (suspect == this.REPLACE && head == this.user_defined_types && pos == 0)))))) ? false : true;
    }

    @Override // oracle.jdbc.driver.parser.Earley
    protected boolean isOptimizable(Parser.Tuple tuple, int preSym, int mid, int y) {
        if (preSym == this.sql_statement && y < mid + 3) {
            return false;
        }
        if (this.notOptimizableHeadSymbols == null) {
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, this.grouping_expression_list);
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, this.json_object_arg_list);
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, this.model_expression);
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, this.arg_list);
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, this.compound_expression);
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("column"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("dotted_expr"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("object_access_expression"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("pivot_clause___0#"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("elsif_clause_opt"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("table_properties___0"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("physical_properties"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("alter_system___0"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("LabeledStatement"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("MemberExpression"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("FunctionExpression"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("AssignmentExpression"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("IfStatement"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("Statement_List1"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("MethodDefinition_List1"));
            this.notOptimizableHeadSymbols = Array.insert(this.notOptimizableHeadSymbols, getSymbol("BinaryExpression"));
        }
        int headSym = tuple.head;
        int i = Array.indexOf(this.notOptimizableHeadSymbols, headSym);
        if (this.notOptimizableHeadSymbols[i] == headSym) {
            return false;
        }
        if (this.subquery == headSym && tuple.rhs.length == 2 && tuple.rhs[1] == getSymbol("row_limiting_clause")) {
            return false;
        }
        return super.isOptimizable(headSym, preSym, mid, y);
    }

    @Override // oracle.jdbc.driver.parser.Earley
    public void parse(List<LexerToken> src, Matrix matrix) {
        try {
            parse(src, matrix, null);
        } catch (InterruptedException e) {
        }
        this.skipRanges = true;
    }

    @Override // oracle.jdbc.driver.parser.Parser
    protected boolean isAsc(int ruleHead) {
        return ruleHead == getSymbol("sql_statements") || ruleHead == getSymbol("subquery");
    }

    public int recognize(List<LexerToken> src) {
        Matrix matrix = new Matrix(getInstance());
        parse(src, matrix);
        for (int i = src.size(); 0 < i; i--) {
            Cell top = matrix.get(0, i);
            if (top != null) {
                return i;
            }
        }
        throw new AssertionError("all empty cells?");
    }

    void initKeywords() {
        for (String k : keywords) {
            this._keywords.add(Integer.valueOf(getSymbol(k)));
        }
    }

    public void reInitKeywords(String[] replacement) {
        this._keywords.clear();
        for (String k : replacement) {
            this._keywords.add(Integer.valueOf(getSymbol(k)));
        }
    }

    @Override // oracle.jdbc.driver.parser.Earley
    protected ParseNode tree(List<LexerToken> src, Matrix m, int x, int y, int rule, int pos) {
        ParseNode ret = super.tree(src, m, x, y, rule, pos);
        if (ret.contains(this.subquery) && !ret.contains(this.query_block)) {
            for (ParseNode child : ret.children()) {
                if (child.contains(this.query_block)) {
                    child.deleteContent(this.query_block);
                }
            }
            ret.addContent(this.query_block);
        }
        return ret;
    }

    @Override // oracle.jdbc.driver.parser.Earley, oracle.jdbc.driver.parser.Parser
    public ParseNode treeForACell(List<LexerToken> src, Matrix m, Parser.EarleyCell cell, int x, int y) {
        int pos;
        for (int i = 0; i < cell.size(); i++) {
            int rule = cell.getRule(i);
            Parser.Tuple t = this.rules[rule];
            if ((t.head == this.sql_statements || t.head == this.select) && this.rules[rule].rhs.length == (pos = cell.getPosition(i))) {
                return tree(src, m, x, y, rule, pos);
            }
        }
        return super.treeForACell(src, m, cell, x, y);
    }

    public void parse(List<LexerToken> src, Matrix matrix, InterruptedException interrupted) throws InterruptedException {
        initCell00(src, matrix);
        predict(matrix);
        while (scan(matrix, src, null)) {
            complete(matrix, src.size());
            predict(matrix);
        }
    }

    @Override // oracle.jdbc.driver.parser.Parser
    public boolean isAuxNode(int symbol) {
        return this.where_gby_hier == symbol;
    }
}
