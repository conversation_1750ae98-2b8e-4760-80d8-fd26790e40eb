package oracle.jdbc.driver.json.binary;

import oracle.jdbc.driver.json.tree.OracleJsonBinaryImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDateImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDecimalImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDoubleImpl;
import oracle.jdbc.driver.json.tree.OracleJsonFloatImpl;
import oracle.jdbc.driver.json.tree.OracleJsonIntervalDSImpl;
import oracle.jdbc.driver.json.tree.OracleJsonIntervalYMImpl;
import oracle.jdbc.driver.json.tree.OracleJsonStringImpl;
import oracle.jdbc.driver.json.tree.OracleJsonStringNumberImpl;
import oracle.jdbc.driver.json.tree.OracleJsonTimestampImpl;
import oracle.jdbc.driver.json.tree.OracleJsonTimestampTZImpl;
import oracle.jdbc.driver.json.tree.OracleJsonVectorImpl;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OracleOsonValueFactory.class */
public class OracleOsonValueFactory extends OsonValueFactory {
    public static OracleOsonValueFactory INSTANCE = new OracleOsonValueFactory();

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OsonAbstractArray createArray(OsonContext ctx, int pos) {
        return new OsonArrayImpl(ctx, pos);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OsonAbstractObject createObject(OsonContext ctx, int pos) {
        return new OsonObjectImpl(ctx, pos);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createString(OsonContext ctx, int pos, int len) {
        ctx.b.position(pos);
        return new OracleJsonStringImpl(ctx.b.readString(len));
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonDecimalImpl createNumber(byte[] raw, OracleJsonDecimal.TargetType type) {
        return new OracleJsonDecimalImpl(raw, type);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonStringNumberImpl createStringNumber(String value) {
        return new OracleJsonStringNumberImpl(value);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonDoubleImpl createDouble(double value) {
        return new OracleJsonDoubleImpl(value);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createBinary(OsonContext ctx, int pos, int len, boolean isId) {
        ctx.b.position(pos);
        byte[] res = new byte[len];
        ctx.b.get(res);
        return new OracleJsonBinaryImpl(res, isId);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonTimestampImpl createTimestamp(byte[] raw) {
        return new OracleJsonTimestampImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonTimestampTZImpl createTimestampTZ(byte[] raw) {
        return new OracleJsonTimestampTZImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonFloatImpl createFloat(float flt) {
        return new OracleJsonFloatImpl(flt);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonDateImpl createDate(byte[] raw) {
        return new OracleJsonDateImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonIntervalYMImpl createIntervalYM(byte[] value) {
        return new OracleJsonIntervalYMImpl(value);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonIntervalDSImpl createIntervalDS(byte[] value) {
        return new OracleJsonIntervalDSImpl(value);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonValue createTrue() {
        return OracleJsonValue.TRUE;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonValue createFalse() {
        return OracleJsonValue.FALSE;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonValue createNull() {
        return OracleJsonValue.NULL;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonValue createVector(OsonContext ctx, int pos, int len) {
        ctx.b.position(pos);
        byte[] res = new byte[len];
        ctx.b.get(res);
        return new OracleJsonVectorImpl(res);
    }
}
