package oracle.jdbc.driver.json.tree;

import java.sql.SQLException;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.CHAR;
import oracle.sql.CharacterSet;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonString;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonStringImpl.class */
public class OracleJsonStringImpl implements OracleJsonString {
    String value;

    public OracleJsonStringImpl(String value) {
        this.value = value;
    }

    @Override // oracle.sql.json.OracleJsonString
    public String getString() {
        return this.value;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.STRING;
    }

    @Override // oracle.sql.json.OracleJsonString
    public boolean equals(Object other) {
        if (other instanceof OracleJsonString) {
            return this == other || getString().equals(((OracleJsonString) other).getString());
        }
        return false;
    }

    @Override // oracle.sql.json.OracleJsonString
    public int hashCode() {
        return getString().hashCode();
    }

    @Override // oracle.sql.json.OracleJsonString
    public CharSequence getChars() {
        return getString();
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        return JsonSerializerImpl.serializeString(getString());
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaStringImpl(this.value));
        }
        return c.cast(new JsonpPrimitive.JsonpStringImpl(this.value));
    }

    @Override // oracle.sql.json.OracleJsonString
    public CHAR getCHAR() {
        try {
            return new CHAR(getString(), CharacterSet.make(871));
        } catch (SQLException e) {
            throw new OracleJsonException(e.getMessage(), e);
        }
    }

    protected OracleJsonExceptions.ExceptionFactory getExceptionFactory() {
        return OracleJsonExceptions.ORACLE_FACTORY;
    }
}
