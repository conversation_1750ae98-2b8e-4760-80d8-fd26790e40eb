package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.logging.Level;
import oracle.jdbc.OracleDatabaseException;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.net.ns.NetException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoer11.class */
class T4CTTIoer11 extends T4CTTIMsg {
    final int MAXERRBUF = 512;
    long curRowNumber;
    long retCode;
    int arrayElemWError;
    int arrayElemErrno;
    int currCursorID;
    short errorPosition;
    short sqlType;
    byte oerFatal;
    short flags;
    short userCursorOpt;
    short upiParam;
    short warningFlag;
    int osError;
    short stmtNumber;
    short callNumber;
    int pad1;
    long successIters;
    int partitionId;
    int tableId;
    int slotNumber;
    long rba;
    long blockNumber;
    int warnLength;
    int warnFlag;
    int[] errorLength;
    byte[] errorMsg;
    byte[] oerepa;
    int startErrorOffset;
    int endErrorOffset;
    int[] batchErrorOffsetArray;
    static final int OERFNCF = 32;
    static final int ORA1403 = 1403;
    public static final boolean TRACE = false;
    private static final String CLASS_NAME = T4CTTIoer11.class.getName();
    private static final String _Copyright_2014_Oracle_All_Rights_Reserved_ = null;

    T4CTTIoer11(T4CConnection _conn) {
        super(_conn, (byte) 4);
        this.MAXERRBUF = 512;
        this.warnLength = 0;
        this.warnFlag = 0;
        this.errorLength = new int[1];
        this.batchErrorOffsetArray = null;
    }

    void init() {
        this.retCode = 0L;
        this.errorMsg = null;
        this.oerepa = null;
        this.startErrorOffset = 0;
        this.endErrorOffset = 0;
        this.batchErrorOffsetArray = null;
    }

    int unmarshal() throws SQLException, IOException {
        return unmarshal(false);
    }

    int unmarshal(boolean ignoreORA1403) throws SQLException, IOException {
        unmarshalAttributes();
        if (this.retCode != 0) {
            if (this.retCode == 1403 && ignoreORA1403) {
                unmarshalErrorMessageAndIgnore();
            } else {
                unmarshalErrorMessage();
            }
        }
        return this.currCursorID;
    }

    void unmarshalErrorMessage() throws SQLException, IOException {
        this.errorMsg = this.meg.unmarshalCLR();
        this.errorLength[0] = this.errorMsg.length;
    }

    void unmarshalErrorMessageAndIgnore() throws SQLException, IOException {
        this.meg.unmarshalCLRAndIgnore();
        this.errorMsg = new byte[0];
        this.errorLength[0] = 0;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v51, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r0v55, types: [byte[], byte[][]] */
    void unmarshalAttributes() throws SQLException, IOException {
        int maxBatchErrorOffsetArrayLen;
        if (this.connection.getTTCVersion() >= 3) {
            short _endToEndECIDSequenceNumber = (short) this.meg.unmarshalUB2();
            this.connection.endToEndECIDSequenceNumber = _endToEndECIDSequenceNumber;
        }
        this.curRowNumber = this.meg.unmarshalUB4();
        this.retCode = this.meg.unmarshalUB2();
        this.arrayElemWError = this.meg.unmarshalUB2();
        this.arrayElemErrno = this.meg.unmarshalUB2();
        this.currCursorID = this.meg.unmarshalUB2();
        this.errorPosition = this.meg.unmarshalSB2();
        this.sqlType = this.meg.unmarshalUB1();
        this.oerFatal = this.meg.unmarshalSB1();
        this.flags = this.meg.unmarshalSB1();
        this.userCursorOpt = this.meg.unmarshalSB1();
        this.upiParam = this.meg.unmarshalUB1();
        this.warningFlag = this.meg.unmarshalUB1();
        this.rba = this.meg.unmarshalUB4();
        this.partitionId = this.meg.unmarshalUB2();
        this.tableId = this.meg.unmarshalUB1();
        this.blockNumber = this.meg.unmarshalUB4();
        this.slotNumber = this.meg.unmarshalUB2();
        this.osError = this.meg.unmarshalSWORD();
        this.stmtNumber = this.meg.unmarshalUB1();
        this.callNumber = this.meg.unmarshalUB1();
        this.pad1 = this.meg.unmarshalUB2();
        this.successIters = this.meg.unmarshalUB4();
        this.meg.unmarshalDALC(this.connection.tmpBytes128, 0);
        byte[] oerrar = this.meg.unmarshalDALC();
        byte[] oerepaTemp = this.connection.tmpBytes128;
        if (oerrar != null && oerrar.length > 0 && (maxBatchErrorOffsetArrayLen = oerrar.length * 5) > this.connection.tmpBytes128.length) {
            oerepaTemp = new byte[maxBatchErrorOffsetArrayLen];
        }
        int oerepal = this.meg.unmarshalDALC(oerepaTemp, 0);
        if (oerepal > 0) {
            if (oerepaTemp == this.connection.tmpBytes128) {
                this.oerepa = Arrays.copyOf(oerepaTemp, oerepaTemp.length);
            } else {
                this.oerepa = oerepaTemp;
            }
        } else {
            this.oerepa = null;
        }
        if (this.oerepa != null && oerrar.length > 0) {
            processBatchedErrors(oerrar.length);
        }
        int oermarl = (int) this.meg.unmarshalUB4();
        if (oermarl > 0) {
            this.meg.unmarshalUB1();
            ?? r0 = new byte[oermarl];
            int[] extensionTextValuesLength = new int[oermarl];
            ?? r02 = new byte[oermarl];
            int[] extensionKeywords = new int[oermarl];
            this.meg.unmarshalKPDKV(r0, extensionTextValuesLength, r02, extensionKeywords);
        }
    }

    private void processBatchedErrors(int errorCount) {
        this.batchErrorOffsetArray = new int[errorCount];
        int position = getErrorOffset(this.oerepa, 0);
        this.batchErrorOffsetArray[0] = this.startErrorOffset;
        for (int i = 1; i < errorCount; i++) {
            position = getErrorOffset(this.oerepa, position);
            this.batchErrorOffsetArray[i] = this.endErrorOffset;
        }
    }

    private int getErrorOffset(byte[] oerepa, int position) {
        int errorOffset = 0;
        if (position < oerepa.length) {
            position++;
            int len = oerepa[position] & 255;
            if (len + position < oerepa.length) {
                if (len == 0) {
                    errorOffset = 0;
                } else if (len == 1) {
                    position++;
                    errorOffset = oerepa[position] & 255;
                } else if (len == 2) {
                    int position2 = position + 1;
                    int i = (oerepa[position] & 255) << 8;
                    position = position2 + 1;
                    errorOffset = i | (oerepa[position2] & 255);
                } else if (len == 4) {
                    int position3 = position + 1;
                    int i2 = (oerepa[position] & 255) << 24;
                    int position4 = position3 + 1;
                    int i3 = i2 | ((oerepa[position3] & 255) << 16);
                    int position5 = position4 + 1;
                    int i4 = i3 | ((oerepa[position4] & 255) << 8);
                    position = position5 + 1;
                    errorOffset = i4 | (oerepa[position5] & 255);
                }
            }
        }
        if (position == 0) {
            this.startErrorOffset = errorOffset;
        } else {
            this.endErrorOffset = errorOffset;
        }
        return position;
    }

    void unmarshalWarning() throws SQLException, IOException {
        this.retCode = this.meg.unmarshalUB2();
        this.warnLength = this.meg.unmarshalUB2();
        this.warnFlag = this.meg.unmarshalUB2();
        if (this.retCode != 0 && this.warnLength > 0) {
            this.errorMsg = this.meg.unmarshalCHR(this.warnLength);
            this.errorLength[0] = this.warnLength;
        }
    }

    void print() throws SQLException {
        if (this.retCode != 0) {
            debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "print", "**** Error Message: {0}", (String) null, (Throwable) null, () -> {
                try {
                    return new Object[]{this.meg.conv.CharBytesToString(this.errorMsg, this.errorLength[0], true)};
                } catch (SQLException e) {
                    return new Object[]{"Got an exception generating debug message: " + e.getMessage()};
                }
            });
        } else if (this.warnFlag != 0) {
            debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "print", "Warning Message: {0}", (String) null, (Throwable) null, () -> {
                try {
                    return new Object[]{this.meg.conv.CharBytesToString(this.errorMsg, this.warnLength, true)};
                } catch (SQLException e) {
                    return new Object[]{"Got an exception generating debug message: " + e.getMessage()};
                }
            });
        }
    }

    void processError() throws SQLException {
        processError(true);
    }

    void processError(boolean throwException) throws SQLException {
        processError(throwException, null);
    }

    void processError(OracleStatement stmt) throws SQLException {
        processError(true, stmt);
    }

    void processError(boolean throwException, OracleStatement stmt) throws SQLException {
        SQLException sqlException;
        if (stmt != null) {
            stmt.numberOfExecutedElementsInBatch = (int) this.successIters;
            stmt.indexOfFailedElementsInBatch = this.batchErrorOffsetArray;
        }
        long errNum = getRetCode();
        if (errNum != 0) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "processError", "retCode = {0}", (String) null, (String) null, (Object) Long.valueOf(errNum));
            debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "processError", "errorMsg = {0}", (String) null, (Throwable) null, () -> {
                try {
                    return new Object[]{this.meg.conv.CharBytesToString(this.errorMsg, this.errorLength[0], true)};
                } catch (SQLException e) {
                    return new Object[]{"Got an exception generating debug message: " + e.getMessage()};
                }
            });
            switch ((int) errNum) {
                case 28:
                case 600:
                case 1012:
                case 1041:
                case NetException.DATABASE_CONNECTION_LOST /* 3113 */:
                case 3114:
                    this.connection.internalClose();
                    break;
                case 902:
                case 21700:
                    this.connection.removeAllDescriptor();
                    break;
                case 41408:
                case 41409:
                    if ((this.flags & 16) == 16) {
                        try {
                            this.connection.abort();
                        } catch (Throwable exc) {
                            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "processError", "Error at OERFEXIT abort: {0}", (String) null, (String) null, (Object) exc);
                        }
                        this.connection.internalClose();
                        break;
                    }
                    break;
            }
            if (throwException) {
                String message = this.meg.conv.CharBytesToString(this.errorMsg, this.errorLength[0], true);
                if (((int) errNum) == 1008 && !message.contains("position 1") && message.contains(" was not provided")) {
                    StringBuilder messageBuilder = new StringBuilder(message);
                    messageBuilder.insert(messageBuilder.indexOf(" was not provided"), ":" + stmt.sqlObject.parameterList[0] + " (position 1)");
                    sqlException = (SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, messageBuilder.toString(), (int) errNum).fillInStackTrace();
                } else {
                    sqlException = (SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, message, (int) errNum).fillInStackTrace();
                }
                if (this.errorPosition >= 0 && stmt != null) {
                    sqlException.initCause(new OracleDatabaseException(this.errorPosition, (int) this.retCode, this.meg.conv.CharBytesToString(this.errorMsg, this.errorLength[0], true), stmt.sqlObject.actualSql, stmt.sqlObject.originalSql, stmt.isSqlRewritten()));
                }
                throw sqlException;
            }
            return;
        }
        if (!throwException) {
            return;
        }
        if ((this.warningFlag & 1) == 1) {
            int wrn = this.warningFlag & (-2);
            if ((wrn & 32) == 32 || (wrn & 4) == 4) {
                SQLException thrownWarning = DatabaseError.createInternallyHandledWarning();
                thrownWarning.fillInStackTrace();
                throw thrownWarning;
            }
        }
        if (this.connection != null && this.connection.plsqlCompilerWarnings && (this.flags & 4) == 4) {
            stmt.foundPlsqlCompilerWarning();
        }
    }

    void processWarning() throws SQLException {
        if (this.retCode != 0) {
            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "processWarning", "retCode = {0}", (String) null, (String) null, Long.valueOf(this.retCode));
            String warningMessgae = this.meg.conv.CharBytesToString(this.errorMsg, this.errorLength[0], true);
            debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "processWarning", "errorMsg = {0}", (String) null, (String) null, (Object) warningMessgae);
            throw DatabaseError.newSqlWarning(warningMessgae, (int) this.retCode);
        }
    }

    long getCurRowNumber() throws SQLException {
        return this.curRowNumber;
    }

    long getRetCode() {
        return this.retCode;
    }

    @Override // oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }

    long updateChecksum(long localCheckSum) throws SQLException {
        return CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(CRC64.updateChecksum(localCheckSum, this.retCode), this.curRowNumber), this.errorPosition), this.sqlType), this.oerFatal), this.flags), this.userCursorOpt), this.upiParam), this.warningFlag), this.osError), this.successIters), this.errorMsg, 0, this.errorMsg.length);
    }
}
