package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_ro.class */
public class ErrorMessagesJson_ro extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "A survenit o excepţie I/O"}, new Object[]{"26302", "Anul \"{0}\" nu este acceptat"}, new Object[]{"26303", "Depăşire, valoare prea mare: {0}."}, new Object[]{"26304", "Opţiune neacceptată (neimplementată)."}, new Object[]{"26305", "JSON-ul binar este nevalid sau corupt."}, new Object[]{"26306", "Versiune neacceptată de JSON binar: {0}."}, new Object[]{"26307", "Lungimea cheii codificate cu UTF-8 nu trebuie să depăşească 256 bytes. Următoarea cheie depăşeşte această limită: \"{0}\"."}, new Object[]{"26308", "JSON-ul specificat este prea mare pentru a fi codificat ca JSON binar. Dimensiunea imaginilor codificate nu trebuie să depăşească 2GB."}, new Object[]{"26309", "JSON-ul binar este nevalid sau corupt. Imaginea specificată conţine doar {0} bytes."}, new Object[]{"26310", "Elementul java.time.Period specificat are setate zile, dar INTERVAL YEAR TO MONTH al Oracle nu acceptă zile."}, new Object[]{"26311", "Generator închis înainte de încheiere."}, new Object[]{"26312", "Trebuie specificată o cheie de obiect în acest context."}, new Object[]{"26313", "Scriere nevalidă. O valoare completă a fost scrisă deja."}, new Object[]{"26314", "Încheiere nepermisă în acest context."}, new Object[]{"26315", "Cheie nepermisă în acest context."}, new Object[]{"26316", "Se aşteaptă o valoare după cheie."}, new Object[]{"26317", "Starea interpretorului trebuie să fie {0}."}, new Object[]{"26318", "Starea interpretorului trebuie să nu fie {0}."}, new Object[]{"26319", "Interpretorul trebuie să fie la o valoare."}, new Object[]{"26320", "\"{0}\" nu este un tip de wrapper acceptat."}, new Object[]{"26321", "Acest obiect nu poate fi modificat. Pentru a crea o copie care poate fi modificată, utilizaţi OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Această matrice nu poate fi modificată. Pentru a crea o copie care poate fi modificată, utilizaţi OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "Obiectul JSON conţine cheie duplicat: {0}."}, new Object[]{"26324", "Nu se poate detecta automat codificarea, caractere insuficiente."}, new Object[]{"26325", "Se aştepta tokenul EOF, dar s-a obţinut {0}."}, new Object[]{"26326", "Caracter neaşteptat {0} la linia {1}, coloana {2}."}, new Object[]{"26327", "Caracter neaşteptat {0} la linia {1}, coloana {2}. Se aştepta {3}."}, new Object[]{"26328", "Token nevalid {0} la linia {1}, coloana {2}. Tokenurile aşteptate sunt: {3}."}, new Object[]{"26329", "JsonParser#getString() este valid numai pentru stările KEY_NAME, VALUE_STRING, VALUE_NUMBER ale interpretorului. Dar starea curentă a interpretorului este {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() este valid numai pentru starea VALUE_NUMBER a interpretorului. Dar starea curentă a interpretorului este {0}."}, new Object[]{"26331", "JsonParser#getInt() este valid numai pentru starea VALUE_NUMBER a interpretorului. Dar starea curentă a interpretorului este {0}."}, new Object[]{"26332", "JsonParser#getLong() este valid numai pentru starea VALUE_NUMBER a interpretorului. Dar starea curentă a interpretorului este {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() este valid numai pentru starea VALUE_NUMBER a interpretorului. Dar starea curentă a interpretorului este {0}."}, new Object[]{"26334", "JsonParser#getArray() este valid doar pentru starea interpretorului START_ARRAY. Însă starea curentă a interpretorului este {0}."}, new Object[]{"26335", "JsonParser#getObject() este valid numai pentru starea interpretorului START_ARRAY. Dar starea curentă a interpretorului este {0}."}, new Object[]{"26336", "Nu este acceptat un marcaj temporal cu o regiune. Se acceptă numai fusuri orare decalate."}, new Object[]{"26337", "Obiectele şi matricele din valoarea JSON nu se pot imbrica mai mult de {0} niveluri"}, new Object[]{"26338", "Cheile unui obiect JSON nu pot depăşi 65.535 bytes"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
