package oracle.jdbc.driver;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import java.util.concurrent.CompletionStage;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.proxy.ProxyFactory;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TrueCacheDriverExtension.class */
class TrueCacheDriverExtension extends OracleDriverExtension {
    static ProxyFactory PROXY_FACTORY;
    private static final Monitor proxyFactoryLock = Monitor.newInstance();

    TrueCacheDriverExtension() {
    }

    static {
        PROXY_FACTORY = null;
        Monitor.CloseableLock lock = proxyFactoryLock.acquireCloseableLock();
        Throwable th = null;
        try {
            if (PROXY_FACTORY == null) {
                PROXY_FACTORY = ProxyFactory.createProxyFactory(AbstractTrueCacheConnection.class, AbstractTrueCacheStatement.class, AbstractTrueCachePreparedStatement.class, AbstractTrueCacheCallableStatement.class, AbstractTrueCacheResultSet.class);
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    Connection getConnection(String url, @Blind(PropertiesBlinder.class) Properties info, AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        Connection connection = (Connection) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OracleConnection.class);
        ((AbstractTrueCacheConnection) connection).initialize(url, info, this, builder);
        return connection;
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    final CompletionStage<Connection> getConnectionAsync(String url, @Blind(PropertiesBlinder.class) Properties info, AbstractConnectionBuilder<?, ?> builder) {
        return CompletionStageUtil.failedStage(new UnsupportedOperationException("Asynchronous connection is not supported by the True Cache driver"));
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    oracle.jdbc.internal.OracleStatement allocateStatement(oracle.jdbc.internal.OracleConnection oracleConnection, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        oracle.jdbc.internal.OracleStatement oracleStatement = (oracle.jdbc.internal.OracleStatement) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OracleStatement.class, oracleConnection);
        ((AbstractTrueCacheStatement) oracleStatement).initialize((AbstractTrueCacheConnection) oracleConnection, resultSetType, 0);
        return oracleStatement;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    oracle.jdbc.internal.OraclePreparedStatement allocatePreparedStatement(oracle.jdbc.internal.OracleConnection oracleConnection, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        oracle.jdbc.internal.OraclePreparedStatement oraclePreparedStatement = (oracle.jdbc.internal.OraclePreparedStatement) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OraclePreparedStatement.class, oracleConnection);
        ((AbstractTrueCachePreparedStatement) oraclePreparedStatement).initialize((AbstractTrueCacheConnection) oracleConnection, sql, resultSetType, 1);
        return oraclePreparedStatement;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    oracle.jdbc.internal.OraclePreparedStatement allocatePreparedStatement(oracle.jdbc.internal.OracleConnection oracleConnection, String sql, AutoKeyInfo autoKeyInfo) throws SQLException {
        oracle.jdbc.internal.OraclePreparedStatement oraclePreparedStatement = (oracle.jdbc.internal.OraclePreparedStatement) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OraclePreparedStatement.class, oracleConnection);
        ((AbstractTrueCachePreparedStatement) oraclePreparedStatement).initialize((AbstractTrueCacheConnection) oracleConnection, sql, autoKeyInfo, 1);
        return oraclePreparedStatement;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    oracle.jdbc.internal.OracleCallableStatement allocateCallableStatement(oracle.jdbc.internal.OracleConnection oracleConnection, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        oracle.jdbc.internal.OracleCallableStatement oracleCallableStatement = (oracle.jdbc.internal.OracleCallableStatement) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OracleCallableStatement.class, oracleConnection);
        ((AbstractTrueCacheCallableStatement) oracleCallableStatement).initialize((AbstractTrueCacheConnection) oracleConnection, sql, resultSetType, 2);
        return oracleCallableStatement;
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    OracleInputStream createInputStream(OracleStatement stmt, int index, Accessor accessor) throws SQLException {
        return new T4CInputStream(stmt, index, accessor);
    }
}
