package oracle.jdbc.driver.oauth;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UncheckedIOException;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;
import javax.net.ssl.HttpsURLConnection;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.net.nt.CustomSSLSocketFactory;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonFactory;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/oauth/IamDataplaneClient.class */
final class IamDataplaneClient {
    private static final int CACHES_SIZE = 128;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("E, dd MMM uuuu HH:mm:ss z", Locale.US);
    private static final Map<Builder, AccessTokenCache<JsonWebToken>> CACHES = Collections.synchronizedMap(new LinkedHashMap<Builder, AccessTokenCache<JsonWebToken>>(16, 0.75f, true) { // from class: oracle.jdbc.driver.oauth.IamDataplaneClient.1
        @Override // java.util.LinkedHashMap
        protected boolean removeEldestEntry(Map.Entry<Builder, AccessTokenCache<JsonWebToken>> entry) {
            return size() > 128;
        }
    });

    IamDataplaneClient() {
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Type inference failed for: r0v14, types: [java.io.InputStream, java.io.OutputStream] */
    @Blind
    public static JsonWebToken requestBearerToken(Builder builder) throws IOException {
        String str;
        URL url = new URL(builder.endPoint);
        if (!"https".equalsIgnoreCase(url.getProtocol())) {
            throw new IllegalArgumentException("Protocol of endpoint is not https: " + url.getProtocol());
        }
        HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");
        connection.setSSLSocketFactory(CustomSSLSocketFactory.getSSLSocketFactory(builder.tlsConfig, null, CommonDiagnosable.getInstance()));
        connection.setRequestProperty("Date", ZonedDateTime.now(ZoneId.of("Z")).format(DATE_FORMATTER));
        connection.setRequestProperty("Authorization", createAuthorization(builder.user, builder.password));
        connection.setDoOutput(true);
        ?? outputStream = connection.getOutputStream();
        Throwable th = null;
        try {
            try {
                Object[] objArr = new Object[2];
                if (builder.compartment == null) {
                    str = "*";
                } else {
                    str = builder.database == null ? builder.compartment : builder.compartment + "::" + builder.database;
                }
                objArr[0] = str;
                objArr[1] = builder.tenancy;
                outputStream.write(String.format("{\"scope\": \"urn:oracle:db::id::%s\", \"tenantId\": \"%s\"}", objArr).getBytes(StandardCharsets.UTF_8));
                outputStream.flush();
                if (outputStream != 0) {
                    if (0 != 0) {
                        try {
                            outputStream.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        outputStream.close();
                    }
                }
                try {
                    try {
                        InputStream response = connection.getInputStream();
                        Throwable th3 = null;
                        String token = new OracleJsonFactory().createJsonTextValue(response).asJsonObject().getString("token");
                        if (token == null) {
                            throw new IOException("JSON response does not contain a token");
                        }
                        char[] tokenChars = token.toCharArray();
                        try {
                            JsonWebToken jsonWebTokenCreateBearerToken = JsonWebToken.createBearerToken(tokenChars);
                            Arrays.fill(tokenChars, (char) 0);
                            if (response != null) {
                                if (0 != 0) {
                                    try {
                                        response.close();
                                    } catch (Throwable th4) {
                                        th3.addSuppressed(th4);
                                    }
                                } else {
                                    response.close();
                                }
                            }
                            return jsonWebTokenCreateBearerToken;
                        } catch (Throwable th5) {
                            Arrays.fill(tokenChars, (char) 0);
                            throw th5;
                        }
                    } finally {
                    }
                } catch (IOException ioException) {
                    StringBuilder messageBuilder = new StringBuilder();
                    InputStream errorStream = connection.getErrorStream();
                    if (errorStream != null) {
                        BufferedReader errorReader = new BufferedReader(new InputStreamReader(errorStream, StandardCharsets.UTF_8));
                        Throwable th6 = null;
                        try {
                            try {
                                String errorResponse = (String) errorReader.lines().collect(Collectors.joining("\n"));
                                messageBuilder.append(errorResponse);
                                if (errorReader != null) {
                                    if (0 != 0) {
                                        try {
                                            errorReader.close();
                                        } catch (Throwable th7) {
                                            th6.addSuppressed(th7);
                                        }
                                    } else {
                                        errorReader.close();
                                    }
                                }
                            } finally {
                            }
                        } catch (Throwable th8) {
                            if (errorReader != null) {
                                if (th6 != null) {
                                    try {
                                        errorReader.close();
                                    } catch (Throwable th9) {
                                        th6.addSuppressed(th9);
                                    }
                                } else {
                                    errorReader.close();
                                }
                            }
                            throw th8;
                        }
                    }
                    String opcRequestId = connection.getHeaderField("opc-request-id");
                    if (opcRequestId != null) {
                        messageBuilder.append("\n(opc-request-id: " + opcRequestId + ")");
                    }
                    if (messageBuilder.length() == 0) {
                        throw ioException;
                    }
                    throw new IOException(messageBuilder.toString(), ioException);
                } catch (OracleJsonException jsonException) {
                    throw new IOException("JSON parsing failure", jsonException);
                }
            } finally {
            }
        } catch (Throwable th10) {
            if (outputStream != 0) {
                if (th != null) {
                    try {
                        outputStream.close();
                    } catch (Throwable th11) {
                        th.addSuppressed(th11);
                    }
                } else {
                    outputStream.close();
                }
            }
            throw th10;
        }
    }

    @Blind
    private static String createAuthorization(String user, @Blind OpaqueString password) {
        byte[] userColonBytes = (user + ":").getBytes(StandardCharsets.UTF_8);
        ByteBuffer passwordBuffer = (ByteBuffer) password.map(chars -> {
            return StandardCharsets.UTF_8.encode(CharBuffer.wrap(chars));
        });
        try {
            byte[] userColonPassword = new byte[userColonBytes.length + passwordBuffer.remaining()];
            try {
                System.arraycopy(userColonBytes, 0, userColonPassword, 0, userColonBytes.length);
                passwordBuffer.get(userColonPassword, userColonBytes.length, passwordBuffer.remaining());
                String str = "Basic " + Base64.getEncoder().encodeToString(userColonPassword);
                Arrays.fill(userColonPassword, (byte) 0);
                passwordBuffer.clear();
                passwordBuffer.put(new byte[passwordBuffer.remaining()]);
                return str;
            } catch (Throwable th) {
                Arrays.fill(userColonPassword, (byte) 0);
                throw th;
            }
        } catch (Throwable th2) {
            passwordBuffer.clear();
            passwordBuffer.put(new byte[passwordBuffer.remaining()]);
            throw th2;
        }
    }

    static Builder requestBuilder() {
        return new Builder();
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/oauth/IamDataplaneClient$Builder.class */
    static final class Builder {
        private String endPoint;
        private String tenancy;
        private String compartment;
        private String database;
        private String user;
        private OpaqueString password;
        private Properties tlsConfig;

        private Builder() {
        }

        Builder endPoint(String endPoint) {
            this.endPoint = endPoint;
            return this;
        }

        Builder tenancy(String tenancy) {
            this.tenancy = tenancy;
            return this;
        }

        Builder compartment(String compartment) {
            this.compartment = compartment;
            return this;
        }

        Builder database(String database) {
            this.database = database;
            return this;
        }

        Builder user(String user) {
            this.user = user;
            return this;
        }

        Builder password(@Blind OpaqueString password) {
            this.password = password;
            return this;
        }

        Builder tlsConfig(Properties tlsConfig) {
            this.tlsConfig = (Properties) tlsConfig.clone();
            this.tlsConfig.put(41, "true");
            return this;
        }

        @Blind
        JsonWebToken build() throws IOException {
            try {
                return (JsonWebToken) ((AccessTokenCache) IamDataplaneClient.CACHES.computeIfAbsent(this, this0 -> {
                    return JsonWebToken.createCache(() -> {
                        try {
                            return IamDataplaneClient.requestBearerToken(this);
                        } catch (IOException ioException) {
                            throw new UncheckedIOException(ioException);
                        }
                    });
                })).get();
            } catch (UncheckedIOException ioException) {
                throw ioException.getCause();
            }
        }

        public boolean equals(Object object) {
            return this == object || ((object instanceof Builder) && Objects.equals(this.endPoint, ((Builder) object).endPoint) && Objects.equals(this.tenancy, ((Builder) object).tenancy) && Objects.equals(this.compartment, ((Builder) object).compartment) && Objects.equals(this.database, ((Builder) object).database) && Objects.equals(this.user, ((Builder) object).user) && Objects.equals(this.password, ((Builder) object).password) && Objects.equals(this.tlsConfig, ((Builder) object).tlsConfig));
        }

        public int hashCode() {
            return Objects.hash(this.endPoint, this.tenancy, this.compartment, this.database, this.user, this.password, this.tlsConfig);
        }
    }
}
