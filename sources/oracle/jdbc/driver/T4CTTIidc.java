package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIidc.class */
class T4CTTIidc extends T4CTTIMsg {
    T4CTTIkscn kpdqidcscn;
    T4CTTIqcinv[] kpdqidccinv;
    T4CTTIqcinv[] kpdqidcusr;
    long kpdqidcflg;

    T4CTTIidc(T4CConnection _conn) {
        super(_conn, (byte) 0);
    }

    void unmarshal() throws SQLException, IOException {
        this.kpdqidcscn = this.connection.kscnForByteLength();
        this.kpdqidcscn.unmarshal();
        int kpdqidccinvl = this.meg.unmarshalSWORD();
        if (kpdqidccinvl > 0) {
            this.kpdqidccinv = new T4CTTIqcinv[kpdqidccinvl];
            for (int i = 0; i < kpdqidccinvl; i++) {
                this.kpdqidccinv[i] = new T4CTTIqcinv(this.connection);
                this.kpdqidccinv[i].unmarshal();
            }
        } else {
            this.kpdqidccinv = null;
        }
        int kpdqidcusrl = this.meg.unmarshalSWORD();
        if (kpdqidcusrl > 0) {
            this.kpdqidcusr = new T4CTTIqcinv[kpdqidcusrl];
            for (int i2 = 0; i2 < kpdqidcusrl; i2++) {
                this.kpdqidcusr[i2] = new T4CTTIqcinv(this.connection);
                this.kpdqidcusr[i2].unmarshal();
            }
        } else {
            this.kpdqidcusr = null;
        }
        this.kpdqidcflg = this.meg.unmarshalUB4();
    }
}
