package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIosessget.class */
class T4CTTIosessget extends T4CTTIfun {
    static final int SESSGET_TAG_MISMATCH = 1;
    static final int SESSGET_PURITY_NEW = 2;
    static final int SESSGET_SESSION_CHANGED = 4;
    static final int SESSGET_STMTCACHE_DESTROY = 8;
    static final int SESSGET_INFLAGS_MATCHANY = 1;
    int sessgetokvn;
    KeywordValueI[] sessgetokv;
    long sessgetflags;
    int sessigetflags;
    String returnTag;
    int returnTagLength;

    T4CTTIosessget(T4CConnection _conn) {
        super(_conn, (byte) 3);
        this.sessgetflags = 0L;
        this.sessigetflags = 0;
        this.returnTag = null;
        this.returnTagLength = 0;
        setFunCode((short) 162);
        this.sessgetflags |= 1;
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.sessigetflags = 0;
        if (this.connection.useDRCPMultipletag) {
            this.sessigetflags |= 1;
        }
        this.meg.marshalDALC(T4CMAREngine.NO_BYTES);
        this.meg.marshalPTR();
        this.meg.marshalPTR();
        if (this.connection.getTTCVersion() >= 8) {
            this.meg.marshalUB2(this.sessigetflags);
            this.meg.marshalPTR();
            this.meg.marshalPTR();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        this.sessgetokvn = this.meg.unmarshalUB2();
        if (this.sessgetokvn > 0) {
            short nbOfBytes = this.meg.unmarshalUB1();
            this.meg.unmarshalNBytes(nbOfBytes);
        }
        this.sessgetflags = this.meg.unmarshalUB4();
        if (this.connection.getTTCVersion() >= 8) {
            this.returnTagLength = this.meg.unmarshalUB2();
            this.returnTag = new String(this.meg.unmarshalNBytes(this.returnTagLength));
        }
    }
}
