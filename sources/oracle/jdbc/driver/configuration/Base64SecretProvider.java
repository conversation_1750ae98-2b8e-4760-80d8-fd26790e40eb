package oracle.jdbc.driver.configuration;

import java.util.Map;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.spi.OracleConfigurationSecretProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/configuration/Base64SecretProvider.class */
public class Base64SecretProvider implements OracleConfigurationSecretProvider {
    private final String className = JsonParser.class.getName();

    @Override // oracle.jdbc.spi.OracleConfigurationSecretProvider
    public char[] getSecret(Map<String, String> secretProperties) {
        CommonDiagnosable.getInstance().trace(Level.WARNING, SecurityLabel.STATIC, this.className, "getSecret", "Base64 Encoding in a JSON Password should only be used in development environments", null, null, new Object[0]);
        return secretProperties.get("value").toCharArray();
    }

    @Override // oracle.jdbc.spi.OracleConfigurationSecretProvider
    public String getSecretType() {
        return "base64";
    }
}
