package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4Ctoh.class */
class T4Ctoh {
    static final int TOPLVL_KPCTOH = 1;
    static final int SERBEG_KPCTOH = 2;
    static final int SEREND_KPCTOH = 4;
    static final int SERONE_KPCTOH = 8;
    static final int NEW_KPCTOH = 16;
    static final int UPDATE_KPCTOH = 32;
    static final int DELETE_KPCTOH = 64;
    static final int LAST_KPCTOH = 128;
    static final int NOOBJ_KPCTOH = 256;
    static final int NNO_KPCTOH = 512;
    static final int RAWSTR_KPCTOH = 1024;
    static final byte KORFPFNNL = 2;
    static final byte EXTENT_OID = 8;
    static final int DONE_KPCTOC = 0;
    static final int MORE_KPCTOC = -1;
    static final int IGNORE_KPCTOC = -2;
    static final int KOLRUG_ENABLE = 1;
    static final int KOIDFLEN = 16;
    static final int KOIDSLEN = 8;
    byte[] toid = null;
    byte[] oid = null;
    byte[] snapshot = null;
    int versionNumber = 0;
    int imageLength = 0;
    int flags = 0;
    int[] intArr = new int[1];
    T4CConnection connection;
    T4CTTIksnp ksnp;
    static final byte[] EOID_KOTTD = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1};
    static final byte[] ANYDATA_TOID = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 17};

    T4Ctoh(T4CConnection _connection) {
        this.connection = _connection;
        if (this.connection.getTTCVersion() >= 8) {
            this.ksnp = new T4CTTIksnp();
        } else {
            this.ksnp = null;
        }
    }

    void init(byte[] _toid, int _imageLength) {
        if (this.toid == null || this.toid.length != 36) {
            this.toid = new byte[36];
        }
        this.toid[0] = 0;
        this.toid[1] = 36;
        this.toid[2] = 2;
        this.toid[3] = 8;
        System.arraycopy(_toid, 0, this.toid, 4, 16);
        System.arraycopy(EOID_KOTTD, 0, this.toid, 20, 16);
        this.imageLength = _imageLength;
        this.oid = null;
        if (this.connection.getTTCVersion() >= 8) {
            this.ksnp.init();
        } else {
            this.snapshot = null;
        }
        this.versionNumber = 0;
        this.flags = 1;
    }

    void marshal(T4CMAREngine meg) throws IOException {
        if (this.toid == null) {
            meg.marshalUB4(0L);
        } else {
            meg.marshalUB4(this.toid.length);
            meg.marshalCLR(this.toid, 0, this.toid.length);
        }
        if (this.oid == null) {
            meg.marshalUB4(0L);
        } else {
            meg.marshalUB4(this.oid.length);
            meg.marshalCLR(this.oid, 0, this.oid.length);
        }
        if (this.connection.getTTCVersion() >= 8) {
            this.ksnp.marshal(meg);
        } else if (this.snapshot == null) {
            meg.marshalUB4(0L);
        } else {
            meg.marshalUB4(this.snapshot.length);
            meg.marshalCLR(this.snapshot, 0, this.snapshot.length);
        }
        meg.marshalUB2(this.versionNumber);
        meg.marshalUB4(this.imageLength);
        meg.marshalUB2(this.flags);
    }

    void unmarshal(T4CMAREngine meg) throws SQLException, IOException {
        int toidLength = (int) meg.unmarshalUB4();
        if (this.toid == null || this.toid.length != toidLength) {
            this.toid = new byte[toidLength];
        }
        if (toidLength > 0) {
            meg.unmarshalCLR(this.toid, 0, this.intArr, toidLength);
        }
        int oidLength = (int) meg.unmarshalUB4();
        this.oid = new byte[oidLength];
        if (oidLength > 0) {
            meg.unmarshalCLR(this.oid, 0, this.intArr, oidLength);
        }
        if (this.connection.getTTCVersion() >= 8) {
            this.ksnp.unmarshal(meg);
        } else {
            int snapshotLength = (int) meg.unmarshalUB4();
            this.snapshot = new byte[snapshotLength];
            if (snapshotLength > 0) {
                meg.unmarshalCLR(this.snapshot, 0, this.intArr, snapshotLength);
            }
        }
        this.versionNumber = meg.unmarshalUB2();
        this.imageLength = (int) meg.unmarshalUB4();
        this.flags = meg.unmarshalUB2();
    }
}
