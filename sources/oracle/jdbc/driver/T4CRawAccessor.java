package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CRawAccessor.class */
class T4CRawAccessor extends RawAccessor implements T4CAccessor {
    T4CMAREngine mare;
    boolean underlyingLongRaw;
    private T4CMarshaller marshaller;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CRawAccessor.class.desiredAssertionStatus();
    }

    T4CRawAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, form, external_type, isOutBind, false);
        this.underlyingLongRaw = false;
        this.marshaller = null;
        this.mare = _mare;
    }

    T4CRawAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len == -1 ? _definedColumnSize : max_len, nullable, flags, precision, scale, contflag, total_elems, form);
        this.underlyingLongRaw = false;
        this.marshaller = null;
        this.mare = _mare;
        if (stmt != null && stmt.implicitDefineForLobPrefetchDone) {
            this.definedColumnType = 0;
            this.definedColumnSize = 0;
        } else {
            this.definedColumnType = _definedColumnType;
            this.definedColumnSize = _definedColumnSize;
        }
        if (max_len == -1) {
            this.underlyingLongRaw = true;
        }
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    @Override // oracle.jdbc.driver.T4CAccessor
    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        return getMarshaller().unmarshalOneRow(this);
    }

    int readStreamFromWire(byte[] buffer, int offset, int length, int[] escapeSequenceArr, boolean[] readHeaderArr, boolean[] readAsNonStreamArr, T4CMAREngine mare, T4CTTIoer11 oer) throws SQLException, IOException {
        return getMarshaller().readStreamFromWire(buffer, offset, length, escapeSequenceArr, readHeaderArr, readAsNonStreamArr, mare, oer);
    }

    private final T4CMarshaller getMarshaller() {
        if (this.marshaller == null) {
            this.marshaller = this.describeType == 24 ? T4CMarshaller.LONG_RAW : T4CMarshaller.RAW;
        }
        return this.marshaller;
    }

    @Override // oracle.jdbc.driver.RawCommonAccessor, oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException {
        String ret = super.getString(currentRow);
        if (ret != null && this.definedColumnSize > 0 && ret.length() > this.definedColumnSize * 2) {
            ret = ret.substring(0, this.definedColumnSize * 2);
        }
        return ret;
    }

    @Override // oracle.jdbc.driver.RawCommonAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getObject(currentRow);
        }
        if (isNull(currentRow)) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.LONGNVARCHAR /* -16 */:
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case -1:
            case 1:
            case 12:
                return getString(currentRow);
            case oracle.jdbc.OracleTypes.PLSQL_INDEX_TABLE /* -14 */:
            case oracle.jdbc.OracleTypes.BFILE /* -13 */:
            case -12:
            case -11:
            case oracle.jdbc.OracleTypes.CURSOR /* -10 */:
            case oracle.jdbc.OracleTypes.ROWID /* -8 */:
            case oracle.jdbc.OracleTypes.BIT /* -7 */:
            case oracle.jdbc.OracleTypes.TINYINT /* -6 */:
            case -5:
            case -3:
            case 0:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 11:
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
            case -2:
                return getRAW(currentRow);
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new AccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CRawAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CRawAccessor(stmt, T4CRawAccessor.this.describeMaxLength, T4CRawAccessor.this.nullable, -1, T4CRawAccessor.this.precision, T4CRawAccessor.this.scale, T4CRawAccessor.this.contflag, -1, T4CRawAccessor.this.formOfUse, T4CRawAccessor.this.definedColumnType, T4CRawAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
