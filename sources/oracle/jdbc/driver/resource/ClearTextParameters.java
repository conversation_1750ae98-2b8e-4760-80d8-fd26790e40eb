package oracle.jdbc.driver.resource;

import java.nio.CharBuffer;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.spi.OracleResourceProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ClearTextParameters.class */
final class ClearTextParameters implements AutoCloseable {
    private final Map<OracleResourceProvider.Parameter, ClearText> clearTextParameters;

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ClearTextParameters$ClearText.class */
    private interface ClearText {
        CharSequence getCharSequence();

        void wipeContents();
    }

    private ClearTextParameters(Builder builder) {
        this.clearTextParameters = new HashMap(builder.clearTextParameters);
    }

    Map<OracleResourceProvider.Parameter, CharSequence> getParameterValues() {
        return (Map) this.clearTextParameters.entrySet().stream().collect(Collectors.toMap((v0) -> {
            return v0.getKey();
        }, entry -> {
            return ((ClearText) entry.getValue()).getCharSequence();
        }));
    }

    @Override // java.lang.AutoCloseable
    public void close() {
        for (ClearText clearText : this.clearTextParameters.values()) {
            clearText.wipeContents();
        }
    }

    static Builder builder() {
        return new Builder();
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ClearTextParameters$Builder.class */
    static final class Builder {
        private final Map<OracleResourceProvider.Parameter, ClearText> clearTextParameters = new HashMap();
        static final /* synthetic */ boolean $assertionsDisabled;

        Builder() {
        }

        static {
            $assertionsDisabled = !ClearTextParameters.class.desiredAssertionStatus();
        }

        Builder addInsensitiveValue(OracleResourceProvider.Parameter parameter, CharSequence value) {
            if (!$assertionsDisabled && parameter.isSensitive()) {
                throw new AssertionError("Parameter is sensitive: " + parameter);
            }
            this.clearTextParameters.put(parameter, new InsensitiveClearText(value));
            return this;
        }

        Builder addSensitiveValue(OracleResourceProvider.Parameter parameter, OpaqueString value) {
            this.clearTextParameters.put(parameter, new SensitiveClearText(value));
            return this;
        }

        ClearTextParameters build() {
            return new ClearTextParameters(this);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ClearTextParameters$InsensitiveClearText.class */
    private static final class InsensitiveClearText implements ClearText {
        private final CharSequence charSequence;

        private InsensitiveClearText(CharSequence charSequence) {
            this.charSequence = charSequence;
        }

        @Override // oracle.jdbc.driver.resource.ClearTextParameters.ClearText
        public CharSequence getCharSequence() {
            return this.charSequence;
        }

        @Override // oracle.jdbc.driver.resource.ClearTextParameters.ClearText
        public void wipeContents() {
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/ClearTextParameters$SensitiveClearText.class */
    private static final class SensitiveClearText implements ClearText {
        private final OpaqueString opaqueString;
        private CharBuffer charBuffer;

        private SensitiveClearText(OpaqueString opaqueString) {
            this.opaqueString = opaqueString;
        }

        @Override // oracle.jdbc.driver.resource.ClearTextParameters.ClearText
        public CharSequence getCharSequence() {
            if (this.charBuffer == null) {
                this.charBuffer = CharBuffer.wrap(this.opaqueString.getChars());
            }
            return this.charBuffer;
        }

        @Override // oracle.jdbc.driver.resource.ClearTextParameters.ClearText
        public void wipeContents() {
            Arrays.fill(this.charBuffer.array(), (char) 0);
            this.charBuffer = null;
        }
    }
}
