package oracle.jdbc.driver.json;

import javax.json.JsonException;
import javax.json.stream.JsonGenerationException;
import javax.json.stream.JsonLocation;
import javax.json.stream.JsonParsingException;
import oracle.jdbc.driver.json.OracleJsonExceptions;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/JsonpExceptionFactory.class */
public final class JsonpExceptionFactory implements OracleJsonExceptions.ExceptionFactory {
    public static JsonpExceptionFactory INSTANCE = new JsonpExceptionFactory();

    private JsonpExceptionFactory() {
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createJsonException(String message, Throwable cause) {
        return new JsonException(message, cause);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createJsonException(String message) {
        return new JsonException(message);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createGenerationException(String message, Throwable cause) {
        return new JsonGenerationException(message, cause);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createGenerationException(String message) {
        return new JsonGenerationException(message);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createParsingException(String message, Throwable cause) {
        return new JsonParsingException(message, cause, (JsonLocation) null);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createParsingException(String message) {
        return new JsonParsingException(message, (JsonLocation) null);
    }
}
