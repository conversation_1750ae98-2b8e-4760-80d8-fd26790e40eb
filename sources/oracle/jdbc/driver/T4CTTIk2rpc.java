package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIk2rpc.class */
final class T4CTTIk2rpc extends T4CTTIfun {
    static final int K2RPClogon = 1;
    static final int K2RPCbegin = 2;
    static final int K2RPCend = 3;
    static final int K2RPCrecover = 4;
    static final int K2RPCsession = 5;
    private int k2rpctyp;
    private int command;

    T4CTTIk2rpc(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 67);
    }

    void doOK2RPC(int _k2rpctyp, int _command) throws SQLException, IOException {
        this.k2rpctyp = _k2rpctyp;
        this.command = _command;
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(0L);
        this.meg.marshalUB4(this.k2rpctyp);
        this.meg.marshalPTR();
        this.meg.marshalUB4(3L);
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(0L);
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(0L);
        this.meg.marshalPTR();
        this.meg.marshalUB4(3L);
        this.meg.marshalPTR();
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(0L);
        this.meg.marshalNULLPTR();
        this.meg.marshalNULLPTR();
        this.meg.marshalUB4(0L);
        this.meg.marshalNULLPTR();
        if (this.connection.getTTCVersion() >= 8) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(2L);
            this.meg.marshalPTR();
            this.meg.marshalUB4(2L);
            this.meg.marshalPTR();
        }
        this.meg.marshalUB4(this.command);
        this.meg.marshalUB4(0L);
        this.meg.marshalUB4(0L);
        if (this.connection.getTTCVersion() >= 8) {
            this.meg.marshalUB4(0L);
            this.meg.marshalUB4(0L);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int k2rpco4l = this.meg.unmarshalUB2();
        for (int i = 0; i < k2rpco4l; i++) {
            this.meg.unmarshalUB4();
        }
        if (this.connection.getTTCVersion() >= 8) {
            int k2rpcoub4l = this.meg.unmarshalUB2();
            for (int i2 = 0; i2 < k2rpcoub4l; i2++) {
                this.meg.unmarshalUB4();
            }
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
