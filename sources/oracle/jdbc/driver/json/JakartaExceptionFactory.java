package oracle.jdbc.driver.json;

import jakarta.json.JsonException;
import jakarta.json.stream.JsonGenerationException;
import jakarta.json.stream.JsonLocation;
import jakarta.json.stream.JsonParsingException;
import oracle.jdbc.driver.json.OracleJsonExceptions;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/JakartaExceptionFactory.class */
public final class JakartaExceptionFactory implements OracleJsonExceptions.ExceptionFactory {
    public static JakartaExceptionFactory INSTANCE = new JakartaExceptionFactory();

    private JakartaExceptionFactory() {
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createJsonException(String message, Throwable cause) {
        return new JsonException(message, cause);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createJsonException(String message) {
        return new JsonException(message);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createGenerationException(String message, Throwable cause) {
        return new JsonGenerationException(message, cause);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createGenerationException(String message) {
        return new JsonGenerationException(message);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createParsingException(String message, Throwable cause) {
        return new JsonParsingException(message, cause, (JsonLocation) null);
    }

    @Override // oracle.jdbc.driver.json.OracleJsonExceptions.ExceptionFactory
    public RuntimeException createParsingException(String message) {
        return new JsonParsingException(message, (JsonLocation) null);
    }
}
