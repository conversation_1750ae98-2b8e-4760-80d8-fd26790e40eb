package oracle.jdbc.driver;

import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.io.Writer;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.SocketException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.AccessController;
import java.security.NoSuchAlgorithmException;
import java.security.PrivilegedAction;
import java.security.spec.InvalidKeySpecException;
import java.sql.Array;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.Date;
import java.sql.NClob;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.SQLWarning;
import java.sql.SQLXML;
import java.sql.Savepoint;
import java.sql.Statement;
import java.sql.Struct;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.EnumSet;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.logging.Level;
import javax.transaction.xa.XAResource;
import oracle.jdbc.LogicalTransactionIdEventListener;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleDatabaseException;
import oracle.jdbc.OracleOCIFailover;
import oracle.jdbc.OracleShardingKey;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.aq.AQDequeueOptions;
import oracle.jdbc.aq.AQEnqueueOptions;
import oracle.jdbc.aq.AQMessage;
import oracle.jdbc.aq.AQMessageProperties;
import oracle.jdbc.aq.AQNotificationRegistration;
import oracle.jdbc.clio.annotations.Debug;
import oracle.jdbc.dcn.DatabaseChangeRegistration;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.JMSDequeueOptions;
import oracle.jdbc.internal.JMSEnqueueOptions;
import oracle.jdbc.internal.JMSMessage;
import oracle.jdbc.internal.JMSNotificationRegistration;
import oracle.jdbc.internal.KeywordValueLong;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.NetStat;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleLargeObject;
import oracle.jdbc.internal.OracleStatement;
import oracle.jdbc.internal.PDBChangeEventListener;
import oracle.jdbc.internal.XSEventListener;
import oracle.jdbc.internal.XSKeyval;
import oracle.jdbc.internal.XSNamespace;
import oracle.jdbc.internal.XSPrincipal;
import oracle.jdbc.internal.XSSecureId;
import oracle.jdbc.internal.XSSessionParameters;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.jdbc.oracore.OracleTypeCLOB;
import oracle.jdbc.pool.OracleOCIConnectionPool;
import oracle.jdbc.pool.OraclePooledConnection;
import oracle.net.ns.NetException;
import oracle.net.resolver.NameResolver;
import oracle.net.resolver.NameResolverFactory;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.BFILE;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.BLOB;
import oracle.sql.BfileDBAccess;
import oracle.sql.BlobDBAccess;
import oracle.sql.CLOB;
import oracle.sql.CharacterSet;
import oracle.sql.ClobDBAccess;
import oracle.sql.CustomDatum;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.INTERVALDS;
import oracle.sql.INTERVALYM;
import oracle.sql.LobPlsqlUtil;
import oracle.sql.NCLOB;
import oracle.sql.NUMBER;
import oracle.sql.SQLName;
import oracle.sql.StructDescriptor;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.TIMEZONETAB;
import oracle.sql.TypeDescriptor;
import oracle.sql.ZONEIDMAP;
import oracle.sql.converter.CharacterSetMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CConnection.class */
public class T2CConnection extends PhysicalConnection implements BfileDBAccess, BlobDBAccess, ClobDBAccess {
    short[] queryMetaData1;
    byte[] queryMetaData2;
    int queryMetaData1Offset;
    int queryMetaData2Offset;
    private OpaqueString password;
    int fatalErrorNumber;
    String fatalErrorMessage;
    static final int QMD_dbtype = 0;
    static final int QMD_dbsize = 1;
    static final int QMD_nullok = 2;
    static final int QMD_precision = 3;
    static final int QMD_scale = 4;
    static final int QMD_formOfUse = 5;
    static final int QMD_columnNameLength = 6;
    static final int QMD_tdo0 = 7;
    static final int QMD_tdo1 = 8;
    static final int QMD_tdo2 = 9;
    static final int QMD_tdo3 = 10;
    static final int QMD_charLength = 11;
    static final int QMD_schemaNameLength = 12;
    static final int QMD_typeNameLength = 13;
    static final int QMD_columnInvisible = 14;
    static final int QMD_columnJSON = 15;
    static final int T2C_LOCATOR_MAX_LEN = 16;
    static final int T2C_LINEARIZED_LOCATOR_MAX_LEN = 4000;
    static final int T2C_LINEARIZED_BFILE_LOCATOR_MAX_LEN = 530;
    static final int METADATA1_INDICES_PER_COLUMN = 16;
    protected static final int SIZEOF_QUERYMETADATA2 = 8;
    static final String defaultDriverNameAttribute = "jdbcoci";
    int queryMetaData1Size;
    int queryMetaData2Size;
    long m_nativeState;
    short m_clientCharacterSet;
    byte byteAlign;
    private static final int EOJ_SUCCESS = 0;
    private static final int EOJ_ERROR = -1;
    private static final int EOJ_WARNING = 1;
    private static final int EOJ_GET_STORAGE_ERROR = -4;
    private static final int EOJ_ORA3113_SERVER_NORMAL = -6;
    private static final String OCILIBRARY = "ocijdbc23";
    private int logon_mode;
    static final int LOGON_MODE_DEFAULT = 0;
    static final int LOGON_MODE_SYSDBA = 2;
    static final int LOGON_MODE_SYSOPER = 4;
    static final int LOGON_MODE_SYSASM = 32768;
    static final int LOGON_MODE_SYSBKP = 131072;
    static final int LOGON_MODE_SYSDGD = 262144;
    static final int LOGON_MODE_SYSKMT = 524288;
    static final int LOGON_MODE_CONNECTION_POOL = 5;
    static final int LOGON_MODE_CONNPOOL_CONNECTION = 6;
    static final int LOGON_MODE_CONNPOOL_PROXY_CONNECTION = 7;
    static final int LOGON_MODE_CONNPOOL_ALIASED_CONNECTION = 8;
    static final int T2C_PROXYTYPE_NONE = 0;
    static final int T2C_PROXYTYPE_USER_NAME = 1;
    static final int T2C_PROXYTYPE_DISTINGUISHED_NAME = 2;
    static final int T2C_PROXYTYPE_CERTIFICATE = 3;
    static final int T2C_CONNECTION_FLAG_DEFAULT_LOB_PREFETCH = 0;
    static final int T2C_CONNECTION_FLAG_PRELIM_AUTH = 1;
    static final int T2C_CONNECTION_FLAG_CHARSET = 2;
    static final int T2C_CONNECTION_FLAG_NCHARSET = 3;
    static final int T2C_CONNECTION_FLAG_BYTE_ALIGN = 4;
    static final int T2C_CONNECTION_FLAG_SERVER_TZ_VERSION = 5;
    static final int T2C_CONNECTION_FLAG_TAF_ENABLED = 6;
    static final int T2C_CONNECTION_TAG_MATCHED = 7;
    static final int T2C_CONNECTION_FLAG_MAXLEN_COMPAT = 8;
    private static final int OCI_ATTR_PURITY_DEFAULT = 0;
    private static final int OCI_ATTR_PURITY_NEW = 1;
    private static final int OCI_ATTR_PURITY_SELF = 2;
    static final int T2C_MAX_SCHEMA_NAME_LENGTH = 258;
    private static boolean isLibraryLoaded;
    static final int T2C_LOGON_OUT_BUFFER_LENGTH = 256;
    static final int EOO_LOGIN_OUT_TYPE_DBID = 1;
    static final int EOO_LOGIN_OUT_INST_START_TIME = 2;
    String databaseUniqueIdentifier;
    OracleOCIFailover appCallback;
    Object appCallbackObject;
    private Properties nativeInfo;
    ByteBuffer nioBufferForLob;
    boolean[] tagMatched;
    static final String CONNECT_DATA_KEYWORD = "CONNECT_DATA";
    static final int OCI_SESSRLS_DROPSESS = 1;
    static final int OCI_SESSRLS_RETAG = 2;
    static final long JDBC_OCI_LIBRARY_VERSION = Long.parseLong("23.8.0.25.04".replaceAll("\\.", ""));
    static final byte[] EMPTY_BYTES = new byte[0];
    private static final String CLASS_NAME = T2CConnection.class.getName();
    private static final Monitor LOAD_LIBRARY_MONITOR = Monitor.newInstance();
    private static final Monitor SET_TIMEZONE_MONITOR = Monitor.newInstance();
    static final Map<String, String> cachedVersionTable = new Hashtable();

    native int t2cAbort(long j);

    native int t2cBeginRequest(long j);

    native int t2cEndRequest(long j);

    native int t2cBlobRead(long j, byte[] bArr, int i, long j2, int i2, byte[] bArr2, int i3, boolean z, ByteBuffer byteBuffer);

    native int t2cClobRead(long j, byte[] bArr, int i, long j2, int i2, char[] cArr, int i3, boolean z, boolean z2, ByteBuffer byteBuffer);

    native int t2cBlobWrite(long j, byte[] bArr, int i, long j2, int i2, byte[] bArr2, int i3, byte[][] bArr3);

    native int t2cClobWrite(long j, byte[] bArr, int i, long j2, int i2, char[] cArr, int i3, byte[][] bArr2, boolean z);

    native long t2cLobGetLength(long j, byte[] bArr, int i);

    native int t2cBfileOpen(long j, byte[] bArr, int i, byte[][] bArr2);

    native int t2cBfileIsOpen(long j, byte[] bArr, int i, boolean[] zArr);

    native int t2cBfileExists(long j, byte[] bArr, int i, boolean[] zArr);

    native String t2cBfileGetName(long j, byte[] bArr, int i);

    native String t2cBfileGetDirAlias(long j, byte[] bArr, int i);

    native int t2cBfileClose(long j, byte[] bArr, int i, byte[][] bArr2);

    native int t2cLobGetChunkSize(long j, byte[] bArr, int i);

    native int t2cLobTrim(long j, int i, long j2, byte[] bArr, int i2, byte[][] bArr2);

    native int t2cLobCreateTemporary(long j, int i, boolean z, int i2, short s, byte[][] bArr);

    native int t2cLobFreeTemporary(long j, int i, byte[] bArr, int i2, byte[][] bArr2);

    native int t2cLobIsTemporary(long j, int i, byte[] bArr, int i2, boolean[] zArr);

    native int t2cLobOpen(long j, int i, byte[] bArr, int i2, int i3, byte[][] bArr2);

    native int t2cLobIsOpen(long j, int i, byte[] bArr, int i2, boolean[] zArr);

    native int t2cLobClose(long j, int i, byte[] bArr, int i2, byte[][] bArr2);

    static native int getLibraryVersionNumber();

    static native short t2cGetServerSessionInfo(long j, Properties properties);

    static native short t2cGetDriverCharSetFromNlsLang();

    native int t2cDescribeError(long j, T2CError t2CError, byte[] bArr, long j2);

    native boolean t2cIsServerStatusValid(long j);

    native boolean t2cIsServerBigTZTC(long j);

    native int t2cCreateState(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, int i3, byte[] bArr4, int i4, byte[] bArr5, int i5, byte[] bArr6, int i6, byte[] bArr7, int i7, boolean z, byte[] bArr8, int i8, int i9, byte[] bArr9, int i10, short s, int i11, byte[] bArr10, byte[] bArr11, byte[] bArr12, long[] jArr);

    native int t2cLogon(long j, byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, int i3, byte[] bArr4, int i4, byte[] bArr5, int i5, byte[] bArr6, int i6, byte[] bArr7, int i7, boolean z, byte[] bArr8, int i8, int i9, byte[] bArr9, int i10, int i11, byte[] bArr10, byte[] bArr11, byte[] bArr12, long[] jArr);

    private native int t2cLogoff(long j);

    private native int t2cCancel(long j);

    private native byte t2cGetAsmVolProperty(long j);

    private native byte t2cGetInstanceType(long j);

    private native int t2cCreateStatement(long j, long j2, byte[] bArr, int i, OracleStatement oracleStatement, boolean z, int i2);

    private native int t2cSetAutoCommit(long j, boolean z);

    private native int t2cCommit(long j, int i);

    private native int t2cRollback(long j);

    private native int t2cPingDatabase(long j);

    private native byte[] t2cGetProductionVersion(long j, long[] jArr);

    private native int t2cGetVersionNumber(long j);

    private native int t2cGetDefaultStreamChunkSize(long j);

    native int t2cGetFormOfUse(long j, OracleTypeCLOB oracleTypeCLOB, byte[] bArr, int i, int i2);

    native long t2cGetTDO(long j, byte[] bArr, int i, int[] iArr);

    native int t2cCreateConnPool(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, int i3, short s, int i4, int i5, int i6, int i7, int i8, int i9, int i10);

    native int t2cConnPoolLogon(long j, byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, int i3, byte[] bArr4, int i4, byte[] bArr5, int i5, int i6, int i7, int i8, String[] strArr, byte[] bArr6, int i9, byte[] bArr7, int i10, byte[] bArr8, int i11, byte[] bArr9, int i12, byte[] bArr10, int i13, byte[] bArr11, byte[] bArr12, long[] jArr);

    native int t2cGetConnPoolInfo(long j, Properties properties);

    native int t2cSetConnPoolInfo(long j, int i, int i2, int i3, int i4, int i5, int i6);

    native int t2cPasswordChange(long j, byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, int i3);

    protected native byte[] t2cGetConnectionId(long j);

    native int t2cGetNetConnectionId(long j, byte[] bArr, int i, int[] iArr);

    native int t2cGetHandles(long j, long[] jArr);

    native int t2cUseConnection(long j, long j2, long j3, long j4, byte[] bArr, long[] jArr);

    native boolean t2cPlatformIsLittleEndian(long j);

    native int t2cRegisterTAFCallback(long j);

    native int t2cGetHeapAllocSize(long j);

    native int t2cGetOciEnvHeapAllocSize(long j);

    native int t2cDoProxySession(long j, int i, byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, int i4, byte[] bArr4, int i5, byte[] bArr5, int i6, int i7, byte[][] bArr6);

    native int t2cCloseProxySession(long j);

    static native int t2cDescribeTable(long j, byte[] bArr, int i, short[] sArr, byte[] bArr2, int i2, int i3, int i4, int i5);

    native int t2cSetApplicationContext(long j, String str, String str2, String str3);

    native int t2cClearAllApplicationContext(long j, String str);

    native int t2cStartupDatabase(long j, int i, String str);

    native int t2cShutdownDatabase(long j, int i);

    static native void t2cSetSessionTimeZone(String str);

    native int t2cCloseDrcpConnection(long j, byte[] bArr, int i, int i2);

    native int t2cOpenDrcpConnection(long j, byte[] bArr, int i, boolean[] zArr, boolean z);

    native int t2cSetCachedServerVersion(long j, short s);

    native int t2cGetImplicitResultSetStatement(long j, long j2, OracleStatement oracleStatement);

    native int t2cGetSchemaName(long j, byte[] bArr);

    native int t2cGetAccessBanner(long j, T2CBanner t2CBanner);

    native int t2cGetAuditBanner(long j, T2CBanner t2CBanner);

    native int t2cGetMajorVersionNumber(long j);

    native int t2cGetMinorVersionNumber(long j);

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean isXAThroughSessionlessTransactions() {
        return super.isXAThroughSessionlessTransactions();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void doPreCallSuspend(int i) throws SQLException {
        super.doPreCallSuspend(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void doStartOrResume(byte[] bArr, int i, int i2, int i3) throws SQLException {
        super.doStartOrResume(bArr, i, i2, i3);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ byte[] getTransactionId() throws SQLException {
        return super.getTransactionId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void suspendTransaction() throws SQLException {
        super.suspendTransaction();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void suspendTransactionImmediately() throws SQLException {
        super.suspendTransactionImmediately();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void resumeTransaction(byte[] bArr, int i) throws SQLException {
        super.resumeTransaction(bArr, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void resumeTransaction(byte[] bArr) throws SQLException {
        super.resumeTransaction(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void startTransaction(byte[] bArr, int i) throws SQLException {
        super.startTransaction(bArr, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void startTransaction(byte[] bArr) throws SQLException {
        super.startTransaction(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ byte[] startTransaction(int i) throws SQLException {
        return super.startTransaction(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ byte[] startTransaction() throws SQLException {
        return super.startTransaction();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ TraceEventListener getTraceEventListener() {
        return super.getTraceEventListener();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void enableDiagnoseFirstFailureDump(boolean z) throws SQLException {
        super.enableDiagnoseFirstFailureDump(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void dumpLog() throws SQLException {
        super.dumpLog();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void enableLogging() throws SQLException {
        super.enableLogging();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void disableLogging() throws SQLException {
        super.disableLogging();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.diagnostics.Diagnosable
    public /* bridge */ /* synthetic */ Diagnosable getDiagnosable() {
        return super.getDiagnosable();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void addFeature(OracleConnection.ClientFeature clientFeature) throws SQLException {
        super.addFeature(clientFeature);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ double getPercentageQueryExecutionOnDirectShard() {
        return super.getPercentageQueryExecutionOnDirectShard();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public /* bridge */ /* synthetic */ String enquoteIdentifier(String str, boolean z) throws SQLException {
        return super.enquoteIdentifier(str, z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public /* bridge */ /* synthetic */ String enquoteLiteral(String str) throws SQLException {
        return super.enquoteLiteral(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public /* bridge */ /* synthetic */ boolean isSimpleIdentifier(String str) throws SQLException {
        return super.isSimpleIdentifier(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Properties getJavaNetProperties() throws SQLException {
        return super.getJavaNetProperties();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getChecksumProviderName() throws SQLException {
        return super.getChecksumProviderName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getEncryptionProviderName() throws SQLException {
        return super.getEncryptionProviderName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean isValid(OracleConnection.ConnectionValidation connectionValidation, int i) throws SQLException {
        return super.isValid(connectionValidation, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ boolean isValid(int i) throws SQLException {
        return super.isValid(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setSafelyClosed(boolean z) throws SQLException {
        super.setSafelyClosed(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void flushRemoteDatabaseTTCCookieCache() {
        super.flushRemoteDatabaseTTCCookieCache();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isSafelyClosed() throws SQLException {
        return super.isSafelyClosed();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setDatabaseSessionState(oracle.jdbc.internal.DatabaseSessionState databaseSessionState) throws SQLException {
        super.setDatabaseSessionState(databaseSessionState);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.internal.DatabaseSessionState getDatabaseSessionState() throws SQLException {
        return super.getDatabaseSessionState();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean hasNoOpenHandles() throws SQLException {
        return super.hasNoOpenHandles();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ NetStat getNetworkStat() {
        return super.getNetworkStat();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getOutboundConnectTimeout() {
        return super.getOutboundConnectTimeout();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isNetworkCompressionEnabled() {
        return super.isNetworkCompressionEnabled();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ OracleConnection.DRCPState getDRCPState() throws SQLException {
        return super.getDRCPState();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setHAManager(HAManager hAManager) throws SQLException {
        super.setHAManager(hAManager);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ HAManager getHAManager() {
        return super.getHAManager();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setShardingKey(OracleShardingKey oracleShardingKey) throws SQLException {
        super.setShardingKey(oracleShardingKey);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean setShardingKeyIfValid(OracleShardingKey oracleShardingKey, int i) throws SQLException {
        return super.setShardingKeyIfValid(oracleShardingKey, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean setShardingKeyIfValid(OracleShardingKey oracleShardingKey, OracleShardingKey oracleShardingKey2, int i) throws SQLException {
        return super.setShardingKeyIfValid(oracleShardingKey, oracleShardingKey2, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setShardingKey(OracleShardingKey oracleShardingKey, OracleShardingKey oracleShardingKey2) throws SQLException {
        super.setShardingKey(oracleShardingKey, oracleShardingKey2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setChunkInfo(OracleShardingKey oracleShardingKey, OracleShardingKey oracleShardingKey2, String str) throws SQLException {
        super.setChunkInfo(oracleShardingKey, oracleShardingKey2, str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public /* bridge */ /* synthetic */ boolean isServerBigSCN() throws SQLException {
        return super.isServerBigSCN();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public /* bridge */ /* synthetic */ void removeFromTemporaryLobs(OracleLargeObject oracleLargeObject) {
        super.removeFromTemporaryLobs(oracleLargeObject);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int freeTemporaryBlobsAndClobs() throws SQLException {
        return super.freeTemporaryBlobsAndClobs();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void removeBfile(oracle.jdbc.internal.OracleBfile oracleBfile) throws SQLException {
        super.removeBfile(oracleBfile);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void addBfile(oracle.jdbc.internal.OracleBfile oracleBfile) throws SQLException {
        super.addBfile(oracleBfile);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void removeLargeObject(OracleLargeObject oracleLargeObject) throws SQLException {
        super.removeLargeObject(oracleLargeObject);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void addLargeObject(OracleLargeObject oracleLargeObject) throws SQLException {
        super.addLargeObject(oracleLargeObject);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void sendRequestFlags() throws SQLException {
        super.sendRequestFlags();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void beginRequest(boolean z, boolean z2) throws SQLException {
        super.beginRequest(z, z2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void clearDrcpTagName() throws SQLException {
        super.clearDrcpTagName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isLifecycleOpen() throws SQLException {
        return super.isLifecycleOpen();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Blind
    public /* bridge */ /* synthetic */ String getServerSessionInfo(String str) throws SQLException {
        return super.getServerSessionInfo(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    @Blind(PropertiesBlinder.class)
    public /* bridge */ /* synthetic */ Properties getSessionInfoInternal() throws SQLException {
        return super.getSessionInfoInternal();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getVarTypeMaxLenCompat() throws SQLException {
        return super.getVarTypeMaxLenCompat();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setChecksumMode(OracleConnection.ChecksumMode checksumMode) throws SQLException {
        super.setChecksumMode(checksumMode);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ byte getNegotiatedTTCVersion() throws SQLException {
        return super.getNegotiatedTTCVersion();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getNegotiatedSDU() throws SQLException {
        return super.getNegotiatedSDU();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.internal.ResultSetCache getResultSetCache() throws SQLException {
        return super.getResultSetCache();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ String getExecutingRPCSQL() {
        return super.getExecutingRPCSQL();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ short getExecutingRPCFunctionCode() {
        return super.getExecutingRPCFunctionCode();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void detachServerConnection(String str) throws SQLException {
        super.detachServerConnection(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getDRCPPLSQLCallbackName() throws SQLException {
        return super.getDRCPPLSQLCallbackName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getDRCPReturnTag() throws SQLException {
        return super.getDRCPReturnTag();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean isDRCPMultitagEnabled() throws SQLException {
        return super.isDRCPMultitagEnabled();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean isDRCPEnabled() throws SQLException {
        return super.isDRCPEnabled();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean attachServerConnection() throws SQLException {
        return super.attachServerConnection();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper
    public /* bridge */ /* synthetic */ void setSchema(String str) throws SQLException {
        super.setSchema(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper
    public /* bridge */ /* synthetic */ int getNetworkTimeout() throws SQLException {
        return super.getNetworkTimeout();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper
    public /* bridge */ /* synthetic */ String getSchema() throws SQLException {
        return super.getSchema();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper
    public /* bridge */ /* synthetic */ void abort(Executor executor) throws SQLException {
        super.abort(executor);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ byte[] getDerivedKeyInternal(byte[] bArr, int i) throws SQLException, InvalidKeySpecException, NoSuchAlgorithmException {
        return super.getDerivedKeyInternal(bArr, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setLastReplayContext(oracle.jdbc.internal.ReplayContext replayContext) throws SQLException {
        super.setLastReplayContext(replayContext);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.internal.ReplayContext getLastReplayContext() throws SQLException {
        return super.getLastReplayContext();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.internal.ReplayContext[] getReplayContext() throws SQLException {
        return super.getReplayContext();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getEOC() throws SQLException {
        return super.getEOC();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void registerEndReplayCallback(OracleConnection.EndReplayCallback endReplayCallback) throws SQLException {
        super.registerEndReplayCallback(endReplayCallback);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setReplayingMode(boolean z) throws SQLException {
        super.setReplayingMode(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setReplayContext(oracle.jdbc.internal.ReplayContext[] replayContextArr) throws SQLException {
        super.setReplayContext(replayContextArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void endNonRequestCalls() throws SQLException {
        super.endNonRequestCalls();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void beginNonRequestCalls() throws SQLException {
        super.beginNonRequestCalls();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setReplayOperations(EnumSet enumSet) throws SQLException {
        super.setReplayOperations(enumSet);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isConnectionSocketKeepAlive() throws SocketException, SQLException {
        return super.isConnectionSocketKeepAlive();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean inLocalTransaction() throws SQLException {
        return super.inLocalTransaction();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ EnumSet getTransactionState() throws SQLException {
        return super.getTransactionState();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ long getCurrentSCN() throws SQLException {
        return super.getCurrentSCN();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isLobStreamPosStandardCompliant() throws SQLException {
        return super.isLobStreamPosStandardCompliant();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isDataInLocatorEnabled() throws SQLException {
        return super.isDataInLocatorEnabled();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ TIMEZONETAB getTIMEZONETAB() throws SQLException {
        return super.getTIMEZONETAB();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getTimezoneVersionNumber() throws SQLException {
        return super.getTimezoneVersionNumber();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TimeZone getDefaultTimeZone() throws SQLException {
        return super.getDefaultTimeZone();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setDefaultTimeZone(TimeZone timeZone) throws SQLException {
        super.setDefaultTimeZone(timeZone);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setUsable(boolean z) {
        super.setUsable(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isUsable(boolean z) {
        return super.isUsable(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean isUsable() {
        return super.isUsable();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TypeDescriptor[] getTypeDescriptorsFromList(String[][] strArr) throws SQLException {
        return super.getTypeDescriptorsFromList(strArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TypeDescriptor[] getTypeDescriptorsFromListInCurrentSchema(String[] strArr) throws SQLException {
        return super.getTypeDescriptorsFromListInCurrentSchema(strArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TypeDescriptor[] getAllTypeDescriptorsInCurrentSchema() throws SQLException {
        return super.getAllTypeDescriptorsInCurrentSchema();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.LogicalTransactionId getLogicalTransactionId() throws SQLException {
        return super.getLogicalTransactionId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void removeLogicalTransactionIdEventListener(LogicalTransactionIdEventListener logicalTransactionIdEventListener) throws SQLException {
        super.removeLogicalTransactionIdEventListener(logicalTransactionIdEventListener);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void addLogicalTransactionIdEventListener(LogicalTransactionIdEventListener logicalTransactionIdEventListener, Executor executor) throws SQLException {
        super.addLogicalTransactionIdEventListener(logicalTransactionIdEventListener, executor);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void addLogicalTransactionIdEventListener(LogicalTransactionIdEventListener logicalTransactionIdEventListener) throws SQLException {
        super.addLogicalTransactionIdEventListener(logicalTransactionIdEventListener);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setPDBChangeEventListener(PDBChangeEventListener pDBChangeEventListener) throws SQLException {
        super.setPDBChangeEventListener(pDBChangeEventListener);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setPDBChangeEventListener(PDBChangeEventListener pDBChangeEventListener, Executor executor) throws SQLException {
        super.setPDBChangeEventListener(pDBChangeEventListener, executor);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void removeAllXSEventListener() throws SQLException {
        super.removeAllXSEventListener();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void removeXSEventListener(XSEventListener xSEventListener) throws SQLException {
        super.removeXSEventListener(xSEventListener);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void addXSEventListener(XSEventListener xSEventListener, Executor executor) throws SQLException {
        super.addXSEventListener(xSEventListener, executor);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void addXSEventListener(XSEventListener xSEventListener) throws SQLException {
        super.addXSEventListener(xSEventListener);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void unregisterDatabaseChangeNotification(long j, String str) throws SQLException {
        super.unregisterDatabaseChangeNotification(j, str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void unregisterDatabaseChangeNotification(int i, String str, int i2) throws SQLException {
        super.unregisterDatabaseChangeNotification(i, str, i2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void unregisterDatabaseChangeNotification(int i) throws SQLException {
        super.unregisterDatabaseChangeNotification(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void unregisterDatabaseChangeNotification(DatabaseChangeRegistration databaseChangeRegistration) throws SQLException {
        super.unregisterDatabaseChangeNotification(databaseChangeRegistration);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ DatabaseChangeRegistration getDatabaseChangeRegistration(int i) throws SQLException {
        return super.getDatabaseChangeRegistration(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Debug(level = Debug.Level.CONFIG)
    public /* bridge */ /* synthetic */ DatabaseChangeRegistration registerDatabaseChangeNotification(@Blind(PropertiesBlinder.class) Properties properties) throws SQLException {
        try {
            debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T2CConnection", "registerDatabaseChangeNotification", "entering args ({0})", (String) null, (String) null, new PropertiesBlinder().blind((PropertiesBlinder) properties));
            DatabaseChangeRegistration databaseChangeRegistrationRegisterDatabaseChangeNotification = super.registerDatabaseChangeNotification(properties);
            debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T2CConnection", "registerDatabaseChangeNotification", "returning {0}", (String) null, (String) null, databaseChangeRegistrationRegisterDatabaseChangeNotification);
            return databaseChangeRegistrationRegisterDatabaseChangeNotification;
        } catch (Throwable th) {
            debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.T2CConnection", "registerDatabaseChangeNotification", "throwing", (String) null, (String) th, new Object[0]);
            throw th;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void unregisterAQNotification(AQNotificationRegistration aQNotificationRegistration) throws SQLException {
        super.unregisterAQNotification(aQNotificationRegistration);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ AQNotificationRegistration[] registerAQNotification(String[] strArr, Properties[] propertiesArr, @Blind(PropertiesBlinder.class) Properties properties) throws SQLException {
        return super.registerAQNotification(strArr, propertiesArr, properties);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void ackJMSNotification(ArrayList arrayList, byte[][] bArr, JMSNotificationRegistration.Directive directive) throws SQLException {
        super.ackJMSNotification((ArrayList<JMSNotificationRegistration>) arrayList, bArr, directive);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void ackJMSNotification(JMSNotificationRegistration jMSNotificationRegistration, byte[] bArr, JMSNotificationRegistration.Directive directive) throws SQLException {
        super.ackJMSNotification(jMSNotificationRegistration, bArr, directive);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void stopJMSNotification(JMSNotificationRegistration jMSNotificationRegistration) throws SQLException {
        super.stopJMSNotification(jMSNotificationRegistration);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void startJMSNotification(JMSNotificationRegistration jMSNotificationRegistration) throws SQLException {
        super.startJMSNotification(jMSNotificationRegistration);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void unregisterJMSNotification(JMSNotificationRegistration jMSNotificationRegistration) throws SQLException {
        super.unregisterJMSNotification(jMSNotificationRegistration);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Map registerJMSNotification(String[] strArr, Map map) throws SQLException {
        return super.registerJMSNotification(strArr, map);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Map registerJMSNotification(String[] strArr, Map map, String str) throws SQLException {
        return super.registerJMSNotification(strArr, map, str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean getMapDateToTimestamp() {
        return super.getMapDateToTimestamp();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    @Deprecated
    public /* bridge */ /* synthetic */ boolean isV8Compatible() throws SQLException {
        return super.isV8Compatible();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public /* bridge */ /* synthetic */ Integer doCommitRollbackSaga(String str, byte[] bArr, String str2, int i, int i2, int i3, String str3) throws SQLException {
        return super.doCommitRollbackSaga(str, bArr, str2, i, i2, i3, str3);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Integer commitRollbackSaga(String str, byte[] bArr, String str2, int i, int i2, int i3, String str3) throws SQLException {
        return super.commitRollbackSaga(str, bArr, str2, i, i2, i3, str3);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public /* bridge */ /* synthetic */ Integer doJoinSaga(String str, byte[] bArr, String str2, String str3, int i, int i2, int i3, int i4, int i5, String str4) throws SQLException {
        return super.doJoinSaga(str, bArr, str2, str3, i, i2, i3, i4, i5, str4);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Integer joinSaga(String str, byte[] bArr, String str2, String str3, int i, int i2, int i3, int i4, int i5, String str4) throws SQLException {
        return super.joinSaga(str, bArr, str2, str3, i, i2, i3, i4, i5, str4);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public /* bridge */ /* synthetic */ byte[] doBeginSaga(String str, int i, String str2, int i2, int i3, int i4, int i5, String str3) throws SQLException {
        return super.doBeginSaga(str, i, str2, i2, i3, i4, i5, str3);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ byte[] beginSaga(String str, int i, String str2, int i2, int i3, int i4, int i5, String str3) throws SQLException {
        return super.beginSaga(str, i, str2, i2, i3, i4, i5, str3);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ JMSMessage jmsDequeue(String str, JMSDequeueOptions jMSDequeueOptions, OutputStream outputStream) throws SQLException {
        return super.jmsDequeue(str, jMSDequeueOptions, outputStream);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ JMSMessage[] jmsDequeue(String str, JMSDequeueOptions jMSDequeueOptions, int i) throws SQLException {
        return super.jmsDequeue(str, jMSDequeueOptions, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ JMSMessage jmsDequeue(String str, JMSDequeueOptions jMSDequeueOptions) throws SQLException {
        return super.jmsDequeue(str, jMSDequeueOptions);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ JMSMessage jmsDequeue(String str, JMSDequeueOptions jMSDequeueOptions, String str2) throws SQLException {
        return super.jmsDequeue(str, jMSDequeueOptions, str2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void jmsEnqueue(String str, JMSEnqueueOptions jMSEnqueueOptions, JMSMessage[] jMSMessageArr, AQMessageProperties[] aQMessagePropertiesArr) throws SQLException {
        super.jmsEnqueue(str, jMSEnqueueOptions, jMSMessageArr, aQMessagePropertiesArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void jmsEnqueue(String str, JMSEnqueueOptions jMSEnqueueOptions, JMSMessage jMSMessage, AQMessageProperties aQMessageProperties) throws SQLException {
        super.jmsEnqueue(str, jMSEnqueueOptions, jMSMessage, aQMessageProperties);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ AQMessage[] dequeue(String str, AQDequeueOptions aQDequeueOptions, byte[] bArr, int i, int i2) throws SQLException {
        return super.dequeue(str, aQDequeueOptions, bArr, i, i2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ AQMessage[] dequeue(String str, AQDequeueOptions aQDequeueOptions, String str2, int i) throws SQLException {
        return super.dequeue(str, aQDequeueOptions, str2, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ int enqueue(String str, AQEnqueueOptions aQEnqueueOptions, AQMessage[] aQMessageArr) throws SQLException {
        return super.enqueue(str, aQEnqueueOptions, aQMessageArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ AQMessage dequeue(String str, AQDequeueOptions aQDequeueOptions, String str2) throws SQLException {
        return super.dequeue(str, aQDequeueOptions, str2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ AQMessage dequeue(String str, AQDequeueOptions aQDequeueOptions, byte[] bArr, int i) throws SQLException {
        return super.dequeue(str, aQDequeueOptions, bArr, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ AQMessage dequeue(String str, AQDequeueOptions aQDequeueOptions, byte[] bArr) throws SQLException {
        return super.dequeue(str, aQDequeueOptions, bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void enqueue(String str, AQEnqueueOptions aQEnqueueOptions, AQMessage aQMessage) throws SQLException {
        super.enqueue(str, aQEnqueueOptions, aQMessage);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void doXSSessionAttachOp(int i, byte[] bArr, XSSecureId xSSecureId, byte[] bArr2, XSPrincipal xSPrincipal, String[] strArr, String[] strArr2, String[] strArr3, XSNamespace[] xSNamespaceArr, XSNamespace[] xSNamespaceArr2, XSNamespace[] xSNamespaceArr3, TIMESTAMPTZ timestamptz, TIMESTAMPTZ timestamptz2, int i2, long j, XSKeyval xSKeyval, int[] iArr) throws SQLException {
        super.doXSSessionAttachOp(i, bArr, xSSecureId, bArr2, xSPrincipal, strArr, strArr2, strArr3, xSNamespaceArr, xSNamespaceArr2, xSNamespaceArr3, timestamptz, timestamptz2, i2, j, xSKeyval, iArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void doXSSessionChangeOp(OracleConnection.XSSessionSetOperationCode xSSessionSetOperationCode, byte[] bArr, XSSecureId xSSecureId, XSSessionParameters xSSessionParameters) throws SQLException {
        super.doXSSessionChangeOp(xSSessionSetOperationCode, bArr, xSSecureId, xSSessionParameters);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void doXSSessionDetachOp(int i, byte[] bArr, XSSecureId xSSecureId, boolean z) throws SQLException {
        super.doXSSessionDetachOp(i, bArr, xSSecureId, z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void doXSSessionDestroyOp(byte[] bArr, XSSecureId xSSecureId, byte[] bArr2) throws SQLException {
        super.doXSSessionDestroyOp(bArr, xSSecureId, bArr2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ byte[] doXSSessionCreateOp(OracleConnection.XSSessionOperationCode xSSessionOperationCode, XSSecureId xSSecureId, byte[] bArr, XSPrincipal xSPrincipal, String str, XSNamespace[] xSNamespaceArr, OracleConnection.XSSessionModeFlag xSSessionModeFlag, XSKeyval xSKeyval) throws SQLException {
        return super.doXSSessionCreateOp(xSSessionOperationCode, xSSecureId, bArr, xSPrincipal, str, xSNamespaceArr, xSSessionModeFlag, xSKeyval);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void doXSNamespaceOp(OracleConnection.XSOperationCode xSOperationCode, byte[] bArr, XSNamespace[] xSNamespaceArr, XSSecureId xSSecureId) throws SQLException {
        super.doXSNamespaceOp(xSOperationCode, bArr, xSNamespaceArr, xSSecureId);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void doXSNamespaceOp(OracleConnection.XSOperationCode xSOperationCode, byte[] bArr, XSNamespace[] xSNamespaceArr, XSNamespace[][] xSNamespaceArr2, XSSecureId xSSecureId) throws SQLException {
        super.doXSNamespaceOp(xSOperationCode, bArr, xSNamespaceArr, xSNamespaceArr2, xSSecureId);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void executeLightweightSessionPiggyback(int i, byte[] bArr, KeywordValueLong[] keywordValueLongArr, int i2) throws SQLException {
        super.executeLightweightSessionPiggyback(i, bArr, keywordValueLongArr, i2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ byte[] createLightweightSession(String str, KeywordValueLong[] keywordValueLongArr, int i, KeywordValueLong[][] keywordValueLongArr2, int[] iArr) throws SQLException {
        return super.createLightweightSession(str, keywordValueLongArr, i, keywordValueLongArr2, iArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void clearAllApplicationContext(String str) throws SQLException {
        super.clearAllApplicationContext(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setApplicationContext(String str, String str2, String str3) throws SQLException {
        super.setApplicationContext(str, str2, str3);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    @Blind(PropertiesBlinder.class)
    public /* bridge */ /* synthetic */ Properties getClientInfoInternal() throws SQLException {
        return super.getClientInfoInternal();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    @Blind(PropertiesBlinder.class)
    public /* bridge */ /* synthetic */ Properties getClientInfo() throws SQLException {
        return super.getClientInfo();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ String getClientInfo(String str) throws SQLException {
        return super.getClientInfo(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void setClientInfo(@Blind(PropertiesBlinder.class) Properties properties) throws SQLException {
        super.setClientInfo(properties);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void setClientInfo(String str, String str2) throws SQLException {
        super.setClientInfo(str, str2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ XAResource getXAResource() throws SQLException {
        return super.getXAResource();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setPlsqlWarnings(String str) throws SQLException {
        super.setPlsqlWarnings(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ SQLXML createSQLXML() throws SQLException {
        return super.createSQLXML();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ NClob createNClob() throws SQLException {
        return super.createNClob();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ Clob createClob() throws SQLException {
        return super.createClob();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ Blob createBlob() throws SQLException {
        return super.createBlob();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPLTZ createTIMESTAMPLTZ(DATE date, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPLTZ(date, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPLTZ createTIMESTAMPLTZ(String str, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPLTZ(str, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPLTZ createTIMESTAMPLTZ(Timestamp timestamp, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPLTZ(timestamp, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPLTZ createTIMESTAMPLTZ(Time time, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPLTZ(time, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPLTZ createTIMESTAMPLTZ(Date date, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPLTZ(date, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(DATE date) throws SQLException {
        return super.createTIMESTAMPTZ(date);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(String str, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPTZ(str, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(String str) throws SQLException {
        return super.createTIMESTAMPTZ(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(Timestamp timestamp, ZoneId zoneId) throws SQLException {
        return super.createTIMESTAMPTZ(timestamp, zoneId);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(Timestamp timestamp, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPTZ(timestamp, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(Timestamp timestamp) throws SQLException {
        return super.createTIMESTAMPTZ(timestamp);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(Time time, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPTZ(time, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(Time time) throws SQLException {
        return super.createTIMESTAMPTZ(time);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(Date date, Calendar calendar) throws SQLException {
        return super.createTIMESTAMPTZ(date, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMPTZ createTIMESTAMPTZ(Date date) throws SQLException {
        return super.createTIMESTAMPTZ(date);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMP createTIMESTAMP(String str) throws SQLException {
        return super.createTIMESTAMP(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMP createTIMESTAMP(Timestamp timestamp, Calendar calendar) throws SQLException {
        return super.createTIMESTAMP(timestamp, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMP createTIMESTAMP(Timestamp timestamp) throws SQLException {
        return super.createTIMESTAMP(timestamp);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMP createTIMESTAMP(Time time) throws SQLException {
        return super.createTIMESTAMP(time);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMP createTIMESTAMP(DATE date) throws SQLException {
        return super.createTIMESTAMP(date);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ TIMESTAMP createTIMESTAMP(Date date) throws SQLException {
        return super.createTIMESTAMP(date);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ Struct createStruct(String str, Object[] objArr) throws SQLException {
        return super.createStruct(str, objArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ Array createArrayOf(String str, Object[] objArr) throws SQLException {
        return super.createArrayOf(str, objArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(String str, int i) throws SQLException {
        return super.createNUMBER(str, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(BigInteger bigInteger) throws SQLException {
        return super.createNUMBER(bigInteger);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(BigDecimal bigDecimal) throws SQLException {
        return super.createNUMBER(bigDecimal);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(double d) throws SQLException {
        return super.createNUMBER(d);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(float f) throws SQLException {
        return super.createNUMBER(f);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(long j) throws SQLException {
        return super.createNUMBER(j);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(int i) throws SQLException {
        return super.createNUMBER(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(short s) throws SQLException {
        return super.createNUMBER(s);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(byte b) throws SQLException {
        return super.createNUMBER(b);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ NUMBER createNUMBER(boolean z) throws SQLException {
        return super.createNUMBER(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ INTERVALYM createINTERVALYM(String str) throws SQLException {
        return super.createINTERVALYM(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ INTERVALDS createINTERVALDS(String str) throws SQLException {
        return super.createINTERVALDS(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ DATE createDATE(String str) throws SQLException {
        return super.createDATE(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ DATE createDATE(Timestamp timestamp, Calendar calendar) throws SQLException {
        return super.createDATE(timestamp, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ DATE createDATE(Time time, Calendar calendar) throws SQLException {
        return super.createDATE(time, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ DATE createDATE(Date date, Calendar calendar) throws SQLException {
        return super.createDATE(date, calendar);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ DATE createDATE(Timestamp timestamp) throws SQLException {
        return super.createDATE(timestamp);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ DATE createDATE(Time time) throws SQLException {
        return super.createDATE(time);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ DATE createDATE(Date date) throws SQLException {
        return super.createDATE(date);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ BINARY_FLOAT createBINARY_FLOAT(float f) throws SQLException {
        return super.createBINARY_FLOAT(f);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ BINARY_DOUBLE createBINARY_DOUBLE(double d) throws SQLException {
        return super.createBINARY_DOUBLE(d);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ Array createOracleArray(String str, Object obj) throws SQLException {
        return super.createOracleArray(str, obj);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ ARRAY createARRAY(String str, Object obj) throws SQLException {
        return super.createARRAY(str, obj);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ BFILE createBfile(byte[] bArr) throws SQLException {
        return super.createBfile(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ BLOB createBlobWithUnpickledBytes(byte[] bArr) throws SQLException {
        return super.createBlobWithUnpickledBytes(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ BLOB createBlob(byte[] bArr) throws SQLException {
        return super.createBlob(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ CLOB createClob(byte[] bArr, short s) throws SQLException {
        return super.createClob(bArr, s);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ CLOB createClobWithUnpickledBytes(byte[] bArr) throws SQLException {
        return super.createClobWithUnpickledBytes(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ CLOB createClob(byte[] bArr) throws SQLException {
        return super.createClob(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Connection getLogicalConnection(OraclePooledConnection oraclePooledConnection, boolean z) throws SQLException {
        return super.getLogicalConnection(oraclePooledConnection, z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.internal.OracleStatement refCursorCursorToStatement(int i) throws SQLException {
        return super.refCursorCursorToStatement(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int javaCharsToNCHARBytes(char[] cArr, int i, byte[] bArr) throws SQLException {
        return super.javaCharsToNCHARBytes(cArr, i, bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int javaCharsToCHARBytes(char[] cArr, int i, byte[] bArr) throws SQLException {
        return super.javaCharsToCHARBytes(cArr, i, bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isCharSetMultibyte(short s) {
        return super.isCharSetMultibyte(s);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getMaxNCharbyteSize() {
        return super.getMaxNCharbyteSize();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getMaxCharbyteSize() {
        return super.getMaxCharbyteSize();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getMaxCharSize() throws SQLException {
        return super.getMaxCharSize();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ CharacterSet getDbCharSet() {
        return super.getDbCharSet();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ short getDriverCharSet() {
        return super.getDriverCharSet();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean IsNCharFixedWith() {
        return super.IsNCharFixedWith();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int NCHARBytesToJavaChars(byte[] bArr, int i, char[] cArr) throws SQLException {
        return super.NCHARBytesToJavaChars(bArr, i, cArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int CHARBytesToJavaChars(byte[] bArr, int i, char[] cArr) throws SQLException {
        return super.CHARBytesToJavaChars(bArr, i, cArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ ResultSetMetaData newStructMetaData(StructDescriptor structDescriptor) throws SQLException {
        return super.newStructMetaData(structDescriptor);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ ResultSet newArrayLocatorResultSet(ArrayDescriptor arrayDescriptor, byte[] bArr, long j, int i, Map map) throws SQLException {
        return super.newArrayLocatorResultSet(arrayDescriptor, bArr, j, i, map);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ ResultSet newArrayDataResultSet(oracle.jdbc.internal.OracleArray oracleArray, long j, int i, Map map) throws SQLException {
        return super.newArrayDataResultSet(oracleArray, j, i, (Map<String, Class<?>>) map);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ ResultSet newArrayDataResultSet(Datum[] datumArr, long j, int i, Map map) throws SQLException {
        return super.newArrayDataResultSet(datumArr, j, i, (Map<String, Class<?>>) map);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ short getNCharSet() {
        return super.getNCharSet();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Datum toDatum(CustomDatum customDatum) throws SQLException {
        return super.toDatum(customDatum);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.internal.OracleConnection physicalConnectionWithin() {
        return super.physicalConnectionWithin();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.OracleConnection getWrapper() {
        return super.getWrapper();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.OracleConnection unwrap() {
        return super.unwrap();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setWrapper(oracle.jdbc.OracleConnection oracleConnection) {
        super.setWrapper(oracleConnection);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Class classForNameAndSchema(String str, String str2) throws ClassNotFoundException {
        return super.classForNameAndSchema(str, str2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean getJDBCStandardBehavior() {
        return super.getJDBCStandardBehavior();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getSessionTimeZoneOffset() throws SQLException {
        return super.getSessionTimeZoneOffset();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ ZoneId getSessionZoneId() {
        return super.getSessionZoneId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getSessionTimeZone() {
        return super.getSessionTimeZone();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ ZoneId getDatabaseZoneId() throws SQLException {
        return super.getDatabaseZoneId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ String getDatabaseTimeZone() throws SQLException {
        return super.getDatabaseTimeZone();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setSessionTimeZone(String str) throws SQLException {
        super.setSessionTimeZone(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.OracleConnection
    @Deprecated
    public /* bridge */ /* synthetic */ void setClientIdentifier(String str) throws SQLException {
        super.setClientIdentifier(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.OracleConnection
    @Deprecated
    public /* bridge */ /* synthetic */ void clearClientIdentifier(String str) throws SQLException {
        super.clearClientIdentifier(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setJavaObjectTypeMap(Map map) {
        super.setJavaObjectTypeMap(map);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Map getJavaObjectTypeMap() {
        return super.getJavaObjectTypeMap();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ int pingDatabase(int i) throws SQLException {
        return super.pingDatabase(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ int pingDatabase() throws SQLException {
        return super.pingDatabase();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getCreateStatementAsRefCursor() {
        return super.getCreateStatementAsRefCursor();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setCreateStatementAsRefCursor(boolean z) {
        super.setCreateStatementAsRefCursor(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getMinorVersionNumber() throws SQLException {
        return super.getMinorVersionNumber();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getMajorVersionNumber() throws SQLException {
        return super.getMajorVersionNumber();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ short getVersionNumber() throws SQLException {
        return super.getVersionNumber();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ String getDatabaseProductVersion() throws SQLException {
        return super.getDatabaseProductVersion();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ OracleConnection.BufferCacheStatistics getCharBufferCacheStatistics() {
        return super.getCharBufferCacheStatistics();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ OracleConnection.BufferCacheStatistics getByteBufferCacheStatistics() {
        return super.getByteBufferCacheStatistics();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.sql.ClobDBAccess
    public /* bridge */ /* synthetic */ char[] getCharBufferSync(int i) {
        return super.getCharBufferSync(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.sql.ClobDBAccess
    public /* bridge */ /* synthetic */ void cacheBufferSync(char[] cArr) {
        super.cacheBufferSync(cArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean isStatementCacheInitialized() {
        return super.isStatementCacheInitialized();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void purgeExplicitCache() throws SQLException {
        super.purgeExplicitCache();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void purgeImplicitCache() throws SQLException {
        super.purgeImplicitCache();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getExplicitCachingEnabled() throws SQLException {
        return super.getExplicitCachingEnabled();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setExplicitCachingEnabled(boolean z) throws SQLException {
        super.setExplicitCachingEnabled(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getImplicitCachingEnabled() throws SQLException {
        return super.getImplicitCachingEnabled();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setImplicitCachingEnabled(boolean z) throws SQLException {
        super.setImplicitCachingEnabled(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ int getStatementCacheSize() throws SQLException {
        return super.getStatementCacheSize();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setStatementCacheSize(int i) throws SQLException {
        super.setStatementCacheSize(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public /* bridge */ /* synthetic */ int getStmtCacheSize() {
        return super.getStmtCacheSize();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public /* bridge */ /* synthetic */ void setStmtCacheSize(int i, boolean z) throws SQLException {
        super.setStmtCacheSize(i, z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public /* bridge */ /* synthetic */ void setStmtCacheSize(int i) throws SQLException {
        super.setStmtCacheSize(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ String getURL() {
        return super.getURL();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ String getProtocolType() {
        return super.getProtocolType();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.ClientDataSupport
    public /* bridge */ /* synthetic */ Object removeClientData(Object obj) {
        return super.removeClientData(obj);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.ClientDataSupport
    public /* bridge */ /* synthetic */ Object setClientData(Object obj, Object obj2) {
        return super.setClientData(obj, obj2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.ClientDataSupport
    public /* bridge */ /* synthetic */ Object getClientData(Object obj) {
        return super.getClientData(obj);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean inSessionlessTxnMode() {
        return super.inSessionlessTxnMode();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getTxnMode() {
        return super.getTxnMode();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setTxnMode(int i) {
        super.setTxnMode(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void oracleReleaseSavepoint(oracle.jdbc.OracleSavepoint oracleSavepoint) throws SQLException {
        super.oracleReleaseSavepoint(oracleSavepoint);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void oracleRollback(oracle.jdbc.OracleSavepoint oracleSavepoint) throws SQLException {
        super.oracleRollback(oracleSavepoint);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.OracleSavepoint oracleSetSavepoint(String str) throws SQLException {
        return super.oracleSetSavepoint(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.OracleSavepoint oracleSetSavepoint() throws SQLException {
        return super.oracleSetSavepoint();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ Statement createStatement(int i, int i2, int i3) throws SQLException {
        return super.createStatement(i, i2, i3);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void releaseSavepoint(Savepoint savepoint) throws SQLException {
        super.releaseSavepoint(savepoint);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void rollback(Savepoint savepoint) throws SQLException {
        super.rollback(savepoint);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ Savepoint setSavepoint(String str) throws SQLException {
        return super.setSavepoint(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ Savepoint setSavepoint() throws SQLException {
        return super.setSavepoint();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ int getHoldability() throws SQLException {
        return super.getHoldability();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void setHoldability(int i) throws SQLException {
        super.setHoldability(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean getBigEndian() throws SQLException {
        return super.getBigEndian();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setFDO(byte[] bArr) throws SQLException {
        super.setFDO(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ byte[] getFDO(boolean z) throws SQLException {
        return super.getFDO(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ String getDefaultSchemaNameForNamedTypes() throws SQLException {
        return super.getDefaultSchemaNameForNamedTypes();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getUserName() throws SQLException {
        return super.getUserName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getXAErrorFlag() {
        return super.getXAErrorFlag();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setXAErrorFlag(boolean z) {
        super.setXAErrorFlag(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getUsingXAFlag() {
        return super.getUsingXAFlag();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setUsingXAFlag(boolean z) {
        super.setUsingXAFlag(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void setTypeMap(Map map) throws SQLException {
        super.setTypeMap(map);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ Map getTypeMap() throws SQLException {
        return super.getTypeMap();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ short getStructAttrNCsId() throws SQLException {
        return super.getStructAttrNCsId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ short getStructAttrCsId() throws SQLException {
        return super.getStructAttrCsId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ short getDbCsId() throws SQLException {
        return super.getDbCsId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ short getJdbcCsId() throws SQLException {
        return super.getJdbcCsId();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Object getDescriptor(byte[] bArr) {
        return super.getDescriptor(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void putDescriptor(byte[] bArr, Object obj) throws SQLException {
        super.putDescriptor(bArr, obj);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Enumeration descriptorCacheKeys() {
        return super.descriptorCacheKeys();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int numberOfDescriptorCacheEntries() {
        return super.numberOfDescriptorCacheEntries();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void removeAllDescriptor() {
        super.removeAllDescriptor();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void removeDescriptor(byte[] bArr) {
        super.removeDescriptor(bArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void removeDescriptor(String str) {
        super.removeDescriptor(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ Object doGetDescriptor(String str) {
        return super.doGetDescriptor(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ Object getDescriptor(String str) {
        return super.getDescriptor(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void putDescriptor(String str, Object obj) throws SQLException {
        super.putDescriptor(str, obj);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ Object getJavaObject(String str) throws SQLException {
        return super.getJavaObject(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getSQLType(Object obj) throws SQLException {
        return super.getSQLType(obj);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void registerSQLType(String str, Class cls) throws SQLException {
        super.registerSQLType(str, (Class<?>) cls);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void registerSQLType(String str, String str2) throws SQLException {
        super.registerSQLType(str, str2);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void archive(int i, int i2, String str) throws SQLException {
        super.archive(i, i2, str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void shutdown(OracleConnection.DatabaseShutdownMode databaseShutdownMode) throws SQLException {
        super.shutdown(databaseShutdownMode);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void startup(OracleConnection.DatabaseStartupMode databaseStartupMode, String str) throws SQLException {
        super.startup(databaseStartupMode, str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void startup(OracleConnection.DatabaseStartupMode databaseStartupMode) throws SQLException {
        super.startup(databaseStartupMode);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void startup(String str, int i) throws SQLException {
        super.startup(str, i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ int getC2SNlsRatio() {
        return super.getC2SNlsRatio();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean getDefaultFixedString() {
        return super.getDefaultFixedString();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void setDefaultFixedString(boolean z) {
        super.setDefaultFixedString(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getRestrictGetTables() {
        return super.getRestrictGetTables();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setRestrictGetTables(boolean z) {
        super.setRestrictGetTables(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getIncludeSynonyms() {
        return super.getIncludeSynonyms();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setEndToEndMetrics(String[] strArr, short s) throws SQLException {
        super.setEndToEndMetrics(strArr, s);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ short getEndToEndECIDSequenceNumber() throws SQLException {
        return super.getEndToEndECIDSequenceNumber();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String[] getEndToEndMetrics() throws SQLException {
        return super.getEndToEndMetrics();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setIncludeSynonyms(boolean z) {
        super.setIncludeSynonyms(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getRemarksReporting() {
        return super.getRemarksReporting();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setRemarksReporting(boolean z) {
        super.setRemarksReporting(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ int getDefaultExecuteBatch() {
        return super.getDefaultExecuteBatch();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setDefaultExecuteBatch(int i) throws SQLException {
        super.setDefaultExecuteBatch(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean getUse1900AsYearForTime() {
        return super.getUse1900AsYearForTime();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean getTimestamptzInGmt() {
        return super.getTimestamptzInGmt();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ int getDefaultRowPrefetch() {
        return super.getDefaultRowPrefetch();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setDefaultRowPrefetch(int i) throws SQLException {
        super.setDefaultRowPrefetch(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void clearWarnings() throws SQLException {
        super.clearWarnings();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ SQLWarning getWarnings() throws SQLException {
        return super.getWarnings();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean getAutoClose() throws SQLException {
        return super.getAutoClose();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void setAutoClose(boolean z) throws SQLException {
        super.setAutoClose(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ int getTransactionIsolation() throws SQLException {
        return super.getTransactionIsolation();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void setTransactionIsolation(int i) throws SQLException {
        super.setTransactionIsolation(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ String getCatalog() throws SQLException {
        return super.getCatalog();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void setCatalog(String str) throws SQLException {
        super.setCatalog(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ boolean isReadOnly() throws SQLException {
        return super.isReadOnly();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void setReadOnly(boolean z) throws SQLException {
        super.setReadOnly(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ DatabaseMetaData getMetaData() throws SQLException {
        return super.getMetaData();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void openProxySession(int i, @Blind(PropertiesBlinder.class) Properties properties) throws SQLException {
        super.openProxySession(i, properties);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean isProxySession() {
        return super.isProxySession();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ PreparedStatement prepareDirectPath(String str, String str2, String[] strArr, String str3, @Blind(PropertiesBlinder.class) Properties properties) throws SQLException {
        return super.prepareDirectPath(str, str2, strArr, str3, properties);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ PreparedStatement prepareDirectPath(String str, String str2, String[] strArr, @Blind(PropertiesBlinder.class) Properties properties) throws SQLException {
        return super.prepareDirectPath(str, str2, strArr, properties);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ PreparedStatement prepareDirectPath(String str, String str2, String[] strArr, String str3) throws SQLException {
        return super.prepareDirectPath(str, str2, strArr, str3);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ PreparedStatement prepareDirectPath(String str, String str2, String[] strArr) throws SQLException {
        return super.prepareDirectPath(str, str2, strArr);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.driver.GeneratedPhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ Set getProviderAllowedProperties() {
        return super.getProviderAllowedProperties();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void abort() throws SQLException {
        super.abort();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void close(int i) throws SQLException {
        super.close(i);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void closeLogicalConnection() throws SQLException {
        super.closeLogicalConnection();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void cleanupAndClose() throws SQLException {
        super.cleanupAndClose();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void cleanupAndClose(boolean z) throws SQLException {
        super.cleanupAndClose(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ void closeInternal(boolean z) throws SQLException {
        super.closeInternal(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getAuthenticationAdaptorName() throws SQLException {
        return super.getAuthenticationAdaptorName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getEncryptionAlgorithmName() throws SQLException {
        return super.getEncryptionAlgorithmName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ String getDataIntegrityAlgorithmName() throws SQLException {
        return super.getDataIntegrityAlgorithmName();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection, java.lang.AutoCloseable
    public /* bridge */ /* synthetic */ void close() throws SQLException {
        super.close();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void rollback() throws SQLException {
        super.rollback();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void commit() throws SQLException {
        super.commit();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void commit(EnumSet enumSet) throws SQLException {
        super.commit((EnumSet<OracleConnection.CommitOption>) enumSet);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ void cancel() throws SQLException {
        super.cancel();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean getAutoCommitInternal() throws SQLException {
        return super.getAutoCommitInternal();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ boolean getAutoCommit() throws SQLException {
        return super.getAutoCommit();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ void setAutoCommit(boolean z) throws SQLException {
        super.setAutoCommit(z);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ String nativeSQL(String str) throws SQLException {
        return super.nativeSQL(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ CallableStatement prepareCallWithKey(String str) throws SQLException {
        return super.prepareCallWithKey(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public /* bridge */ /* synthetic */ PreparedStatement prepareStatementWithKey(String str) throws SQLException {
        return super.prepareStatementWithKey(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public /* bridge */ /* synthetic */ PreparedStatement prepareStatement(String str) throws SQLException {
        return super.prepareStatement(str);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public /* bridge */ /* synthetic */ boolean isLogicalConnection() {
        return super.isLogicalConnection();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ oracle.jdbc.internal.OracleConnection getPhysicalConnection() {
        return super.getPhysicalConnection();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public /* bridge */ /* synthetic */ Connection _getPC() {
        return super._getPC();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Blind(PropertiesBlinder.class)
    public /* bridge */ /* synthetic */ Properties getProperties() {
        return super.getProperties();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean serverSupportsExplicitBoundaryBit() throws SQLException {
        return super.serverSupportsExplicitBoundaryBit();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public /* bridge */ /* synthetic */ boolean serverSupportsRequestBoundaries() throws SQLException {
        return super.serverSupportsRequestBoundaries();
    }

    protected T2CConnection(String ur, @Blind(PropertiesBlinder.class) Properties info, OracleDriverExtension ext) throws SQLException {
        super(ur, info, ext);
        this.queryMetaData1 = null;
        this.queryMetaData2 = null;
        this.queryMetaData1Offset = 0;
        this.queryMetaData2Offset = 0;
        this.fatalErrorNumber = 0;
        this.fatalErrorMessage = null;
        this.queryMetaData1Size = 100;
        this.queryMetaData2Size = 800;
        this.logon_mode = 0;
        this.appCallback = null;
        this.appCallbackObject = null;
        allocQueryMetaDataBuffers();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    final void initializePassword(OpaqueString p) throws SQLException {
        this.password = p;
    }

    private void allocQueryMetaDataBuffers() {
        this.queryMetaData1Offset = 0;
        this.queryMetaData1 = new short[this.queryMetaData1Size * 16];
        this.queryMetaData2Offset = 0;
        this.queryMetaData2 = new byte[this.queryMetaData2Size];
        this.namedTypeAccessorByteLen = 0;
        this.refTypeAccessorByteLen = 0;
    }

    void reallocateQueryMetaData(int numColumns, int charSize) {
        this.queryMetaData1 = null;
        this.queryMetaData2 = null;
        this.queryMetaData1Size = Math.max(numColumns, this.queryMetaData1Size);
        this.queryMetaData2Size = Math.max(charSize, this.queryMetaData2Size);
        allocQueryMetaDataBuffers();
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void readConnectionProperties(String ul, @Blind(PropertiesBlinder.class) Properties info) throws SQLException {
        super.readConnectionProperties(ul, info);
        this.database = translateConnStr(this.database);
        if (null != this.targetInstanceName && !"".equals(this.targetInstanceName)) {
            this.database = createNamedInstanceUrl(this.database, this.targetInstanceName);
        }
        readOCIConnectionPoolProperties(info);
    }

    private String translateConnStr(String database) throws SQLException {
        if (database == null || database.equals("")) {
            return database;
        }
        if (database.indexOf(41) != -1) {
            return database;
        }
        String database2 = database.trim();
        if (database2.startsWith("//") || database2.matches("[[\\w-]\\.]*:[\\d]*/[[\\w\\$\\#]\\.]*(?i)(:[\\w]*)?(?-i)")) {
            String database3 = database2.replaceAll("#", "\\\\#");
            try {
                NameResolver nr = NameResolverFactory.getNameResolver(null, "", "");
                return nr.resolveName(database3);
            } catch (Exception e) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 67, database3).fillInStackTrace());
            }
        }
        return resolveSimple(database2);
    }

    private String resolveSimple(String database) throws SQLException {
        String host;
        String sid;
        String translate_database;
        int fColon = 0;
        String serverMode = null;
        boolean ipV6LiteralFormat = false;
        if (database.indexOf(91) != -1) {
            fColon = database.indexOf(93);
            if (fColon == -1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 67, database).fillInStackTrace());
            }
            ipV6LiteralFormat = true;
        }
        int fColon2 = database.indexOf(58, fColon);
        if (fColon2 == -1) {
            return database;
        }
        int sColon = database.indexOf(58, fColon2 + 1);
        if (sColon == -1) {
            return database;
        }
        int tColon = database.indexOf(58, sColon + 1);
        if (tColon != -1) {
            serverMode = database.substring(tColon);
        } else {
            tColon = sColon;
        }
        if (database.indexOf(58, tColon + 1) != -1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 67, database).fillInStackTrace());
        }
        if (ipV6LiteralFormat) {
            host = database.substring(1, fColon2 - 1);
        } else {
            host = database.substring(0, fColon2);
        }
        String port = database.substring(fColon2 + 1, sColon);
        if (serverMode == null) {
            sid = database.substring(sColon + 1, database.length());
        } else {
            sid = database.substring(sColon + 1, tColon);
        }
        if (serverMode == null) {
            translate_database = "(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=" + host + ")(PORT=" + port + "))(CONNECT_DATA=(SID=" + sid + ")))";
        } else {
            translate_database = "(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=" + host + ")(PORT=" + port + "))(CONNECT_DATA=(SID=" + sid + ")(SERVER=" + serverMode + ")))";
        }
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "resolveSimple", "translate_database={0}", (String) null, (String) null, (Object) translate_database);
        return translate_database;
    }

    private String createNamedInstanceUrl(String url, String targetInstanceName) {
        StringBuffer urlBuf = new StringBuffer(url);
        int keywordIndex = urlBuf.indexOf(CONNECT_DATA_KEYWORD);
        if (keywordIndex != -1) {
            int equalSignIndex = urlBuf.indexOf("=", keywordIndex + CONNECT_DATA_KEYWORD.length());
            if (equalSignIndex != -1) {
                urlBuf.insert(equalSignIndex + 1, "(INSTANCE_NAME=" + targetInstanceName + ")");
            } else {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "createNamedInstanceUrl", "equal sign not found", (String) null, (Throwable) null);
            }
        } else {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "createNamedInstanceUrl", "CONNECT_DATA keyword not found", (String) null, (Throwable) null);
        }
        String updatedUrl = urlBuf.toString();
        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "createNamedInstanceUrl", "updatedUrl={0}", (String) null, (String) null, (Object) updatedUrl);
        return updatedUrl;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void logon(AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        byte[] driverNameAttributeBytes;
        if (!isLibraryLoaded) {
            loadNativeLibrary();
        }
        this.tagMatched = new boolean[]{false};
        if (this.database == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 64).fillInStackTrace());
        }
        byte[] outByteArray = new byte[256];
        if (this.ociConnectionPoolIsPooling) {
            processOCIConnectionPooling();
            return;
        }
        long OCISvcCtxHandle = this.ociSvcCtxHandle;
        long OCIEnvHandle = this.ociEnvHandle;
        long OCIErrHandle = this.ociErrHandle;
        if (OCISvcCtxHandle != 0 && OCIEnvHandle != 0) {
            if (this.ociDriverCharset != null) {
                this.m_clientCharacterSet = new Integer(this.ociDriverCharset).shortValue();
                this.conversion = new DBConversion(this.m_clientCharacterSet, this.m_clientCharacterSet, this.m_clientCharacterSet);
                long[] nativeConnectionFlags = new long[11];
                nativeConnectionFlags[0] = this.defaultLobPrefetchSize;
                nativeConnectionFlags[1] = 0;
                nativeConnectionFlags[2] = 0;
                nativeConnectionFlags[3] = 0;
                nativeConnectionFlags[4] = 0;
                nativeConnectionFlags[5] = 0;
                nativeConnectionFlags[6] = 0;
                nativeConnectionFlags[7] = 0;
                nativeConnectionFlags[8] = 0;
                nativeConnectionFlags[9] = this.enableOCIFAN ? 1 : 0;
                nativeConnectionFlags[10] = this.ociExternalAuthentication ? 1 : 0;
                this.sqlWarning = checkError(t2cUseConnection(this.m_nativeState, OCIEnvHandle, OCISvcCtxHandle, OCIErrHandle, outByteArray, nativeConnectionFlags), this.sqlWarning);
                this.conversion = new DBConversion((short) (nativeConnectionFlags[2] & 65535), this.m_clientCharacterSet, (short) (nativeConnectionFlags[3] & 65535));
                this.byteAlign = (byte) (nativeConnectionFlags[4] & 255);
                this.timeZoneVersionNumber = (int) nativeConnectionFlags[5];
                if (nativeConnectionFlags[6] != 0) {
                    this.useOCIDefaultDefines = true;
                }
                this.tagMatched[0] = nativeConnectionFlags[7] != 0;
                this.varTypeMaxLenCompat = (int) nativeConnectionFlags[8];
                return;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 89).fillInStackTrace());
        }
        if (this.internalLogon == null) {
            this.logon_mode = 0;
        } else if (this.internalLogon.equalsIgnoreCase("SYSDBA")) {
            this.logon_mode = 2;
        } else if (this.internalLogon.equalsIgnoreCase("SYSOPER")) {
            this.logon_mode = 4;
        } else if (this.internalLogon.equalsIgnoreCase("SYSASM")) {
            this.logon_mode = 32768;
        } else if (this.internalLogon.equalsIgnoreCase("SYSBACKUP")) {
            this.logon_mode = 131072;
        } else if (this.internalLogon.equalsIgnoreCase("SYSDG")) {
            this.logon_mode = 262144;
        } else if (this.internalLogon.equalsIgnoreCase("SYSKM")) {
            this.logon_mode = LOGON_MODE_SYSKMT;
        }
        byte[] l_connectionClass = EMPTY_BYTES;
        byte[] l_drcpTagName = EMPTY_BYTES;
        String newPassword = this.newPasswordValue.get();
        byte[] newPasswordBytes = EMPTY_BYTES;
        byte[] editionNameBytes = EMPTY_BYTES;
        byte[] bArr = EMPTY_BYTES;
        if (this.nlsLangBackdoor) {
            this.m_clientCharacterSet = getDriverCharSetIdFromNLS_LANG();
        } else {
            this.m_clientCharacterSet = getClientCharSetId();
        }
        if (newPassword != null) {
            newPasswordBytes = DBConversion.stringToDriverCharBytes(newPassword, this.m_clientCharacterSet);
        }
        if (this.editionName != null) {
            editionNameBytes = DBConversion.stringToDriverCharBytes(this.editionName, this.m_clientCharacterSet);
        }
        if (this.driverNameAttribute == null) {
            String namedVersion = "jdbcoci : " + BuildInfo.getDriverVersion();
            driverNameAttributeBytes = DBConversion.stringToDriverCharBytes(namedVersion, this.m_clientCharacterSet);
        } else {
            driverNameAttributeBytes = DBConversion.stringToDriverCharBytes(this.driverNameAttribute, this.m_clientCharacterSet);
        }
        byte[] l_userName = this.userName == null ? EMPTY_BYTES : DBConversion.stringToDriverCharBytes(this.userName, this.m_clientCharacterSet);
        byte[] l_proxyClientName = this.proxyClientName == null ? EMPTY_BYTES : DBConversion.stringToDriverCharBytes(this.proxyClientName, this.m_clientCharacterSet);
        byte[] l_password = (this.password == null || this.password.isNull()) ? EMPTY_BYTES : DBConversion.stringToDriverCharBytes(this.password.get().trim(), this.m_clientCharacterSet);
        int purity = 0;
        if (this.drcpEnabled) {
            if (this.drcpConnectionClass != null && !this.drcpConnectionClass.isEmpty()) {
                l_connectionClass = DBConversion.stringToDriverCharBytes(this.drcpConnectionClass, this.m_clientCharacterSet);
            }
            if (this.drcpConnectionPurity != null) {
                if (this.drcpConnectionPurity.equals("NEW")) {
                    purity = 1;
                } else if (this.drcpConnectionPurity.equals(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_CONNECTION_PURITY_DEFAULT)) {
                    purity = 2;
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_INVALID_SESSION_PURITY).fillInStackTrace());
                }
            }
        }
        if (this.drcpTagName != null) {
            l_drcpTagName = DBConversion.stringToDriverCharBytes(this.drcpTagName, this.m_clientCharacterSet);
        }
        byte[] l_database = DBConversion.stringToDriverCharBytes(this.database, this.m_clientCharacterSet);
        String s = CharacterSetMetaData.getNLSLanguage(Locale.getDefault(Locale.Category.FORMAT));
        byte[] nlslanguage = s != null ? s.getBytes() : null;
        String s2 = CharacterSetMetaData.getNLSTerritory(Locale.getDefault(Locale.Category.FORMAT));
        byte[] nlsterritory = s2 != null ? s2.getBytes() : null;
        if (nlslanguage == null || nlsterritory == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 176).fillInStackTrace());
        }
        TimeZone tz = TimeZone.getDefault();
        String defaultTimeZone = tz.getID();
        if (!ZONEIDMAP.isValidRegion(defaultTimeZone) || !this.timezoneAsRegion) {
            int tzOffset = tz.getOffset(System.currentTimeMillis());
            int hr = tzOffset / 3600000;
            int mi = (tzOffset / 60000) % 60;
            defaultTimeZone = (hr < 0 ? "" + hr : "+" + hr) + (mi < 10 ? ":0" + mi : ":" + mi);
        }
        doSetSessionTimeZone(defaultTimeZone);
        this.sessionTimeZone = defaultTimeZone;
        this.conversion = new DBConversion(this.m_clientCharacterSet, this.m_clientCharacterSet, this.m_clientCharacterSet);
        long[] nativeConnectionFlags2 = new long[11];
        nativeConnectionFlags2[0] = this.defaultLobPrefetchSize;
        nativeConnectionFlags2[1] = this.prelimAuth ? 1 : 0;
        nativeConnectionFlags2[2] = 0;
        nativeConnectionFlags2[3] = 0;
        nativeConnectionFlags2[4] = 0;
        nativeConnectionFlags2[5] = 0;
        nativeConnectionFlags2[6] = 0;
        nativeConnectionFlags2[7] = 0;
        nativeConnectionFlags2[8] = 0;
        nativeConnectionFlags2[9] = this.enableOCIFAN ? 1 : 0;
        nativeConnectionFlags2[10] = this.ociExternalAuthentication ? 1 : 0;
        if (this.m_nativeState == 0) {
            this.sqlWarning = checkError(t2cCreateState(l_userName, l_userName.length, l_proxyClientName, l_proxyClientName.length, l_password, l_password.length, newPasswordBytes, newPasswordBytes.length, editionNameBytes, editionNameBytes.length, driverNameAttributeBytes, driverNameAttributeBytes.length, l_database, l_database.length, this.drcpEnabled, l_connectionClass, l_connectionClass.length, purity, l_drcpTagName, l_drcpTagName.length, this.m_clientCharacterSet, this.logon_mode, nlslanguage, nlsterritory, outByteArray, nativeConnectionFlags2), this.sqlWarning);
        } else {
            this.sqlWarning = checkError(t2cLogon(this.m_nativeState, l_userName, l_userName.length, l_proxyClientName, l_proxyClientName.length, l_password, l_password.length, newPasswordBytes, newPasswordBytes.length, editionNameBytes, editionNameBytes.length, driverNameAttributeBytes, driverNameAttributeBytes.length, l_database, l_database.length, this.drcpEnabled, l_connectionClass, l_connectionClass.length, purity, l_drcpTagName, l_drcpTagName.length, this.logon_mode, nlslanguage, nlsterritory, outByteArray, nativeConnectionFlags2), this.sqlWarning);
        }
        if (this.drcpEnabled) {
            this.drcpState = OracleConnection.DRCPState.ATTACHED_IMPLICIT;
        }
        this.conversion = new DBConversion((short) (nativeConnectionFlags2[2] & 65535), this.m_clientCharacterSet, (short) (nativeConnectionFlags2[3] & 65535));
        this.byteAlign = (byte) (nativeConnectionFlags2[4] & 255);
        this.timeZoneVersionNumber = (int) nativeConnectionFlags2[5];
        if (nativeConnectionFlags2[6] != 0) {
            this.useOCIDefaultDefines = true;
        }
        this.tagMatched[0] = nativeConnectionFlags2[7] != 0;
        this.varTypeMaxLenCompat = (int) nativeConnectionFlags2[8];
        int offset = 0;
        String instanceStartTime = null;
        while (true) {
            int outType = readShort(outByteArray, offset);
            if (outType != 0) {
                int offset2 = offset + 2;
                int outLength = readShort(outByteArray, offset2);
                offset = offset2 + 2;
                switch (outType) {
                    case 1:
                        this.databaseUniqueIdentifier = new String(outByteArray, offset, outLength);
                        offset += outLength;
                        break;
                    case 2:
                        instanceStartTime = new String(outByteArray, offset, outLength);
                        offset += outLength;
                        break;
                }
            } else {
                if (this.databaseUniqueIdentifier != null) {
                    String versionString = cachedVersionTable.get(this.databaseUniqueIdentifier + instanceStartTime);
                    if (versionString == null) {
                        cachedVersionTable.put(this.databaseUniqueIdentifier + instanceStartTime, String.valueOf((int) getVersionNumber()));
                        return;
                    } else {
                        this.versionNumber = Short.parseShort(versionString);
                        t2cSetCachedServerVersion(this.m_nativeState, this.versionNumber);
                        return;
                    }
                }
                return;
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    final CompletionStage<Void> logonAsync(AbstractConnectionBuilder<?, ?> builder) {
        return CompletionStageUtil.failedStage(new UnsupportedOperationException("Asynchronous connection is not supported by the Type 2 OCI driver"));
    }

    int readShort(byte[] buffer, int offset) {
        return ((buffer[offset] & 255) << 8) | (buffer[offset + 1] & 255);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void logoff() throws SQLException {
        try {
            if (getLifecycle() == 8 || getLifecycle() == 2) {
                checkError(t2cLogoff(this.m_nativeState));
            }
        } catch (NullPointerException e) {
        }
        this.m_nativeState = 0L;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public void open(OracleStatement stmt) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                byte[] l_sqlString = stmt.sqlObject.getSql(stmt.processEscapes, stmt.convertNcharLiterals).getBytes();
                checkError(t2cCreateStatement(this.m_nativeState, 0L, l_sqlString, l_sqlString.length, stmt, false, stmt.rowPrefetch));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    OracleStatement createImplicitResultSetStatement(OracleStatement parent) throws SQLException {
        T2CStatement stmt = new T2CStatement(this, OracleResultSet.ResultSetType.UNKNOWN);
        checkError(t2cGetImplicitResultSetStatement(this.m_nativeState, parent.c_state, stmt));
        stmt.needToParse = false;
        stmt.isOpen = true;
        stmt.processEscapes = false;
        stmt.sqlKind = OracleStatement.SqlKind.SELECT;
        stmt.prepareForNewResults(true, false, true);
        parent.addImplicitResultSetStmt(stmt);
        return stmt;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void cancelOperationOnServer(boolean isStatementCancel) throws SQLException {
        checkError(t2cCancel(this.m_nativeState));
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doAbort() throws SQLException {
        checkError(t2cAbort(this.m_nativeState));
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void beginRequest() throws SQLException {
        if (this.drcpEnabled) {
            return;
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            super.beginRequest();
            checkError(t2cBeginRequest(this.m_nativeState));
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void endRequest(boolean implicit) throws SQLException {
        if (this.drcpEnabled) {
            return;
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                checkError(t2cEndRequest(this.m_nativeState));
                super.endRequest(implicit);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void setDriverSpecificAutoCommit(boolean on) throws SQLException {
        checkError(t2cSetAutoCommit(this.m_nativeState, on));
        this.autocommit = on;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void doSetAutoCommit(boolean autoCommit) throws SQLException {
        if (this.autoCommitSpecCompliant && !getAutoCommit() && autoCommit) {
            commit();
        }
        setDriverSpecificAutoCommit(autoCommit);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void doCommit(int flags) throws SQLException {
        checkError(t2cCommit(this.m_nativeState, flags));
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void doRollback() throws SQLException {
        checkError(t2cRollback(this.m_nativeState));
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    int doPingDatabase() throws SQLException {
        assertLockHeldByCurrentThread();
        if (this.drcpEnabled && this.drcpState == OracleConnection.DRCPState.DETACHED) {
            attachServerConnection();
        }
        if (t2cPingDatabase(this.m_nativeState) == 0) {
            return 0;
        }
        return -1;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected String doGetDatabaseProductVersion() throws SQLException {
        long[] serverVersionRet = new long[1];
        return doGetDatabaseProductVersion(serverVersionRet);
    }

    private String doGetDatabaseProductVersion(long[] serverVersionRet) throws SQLException {
        byte[] l_version = t2cGetProductionVersion(this.m_nativeState, serverVersionRet);
        return this.conversion.CharBytesToString(l_version, l_version.length);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected short doGetVersionNumber() throws SQLException {
        short version_num = 0;
        try {
            long[] serverVersionRet = {0};
            doGetDatabaseProductVersion(serverVersionRet);
            version_num = (short) serverVersionRet[0];
        } catch (IllegalStateException e) {
        }
        return version_num;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected int doGetMajorVersionNumber() throws SQLException {
        requireOpenConnection();
        int version_num = 0;
        try {
            version_num = t2cGetMajorVersionNumber(this.m_nativeState);
        } catch (IllegalStateException e) {
        }
        return version_num;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected int doGetMinorVersionNumber() throws SQLException {
        requireOpenConnection();
        int version_num = 0;
        try {
            version_num = t2cGetMinorVersionNumber(this.m_nativeState);
        } catch (IllegalStateException e) {
        }
        return version_num;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public ClobDBAccess createClobDBAccess() {
        return this;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public BlobDBAccess createBlobDBAccess() {
        return this;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public BfileDBAccess createBfileDBAccess() {
        return this;
    }

    protected SQLWarning checkError(int errStatus) throws SQLException {
        return checkError(errStatus, null, 0L, null);
    }

    protected SQLWarning checkError(int errStatus, long cStateStatement, OracleSql sqlObject) throws SQLException {
        return checkError(errStatus, null, cStateStatement, sqlObject);
    }

    protected SQLWarning checkError(int errStatus, SQLWarning warning) throws SQLException {
        return checkError(errStatus, warning, 0L, null);
    }

    protected SQLWarning checkError(int errStatus, SQLWarning warning, long cStateStatement, OracleSql sqlObject) throws SQLException {
        switch (errStatus) {
            case -4:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_OUT_OF_MEMORY_ERROR).fillInStackTrace());
            case -1:
            case 1:
                T2CError l_error = new T2CError();
                if (getLifecycle() == 1 || getLifecycle() == 16) {
                    int l_status = t2cDescribeError(this.m_nativeState, l_error, l_error.m_errorMessage, cStateStatement);
                    String l_reason = null;
                    if (l_status != -1) {
                        int msgLength = 0;
                        while (msgLength < l_error.m_errorMessage.length && l_error.m_errorMessage[msgLength] != 0) {
                            msgLength++;
                        }
                        if (this.conversion == null) {
                            throw new Error("conversion == null");
                        }
                        if (l_error == null) {
                            throw new Error("l_error == null");
                        }
                        l_reason = this.conversion.CharBytesToString(l_error.m_errorMessage, msgLength, true);
                    }
                    DBConversion savedConversion = null;
                    switch (l_error.m_errorNumber) {
                        case -6:
                            l_error.m_errorNumber = NetException.DATABASE_CONNECTION_LOST;
                            break;
                        case 28:
                        case 600:
                        case 1012:
                        case 1041:
                            internalClose();
                            break;
                        case 902:
                        case 21700:
                            removeAllDescriptor();
                            break;
                        case NetException.DATABASE_CONNECTION_LOST /* 3113 */:
                        case 3114:
                            setUsable(false);
                            savedConversion = this.conversion;
                            close();
                            break;
                    }
                    if (l_status == -1) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Fetch error message failed!").fillInStackTrace());
                    }
                    if (errStatus == -1) {
                        SQLException sqlException = (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), l_reason, l_error.m_errorNumber).fillInStackTrace();
                        if (l_error.m_errorPosition >= 0) {
                            int msgLength2 = 0;
                            while (msgLength2 < l_error.m_errorMessage.length && l_error.m_errorMessage[msgLength2] != 0) {
                                msgLength2++;
                            }
                            if (this.conversion == null) {
                                this.conversion = savedConversion;
                            }
                            String errorMsg = this.conversion.CharBytesToString(l_error.m_errorMessage, msgLength2, true);
                            sqlException.initCause(new OracleDatabaseException(l_error.m_errorPosition, l_error.m_errorNumber, errorMsg, sqlObject.actualSql, sqlObject.originalSql));
                        }
                        throw sqlException;
                    }
                    warning = DatabaseError.addSqlWarning(warning, l_reason, l_error.m_errorNumber);
                    break;
                } else {
                    if (this.fatalErrorNumber != 0) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_OCI_FATAL_ERROR).fillInStackTrace());
                    }
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 8).fillInStackTrace());
                }
                break;
        }
        return warning;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    OracleStatement RefCursorBytesToStatement(byte[] bytes, OracleStatement parent) throws SQLException {
        T2CStatement stmt = new T2CStatement(this, OracleResultSet.ResultSetType.UNKNOWN);
        stmt.needToParse = false;
        stmt.serverCursor = true;
        stmt.isOpen = true;
        stmt.processEscapes = false;
        stmt.prepareForNewResults(true, false, true);
        if (this.useOCIDefaultDefines) {
            stmt.savedRowPrefetch = this.defaultRowPrefetch;
            stmt.rowPrefetch = 1;
        }
        stmt.sqlObject.initialize("select unknown as ref cursor from whatever");
        stmt.sqlKind = OracleStatement.SqlKind.SELECT;
        checkError(t2cCreateStatement(this.m_nativeState, parent.c_state, bytes, bytes.length, stmt, true, this.defaultRowPrefetch));
        parent.addChild(stmt);
        return stmt;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void getForm(OracleTypeADT otypeADT, OracleTypeCLOB otype, int attrIndex) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (otype != null) {
                String[] schema = new String[1];
                String[] type = new String[1];
                SQLName.parse(otypeADT.getFullName(), schema, type, true);
                String fullname = "\"" + schema[0] + "\".\"" + type[0] + "\"";
                byte[] utf8bytes = this.conversion.StringToCharBytes(fullname);
                int formOfUse = t2cGetFormOfUse(this.m_nativeState, otype, utf8bytes, utf8bytes.length, attrIndex);
                if (formOfUse < 0) {
                    checkError(formOfUse);
                }
                otype.setForm(formOfUse);
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public long getTdoCState(String schemaName, String typeName) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                String fullNameWithQuotes = "\"" + schemaName + "\".\"" + typeName + "\"";
                byte[] dbcsbytes = this.conversion.StringToCharBytes(fullNameWithQuotes);
                int[] err = new int[1];
                long tdoCState = t2cGetTDO(this.m_nativeState, dbcsbytes, dbcsbytes.length, err);
                if (tdoCState == 0) {
                    checkError(err[0]);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return tdoCState;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public long getTdoCState(String fullNameWithQuotes) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                byte[] dbcsbytes = this.conversion.StringToCharBytes(fullNameWithQuotes);
                int[] err = new int[1];
                long tdoCState = t2cGetTDO(this.m_nativeState, dbcsbytes, dbcsbytes.length, err);
                if (tdoCState == 0) {
                    checkError(err[0]);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return tdoCState;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    @Deprecated
    @Blind(PropertiesBlinder.class)
    public Properties getDBAccessProperties() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Properties oCIHandles = getOCIHandles();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return oCIHandles;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    @Blind(PropertiesBlinder.class)
    public Properties getOCIHandles() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (this.nativeInfo == null) {
                long[] handles = new long[3];
                checkError(t2cGetHandles(this.m_nativeState, handles));
                this.nativeInfo = new Properties();
                this.nativeInfo.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_OCI_ENV_HANDLE, String.valueOf(handles[0]));
                this.nativeInfo.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_OCI_SVC_CTX_HANDLE, String.valueOf(handles[1]));
                this.nativeInfo.put(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_OCI_ERR_HANDLE, String.valueOf(handles[2]));
                this.nativeInfo.put("ClientCharSet", String.valueOf((int) this.m_clientCharacterSet));
            }
            Properties properties = this.nativeInfo;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return properties;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Blind(PropertiesBlinder.class)
    public Properties getServerSessionInfo() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (this.sessionProperties == null) {
                this.sessionProperties = new Properties();
            }
            if (getVersionNumber() < 10200) {
                if (this.ociConnectionPoolLogonMode != "connection_pool") {
                    queryFCFProperties();
                }
            } else {
                checkError(t2cGetServerSessionInfo(this.m_nativeState, this.sessionProperties));
            }
            Properties properties = this.sessionProperties;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return properties;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public byte getInstanceProperty(OracleConnection.InstanceProperty whatProperty) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            byte ret = 0;
            if (whatProperty == OracleConnection.InstanceProperty.ASM_VOLUME_SUPPORTED) {
                ret = t2cGetAsmVolProperty(this.m_nativeState);
            } else if (whatProperty == OracleConnection.InstanceProperty.INSTANCE_TYPE) {
                ret = t2cGetInstanceType(this.m_nativeState);
            }
            return ret;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Blind(PropertiesBlinder.class)
    public Properties getConnectionPoolInfo() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            Properties info = new Properties();
            checkError(t2cGetConnPoolInfo(this.m_nativeState, info));
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return info;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    public void setConnectionPoolInfo(int cpool_min, int cpool_max, int cpool_incr, int cpool_timeout, int cpool_nowait, int cpool_dist_txn) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                checkError(t2cSetConnPoolInfo(this.m_nativeState, cpool_min, cpool_max, cpool_incr, cpool_timeout, cpool_nowait, cpool_dist_txn));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public void ociPasswordChange(String user, @Blind String oldPassword, @Blind String newPassword) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                requireOpenConnection();
                byte[] l_userName = user == null ? new byte[0] : DBConversion.stringToDriverCharBytes(user, this.m_clientCharacterSet);
                byte[] l_oldPwd = oldPassword == null ? new byte[0] : DBConversion.stringToDriverCharBytes(oldPassword, this.m_clientCharacterSet);
                byte[] l_newPwd = newPassword == null ? new byte[0] : DBConversion.stringToDriverCharBytes(newPassword, this.m_clientCharacterSet);
                this.sqlWarning = checkError(t2cPasswordChange(this.m_nativeState, l_userName, l_userName.length, l_oldPwd, l_oldPwd.length, l_newPwd, l_newPwd.length), this.sqlWarning);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private void processOCIConnectionPooling() throws SQLException {
        int proxy_type;
        requireOpenConnection();
        T2CConnection m_conn_pool = null;
        if (this.ociConnectionPoolLogonMode == "connection_pool") {
            if (this.nlsLangBackdoor) {
                this.m_clientCharacterSet = getDriverCharSetIdFromNLS_LANG();
            } else {
                this.m_clientCharacterSet = getClientCharSetId();
            }
        } else {
            m_conn_pool = (T2CConnection) this.ociConnectionPoolObject;
            this.m_clientCharacterSet = m_conn_pool.m_clientCharacterSet;
        }
        byte[] l_password = (this.password == null || this.password.isNull()) ? new byte[0] : DBConversion.stringToDriverCharBytes(this.password.get().trim(), this.m_clientCharacterSet);
        byte[] l_editionName = this.editionName == null ? new byte[0] : DBConversion.stringToDriverCharBytes(this.editionName, this.m_clientCharacterSet);
        byte[] l_driverNameAttribute = DBConversion.stringToDriverCharBytes(this.driverNameAttribute == null ? "jdbcoci : " + BuildInfo.getDriverVersion() : this.driverNameAttribute, this.m_clientCharacterSet);
        byte[] l_database = DBConversion.stringToDriverCharBytes(this.database, this.m_clientCharacterSet);
        byte[] nlslanguage = CharacterSetMetaData.getNLSLanguage(Locale.getDefault(Locale.Category.FORMAT)).getBytes();
        byte[] nlsterritory = CharacterSetMetaData.getNLSTerritory(Locale.getDefault(Locale.Category.FORMAT)).getBytes();
        if (nlslanguage == null || nlsterritory == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 176).fillInStackTrace());
        }
        long[] nativeConnectionFlags = new long[11];
        nativeConnectionFlags[0] = this.defaultLobPrefetchSize;
        nativeConnectionFlags[1] = 0;
        nativeConnectionFlags[2] = 0;
        nativeConnectionFlags[3] = 0;
        nativeConnectionFlags[4] = 0;
        nativeConnectionFlags[5] = 0;
        nativeConnectionFlags[6] = 0;
        nativeConnectionFlags[7] = 0;
        nativeConnectionFlags[8] = 0;
        nativeConnectionFlags[9] = 0;
        nativeConnectionFlags[10] = this.ociExternalAuthentication ? 1 : 0;
        if (this.ociConnectionPoolLogonMode == "connection_pool") {
            byte[] l_userName = this.userName == null ? new byte[0] : DBConversion.stringToDriverCharBytes(this.userName, this.m_clientCharacterSet);
            this.conversion = new DBConversion(this.m_clientCharacterSet, this.m_clientCharacterSet, this.m_clientCharacterSet);
            this.logon_mode = 5;
            if (getLifecycle() == 1) {
                int[] p = new int[6];
                OracleOCIConnectionPool.readPoolConfig(this.ociConnectionPoolMinLimit, this.ociConnectionPoolMaxLimit, this.ociConnectionPoolIncrement, this.ociConnectionPoolTimeout, this.ociConnectionPoolNoWait, this.ociConnectionPoolTransactionDistributed, p);
                this.sqlWarning = checkError(t2cCreateConnPool(l_userName, l_userName.length, l_password, l_password.length, l_database, l_database.length, this.m_clientCharacterSet, this.logon_mode, p[0], p[1], p[2], p[3], p[4], p[5]), this.sqlWarning);
                this.versionNumber = (short) 10000;
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 0, "Internal Error: ").fillInStackTrace());
            }
        } else if (this.ociConnectionPoolLogonMode == OracleOCIConnectionPool.CONNPOOL_CONNECTION) {
            this.logon_mode = 6;
            byte[] l_userName2 = this.userName == null ? new byte[0] : DBConversion.stringToDriverCharBytes(this.userName, this.m_clientCharacterSet);
            this.conversion = new DBConversion(this.m_clientCharacterSet, this.m_clientCharacterSet, this.m_clientCharacterSet);
            this.sqlWarning = checkError(t2cConnPoolLogon(m_conn_pool.m_nativeState, l_userName2, l_userName2.length, l_password, l_password.length, l_editionName, l_editionName.length, l_driverNameAttribute, l_driverNameAttribute.length, l_database, l_database.length, this.logon_mode, 0, 0, null, null, 0, null, 0, null, 0, null, 0, null, 0, nlslanguage, nlsterritory, nativeConnectionFlags), this.sqlWarning);
        } else if (this.ociConnectionPoolLogonMode == OracleOCIConnectionPool.CONNPOOL_ALIASED_CONNECTION) {
            this.logon_mode = 8;
            byte[] connection_id = (byte[]) this.ociConnectionPoolConnID;
            byte[] l_userName3 = this.userName == null ? new byte[0] : DBConversion.stringToDriverCharBytes(this.userName, this.m_clientCharacterSet);
            this.conversion = new DBConversion(this.m_clientCharacterSet, this.m_clientCharacterSet, this.m_clientCharacterSet);
            this.sqlWarning = checkError(t2cConnPoolLogon(m_conn_pool.m_nativeState, l_userName3, l_userName3.length, l_password, l_password.length, l_editionName, l_editionName.length, l_driverNameAttribute, l_driverNameAttribute.length, l_database, l_database.length, this.logon_mode, 0, 0, null, null, 0, null, 0, null, 0, null, 0, connection_id, connection_id == null ? 0 : connection_id.length, nlslanguage, nlsterritory, nativeConnectionFlags), this.sqlWarning);
        } else if (this.ociConnectionPoolLogonMode == OracleOCIConnectionPool.CONNPOOL_PROXY_CONNECTION) {
            this.logon_mode = 7;
            String proxyType = this.ociConnectionPoolProxyType;
            int num_proxy_roles = this.ociConnectionPoolProxyNumRoles.intValue();
            String[] proxy_roles = null;
            if (num_proxy_roles > 0) {
                proxy_roles = (String[]) this.ociConnectionPoolProxyRoles;
            }
            byte[] proxy_un = null;
            byte[] proxy_pd = null;
            byte[] proxy_dn = null;
            byte[] proxy_cf = null;
            if (proxyType == OracleOCIConnectionPool.PROXYTYPE_USER_NAME) {
                proxy_type = 1;
                String p2 = this.ociConnectionPoolProxyUserName;
                if (p2 != null) {
                    proxy_un = p2.getBytes();
                }
                String p3 = this.ociConnectionPoolProxyPassword.get();
                if (p3 != null) {
                    proxy_pd = p3.getBytes();
                }
            } else if (proxyType == OracleOCIConnectionPool.PROXYTYPE_DISTINGUISHED_NAME) {
                proxy_type = 2;
                String p4 = this.ociConnectionPoolProxyDistinguishedName;
                if (p4 != null) {
                    proxy_dn = p4.getBytes();
                }
            } else if (proxyType == OracleOCIConnectionPool.PROXYTYPE_CERTIFICATE) {
                proxy_type = 3;
                proxy_cf = (byte[]) this.ociConnectionPoolProxyCertificate;
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 107).fillInStackTrace());
            }
            byte[] l_userName4 = this.userName == null ? new byte[0] : DBConversion.stringToDriverCharBytes(this.userName, this.m_clientCharacterSet);
            this.conversion = new DBConversion(this.m_clientCharacterSet, this.m_clientCharacterSet, this.m_clientCharacterSet);
            this.sqlWarning = checkError(t2cConnPoolLogon(m_conn_pool.m_nativeState, l_userName4, l_userName4.length, l_password, l_password.length, l_editionName, l_editionName.length, l_driverNameAttribute, l_driverNameAttribute.length, l_database, l_database.length, this.logon_mode, proxy_type, num_proxy_roles, proxy_roles, proxy_un, proxy_un == null ? 0 : proxy_un.length, proxy_pd, proxy_pd == null ? 0 : proxy_pd.length, proxy_dn, proxy_dn == null ? 0 : proxy_dn.length, proxy_cf, proxy_cf == null ? 0 : proxy_cf.length, null, 0, nlslanguage, nlsterritory, nativeConnectionFlags), this.sqlWarning);
        } else {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 23, "connection-pool-logon").fillInStackTrace());
        }
        this.conversion = new DBConversion((short) (nativeConnectionFlags[2] & 65535), this.m_clientCharacterSet, (short) (nativeConnectionFlags[3] & 65535));
        this.byteAlign = (byte) (nativeConnectionFlags[4] & 255);
        this.timeZoneVersionNumber = (int) nativeConnectionFlags[5];
        if (nativeConnectionFlags[6] != 0) {
            this.useOCIDefaultDefines = true;
        }
        this.tagMatched[0] = nativeConnectionFlags[7] != 0;
        this.varTypeMaxLenCompat = (int) nativeConnectionFlags[8];
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public boolean isDescriptorSharable(oracle.jdbc.internal.OracleConnection conn) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Monitor.CloseableLock connLock = conn.acquireCloseableLock();
            Throwable th2 = null;
            try {
                try {
                    PhysicalConnection c2 = (PhysicalConnection) conn.getPhysicalConnection();
                    boolean z = this == c2;
                    if (connLock != null) {
                        if (0 != 0) {
                            try {
                                connLock.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            connLock.close();
                        }
                    }
                    return z;
                } finally {
                }
            } catch (Throwable th4) {
                if (connLock != null) {
                    if (th2 != null) {
                        try {
                            connLock.close();
                        } catch (Throwable th5) {
                            th2.addSuppressed(th5);
                        }
                    } else {
                        connLock.close();
                    }
                }
                throw th4;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getNetConnectionId() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.ociConnectionPoolIsPooling) {
                return null;
            }
            byte[] buffer = new byte[33];
            int[] bufferSize = new int[1];
            checkError(t2cGetNetConnectionId(this.m_nativeState, buffer, 33, bufferSize));
            String str = new String(buffer, 0, bufferSize[0] - 1, StandardCharsets.US_ASCII);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private long lobLength(byte[] locator) throws SQLException {
        long result = t2cLobGetLength(this.m_nativeState, locator, locator.length);
        checkError((int) result);
        return result;
    }

    private int blobRead(byte[] locator, long offset, int amount, byte[] buffer, boolean enableNio, ByteBuffer nioBuffer) throws SQLException {
        int result = t2cBlobRead(this.m_nativeState, locator, locator.length, offset, amount, buffer, buffer.length, enableNio, nioBuffer);
        checkError(result);
        return result;
    }

    private int blobWrite(byte[] locator, long offset, byte[] buffer, byte[][] newLocatorHolder, int bytesOffset, int amount) throws SQLException {
        int result = t2cBlobWrite(this.m_nativeState, locator, locator.length, offset, amount, buffer, bytesOffset, newLocatorHolder);
        checkError(result);
        return result;
    }

    private int clobWrite(byte[] locator, long offset, char[] buffer, byte[][] newLocatorHolder, boolean isNclob, int charsOffset, int amount) throws SQLException {
        int result = t2cClobWrite(this.m_nativeState, locator, locator.length, offset, amount, buffer, charsOffset, newLocatorHolder, isNclob);
        checkError(result);
        return result;
    }

    private int lobGetChunkSize(byte[] locator) throws SQLException {
        int result = t2cLobGetChunkSize(this.m_nativeState, locator, locator.length);
        checkError(result);
        return result;
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r8
      0x0032: PHI (r8v2 'locator' byte[]) = (r8v1 'locator' byte[]), (r8v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public long length(oracle.jdbc.internal.OracleBfile r5) throws java.sql.SQLException {
        /*
            r4 = this;
            r0 = r4
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r6 = r0
            r0 = 0
            r7 = r0
            r0 = 0
            r8 = r0
            r0 = r4
            r1 = r4
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r0 = r4
            r1 = r5
            if (r1 == 0) goto L32
            r1 = r5
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r2 = r1
            r8 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r0 = r4
            r1 = r8
            long r0 = r0.lobLength(r1)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r9 = r0
            r0 = r6
            if (r0 == 0) goto L5e
            r0 = r7
            if (r0 == 0) goto L5a
            r0 = r6
            r0.close()     // Catch: java.lang.Throwable -> L4f
            goto L5e
        L4f:
            r11 = move-exception
            r0 = r7
            r1 = r11
            r0.addSuppressed(r1)
            goto L5e
        L5a:
            r0 = r6
            r0.close()
        L5e:
            r0 = r9
            return r0
        L61:
            r8 = move-exception
            r0 = r8
            r7 = r0
            r0 = r8
            throw r0     // Catch: java.lang.Throwable -> L69
        L69:
            r12 = move-exception
            r0 = r6
            if (r0 == 0) goto L89
            r0 = r7
            if (r0 == 0) goto L85
            r0 = r6
            r0.close()     // Catch: java.lang.Throwable -> L7a
            goto L89
        L7a:
            r13 = move-exception
            r0 = r7
            r1 = r13
            r0.addSuppressed(r1)
            goto L89
        L85:
            r0 = r6
            r0.close()
        L89:
            r0 = r12
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.length(oracle.jdbc.internal.OracleBfile):long");
    }

    @Override // oracle.sql.BfileDBAccess
    public long position(oracle.jdbc.internal.OracleBfile bfile, Datum bfileDatum, byte[] pattern, long start) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (start < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
            }
            long result = LobPlsqlUtil.hasPattern(bfile, bfileDatum, pattern, start);
            return result == 0 ? -1L : result;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.BfileDBAccess
    public long position(oracle.jdbc.internal.OracleBfile bfile, Datum bfileDatum, Datum pattern, long start) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (start < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
            }
            long result = LobPlsqlUtil.isSubLob(bfile, bfileDatum, pattern, start);
            return result == 0 ? -1L : result;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:16:0x0050 A[PHI: r17
      0x0050: PHI (r17v2 'locator' byte[]) = (r17v1 'locator' byte[]), (r17v4 'locator' byte[]) binds: [B:12:0x003d, B:14:0x0049] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int getBytes(oracle.jdbc.internal.OracleBfile r10, long r11, int r13, byte[] r14) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 330
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getBytes(oracle.jdbc.internal.OracleBfile, long, int, byte[]):int");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0035 A[PHI: r10
      0x0035: PHI (r10v2 'locator' byte[]) = (r10v1 'locator' byte[]), (r10v4 'locator' byte[]) binds: [B:8:0x0022, B:10:0x002e] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.lang.String getName(oracle.jdbc.internal.OracleBfile r7) throws java.sql.SQLException {
        /*
            r6 = this;
            r0 = r6
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r8 = r0
            r0 = 0
            r9 = r0
            r0 = 0
            r10 = r0
            r0 = 0
            r11 = r0
            r0 = r6
            r1 = r6
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r2 = 1
            if (r1 != r2) goto L1a
            r1 = 1
            goto L1b
        L1a:
            r1 = 0
        L1b:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r0 = r6
            r1 = r7
            if (r1 == 0) goto L35
            r1 = r7
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r2 = r1
            r10 = r2
            if (r1 == 0) goto L35
            r1 = 1
            goto L36
        L35:
            r1 = 0
        L36:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r0 = r6
            r1 = r6
            long r1 = r1.m_nativeState     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r2 = r10
            r3 = r10
            int r3 = r3.length     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            java.lang.String r0 = r0.t2cBfileGetName(r1, r2, r3)     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r11 = r0
            r0 = r6
            r1 = r11
            int r1 = r1.length()     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r0 = r11
            r12 = r0
            r0 = r8
            if (r0 == 0) goto L76
            r0 = r9
            if (r0 == 0) goto L72
            r0 = r8
            r0.close()     // Catch: java.lang.Throwable -> L67
            goto L76
        L67:
            r13 = move-exception
            r0 = r9
            r1 = r13
            r0.addSuppressed(r1)
            goto L76
        L72:
            r0 = r8
            r0.close()
        L76:
            r0 = r12
            return r0
        L79:
            r10 = move-exception
            r0 = r10
            r9 = r0
            r0 = r10
            throw r0     // Catch: java.lang.Throwable -> L81
        L81:
            r14 = move-exception
            r0 = r8
            if (r0 == 0) goto La1
            r0 = r9
            if (r0 == 0) goto L9d
            r0 = r8
            r0.close()     // Catch: java.lang.Throwable -> L92
            goto La1
        L92:
            r15 = move-exception
            r0 = r9
            r1 = r15
            r0.addSuppressed(r1)
            goto La1
        L9d:
            r0 = r8
            r0.close()
        La1:
            r0 = r14
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getName(oracle.jdbc.internal.OracleBfile):java.lang.String");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0035 A[PHI: r10
      0x0035: PHI (r10v2 'locator' byte[]) = (r10v1 'locator' byte[]), (r10v4 'locator' byte[]) binds: [B:8:0x0022, B:10:0x002e] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.lang.String getDirAlias(oracle.jdbc.internal.OracleBfile r7) throws java.sql.SQLException {
        /*
            r6 = this;
            r0 = r6
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r8 = r0
            r0 = 0
            r9 = r0
            r0 = 0
            r10 = r0
            r0 = 0
            r11 = r0
            r0 = r6
            r1 = r6
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r2 = 1
            if (r1 != r2) goto L1a
            r1 = 1
            goto L1b
        L1a:
            r1 = 0
        L1b:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r0 = r6
            r1 = r7
            if (r1 == 0) goto L35
            r1 = r7
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r2 = r1
            r10 = r2
            if (r1 == 0) goto L35
            r1 = 1
            goto L36
        L35:
            r1 = 0
        L36:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r0 = r6
            r1 = r6
            long r1 = r1.m_nativeState     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r2 = r10
            r3 = r10
            int r3 = r3.length     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            java.lang.String r0 = r0.t2cBfileGetDirAlias(r1, r2, r3)     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r11 = r0
            r0 = r6
            r1 = r11
            int r1 = r1.length()     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L79 java.lang.Throwable -> L81
            r0 = r11
            r12 = r0
            r0 = r8
            if (r0 == 0) goto L76
            r0 = r9
            if (r0 == 0) goto L72
            r0 = r8
            r0.close()     // Catch: java.lang.Throwable -> L67
            goto L76
        L67:
            r13 = move-exception
            r0 = r9
            r1 = r13
            r0.addSuppressed(r1)
            goto L76
        L72:
            r0 = r8
            r0.close()
        L76:
            r0 = r12
            return r0
        L79:
            r10 = move-exception
            r0 = r10
            r9 = r0
            r0 = r10
            throw r0     // Catch: java.lang.Throwable -> L81
        L81:
            r14 = move-exception
            r0 = r8
            if (r0 == 0) goto La1
            r0 = r9
            if (r0 == 0) goto L9d
            r0 = r8
            r0.close()     // Catch: java.lang.Throwable -> L92
            goto La1
        L92:
            r15 = move-exception
            r0 = r9
            r1 = r15
            r0.addSuppressed(r1)
            goto La1
        L9d:
            r0 = r8
            r0.close()
        La1:
            r0 = r14
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getDirAlias(oracle.jdbc.internal.OracleBfile):java.lang.String");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r12
      0x0032: PHI (r12v2 'locator' byte[]) = (r12v1 'locator' byte[]), (r12v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void openFile(oracle.jdbc.internal.OracleBfile r9) throws java.sql.SQLException {
        /*
            r8 = this;
            r0 = r8
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r10 = r0
            r0 = 0
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = r8
            r1 = r8
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0 = r8
            r1 = r9
            if (r1 == 0) goto L32
            r1 = r9
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r2 = r1
            r12 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r13 = r0
            r0 = r8
            r1 = r8
            r2 = r8
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r3 = r12
            r4 = r12
            int r4 = r4.length     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r5 = r13
            int r1 = r1.t2cBfileOpen(r2, r3, r4, r5)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0 = r9
            r1 = r13
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0.setLocator(r1)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0 = r10
            if (r0 == 0) goto L7a
            r0 = r11
            if (r0 == 0) goto L76
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L6b
            goto L7a
        L6b:
            r14 = move-exception
            r0 = r11
            r1 = r14
            r0.addSuppressed(r1)
            goto L7a
        L76:
            r0 = r10
            r0.close()
        L7a:
            return
        L7b:
            r12 = move-exception
            r0 = r12
            r11 = r0
            r0 = r12
            throw r0     // Catch: java.lang.Throwable -> L83
        L83:
            r15 = move-exception
            r0 = r10
            if (r0 == 0) goto La3
            r0 = r11
            if (r0 == 0) goto L9f
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L94
            goto La3
        L94:
            r16 = move-exception
            r0 = r11
            r1 = r16
            r0.addSuppressed(r1)
            goto La3
        L9f:
            r0 = r10
            r0.close()
        La3:
            r0 = r15
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.openFile(oracle.jdbc.internal.OracleBfile):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r12
      0x0032: PHI (r12v2 'locator' byte[]) = (r12v1 'locator' byte[]), (r12v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean isFileOpen(oracle.jdbc.internal.OracleBfile r9) throws java.sql.SQLException {
        /*
            r8 = this;
            r0 = r8
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r10 = r0
            r0 = 0
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = r8
            r1 = r8
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r0 = r8
            r1 = r9
            if (r1 == 0) goto L32
            r1 = r9
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r2 = r1
            r12 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r0 = 1
            boolean[] r0 = new boolean[r0]     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r13 = r0
            r0 = r8
            r1 = r8
            r2 = r8
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r3 = r12
            r4 = r12
            int r4 = r4.length     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r5 = r13
            int r1 = r1.t2cBfileIsOpen(r2, r3, r4, r5)     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r0 = r13
            r1 = 0
            r0 = r0[r1]     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r14 = r0
            r0 = r10
            if (r0 == 0) goto L75
            r0 = r11
            if (r0 == 0) goto L71
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L66
            goto L75
        L66:
            r15 = move-exception
            r0 = r11
            r1 = r15
            r0.addSuppressed(r1)
            goto L75
        L71:
            r0 = r10
            r0.close()
        L75:
            r0 = r14
            return r0
        L78:
            r12 = move-exception
            r0 = r12
            r11 = r0
            r0 = r12
            throw r0     // Catch: java.lang.Throwable -> L80
        L80:
            r16 = move-exception
            r0 = r10
            if (r0 == 0) goto La0
            r0 = r11
            if (r0 == 0) goto L9c
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L91
            goto La0
        L91:
            r17 = move-exception
            r0 = r11
            r1 = r17
            r0.addSuppressed(r1)
            goto La0
        L9c:
            r0 = r10
            r0.close()
        La0:
            r0 = r16
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.isFileOpen(oracle.jdbc.internal.OracleBfile):boolean");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r12
      0x0032: PHI (r12v2 'locator' byte[]) = (r12v1 'locator' byte[]), (r12v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean fileExists(oracle.jdbc.internal.OracleBfile r9) throws java.sql.SQLException {
        /*
            r8 = this;
            r0 = r8
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r10 = r0
            r0 = 0
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = r8
            r1 = r8
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r0 = r8
            r1 = r9
            if (r1 == 0) goto L32
            r1 = r9
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r2 = r1
            r12 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r0 = 1
            boolean[] r0 = new boolean[r0]     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r13 = r0
            r0 = r8
            r1 = r8
            r2 = r8
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r3 = r12
            r4 = r12
            int r4 = r4.length     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r5 = r13
            int r1 = r1.t2cBfileExists(r2, r3, r4, r5)     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r0 = r13
            r1 = 0
            r0 = r0[r1]     // Catch: java.lang.Throwable -> L78 java.lang.Throwable -> L80
            r14 = r0
            r0 = r10
            if (r0 == 0) goto L75
            r0 = r11
            if (r0 == 0) goto L71
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L66
            goto L75
        L66:
            r15 = move-exception
            r0 = r11
            r1 = r15
            r0.addSuppressed(r1)
            goto L75
        L71:
            r0 = r10
            r0.close()
        L75:
            r0 = r14
            return r0
        L78:
            r12 = move-exception
            r0 = r12
            r11 = r0
            r0 = r12
            throw r0     // Catch: java.lang.Throwable -> L80
        L80:
            r16 = move-exception
            r0 = r10
            if (r0 == 0) goto La0
            r0 = r11
            if (r0 == 0) goto L9c
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L91
            goto La0
        L91:
            r17 = move-exception
            r0 = r11
            r1 = r17
            r0.addSuppressed(r1)
            goto La0
        L9c:
            r0 = r10
            r0.close()
        La0:
            r0 = r16
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.fileExists(oracle.jdbc.internal.OracleBfile):boolean");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r12
      0x0032: PHI (r12v2 'locator' byte[]) = (r12v1 'locator' byte[]), (r12v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void closeFile(oracle.jdbc.internal.OracleBfile r9) throws java.sql.SQLException {
        /*
            r8 = this;
            r0 = r8
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r10 = r0
            r0 = 0
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = r8
            r1 = r8
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0 = r8
            r1 = r9
            if (r1 == 0) goto L32
            r1 = r9
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r2 = r1
            r12 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r13 = r0
            r0 = r8
            r1 = r8
            r2 = r8
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r3 = r12
            r4 = r12
            int r4 = r4.length     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r5 = r13
            int r1 = r1.t2cBfileClose(r2, r3, r4, r5)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0 = r9
            r1 = r13
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0.setLocator(r1)     // Catch: java.lang.Throwable -> L7b java.lang.Throwable -> L83
            r0 = r10
            if (r0 == 0) goto L7a
            r0 = r11
            if (r0 == 0) goto L76
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L6b
            goto L7a
        L6b:
            r14 = move-exception
            r0 = r11
            r1 = r14
            r0.addSuppressed(r1)
            goto L7a
        L76:
            r0 = r10
            r0.close()
        L7a:
            return
        L7b:
            r12 = move-exception
            r0 = r12
            r11 = r0
            r0 = r12
            throw r0     // Catch: java.lang.Throwable -> L83
        L83:
            r15 = move-exception
            r0 = r10
            if (r0 == 0) goto La3
            r0 = r11
            if (r0 == 0) goto L9f
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L94
            goto La3
        L94:
            r16 = move-exception
            r0 = r11
            r1 = r16
            r0.addSuppressed(r1)
            goto La3
        L9f:
            r0 = r10
            r0.close()
        La3:
            r0 = r15
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.closeFile(oracle.jdbc.internal.OracleBfile):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0033 A[PHI: r15
      0x0033: PHI (r15v2 'locator' byte[]) = (r15v1 'locator' byte[]), (r15v4 'locator' byte[]) binds: [B:8:0x0020, B:10:0x002c] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void openLob(oracle.jdbc.internal.OracleBfile r11, int r12) throws java.sql.SQLException {
        /*
            r10 = this;
            r0 = r10
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r13 = r0
            r0 = 0
            r14 = r0
            r0 = 0
            r15 = r0
            r0 = r10
            r1 = r10
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r2 = 1
            if (r1 != r2) goto L18
            r1 = 1
            goto L19
        L18:
            r1 = 0
        L19:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0 = r10
            r1 = r11
            if (r1 == 0) goto L33
            r1 = r11
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r2 = r1
            r15 = r2
            if (r1 == 0) goto L33
            r1 = 1
            goto L34
        L33:
            r1 = 0
        L34:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r16 = r0
            r0 = r10
            r1 = r10
            r2 = r10
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r3 = 114(0x72, float:1.6E-43)
            r4 = r15
            r5 = r15
            int r5 = r5.length     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r6 = r12
            r7 = r16
            int r1 = r1.t2cLobOpen(r2, r3, r4, r5, r6, r7)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0 = r11
            r1 = r16
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0.setShareBytes(r1)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0 = r13
            if (r0 == 0) goto L80
            r0 = r14
            if (r0 == 0) goto L7c
            r0 = r13
            r0.close()     // Catch: java.lang.Throwable -> L70
            goto L80
        L70:
            r17 = move-exception
            r0 = r14
            r1 = r17
            r0.addSuppressed(r1)
            goto L80
        L7c:
            r0 = r13
            r0.close()
        L80:
            return
        L81:
            r15 = move-exception
            r0 = r15
            r14 = r0
            r0 = r15
            throw r0     // Catch: java.lang.Throwable -> L8a
        L8a:
            r18 = move-exception
            r0 = r13
            if (r0 == 0) goto Lac
            r0 = r14
            if (r0 == 0) goto La8
            r0 = r13
            r0.close()     // Catch: java.lang.Throwable -> L9c
            goto Lac
        L9c:
            r19 = move-exception
            r0 = r14
            r1 = r19
            r0.addSuppressed(r1)
            goto Lac
        La8:
            r0 = r13
            r0.close()
        Lac:
            r0 = r18
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.openLob(oracle.jdbc.internal.OracleBfile, int):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r13
      0x0032: PHI (r13v2 'locator' byte[]) = (r13v1 'locator' byte[]), (r13v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void closeLob(oracle.jdbc.internal.OracleBfile r10) throws java.sql.SQLException {
        /*
            r9 = this;
            r0 = r9
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = 0
            r13 = r0
            r0 = r9
            r1 = r9
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0 = r9
            r1 = r10
            if (r1 == 0) goto L32
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r2 = r1
            r13 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r14 = r0
            r0 = r9
            r1 = r9
            r2 = r9
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r3 = 114(0x72, float:1.6E-43)
            r4 = r13
            r5 = r13
            int r5 = r5.length     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r6 = r14
            int r1 = r1.t2cLobClose(r2, r3, r4, r5, r6)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0 = r10
            r1 = r14
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0.setShareBytes(r1)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0 = r11
            if (r0 == 0) goto L7c
            r0 = r12
            if (r0 == 0) goto L78
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L6d
            goto L7c
        L6d:
            r15 = move-exception
            r0 = r12
            r1 = r15
            r0.addSuppressed(r1)
            goto L7c
        L78:
            r0 = r11
            r0.close()
        L7c:
            return
        L7d:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L85
        L85:
            r16 = move-exception
            r0 = r11
            if (r0 == 0) goto La5
            r0 = r12
            if (r0 == 0) goto La1
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L96
            goto La5
        L96:
            r17 = move-exception
            r0 = r12
            r1 = r17
            r0.addSuppressed(r1)
            goto La5
        La1:
            r0 = r11
            r0.close()
        La5:
            r0 = r16
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.closeLob(oracle.jdbc.internal.OracleBfile):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r13
      0x0032: PHI (r13v2 'locator' byte[]) = (r13v1 'locator' byte[]), (r13v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean isOpenLob(oracle.jdbc.internal.OracleBfile r10) throws java.sql.SQLException {
        /*
            r9 = this;
            r0 = r9
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = 0
            r13 = r0
            r0 = r9
            r1 = r9
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r0 = r9
            r1 = r10
            if (r1 == 0) goto L32
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r2 = r1
            r13 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r0 = 1
            boolean[] r0 = new boolean[r0]     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r14 = r0
            r0 = r9
            r1 = r9
            r2 = r9
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r3 = 114(0x72, float:1.6E-43)
            r4 = r13
            r5 = r13
            int r5 = r5.length     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r6 = r14
            int r1 = r1.t2cLobIsOpen(r2, r3, r4, r5, r6)     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r0 = r14
            r1 = 0
            r0 = r0[r1]     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r15 = r0
            r0 = r11
            if (r0 == 0) goto L77
            r0 = r12
            if (r0 == 0) goto L73
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L68
            goto L77
        L68:
            r16 = move-exception
            r0 = r12
            r1 = r16
            r0.addSuppressed(r1)
            goto L77
        L73:
            r0 = r11
            r0.close()
        L77:
            r0 = r15
            return r0
        L7a:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L82
        L82:
            r17 = move-exception
            r0 = r11
            if (r0 == 0) goto La2
            r0 = r12
            if (r0 == 0) goto L9e
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L93
            goto La2
        L93:
            r18 = move-exception
            r0 = r12
            r1 = r18
            r0.addSuppressed(r1)
            goto La2
        L9e:
            r0 = r11
            r0.close()
        La2:
            r0 = r17
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.isOpenLob(oracle.jdbc.internal.OracleBfile):boolean");
    }

    @Override // oracle.sql.BfileDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBfile bfile, int chunkSize, long pos) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (pos == 0) {
                OracleBlobInputStream oracleBlobInputStream = new OracleBlobInputStream(bfile, chunkSize);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleBlobInputStream;
            }
            OracleBlobInputStream oracleBlobInputStream2 = new OracleBlobInputStream(bfile, chunkSize, pos);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleBlobInputStream2;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:8:0x001a  */
    @Override // oracle.sql.BfileDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.io.InputStream newConversionInputStream(oracle.jdbc.internal.OracleBfile r8, int r9) throws java.sql.SQLException {
        /*
            r7 = this;
            r0 = r7
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r10 = r0
            r0 = 0
            r11 = r0
            r0 = r7
            r1 = r8
            if (r1 == 0) goto L1a
            r1 = r8
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L5f java.lang.Throwable -> L68
            if (r1 == 0) goto L1a
            r1 = 1
            goto L1b
        L1a:
            r1 = 0
        L1b:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L5f java.lang.Throwable -> L68
            oracle.jdbc.driver.OracleConversionInputStream r0 = new oracle.jdbc.driver.OracleConversionInputStream     // Catch: java.lang.Throwable -> L5f java.lang.Throwable -> L68
            r1 = r0
            r2 = r7
            oracle.jdbc.driver.DBConversion r2 = r2.conversion     // Catch: java.lang.Throwable -> L5f java.lang.Throwable -> L68
            r3 = r8
            java.io.InputStream r3 = r3.getBinaryStream()     // Catch: java.lang.Throwable -> L5f java.lang.Throwable -> L68
            r4 = r9
            r5 = r7
            oracle.jdbc.internal.OracleConnection r5 = r5.getPhysicalConnection()     // Catch: java.lang.Throwable -> L5f java.lang.Throwable -> L68
            r1.<init>(r2, r3, r4, r5)     // Catch: java.lang.Throwable -> L5f java.lang.Throwable -> L68
            r12 = r0
            r0 = r12
            r13 = r0
            r0 = r10
            if (r0 == 0) goto L5c
            r0 = r11
            if (r0 == 0) goto L58
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L4c
            goto L5c
        L4c:
            r14 = move-exception
            r0 = r11
            r1 = r14
            r0.addSuppressed(r1)
            goto L5c
        L58:
            r0 = r10
            r0.close()
        L5c:
            r0 = r13
            return r0
        L5f:
            r12 = move-exception
            r0 = r12
            r11 = r0
            r0 = r12
            throw r0     // Catch: java.lang.Throwable -> L68
        L68:
            r15 = move-exception
            r0 = r10
            if (r0 == 0) goto L8a
            r0 = r11
            if (r0 == 0) goto L86
            r0 = r10
            r0.close()     // Catch: java.lang.Throwable -> L7a
            goto L8a
        L7a:
            r16 = move-exception
            r0 = r11
            r1 = r16
            r0.addSuppressed(r1)
            goto L8a
        L86:
            r0 = r10
            r0.close()
        L8a:
            r0 = r15
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.newConversionInputStream(oracle.jdbc.internal.OracleBfile, int):java.io.InputStream");
    }

    @Override // oracle.sql.BfileDBAccess
    public Reader newConversionReader(oracle.jdbc.internal.OracleBfile bfile, int conversionType) throws SQLException {
        checkTrue((bfile == null || bfile.shareBytes() == null) ? false : true, 54);
        Reader result = new OracleConversionReader(this.conversion, bfile.getBinaryStream(), conversionType, getPhysicalConnection());
        return result;
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r8
      0x0032: PHI (r8v2 'locator' byte[]) = (r8v1 'locator' byte[]), (r8v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public long length(oracle.jdbc.internal.OracleBlob r5) throws java.sql.SQLException {
        /*
            r4 = this;
            r0 = r4
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r6 = r0
            r0 = 0
            r7 = r0
            r0 = 0
            r8 = r0
            r0 = r4
            r1 = r4
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r0 = r4
            r1 = r5
            if (r1 == 0) goto L32
            r1 = r5
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r2 = r1
            r8 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r0 = r4
            r1 = r8
            long r0 = r0.lobLength(r1)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r9 = r0
            r0 = r6
            if (r0 == 0) goto L5e
            r0 = r7
            if (r0 == 0) goto L5a
            r0 = r6
            r0.close()     // Catch: java.lang.Throwable -> L4f
            goto L5e
        L4f:
            r11 = move-exception
            r0 = r7
            r1 = r11
            r0.addSuppressed(r1)
            goto L5e
        L5a:
            r0 = r6
            r0.close()
        L5e:
            r0 = r9
            return r0
        L61:
            r8 = move-exception
            r0 = r8
            r7 = r0
            r0 = r8
            throw r0     // Catch: java.lang.Throwable -> L69
        L69:
            r12 = move-exception
            r0 = r6
            if (r0 == 0) goto L89
            r0 = r7
            if (r0 == 0) goto L85
            r0 = r6
            r0.close()     // Catch: java.lang.Throwable -> L7a
            goto L89
        L7a:
            r13 = move-exception
            r0 = r7
            r1 = r13
            r0.addSuppressed(r1)
            goto L89
        L85:
            r0 = r6
            r0.close()
        L89:
            r0 = r12
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.length(oracle.jdbc.internal.OracleBlob):long");
    }

    @Override // oracle.sql.BlobDBAccess
    public long position(oracle.jdbc.internal.OracleBlob blob, Datum blobDatum, byte[] pattern, long start) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            checkTrue(getLifecycle() == 1, 8);
            checkTrue((blob == null || blob.shareBytes() == null) ? false : true, 54);
            if (start < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
            }
            long result = LobPlsqlUtil.hasPattern(blob, blobDatum, pattern, start);
            return result == 0 ? -1L : result;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public long position(oracle.jdbc.internal.OracleBlob blob, Datum blobDatum, Datum pattern, long start) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            checkTrue(getLifecycle() == 1, 8);
            checkTrue((blob == null || blob.shareBytes() == null) ? false : true, 54);
            checkTrue((pattern == null || pattern.shareBytes() == null) ? false : true, 54);
            if (start < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
            }
            long result = LobPlsqlUtil.isSubLob(blob, blobDatum, pattern, start);
            return result == 0 ? -1L : result;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:16:0x0053 A[PHI: r18
      0x0053: PHI (r18v2 'locator' byte[]) = (r18v1 'locator' byte[]), (r18v4 'locator' byte[]) binds: [B:12:0x0040, B:14:0x004c] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int getBytes(oracle.jdbc.internal.OracleBlob r11, long r12, int r14, byte[] r15) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 520
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getBytes(oracle.jdbc.internal.OracleBlob, long, int, byte[]):int");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:38:0x0094 A[PHI: r18
      0x0094: PHI (r18v3 'locator' byte[]) = (r18v2 'locator' byte[]), (r18v5 'locator' byte[]) binds: [B:34:0x0081, B:36:0x008d] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v29, types: [byte[], byte[][]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int putBytes(oracle.jdbc.internal.OracleBlob r10, long r11, byte[] r13, int r14, int r15) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 304
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.putBytes(oracle.jdbc.internal.OracleBlob, long, byte[], int, int):int");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r8
      0x0032: PHI (r8v2 'locator' byte[]) = (r8v1 'locator' byte[]), (r8v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int getChunkSize(oracle.jdbc.internal.OracleBlob r5) throws java.sql.SQLException {
        /*
            r4 = this;
            r0 = r4
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r6 = r0
            r0 = 0
            r7 = r0
            r0 = 0
            r8 = r0
            r0 = r4
            r1 = r4
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r0 = r4
            r1 = r5
            if (r1 == 0) goto L32
            r1 = r5
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r2 = r1
            r8 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r0 = r4
            r1 = r8
            int r0 = r0.lobGetChunkSize(r1)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r9 = r0
            r0 = r6
            if (r0 == 0) goto L5e
            r0 = r7
            if (r0 == 0) goto L5a
            r0 = r6
            r0.close()     // Catch: java.lang.Throwable -> L4f
            goto L5e
        L4f:
            r10 = move-exception
            r0 = r7
            r1 = r10
            r0.addSuppressed(r1)
            goto L5e
        L5a:
            r0 = r6
            r0.close()
        L5e:
            r0 = r9
            return r0
        L61:
            r8 = move-exception
            r0 = r8
            r7 = r0
            r0 = r8
            throw r0     // Catch: java.lang.Throwable -> L69
        L69:
            r11 = move-exception
            r0 = r6
            if (r0 == 0) goto L89
            r0 = r7
            if (r0 == 0) goto L85
            r0 = r6
            r0.close()     // Catch: java.lang.Throwable -> L7a
            goto L89
        L7a:
            r12 = move-exception
            r0 = r7
            r1 = r12
            r0.addSuppressed(r1)
            goto L89
        L85:
            r0 = r6
            r0.close()
        L89:
            r0 = r11
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getChunkSize(oracle.jdbc.internal.OracleBlob):int");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0034 A[PHI: r17
      0x0034: PHI (r17v2 'locator' byte[]) = (r17v1 'locator' byte[]), (r17v4 'locator' byte[]) binds: [B:8:0x0021, B:10:0x002d] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v16, types: [byte[], byte[][]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void trim(oracle.jdbc.internal.OracleBlob r12, long r13) throws java.sql.SQLException {
        /*
            r11 = this;
            r0 = r11
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r15 = r0
            r0 = 0
            r16 = r0
            r0 = 0
            r17 = r0
            r0 = r11
            r1 = r11
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r2 = 1
            if (r1 != r2) goto L19
            r1 = 1
            goto L1a
        L19:
            r1 = 0
        L1a:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r11
            r1 = r12
            if (r1 == 0) goto L34
            r1 = r12
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r2 = r1
            r17 = r2
            if (r1 == 0) goto L34
            r1 = 1
            goto L35
        L34:
            r1 = 0
        L35:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r11
            r1 = r12
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            java.lang.String r2 = "trim"
            r0.assertNotNull(r1, r2)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r18 = r0
            r0 = r12
            r1 = 0
            r0.setActivePrefetch(r1)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r12
            r0.clearCachedData()     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r11
            r1 = r11
            r2 = r11
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r3 = 113(0x71, float:1.58E-43)
            r4 = r13
            r5 = r17
            r6 = r17
            int r6 = r6.length     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r7 = r18
            int r1 = r1.t2cLobTrim(r2, r3, r4, r5, r6, r7)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r12
            r1 = r18
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0.setShareBytes(r1)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r15
            if (r0 == 0) goto L9e
            r0 = r16
            if (r0 == 0) goto L99
            r0 = r15
            r0.close()     // Catch: java.lang.Throwable -> L8d
            goto L9e
        L8d:
            r19 = move-exception
            r0 = r16
            r1 = r19
            r0.addSuppressed(r1)
            goto L9e
        L99:
            r0 = r15
            r0.close()
        L9e:
            return
        L9f:
            r17 = move-exception
            r0 = r17
            r16 = r0
            r0 = r17
            throw r0     // Catch: java.lang.Throwable -> La8
        La8:
            r20 = move-exception
            r0 = r15
            if (r0 == 0) goto Lcd
            r0 = r16
            if (r0 == 0) goto Lc8
            r0 = r15
            r0.close()     // Catch: java.lang.Throwable -> Lbc
            goto Lcd
        Lbc:
            r21 = move-exception
            r0 = r16
            r1 = r21
            r0.addSuppressed(r1)
            goto Lcd
        Lc8:
            r0 = r15
            r0.close()
        Lcd:
            r0 = r20
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.trim(oracle.jdbc.internal.OracleBlob, long):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.driver.PhysicalConnection
    public BLOB createTemporaryBlob(Connection conn, boolean cache, int duration) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                checkTrue(getLifecycle() == 1, 8);
                BLOB blob = new BLOB((PhysicalConnection) conn);
                ?? r0 = new byte[1];
                checkError(t2cLobCreateTemporary(this.m_nativeState, 113, cache, duration, (short) 0, r0));
                addTemporaryLob(blob.getInternal());
                blob.setShareBytes(r0[0]);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return blob;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0034 A[PHI: r15
      0x0034: PHI (r15v4 'locator' byte[]) = (r15v3 'locator' byte[]), (r15v6 'locator' byte[]) binds: [B:8:0x0021, B:10:0x002d] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v27, types: [byte[], byte[][]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void freeTemporary(oracle.jdbc.internal.OracleBlob r10, oracle.sql.Datum r11, boolean r12) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 236
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.freeTemporary(oracle.jdbc.internal.OracleBlob, oracle.sql.Datum, boolean):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:8:0x001f A[PHI: r13
      0x001f: PHI (r13v4 'locator' byte[]) = (r13v0 'locator' byte[]), (r13v1 'locator' byte[]) binds: [B:4:0x000c, B:6:0x0018] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean isTemporary(oracle.jdbc.internal.OracleBlob r10) throws java.sql.SQLException {
        /*
            r9 = this;
            r0 = r9
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = 0
            r13 = r0
            r0 = r9
            r1 = r10
            if (r1 == 0) goto L1f
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            r2 = r1
            r13 = r2
            if (r1 == 0) goto L1f
            r1 = 1
            goto L20
        L1f:
            r1 = 0
        L20:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            r0 = r9
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            java.lang.String r2 = "isTemporary"
            r0.assertNotNull(r1, r2)     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            r0 = 1
            boolean[] r0 = new boolean[r0]     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            r14 = r0
            r0 = r9
            r1 = r9
            r2 = r9
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            r3 = 113(0x71, float:1.58E-43)
            r4 = r13
            r5 = r13
            int r5 = r5.length     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            r6 = r14
            int r1 = r1.t2cLobIsTemporary(r2, r3, r4, r5, r6)     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            r0 = r14
            r1 = 0
            r0 = r0[r1]     // Catch: java.lang.Throwable -> L74 java.lang.Throwable -> L7c
            r15 = r0
            r0 = r11
            if (r0 == 0) goto L71
            r0 = r12
            if (r0 == 0) goto L6d
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L62
            goto L71
        L62:
            r16 = move-exception
            r0 = r12
            r1 = r16
            r0.addSuppressed(r1)
            goto L71
        L6d:
            r0 = r11
            r0.close()
        L71:
            r0 = r15
            return r0
        L74:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L7c
        L7c:
            r17 = move-exception
            r0 = r11
            if (r0 == 0) goto L9c
            r0 = r12
            if (r0 == 0) goto L98
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L8d
            goto L9c
        L8d:
            r18 = move-exception
            r0 = r12
            r1 = r18
            r0.addSuppressed(r1)
            goto L9c
        L98:
            r0 = r11
            r0.close()
        L9c:
            r0 = r17
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.isTemporary(oracle.jdbc.internal.OracleBlob):boolean");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Can't find top splitter block for handler:B:22:0x0044
        	at jadx.core.utils.BlockUtils.getTopSplitterForHandler(BlockUtils.java:1178)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.collectHandlerRegions(ExcHandlersRegionMaker.java:53)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.process(ExcHandlersRegionMaker.java:38)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:27)
        */
    /* JADX WARN: Unreachable blocks removed: 14, instructions: 21 */
    @Override // oracle.sql.BlobDBAccess
    public short getDuration(oracle.jdbc.internal.OracleBlob r4) throws java.sql.SQLException {
        /*
            r3 = this;
            r0 = r3
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r5 = r0
            r0 = 0
            r6 = r0
            r0 = -1
            r7 = r0
            r0 = r5
            if (r0 == 0) goto L28
            r0 = r6
            if (r0 == 0) goto L24
            r0 = r5
            r0.close()     // Catch: java.lang.Throwable -> L19
            goto L28
        L19:
            r8 = move-exception
            r0 = r6
            r1 = r8
            r0.addSuppressed(r1)
            goto L28
        L24:
            r0 = r5
            r0.close()
        L28:
            r0 = r7
            return r0
        L2b:
            r7 = move-exception
            r0 = r7
            r6 = r0
            r0 = r7
            throw r0     // Catch: java.lang.Throwable -> L33
        L33:
            r9 = move-exception
            r0 = r5
            if (r0 == 0) goto L53
            r0 = r6
            if (r0 == 0) goto L4f
            r0 = r5
            r0.close()     // Catch: java.lang.Throwable -> L44
            goto L53
        L44:
            r10 = move-exception
            r0 = r6
            r1 = r10
            r0.addSuppressed(r1)
            goto L53
        L4f:
            r0 = r5
            r0.close()
        L53:
            r0 = r9
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getDuration(oracle.jdbc.internal.OracleBlob):short");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0033 A[PHI: r15
      0x0033: PHI (r15v2 'locator' byte[]) = (r15v1 'locator' byte[]), (r15v4 'locator' byte[]) binds: [B:8:0x0020, B:10:0x002c] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v16, types: [byte[], byte[][]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void openLob(oracle.jdbc.internal.OracleBlob r11, int r12) throws java.sql.SQLException {
        /*
            r10 = this;
            r0 = r10
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r13 = r0
            r0 = 0
            r14 = r0
            r0 = 0
            r15 = r0
            r0 = r10
            r1 = r10
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r2 = 1
            if (r1 != r2) goto L18
            r1 = 1
            goto L19
        L18:
            r1 = 0
        L19:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r0 = r10
            r1 = r11
            if (r1 == 0) goto L33
            r1 = r11
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r2 = r1
            r15 = r2
            if (r1 == 0) goto L33
            r1 = 1
            goto L34
        L33:
            r1 = 0
        L34:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r0 = r10
            r1 = r11
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            java.lang.String r2 = "open"
            r0.assertNotNull(r1, r2)     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r16 = r0
            r0 = r10
            r1 = r10
            r2 = r10
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r3 = 113(0x71, float:1.58E-43)
            r4 = r15
            r5 = r15
            int r5 = r5.length     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r6 = r12
            r7 = r16
            int r1 = r1.t2cLobOpen(r2, r3, r4, r5, r6, r7)     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r0 = r11
            r1 = r16
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r0.setShareBytes(r1)     // Catch: java.lang.Throwable -> L8e java.lang.Throwable -> L97
            r0 = r13
            if (r0 == 0) goto L8d
            r0 = r14
            if (r0 == 0) goto L89
            r0 = r13
            r0.close()     // Catch: java.lang.Throwable -> L7d
            goto L8d
        L7d:
            r17 = move-exception
            r0 = r14
            r1 = r17
            r0.addSuppressed(r1)
            goto L8d
        L89:
            r0 = r13
            r0.close()
        L8d:
            return
        L8e:
            r15 = move-exception
            r0 = r15
            r14 = r0
            r0 = r15
            throw r0     // Catch: java.lang.Throwable -> L97
        L97:
            r18 = move-exception
            r0 = r13
            if (r0 == 0) goto Lb9
            r0 = r14
            if (r0 == 0) goto Lb5
            r0 = r13
            r0.close()     // Catch: java.lang.Throwable -> La9
            goto Lb9
        La9:
            r19 = move-exception
            r0 = r14
            r1 = r19
            r0.addSuppressed(r1)
            goto Lb9
        Lb5:
            r0 = r13
            r0.close()
        Lb9:
            r0 = r18
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.openLob(oracle.jdbc.internal.OracleBlob, int):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r13
      0x0032: PHI (r13v2 'locator' byte[]) = (r13v1 'locator' byte[]), (r13v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v16, types: [byte[], byte[][]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void closeLob(oracle.jdbc.internal.OracleBlob r10) throws java.sql.SQLException {
        /*
            r9 = this;
            r0 = r9
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = 0
            r13 = r0
            r0 = r9
            r1 = r9
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r0 = r9
            r1 = r10
            if (r1 == 0) goto L32
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r2 = r1
            r13 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r0 = r9
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            java.lang.String r2 = "close"
            r0.assertNotNull(r1, r2)     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r14 = r0
            r0 = r9
            r1 = r9
            r2 = r9
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r3 = 113(0x71, float:1.58E-43)
            r4 = r13
            r5 = r13
            int r5 = r5.length     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r6 = r14
            int r1 = r1.t2cLobClose(r2, r3, r4, r5, r6)     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r0 = r10
            r1 = r14
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r0.setShareBytes(r1)     // Catch: java.lang.Throwable -> L8a java.lang.Throwable -> L92
            r0 = r11
            if (r0 == 0) goto L89
            r0 = r12
            if (r0 == 0) goto L85
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L7a
            goto L89
        L7a:
            r15 = move-exception
            r0 = r12
            r1 = r15
            r0.addSuppressed(r1)
            goto L89
        L85:
            r0 = r11
            r0.close()
        L89:
            return
        L8a:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L92
        L92:
            r16 = move-exception
            r0 = r11
            if (r0 == 0) goto Lb2
            r0 = r12
            if (r0 == 0) goto Lae
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> La3
            goto Lb2
        La3:
            r17 = move-exception
            r0 = r12
            r1 = r17
            r0.addSuppressed(r1)
            goto Lb2
        Lae:
            r0 = r11
            r0.close()
        Lb2:
            r0 = r16
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.closeLob(oracle.jdbc.internal.OracleBlob):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r13
      0x0032: PHI (r13v2 'locator' byte[]) = (r13v1 'locator' byte[]), (r13v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean isOpenLob(oracle.jdbc.internal.OracleBlob r10) throws java.sql.SQLException {
        /*
            r9 = this;
            r0 = r9
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = 0
            r13 = r0
            r0 = r9
            r1 = r9
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r0 = r9
            r1 = r10
            if (r1 == 0) goto L32
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r2 = r1
            r13 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r0 = r9
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            java.lang.String r2 = "isOpen"
            r0.assertNotNull(r1, r2)     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r0 = 1
            boolean[] r0 = new boolean[r0]     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r14 = r0
            r0 = r9
            r1 = r9
            r2 = r9
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r3 = 113(0x71, float:1.58E-43)
            r4 = r13
            r5 = r13
            int r5 = r5.length     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r6 = r14
            int r1 = r1.t2cLobIsOpen(r2, r3, r4, r5, r6)     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r0 = r14
            r1 = 0
            r0 = r0[r1]     // Catch: java.lang.Throwable -> L87 java.lang.Throwable -> L8f
            r15 = r0
            r0 = r11
            if (r0 == 0) goto L84
            r0 = r12
            if (r0 == 0) goto L80
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L75
            goto L84
        L75:
            r16 = move-exception
            r0 = r12
            r1 = r16
            r0.addSuppressed(r1)
            goto L84
        L80:
            r0 = r11
            r0.close()
        L84:
            r0 = r15
            return r0
        L87:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L8f
        L8f:
            r17 = move-exception
            r0 = r11
            if (r0 == 0) goto Laf
            r0 = r12
            if (r0 == 0) goto Lab
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> La0
            goto Laf
        La0:
            r18 = move-exception
            r0 = r12
            r1 = r18
            r0.addSuppressed(r1)
            goto Laf
        Lab:
            r0 = r11
            r0.close()
        Laf:
            r0 = r17
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.isOpenLob(oracle.jdbc.internal.OracleBlob):boolean");
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            InputStream inputStreamNewInputStream = newInputStream(blob, chunkSize, pos, false);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return inputStreamNewInputStream;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos, boolean isInternal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (pos == 0) {
                OracleBlobInputStream oracleBlobInputStream = new OracleBlobInputStream(blob, chunkSize, isInternal);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleBlobInputStream;
            }
            OracleBlobInputStream oracleBlobInputStream2 = new OracleBlobInputStream(blob, chunkSize, pos, isInternal);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleBlobInputStream2;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos, long length) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                InputStream inputStreamNewInputStream = newInputStream(blob, chunkSize, pos, length, false);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return inputStreamNewInputStream;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos, long length, boolean isInternal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                OracleBlobInputStream oracleBlobInputStream = new OracleBlobInputStream(blob, chunkSize, pos, length, isInternal);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleBlobInputStream;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess
    public OutputStream newOutputStream(oracle.jdbc.internal.OracleBlob blob, int chunkSize, long pos, boolean zeroInvalid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (pos == 0) {
                if (zeroInvalid & this.lobStreamPosStandardCompliant) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
                }
                OracleBlobOutputStream oracleBlobOutputStream = new OracleBlobOutputStream(blob, chunkSize);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleBlobOutputStream;
            }
            OracleBlobOutputStream oracleBlobOutputStream2 = new OracleBlobOutputStream(blob, chunkSize, pos);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleBlobOutputStream2;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    public InputStream newConversionInputStream(oracle.jdbc.internal.OracleBlob blob, int conversionType) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                InputStream inputStreamNewConversionInputStream = newConversionInputStream(blob, conversionType, false);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return inputStreamNewConversionInputStream;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:8:0x001b  */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.io.InputStream newConversionInputStream(oracle.jdbc.internal.OracleBlob r8, int r9, boolean r10) throws java.sql.SQLException {
        /*
            r7 = this;
            r0 = r7
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = r7
            r1 = r8
            if (r1 == 0) goto L1b
            r1 = r8
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            if (r1 == 0) goto L1b
            r1 = 1
            goto L1c
        L1b:
            r1 = 0
        L1c:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            oracle.jdbc.driver.OracleConversionInputStream r0 = new oracle.jdbc.driver.OracleConversionInputStream     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r1 = r0
            r2 = r7
            oracle.jdbc.driver.DBConversion r2 = r2.conversion     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r3 = r8
            r4 = r10
            java.io.InputStream r3 = r3.binaryStreamValue(r4)     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r4 = r9
            r5 = r7
            oracle.jdbc.internal.OracleConnection r5 = r5.getPhysicalConnection()     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r1.<init>(r2, r3, r4, r5)     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r13 = r0
            r0 = r13
            r14 = r0
            r0 = r11
            if (r0 == 0) goto L61
            r0 = r12
            if (r0 == 0) goto L5c
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L50
            goto L61
        L50:
            r15 = move-exception
            r0 = r12
            r1 = r15
            r0.addSuppressed(r1)
            goto L61
        L5c:
            r0 = r11
            r0.close()
        L61:
            r0 = r14
            return r0
        L64:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L6d
        L6d:
            r16 = move-exception
            r0 = r11
            if (r0 == 0) goto L92
            r0 = r12
            if (r0 == 0) goto L8d
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L81
            goto L92
        L81:
            r17 = move-exception
            r0 = r12
            r1 = r17
            r0.addSuppressed(r1)
            goto L92
        L8d:
            r0 = r11
            r0.close()
        L92:
            r0 = r16
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.newConversionInputStream(oracle.jdbc.internal.OracleBlob, int, boolean):java.io.InputStream");
    }

    public Reader newConversionReader(oracle.jdbc.internal.OracleBlob blob, int conversionType) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                Reader readerNewConversionReader = newConversionReader(blob, conversionType, false);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return readerNewConversionReader;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:8:0x001b  */
    @Override // oracle.sql.BlobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.io.Reader newConversionReader(oracle.jdbc.internal.OracleBlob r8, int r9, boolean r10) throws java.sql.SQLException {
        /*
            r7 = this;
            r0 = r7
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = r7
            r1 = r8
            if (r1 == 0) goto L1b
            r1 = r8
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            if (r1 == 0) goto L1b
            r1 = 1
            goto L1c
        L1b:
            r1 = 0
        L1c:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            oracle.jdbc.driver.OracleConversionReader r0 = new oracle.jdbc.driver.OracleConversionReader     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r1 = r0
            r2 = r7
            oracle.jdbc.driver.DBConversion r2 = r2.conversion     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r3 = r8
            r4 = r10
            java.io.InputStream r3 = r3.binaryStreamValue(r4)     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r4 = r9
            r5 = r7
            oracle.jdbc.internal.OracleConnection r5 = r5.getPhysicalConnection()     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r1.<init>(r2, r3, r4, r5)     // Catch: java.lang.Throwable -> L64 java.lang.Throwable -> L6d
            r13 = r0
            r0 = r13
            r14 = r0
            r0 = r11
            if (r0 == 0) goto L61
            r0 = r12
            if (r0 == 0) goto L5c
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L50
            goto L61
        L50:
            r15 = move-exception
            r0 = r12
            r1 = r15
            r0.addSuppressed(r1)
            goto L61
        L5c:
            r0 = r11
            r0.close()
        L61:
            r0 = r14
            return r0
        L64:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L6d
        L6d:
            r16 = move-exception
            r0 = r11
            if (r0 == 0) goto L92
            r0 = r12
            if (r0 == 0) goto L8d
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L81
            goto L92
        L81:
            r17 = move-exception
            r0 = r12
            r1 = r17
            r0.addSuppressed(r1)
            goto L92
        L8d:
            r0 = r11
            r0.close()
        L92:
            r0 = r16
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.newConversionReader(oracle.jdbc.internal.OracleBlob, int, boolean):java.io.Reader");
    }

    @Override // oracle.sql.ClobDBAccess
    public long length(oracle.jdbc.internal.OracleClob clob) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            checkTrue(getLifecycle() == 1, 8);
            assertNotNull(clob.shareBytes(), "length");
            byte[] locator = clob.getLocator();
            checkTrue(locator != null, 54);
            long jLobLength = lobLength(locator);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return jLobLength;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public long position(oracle.jdbc.internal.OracleClob clob, String pattern, long start) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (pattern == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            checkTrue(getLifecycle() == 1, 8);
            checkTrue((clob == null || clob.shareBytes() == null) ? false : true, 54);
            if (start < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
            }
            char[] chars = new char[pattern.length()];
            pattern.getChars(0, chars.length, chars, 0);
            long result = LobPlsqlUtil.hasPattern(clob, chars, start);
            return result == 0 ? -1L : result;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public long position(oracle.jdbc.internal.OracleClob clob, oracle.jdbc.internal.OracleClob pattern, long start) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            checkTrue(getLifecycle() == 1, 8);
            checkTrue((clob == null || clob.shareBytes() == null) ? false : true, 54);
            checkTrue((pattern == null || pattern.shareBytes() == null) ? false : true, 54);
            if (start < 1) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "position()").fillInStackTrace());
            }
            long result = LobPlsqlUtil.isSubLob(clob, pattern, start);
            return result == 0 ? -1L : result;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0034 A[PHI: r23
      0x0034: PHI (r23v2 'locator' byte[]) = (r23v1 'locator' byte[]), (r23v4 'locator' byte[]) binds: [B:8:0x0021, B:10:0x002d] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int getChars(oracle.jdbc.internal.OracleClob r16, long r17, int r19, char[] r20) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 531
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getChars(oracle.jdbc.internal.OracleClob, long, int, char[]):int");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:16:0x0045 A[PHI: r19
      0x0045: PHI (r19v2 'locator' byte[]) = (r19v1 'locator' byte[]), (r19v4 'locator' byte[]) binds: [B:12:0x0032, B:14:0x003e] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v26, types: [byte[], byte[][]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int putChars(oracle.jdbc.internal.OracleClob r11, long r12, char[] r14, int r15, int r16) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 282
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.putChars(oracle.jdbc.internal.OracleClob, long, char[], int, int):int");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r8
      0x0032: PHI (r8v2 'locator' byte[]) = (r8v1 'locator' byte[]), (r8v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public int getChunkSize(oracle.jdbc.internal.OracleClob r5) throws java.sql.SQLException {
        /*
            r4 = this;
            r0 = r4
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r6 = r0
            r0 = 0
            r7 = r0
            r0 = 0
            r8 = r0
            r0 = r4
            r1 = r4
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r0 = r4
            r1 = r5
            if (r1 == 0) goto L32
            r1 = r5
            byte[] r1 = r1.getLocator()     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r2 = r1
            r8 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r0 = r4
            r1 = r8
            int r0 = r0.lobGetChunkSize(r1)     // Catch: java.lang.Throwable -> L61 java.lang.Throwable -> L69
            r9 = r0
            r0 = r6
            if (r0 == 0) goto L5e
            r0 = r7
            if (r0 == 0) goto L5a
            r0 = r6
            r0.close()     // Catch: java.lang.Throwable -> L4f
            goto L5e
        L4f:
            r10 = move-exception
            r0 = r7
            r1 = r10
            r0.addSuppressed(r1)
            goto L5e
        L5a:
            r0 = r6
            r0.close()
        L5e:
            r0 = r9
            return r0
        L61:
            r8 = move-exception
            r0 = r8
            r7 = r0
            r0 = r8
            throw r0     // Catch: java.lang.Throwable -> L69
        L69:
            r11 = move-exception
            r0 = r6
            if (r0 == 0) goto L89
            r0 = r7
            if (r0 == 0) goto L85
            r0 = r6
            r0.close()     // Catch: java.lang.Throwable -> L7a
            goto L89
        L7a:
            r12 = move-exception
            r0 = r7
            r1 = r12
            r0.addSuppressed(r1)
            goto L89
        L85:
            r0 = r6
            r0.close()
        L89:
            r0 = r11
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getChunkSize(oracle.jdbc.internal.OracleClob):int");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0034 A[PHI: r17
      0x0034: PHI (r17v2 'locator' byte[]) = (r17v1 'locator' byte[]), (r17v4 'locator' byte[]) binds: [B:8:0x0021, B:10:0x002d] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v16, types: [byte[], byte[][]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void trim(oracle.jdbc.internal.OracleClob r12, long r13) throws java.sql.SQLException {
        /*
            r11 = this;
            r0 = r11
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r15 = r0
            r0 = 0
            r16 = r0
            r0 = 0
            r17 = r0
            r0 = r11
            r1 = r11
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r2 = 1
            if (r1 != r2) goto L19
            r1 = 1
            goto L1a
        L19:
            r1 = 0
        L1a:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r11
            r1 = r12
            if (r1 == 0) goto L34
            r1 = r12
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r2 = r1
            r17 = r2
            if (r1 == 0) goto L34
            r1 = 1
            goto L35
        L34:
            r1 = 0
        L35:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r11
            r1 = r12
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            java.lang.String r2 = "trim"
            r0.assertNotNull(r1, r2)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r18 = r0
            r0 = r12
            r1 = 0
            r0.setActivePrefetch(r1)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r12
            r0.clearCachedData()     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r11
            r1 = r11
            r2 = r11
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r3 = 112(0x70, float:1.57E-43)
            r4 = r13
            r5 = r17
            r6 = r17
            int r6 = r6.length     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r7 = r18
            int r1 = r1.t2cLobTrim(r2, r3, r4, r5, r6, r7)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r12
            r1 = r18
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0.setShareBytes(r1)     // Catch: java.lang.Throwable -> L9f java.lang.Throwable -> La8
            r0 = r15
            if (r0 == 0) goto L9e
            r0 = r16
            if (r0 == 0) goto L99
            r0 = r15
            r0.close()     // Catch: java.lang.Throwable -> L8d
            goto L9e
        L8d:
            r19 = move-exception
            r0 = r16
            r1 = r19
            r0.addSuppressed(r1)
            goto L9e
        L99:
            r0 = r15
            r0.close()
        L9e:
            return
        L9f:
            r17 = move-exception
            r0 = r17
            r16 = r0
            r0 = r17
            throw r0     // Catch: java.lang.Throwable -> La8
        La8:
            r20 = move-exception
            r0 = r15
            if (r0 == 0) goto Lcd
            r0 = r16
            if (r0 == 0) goto Lc8
            r0 = r15
            r0.close()     // Catch: java.lang.Throwable -> Lbc
            goto Lcd
        Lbc:
            r21 = move-exception
            r0 = r16
            r1 = r21
            r0.addSuppressed(r1)
            goto Lcd
        Lc8:
            r0 = r15
            r0.close()
        Lcd:
            r0 = r20
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.trim(oracle.jdbc.internal.OracleClob, long):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v16, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.driver.PhysicalConnection
    public CLOB createTemporaryClob(Connection conn, boolean cache, int duration, short form_of_use) throws SQLException {
        CLOB lob;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            checkTrue(getLifecycle() == 1, 8);
            if (form_of_use == 1) {
                lob = new CLOB((PhysicalConnection) conn);
            } else {
                lob = new NCLOB((PhysicalConnection) conn);
            }
            ?? r0 = new byte[1];
            checkError(t2cLobCreateTemporary(this.m_nativeState, 112, cache, duration, form_of_use, r0));
            addTemporaryLob(lob.getInternal());
            lob.setShareBytes(r0[0]);
            CLOB clob = lob;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return clob;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0034 A[PHI: r15
      0x0034: PHI (r15v4 'locator' byte[]) = (r15v3 'locator' byte[]), (r15v6 'locator' byte[]) binds: [B:8:0x0021, B:10:0x002d] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v26, types: [byte[], byte[][]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void freeTemporary(oracle.jdbc.internal.OracleClob r10, oracle.sql.Datum r11, boolean r12) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 223
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.freeTemporary(oracle.jdbc.internal.OracleClob, oracle.sql.Datum, boolean):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:8:0x001f A[PHI: r13
      0x001f: PHI (r13v4 'locator' byte[]) = (r13v0 'locator' byte[]), (r13v1 'locator' byte[]) binds: [B:4:0x000c, B:6:0x0018] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean isTemporary(oracle.jdbc.internal.OracleClob r10) throws java.sql.SQLException {
        /*
            r9 = this;
            r0 = r9
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = 0
            r13 = r0
            r0 = r9
            r1 = r10
            if (r1 == 0) goto L1f
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L67 java.lang.Throwable -> L6f
            r2 = r1
            r13 = r2
            if (r1 == 0) goto L1f
            r1 = 1
            goto L20
        L1f:
            r1 = 0
        L20:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L67 java.lang.Throwable -> L6f
            r0 = 1
            boolean[] r0 = new boolean[r0]     // Catch: java.lang.Throwable -> L67 java.lang.Throwable -> L6f
            r14 = r0
            r0 = r9
            r1 = r9
            r2 = r9
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L67 java.lang.Throwable -> L6f
            r3 = 112(0x70, float:1.57E-43)
            r4 = r13
            r5 = r13
            int r5 = r5.length     // Catch: java.lang.Throwable -> L67 java.lang.Throwable -> L6f
            r6 = r14
            int r1 = r1.t2cLobIsTemporary(r2, r3, r4, r5, r6)     // Catch: java.lang.Throwable -> L67 java.lang.Throwable -> L6f
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L67 java.lang.Throwable -> L6f
            r0 = r14
            r1 = 0
            r0 = r0[r1]     // Catch: java.lang.Throwable -> L67 java.lang.Throwable -> L6f
            r15 = r0
            r0 = r11
            if (r0 == 0) goto L64
            r0 = r12
            if (r0 == 0) goto L60
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L55
            goto L64
        L55:
            r16 = move-exception
            r0 = r12
            r1 = r16
            r0.addSuppressed(r1)
            goto L64
        L60:
            r0 = r11
            r0.close()
        L64:
            r0 = r15
            return r0
        L67:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L6f
        L6f:
            r17 = move-exception
            r0 = r11
            if (r0 == 0) goto L8f
            r0 = r12
            if (r0 == 0) goto L8b
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L80
            goto L8f
        L80:
            r18 = move-exception
            r0 = r12
            r1 = r18
            r0.addSuppressed(r1)
            goto L8f
        L8b:
            r0 = r11
            r0.close()
        L8f:
            r0 = r17
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.isTemporary(oracle.jdbc.internal.OracleClob):boolean");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Can't find top splitter block for handler:B:22:0x0044
        	at jadx.core.utils.BlockUtils.getTopSplitterForHandler(BlockUtils.java:1178)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.collectHandlerRegions(ExcHandlersRegionMaker.java:53)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.process(ExcHandlersRegionMaker.java:38)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:27)
        */
    /* JADX WARN: Unreachable blocks removed: 14, instructions: 21 */
    @Override // oracle.sql.ClobDBAccess
    public short getDuration(oracle.jdbc.internal.OracleClob r4) throws java.sql.SQLException {
        /*
            r3 = this;
            r0 = r3
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r5 = r0
            r0 = 0
            r6 = r0
            r0 = -1
            r7 = r0
            r0 = r5
            if (r0 == 0) goto L28
            r0 = r6
            if (r0 == 0) goto L24
            r0 = r5
            r0.close()     // Catch: java.lang.Throwable -> L19
            goto L28
        L19:
            r8 = move-exception
            r0 = r6
            r1 = r8
            r0.addSuppressed(r1)
            goto L28
        L24:
            r0 = r5
            r0.close()
        L28:
            r0 = r7
            return r0
        L2b:
            r7 = move-exception
            r0 = r7
            r6 = r0
            r0 = r7
            throw r0     // Catch: java.lang.Throwable -> L33
        L33:
            r9 = move-exception
            r0 = r5
            if (r0 == 0) goto L53
            r0 = r6
            if (r0 == 0) goto L4f
            r0 = r5
            r0.close()     // Catch: java.lang.Throwable -> L44
            goto L53
        L44:
            r10 = move-exception
            r0 = r6
            r1 = r10
            r0.addSuppressed(r1)
            goto L53
        L4f:
            r0 = r5
            r0.close()
        L53:
            r0 = r9
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.getDuration(oracle.jdbc.internal.OracleClob):short");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0033 A[PHI: r15
      0x0033: PHI (r15v2 'locator' byte[]) = (r15v1 'locator' byte[]), (r15v4 'locator' byte[]) binds: [B:8:0x0020, B:10:0x002c] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void openLob(oracle.jdbc.internal.OracleClob r11, int r12) throws java.sql.SQLException {
        /*
            r10 = this;
            r0 = r10
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r13 = r0
            r0 = 0
            r14 = r0
            r0 = 0
            r15 = r0
            r0 = r10
            r1 = r10
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r2 = 1
            if (r1 != r2) goto L18
            r1 = 1
            goto L19
        L18:
            r1 = 0
        L19:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0 = r10
            r1 = r11
            if (r1 == 0) goto L33
            r1 = r11
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r2 = r1
            r15 = r2
            if (r1 == 0) goto L33
            r1 = 1
            goto L34
        L33:
            r1 = 0
        L34:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r16 = r0
            r0 = r10
            r1 = r10
            r2 = r10
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r3 = 112(0x70, float:1.57E-43)
            r4 = r15
            r5 = r15
            int r5 = r5.length     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r6 = r12
            r7 = r16
            int r1 = r1.t2cLobOpen(r2, r3, r4, r5, r6, r7)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0 = r11
            r1 = r16
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0.setShareBytes(r1)     // Catch: java.lang.Throwable -> L81 java.lang.Throwable -> L8a
            r0 = r13
            if (r0 == 0) goto L80
            r0 = r14
            if (r0 == 0) goto L7c
            r0 = r13
            r0.close()     // Catch: java.lang.Throwable -> L70
            goto L80
        L70:
            r17 = move-exception
            r0 = r14
            r1 = r17
            r0.addSuppressed(r1)
            goto L80
        L7c:
            r0 = r13
            r0.close()
        L80:
            return
        L81:
            r15 = move-exception
            r0 = r15
            r14 = r0
            r0 = r15
            throw r0     // Catch: java.lang.Throwable -> L8a
        L8a:
            r18 = move-exception
            r0 = r13
            if (r0 == 0) goto Lac
            r0 = r14
            if (r0 == 0) goto La8
            r0 = r13
            r0.close()     // Catch: java.lang.Throwable -> L9c
            goto Lac
        L9c:
            r19 = move-exception
            r0 = r14
            r1 = r19
            r0.addSuppressed(r1)
            goto Lac
        La8:
            r0 = r13
            r0.close()
        Lac:
            r0 = r18
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.openLob(oracle.jdbc.internal.OracleClob, int):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r13
      0x0032: PHI (r13v2 'locator' byte[]) = (r13v1 'locator' byte[]), (r13v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void closeLob(oracle.jdbc.internal.OracleClob r10) throws java.sql.SQLException {
        /*
            r9 = this;
            r0 = r9
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = 0
            r13 = r0
            r0 = r9
            r1 = r9
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0 = r9
            r1 = r10
            if (r1 == 0) goto L32
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r2 = r1
            r13 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0 = 1
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r14 = r0
            r0 = r9
            r1 = r9
            r2 = r9
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r3 = 112(0x70, float:1.57E-43)
            r4 = r13
            r5 = r13
            int r5 = r5.length     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r6 = r14
            int r1 = r1.t2cLobClose(r2, r3, r4, r5, r6)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0 = r10
            r1 = r14
            r2 = 0
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0.setShareBytes(r1)     // Catch: java.lang.Throwable -> L7d java.lang.Throwable -> L85
            r0 = r11
            if (r0 == 0) goto L7c
            r0 = r12
            if (r0 == 0) goto L78
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L6d
            goto L7c
        L6d:
            r15 = move-exception
            r0 = r12
            r1 = r15
            r0.addSuppressed(r1)
            goto L7c
        L78:
            r0 = r11
            r0.close()
        L7c:
            return
        L7d:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L85
        L85:
            r16 = move-exception
            r0 = r11
            if (r0 == 0) goto La5
            r0 = r12
            if (r0 == 0) goto La1
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L96
            goto La5
        L96:
            r17 = move-exception
            r0 = r12
            r1 = r17
            r0.addSuppressed(r1)
            goto La5
        La1:
            r0 = r11
            r0.close()
        La5:
            r0 = r16
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.closeLob(oracle.jdbc.internal.OracleClob):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0032 A[PHI: r13
      0x0032: PHI (r13v2 'locator' byte[]) = (r13v1 'locator' byte[]), (r13v4 'locator' byte[]) binds: [B:8:0x001f, B:10:0x002b] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // oracle.sql.ClobDBAccess
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean isOpenLob(oracle.jdbc.internal.OracleClob r10) throws java.sql.SQLException {
        /*
            r9 = this;
            r0 = r9
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r11 = r0
            r0 = 0
            r12 = r0
            r0 = 0
            r13 = r0
            r0 = r9
            r1 = r9
            int r1 = r1.getLifecycle()     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r2 = 1
            if (r1 != r2) goto L17
            r1 = 1
            goto L18
        L17:
            r1 = 0
        L18:
            r2 = 8
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r0 = r9
            r1 = r10
            if (r1 == 0) goto L32
            r1 = r10
            byte[] r1 = r1.shareBytes()     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r2 = r1
            r13 = r2
            if (r1 == 0) goto L32
            r1 = 1
            goto L33
        L32:
            r1 = 0
        L33:
            r2 = 54
            r0.checkTrue(r1, r2)     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r0 = 1
            boolean[] r0 = new boolean[r0]     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r14 = r0
            r0 = r9
            r1 = r9
            r2 = r9
            long r2 = r2.m_nativeState     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r3 = 112(0x70, float:1.57E-43)
            r4 = r13
            r5 = r13
            int r5 = r5.length     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r6 = r14
            int r1 = r1.t2cLobIsOpen(r2, r3, r4, r5, r6)     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            java.sql.SQLWarning r0 = r0.checkError(r1)     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r0 = r14
            r1 = 0
            r0 = r0[r1]     // Catch: java.lang.Throwable -> L7a java.lang.Throwable -> L82
            r15 = r0
            r0 = r11
            if (r0 == 0) goto L77
            r0 = r12
            if (r0 == 0) goto L73
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L68
            goto L77
        L68:
            r16 = move-exception
            r0 = r12
            r1 = r16
            r0.addSuppressed(r1)
            goto L77
        L73:
            r0 = r11
            r0.close()
        L77:
            r0 = r15
            return r0
        L7a:
            r13 = move-exception
            r0 = r13
            r12 = r0
            r0 = r13
            throw r0     // Catch: java.lang.Throwable -> L82
        L82:
            r17 = move-exception
            r0 = r11
            if (r0 == 0) goto La2
            r0 = r12
            if (r0 == 0) goto L9e
            r0 = r11
            r0.close()     // Catch: java.lang.Throwable -> L93
            goto La2
        L93:
            r18 = move-exception
            r0 = r12
            r1 = r18
            r0.addSuppressed(r1)
            goto La2
        L9e:
            r0 = r11
            r0.close()
        La2:
            r0 = r17
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T2CConnection.isOpenLob(oracle.jdbc.internal.OracleClob):boolean");
    }

    @Override // oracle.sql.ClobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            InputStream inputStreamNewInputStream = newInputStream(clob, chunkSize, pos, false);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return inputStreamNewInputStream;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public InputStream newInputStream(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos, boolean isInternal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (pos == 0) {
                OracleClobInputStream oracleClobInputStream = new OracleClobInputStream(clob, chunkSize, isInternal);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleClobInputStream;
            }
            OracleClobInputStream oracleClobInputStream2 = new OracleClobInputStream(clob, chunkSize, pos, isInternal);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleClobInputStream2;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public OutputStream newOutputStream(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos, boolean zeroInvalid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (pos == 0) {
                if (zeroInvalid & this.lobStreamPosStandardCompliant) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
                }
                OracleClobOutputStream oracleClobOutputStream = new OracleClobOutputStream(clob, chunkSize);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleClobOutputStream;
            }
            OracleClobOutputStream oracleClobOutputStream2 = new OracleClobOutputStream(clob, chunkSize, pos);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleClobOutputStream2;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public Reader newReader(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (pos == 0) {
                OracleClobReader oracleClobReader = new OracleClobReader(clob, chunkSize);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleClobReader;
            }
            OracleClobReader oracleClobReader2 = new OracleClobReader(clob, chunkSize, pos);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleClobReader2;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public Reader newReader(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos, long length) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                OracleClobReader oracleClobReader = new OracleClobReader(clob, chunkSize, pos, length);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleClobReader;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.ClobDBAccess
    public Writer newWriter(oracle.jdbc.internal.OracleClob clob, int chunkSize, long pos, boolean zeroInvalid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (pos == 0) {
                if (zeroInvalid & this.lobStreamPosStandardCompliant) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
                }
                OracleClobWriter oracleClobWriter = new OracleClobWriter(clob, chunkSize);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleClobWriter;
            }
            OracleClobWriter oracleClobWriter2 = new OracleClobWriter(clob, chunkSize, pos);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleClobWriter2;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void registerTAFCallback(OracleOCIFailover cbk, Object obj) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.appCallback = cbk;
                this.appCallbackObject = obj;
                checkError(t2cRegisterTAFCallback(this.m_nativeState));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    int callTAFCallbackMethod(int type, int event) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                int retCode = 0;
                if (this.appCallback != null) {
                    retCode = this.appCallback.callbackFn(this, this.appCallbackObject, type, event);
                }
                int i = retCode;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return i;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    int callPDBChangeCallbackMethod(int mode) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int retCode = 0;
            try {
                try {
                    onPDBChange(null);
                    NTFPDBChangeEvent pdbChangeEvent = new NTFPDBChangeEvent(this);
                    notify(pdbChangeEvent);
                } finally {
                }
            } catch (SQLException sqex) {
                debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "callPDBChangeCallbackMethod", "callPDBChangeCallbackMethod(): {0}", (String) null, (String) null, (Object) sqex.getMessage());
                retCode = -1;
            }
            int i = retCode;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return i;
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void onPDBChange(OracleStatement catalyst) throws SQLException {
        super.onPDBChange(catalyst);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    public boolean isValidCursorId(int cursorId) {
        return true;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean isValidLight(int timeout) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (checkAndDrain()) {
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return false;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return true;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean drainOnInbandNotification() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean valid = t2cIsServerStatusValid(this.m_nativeState);
            if (!valid) {
                closeConnectionSafely();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return true;
            }
            return false;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public int getHeapAllocSize() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            int size = t2cGetHeapAllocSize(this.m_nativeState);
            if (size < 0) {
                if (size == -999) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 23).fillInStackTrace());
                }
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 89).fillInStackTrace());
            }
            return size;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public int getOCIEnvHeapAllocSize() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            int size = t2cGetOciEnvHeapAllocSize(this.m_nativeState);
            if (size < 0) {
                if (size == -999) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 23).fillInStackTrace());
                }
                checkError(size);
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 89).fillInStackTrace());
            }
            return size;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    public static final short getClientCharSetId() {
        return (short) 871;
    }

    public static short getDriverCharSetIdFromNLS_LANG() throws SQLException {
        if (!isLibraryLoaded) {
            loadNativeLibrary();
        }
        short driverCharSetId = t2cGetDriverCharSetFromNlsLang();
        if (driverCharSetId < 0) {
            throw ((SQLException) DatabaseError.createSqlException((oracle.jdbc.internal.OracleConnection) null, 8).fillInStackTrace());
        }
        return driverCharSetId;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v21, types: [byte[]] */
    @Override // oracle.jdbc.driver.PhysicalConnection
    void doProxySession(int type, @Blind(PropertiesBlinder.class) Properties prop) throws SQLException {
        byte[][] roles = (byte[][]) null;
        int numProxyRoles = 0;
        this.savedUser = this.userName;
        this.userName = null;
        byte[] bArr = new byte[0];
        byte[] editionNameBytes = bArr;
        byte[] cfBytes = bArr;
        byte[] dnBytes = bArr;
        byte[] pwdBytes = bArr;
        byte[] userBytes = bArr;
        switch (type) {
            case 1:
                this.userName = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_NAME);
                String pwd = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_PASSWORD);
                if (this.userName != null) {
                    userBytes = DBConversion.stringToDriverCharBytes(this.userName, this.m_clientCharacterSet);
                }
                if (pwd != null) {
                    pwdBytes = DBConversion.stringToDriverCharBytes(pwd, this.m_clientCharacterSet);
                    break;
                }
                break;
            case 2:
                String distName = prop.getProperty(oracle.jdbc.OracleConnection.PROXY_DISTINGUISHED_NAME);
                if (distName != null) {
                    dnBytes = DBConversion.stringToDriverCharBytes(distName, this.m_clientCharacterSet);
                    break;
                }
                break;
            case 3:
                Object certif = prop.get(oracle.jdbc.OracleConnection.PROXY_CERTIFICATE);
                cfBytes = (byte[]) certif;
                break;
        }
        if (this.editionName != null) {
            editionNameBytes = DBConversion.stringToDriverCharBytes(this.editionName, this.m_clientCharacterSet);
        }
        String[] strRoles = (String[]) prop.get(oracle.jdbc.OracleConnection.PROXY_ROLES);
        if (strRoles != null) {
            numProxyRoles = strRoles.length;
            roles = new byte[numProxyRoles];
            for (int i = 0; i < numProxyRoles; i++) {
                if (strRoles[i] == null) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150).fillInStackTrace());
                }
                roles[i] = DBConversion.stringToDriverCharBytes(strRoles[i], this.m_clientCharacterSet);
            }
        }
        this.currentSchema = null;
        this.sqlWarning = checkError(t2cDoProxySession(this.m_nativeState, type, userBytes, userBytes.length, pwdBytes, pwdBytes.length, dnBytes, dnBytes.length, cfBytes, cfBytes.length, editionNameBytes, editionNameBytes.length, numProxyRoles, roles), this.sqlWarning);
        this.isProxy = true;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void closeProxySession() throws SQLException {
        checkError(t2cCloseProxySession(this.m_nativeState));
        this.currentSchema = null;
        this.userName = this.savedUser;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    protected void doDescribeTable(AutoKeyInfo info) throws SQLException {
        boolean tryAgain;
        int numColumns;
        String tableName = info.getTableName();
        byte[] bytesTableName = DBConversion.stringToDriverCharBytes(tableName, this.m_clientCharacterSet);
        do {
            tryAgain = false;
            numColumns = t2cDescribeTable(this.m_nativeState, bytesTableName, bytesTableName.length, this.queryMetaData1, this.queryMetaData2, this.queryMetaData1Offset, this.queryMetaData2Offset, this.queryMetaData1Size, this.queryMetaData2Size);
            if (numColumns == -1) {
                checkError(numColumns);
            }
            if (numColumns == T2CStatement.T2C_EXTEND_BUFFER) {
                tryAgain = true;
                reallocateQueryMetaData(this.queryMetaData1Size * 2, this.queryMetaData2Size * 2);
            }
        } while (tryAgain);
        processDescribeTableData(numColumns, info);
    }

    private void processDescribeTableData(int numColumns, AutoKeyInfo info) throws SQLException {
        short[] s = this.queryMetaData1;
        byte[] c = this.queryMetaData2;
        int currentShort = this.queryMetaData1Offset;
        int currentChar = this.queryMetaData2Offset;
        info.allocateSpaceForDescribedData(numColumns);
        for (int i = 0; i < numColumns; i++) {
            short s2 = s[currentShort + 0];
            short columnNameLen = s[currentShort + 6];
            String columnName = bytes2String(c, currentChar, columnNameLen, this.conversion);
            short s3 = s[currentShort + 1];
            short s4 = s[currentShort + 11];
            boolean nullable = s[currentShort + 2] != 0;
            short formOfUse = s[currentShort + 5];
            short s5 = s[currentShort + 3];
            short s6 = s[currentShort + 4];
            short s7 = s[currentShort + 13];
            boolean z = s[currentShort + 14] != 0;
            boolean z2 = s[currentShort + 15] != 0;
            currentChar += columnNameLen;
            currentShort += 16;
            String typeName = null;
            if (s7 > 0) {
                typeName = bytes2String(c, currentChar, s7, this.conversion);
                currentChar += s7;
            }
            info.fillDescribedData(i, columnName, s2, s4 > 0 ? s4 : s3, nullable, formOfUse, s5, s6, typeName, null, null, null);
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doSetApplicationContext(String nameSpace, String attribute, String value) throws SQLException {
        if (this.m_nativeState != 0) {
            checkError(t2cSetApplicationContext(this.m_nativeState, nameSpace, attribute, value));
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doClearAllApplicationContext(String nameSpace) throws SQLException {
        if (this.m_nativeState != 0) {
            checkError(t2cClearAllApplicationContext(this.m_nativeState, nameSpace));
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doStartup(int mode) throws SQLException {
        checkError(t2cStartupDatabase(this.m_nativeState, mode, null));
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doStartup(int mode, String pfileName) throws SQLException {
        checkError(t2cStartupDatabase(this.m_nativeState, mode, pfileName));
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void doShutdown(int mode) throws SQLException {
        checkError(t2cShutdownDatabase(this.m_nativeState, mode));
    }

    private static final void loadNativeLibrary() throws SQLException {
        if (!isLibraryLoaded) {
            Monitor.CloseableLock lock = LOAD_LIBRARY_MONITOR.acquireCloseableLock();
            Throwable th = null;
            try {
                if (!isLibraryLoaded) {
                    AccessController.doPrivileged(new PrivilegedAction<Object>() { // from class: oracle.jdbc.driver.T2CConnection.1
                        @Override // java.security.PrivilegedAction
                        public Object run() {
                            System.loadLibrary(T2CConnection.OCILIBRARY);
                            int libraryVersion = T2CConnection.getLibraryVersionNumber();
                            if (libraryVersion != T2CConnection.JDBC_OCI_LIBRARY_VERSION) {
                                throw new Error("Incompatible version of libocijdbc[Jdbc:" + T2CConnection.JDBC_OCI_LIBRARY_VERSION + ", Jdbc-OCI:" + libraryVersion);
                            }
                            return null;
                        }
                    });
                    isLibraryLoaded = true;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
    }

    private final void checkTrue(boolean assertion, int errCode) throws SQLException {
        if (!assertion) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), errCode).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean useLittleEndianSetCHARBinder() throws SQLException {
        return t2cPlatformIsLittleEndian(this.m_nativeState);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public void getPropertyForPooledConnection(OraclePooledConnection pc) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                super.getPropertyForPooledConnection(pc, this.password.get());
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    static final char[] getCharArray(String s) {
        char[] r;
        if (s == null) {
            r = new char[0];
        } else {
            r = new char[s.length()];
            s.getChars(0, s.length(), r, 0);
        }
        return r;
    }

    static String bytes2String(byte[] bytes, int offset, int size, DBConversion conversion) throws SQLException {
        byte[] tmp = new byte[size];
        System.arraycopy(bytes, offset, tmp, 0, size);
        return conversion.CharBytesToString(tmp, size);
    }

    void disableNio() {
        this.useNio = false;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.internal.OracleConnection
    public boolean isConnectionBigTZTC() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            checkTrue(getLifecycle() == 1, 8);
            boolean zT2cIsServerBigTZTC = t2cIsServerBigTZTC(this.m_nativeState);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zT2cIsServerBigTZTC;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private static void doSetSessionTimeZone(String defaultTimeZone) throws SQLException {
        Monitor.CloseableLock lock = SET_TIMEZONE_MONITOR.acquireCloseableLock();
        Throwable th = null;
        try {
            t2cSetSessionTimeZone(defaultTimeZone);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.sql.BlobDBAccess, oracle.sql.ClobDBAccess
    public void incrementTempLobReferenceCount(byte[] locator) throws SQLException {
    }

    @Override // oracle.sql.BlobDBAccess, oracle.sql.ClobDBAccess
    public int decrementTempLobReferenceCount(byte[] locator) throws SQLException {
        return 0;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    void releaseConnectionToPool() throws SQLException {
        if (!this.drcpEnabled || this.drcpState == OracleConnection.DRCPState.DETACHED) {
            return;
        }
        this.drcpState = OracleConnection.DRCPState.DETACHED;
        closeStatements(false);
        purgeStatementCache();
        int mode = 0;
        byte[] l_drcpTagName = EMPTY_BYTES;
        if (this.drcpTagName != null) {
            this.tagMatched[0] = false;
            l_drcpTagName = DBConversion.stringToDriverCharBytes(this.drcpTagName, this.m_clientCharacterSet);
            mode = 2;
        } else {
            this.tagMatched[0] = true;
        }
        checkError(t2cCloseDrcpConnection(this.m_nativeState, l_drcpTagName, l_drcpTagName.length, mode), this.sqlWarning);
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    boolean reusePooledConnection() throws SQLException {
        if (this.drcpState == OracleConnection.DRCPState.DETACHED) {
            this.drcpState = OracleConnection.DRCPState.ATTACHED_EXPLICIT;
            byte[] l_drcpTagName = EMPTY_BYTES;
            if (this.drcpTagName != null) {
                l_drcpTagName = DBConversion.stringToDriverCharBytes(this.drcpTagName, this.m_clientCharacterSet);
            }
            checkError(t2cOpenDrcpConnection(this.m_nativeState, l_drcpTagName, l_drcpTagName.length, this.tagMatched, this.ociExternalAuthentication), this.sqlWarning);
            resetAfterReusePooledConnection();
        }
        return this.tagMatched[0];
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean needToPurgeStatementCache() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (!this.drcpEnabled) {
                return true;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return true;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getCurrentSchema() throws SQLException {
        String tmpSchemaName;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            byte[] bCurrentSchema = new byte[258];
            if (this.currentSchema == null && getVersionNumber() < 11100) {
                super.getCurrentSchema();
            } else {
                int bCurrentSchemaLen = t2cGetSchemaName(this.m_nativeState, bCurrentSchema);
                checkError(bCurrentSchemaLen);
                if (bCurrentSchemaLen > 0) {
                    tmpSchemaName = this.conversion.CharBytesToString(bCurrentSchema, bCurrentSchemaLen);
                } else {
                    tmpSchemaName = super.getCurrentSchema();
                    if (tmpSchemaName == null || tmpSchemaName.length() == 0) {
                        tmpSchemaName = this.userName;
                    }
                }
                this.currentSchema = tmpSchemaName;
            }
            String str = this.currentSchema;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    String getAuditBanner() throws SQLException {
        requireOpenConnection();
        String tmpAuditBanner = null;
        if (getVersionNumber() >= 11100) {
            T2CBanner auditBanner = new T2CBanner(this.conversion);
            checkError(t2cGetAuditBanner(this.m_nativeState, auditBanner));
            tmpAuditBanner = auditBanner.getBanner();
        }
        return tmpAuditBanner;
    }

    @Override // oracle.jdbc.driver.PhysicalConnection
    String getAccessBanner() throws SQLException {
        requireOpenConnection();
        String tmpAccessBanner = null;
        if (getVersionNumber() >= 11100) {
            T2CBanner accessBanner = new T2CBanner(this.conversion);
            checkError(t2cGetAccessBanner(this.m_nativeState, accessBanner));
            tmpAccessBanner = accessBanner.getBanner();
        }
        return tmpAccessBanner;
    }
}
