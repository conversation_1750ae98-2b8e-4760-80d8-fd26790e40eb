package oracle.jdbc.driver;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringReader;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.InetAddress;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.security.AccessController;
import java.security.NoSuchAlgorithmException;
import java.security.PrivilegedAction;
import java.security.spec.InvalidKeySpecException;
import java.sql.Array;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.ClientInfoStatus;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.NClob;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLClientInfoException;
import java.sql.SQLException;
import java.sql.SQLPermission;
import java.sql.SQLRecoverableException;
import java.sql.SQLWarning;
import java.sql.SQLXML;
import java.sql.Savepoint;
import java.sql.Statement;
import java.sql.Struct;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.EnumSet;
import java.util.Enumeration;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.IdentityHashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Properties;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.TimeZone;
import java.util.UUID;
import java.util.Vector;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.transaction.xa.XAResource;
import oracle.jdbc.DatabaseFunction;
import oracle.jdbc.LogicalTransactionIdEventListener;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleOCIFailover;
import oracle.jdbc.OracleSQLPermission;
import oracle.jdbc.OracleShardingKey;
import oracle.jdbc.SwitchableBugFix;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.TraceKey;
import oracle.jdbc.aq.AQDequeueOptions;
import oracle.jdbc.aq.AQEnqueueOptions;
import oracle.jdbc.aq.AQMessage;
import oracle.jdbc.aq.AQMessageProperties;
import oracle.jdbc.aq.AQNotificationRegistration;
import oracle.jdbc.clio.annotations.Debug;
import oracle.jdbc.clio.annotations.Trace;
import oracle.jdbc.dcn.DatabaseChangeRegistration;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.DefaultTraceEventListenerProvider;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.Metrics;
import oracle.jdbc.diagnostics.OracleDiagnosticsMXBean;
import oracle.jdbc.diagnostics.OracleTraceKey;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.BlockSource;
import oracle.jdbc.driver.DMSFactory;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.driver.resource.DriverResources;
import oracle.jdbc.driver.resource.ProviderProperties;
import oracle.jdbc.driver.resource.ResourceType;
import oracle.jdbc.driver.utils.OracleWalletUtils;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.JMSDequeueOptions;
import oracle.jdbc.internal.JMSEnqueueOptions;
import oracle.jdbc.internal.JMSFactory;
import oracle.jdbc.internal.JMSMessage;
import oracle.jdbc.internal.JMSMessageProperties;
import oracle.jdbc.internal.JMSNotificationRegistration;
import oracle.jdbc.internal.KeywordValueLong;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.NetStat;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.internal.OracleLargeObject;
import oracle.jdbc.internal.OracleStatement;
import oracle.jdbc.internal.PDBChangeEventListener;
import oracle.jdbc.internal.XSEventListener;
import oracle.jdbc.internal.XSKeyval;
import oracle.jdbc.internal.XSNamespace;
import oracle.jdbc.internal.XSPrincipal;
import oracle.jdbc.internal.XSSecureId;
import oracle.jdbc.internal.XSSessionParameters;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.oracore.OracleTypeADT;
import oracle.jdbc.oracore.OracleTypeCLOB;
import oracle.jdbc.oracore.Util;
import oracle.jdbc.pool.OracleOCIConnectionPool;
import oracle.jdbc.pool.OraclePooledConnection;
import oracle.jdbc.spi.OsonConverter;
import oracle.net.ano.AnoServices;
import oracle.net.jdbc.nl.NLException;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;
import oracle.net.resolver.TNSNamesNamingAdapter;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.BFILE;
import oracle.sql.BINARY_DOUBLE;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.BLOB;
import oracle.sql.BfileDBAccess;
import oracle.sql.BlobDBAccess;
import oracle.sql.CLOB;
import oracle.sql.CharacterSet;
import oracle.sql.ClobDBAccess;
import oracle.sql.CustomDatum;
import oracle.sql.DATE;
import oracle.sql.Datum;
import oracle.sql.INTERVALDS;
import oracle.sql.INTERVALYM;
import oracle.sql.NCLOB;
import oracle.sql.NUMBER;
import oracle.sql.SQLName;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;
import oracle.sql.TIMESTAMP;
import oracle.sql.TIMESTAMPLTZ;
import oracle.sql.TIMESTAMPTZ;
import oracle.sql.TIMEZONETAB;
import oracle.sql.TypeDescriptor;
import oracle.sql.json.OracleJsonFactory;
import oracle.xdb.XMLType;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/PhysicalConnection.class */
abstract class PhysicalConnection extends GeneratedPhysicalConnection implements Diagnosable {
    public static final char slash_character = '/';
    public static final char at_sign_character = '@';
    public static final char left_square_bracket_character = '[';
    public static final char right_square_bracket_character = ']';
    public static final char left_round_bracket_character = '(';
    public static final char right_round_bracket_character = ')';
    static final int STREAM_CHUNK_SIZE = 32768;
    private static final boolean DEBUG = false;
    private static final String DEFAULT_CONNECTION_VALIDATION_QUERY = "SELECT 'x' FROM DUAL";
    private long connCreationStartTime;
    private static final String VALID_PROXY_USERNAME = "\\[[a-zA-Z][a-zA-Z0-9\\-_\\.]+\\]";
    static final int DEFAULT_JSON_PREFETCH_SIZE = 33554432;
    static final int DEFAULT_VECTOR_PREFETCH_SIZE = 524308;
    long outScn;
    char[][] charOutput;
    byte[][] byteOutput;
    short[][] shortOutput;
    byte[] methodTempLittleByteBuffer;
    byte[] methodTempLargeByteBuffer;
    byte[] tmpByteBuf;
    char[] tmpCharBuf;
    NTFEventListener pdbChangeListener;
    Properties sessionProperties;
    private Properties sessionPropertiesCopy;
    private Properties sessionPropertiesDelta;
    private boolean startTrackingDelta;
    private static final String END_TO_END_CLIENTINFO_KEY_SEQ_NO = "OCSID.SEQUENCE_NUMBER";
    public OracleConnection.ChecksumMode checksumMode;
    boolean isPDBChanged;
    String url;
    String savedUser;
    String currentSchema;
    public int protocolId;
    OracleTimeout timeout;
    DBConversion conversion;
    boolean xaWantsError;
    boolean usingXA;
    int txnMode;
    byte[] fdo;
    Boolean bigEndian;
    OracleStatement statements;
    private int lifecycle;
    static final int OPEN = 1;
    static final int CLOSING = 2;
    static final int CLOSED = 4;
    static final int ABORTED = 8;
    static final int BLOCKED = 16;
    boolean cancelInProgressFlag;
    boolean isRowPrefetchSetExplicitly;
    int txnLevel;
    Map<String, Class<?>> sqlTypeToJavaClassMap;
    Map<String, String> javaClassNameToSqlTypeMap;
    Map<String, Class<?>> javaObjectMap;
    final Hashtable<String, Object>[] descriptorCacheStack;
    int descriptorCacheTop;
    OracleStatement statementHoldingLine;
    oracle.jdbc.OracleDatabaseMetaData databaseMetaData;
    LogicalConnection logicalConnectionAttached;
    boolean isProxy;
    OracleSql sqlObj;
    SQLWarning sqlWarning;
    boolean readOnly;
    LRUStatementCache statementCache;
    boolean clearStatementMetaData;
    OracleCloseCallback closeCallback;
    Object privateData;
    boolean isUsable;
    TimeZone defaultTimeZone;
    static final String DMS_ROOT_NAME = "JDBC";
    static final String DMS_DEFAULT_PARENT_NAME = "Driver";
    static final String DMS_DEFAULT_PARENT_TYPE = "JDBC_Driver";
    static final String DMS_CONNECTION_PREFIX = "CONNECTION_";
    static final String DMS_CONNECTION_TYPE = "JDBC_Connection";
    public static final String DMS_CONNECTION_URL = "JDBC_Connection_Url";
    public static final String DMS_CONNECTION_USER_NAME = "JDBC_Connection_Username";
    static final String DMS_OPEN_COUNT_NAME = "ConnectionOpenCount";
    static final String DMS_OPEN_COUNT_DESCRIPTION = "number of connections that have been opened";
    static final String DMS_CLOSE_COUNT_NAME = "ConnectionCloseCount";
    static final String DMS_CLOSE_COUNT_DESCRIPTION = "number of connections that have been closed";
    static final String DMS_GETCONNECTION_NAME = "ConnectionCreate";
    static final String DMS_GETCONNECTION_DESCRIPTION = "time spent creating a connection";
    static final String DMS_LOGICAL_CONNECTION_NAME = "LogicalConnection";
    static final String DMS_LOGICAL_CONNECTION_DESCRIPTION = "logical connection holding this physical connection";
    static final String DMS_EMPTY_UNITS = "";
    static final String DMS_LOGICAL_CONNECTION_UNITS = "";
    static final String DMS_NEW_STATEMENT_NAME = "CreateNewStatement";
    static final String DMS_NEW_STATEMENT_DESCRIPTION = "time spent creating a new statement";
    static final String DMS_GET_STATEMENT_NAME = "CreateStatement";
    static final String DMS_GET_STATEMENT_DESCRIPTION = "time spent retrieving a statement from cache or creating it anew";
    static final String DMS_CONNECTION_URL_DESCRIPTION = "url specified for the connection";
    static final String DMS_CONNECTION_USER_DESCRIPTION = "user name used for the connection";
    static final String DMS_STATEMENT_PARENT_NAME = "Statement";
    static final String DMS_STATEMENT_PARENT_TYPE = "JDBC_Statement";
    static final String DMS_SQLTEXT_NAME = "SQLText";
    static final String DMS_SQLTEXT_DESCRIPTION = "current SQL text";
    static final String DMS_SQLTEXT_UNITS = "";
    static final String DMS_EXECUTE_NAME = "Execute";
    static final String DMS_EXECUTE_DESCRIPTION = "the time required for all executions of this statement";
    static final String DMS_FETCH_NAME = "Fetch";
    static final String DMS_FETCH_DESCRIPTION = "the time required for all fetches by this statement";
    DMSFactory.DMSNoun dmsParent;
    DMSFactory.DMSEvent dmsOpenCount;
    DMSFactory.DMSEvent dmsCloseCount;
    DMSFactory.DMSPhase dmsGetConnection;
    DMSFactory.DMSState dmsLogicalConnection;
    DMSFactory.DMSPhase dmsCreateNewStatement;
    DMSFactory.DMSPhase dmsCreateStatement;
    DMSFactory.DMSNoun commonDmsParent;
    DMSFactory.DMSState commonDmsSqlText;
    DMSFactory.DMSPhase commonDmsExecute;
    DMSFactory.DMSPhase commonDmsFetch;
    DMSFactory.DMSState dmsUrl;
    DMSFactory.DMSState dmsUser;
    static final int END_TO_END_DBOP_INDEX = 4;
    static final int END_TO_END_CLIENTINFO_INDEX = 5;
    static final int END_TO_END_STATE_INDEX_MAX_POST_1200 = 6;
    final int[] endToEndMaxLength;
    boolean endToEndAnyChanged;
    final boolean[] endToEndHasChanged;
    short endToEndECIDSequenceNumber;
    final String[] arrayOfNullStrings;
    String[] endToEndValues;
    final DMSFactory.DMSVersion dmsVersion;
    Map<String, Map<String, String>> currentSystemContext;
    oracle.jdbc.OracleConnection wrapper;
    int minVcsBindSize;
    int maxRawBytesSql;
    int maxRawBytesPlsql;
    int maxVcsCharsSql;
    int maxVcsNCharsSql;
    int maxVcsBytesPlsql;
    int maxVcsBytesPlsqlOut;
    int maxIbtVarcharElementLength;
    int maxVarcharLength;
    int maxNVarcharLength;
    int maxRawLength;
    String instanceName;
    String dbName;
    OracleDriverExtension driverExtension;
    static final String uninitializedMarker = "";
    String databaseProductVersion;
    short versionNumber;
    int namedTypeAccessorByteLen;
    int refTypeAccessorByteLen;
    protected final Monitor cancelInProgressLockForThin;
    boolean plsqlCompilerWarnings;
    private boolean savedAutoCommitFlag;
    private int savedTxnMode;
    private BlockSource blockSource;
    int thinACLastLtxidHash;
    LogicalTransactionId thinACCurrentLTXID;
    ReplayContext[] thinACReplayContextReceived;
    int thinACReplayContextReceivedCurrent;
    ReplayContext thinACLastReplayContextReceived;
    OracleConnection.DRCPState drcpState;
    boolean currentlyInTransaction;
    boolean drcpEnabled;
    HAManager haManager;
    protected boolean safelyClosed;
    private String cachedCompatibleString;
    private static final String TNS_ADMIN = "TNS_ADMIN";
    private Executor asyncExecutor;
    protected boolean isSepsCredentials;
    protected boolean isInRequest;
    private DriverResources driverResources;
    private OsonConverter osonConverter;
    final ByteArrayOutputStream osonSerializerStream;
    private int openCursorCount;
    Hashtable<Object, Object> clientData;
    private BufferCacheStore connectionBufferCacheStore;
    private static ThreadLocal<BufferCacheStore> threadLocalBufferCacheStore;
    private int pingResult;
    String sessionTimeZone;
    String databaseTimeZone;
    ZoneId databaseZoneId;
    ZoneId sessionZoneId;
    Calendar dbTzCalendar;
    private volatile OracleJsonFactory jsonFactory;
    private static final boolean IS_JSON_JAR_LOADED;
    private static final boolean IS_JAKARTA_JAR_LOADED;
    static final String SETCLIENTINFO_PERMISSION_NAME = "clientInfo.";
    static final List<String> RESERVED_NAMESPACES;
    static final Pattern SUPPORTED_NAME_PATTERN;
    protected final Properties clientInfo;
    private short lastEndToEndSequenceNumber;
    static final String RAW_STR = "RAW";
    static final String SYS_RAW_STR = "SYS.RAW";
    static final String SYS_ANYDATA_STR = "SYS.ANYDATA";
    static final String SYS_XMLTYPE_STR = "SYS.XMLTYPE";
    static final String JSON_STR = "JSON";
    int timeZoneVersionNumber;
    TIMEZONETAB timeZoneTab;
    private static final SQLPermission CALL_ABORT_PERMISSION;
    private static final SQLPermission CALL_SETNETWORKTIMEOUT_PERMISSION;
    private Executor closeExecutor;
    int varTypeMaxLenCompat;
    private boolean isAutoONSConfigUpdated;
    private static final String AUTH_ONS_CONFIG = "AUTH_ONS_CONFIG";
    static final String DATABASE_NAME = "DATABASE_NAME";
    static final String SERVER_HOST = "SERVER_HOST";
    static final String INSTANCE_NAME = "INSTANCE_NAME";
    static final String SERVICE_NAME = "SERVICE_NAME";
    static final String TENANT_NAME = "TENANT_NAME";
    private static final String ONS_WALLET_CONFIG = "\nwalletfile=";
    private static final String ONS_WALLET_PASSWD_CONFIG = "\nwalletpassword=";
    private static final Pattern nonQuotedIdentifierPattern;
    protected AtomicLong lobCount;
    protected AtomicLong bfileCount;
    protected HashMap<OracleLargeObject, String> temporaryLobs;
    private static final String DUMMY_VAL = "xyzzy";
    boolean isServerBigSCN;
    private static final Predicate<String> IS_SIMPLE_IDENTIFIER;
    private static final Predicate<String> IS_QUOTED_IDENTIFIER;
    private static final Predicate<String> IS_VALID_IDENTIFIER;
    private static final TraceKey[] TRACEKEY_AT_END_TO_END_INDEX;
    protected final ConnectionDiagnosable connectionDiagnosable;
    private String tenantName;
    private DatabaseFunction executingRpcFunction;
    private Boolean isLastRpcCompletedExceptionally;
    private TraceEventListener traceEventListener;
    protected Object traceUserContext;
    private static final String _Copyright_2014_Oracle_All_Rights_Reserved_;
    public static final boolean TRACE = false;
    static final CRC64 CHECKSUM = new CRC64();
    static final byte[] EMPTY_BYTE_ARRAY = new byte[0];
    static final char[] EMPTY_CHAR_ARRAY = new char[0];
    private static final String CLASS_NAME = PhysicalConnection.class.getName();
    static final int[] TempCharLengths = {4000, 32768};
    static final int[] TempByteLengths = {8000, 98304};
    private static final String[] END_TO_END_CLIENTINFO_KEYS = {"OCSID.ACTION", "OCSID.CLIENTID", "OCSID.ECID", "OCSID.MODULE", "OCSID.DBOP", "OCSID.CLIENT_INFO"};
    static NTFManager ntfManager = new NTFManager();
    static AtomicLong DMS_CONNECTION_COUNT = new AtomicLong(0);
    private static final String[] e2eKeys = {DMSFactory.Context.ECForJDBC.ACTION, DMSFactory.Context.ECForJDBC.CLIENTID, DMSFactory.Context.ECForJDBC.ECID, DMSFactory.Context.ECForJDBC.MODULE};
    private static final Pattern URL_PATTERN = Pattern.compile("(?i)jdbc:(oracle|default):(thin|oci[8]?|kprb|sharding|connection)(?-i)(:(((([\\w\\[\\]$#]*)|(\"[^��\"]+\"))/(([\\w$#\\+%\\^\\&\\*_~=\\;\\:\\<\\>\\[\\]\\(\\)\\!\\p{L}]*)|(\"[^��\"]+\")))?@(.*)?)?)?", 40);
    private static final Pattern USERNAME_PATTERN = Pattern.compile("(?iU)(([\\w$#]+)|(\"[^��\"]+\"))?(\\[(([\\w$#]+)|(\"[^��\"]+\"))\\])?((\\s+AS\\s+)(SYSDBA|SYSOPER|SYSASM|SYSBACKUP|SYSDG|SYSKM))?");
    private static final AtomicLong CONNECTIONID_SEQ = new AtomicLong();
    private static final boolean IS_BUG_11891661_DISABLED = SwitchableBugFix.isDisabled(SwitchableBugFix.BugNumber.BUG_11891661);
    private static final Pattern driverNameAttributePattern = Pattern.compile("[\\x20-\\x7e]{0,30}");
    private static final OracleSQLPermission CALL_ORACLE_ABORT_PERMISSION = new OracleSQLPermission("callAbort");

    abstract void initializePassword(OpaqueString opaqueString) throws SQLException;

    abstract void doAbort() throws SQLException;

    public abstract void getPropertyForPooledConnection(OraclePooledConnection oraclePooledConnection) throws SQLException;

    abstract void logon(AbstractConnectionBuilder<?, ?> abstractConnectionBuilder) throws SQLException;

    abstract CompletionStage<Void> logonAsync(AbstractConnectionBuilder<?, ?> abstractConnectionBuilder);

    abstract void open(OracleStatement oracleStatement) throws SQLException;

    abstract void cancelOperationOnServer(boolean z) throws SQLException;

    abstract void doSetAutoCommit(boolean z) throws SQLException;

    abstract void doCommit(int i) throws SQLException;

    abstract void doRollback() throws SQLException;

    abstract String doGetDatabaseProductVersion() throws SQLException;

    abstract short doGetVersionNumber() throws SQLException;

    abstract int doGetMajorVersionNumber() throws SQLException;

    abstract int doGetMinorVersionNumber() throws SQLException;

    abstract OracleStatement RefCursorBytesToStatement(byte[] bArr, OracleStatement oracleStatement) throws SQLException;

    abstract OracleStatement createImplicitResultSetStatement(OracleStatement oracleStatement) throws SQLException;

    public abstract BLOB createTemporaryBlob(Connection connection, boolean z, int i) throws SQLException;

    public abstract CLOB createTemporaryClob(Connection connection, boolean z, int i, short s) throws SQLException;

    static {
        boolean isJsonJarLoaded = false;
        try {
            Class.forName("javax.json.JsonValue");
            isJsonJarLoaded = true;
        } catch (ClassNotFoundException e) {
        }
        boolean isJakartaJarLoaded = false;
        try {
            Class.forName("jakarta.json.JsonValue");
            isJakartaJarLoaded = true;
        } catch (ClassNotFoundException e2) {
        }
        IS_JAKARTA_JAR_LOADED = isJakartaJarLoaded;
        IS_JSON_JAR_LOADED = isJsonJarLoaded;
        RESERVED_NAMESPACES = Arrays.asList("SYS");
        SUPPORTED_NAME_PATTERN = Pattern.compile("\\w+\\.[a-zA-Z0-9_$]+");
        CALL_ABORT_PERMISSION = new SQLPermission("callAbort");
        CALL_SETNETWORKTIMEOUT_PERMISSION = new SQLPermission("setNetworkTimeout");
        nonQuotedIdentifierPattern = Pattern.compile("[a-zA-Z]\\w*");
        IS_SIMPLE_IDENTIFIER = Pattern.compile("\\A\\p{IsAlphabetic}[\\p{IsAlphabetic}\\p{IsDigit}_$#]*\\z").asPredicate();
        IS_QUOTED_IDENTIFIER = Pattern.compile("\\A\"[^\"\\u0000]+\"\\z").asPredicate();
        IS_VALID_IDENTIFIER = Pattern.compile("\\A[^\"\\u0000]+\\z").asPredicate();
        TRACEKEY_AT_END_TO_END_INDEX = new TraceKey[6];
        TRACEKEY_AT_END_TO_END_INDEX[0] = OracleTraceKey.ACTION;
        TRACEKEY_AT_END_TO_END_INDEX[1] = OracleTraceKey.CLIENTID;
        TRACEKEY_AT_END_TO_END_INDEX[2] = OracleTraceKey.ECID;
        TRACEKEY_AT_END_TO_END_INDEX[3] = OracleTraceKey.MODULE;
        TRACEKEY_AT_END_TO_END_INDEX[4] = OracleTraceKey.DBOP;
        TRACEKEY_AT_END_TO_END_INDEX[5] = OracleTraceKey.CLIENTINFO;
        _Copyright_2014_Oracle_All_Rights_Reserved_ = null;
    }

    char[] getMethodTempCharBuffer(int length) {
        int newLength = 0;
        if (this.tmpCharBuf == null || this.tmpCharBuf.length < length) {
            int[] iArr = TempCharLengths;
            int length2 = iArr.length;
            int i = 0;
            while (true) {
                if (i >= length2) {
                    break;
                }
                int l = iArr[i];
                if (l >= length) {
                    newLength = l;
                    break;
                }
                newLength = length;
                i++;
            }
            this.tmpCharBuf = new char[newLength];
            int newByteLength = newLength * (this.conversion.sMaxCharSize > this.conversion.maxNCharSize ? this.conversion.sMaxCharSize : this.conversion.maxNCharSize);
            this.tmpByteBuf = new byte[newByteLength];
        }
        return this.tmpCharBuf;
    }

    byte[] getMethodTempByteBuffer(int length) {
        int newLength = 0;
        if (this.tmpByteBuf == null || this.tmpByteBuf.length < length) {
            int[] iArr = TempByteLengths;
            int length2 = iArr.length;
            int i = 0;
            while (true) {
                if (i >= length2) {
                    break;
                }
                int l = iArr[i];
                if (l >= length) {
                    newLength = l;
                    break;
                }
                newLength = length;
                i++;
            }
            this.tmpByteBuf = new byte[newLength];
        }
        return this.tmpByteBuf;
    }

    protected BlockSource setBlockSource() {
        return BlockSource.createBlockSource(this.useThreadLocalBufferCache, BlockSource.Implementation.THREADED);
    }

    /* JADX WARN: Type inference failed for: r1v2, types: [char[], char[][]] */
    /* JADX WARN: Type inference failed for: r1v4, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v6, types: [short[], short[][]] */
    protected PhysicalConnection() {
        this.outScn = 0L;
        this.charOutput = new char[1];
        this.byteOutput = new byte[1];
        this.shortOutput = new short[1];
        this.methodTempLittleByteBuffer = new byte[128];
        this.methodTempLargeByteBuffer = new byte[4096];
        this.tmpByteBuf = null;
        this.tmpCharBuf = null;
        this.pdbChangeListener = null;
        this.sessionProperties = null;
        this.sessionPropertiesCopy = null;
        this.sessionPropertiesDelta = null;
        this.startTrackingDelta = false;
        this.currentSchema = null;
        this.protocolId = -3;
        this.txnMode = 0;
        this.cancelInProgressFlag = false;
        this.isRowPrefetchSetExplicitly = false;
        this.txnLevel = 2;
        this.sqlTypeToJavaClassMap = null;
        this.javaClassNameToSqlTypeMap = null;
        this.javaObjectMap = new Hashtable(10);
        this.descriptorCacheStack = new Hashtable[2];
        this.descriptorCacheTop = 0;
        this.databaseMetaData = null;
        this.logicalConnectionAttached = null;
        this.isProxy = false;
        this.sqlObj = null;
        this.sqlWarning = null;
        this.readOnly = false;
        this.statementCache = null;
        this.clearStatementMetaData = false;
        this.closeCallback = null;
        this.privateData = null;
        this.isUsable = true;
        this.defaultTimeZone = null;
        this.dmsParent = null;
        this.dmsOpenCount = null;
        this.dmsCloseCount = null;
        this.dmsGetConnection = null;
        this.dmsLogicalConnection = null;
        this.dmsCreateNewStatement = null;
        this.dmsCreateStatement = null;
        this.commonDmsParent = null;
        this.commonDmsSqlText = null;
        this.commonDmsExecute = null;
        this.commonDmsFetch = null;
        this.endToEndMaxLength = new int[6];
        this.endToEndAnyChanged = false;
        this.endToEndHasChanged = new boolean[6];
        this.endToEndECIDSequenceNumber = Short.MIN_VALUE;
        this.arrayOfNullStrings = new String[6];
        this.endToEndValues = this.arrayOfNullStrings;
        this.currentSystemContext = new IdentityHashMap();
        this.wrapper = null;
        this.instanceName = null;
        this.dbName = null;
        this.databaseProductVersion = "";
        this.versionNumber = (short) -1;
        this.cancelInProgressLockForThin = Monitor.newInstance();
        this.plsqlCompilerWarnings = false;
        this.thinACLastLtxidHash = 0;
        this.thinACReplayContextReceived = new ReplayContext[10];
        this.thinACReplayContextReceivedCurrent = 0;
        this.thinACLastReplayContextReceived = null;
        this.drcpState = OracleConnection.DRCPState.DETACHED;
        this.currentlyInTransaction = false;
        this.drcpEnabled = false;
        this.haManager = NoSupportHAManager.getInstance();
        this.safelyClosed = false;
        this.cachedCompatibleString = null;
        this.isSepsCredentials = false;
        this.isInRequest = false;
        this.osonConverter = null;
        this.osonSerializerStream = new ByteArrayOutputStream();
        this.sessionTimeZone = null;
        this.databaseTimeZone = null;
        this.databaseZoneId = null;
        this.sessionZoneId = null;
        this.dbTzCalendar = null;
        this.clientInfo = new Properties();
        this.lastEndToEndSequenceNumber = (short) -1;
        this.timeZoneVersionNumber = -1;
        this.timeZoneTab = null;
        this.closeExecutor = null;
        this.varTypeMaxLenCompat = 0;
        this.isAutoONSConfigUpdated = false;
        this.lobCount = new AtomicLong();
        this.bfileCount = new AtomicLong();
        this.temporaryLobs = new HashMap<>();
        this.isServerBigSCN = false;
        this.connectionDiagnosable = new ConnectionDiagnosable(this);
        this.tenantName = null;
        this.executingRpcFunction = null;
        this.isLastRpcCompletedExceptionally = false;
        this.traceEventListener = DefaultTraceEventListenerProvider.NO_OP_TRACE_EVENT_LISTENER;
        this.traceUserContext = null;
        this.dmsVersion = DMSFactory.DMSVersion.NONE;
    }

    /* JADX WARN: Type inference failed for: r1v2, types: [char[], char[][]] */
    /* JADX WARN: Type inference failed for: r1v4, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v6, types: [short[], short[][]] */
    PhysicalConnection(String ur, @Blind(PropertiesBlinder.class) Properties info, OracleDriverExtension ext) throws SQLException {
        this.outScn = 0L;
        this.charOutput = new char[1];
        this.byteOutput = new byte[1];
        this.shortOutput = new short[1];
        this.methodTempLittleByteBuffer = new byte[128];
        this.methodTempLargeByteBuffer = new byte[4096];
        this.tmpByteBuf = null;
        this.tmpCharBuf = null;
        this.pdbChangeListener = null;
        this.sessionProperties = null;
        this.sessionPropertiesCopy = null;
        this.sessionPropertiesDelta = null;
        this.startTrackingDelta = false;
        this.currentSchema = null;
        this.protocolId = -3;
        this.txnMode = 0;
        this.cancelInProgressFlag = false;
        this.isRowPrefetchSetExplicitly = false;
        this.txnLevel = 2;
        this.sqlTypeToJavaClassMap = null;
        this.javaClassNameToSqlTypeMap = null;
        this.javaObjectMap = new Hashtable(10);
        this.descriptorCacheStack = new Hashtable[2];
        this.descriptorCacheTop = 0;
        this.databaseMetaData = null;
        this.logicalConnectionAttached = null;
        this.isProxy = false;
        this.sqlObj = null;
        this.sqlWarning = null;
        this.readOnly = false;
        this.statementCache = null;
        this.clearStatementMetaData = false;
        this.closeCallback = null;
        this.privateData = null;
        this.isUsable = true;
        this.defaultTimeZone = null;
        this.dmsParent = null;
        this.dmsOpenCount = null;
        this.dmsCloseCount = null;
        this.dmsGetConnection = null;
        this.dmsLogicalConnection = null;
        this.dmsCreateNewStatement = null;
        this.dmsCreateStatement = null;
        this.commonDmsParent = null;
        this.commonDmsSqlText = null;
        this.commonDmsExecute = null;
        this.commonDmsFetch = null;
        this.endToEndMaxLength = new int[6];
        this.endToEndAnyChanged = false;
        this.endToEndHasChanged = new boolean[6];
        this.endToEndECIDSequenceNumber = Short.MIN_VALUE;
        this.arrayOfNullStrings = new String[6];
        this.endToEndValues = this.arrayOfNullStrings;
        this.currentSystemContext = new IdentityHashMap();
        this.wrapper = null;
        this.instanceName = null;
        this.dbName = null;
        this.databaseProductVersion = "";
        this.versionNumber = (short) -1;
        this.cancelInProgressLockForThin = Monitor.newInstance();
        this.plsqlCompilerWarnings = false;
        this.thinACLastLtxidHash = 0;
        this.thinACReplayContextReceived = new ReplayContext[10];
        this.thinACReplayContextReceivedCurrent = 0;
        this.thinACLastReplayContextReceived = null;
        this.drcpState = OracleConnection.DRCPState.DETACHED;
        this.currentlyInTransaction = false;
        this.drcpEnabled = false;
        this.haManager = NoSupportHAManager.getInstance();
        this.safelyClosed = false;
        this.cachedCompatibleString = null;
        this.isSepsCredentials = false;
        this.isInRequest = false;
        this.osonConverter = null;
        this.osonSerializerStream = new ByteArrayOutputStream();
        this.sessionTimeZone = null;
        this.databaseTimeZone = null;
        this.databaseZoneId = null;
        this.sessionZoneId = null;
        this.dbTzCalendar = null;
        this.clientInfo = new Properties();
        this.lastEndToEndSequenceNumber = (short) -1;
        this.timeZoneVersionNumber = -1;
        this.timeZoneTab = null;
        this.closeExecutor = null;
        this.varTypeMaxLenCompat = 0;
        this.isAutoONSConfigUpdated = false;
        this.lobCount = new AtomicLong();
        this.bfileCount = new AtomicLong();
        this.temporaryLobs = new HashMap<>();
        this.isServerBigSCN = false;
        this.connectionDiagnosable = new ConnectionDiagnosable(this);
        this.tenantName = null;
        this.executingRpcFunction = null;
        this.isLastRpcCompletedExceptionally = false;
        this.traceEventListener = DefaultTraceEventListenerProvider.NO_OP_TRACE_EVENT_LISTENER;
        this.traceUserContext = null;
        this.connCreationStartTime = System.currentTimeMillis();
        begin(Metrics.ConnectionEvent.CONNECT);
        readConnectionProperties(ur, info);
        this.connectionDiagnosable.init();
        debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "PhysicalConnection", "Connection Properties={0}. ", (String) null, (String) null, new PropertiesBlinder().blind(info));
        createDMSSensors();
        this.dmsUrl.update(this.url);
        this.dmsUser.update(this.userName);
        this.dmsVersion = DMSFactory.getDMSVersion();
        this.driverExtension = ext;
        this.blockSource = setBlockSource();
        this.descriptorCacheStack[this.descriptorCacheTop] = new Hashtable<>(10);
    }

    private TraceEventListener getTraceEventListener(AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        TraceEventListener traceEventListener = builder.getTraceEventListener();
        if (traceEventListener != null) {
            return traceEventListener;
        }
        return (TraceEventListener) this.driverResources.getResource(ResourceType.TRACE_EVENT_LISTENER);
    }

    private String readExplicitlySetProperty(Properties connProperties, Hashtable<String, ?> urlProperties, Properties propertiesFromFile, String property) {
        String propertyValue = null;
        if (connProperties != null) {
            propertyValue = connProperties.getProperty(property);
        }
        if (propertyValue == null) {
            Supplier<String> systemProperty = () -> {
                return (String) AccessController.doPrivileged(new PrivilegedAction<String>() { // from class: oracle.jdbc.driver.PhysicalConnection.1
                    /* JADX WARN: Can't rename method to resolve collision */
                    @Override // java.security.PrivilegedAction
                    public String run() {
                        return System.getProperty(property, null);
                    }
                });
            };
            propertyValue = systemProperty.get();
        }
        if (propertyValue == null && urlProperties != null) {
            propertyValue = (String) urlProperties.get(property);
        }
        if (propertyValue == null && propertiesFromFile != null) {
            propertyValue = propertiesFromFile.getProperty(property);
        }
        return propertyValue;
    }

    void setDriverSpecificAutoCommit(boolean on) throws SQLException {
    }

    boolean isShardingDriverMode() {
        return false;
    }

    boolean isTrueCacheDriverMode() {
        return false;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isConnectionBigTZTC() throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean serverSupportsRequestBoundaries() throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean serverSupportsExplicitBoundaryBit() throws SQLException {
        return false;
    }

    @Trace(level = Trace.Level.INFO)
    void connect(AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        try {
            trace(Level.INFO, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "connect", "entering args ({0})", null, null, builder);
            setTraceEventListener(getTraceEventListener(builder));
            long startToken = this.dmsGetConnection.start();
            try {
                try {
                    setLifecycle(1);
                    needLine();
                    initializeAsyncExecutor(builder);
                    if (isDRCPConnection(this.url)) {
                        this.drcpEnabled = true;
                        if (this.drcpConnectionClass != null) {
                            this.drcpConnectionClass = this.drcpConnectionClass.trim();
                        }
                    }
                    logon(builder);
                    this.tenantName = getCurrentTenantName();
                    updateSessionProperties(TENANT_NAME, this.tenantName);
                    this.connectionDiagnosable.enableDebugTenant(this.tenantName);
                    this.connectionDiagnosable.setTraceAttribute(OracleTraceKey.CONNECTION_ID, getNetConnectionId());
                    String realDbUniqueName = this.sessionProperties.getProperty("AUTH_SC_REAL_DBUNIQUE_NAME", "");
                    if (getVersionNumber() > 19000) {
                        String authDbName = this.sessionProperties.getProperty("AUTH_DBNAME", "");
                        int index = authDbName.indexOf(oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR);
                        String containerName = index == -1 ? authDbName : authDbName.substring(0, index);
                        String shardName = realDbUniqueName + "_" + containerName;
                        updateSessionProperties("SHARD_NAME", shardName.toLowerCase());
                    } else {
                        updateSessionProperties("SHARD_NAME", realDbUniqueName.toLowerCase());
                    }
                    setAutoCommit(this.autocommit);
                    setDriverSpecificAutoCommit(this.autocommit);
                    versionDependentInit(getVersionNumber());
                    if (this.implicitStatementCacheSize > 0) {
                        setStatementCacheSize(this.implicitStatementCacheSize);
                        setImplicitCachingEnabled(true);
                    }
                    this.dmsOpenCount.occurred();
                    if (this.fanEnabled) {
                        HAManager.enableHAIfNecessary(this.url, this);
                    } else {
                        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "connect", "HA is explicitly disabled for {0}. ", (String) null, (String) null, (Object) getNetConnectionIdForLogging());
                    }
                    end(Metrics.ConnectionEvent.CONNECT);
                    debug(Level.INFO, SecurityLabel.INTERNAL, CLASS_NAME, "connect", "Time taken to establish connection={0}ms", (String) null, (String) null, Long.valueOf(System.currentTimeMillis() - this.connCreationStartTime));
                    this.txnMode = 0;
                    this.dmsGetConnection.stop(startToken);
                    if (0 != 0) {
                        this.dmsParent.destroy();
                        this.dmsParent = null;
                        setLifecycle(4);
                    }
                    trace(Level.INFO, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "connect", "returning void", null, null, new Object[0]);
                } catch (SQLException ea) {
                    setLifecycle(2);
                    try {
                        logoff();
                    } catch (SQLException e) {
                    }
                    setLifecycle(4);
                    throw ea;
                }
            } catch (Throwable th) {
                this.dmsGetConnection.stop(startToken);
                if (1 != 0) {
                    this.dmsParent.destroy();
                    this.dmsParent = null;
                    setLifecycle(4);
                }
                throw th;
            }
        } catch (Throwable th2) {
            trace(Level.INFO, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "connect", "throwing", null, th2, new Object[0]);
            throw th2;
        }
    }

    private String getCurrentTenantName() throws SQLException {
        String authDBName = getServerSessionInfo().getProperty("AUTH_DBNAME", "");
        String authDBDomainName = getServerSessionInfo().getProperty("AUTH_SC_DB_DOMAIN", "");
        String tenantName = "";
        if (!"".equals(authDBDomainName)) {
            int index = authDBName.toLowerCase().lastIndexOf(authDBDomainName.toLowerCase());
            if (index > 0) {
                tenantName = authDBName.substring(0, index - 1);
            }
        } else {
            tenantName = authDBName;
        }
        return tenantName;
    }

    final CompletionStage<Void> connectAsync(AbstractConnectionBuilder<?, ?> builder) {
        try {
            setTraceEventListener(getTraceEventListener(builder));
            long startToken = this.dmsGetConnection.start();
            try {
                initializeAsyncExecutor(builder);
                setLifecycle(1);
                needLine();
                if (isDRCPConnection(this.url)) {
                    this.drcpEnabled = true;
                    if (this.drcpConnectionClass != null) {
                        this.drcpConnectionClass = this.drcpConnectionClass.trim();
                    }
                }
                Monitor.CloseableLock lock = acquireCloseableLock();
                Throwable th = null;
                try {
                    CompletionStage<Void> logonStage = logonAsync(builder).thenApply(CompletionStageUtil.normalCompletionHandler(nil -> {
                        setAutoCommit(this.autocommit);
                        setDriverSpecificAutoCommit(this.autocommit);
                        versionDependentInit(getVersionNumber());
                        if (this.implicitStatementCacheSize > 0) {
                            setStatementCacheSize(this.implicitStatementCacheSize);
                            setImplicitCachingEnabled(true);
                        }
                        this.dmsOpenCount.occurred();
                        if (this.fanEnabled) {
                            HAManager.enableHAIfNecessary(this.url, this);
                        } else {
                            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "connectAsync", "HA is explicitly disabled for {0}. ", (String) null, (String) null, (Object) getNetConnectionIdForLogging());
                        }
                        this.txnMode = 0;
                        return (Void) null;
                    })).exceptionally(CompletionStageUtil.exceptionalCompletionHandler(SQLException.class, sqlE -> {
                        Monitor.CloseableLock exceptionLock = acquireCloseableLock();
                        Throwable th2 = null;
                        try {
                            try {
                                setLifecycle(2);
                                try {
                                    logoff();
                                } catch (SQLException e) {
                                }
                                setLifecycle(4);
                                throw sqlE;
                            } finally {
                            }
                        } catch (Throwable th3) {
                            if (exceptionLock != null) {
                                if (th2 != null) {
                                    try {
                                        exceptionLock.close();
                                    } catch (Throwable th4) {
                                        th2.addSuppressed(th4);
                                    }
                                } else {
                                    exceptionLock.close();
                                }
                            }
                            throw th3;
                        }
                    })).handle((nil2, e) -> {
                        this.dmsGetConnection.stop(startToken);
                        if (e != null) {
                            this.dmsParent.destroy();
                            this.dmsParent = null;
                            if (e instanceof CompletionException) {
                                throw ((CompletionException) e);
                            }
                            throw new CompletionException(e);
                        }
                        return nil2;
                    });
                    CompletionStage<Void> completionStageWhenCompleteAsync = logonStage.whenCompleteAsync((connection, error) -> {
                    }, getAsyncExecutor());
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return completionStageWhenCompleteAsync;
                } catch (Throwable th3) {
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    throw th3;
                }
            } catch (SQLException preConnectFailure) {
                return CompletionStageUtil.failedStage(preConnectFailure);
            }
        } catch (SQLException sqlException) {
            return CompletionStageUtil.failedStage(sqlException);
        }
    }

    private final void initializeAsyncExecutor(AbstractConnectionBuilder<?, ?> builder) {
        Executor configuredExecutor = builder == null ? null : builder.getExecutor();
        this.asyncExecutor = configuredExecutor == null ? ForkJoinPool.commonPool() : configuredExecutor;
    }

    protected void versionDependentInit(short versionNumber) {
        if (versionNumber >= 12100 || versionNumber >= 11202) {
            this.endToEndMaxLength[0] = 64;
            this.endToEndMaxLength[1] = 64;
            this.endToEndMaxLength[2] = 64;
            this.endToEndMaxLength[3] = 64;
            this.endToEndMaxLength[4] = 64;
            this.endToEndMaxLength[5] = 64;
        } else if (versionNumber < 11000 && versionNumber >= 10000) {
            this.endToEndMaxLength[0] = 32;
            this.endToEndMaxLength[1] = 64;
            this.endToEndMaxLength[2] = 64;
            this.endToEndMaxLength[3] = 48;
            this.endToEndMaxLength[4] = 64;
            this.endToEndMaxLength[5] = 64;
        } else {
            this.endToEndMaxLength[0] = 32;
            this.endToEndMaxLength[1] = 64;
            this.endToEndMaxLength[2] = 64;
            this.endToEndMaxLength[3] = 48;
            this.endToEndMaxLength[4] = 64;
            this.endToEndMaxLength[5] = 64;
        }
        if ((versionNumber >= 12000) & (this.varTypeMaxLenCompat == 2)) {
            this.minVcsBindSize = 32766;
            this.maxRawBytesSql = 32766;
            this.maxRawBytesPlsql = 32766;
            this.maxVcsCharsSql = 32766;
            this.maxVcsNCharsSql = 32766;
            this.maxVcsBytesPlsql = 32766;
            this.maxVcsBytesPlsqlOut = 32767;
            this.maxIbtVarcharElementLength = 32766;
            this.maxVarcharLength = 32767;
            this.maxNVarcharLength = 32766;
            this.maxRawLength = 32767;
            return;
        }
        if (versionNumber >= 11202) {
            this.minVcsBindSize = 4001;
            this.maxRawBytesSql = 4000;
            this.maxRawBytesPlsql = 32766;
            this.maxVcsCharsSql = 32766;
            this.maxVcsNCharsSql = 32766;
            this.maxVcsBytesPlsql = 32766;
            this.maxVcsBytesPlsqlOut = 32767;
            this.maxIbtVarcharElementLength = 32766;
            this.maxVarcharLength = 4000;
            this.maxNVarcharLength = 4000;
            this.maxRawLength = 2000;
            return;
        }
        if (versionNumber >= 11000) {
            this.minVcsBindSize = 4001;
            this.maxRawBytesSql = 4000;
            this.maxRawBytesPlsql = 32766;
            this.maxVcsCharsSql = 32766;
            this.maxVcsNCharsSql = 32766;
            this.maxVcsBytesPlsql = 32766;
            this.maxVcsBytesPlsqlOut = 32767;
            this.maxIbtVarcharElementLength = 32766;
            this.maxVarcharLength = 4000;
            this.maxNVarcharLength = 4000;
            this.maxRawLength = 2000;
            return;
        }
        if (versionNumber >= 10000) {
            this.minVcsBindSize = 4001;
            this.maxRawBytesSql = 4000;
            this.maxRawBytesPlsql = 32512;
            this.maxVcsCharsSql = 32766;
            this.maxVcsNCharsSql = 32766;
            this.maxVcsBytesPlsql = 32512;
            this.maxVcsBytesPlsqlOut = 32512;
            this.maxIbtVarcharElementLength = 32766;
            this.maxVarcharLength = 4000;
            this.maxNVarcharLength = 4000;
            this.maxRawLength = 2000;
        }
    }

    int getMaxSizeForVarchar(OracleStatement.SqlKind sqlKind, int maxLength, boolean _psqlVarcharParameter4KOnly) throws SQLException {
        int returnMaxLength;
        if (sqlKind == OracleStatement.SqlKind.PLSQL_BLOCK) {
            if (maxLength > 0) {
                returnMaxLength = Math.max(this.maxVcsBytesPlsql, maxLength);
            } else if (_psqlVarcharParameter4KOnly) {
                returnMaxLength = 4000;
            } else {
                returnMaxLength = this.maxVcsBytesPlsqlOut;
            }
        } else {
            returnMaxLength = this.maxVarcharLength;
        }
        return returnMaxLength;
    }

    private static final String propertyVariableName(String fieldName) {
        if (fieldName.equals("commitOptionProperty")) {
            return "COMMIT_OPTION";
        }
        if (fieldName.equals("calculateChecksumProperty")) {
            return "CALCULATE_CHECKSUM";
        }
        if (fieldName.equals("isResultSetCacheEnabled")) {
            return "ENABLE_QUERY_RESULT_CACHE";
        }
        char[] fieldNameInChars = new char[fieldName.length()];
        fieldName.getChars(0, fieldName.length(), fieldNameInChars, 0);
        String propertyVariableName = "";
        for (int i = 0; i < fieldNameInChars.length; i++) {
            if (Character.isUpperCase(fieldNameInChars[i])) {
                propertyVariableName = propertyVariableName + "_";
            }
            propertyVariableName = propertyVariableName + Character.toUpperCase(fieldNameInChars[i]);
        }
        return propertyVariableName;
    }

    private void initializeUserDefaults(@Blind(PropertiesBlinder.class) Properties info) {
        for (String k : OracleDriver.DEFAULT_CONNECTION_PROPERTIES.stringPropertyNames()) {
            if (!info.containsKey(k)) {
                info.setProperty(k, OracleDriver.DEFAULT_CONNECTION_PROPERTIES.getProperty(k));
                debugp(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "initializeUserDefaults", "setting default connection property. key={0}, value={1}. ", (String) null, (Throwable) null, () -> {
                    return new Object[]{k, OracleDriver.DEFAULT_CONNECTION_PROPERTIES.getProperty(k)};
                });
            }
        }
    }

    /* JADX WARN: Finally extract failed */
    protected void readConnectionProperties(String ul, @Blind(PropertiesBlinder.class) Properties info) throws SQLException {
        String providedDatabase;
        String providedUserName;
        char[] providedPassword;
        int login_timeout_value;
        initializeUserDefaults(info);
        this.url = ul;
        Hashtable<String, ?> url_properties = parseUrl(this.url);
        String tempTnsAdmin = info.getProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_TNS_ADMIN);
        if (tempTnsAdmin == null) {
            tempTnsAdmin = getSystemPropertyTnsAdmin(null);
        }
        boolean tnsAdminFromEnv = tempTnsAdmin == null;
        if (tnsAdminFromEnv) {
            tempTnsAdmin = getTnsAdminFromEnv();
        }
        Properties propertiesFromFile = getConnectionPropertiesFromFile(info, url_properties, tempTnsAdmin, tnsAdminFromEnv);
        super.readConnectionProperties(this.url, info, propertiesFromFile);
        this.driverResources = new DriverResources(ProviderProperties.create(info, propertiesFromFile, OracleDriver.getProviderSystemProperties()));
        this.connectionDiagnosable.isLoggingExplicitlyDisabled = Boolean.valueOf("false".equalsIgnoreCase(readExplicitlySetProperty(info, url_properties, propertiesFromFile, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_ENABLE_LOGGING)));
        if (readExplicitlySetProperty(info, url_properties, propertiesFromFile, "defaultRowPrefetch") != null || readExplicitlySetProperty(info, url_properties, propertiesFromFile, "oracle.jdbc.defaultRowPrefetch") != null) {
            this.isRowPrefetchSetExplicitly = true;
        } else {
            this.isRowPrefetchSetExplicitly = false;
        }
        if (this.tnsAdmin == null && tempTnsAdmin != null) {
            this.tnsAdmin = tempTnsAdmin;
        }
        if (this.commitOptionProperty != null) {
            this.commitOption = 0;
            String[] values = this.commitOptionProperty.split(",");
            if (values != null && values.length > 0) {
                for (String opt : values) {
                    if (opt.trim() != "") {
                        this.commitOption |= OracleConnection.CommitOption.valueOf(opt.trim()).getCode();
                    }
                }
            }
        }
        if (this.calculateChecksumProperty == null) {
            this.checksumMode = OracleConnection.ChecksumMode.NO_CHECKSUM;
        } else {
            this.checksumMode = OracleConnection.ChecksumMode.valueOf(this.calculateChecksumProperty);
        }
        if (this.defaultRowPrefetch <= 0) {
            this.defaultRowPrefetch = Integer.parseInt(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_DEFAULT_ROW_PREFETCH_DEFAULT);
        }
        if (this.defaultLobPrefetchSize < -1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 267).fillInStackTrace());
        }
        if (this.thinVsessionOsuser == null) {
            this.thinVsessionOsuser = getSystemPropertyUserName();
            if (this.thinVsessionOsuser == null) {
                this.thinVsessionOsuser = "jdbcuser";
            }
        }
        if (this.thinNetConnectTimeout == oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_THIN_NET_CONNECT_TIMEOUT_DEFAULT && (login_timeout_value = DriverManager.getLoginTimeout()) != 0) {
            this.thinNetConnectTimeout = "" + (login_timeout_value * 1000);
        }
        this.autocommit = this.defaultautocommit;
        if (this.userName == null) {
            this.userName = url_properties.get("user");
        }
        CharSequence password = null;
        try {
            password = info.getProperty("password");
            if (password == null) {
                password = info.getProperty("oracle.jdbc.password");
            }
            if (password == null && propertiesFromFile != null) {
                password = propertiesFromFile.getProperty("password");
                if (password == null) {
                    password = propertiesFromFile.getProperty("oracle.jdbc.password");
                }
            }
            if (password == null) {
                password = url_properties.get("password");
            }
            if (this.database == null) {
                this.database = info.getProperty("server", CONNECTION_PROPERTY_DATABASE_DEFAULT);
            }
            if (this.database == null) {
                this.database = url_properties.get("database");
            }
            if ((this.database == null || this.database.isEmpty()) && (providedDatabase = (String) this.driverResources.getResource(ResourceType.CONNECTION_STRING)) != null) {
                this.database = providedDatabase;
            }
            boolean noUser = this.userName == null || this.userName.length() == 0;
            boolean noPassword = password == null || password.length() == 0;
            if ((noUser || noPassword) && !"TCPS".equalsIgnoreCase(this.thinNetAuthenticationServices) && this.database != null && this.database.length() > 0) {
                String[] tmpCreds = OracleWalletUtils.getSecretStoreCredentials(this.database, this.walletLocation, this.walletPassword);
                this.isSepsCredentials = (tmpCreds[0] == null && tmpCreds[1] == null) ? false : true;
                if (this.isSepsCredentials) {
                    if (noUser) {
                        this.userName = tmpCreds[0];
                    } else if (noPassword && this.userName.matches(VALID_PROXY_USERNAME)) {
                        this.userName = tmpCreds[0] + this.userName;
                    }
                    if (noPassword) {
                        password = tmpCreds[1];
                    }
                }
            }
            if ((this.userName == null || this.isSepsCredentials) && (providedUserName = (String) this.driverResources.getResource(ResourceType.USERNAME)) != null) {
                this.userName = providedUserName;
            }
            if ((password == null || this.isSepsCredentials) && (providedPassword = (char[]) this.driverResources.getResource(ResourceType.PASSWORD)) != null) {
                password = CharBuffer.wrap(providedPassword);
            }
            initializePassword(OpaqueString.newOpaqueString(password));
            if (password instanceof CharBuffer) {
                CharBuffer passwordBuffer = (CharBuffer) password;
                passwordBuffer.clear();
                passwordBuffer.put(new char[passwordBuffer.capacity()]);
            }
            String[] loginModeOUT = new String[1];
            String[] proxyClientNameOUT = new String[1];
            this.userName = parseLoginOption(this.userName, info, loginModeOUT, proxyClientNameOUT);
            if (loginModeOUT[0] != null) {
                this.internalLogon = loginModeOUT[0];
            }
            if (proxyClientNameOUT[0] != null) {
                this.proxyClientName = proxyClientNameOUT[0];
            }
            this.protocol = url_properties.get("protocol");
            if (this.protocol == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 40, "Protocol is not specified in URL").fillInStackTrace());
            }
            if (info.getProperty(OracleOCIConnectionPool.IS_CONNECTION_POOLING) == "true" && this.database == null) {
                this.database = "";
            }
            if ((this.thinNetAuthenticationServices == null || !AnoServices.AUTHENTICATION_KERBEROS5.equalsIgnoreCase(this.thinNetAuthenticationServices)) && this.userName != null && !this.userName.startsWith("\"")) {
                char[] srcChars = this.userName.toCharArray();
                for (int i = 0; i < srcChars.length; i++) {
                    srcChars[i] = Character.toUpperCase(srcChars[i]);
                }
                this.userName = String.copyValueOf(srcChars);
            }
            this.xaWantsError = false;
            this.usingXA = false;
            if (this.networkCompression != null) {
                this.networkCompression = this.networkCompression.toLowerCase();
            }
            if (this.drcpTagName != null && this.drcpTagName.isEmpty()) {
                this.drcpTagName = null;
            }
            if (IS_BUG_11891661_DISABLED) {
                this.autoCommitSpecCompliant = false;
                CommonDiagnosable.getInstance().debug(Level.INFO, SecurityLabel.UNKNOWN, "PhysicalConnection", "readConnectionProperties", "autoCommitSpecCompliant is set to false", "bug fix for BUG_11891661 is disabled ", null);
            }
            loadOsonConverter();
            validateConnectionProperties();
        } catch (Throwable th) {
            if (password instanceof CharBuffer) {
                CharBuffer passwordBuffer2 = (CharBuffer) password;
                passwordBuffer2.clear();
                passwordBuffer2.put(new char[passwordBuffer2.capacity()]);
            }
            throw th;
        }
    }

    void validateConnectionProperties() throws SQLException {
        if (this.driverNameAttribute != null && !driverNameAttributePattern.matcher(this.driverNameAttribute).matches()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_INVALID_DRIVER_NAME_ATTR).fillInStackTrace());
        }
        if (this.loginTimeout < 0) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 190, "oracle.jdbc.loginTimeout is less than 0: " + this.loginTimeout).fillInStackTrace());
        }
        if (this.maxBatchMemory < 0) {
            throw ((SQLException) DatabaseError.formatSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_INVALID_CONNECTION_PROPERTY, null, null, Long.valueOf(this.maxBatchMemory), oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_MAX_BATCH_MEMORY).fillInStackTrace());
        }
        if (this.vectorDefaultGetObjectType != null) {
            Class<?> configuredClass = VectorAccessor.getDefaultConversion(this.vectorDefaultGetObjectType);
            if (configuredClass == null) {
                throw ((SQLException) DatabaseError.formatSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_INVALID_CONNECTION_PROPERTY, null, null, this.vectorDefaultGetObjectType, oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_VECTOR_DEFAULT_GET_OBJECT_TYPE).fillInStackTrace());
            }
        }
    }

    private static String parseLoginOption(String _user, @Blind(PropertiesBlinder.class) Properties info, String[] loginModeOUT, String[] proxyClientNameOUT) {
        String userOnly;
        if (_user == null || _user.length() == 0) {
            return null;
        }
        String localUser = _user.trim();
        Matcher m = USERNAME_PATTERN.matcher(localUser);
        if (m.matches()) {
            userOnly = m.group(1);
            String proxyClient = m.group(5);
            if (proxyClient != null && proxyClient.length() != 0) {
                proxyClientNameOUT[0] = proxyClient;
            }
            String loginMode = m.group(10);
            if (loginMode != null && loginMode.length() != 0) {
                loginModeOUT[0] = loginMode.toLowerCase();
            }
        } else {
            userOnly = localUser;
        }
        return userOnly;
    }

    static final Hashtable<String, String> parseUrl(String url) throws SQLException {
        Hashtable<String, String> result = new Hashtable<>(5);
        if (url == null || url.length() == 0) {
            return result;
        }
        Matcher m = URL_PATTERN.matcher(url);
        if (m.matches()) {
            m.group(1);
            String driver = m.group(2);
            result.put("protocol", driver);
            String user = m.group(6);
            String passwd = m.group(9);
            String tns = m.group(12);
            if (user != null && user.length() > 0) {
                result.put("user", user);
            }
            if (passwd != null && passwd.length() > 0) {
                result.put("password", passwd);
            }
            if (tns != null) {
                result.put("database", tns);
            }
            return result;
        }
        throw ((SQLException) DatabaseError.createSqlException(67).fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Blind(PropertiesBlinder.class)
    public Properties getProperties() {
        String propertyValue;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Properties props = new Properties();
            try {
                Field[] physicalConnectionFields = GeneratedPhysicalConnection.class.getDeclaredFields();
                for (int i = 0; i < physicalConnectionFields.length; i++) {
                    int modifier = physicalConnectionFields[i].getModifiers();
                    if (!Modifier.isStatic(modifier)) {
                        String fieldName = physicalConnectionFields[i].getName();
                        String propertyConstantName = "CONNECTION_PROPERTY_" + propertyVariableName(fieldName);
                        try {
                            Field connectionPropertyField = oracle.jdbc.OracleConnection.class.getField(propertyConstantName);
                            if ((!propertyConstantName.matches(".*PASSWORD.*") || propertyConstantName.equals("CONNECTION_PROPERTY_PASSWORD_AUTHENTICATION")) && !propertyConstantName.equals("CONNECTION_PROPERTY_ACCESS_TOKEN") && !propertyConstantName.equals("CONNECTION_PROPERTY_CLIENT_SECRET")) {
                                String propertyName = (String) connectionPropertyField.get(null);
                                String typeName = physicalConnectionFields[i].getType().getName();
                                if (typeName.equals("boolean")) {
                                    if (physicalConnectionFields[i].getBoolean(this)) {
                                        props.setProperty(propertyName, "true");
                                    } else {
                                        props.setProperty(propertyName, "false");
                                    }
                                } else if (typeName.equals("int")) {
                                    props.setProperty(propertyName, Integer.toString(physicalConnectionFields[i].getInt(this)));
                                } else if (typeName.equals("long")) {
                                    props.setProperty(propertyName, Long.toString(physicalConnectionFields[i].getLong(this)));
                                } else if (typeName.equals("java.lang.String") && (propertyValue = (String) physicalConnectionFields[i].get(this)) != null) {
                                    props.setProperty(propertyName, propertyValue);
                                }
                            }
                        } catch (NoSuchFieldException e) {
                        }
                    }
                }
            } catch (IllegalAccessException e2) {
            }
            return props;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public Connection _getPC() {
        return null;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.OracleConnection getPhysicalConnection() {
        return this;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean isLogicalConnection() {
        return false;
    }

    void createDMSSensors() {
        DMSFactory dmsInstance = DMSFactory.getInstance();
        this.dmsParent = dmsInstance.createNoun(null, DMS_ROOT_NAME, null);
        this.dmsParent = dmsInstance.createNoun(this.dmsParent, this.dmsParentName, this.dmsParentType);
        this.dmsOpenCount = dmsInstance.createEvent(this.dmsParent, DMS_OPEN_COUNT_NAME, DMS_OPEN_COUNT_DESCRIPTION);
        this.dmsCloseCount = dmsInstance.createEvent(this.dmsParent, DMS_CLOSE_COUNT_NAME, DMS_CLOSE_COUNT_DESCRIPTION);
        this.dmsGetConnection = dmsInstance.createPhaseEvent(this.dmsParent, DMS_GETCONNECTION_NAME, DMS_GETCONNECTION_DESCRIPTION);
        this.dmsGetConnection.deriveMetric(DMSFactory.SensorIntf_all);
        this.dmsParent = dmsInstance.createNoun(this.dmsParent, DMS_CONNECTION_PREFIX + DMS_CONNECTION_COUNT.incrementAndGet(), DMS_CONNECTION_TYPE);
        this.dmsUrl = dmsInstance.createState(this.dmsParent, DMS_CONNECTION_URL, "", DMS_CONNECTION_URL_DESCRIPTION, (Object) null);
        this.dmsUser = dmsInstance.createState(this.dmsParent, DMS_CONNECTION_USER_NAME, "", DMS_CONNECTION_USER_DESCRIPTION, (Object) null);
        this.dmsLogicalConnection = dmsInstance.createState(this.dmsParent, DMS_LOGICAL_CONNECTION_NAME, "", DMS_LOGICAL_CONNECTION_DESCRIPTION, (Object) null);
        this.dmsCreateNewStatement = dmsInstance.createPhaseEvent(this.dmsParent, DMS_NEW_STATEMENT_NAME, DMS_NEW_STATEMENT_DESCRIPTION);
        this.dmsCreateNewStatement.deriveMetric(DMSFactory.SensorIntf_all - DMSFactory.SensorIntf_active);
        this.dmsCreateStatement = dmsInstance.createPhaseEvent(this.dmsParent, DMS_GET_STATEMENT_NAME, DMS_GET_STATEMENT_DESCRIPTION);
        this.dmsCreateStatement.deriveMetric(DMSFactory.SensorIntf_all - DMSFactory.SensorIntf_active);
        this.commonDmsParent = dmsInstance.createNoun(this.dmsParent, DMS_STATEMENT_PARENT_NAME, DMS_STATEMENT_PARENT_TYPE);
        this.commonDmsSqlText = dmsInstance.createState(this.commonDmsParent, DMS_SQLTEXT_NAME, "", DMS_SQLTEXT_DESCRIPTION, (Object) null);
        this.commonDmsExecute = dmsInstance.createPhaseEvent(this.commonDmsParent, DMS_EXECUTE_NAME, DMS_EXECUTE_DESCRIPTION);
        this.commonDmsExecute.deriveMetric(DMSFactory.PhaseEventIntf_all);
        this.commonDmsFetch = dmsInstance.createPhaseEvent(this.commonDmsParent, DMS_FETCH_NAME, DMS_FETCH_DESCRIPTION);
        this.commonDmsFetch.deriveMetric(DMSFactory.PhaseEventIntf_all);
    }

    boolean dmsUpdateSqlText() {
        if (this.dmsVersion.equals(DMSFactory.DMSVersion.v11)) {
            return DMSFactory.Context.getECForJDBC().updateSqlText();
        }
        return this.dmsStmtMetrics;
    }

    OracleTimeout getTimeout() throws SQLException {
        if (this.timeout == null) {
            this.timeout = OracleTimeout.newTimeout(this.url);
        }
        return this.timeout;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final Statement createStatement() throws SQLException {
        return createStatement(-1, -1);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            OracleResultSet.ResultSetType resultSetTypeEnum = OracleResultSet.ResultSetType.typeFor(resultSetType, resultSetConcurrency);
            OracleStatement internalStatement = createStatementInternal(resultSetTypeEnum);
            OracleStatementWrapper oracleStatementWrapper = new OracleStatementWrapper(internalStatement);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleStatementWrapper;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    final OracleStatement createStatementInternal(OracleResultSet.ResultSetType resultSetType) throws SQLException {
        long startToken = this.dmsCreateNewStatement.start();
        try {
            OracleStatement internalStatement = (OracleStatement) this.driverExtension.allocateStatement(this, resultSetType);
            this.dmsCreateNewStatement.stop(startToken);
            return internalStatement;
        } catch (Throwable th) {
            this.dmsCreateNewStatement.stop(startToken);
            throw th;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public PreparedStatement prepareStatement(String sql) throws SQLException {
        return prepareStatement(sql, -1, -1);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public PreparedStatement prepareStatementWithKey(String key) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (key == null) {
                return null;
            }
            if (!isStatementCacheInitialized()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 95).fillInStackTrace());
            }
            long startToken = DMSFactory.getInstance().getToken();
            DMSFactory.DMSPhase dmsSensor = this.dmsCreateNewStatement;
            PreparedStatement pstmt = (OraclePreparedStatement) this.statementCache.searchExplicitCache(key);
            if (pstmt != null) {
                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "prepareStatementWithKey", "retrieved statement from cache for key={0}. ", (String) null, (String) null, key);
                dmsSensor = this.dmsCreateStatement;
            }
            dmsSensor.start(startToken);
            dmsSensor.stop(startToken);
            if (pstmt != null) {
                pstmt = new OraclePreparedStatementWrapper((oracle.jdbc.OraclePreparedStatement) pstmt);
            }
            PreparedStatement preparedStatement = pstmt;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return preparedStatement;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                requireOpenConnection();
                requireNonNullSql(sql);
                OracleResultSet.ResultSetType resultSetTypeEnum = OracleResultSet.ResultSetType.typeFor(resultSetType, resultSetConcurrency);
                OraclePreparedStatement internalStatement = prepareStatementInternal(sql, resultSetTypeEnum);
                OraclePreparedStatementWrapper oraclePreparedStatementWrapper = new OraclePreparedStatementWrapper(internalStatement);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oraclePreparedStatementWrapper;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    final OraclePreparedStatement prepareStatementInternal(String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        long startToken = DMSFactory.getInstance().getToken();
        DMSFactory.DMSPhase dmsSensor = this.dmsCreateNewStatement;
        OraclePreparedStatement internalStatement = (OraclePreparedStatement) getCachedStatement(sql, 1, resultSetType);
        if (internalStatement == null) {
            OraclePreparedStatement allocatedStatement = (OraclePreparedStatement) this.driverExtension.allocatePreparedStatement(this, sql, resultSetType);
            internalStatement = allocatedStatement;
        } else {
            dmsSensor = this.dmsCreateStatement;
        }
        dmsSensor.start(startToken);
        dmsSensor.stop(startToken);
        return internalStatement;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final CallableStatement prepareCall(String sql) throws SQLException {
        return prepareCall(sql, -1, -1);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    @Debug(level = Debug.Level.FINER)
    public final CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
        try {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "prepareCall", "entering args ({0}, {1}, {2})", (String) null, (String) null, sql, Integer.valueOf(resultSetType), Integer.valueOf(resultSetConcurrency));
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                try {
                    requireOpenConnection();
                    requireNonNullSql(sql);
                    OracleResultSet.ResultSetType resultSetTypeEnum = OracleResultSet.ResultSetType.typeFor(resultSetType, resultSetConcurrency);
                    OracleCallableStatement internalStatement = prepareCallInternal(sql, resultSetTypeEnum);
                    OracleCallableStatementWrapper oracleCallableStatementWrapper = new OracleCallableStatementWrapper(internalStatement);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "prepareCall", "returning {0}", (String) null, (String) null, oracleCallableStatementWrapper);
                    return oracleCallableStatementWrapper;
                } finally {
                }
            } finally {
            }
        } catch (Throwable th3) {
            debug(Level.FINER, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "prepareCall", "throwing", (String) null, (String) th3, new Object[0]);
            throw th3;
        }
    }

    final OracleCallableStatement prepareCallInternal(String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        long startToken = DMSFactory.getInstance().getToken();
        DMSFactory.DMSPhase dmsSensor = this.dmsCreateNewStatement;
        OracleCallableStatement internalStatement = (OracleCallableStatement) getCachedStatement(sql, 2, resultSetType);
        if (internalStatement == null) {
            OracleCallableStatement allocatedStatement = (OracleCallableStatement) this.driverExtension.allocateCallableStatement(this, sql, resultSetType);
            internalStatement = allocatedStatement;
        } else {
            dmsSensor = this.dmsCreateStatement;
        }
        dmsSensor.start(startToken);
        dmsSensor.stop(startToken);
        return internalStatement;
    }

    private void requireNonNullSql(String sql) throws SQLException {
        if (sql == null || sql.length() == 0) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 104).fillInStackTrace());
        }
    }

    private OracleStatement getCachedStatement(String sql, int statementType, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        int iOrdinal;
        if (this.statementCache == null) {
            return null;
        }
        if (resultSetType == OracleResultSet.ResultSetType.UNKNOWN) {
            iOrdinal = OracleStatement.DEFAULT_RESULT_SET_TYPE.ordinal();
        } else {
            iOrdinal = resultSetType.ordinal();
        }
        int resultSetTypeOrdinal = iOrdinal;
        OracleStatement statement = this.statementCache.searchImplicitCache(sql, statementType, resultSetTypeOrdinal, this);
        if (statement != null) {
            debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getCachedStatement", "retrieved from implicit cache. sql={0}.", (String) null, (String) null, sql);
        }
        return statement;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public CallableStatement prepareCallWithKey(String key) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (key == null) {
                return null;
            }
            if (!isStatementCacheInitialized()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 95).fillInStackTrace());
            }
            long startToken = DMSFactory.getInstance().getToken();
            DMSFactory.DMSPhase dmsSensor = this.dmsCreateNewStatement;
            CallableStatement cstmt = (OracleCallableStatement) this.statementCache.searchExplicitCache(key);
            if (cstmt != null) {
                debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "prepareCallWithKey", "retrieved statement from cache for key={0}. ", (String) null, (String) null, key);
                dmsSensor = this.dmsCreateStatement;
            }
            dmsSensor.start(startToken);
            dmsSensor.stop(startToken);
            if (cstmt != null) {
                cstmt = new OracleCallableStatementWrapper((oracle.jdbc.OracleCallableStatement) cstmt);
            }
            CallableStatement callableStatement = cstmt;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return callableStatement;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public String nativeSQL(String sql) throws SQLException {
        if (this.sqlObj == null) {
            this.sqlObj = new OracleSql(this.conversion, this.allowMixingJdbcAndNamedBinds);
        }
        this.sqlObj.initialize(sql);
        String osql = this.sqlObj.getSql(this.processEscapes, this.convertNcharLiterals);
        return osql;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void setAutoCommit(boolean autoCommit) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                requireOpenConnection();
                if (this.autocommit != autoCommit) {
                    if (autoCommit) {
                        disallowGlobalTxnMode(DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN);
                    }
                    needLine();
                    doSetAutoCommit(autoCommit);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th4) {
                th = th4;
                throw th4;
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public boolean getAutoCommit() throws SQLException {
        requireOpenConnection();
        return this.autocommit;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean getAutoCommitInternal() throws SQLException {
        return this.autocommit;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void cancel() throws SQLException {
        if (this.lifecycle != 1 && this.lifecycle != 16) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 8).fillInStackTrace());
        }
        boolean connectionHasBeenCancelled = false;
        for (OracleStatement stmt = this.statements; stmt != null; stmt = stmt.next) {
            try {
                if (stmt.doCancel()) {
                    connectionHasBeenCancelled = true;
                }
            } catch (SQLException e) {
                debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "cancel", null, (String) null, e);
            }
        }
        if (!connectionHasBeenCancelled) {
            cancelOperationOnServer(false);
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void commit(EnumSet<OracleConnection.CommitOption> options) throws SQLException {
        int optionsAsInt = 0;
        if (options != null) {
            if ((options.contains(OracleConnection.CommitOption.WRITEBATCH) && options.contains(OracleConnection.CommitOption.WRITEIMMED)) || (options.contains(OracleConnection.CommitOption.WAIT) && options.contains(OracleConnection.CommitOption.NOWAIT))) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 191).fillInStackTrace());
            }
            Iterator it = options.iterator();
            while (it.hasNext()) {
                OracleConnection.CommitOption option = (OracleConnection.CommitOption) it.next();
                optionsAsInt |= option.getCode();
            }
        }
        commit(optionsAsInt);
    }

    void commit(int flags) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "commit(int)", "flags={0}. ", (String) null, (String) null, Integer.valueOf(flags));
            disallowGlobalTxnMode(114);
            if (this.autoCommitSpecCompliant && getAutoCommit()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_CALLCOMMIT_WITH_AUTOCOMMIT).fillInStackTrace());
            }
            requireOpenConnection();
            validateCommitOptionFlags(flags);
            needLine();
            doCommit(flags);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private final void validateCommitOptionFlags(int optionFlags) throws SQLException {
        if (((optionFlags & OracleConnection.CommitOption.WRITEBATCH.getCode()) != 0 && (optionFlags & OracleConnection.CommitOption.WRITEIMMED.getCode()) != 0) || ((optionFlags & OracleConnection.CommitOption.WAIT.getCode()) != 0 && (optionFlags & OracleConnection.CommitOption.NOWAIT.getCode()) != 0)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 191).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void commit() throws SQLException {
        commit(this.commitOption);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void rollback() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "rollback", null, null, null);
            disallowGlobalTxnMode(115);
            if (this.autoCommitSpecCompliant && getAutoCommit()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_CALLROLLBACK_WITH_AUTOCOMMIT).fillInStackTrace());
            }
            requireOpenConnection();
            needLine();
            doRollback();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection, java.lang.AutoCloseable
    public void close() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                doClose();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    protected void doClose() throws SQLException {
        assertLockHeldByCurrentThread();
        if (this.lifecycle == 2 || this.lifecycle == 4) {
            return;
        }
        needLineUnchecked();
        try {
            prepareForLogoff();
            logoff();
            cleanup();
            if (this.timeout != null) {
                this.timeout.close();
            }
            if (this.closeCallback != null) {
                this.closeCallback.afterClose(this.privateData);
            }
        } finally {
            setLifecycle(4);
            this.isUsable = false;
            this.dmsCloseCount.occurred();
            this.dmsParent.destroy();
            this.dmsParent = null;
        }
    }

    private void prepareForLogoff() throws SQLException {
        prepareForLogoff(true);
    }

    private void prepareForLogoff(boolean isClosingDependents) throws SQLException {
        if (this.lifecycle != 8) {
            getHAManager().dropConnection(this);
        }
        if (this.closeCallback != null) {
            this.closeCallback.beforeClose(this, this.privateData);
        }
        if (isClosingDependents) {
            closeDependents();
        }
        if (this.isProxy) {
            close(1);
        }
        if (this.lifecycle == 1) {
            setLifecycle(2);
        }
    }

    private void closeDependents() throws SQLException {
        SQLException closeException = null;
        try {
            closeStatementCache();
        } catch (SQLException sqlException) {
            closeException = sqlException;
        }
        try {
            closeStatements(false);
        } catch (SQLException sqlException2) {
            if (closeException == null) {
                closeException = sqlException2;
            } else {
                closeException.addSuppressed(sqlException2);
            }
        }
        try {
            freeTemporaryBlobsAndClobs();
        } catch (SQLException sqlException3) {
            if (closeException == null) {
                closeException = sqlException3;
            } else {
                closeException.addSuppressed(sqlException3);
            }
        }
        try {
            if (this.timeZoneTab != null) {
                this.timeZoneTab.freeInstance();
            }
        } catch (SQLException sqlException4) {
            if (closeException == null) {
                closeException = sqlException4;
            } else {
                closeException.addSuppressed(sqlException4);
            }
        }
        if (closeException != null) {
            throw closeException;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getDataIntegrityAlgorithmName() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getDataIntegrityAlgorithmName").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getEncryptionAlgorithmName() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getEncryptionAlgorithmName").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getAuthenticationAdaptorName() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getAuthenticationAdaptorName").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void closeInternal(boolean putPhysicalConnBackInCache) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("closeInternal").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void cleanupAndClose(boolean putPhysicalConnBackInCache) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("cleanupAndClose").fillInStackTrace());
    }

    public void cleanupAndClose() throws SQLException {
        if (this.lifecycle != 1) {
            return;
        }
        setLifecycle(16);
        cancel();
    }

    public void closeLogicalConnection() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.lifecycle == 1 || this.lifecycle == 16 || this.lifecycle == 2) {
                closeStatements(true);
                freeTemporaryBlobsAndClobs();
                this.logicalConnectionAttached = null;
                setLifecycle(1);
                this.dmsLogicalConnection.update(null);
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void close(int opt) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.lifecycle == 2 || this.lifecycle == 4) {
                if (lock != null) {
                    if (0 == 0) {
                        lock.close();
                        return;
                    }
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                return;
            }
            if ((opt & 4096) != 0) {
                close();
                if (lock != null) {
                    if (0 == 0) {
                        lock.close();
                        return;
                    }
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                        return;
                    }
                }
                return;
            }
            if ((opt & 1) != 0 && this.isProxy) {
                purgeStatementCache();
                closeStatements(false);
                Hashtable<String, Object>[] hashtableArr = this.descriptorCacheStack;
                int i = this.descriptorCacheTop;
                this.descriptorCacheTop = i - 1;
                hashtableArr[i] = null;
                closeProxySession();
                this.isProxy = false;
                this.autocommit = this.savedAutoCommitFlag;
                this.txnMode = this.savedTxnMode;
            }
            if (lock != null) {
                if (0 == 0) {
                    lock.close();
                    return;
                }
                try {
                    lock.close();
                } catch (Throwable th4) {
                    th.addSuppressed(th4);
                }
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void abort() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            SecurityManager security = System.getSecurityManager();
            if (security != null) {
                security.checkPermission(CALL_ORACLE_ABORT_PERMISSION);
            }
            if (this.lifecycle != 4 && this.lifecycle != 8) {
                setLifecycle(8);
                doAbort();
                this.isUsable = false;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                    return;
                }
                return;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.GeneratedPhysicalConnection, oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public Set<String> getProviderAllowedProperties() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Set<String> providerAllowedProperties = super.getProviderAllowedProperties();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return providerAllowedProperties;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void closeProxySession() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("closeProxySession").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public PreparedStatement prepareDirectPath(String schemaName, String tableName, String[] colNames) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("prepareDirectPath").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public PreparedStatement prepareDirectPath(String schemaName, String tableName, String[] colNames, String partitionName) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("prepareDirectPath").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public PreparedStatement prepareDirectPath(String schemaName, String tableName, String[] colNames, @Blind(PropertiesBlinder.class) Properties dpStmtProps) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("prepareDirectPath").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public PreparedStatement prepareDirectPath(String schemaName, String tableName, String[] colNames, String partitionName, @Blind(PropertiesBlinder.class) Properties dpStmtProps) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("prepareDirectPath").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final boolean isClosed() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean zIsClosedInternal = isClosedInternal();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zIsClosedInternal;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    final boolean isClosedInternal() {
        assertLockHeldByCurrentThread();
        return this.lifecycle != 1;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean isProxySession() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean z = this.isProxy;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void openProxySession(int type, @Blind(PropertiesBlinder.class) Properties prop) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int proxyError = 1;
            if (this.isProxy) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_ALREADY_PROXY).fillInStackTrace());
            }
            Properties prop2 = (Properties) prop.clone();
            if (type == 1) {
                String user = prop2.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_NAME);
                if (user == null || user.length() == 0) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150, "PROXY_USER_NAME cannot be null or empty.").fillInStackTrace());
                }
                Matcher userMatcher = USERNAME_PATTERN.matcher(user);
                if (!userMatcher.lookingAt()) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150, "Unrecognized format for PROXY_USER_NAME").fillInStackTrace());
                }
                int afterUser = userMatcher.end();
                if (afterUser < user.length()) {
                    if (!user.substring(afterUser, afterUser + 1).equals("/")) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150, "Unrecognized format for PROXY_USER_NAME").fillInStackTrace());
                    }
                    String passwd = prop2.getProperty(oracle.jdbc.OracleConnection.PROXY_USER_PASSWORD);
                    if (passwd != null && passwd.length() > 0) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150, "Password cannot be specified in both PROXY_USER_NAME and PROXY_USER_PASSWORD").fillInStackTrace());
                    }
                    prop2.setProperty(oracle.jdbc.OracleConnection.PROXY_USER_NAME, user.substring(0, afterUser));
                    prop2.setProperty(oracle.jdbc.OracleConnection.PROXY_USER_PASSWORD, user.substring(afterUser + 1));
                }
            } else if (type == 2) {
                String distName = prop2.getProperty(oracle.jdbc.OracleConnection.PROXY_DISTINGUISHED_NAME);
                if (distName == null || distName.length() == 0) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150, "PROXY_DISTINGUISHED_NAME cannot be null or empty.").fillInStackTrace());
                }
            } else if (type == 3) {
                Object certif = prop2.get(oracle.jdbc.OracleConnection.PROXY_CERTIFICATE);
                if (!(certif instanceof byte[])) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150, "PROXY_CERTIFICATE must be a provided as a byte[]").fillInStackTrace());
                }
                prop2.put(oracle.jdbc.OracleConnection.PROXY_CERTIFICATE, ((byte[]) certif).clone());
            } else {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150, "Unrecognized type argument: " + type).fillInStackTrace());
            }
            Object roles = prop2.get(oracle.jdbc.OracleConnection.PROXY_ROLES);
            if (roles instanceof String[]) {
                prop2.put(oracle.jdbc.OracleConnection.PROXY_ROLES, ((String[]) roles).clone());
            } else if (roles != null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 150, "PROXY_ROLES must be provided as a String[]").fillInStackTrace());
            }
            purgeStatementCache();
            closeStatements(false);
            try {
                doProxySession(type, prop2);
                this.descriptorCacheTop++;
                this.savedAutoCommitFlag = this.autocommit;
                this.autocommit = this.defaultautocommit;
                this.savedTxnMode = this.txnMode;
                this.txnMode = 0;
                proxyError = 0;
                this.currentSchema = null;
                if (0 == 1 && !isClosed()) {
                    closeProxySession();
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                if (proxyError == 1 && !isClosed()) {
                    closeProxySession();
                }
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void doProxySession(int type, @Blind(PropertiesBlinder.class) Properties prop) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doProxySession").fillInStackTrace());
    }

    void cleanup() {
        this.fdo = null;
        this.conversion = null;
        this.statements = null;
        this.descriptorCacheStack[this.descriptorCacheTop] = null;
        this.sqlTypeToJavaClassMap = null;
        this.javaClassNameToSqlTypeMap = null;
        this.javaObjectMap = null;
        this.statementHoldingLine = null;
        this.sqlObj = null;
        this.isProxy = false;
        this.blockSource = null;
        this.connectionBufferCacheStore = null;
        threadLocalBufferCacheStore = null;
        this.tmpByteBuf = null;
        this.tmpCharBuf = null;
        this.currentSchema = null;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public DatabaseMetaData getMetaData() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            DatabaseMetaData databaseMetaDataDoGetMetaData = doGetMetaData();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return databaseMetaDataDoGetMetaData;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    protected DatabaseMetaData doGetMetaData() throws SQLException {
        requireOpenConnection();
        if (this.databaseMetaData == null) {
            this.databaseMetaData = new OracleDatabaseMetaData((OracleConnection) this);
        }
        return this.databaseMetaData;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void setReadOnly(boolean value) throws SQLException {
        requireOpenConnection();
        this.readOnly = value;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public boolean isReadOnly() throws SQLException {
        requireOpenConnection();
        return this.readOnly;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void setCatalog(String catalog) throws SQLException {
        requireOpenConnection();
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public String getCatalog() throws SQLException {
        requireOpenConnection();
        return null;
    }

    /* JADX WARN: Finally extract failed */
    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void setTransactionIsolation(int level) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (this.txnLevel != level) {
                Statement ostmt = createStatement();
                try {
                    switch (level) {
                        case 2:
                            ostmt.execute("ALTER SESSION SET ISOLATION_LEVEL = READ COMMITTED");
                            this.txnLevel = 2;
                            break;
                        case 8:
                            this.isResultSetCacheEnabled = false;
                            ostmt.execute("ALTER SESSION SET ISOLATION_LEVEL = SERIALIZABLE");
                            this.txnLevel = 8;
                            break;
                        default:
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 30).fillInStackTrace());
                    }
                    ostmt.close();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                } catch (Throwable th3) {
                    ostmt.close();
                    throw th3;
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public int getTransactionIsolation() throws SQLException {
        requireOpenConnection();
        return this.txnLevel;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setAutoClose(boolean autoClose) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (!autoClose) {
            try {
                try {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 31).fillInStackTrace());
                } catch (Throwable th2) {
                    th = th2;
                    throw th2;
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                    return;
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                    return;
                }
            }
            lock.close();
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Can't find top splitter block for handler:B:22:0x003f
        	at jadx.core.utils.BlockUtils.getTopSplitterForHandler(BlockUtils.java:1178)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.collectHandlerRegions(ExcHandlersRegionMaker.java:53)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.process(ExcHandlersRegionMaker.java:38)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:27)
        */
    /* JADX WARN: Unreachable blocks removed: 14, instructions: 21 */
    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getAutoClose() throws java.sql.SQLException {
        /*
            r3 = this;
            r0 = r3
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r4 = r0
            r0 = 0
            r5 = r0
            r0 = 1
            r6 = r0
            r0 = r4
            if (r0 == 0) goto L27
            r0 = r5
            if (r0 == 0) goto L23
            r0 = r4
            r0.close()     // Catch: java.lang.Throwable -> L18
            goto L27
        L18:
            r7 = move-exception
            r0 = r5
            r1 = r7
            r0.addSuppressed(r1)
            goto L27
        L23:
            r0 = r4
            r0.close()
        L27:
            r0 = r6
            return r0
        L29:
            r6 = move-exception
            r0 = r6
            r5 = r0
            r0 = r6
            throw r0     // Catch: java.lang.Throwable -> L2e
        L2e:
            r8 = move-exception
            r0 = r4
            if (r0 == 0) goto L4e
            r0 = r5
            if (r0 == 0) goto L4a
            r0 = r4
            r0.close()     // Catch: java.lang.Throwable -> L3f
            goto L4e
        L3f:
            r9 = move-exception
            r0 = r5
            r1 = r9
            r0.addSuppressed(r1)
            goto L4e
        L4a:
            r0 = r4
            r0.close()
        L4e:
            r0 = r8
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.PhysicalConnection.getAutoClose():boolean");
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public SQLWarning getWarnings() throws SQLException {
        requireOpenConnection();
        return this.sqlWarning;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void clearWarnings() throws SQLException {
        requireOpenConnection();
        this.sqlWarning = null;
    }

    void setWarnings(SQLWarning warn) {
        this.sqlWarning = warn;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setDefaultRowPrefetch(int value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (value <= 0) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 20).fillInStackTrace());
            }
            this.defaultRowPrefetch = value;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public int getDefaultRowPrefetch() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int i = this.defaultRowPrefetch;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return i;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean getTimestamptzInGmt() {
        return this.timestamptzInGmt;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean getUse1900AsYearForTime() {
        return this.use1900AsYearForTime;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setDefaultExecuteBatch(int batch) throws SQLException {
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Can't find top splitter block for handler:B:22:0x003f
        	at jadx.core.utils.BlockUtils.getTopSplitterForHandler(BlockUtils.java:1178)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.collectHandlerRegions(ExcHandlersRegionMaker.java:53)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.process(ExcHandlersRegionMaker.java:38)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:27)
        */
    /* JADX WARN: Unreachable blocks removed: 14, instructions: 21 */
    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public int getDefaultExecuteBatch() {
        /*
            r3 = this;
            r0 = r3
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r4 = r0
            r0 = 0
            r5 = r0
            r0 = 1
            r6 = r0
            r0 = r4
            if (r0 == 0) goto L27
            r0 = r5
            if (r0 == 0) goto L23
            r0 = r4
            r0.close()     // Catch: java.lang.Throwable -> L18
            goto L27
        L18:
            r7 = move-exception
            r0 = r5
            r1 = r7
            r0.addSuppressed(r1)
            goto L27
        L23:
            r0 = r4
            r0.close()
        L27:
            r0 = r6
            return r0
        L29:
            r6 = move-exception
            r0 = r6
            r5 = r0
            r0 = r6
            throw r0     // Catch: java.lang.Throwable -> L2e
        L2e:
            r8 = move-exception
            r0 = r4
            if (r0 == 0) goto L4e
            r0 = r5
            if (r0 == 0) goto L4a
            r0 = r4
            r0.close()     // Catch: java.lang.Throwable -> L3f
            goto L4e
        L3f:
            r9 = move-exception
            r0 = r5
            r1 = r9
            r0.addSuppressed(r1)
            goto L4e
        L4a:
            r0 = r4
            r0.close()
        L4e:
            r0 = r8
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.PhysicalConnection.getDefaultExecuteBatch():int");
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setRemarksReporting(boolean _reportRemarks) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.reportRemarks = _reportRemarks;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getRemarksReporting() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean z = this.reportRemarks;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setIncludeSynonyms(boolean synonyms) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.includeSynonyms = synonyms;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String[] getEndToEndMetrics() throws SQLException {
        String[] result;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.endToEndValues == null) {
                result = null;
            } else {
                result = new String[4];
                System.arraycopy(this.endToEndValues, 0, result, 0, 4);
            }
            return result;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public short getEndToEndECIDSequenceNumber() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            short s = this.endToEndECIDSequenceNumber;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return s;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setEndToEndMetrics(String[] metrics, short sequenceNumber) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.dmsVersion.equals(DMSFactory.DMSVersion.NONE)) {
                    String[] copyMetrics = new String[metrics.length];
                    System.arraycopy(metrics, 0, copyMetrics, 0, metrics.length);
                    setEndToEndMetricsInternal(copyMetrics, sequenceNumber);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void setEndToEndMetricsInternal(String[] metrics, short sequenceNumber) throws SQLException {
        if (metrics != this.endToEndValues) {
            if (metrics.length != 4) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 156).fillInStackTrace());
            }
            for (int i = 0; i < 4; i++) {
                String s = metrics[i];
                if (s != null && s.length() > this.endToEndMaxLength[i]) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 159, s).fillInStackTrace());
                }
            }
            if (this.endToEndValues != null) {
                for (int i2 = 0; i2 < 4; i2++) {
                    String s2 = metrics[i2];
                    if ((s2 == null && this.endToEndValues[i2] != null) || (s2 != null && !s2.equals(this.endToEndValues[i2]))) {
                        this.endToEndHasChanged[i2] = true;
                        this.endToEndAnyChanged = true;
                    }
                }
                boolean[] zArr = this.endToEndHasChanged;
                zArr[0] = zArr[0] | this.endToEndHasChanged[3];
            } else {
                for (int i3 = 0; i3 < 4; i3++) {
                    this.endToEndHasChanged[i3] = true;
                }
                this.endToEndAnyChanged = true;
            }
            System.arraycopy(metrics, 0, this.endToEndValues, 0, 4);
            for (int i4 = 0; i4 < 4; i4++) {
                if (metrics[i4] == null) {
                    this.clientInfo.remove(END_TO_END_CLIENTINFO_KEYS[i4]);
                } else {
                    this.clientInfo.put(END_TO_END_CLIENTINFO_KEYS[i4], metrics[i4]);
                }
            }
        }
        this.endToEndECIDSequenceNumber = sequenceNumber;
        this.clientInfo.put(END_TO_END_CLIENTINFO_KEY_SEQ_NO, Short.toString(sequenceNumber));
        updateTraceAttributes();
    }

    void updateSystemContext() throws SQLException {
        if (this.dmsVersion.equals(DMSFactory.DMSVersion.v10G)) {
            String[] metrics = DMSFactory.getExecutionContextForJDBC().getExecutionContextState();
            if (metrics != null) {
                setEndToEndMetricsInternal(metrics, (short) DMSFactory.getExecutionContextForJDBC().getECIDSequenceNumber());
                return;
            }
            return;
        }
        if (this.dmsVersion.equals(DMSFactory.DMSVersion.v11)) {
            if (getVersionNumber() >= 11000) {
                updateSystemContext11();
                return;
            }
            Map<String, Map<String, String>> desired = DMSFactory.Context.getECForJDBC().getMap();
            Map<String, String> m = desired.get("E2E_CONTEXT");
            if (m != null && m.size() != 0) {
                String[] metrics2 = new String[4];
                for (int i = 0; i < e2eKeys.length; i++) {
                    metrics2[i] = m.get(e2eKeys[i]);
                }
                setEndToEndMetricsInternal(metrics2, (short) 0);
            }
        }
    }

    void resetSystemContext() {
        for (Map.Entry<String, Map<String, String>> nsEntry : this.currentSystemContext.entrySet()) {
            String namespace = nsEntry.getKey();
            if (namespace != null && !namespace.isEmpty()) {
                try {
                    doClearAllApplicationContext(namespace);
                } catch (SQLException e) {
                }
            }
        }
        this.currentSystemContext = new IdentityHashMap();
    }

    void updateSystemContext11() throws SQLException {
        Map<String, Map<String, String>> desired = new IdentityHashMap<>(DMSFactory.Context.getECForJDBC().getMap());
        Map<String, Map<String, String>> current = new IdentityHashMap<>(this.currentSystemContext);
        for (Map.Entry<String, Map<String, String>> desiredEntry : desired.entrySet()) {
            String namespace = desiredEntry.getKey();
            Map<String, String> d = desiredEntry.getValue();
            Map<String, String> cur = current.remove(namespace);
            if (cur == null) {
                if (d != null && !d.isEmpty()) {
                    cur = new IdentityHashMap();
                    for (Map.Entry<String, String> de : d.entrySet()) {
                        String k = de.getKey();
                        String dv = de.getValue();
                        doSetApplicationContext(namespace, k, dv);
                        cur.put(k, dv);
                    }
                }
            } else {
                Map<String, String> c = new IdentityHashMap<>(cur);
                for (Map.Entry<String, String> de2 : d.entrySet()) {
                    String k2 = de2.getKey();
                    String dv2 = de2.getValue();
                    String cv = c.remove(k2);
                    if (dv2 != cv) {
                        doSetApplicationContext(namespace, k2, dv2);
                        cur.put(k2, dv2);
                    }
                }
                if (d.isEmpty()) {
                    doClearAllApplicationContext(namespace);
                    this.currentSystemContext.remove(namespace);
                } else {
                    for (String k3 : c.keySet()) {
                        doSetApplicationContext(namespace, k3, "");
                        cur.remove(k3);
                    }
                }
            }
            if (cur != null && !cur.isEmpty()) {
                this.currentSystemContext.put(namespace, cur);
            }
        }
        for (String n : current.keySet()) {
            doClearAllApplicationContext(n);
            this.currentSystemContext.remove(n);
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getIncludeSynonyms() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean z = this.includeSynonyms;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setRestrictGetTables(boolean restrict) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.restrictGettables = restrict;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getRestrictGetTables() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean z = this.restrictGettables;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setDefaultFixedString(boolean fixed) {
        this.fixedString = fixed;
    }

    void setDefaultNChar(boolean defnchar) {
        this.defaultnchar = defnchar;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean getDefaultFixedString() {
        return this.fixedString;
    }

    int getNlsRatio() {
        return 1;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getC2SNlsRatio() {
        return 1;
    }

    void addStatement(OracleStatement os) {
        assertLockHeldByCurrentThread();
        if (os.next != null) {
            throw new Error("add_statement called twice on " + os);
        }
        os.next = this.statements;
        if (this.statements != null) {
            this.statements.prev = os;
        }
        this.statements = os;
    }

    void removeStatement(OracleStatement os) {
        assertLockHeldByCurrentThread();
        OracleStatement p = os.prev;
        OracleStatement n = os.next;
        if (p == null) {
            if (this.statements != os) {
                return;
            } else {
                this.statements = n;
            }
        } else {
            p.next = n;
        }
        if (n != null) {
            n.prev = p;
        }
        os.next = null;
        os.prev = null;
    }

    void closeStatements(boolean needToCache) throws SQLException {
        assertLockHeldByCurrentThread();
        closeStatements(needToCache, true);
    }

    void closeStatements(boolean needToCache, boolean removeAll) throws SQLException {
        assertLockHeldByCurrentThread();
        OracleStatement oracleStatement = this.statements;
        while (true) {
            OracleStatement s = oracleStatement;
            if (s == null) {
                break;
            }
            OracleStatement n = s.nextChild;
            if (s.serverCursor) {
                s.closeOrCache(null);
                if (removeAll) {
                    removeStatement(s);
                }
            }
            oracleStatement = n;
        }
        OracleStatement oracleStatement2 = this.statements;
        while (true) {
            OracleStatement s2 = oracleStatement2;
            if (s2 != null) {
                OracleStatement n2 = s2.next;
                if (needToCache) {
                    s2.closeWrapper(isClosedInternal());
                    s2.closeOrCache(null);
                } else {
                    s2.hardClose();
                    s2.closeWrapper(isClosedInternal());
                }
                if (removeAll) {
                    removeStatement(s2);
                }
                oracleStatement2 = n2;
            } else {
                return;
            }
        }
    }

    final void purgeStatementCache() throws SQLException {
        if (isStatementCacheInitialized()) {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "purgeStatementCache", (String) null, (String) null, null);
            this.statementCache.purgeImplicitCache();
            this.statementCache.purgeExplicitCache();
        }
    }

    final void closeStatementCache() throws SQLException {
        if (isStatementCacheInitialized()) {
            this.statementCache.close();
            this.statementCache = null;
            this.clearStatementMetaData = true;
        }
    }

    void needLine() throws SQLException {
        requireOpenConnection();
        needLineUnchecked();
    }

    void needLineUnchecked() throws SQLException {
        if (this.statementHoldingLine != null) {
            this.statementHoldingLine.freeLine();
        }
    }

    void holdLine(oracle.jdbc.internal.OracleStatement stmt) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                holdLine((OracleStatement) stmt);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void holdLine(OracleStatement statement) {
        this.statementHoldingLine = statement;
    }

    void releaseLine() {
        assertLockHeldByCurrentThread();
        releaseLineForCancel();
    }

    void releaseLineForCancel() {
        this.statementHoldingLine = null;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void startup(String startup_str, int mode) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                requireOpenConnection();
                throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("startup").fillInStackTrace());
            } finally {
            }
        } catch (Throwable th2) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void startup(OracleConnection.DatabaseStartupMode mode) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (mode == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            needLine();
            doStartup(mode.getMode());
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void startup(OracleConnection.DatabaseStartupMode mode, String pfileName) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (mode == null || pfileName == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            needLine();
            doStartup(mode.getMode(), pfileName);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void doStartup(int mode) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doStartup").fillInStackTrace());
    }

    void doStartup(int mode, String pfileName) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doStartup").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void shutdown(OracleConnection.DatabaseShutdownMode mode) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (mode == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            needLine();
            doShutdown(mode.getMode());
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void doShutdown(int mode) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doShutdown").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void archive(int mode, int aseq, String acstext) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                requireOpenConnection();
                throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("archive").fillInStackTrace());
            } finally {
            }
        } catch (Throwable th2) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void registerSQLType(String sql_name, String java_class_name) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (sql_name == null || java_class_name == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            try {
                registerSQLType(sql_name, Class.forName(java_class_name));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (ClassNotFoundException e) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, "Class not found: " + java_class_name).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void registerSQLType(String sql_name, Class<?> java_class) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (sql_name == null || java_class == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
            if (this.sqlTypeToJavaClassMap == null) {
                this.sqlTypeToJavaClassMap = new Hashtable(10);
            }
            this.sqlTypeToJavaClassMap.put(sql_name, java_class);
            if (this.javaClassNameToSqlTypeMap == null) {
                this.javaClassNameToSqlTypeMap = new Hashtable(10);
            }
            this.javaClassNameToSqlTypeMap.put(java_class.getName(), sql_name);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getSQLType(Object obj) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (obj != null) {
            try {
                try {
                    if (this.javaClassNameToSqlTypeMap != null) {
                        String java_class_name = obj.getClass().getName();
                        String str = this.javaClassNameToSqlTypeMap.get(java_class_name);
                        if (lock != null) {
                            if (0 != 0) {
                                try {
                                    lock.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                lock.close();
                            }
                        }
                        return str;
                    }
                } finally {
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                }
            } else {
                lock.close();
            }
        }
        return null;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public Object getJavaObject(String sql_name) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Object obj = null;
            if (sql_name != null) {
                try {
                    if (this.sqlTypeToJavaClassMap != null) {
                        Class<?> java_class = this.sqlTypeToJavaClassMap.get(sql_name);
                        obj = java_class.newInstance();
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InstantiationException e2) {
                    e2.printStackTrace();
                }
            }
            return obj;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void putDescriptor(String sql_name, Object desc) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (sql_name != null && desc != null) {
                if (this.descriptorCacheStack[this.descriptorCacheTop] == null) {
                    this.descriptorCacheStack[this.descriptorCacheTop] = new Hashtable<>(10);
                }
                ((TypeDescriptor) desc).fixupConnection(this);
                this.descriptorCacheStack[this.descriptorCacheTop].put(sql_name, desc);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                    return;
                }
                return;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public Object getDescriptor(String sql_name) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                Object objDoGetDescriptor = doGetDescriptor(sql_name);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return objDoGetDescriptor;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.internal.OracleConnection
    public Object doGetDescriptor(String sql_name) {
        Object desc = null;
        if (sql_name != null) {
            if (this.descriptorCacheStack[this.descriptorCacheTop] != null) {
                desc = this.descriptorCacheStack[this.descriptorCacheTop].get(sql_name);
            }
            if (desc == null && this.descriptorCacheTop == 1 && this.descriptorCacheStack[0] != null) {
                desc = this.descriptorCacheStack[0].get(sql_name);
            }
        }
        return desc;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void removeDescriptor(String sql_name) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (sql_name != null) {
            try {
                try {
                    if (this.descriptorCacheStack[this.descriptorCacheTop] != null) {
                        this.descriptorCacheStack[this.descriptorCacheTop].remove(sql_name);
                    }
                } catch (Throwable th2) {
                    th = th2;
                    throw th2;
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (sql_name != null && this.descriptorCacheTop == 1 && this.descriptorCacheStack[0] != null) {
            this.descriptorCacheStack[0].remove(sql_name);
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                    return;
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                    return;
                }
            }
            lock.close();
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void removeDescriptor(byte[] toid) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        if (toid != null) {
            try {
                try {
                    String k = toidToString(toid);
                    if (this.descriptorCacheStack[this.descriptorCacheTop] != null) {
                        this.descriptorCacheStack[this.descriptorCacheTop].remove(k);
                    }
                    if (this.descriptorCacheTop == 1 && this.descriptorCacheStack[0] != null) {
                        this.descriptorCacheStack[0].remove(k);
                    }
                } catch (Throwable th2) {
                    th = th2;
                    throw th2;
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }
        if (lock != null) {
            if (0 != 0) {
                try {
                    lock.close();
                    return;
                } catch (Throwable th5) {
                    th.addSuppressed(th5);
                    return;
                }
            }
            lock.close();
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void removeAllDescriptor() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            for (int i = 0; i <= this.descriptorCacheTop; i++) {
                if (this.descriptorCacheStack[i] != null) {
                    this.descriptorCacheStack[i].clear();
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int numberOfDescriptorCacheEntries() {
        int result = 0;
        for (int i = 0; i <= this.descriptorCacheTop; i++) {
            if (this.descriptorCacheStack[i] != null) {
                result += this.descriptorCacheStack[i].size();
            }
        }
        return result;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Enumeration<String> descriptorCacheKeys() {
        if (this.descriptorCacheTop == 0) {
            if (this.descriptorCacheStack[this.descriptorCacheTop] != null) {
                return this.descriptorCacheStack[this.descriptorCacheTop].keys();
            }
            return null;
        }
        if (this.descriptorCacheStack[0] == null && this.descriptorCacheStack[1] != null) {
            return this.descriptorCacheStack[1].keys();
        }
        if (this.descriptorCacheStack[1] == null && this.descriptorCacheStack[0] != null) {
            return this.descriptorCacheStack[0].keys();
        }
        if (this.descriptorCacheStack[0] == null && this.descriptorCacheStack[1] == null) {
            return null;
        }
        Vector<String> v = new Vector<>(this.descriptorCacheStack[1].keySet());
        v.addAll(this.descriptorCacheStack[0].keySet());
        return v.elements();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void putDescriptor(byte[] toid, Object desc) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (toid != null && desc != null) {
                if (this.descriptorCacheStack[this.descriptorCacheTop] == null) {
                    this.descriptorCacheStack[this.descriptorCacheTop] = new Hashtable<>(10);
                }
                this.descriptorCacheStack[this.descriptorCacheTop].put(toidToString(toid), desc);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                    return;
                }
                return;
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private static final String toidToString(byte[] toid) {
        return "TOID��" + new String(toid, 0);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Object getDescriptor(byte[] toid) {
        assertLockHeldByCurrentThread();
        Object desc = null;
        if (toid != null) {
            String k = toidToString(toid);
            if (this.descriptorCacheStack[this.descriptorCacheTop] != null) {
                desc = this.descriptorCacheStack[this.descriptorCacheTop].get(k);
            }
            if (desc == null && this.descriptorCacheTop == 1 && this.descriptorCacheStack[0] != null) {
                desc = this.descriptorCacheStack[0].get(k);
            }
        }
        return desc;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public short getJdbcCsId() throws SQLException {
        if (this.conversion == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 65).fillInStackTrace());
        }
        return this.conversion.getClientCharSet();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public short getDbCsId() throws SQLException {
        if (this.conversion == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 65).fillInStackTrace());
        }
        return this.conversion.getServerCharSetId();
    }

    short getNCsId() throws SQLException {
        if (this.conversion == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 65).fillInStackTrace());
        }
        return this.conversion.getNCharSetId();
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public short getStructAttrCsId() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            short dbCsId = getDbCsId();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return dbCsId;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public short getStructAttrNCsId() throws SQLException {
        return getNCsId();
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public Map<String, Class<?>> getTypeMap() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (this.sqlTypeToJavaClassMap == null) {
                this.sqlTypeToJavaClassMap = new Hashtable(10);
            }
            Map<String, Class<?>> map = this.sqlTypeToJavaClassMap;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return map;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void setTypeMap(Map<String, Class<?>> map) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                requireOpenConnection();
                this.sqlTypeToJavaClassMap = map;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setUsingXAFlag(boolean value) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.usingXA = value;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getUsingXAFlag() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean z = this.usingXA;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setXAErrorFlag(boolean value) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.xaWantsError = value;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getXAErrorFlag() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean z = this.xaWantsError;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    String getPropertyFromDatabase(String sql) throws SQLException {
        String returnValue = null;
        Statement stmt = null;
        ResultSet rs = null;
        beginNonRequestCalls();
        try {
            stmt = createStatement();
            stmt.setFetchSize(1);
            rs = stmt.executeQuery(sql);
            if (rs.next()) {
                returnValue = rs.getString(1);
            }
            if (rs != null) {
                rs.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            endNonRequestCalls();
            return returnValue;
        } catch (Throwable th) {
            if (rs != null) {
                rs.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            endNonRequestCalls();
            throw th;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getUserName() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String strDoGetUserName = doGetUserName();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return strDoGetUserName;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    protected String doGetUserName() throws SQLException {
        assertLockHeldByCurrentThread();
        if (this.proxyClientName != null) {
            return this.proxyClientName;
        }
        if (this.userName == null) {
            this.userName = getPropertyFromDatabase("SELECT USER FROM DUAL");
        }
        return this.userName;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getCurrentSchema() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            this.currentSchema = getPropertyFromDatabase("SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') FROM DUAL");
            String str = this.currentSchema;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public String getDefaultSchemaNameForNamedTypes() throws SQLException {
        String returnValue;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.createDescriptorUseCurrentSchemaForSchemaName) {
                returnValue = getCurrentSchema();
            } else {
                returnValue = doGetUserName();
            }
            return returnValue;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public byte[] getFDO(boolean init) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.fdo == null && init) {
                CallableStatement cstmt = null;
                beginNonRequestCalls();
                try {
                    cstmt = prepareCall("begin :1 := sys.dbms_pickler.get_format (:2); end;");
                    cstmt.registerOutParameter(1, 2);
                    cstmt.registerOutParameter(2, -4);
                    cstmt.execute();
                    this.fdo = cstmt.getBytes(2);
                    if (cstmt != null) {
                        cstmt.close();
                    }
                    endNonRequestCalls();
                } catch (Throwable th2) {
                    if (cstmt != null) {
                        cstmt.close();
                    }
                    endNonRequestCalls();
                    throw th2;
                }
            }
            byte[] bArr = this.fdo;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return bArr;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setFDO(byte[] fdo) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.fdo = fdo;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean getBigEndian() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.bigEndian == null) {
                int[] ub1fdo = Util.toJavaUnsignedBytes(getFDO(true));
                int kopfdo_auxinfo = ub1fdo[6 + ub1fdo[5] + ub1fdo[6] + 5];
                int offset = (byte) (kopfdo_auxinfo & 16);
                if (offset < 0) {
                    offset += 256;
                }
                if (offset > 0) {
                    this.bigEndian = Boolean.TRUE;
                } else {
                    this.bigEndian = Boolean.FALSE;
                }
            }
            boolean zBooleanValue = this.bigEndian.booleanValue();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zBooleanValue;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void setHoldability(int holdability) throws SQLException {
        requireOpenConnection();
        if (!getMetaData().supportsResultSetHoldability(holdability)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 162).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public int getHoldability() throws SQLException {
        requireOpenConnection();
        return 1;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public Savepoint setSavepoint() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            oracle.jdbc.OracleSavepoint oracleSavepointOracleSetSavepoint = oracleSetSavepoint();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleSavepointOracleSetSavepoint;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public Savepoint setSavepoint(String name) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                oracle.jdbc.OracleSavepoint oracleSavepointOracleSetSavepoint = oracleSetSavepoint(name);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return oracleSavepointOracleSetSavepoint;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void rollback(Savepoint savepoint) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "rollback(Savepoint)", null, null, null);
            disallowGlobalTxnMode(115);
            if (this.autocommit) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 121).fillInStackTrace());
            }
            String _svptName = null;
            if (savepoint != null) {
                try {
                    _svptName = savepoint.getSavepointName();
                } catch (SQLException e) {
                    _svptName = "ORACLE_SVPT_" + savepoint.getSavepointId();
                }
            }
            Statement stmt = createStatement();
            Throwable th2 = null;
            try {
                try {
                    stmt.executeUpdate("ROLLBACK TO " + _svptName);
                    if (stmt != null) {
                        if (0 != 0) {
                            try {
                                stmt.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            stmt.close();
                        }
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (Throwable th5) {
                    th2 = th5;
                    throw th5;
                }
            } catch (Throwable th6) {
                if (stmt != null) {
                    if (th2 != null) {
                        try {
                            stmt.close();
                        } catch (Throwable th7) {
                            th2.addSuppressed(th7);
                        }
                    } else {
                        stmt.close();
                    }
                }
                throw th6;
            }
        } catch (Throwable th8) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th9) {
                        th.addSuppressed(th9);
                    }
                } else {
                    lock.close();
                }
            }
            throw th8;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void releaseSavepoint(Savepoint savepoint) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("releaseSavepoint").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        if (!getMetaData().supportsResultSetHoldability(resultSetHoldability)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 162).fillInStackTrace());
        }
        return createStatement(resultSetType, resultSetConcurrency);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        if (!getMetaData().supportsResultSetHoldability(resultSetHoldability)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 162).fillInStackTrace());
        }
        return prepareStatement(sql, resultSetType, resultSetConcurrency);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
        if (!getMetaData().supportsResultSetHoldability(resultSetHoldability)) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 162).fillInStackTrace());
        }
        return prepareCall(sql, resultSetType, resultSetConcurrency);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final PreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            AutoKeyInfo info = AutoKeyInfo.create(sql, autoGeneratedKeys);
            if (info == null) {
                PreparedStatement preparedStatementPrepareStatement = prepareStatement(sql);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return preparedStatementPrepareStatement;
            }
            OraclePreparedStatement internalStatement = prepareStatementInternal(info);
            OraclePreparedStatementWrapper oraclePreparedStatementWrapper = new OraclePreparedStatementWrapper(internalStatement);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oraclePreparedStatementWrapper;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final PreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            AutoKeyInfo info = AutoKeyInfo.create(sql, columnIndexes);
            if (info == null) {
                PreparedStatement preparedStatementPrepareStatement = prepareStatement(sql);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return preparedStatementPrepareStatement;
            }
            info.initialize(this);
            OraclePreparedStatement internalStatement = prepareStatementInternal(info);
            OraclePreparedStatementWrapper oraclePreparedStatementWrapper = new OraclePreparedStatementWrapper(internalStatement);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oraclePreparedStatementWrapper;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public final PreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            AutoKeyInfo info = AutoKeyInfo.create(sql, columnNames);
            if (info == null) {
                PreparedStatement preparedStatementPrepareStatement = prepareStatement(sql);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return preparedStatementPrepareStatement;
            }
            info.getTableName();
            OraclePreparedStatement internalStatement = prepareStatementInternal(info);
            OraclePreparedStatementWrapper oraclePreparedStatementWrapper = new OraclePreparedStatementWrapper(internalStatement);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return oraclePreparedStatementWrapper;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    final OraclePreparedStatement prepareStatementInternal(AutoKeyInfo autoKeyInfo) throws SQLException {
        long startToken = DMSFactory.getInstance().getToken();
        DMSFactory.DMSPhase dmsSensor = this.dmsCreateNewStatement;
        String sql = autoKeyInfo.getNewSql();
        OraclePreparedStatement internalStatement = (OraclePreparedStatement) getCachedStatement(sql, 1, OracleResultSet.ResultSetType.UNKNOWN);
        if (internalStatement == null) {
            OraclePreparedStatement allocatedStatement = (OraclePreparedStatement) this.driverExtension.allocatePreparedStatement(this, sql, autoKeyInfo);
            internalStatement = allocatedStatement;
        } else {
            dmsSensor = this.dmsCreateStatement;
        }
        dmsSensor.start(startToken);
        dmsSensor.stop(startToken);
        return internalStatement;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public oracle.jdbc.OracleSavepoint oracleSetSavepoint() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            disallowGlobalTxnMode(DatabaseError.EOJ_SETSVPT_IN_GLOBAL_TXN);
            if (this.autocommit) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 120).fillInStackTrace());
            }
            OracleSavepoint _osvpt = new OracleSavepoint();
            String _svptSqlString = "SAVEPOINT ORACLE_SVPT_" + _osvpt.getSavepointId();
            Statement stmt = createStatement();
            Throwable th2 = null;
            try {
                try {
                    stmt.executeUpdate(_svptSqlString);
                    if (stmt != null) {
                        if (0 != 0) {
                            try {
                                stmt.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            stmt.close();
                        }
                    }
                    return _osvpt;
                } finally {
                }
            } catch (Throwable th4) {
                if (stmt != null) {
                    if (th2 != null) {
                        try {
                            stmt.close();
                        } catch (Throwable th5) {
                            th2.addSuppressed(th5);
                        }
                    } else {
                        stmt.close();
                    }
                }
                throw th4;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public oracle.jdbc.OracleSavepoint oracleSetSavepoint(String name) throws SQLException {
        String _svptSqlString;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            disallowGlobalTxnMode(DatabaseError.EOJ_SETSVPT_IN_GLOBAL_TXN);
            if (this.autocommit) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 120).fillInStackTrace());
            }
            OracleSavepoint _osvpt = new OracleSavepoint(name);
            if (_osvpt.getType() == 1) {
                _svptSqlString = "SAVEPOINT ORACLE_SVPT_" + _osvpt.getSavepointId();
            } else {
                _svptSqlString = "SAVEPOINT " + _osvpt.getSavepointName();
            }
            Statement stmt = createStatement();
            Throwable th2 = null;
            try {
                try {
                    stmt.executeUpdate(_svptSqlString);
                    if (stmt != null) {
                        if (0 != 0) {
                            try {
                                stmt.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            stmt.close();
                        }
                    }
                    return _osvpt;
                } finally {
                }
            } catch (Throwable th4) {
                if (stmt != null) {
                    if (th2 != null) {
                        try {
                            stmt.close();
                        } catch (Throwable th5) {
                            th2.addSuppressed(th5);
                        }
                    } else {
                        stmt.close();
                    }
                }
                throw th4;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void oracleRollback(oracle.jdbc.OracleSavepoint savepoint) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            disallowGlobalTxnMode(115);
            if (this.autocommit) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 121).fillInStackTrace());
            }
            String _svptName = null;
            if (savepoint != null) {
                try {
                    _svptName = savepoint.getSavepointName();
                } catch (SQLException e) {
                    _svptName = "ORACLE_SVPT_" + savepoint.getSavepointId();
                }
            }
            Statement stmt = createStatement();
            Throwable th2 = null;
            try {
                try {
                    stmt.executeUpdate("ROLLBACK TO " + _svptName);
                    if (stmt != null) {
                        if (0 != 0) {
                            try {
                                stmt.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            stmt.close();
                        }
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (Throwable th5) {
                    th2 = th5;
                    throw th5;
                }
            } catch (Throwable th6) {
                if (stmt != null) {
                    if (th2 != null) {
                        try {
                            stmt.close();
                        } catch (Throwable th7) {
                            th2.addSuppressed(th7);
                        }
                    } else {
                        stmt.close();
                    }
                }
                throw th6;
            }
        } catch (Throwable th8) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th9) {
                        th.addSuppressed(th9);
                    }
                } else {
                    lock.close();
                }
            }
            throw th8;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void oracleReleaseSavepoint(oracle.jdbc.OracleSavepoint savepoint) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("oracleReleaseSavepoint").fillInStackTrace());
    }

    void disallowGlobalTxnMode(int errorCode) throws SQLException {
        if (this.txnMode == 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), errorCode).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setTxnMode(int mode) {
        this.txnMode = mode;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getTxnMode() {
        return this.txnMode;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean inSessionlessTxnMode() {
        return this.txnMode == 2 || this.txnMode == 3;
    }

    @Override // oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.ClientDataSupport
    public Object getClientData(Object key) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.clientData != null) {
                    Object obj = this.clientData.get(key);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return obj;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return null;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.ClientDataSupport
    public Object setClientData(Object key, Object value) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.clientData == null) {
                this.clientData = new Hashtable<>();
            }
            Object objPut = this.clientData.put(key, value);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return objPut;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.ClientDataSupport
    public Object removeClientData(Object key) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.clientData != null) {
                    Object objRemove = this.clientData.remove(key);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return objRemove;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return null;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public BlobDBAccess createBlobDBAccess() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("createBlobDBAccess").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public ClobDBAccess createClobDBAccess() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("createClobDBAccess").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public BfileDBAccess createBfileDBAccess() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("createBfileDBAccess").fillInStackTrace());
    }

    void printState() {
        try {
            getJdbcCsId();
            getDbCsId();
            getStructAttrCsId();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public String getProtocolType() {
        return this.protocol;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public String getURL() {
        return this.url;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public void setStmtCacheSize(int size) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                setStatementCacheSize(size);
                setImplicitCachingEnabled(true);
                setExplicitCachingEnabled(true);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public void setStmtCacheSize(int size, boolean clearMetaData) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                setStatementCacheSize(size);
                setImplicitCachingEnabled(true);
                setExplicitCachingEnabled(true);
                this.clearStatementMetaData = clearMetaData;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Deprecated
    public int getStmtCacheSize() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            int returnValue = 0;
            try {
                returnValue = getStatementCacheSize();
            } catch (SQLException e) {
            }
            if (returnValue == -1) {
                returnValue = 0;
            }
            return returnValue;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setStatementCacheSize(int size) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache == null) {
                this.statementCache = new LRUStatementCache(size);
                this.statementCache.createDMSSensors(this.dmsParent);
            } else {
                this.statementCache.resize(size);
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public int getStatementCacheSize() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache == null) {
                return -1;
            }
            int cacheSize = this.statementCache.getCacheSize();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return cacheSize;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setImplicitCachingEnabled(boolean cache) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.statementCache == null) {
                    this.statementCache = new LRUStatementCache(0);
                    this.statementCache.createDMSSensors(this.dmsParent);
                }
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "setImplicitCachingEnabled", "implicitCacheEnabled={0}. ", (String) null, null, Boolean.valueOf(cache));
                this.statementCache.setImplicitCachingEnabled(cache);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getImplicitCachingEnabled() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache == null) {
                return false;
            }
            boolean implicitCachingEnabled = this.statementCache.getImplicitCachingEnabled();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return implicitCachingEnabled;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setExplicitCachingEnabled(boolean cache) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.statementCache == null) {
                    this.statementCache = new LRUStatementCache(0);
                    this.statementCache.createDMSSensors(this.dmsParent);
                }
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "setExplicitCachingEnabled", "explicitCacheEnabled={0}. ", (String) null, null, Boolean.valueOf(cache));
                this.statementCache.setExplicitCachingEnabled(cache);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getExplicitCachingEnabled() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache == null) {
                return false;
            }
            boolean explicitCachingEnabled = this.statementCache.getExplicitCachingEnabled();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return explicitCachingEnabled;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void purgeImplicitCache() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache != null) {
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "purgeImplicitCache", (String) null, (String) null, null, new Object[0]);
                this.statementCache.purgeImplicitCache();
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void purgeExplicitCache() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache != null) {
                trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "purgeExplicitCache", (String) null, (String) null, null, new Object[0]);
                this.statementCache.purgeExplicitCache();
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public final PreparedStatement getStatementWithKey(String key) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                OracleStatement cachedStatement = getCachedStatementWithKey(key, 1);
                if (cachedStatement != null) {
                    OraclePreparedStatement preparedStatement = (OraclePreparedStatement) cachedStatement;
                    OraclePreparedStatementWrapper oraclePreparedStatementWrapper = new OraclePreparedStatementWrapper(preparedStatement);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return oraclePreparedStatementWrapper;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return null;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public final CallableStatement getCallWithKey(String key) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                OracleStatement cachedStatement = getCachedStatementWithKey(key, 2);
                if (cachedStatement != null) {
                    OracleCallableStatement callableStatement = (OracleCallableStatement) cachedStatement;
                    OracleCallableStatementWrapper oracleCallableStatementWrapper = new OracleCallableStatementWrapper(callableStatement);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return oracleCallableStatementWrapper;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return null;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    protected OracleStatement getCachedStatementWithKey(String key, int statementType) throws SQLException {
        OracleStatement cachedStatement;
        if (this.statementCache == null || (cachedStatement = this.statementCache.searchExplicitCache(key)) == null) {
            return null;
        }
        if (cachedStatement.statementType != statementType) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 125).fillInStackTrace());
        }
        debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getCachedStatementWithKey", "statement is active. key={0}, state={1}. ", (String) null, (Throwable) null, key, Integer.valueOf(cachedStatement.cacheState));
        return cachedStatement;
    }

    void cacheImplicitStatement(OraclePreparedStatement stmt, String sql, int statementType, OracleResultSet.ResultSetType scrollType) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 95).fillInStackTrace());
            }
            this.statementCache.addToImplicitCache(stmt, sql, statementType, scrollType.ordinal());
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void cacheExplicitStatement(OraclePreparedStatement stmt, String key) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache == null) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 95).fillInStackTrace());
            }
            this.statementCache.addToExplicitCache(stmt, key);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isStatementCacheInitialized() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.statementCache == null) {
                return false;
            }
            if (this.statementCache.getCacheSize() == 0) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return false;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return true;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    BlockSource getBlockSource() {
        return this.blockSource;
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/PhysicalConnection$BufferCacheStore.class */
    private static final class BufferCacheStore {
        static int MAX_CACHED_BUFFER_SIZE = Integer.MAX_VALUE;
        final BufferCache<byte[]> byteBufferCache;
        final BufferCache<char[]> charBufferCache;

        BufferCacheStore() {
            this(MAX_CACHED_BUFFER_SIZE);
        }

        BufferCacheStore(int maxCachedBufferSize) {
            this.byteBufferCache = new BufferCache<>(maxCachedBufferSize);
            this.charBufferCache = new BufferCache<>(maxCachedBufferSize);
        }
    }

    private BufferCacheStore getBufferCacheStore() {
        if (this.useThreadLocalBufferCache) {
            if (threadLocalBufferCacheStore == null) {
                BufferCacheStore.MAX_CACHED_BUFFER_SIZE = this.maxCachedBufferSize;
                threadLocalBufferCacheStore = new ThreadLocal<BufferCacheStore>() { // from class: oracle.jdbc.driver.PhysicalConnection.2
                    /* JADX INFO: Access modifiers changed from: protected */
                    /* JADX WARN: Can't rename method to resolve collision */
                    @Override // java.lang.ThreadLocal
                    public BufferCacheStore initialValue() {
                        return new BufferCacheStore();
                    }
                };
            }
            return threadLocalBufferCacheStore.get();
        }
        if (this.connectionBufferCacheStore == null) {
            assertLockHeldByCurrentThread();
            if (this.connectionBufferCacheStore == null) {
                this.connectionBufferCacheStore = new BufferCacheStore(this.maxCachedBufferSize);
            }
        }
        return this.connectionBufferCacheStore;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public void cacheBuffer(byte[] buffer) {
        assertLockHeldByCurrentThread();
        if (buffer != null) {
            BufferCacheStore s = getBufferCacheStore();
            s.byteBufferCache.put(buffer);
        }
    }

    void cacheBufferSync(byte[] buffer) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                cacheBuffer(buffer);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void cacheBuffer(char[] buffer) {
        assertLockHeldByCurrentThread();
        if (buffer != null) {
            BufferCacheStore s = getBufferCacheStore();
            s.charBufferCache.put(buffer);
        }
    }

    public void cacheBufferSync(char[] buffer) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                cacheBuffer(buffer);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public byte[] getByteBuffer(int length) {
        assertLockHeldByCurrentThread();
        BufferCacheStore s = getBufferCacheStore();
        return s.byteBufferCache.get(Byte.TYPE, length);
    }

    byte[] getByteBufferSync(int length) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                byte[] byteBuffer = getByteBuffer(length);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return byteBuffer;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    char[] getCharBuffer(int length) {
        assertLockHeldByCurrentThread();
        BufferCacheStore s = getBufferCacheStore();
        return s.charBufferCache.get(Character.TYPE, length);
    }

    public char[] getCharBufferSync(int length) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                char[] charBuffer = getCharBuffer(length);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return charBuffer;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public OracleConnection.BufferCacheStatistics getByteBufferCacheStatistics() {
        BufferCacheStore s = getBufferCacheStore();
        return s.byteBufferCache.getStatistics();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public OracleConnection.BufferCacheStatistics getCharBufferCacheStatistics() {
        BufferCacheStore s = getBufferCacheStore();
        return s.charBufferCache.getStatistics();
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void registerTAFCallback(OracleOCIFailover cbk, Object obj) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("registerTAFCallback").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public String getDatabaseProductVersion() throws SQLException {
        if (this.databaseProductVersion == "") {
            needLine();
            this.databaseProductVersion = doGetDatabaseProductVersion();
        }
        return this.databaseProductVersion;
    }

    boolean getReportRemarks() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean z = this.reportRemarks;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public short getVersionNumber() throws SQLException {
        if (this.versionNumber == -1) {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                if (this.versionNumber == -1) {
                    needLine();
                    this.versionNumber = doGetVersionNumber();
                }
            } finally {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
            }
        }
        return this.versionNumber;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getMajorVersionNumber() throws SQLException {
        return doGetMajorVersionNumber();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getMinorVersionNumber() throws SQLException {
        return doGetMinorVersionNumber();
    }

    void registerCloseCallback(OracleCloseCallback occ, Object privData) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.closeCallback = occ;
                this.privateData = privData;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setCreateStatementAsRefCursor(boolean value) {
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Can't find top splitter block for handler:B:22:0x003f
        	at jadx.core.utils.BlockUtils.getTopSplitterForHandler(BlockUtils.java:1178)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.collectHandlerRegions(ExcHandlersRegionMaker.java:53)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.process(ExcHandlersRegionMaker.java:38)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:27)
        */
    /* JADX WARN: Unreachable blocks removed: 14, instructions: 21 */
    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean getCreateStatementAsRefCursor() {
        /*
            r3 = this;
            r0 = r3
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r4 = r0
            r0 = 0
            r5 = r0
            r0 = 0
            r6 = r0
            r0 = r4
            if (r0 == 0) goto L27
            r0 = r5
            if (r0 == 0) goto L23
            r0 = r4
            r0.close()     // Catch: java.lang.Throwable -> L18
            goto L27
        L18:
            r7 = move-exception
            r0 = r5
            r1 = r7
            r0.addSuppressed(r1)
            goto L27
        L23:
            r0 = r4
            r0.close()
        L27:
            r0 = r6
            return r0
        L29:
            r6 = move-exception
            r0 = r6
            r5 = r0
            r0 = r6
            throw r0     // Catch: java.lang.Throwable -> L2e
        L2e:
            r8 = move-exception
            r0 = r4
            if (r0 == 0) goto L4e
            r0 = r5
            if (r0 == 0) goto L4a
            r0 = r4
            r0.close()     // Catch: java.lang.Throwable -> L3f
            goto L4e
        L3f:
            r9 = move-exception
            r0 = r5
            r1 = r9
            r0.addSuppressed(r1)
            goto L4e
        L4a:
            r0 = r4
            r0.close()
        L4e:
            r0 = r8
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.PhysicalConnection.getCreateStatementAsRefCursor():boolean");
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public int pingDatabase() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.lifecycle != 1) {
                return -1;
            }
            if (checkAndDrain()) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return -1;
            }
            int iDoPingDatabase = doPingDatabase();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return iDoPingDatabase;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public int pingDatabase(int timeOut) throws SQLException, InterruptedException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.lifecycle != 1) {
                return -1;
            }
            if (timeOut < 0) {
                throw new SQLException("connection validation timeout cannot be negative.");
            }
            if (checkAndDrain()) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return -1;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            if (timeOut != 0) {
                try {
                    this.pingResult = -2;
                    Thread t = new Thread(new Runnable() { // from class: oracle.jdbc.driver.PhysicalConnection.3
                        @Override // java.lang.Runnable
                        public void run() {
                            try {
                                Monitor.CloseableLock lock2 = PhysicalConnection.this.acquireCloseableLock();
                                Throwable th4 = null;
                                try {
                                    PhysicalConnection.this.pingResult = PhysicalConnection.this.doPingDatabase();
                                    if (lock2 != null) {
                                        if (0 != 0) {
                                            try {
                                                lock2.close();
                                            } catch (Throwable th5) {
                                                th4.addSuppressed(th5);
                                            }
                                        } else {
                                            lock2.close();
                                        }
                                    }
                                } finally {
                                }
                            } catch (Throwable th6) {
                            }
                        }
                    });
                    t.start();
                    t.join(TimeUnit.SECONDS.toMillis(timeOut));
                    t.interrupt();
                    return this.pingResult;
                } catch (InterruptedException e) {
                    Thread.interrupted();
                    return -3;
                }
            }
            Monitor.CloseableLock lock2 = acquireCloseableLock();
            Throwable th4 = null;
            try {
                try {
                    int iDoPingDatabase = doPingDatabase();
                    if (lock2 != null) {
                        if (0 != 0) {
                            try {
                                lock2.close();
                            } catch (Throwable th5) {
                                th4.addSuppressed(th5);
                            }
                        } else {
                            lock2.close();
                        }
                    }
                    return iDoPingDatabase;
                } finally {
                }
            } catch (Throwable th6) {
                if (lock2 != null) {
                    if (th4 != null) {
                        try {
                            lock2.close();
                        } catch (Throwable th7) {
                            th4.addSuppressed(th7);
                        }
                    } else {
                        lock2.close();
                    }
                }
                throw th6;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th8) {
                        th.addSuppressed(th8);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    int doPingDatabase() throws SQLException {
        return executeDefaultConnectionValidationQuery(0);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Map<String, Class<?>> getJavaObjectTypeMap() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Map<String, Class<?>> map = this.javaObjectMap;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return map;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setJavaObjectTypeMap(Map<String, Class<?>> map) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.javaObjectMap = map;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.OracleConnection
    @Deprecated
    public void clearClientIdentifier(String clientId) throws SQLException {
        String[] metrics;
        if (this.dmsVersion.equals(DMSFactory.DMSVersion.NONE) && clientId != null && clientId.length() != 0 && (metrics = getEndToEndMetrics()) != null && clientId.equals(metrics[1])) {
            metrics[1] = null;
            setEndToEndMetrics(metrics, getEndToEndECIDSequenceNumber());
        }
    }

    @Override // oracle.jdbc.driver.OracleConnection, oracle.jdbc.internal.OracleConnection
    @Deprecated
    public void setClientIdentifier(String clientId) throws SQLException {
        if (this.dmsVersion.equals(DMSFactory.DMSVersion.NONE)) {
            String[] metrics = getEndToEndMetrics();
            if (metrics == null) {
                metrics = new String[4];
            }
            metrics[1] = clientId;
            setEndToEndMetrics(metrics, getEndToEndECIDSequenceNumber());
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setSessionTimeZone(String regionName) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Statement stmt = null;
            try {
                try {
                    stmt = createStatement();
                    stmt.executeUpdate("ALTER SESSION SET TIME_ZONE = '" + regionName + "'");
                    if (this.dbTzCalendar == null) {
                        setDbTzCalendar(getDatabaseTimeZone());
                    }
                    if (stmt != null) {
                        stmt.close();
                    }
                    this.sessionTimeZone = regionName;
                    this.sessionZoneId = ZoneId.of(regionName, ZoneId.SHORT_IDS);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (SQLException e) {
                    throw e;
                }
            } catch (Throwable th3) {
                if (stmt != null) {
                    stmt.close();
                }
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.internal.OracleConnection
    public String getDatabaseTimeZone() throws SQLException {
        if (this.databaseTimeZone == null) {
            this.databaseTimeZone = getPropertyFromDatabase("SELECT DBTIMEZONE FROM DUAL");
            this.databaseZoneId = ZoneId.of(this.databaseTimeZone);
        }
        return this.databaseTimeZone;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public ZoneId getDatabaseZoneId() throws SQLException {
        if (this.databaseZoneId == null) {
            getDatabaseTimeZone();
        }
        return this.databaseZoneId;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getSessionTimeZone() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.sessionTimeZone;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public ZoneId getSessionZoneId() {
        return this.sessionZoneId;
    }

    private static String to2DigitString(int number) {
        String twoDigit;
        if (number < 10) {
            twoDigit = "0" + number;
        } else {
            twoDigit = "" + number;
        }
        return twoDigit;
    }

    String tzToOffset(String sessiontzhours) {
        if (sessiontzhours == null) {
            return sessiontzhours;
        }
        char sign = sessiontzhours.charAt(0);
        if (sign != '-' && sign != '+') {
            TimeZone tz = TimeZone.getTimeZone(sessiontzhours);
            int sessTZOffsetMillis = tz.getOffset(System.currentTimeMillis());
            if (sessTZOffsetMillis != 0) {
                int sessTZOffsetMinutes = sessTZOffsetMillis / 60000;
                int sessTZOffsetHours = sessTZOffsetMinutes / 60;
                int sessTZOffsetMinutes2 = sessTZOffsetMinutes - (sessTZOffsetHours * 60);
                if (sessTZOffsetMillis > 0) {
                    sessiontzhours = "+" + to2DigitString(sessTZOffsetHours) + ":" + to2DigitString(sessTZOffsetMinutes2);
                } else {
                    sessiontzhours = "-" + to2DigitString(-sessTZOffsetHours) + ":" + to2DigitString(-sessTZOffsetMinutes2);
                }
            } else {
                sessiontzhours = "+00:00";
            }
        }
        return sessiontzhours;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getSessionTimeZoneOffset() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String sessiontzhours = getPropertyFromDatabase("SELECT SESSIONTIMEZONE FROM DUAL");
            if (sessiontzhours != null) {
                sessiontzhours = tzToOffset(sessiontzhours.trim());
            }
            return sessiontzhours;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    private void setDbTzCalendar(String dbTzStr) {
        char sign = dbTzStr.charAt(0);
        if (sign == '-' || sign == '+') {
            dbTzStr = "GMT" + dbTzStr;
        }
        TimeZone tz = TimeZone.getTimeZone(dbTzStr);
        this.dbTzCalendar = new GregorianCalendar(tz);
    }

    Calendar getDbTzCalendar() throws SQLException {
        if (this.dbTzCalendar == null) {
            setDbTzCalendar(getDatabaseTimeZone());
        }
        Calendar result = null;
        if (this.dbTzCalendar != null) {
            result = (Calendar) this.dbTzCalendar.clone();
        }
        return result;
    }

    void setAccumulateBatchResult(boolean val) {
        this.accumulateBatchResult = val;
    }

    boolean isAccumulateBatchResult() {
        return this.accumulateBatchResult;
    }

    void setJ2EE13Compliant(boolean val) {
        this.j2ee13Compliant = val;
    }

    boolean getJ2EE13Compliant() {
        return this.j2ee13Compliant;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean getJDBCStandardBehavior() {
        return this.jdbcStandardBehavior;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Class<?> classForNameAndSchema(String name, String schemaName) throws ClassNotFoundException {
        return Class.forName(name);
    }

    Class<?> safelyGetClassForName(String name) throws ClassNotFoundException {
        return Class.forName(name);
    }

    static boolean isJsonJarPresent() {
        return IS_JSON_JAR_LOADED;
    }

    static boolean isJakartaJarPresent() {
        return IS_JAKARTA_JAR_LOADED;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getHeapAllocSize() throws SQLException {
        requireOpenConnection();
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getHeapAllocSize").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getOCIEnvHeapAllocSize() throws SQLException {
        requireOpenConnection();
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getOCIEnvHeapAllocSize").fillInStackTrace());
    }

    static OracleConnection unwrapCompletely(oracle.jdbc.OracleConnection wrappedConnection) {
        oracle.jdbc.OracleConnection previous = wrappedConnection;
        oracle.jdbc.OracleConnection oracleConnectionUnwrap = previous;
        while (true) {
            oracle.jdbc.OracleConnection next = oracleConnectionUnwrap;
            if (next == null) {
                return (OracleConnection) previous;
            }
            previous = next;
            oracleConnectionUnwrap = previous.unwrap();
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setWrapper(oracle.jdbc.OracleConnection w) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.wrapper = w;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public oracle.jdbc.OracleConnection unwrap() {
        return null;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.OracleConnection getWrapper() {
        if (this.wrapper != null) {
            return this.wrapper;
        }
        return this;
    }

    static oracle.jdbc.internal.OracleConnection _physicalConnectionWithin(Connection possiblyWrappedPossiblyLogicalConnection) {
        oracle.jdbc.internal.OracleConnection unwrappedConnection = null;
        if (possiblyWrappedPossiblyLogicalConnection != null) {
            unwrappedConnection = unwrapCompletely((oracle.jdbc.OracleConnection) possiblyWrappedPossiblyLogicalConnection);
        }
        return unwrappedConnection;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public oracle.jdbc.internal.OracleConnection physicalConnectionWithin() {
        return this;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public long getTdoCState(String schemaName, String typeName) throws SQLException {
        return 0L;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public long getTdoCState(String typeNameByUser) throws SQLException {
        return 0L;
    }

    void getOracleTypeADT(OracleTypeADT otype) throws SQLException {
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Datum toDatum(CustomDatum inObject) throws SQLException {
        return inObject.toDatum(this);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public short getNCharSet() {
        return this.conversion.getNCharSetId();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public ResultSet newArrayDataResultSet(Datum[] data, long index, int count, Map<String, Class<?>> map) throws SQLException {
        return new ArrayDataResultSet(this, data, index, count, map);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public ResultSet newArrayDataResultSet(oracle.jdbc.internal.OracleArray array, long index, int count, Map<String, Class<?>> map) throws SQLException {
        return new ArrayDataResultSet(this, array, index, count, map);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public ResultSet newArrayLocatorResultSet(ArrayDescriptor desc, byte[] locator, long index, int count, Map<String, Class<?>> map) throws SQLException {
        return ArrayLocatorResultSet.create(this, desc, locator, index, count, map);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public ResultSetMetaData newStructMetaData(StructDescriptor desc) throws SQLException {
        return new StructMetaData(desc);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int CHARBytesToJavaChars(byte[] bytes, int nbytes, char[] chars) throws SQLException {
        int[] nBytes = {nbytes};
        return this.conversion.CHARBytesToJavaChars(bytes, 0, chars, 0, nBytes, chars.length);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int NCHARBytesToJavaChars(byte[] bytes, int nbytes, char[] chars) throws SQLException {
        int[] nBytes = new int[1];
        return this.conversion.NCHARBytesToJavaChars(bytes, 0, chars, 0, nBytes, chars.length);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean IsNCharFixedWith() {
        return this.conversion.IsNCharFixedWith();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public short getDriverCharSet() {
        return this.conversion.getClientCharSet();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public CharacterSet getDbCharSet() {
        return this.conversion.getDbCharSetObj();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getMaxCharSize() throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 58).fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getMaxCharbyteSize() {
        return this.conversion.getMaxCharbyteSize();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getMaxNCharbyteSize() {
        return this.conversion.getMaxNCharbyteSize();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isCharSetMultibyte(short charSet) {
        return DBConversion.isCharSetMultibyte(charSet);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int javaCharsToCHARBytes(char[] chars, int nchars, byte[] bytes) throws SQLException {
        return this.conversion.javaCharsToCHARBytes(chars, nchars, bytes);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int javaCharsToNCHARBytes(char[] chars, int nchars, byte[] bytes) throws SQLException {
        return this.conversion.javaCharsToNCHARBytes(chars, nchars, bytes);
    }

    final void getPropertyForPooledConnection(OraclePooledConnection pc, @Blind String password) throws SQLException {
        Hashtable<String, Object> hashTable = new Hashtable<>();
        hashTable.put(OraclePooledConnection.object_type_map, this.javaObjectMap);
        Properties prop = new Properties();
        prop.put("user", this.userName);
        prop.put("password", password);
        prop.put(OraclePooledConnection.url_string, this.url);
        prop.put(OraclePooledConnection.connect_auto_commit_string, "" + this.autocommit);
        prop.put(OraclePooledConnection.transaction_isolation, "" + this.txnLevel);
        if (getStatementCacheSize() != -1) {
            prop.put(OraclePooledConnection.statement_cache_size, "" + getStatementCacheSize());
            prop.put(OraclePooledConnection.implicit_caching_enabled, "" + getImplicitCachingEnabled());
            prop.put(OraclePooledConnection.explicit_caching_enabled, "" + getExplicitCachingEnabled());
        }
        prop.put("defaultRowPrefetch", "" + this.defaultRowPrefetch);
        prop.put("remarksReporting", "" + this.reportRemarks);
        prop.put("AccumulateBatchResult", "" + this.accumulateBatchResult);
        prop.put("oracle.jdbc.J2EE13Compliant", "" + this.j2ee13Compliant);
        prop.put("processEscapes", "" + this.processEscapes);
        prop.put("restrictGetTables", "" + this.restrictGettables);
        prop.put("includeSynonyms", "" + this.includeSynonyms);
        prop.put("fixedString", "" + this.fixedString);
        hashTable.put(OraclePooledConnection.connection_properties_string, prop);
        pc.setProperties(hashTable);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Properties getDBAccessProperties() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getDBAccessProperties").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Properties getOCIHandles() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getOCIHandles").fillInStackTrace());
    }

    void logoff() throws SQLException {
    }

    int getDefaultStreamChunkSize() {
        return 32768;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.OracleStatement refCursorCursorToStatement(int cursorNumber) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("refCursorCursorToStatement").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Connection getLogicalConnection(OraclePooledConnection pc, boolean autoCommit) throws SQLException {
        if (this.logicalConnectionAttached != null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 143).fillInStackTrace());
        }
        LogicalConnection logicalConn = new LogicalConnection(pc, this, autoCommit);
        this.dmsLogicalConnection.update(logicalConn);
        this.logicalConnectionAttached = logicalConn;
        return logicalConn;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void getForm(OracleTypeADT otypeADT, OracleTypeCLOB otype, int attrIndex) throws SQLException {
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public CLOB createClob(byte[] locator_bytes) throws SQLException {
        CLOB result = new CLOB(this, locator_bytes);
        if (result.isNCLOB()) {
            result = new NCLOB(result);
            result.getDBAccess().decrementTempLobReferenceCount(locator_bytes);
        }
        return result;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public CLOB createClobWithUnpickledBytes(byte[] locator_bytes) throws SQLException {
        CLOB result = new CLOB((oracle.jdbc.OracleConnection) this, locator_bytes, true);
        if (result.isNCLOB()) {
            result = new NCLOB(result);
            result.getDBAccess().decrementTempLobReferenceCount(locator_bytes);
        }
        return result;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public CLOB createClob(byte[] locator_bytes, short csform) throws SQLException {
        if (csform == 2) {
            return new NCLOB(this, locator_bytes);
        }
        return new CLOB(this, locator_bytes, csform);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public BLOB createBlob(byte[] locator_bytes) throws SQLException {
        return new BLOB(this, locator_bytes);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public BLOB createBlobWithUnpickledBytes(byte[] locator_bytes) throws SQLException {
        return new BLOB(this, locator_bytes, true);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public BFILE createBfile(byte[] locator_bytes) throws SQLException {
        return new BFILE(this, locator_bytes);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public ARRAY createARRAY(String typeName, Object elements) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                ArrayDescriptor descriptor = ArrayDescriptor.createDescriptor(typeName, this);
                ARRAY array = new ARRAY(descriptor, this, elements);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return array;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public Array createOracleArray(String arrayTypeName, Object elements) throws SQLException {
        return createARRAY(arrayTypeName, elements);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public BINARY_DOUBLE createBINARY_DOUBLE(double value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                BINARY_DOUBLE binary_double = new BINARY_DOUBLE(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return binary_double;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public BINARY_FLOAT createBINARY_FLOAT(float value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                BINARY_FLOAT binary_float = new BINARY_FLOAT(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return binary_float;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public DATE createDATE(Date value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                DATE date = new DATE(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return date;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public DATE createDATE(Time value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                DATE date = new DATE(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return date;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public DATE createDATE(Timestamp value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                DATE date = new DATE(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return date;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public DATE createDATE(Date value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                DATE date = new DATE(value, cal);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return date;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public DATE createDATE(Time value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                DATE date = new DATE(value, cal);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return date;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public DATE createDATE(Timestamp value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                DATE date = new DATE(value, cal);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return date;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public DATE createDATE(String value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                DATE date = new DATE(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return date;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public INTERVALDS createINTERVALDS(String value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                INTERVALDS intervalds = new INTERVALDS(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return intervalds;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public INTERVALYM createINTERVALYM(String value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                INTERVALYM intervalym = new INTERVALYM(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return intervalym;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(boolean value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(byte value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(short value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(int value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(long value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(float value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(double value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(BigDecimal value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(BigInteger value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public NUMBER createNUMBER(String value, int scale) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NUMBER number = new NUMBER(value, scale);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return number;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public Array createArrayOf(String typeName, Object[] elements) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("createArrayOf").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public Struct createStruct(String typeName, Object[] attributes) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                try {
                    StructDescriptor descriptor = StructDescriptor.createDescriptor(typeName, this);
                    STRUCT struct = new STRUCT(descriptor, this, attributes);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return struct;
                } catch (SQLException ex) {
                    if (ex.getErrorCode() == 17049) {
                        removeAllDescriptor();
                    }
                    throw ex;
                }
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMP createTIMESTAMP(Date value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMP timestamp = new TIMESTAMP(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamp;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMP createTIMESTAMP(DATE value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMP timestamp = new TIMESTAMP(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamp;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMP createTIMESTAMP(Time value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMP timestamp = new TIMESTAMP(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamp;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMP createTIMESTAMP(Timestamp value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMP timestamp = new TIMESTAMP(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamp;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMP createTIMESTAMP(Timestamp value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMP timestamp = new TIMESTAMP(value, cal);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamp;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMP createTIMESTAMP(String value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMP timestamp = new TIMESTAMP(value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamp;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(Date value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamptz;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(Date value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value, cal);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestamptz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(Time value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamptz;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(Time value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value, cal);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestamptz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(Timestamp value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamptz;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(Timestamp value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value, cal);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestamptz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(Timestamp value, ZoneId tzid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value, tzid);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestamptz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(String value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamptz;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(String value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value, cal);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestamptz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPTZ createTIMESTAMPTZ(DATE value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                TIMESTAMPTZ timestamptz = new TIMESTAMPTZ(this, value);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return timestamptz;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPLTZ createTIMESTAMPLTZ(Date value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPLTZ timestampltz = new TIMESTAMPLTZ(this, cal, value);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestampltz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPLTZ createTIMESTAMPLTZ(Time value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPLTZ timestampltz = new TIMESTAMPLTZ(this, cal, value);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestampltz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPLTZ createTIMESTAMPLTZ(Timestamp value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPLTZ timestampltz = new TIMESTAMPLTZ(this, cal, value);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestampltz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPLTZ createTIMESTAMPLTZ(String value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPLTZ timestampltz = new TIMESTAMPLTZ(this, cal, value);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestampltz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TIMESTAMPLTZ createTIMESTAMPLTZ(DATE value, Calendar cal) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TIMESTAMPLTZ timestampltz = new TIMESTAMPLTZ(this, cal, value);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timestampltz;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public Blob createBlob() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            BLOB blobCreateTemporaryBlob = createTemporaryBlob(this, true, 10);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return blobCreateTemporaryBlob;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public Clob createClob() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            CLOB clobCreateTemporaryClob = createTemporaryClob(this, true, 10, (short) 1);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return clobCreateTemporaryClob;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public NClob createNClob() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            NClob nClob = (NClob) createTemporaryClob(this, true, 10, (short) 2);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return nClob;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public SQLXML createSQLXML() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            XMLType xMLType = new XMLType(this, (String) null);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return xMLType;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x004d  */
    @Override // oracle.jdbc.internal.OracleConnection
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean isDescriptorSharable(oracle.jdbc.internal.OracleConnection r4) throws java.sql.SQLException {
        /*
            Method dump skipped, instructions count: 244
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.PhysicalConnection.isDescriptorSharable(oracle.jdbc.internal.OracleConnection):boolean");
    }

    boolean useLittleEndianSetCHARBinder() throws SQLException {
        return false;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setPlsqlWarnings(String setting) throws SQLException {
        if (setting == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
        }
        if (setting != null) {
            String strTrim = setting.trim();
            setting = strTrim;
            if (strTrim.length() > 0 && !OracleSql.isValidPlsqlWarning(setting)) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
            }
        }
        Statement stmt = createStatement(-1, -1);
        Throwable th = null;
        try {
            debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "setPlsqlWarnings", "setting={0}. ", (String) null, (String) null, (Object) setting);
            stmt.execute("ALTER SESSION SET PLSQL_WARNINGS=" + setting);
            this.plsqlCompilerWarnings = !setting.equals("'DISABLE:ALL'");
            if (this.plsqlCompilerWarnings) {
                try {
                    stmt.executeQuery("ALTER SESSION SET EVENTS='10933 TRACE NAME CONTEXT LEVEL 32768'");
                } catch (SQLException sqlE) {
                    debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "setPlsqlWarnings", "Failed to relax PLSQL Compiler Warnings with event 10933. ", (String) null, sqlE);
                }
            }
            if (stmt != null) {
                if (0 != 0) {
                    try {
                        stmt.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                stmt.close();
            }
        } catch (Throwable th3) {
            if (stmt != null) {
                if (0 != 0) {
                    try {
                        stmt.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    stmt.close();
                }
            }
            throw th3;
        }
    }

    void internalClose() throws SQLException {
        setLifecycle(4);
        OracleStatement oracleStatement = this.statements;
        while (true) {
            OracleStatement s = oracleStatement;
            if (s == null) {
                break;
            }
            OracleStatement n = s.nextChild;
            if (s.serverCursor) {
                s.internalClose();
                removeStatement(s);
            }
            oracleStatement = n;
        }
        OracleStatement oracleStatement2 = this.statements;
        while (true) {
            OracleStatement s2 = oracleStatement2;
            if (s2 != null) {
                OracleStatement n2 = s2.next;
                s2.internalClose();
                oracleStatement2 = n2;
            } else {
                this.statements = null;
                return;
            }
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public XAResource getXAResource() throws SQLException {
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 164).fillInStackTrace());
    }

    protected void doDescribeTable(AutoKeyInfo info) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doDescribeTable").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void setClientInfo(String name, String value) throws SQLException {
        if (this.lifecycle != 1) {
            throw ((SQLClientInfoException) DatabaseError.createSQLClientInfoException(8, null, null).fillInStackTrace());
        }
        SecurityManager security = System.getSecurityManager();
        if (security != null) {
            OracleSQLPermission permission = new OracleSQLPermission(SETCLIENTINFO_PERMISSION_NAME + name);
            security.checkPermission(permission);
        }
        setClientInfoInternal(name, value);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public void setClientInfo(@Blind(PropertiesBlinder.class) Properties properties) throws SQLException {
        if (this.lifecycle != 1) {
            throw ((SQLClientInfoException) DatabaseError.createSQLClientInfoException(8, null, null).fillInStackTrace());
        }
        SecurityManager security = System.getSecurityManager();
        if (security != null) {
            OracleSQLPermission permission = new OracleSQLPermission("clientInfo.*");
            security.checkPermission(permission);
        }
        this.clientInfo.clear();
        Map<String, ClientInfoStatus> failed = new HashMap<>();
        for (String k : properties.stringPropertyNames()) {
            String v = properties.getProperty(k);
            try {
                setClientInfoInternal(k, v);
            } catch (SQLClientInfoException e) {
                failed.put(k, ClientInfoStatus.REASON_UNKNOWN_PROPERTY);
            }
        }
        if (!failed.isEmpty()) {
            throw ((SQLClientInfoException) DatabaseError.createSQLClientInfoException(DatabaseError.EOJ_INVALID_NAME_FOR_CLIENTINFO, failed, null).fillInStackTrace());
        }
    }

    void setClientInfoInternal(String name, String value) throws SQLException {
        if (!SUPPORTED_NAME_PATTERN.matcher(name).matches()) {
            throw ((SQLClientInfoException) DatabaseError.createSQLClientInfoException(DatabaseError.EOJ_INVALID_NAME_FOR_CLIENTINFO, null, null).fillInStackTrace());
        }
        String[] nameKeyPair = name.split("\\.", 2);
        if (RESERVED_NAMESPACES.contains(nameKeyPair[0])) {
            throw ((SQLClientInfoException) DatabaseError.createSQLClientInfoException(DatabaseError.EOJ_ATTEMPT_TO_USE_RESERVED_NAMESPACE, null, null).fillInStackTrace());
        }
        String nameSpace = nameKeyPair[0];
        String attribute = nameKeyPair[1];
        try {
            if (nameSpace.equals(oracle.jdbc.OracleConnection.OCSID_NAMESPACE)) {
                if (attribute.equals(oracle.jdbc.OracleConnection.OCSID_ACTION_KEY)) {
                    if (value != null && value.length() > this.endToEndMaxLength[0]) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 159, value).fillInStackTrace());
                    }
                    if ((value == null && this.endToEndValues[0] != null) || value != null) {
                        this.endToEndValues[0] = value;
                        this.endToEndHasChanged[0] = true;
                        this.endToEndAnyChanged = true;
                    }
                } else if (attribute.equals(oracle.jdbc.OracleConnection.OCSID_CLIENTID_KEY)) {
                    if (value != null && value.length() > this.endToEndMaxLength[1]) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 159, value).fillInStackTrace());
                    }
                    if ((value == null && this.endToEndValues[1] != null) || value != null) {
                        this.endToEndValues[1] = value;
                        this.endToEndHasChanged[1] = true;
                        this.endToEndAnyChanged = true;
                    }
                } else if (attribute.equals(oracle.jdbc.OracleConnection.OCSID_ECID_KEY)) {
                    if (value != null && value.length() > this.endToEndMaxLength[2]) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 159, value).fillInStackTrace());
                    }
                    if ((value == null && this.endToEndValues[2] != null) || value != null) {
                        this.endToEndValues[2] = value;
                        this.endToEndHasChanged[2] = true;
                        this.endToEndAnyChanged = true;
                    }
                } else if (attribute.equals(oracle.jdbc.OracleConnection.OCSID_MODULE_KEY)) {
                    if (value != null && value.length() > this.endToEndMaxLength[3]) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 159, value).fillInStackTrace());
                    }
                    if ((value == null && this.endToEndValues[3] != null) || value != null) {
                        this.endToEndValues[3] = value;
                        this.endToEndHasChanged[3] = true;
                        this.endToEndHasChanged[0] = true;
                        this.endToEndAnyChanged = true;
                    }
                } else if (attribute.equals(oracle.jdbc.OracleConnection.OCSID_SEQUENCE_NUMBER_KEY)) {
                    short seq = 0;
                    if (value != null) {
                        try {
                            seq = Short.valueOf(value).shortValue();
                        } catch (NumberFormatException e) {
                            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 160, value).fillInStackTrace());
                        }
                    }
                    this.endToEndECIDSequenceNumber = seq;
                    this.endToEndAnyChanged = true;
                } else if (attribute.equals(oracle.jdbc.OracleConnection.OCSID_DBOP_KEY)) {
                    if (value != null && value.length() > this.endToEndMaxLength[4]) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 159, value).fillInStackTrace());
                    }
                    if ((value == null && this.endToEndValues[4] != null) || value != null) {
                        this.endToEndValues[4] = value;
                        this.endToEndHasChanged[4] = true;
                        this.endToEndAnyChanged = true;
                    }
                } else if (attribute.equals(oracle.jdbc.OracleConnection.OCSID_CLIENT_INFO_KEY)) {
                    if (value != null && value.length() > this.endToEndMaxLength[5]) {
                        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 159, value).fillInStackTrace());
                    }
                    if ((value == null && this.endToEndValues[5] != null) || value != null) {
                        this.endToEndValues[5] = value;
                        this.endToEndHasChanged[5] = true;
                        this.endToEndAnyChanged = true;
                    }
                } else {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_INVALID_NAME_FOR_CLIENTINFO, "OCSID." + attribute).fillInStackTrace());
                }
                updateTraceAttributes();
            } else {
                if (attribute.length() > 30) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 171).fillInStackTrace());
                }
                if (value != null && value.length() > 4000) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 172).fillInStackTrace());
                }
                doSetApplicationContext(nameSpace, attribute, value == null ? "" : value);
            }
            if (value == null) {
                this.clientInfo.remove(name);
            } else {
                this.clientInfo.put(name, value);
            }
        } catch (SQLException ex) {
            throw ((SQLClientInfoException) DatabaseError.createSQLClientInfoException(1, null, ex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public String getClientInfo(String name) throws SQLException {
        requireOpenConnection();
        if (name == null) {
            return null;
        }
        if (!SUPPORTED_NAME_PATTERN.matcher(name).matches()) {
            throw ((SQLClientInfoException) DatabaseError.createSQLClientInfoException(DatabaseError.EOJ_INVALID_NAME_FOR_CLIENTINFO, null, null).fillInStackTrace());
        }
        return this.clientInfo.getProperty(name);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    @Blind(PropertiesBlinder.class)
    public Properties getClientInfo() throws SQLException {
        requireOpenConnection();
        return (Properties) this.clientInfo.clone();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    @Blind(PropertiesBlinder.class)
    public Properties getClientInfoInternal() throws SQLException {
        return (Properties) this.clientInfo.clone();
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setApplicationContext(String nameSpace, String attribute, String value) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (attribute == null) {
                throw new NullPointerException();
            }
            if (nameSpace == null || nameSpace.equals("")) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 170).fillInStackTrace());
            }
            setClientInfoInternal(nameSpace + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR + attribute, value);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void doSetApplicationContext(String nameSpace, String attribute, String value) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doSetApplicationContext").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void clearAllApplicationContext(String nameSpace) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (nameSpace == null) {
                throw new NullPointerException();
            }
            if (nameSpace.equals("")) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 170).fillInStackTrace());
            }
            doClearAllApplicationContext(nameSpace);
            if (!nameSpace.equals(oracle.jdbc.OracleConnection.OCSID_NAMESPACE)) {
                for (String key : this.clientInfo.stringPropertyNames()) {
                    if (key.startsWith(nameSpace + oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR)) {
                        this.clientInfo.remove(key);
                    }
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void doClearAllApplicationContext(String nameSpace) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doClearAllApplicationContext").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public byte[] createLightweightSession(String userName, KeywordValueLong[] inKeyVal, int inFlags, KeywordValueLong[][] outKeyVal, int[] outFlags) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("createLightweightSession").fillInStackTrace());
    }

    void executeLightweightSessionRoundtrip(int functionId, byte[] sessionId, KeywordValueLong[] inKeyVal, int inFlags, KeywordValueLong[][] outKeyVal, int[] outFlags) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("executeLightweightSessionRoundtrip").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void executeLightweightSessionPiggyback(int functionId, byte[] sessionId, KeywordValueLong[] inKeyVal, int inFlags) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("executeLightweightSessionPiggyback").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void doXSNamespaceOp(OracleConnection.XSOperationCode operationCode, byte[] sessionId, XSNamespace[] namespaces, XSNamespace[][] returnedNamespaces, XSSecureId secureId) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doXSNamespaceOp").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void doXSNamespaceOp(OracleConnection.XSOperationCode operationCode, byte[] sessionId, XSNamespace[] namespaces, XSSecureId secureId) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doXSNamespaceOp").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public byte[] doXSSessionCreateOp(OracleConnection.XSSessionOperationCode opcode, XSSecureId sidp, byte[] cookie, XSPrincipal username, String tenant, XSNamespace[] namespaces, OracleConnection.XSSessionModeFlag mode, XSKeyval Kv) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doXSSessionCreateOp").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void doXSSessionDestroyOp(byte[] sessionId, XSSecureId sidp, byte[] cookie) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doXSSessionDestroyOp").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void doXSSessionDetachOp(int opcode, byte[] sessionId, XSSecureId sidp, boolean roundTripRPC) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doXSSessionDetachOp").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void doXSSessionChangeOp(OracleConnection.XSSessionSetOperationCode opCode, byte[] sessionId, XSSecureId sidp, XSSessionParameters sessParam) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doXSSessionChangeOp").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void doXSSessionAttachOp(int opCode, byte[] sessionId, XSSecureId sidp, byte[] cookie, XSPrincipal username, String[] disabledRoles, String[] enabledRoles, String[] externalRoles, XSNamespace[] namespaces, XSNamespace[] cacheNamespace, XSNamespace[] deleteNamespace, TIMESTAMPTZ midTierTimestamp, TIMESTAMPTZ authtime, int roleVersion, long inputFlag, XSKeyval Kv, int[] roleVersionOutput) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doXSSessionAttachOp").fillInStackTrace());
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v3, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void enqueue(String queueName, AQEnqueueOptions opt, AQMessage mesg) throws SQLException {
        AQMessageI aQMessageI = (AQMessageI) mesg;
        ?? r0 = new byte[1];
        try {
            updateSystemContext();
            doEnqueue(queueName, opt, aQMessageI.getMessagePropertiesI(), aQMessageI.getPayloadTOID(), aQMessageI.getPayloadVersion(), aQMessageI.getPayload(), r0, aQMessageI.isRAWPayload());
            if (r0[0] != 0) {
                aQMessageI.setMessageId(r0[0]);
            }
        } catch (SQLException e) {
            resetSystemContext();
            throw e;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public AQMessage dequeue(String queueName, AQDequeueOptions opt, byte[] toid) throws SQLException {
        String typeName = OracleTypeADT.toid2typename(this, toid);
        return dequeue(queueName, opt, typeName);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v12, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public AQMessage dequeue(String str, AQDequeueOptions aQDequeueOptions, byte[] bArr, int i) throws SQLException {
        Monitor.CloseableLock closeableLockAcquireCloseableLock = acquireCloseableLock();
        Throwable th = null;
        try {
            ?? r0 = new byte[1];
            AQMessagePropertiesI aQMessagePropertiesI = new AQMessagePropertiesI();
            ?? r02 = new byte[1];
            try {
                updateSystemContext();
                AQMessageI aQMessageI = null;
                if (doDequeue(str, aQDequeueOptions, aQMessagePropertiesI, bArr, i, r02, r0, AQMessageI.compareToid(bArr, TypeDescriptor.RAWTOID))) {
                    AQMessageI aQMessageI2 = new AQMessageI(aQMessagePropertiesI, this);
                    aQMessageI2.setPayload(r02[0], bArr);
                    aQMessageI2.setMessageId(r0[0]);
                    aQMessageI = aQMessageI2;
                }
                return aQMessageI;
            } catch (SQLException e) {
                resetSystemContext();
                throw e;
            }
        } finally {
            if (closeableLockAcquireCloseableLock != null) {
                if (0 != 0) {
                    try {
                        closeableLockAcquireCloseableLock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    closeableLockAcquireCloseableLock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public AQMessage dequeue(String queueName, AQDequeueOptions opt, String typeName) throws SQLException {
        byte[] toid;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                int version = 1;
                TypeDescriptor sd = null;
                if (RAW_STR.equals(typeName) || SYS_RAW_STR.equals(typeName)) {
                    toid = TypeDescriptor.RAWTOID;
                } else if (SYS_ANYDATA_STR.equals(typeName)) {
                    toid = TypeDescriptor.ANYDATATOID;
                } else if (SYS_XMLTYPE_STR.equals(typeName)) {
                    toid = TypeDescriptor.XMLTYPETOID;
                } else if (JSON_STR.equals(typeName)) {
                    toid = TypeDescriptor.JSONTOID;
                } else {
                    sd = TypeDescriptor.getTypeDescriptor(typeName, this);
                    toid = ((OracleTypeADT) sd.getPickler()).getTOID();
                    version = ((OracleTypeADT) sd.getPickler()).getTypeVersion();
                }
                AQMessageI msg = (AQMessageI) dequeue(queueName, opt, toid, version);
                if (msg != null) {
                    msg.setTypeName(typeName);
                    msg.setTypeDescriptor(sd);
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return msg;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public int enqueue(String queueName, AQEnqueueOptions opt, AQMessage[] mesgs) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("enqueue[]").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public AQMessage[] dequeue(String queueName, AQDequeueOptions opt, String typeName, int deqSize) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("dequeue[]").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public AQMessage[] dequeue(String queueName, AQDequeueOptions opt, byte[] tdo, int version, int deqsize) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("dequeue[]").fillInStackTrace());
    }

    void doEnqueue(String queueName, AQEnqueueOptions enqueueOptions, AQMessagePropertiesI prop, byte[] payloadTDO, int payloadVersion, byte[] payload, byte[][] msgId, boolean isRawPayload) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doEnqueue").fillInStackTrace());
        } catch (Throwable th2) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    boolean doDequeue(String queueName, AQDequeueOptions dequeueOptions, AQMessagePropertiesI msgProp, byte[] payloadTDO, int payloadVersion, byte[][] payload, byte[][] msgid, boolean isRawQueue) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doDequeue").fillInStackTrace());
        } catch (Throwable th2) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v1, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.internal.OracleConnection
    public void jmsEnqueue(String queueName, JMSEnqueueOptions jmsEnqueueOpt, JMSMessage jMSMessage, AQMessageProperties aqMesgProp) throws SQLException {
        ?? r0 = new byte[1];
        AQMessagePropertiesI aqMesgPropI = (AQMessagePropertiesI) aqMesgProp;
        if (jMSMessage.getStreamPayload() != null) {
            doJMSEnqueue(queueName, jmsEnqueueOpt, aqMesgPropI, jMSMessage.getJMSMessageProperties(), jMSMessage.getToid(), jMSMessage.getStreamPayload(), r0, jMSMessage.getChunkSize());
        } else {
            doJMSEnqueue(queueName, jmsEnqueueOpt, aqMesgPropI, jMSMessage.getJMSMessageProperties(), jMSMessage.getToid(), jMSMessage.getPayload(), r0);
        }
        if (r0[0] != 0) {
            jMSMessage.setMessageId(r0[0]);
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void jmsEnqueue(String queueName, JMSEnqueueOptions jmsEnqueueOpt, JMSMessage[] mesgs, AQMessageProperties[] aqMesgProps) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("jmsEnqueue").fillInStackTrace());
    }

    void doJMSEnqueue(String queueName, JMSEnqueueOptions jmsEnqueueOpt, AQMessagePropertiesI prop, JMSMessageProperties jmsProp, byte[] payloadTDO, InputStream payload, byte[][] msgId, int bBlockSize) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doJMSEnqueue").fillInStackTrace());
        } catch (Throwable th2) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    void doJMSEnqueue(String queueName, JMSEnqueueOptions jmsEnqueueOpt, AQMessagePropertiesI prop, JMSMessageProperties jmsProp, byte[] payloadTDO, byte[] payload, byte[][] msgId) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doJMSEnqueue").fillInStackTrace());
        } catch (Throwable th2) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public JMSMessage jmsDequeue(String queueName, JMSDequeueOptions opt, String typeName) throws SQLException {
        JMSMessage msg = jmsDequeue(queueName, opt);
        return msg;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v2, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r0v6, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.internal.OracleConnection
    public JMSMessage jmsDequeue(String str, JMSDequeueOptions jMSDequeueOptions) throws SQLException {
        byte[] bArr = TypeDescriptor.RAWTOID;
        ?? r0 = new byte[1];
        AQMessagePropertiesI aQMessagePropertiesI = new AQMessagePropertiesI();
        JMSMessagePropertiesI jMSMessagePropertiesI = new JMSMessagePropertiesI();
        ?? r02 = new byte[1];
        JMSMessage jMSMessage = null;
        if (doJmsDequeue(str, jMSDequeueOptions, aQMessagePropertiesI, jMSMessagePropertiesI, bArr, (byte[][]) r02, (byte[][]) r0)) {
            JMSMessage jMSMessageCreateJMSMessage = JMSFactory.createJMSMessage(jMSMessagePropertiesI);
            jMSMessageCreateJMSMessage.setPayload(r02[0]);
            jMSMessageCreateJMSMessage.setMessageId(r0[0]);
            jMSMessageCreateJMSMessage.setJMSMessageProperties(jMSMessagePropertiesI);
            jMSMessageCreateJMSMessage.setAQMessageProperties(aQMessagePropertiesI);
            jMSMessage = jMSMessageCreateJMSMessage;
        }
        return jMSMessage;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public JMSMessage[] jmsDequeue(String queueName, JMSDequeueOptions opt, int size) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("jmsDequeue").fillInStackTrace());
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v2, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.internal.OracleConnection
    public JMSMessage jmsDequeue(String str, JMSDequeueOptions jMSDequeueOptions, OutputStream outputStream) throws SQLException {
        byte[] bArr = TypeDescriptor.RAWTOID;
        ?? r0 = new byte[1];
        AQMessagePropertiesI aQMessagePropertiesI = new AQMessagePropertiesI();
        JMSMessagePropertiesI jMSMessagePropertiesI = new JMSMessagePropertiesI();
        JMSMessage jMSMessage = null;
        if (doJmsDequeue(str, jMSDequeueOptions, aQMessagePropertiesI, jMSMessagePropertiesI, bArr, outputStream, (byte[][]) r0)) {
            JMSMessage jMSMessageCreateJMSMessage = JMSFactory.createJMSMessage(jMSMessagePropertiesI);
            jMSMessageCreateJMSMessage.setMessageId(r0[0]);
            jMSMessageCreateJMSMessage.setJMSMessageProperties(jMSMessagePropertiesI);
            jMSMessageCreateJMSMessage.setAQMessageProperties(aQMessagePropertiesI);
            jMSMessage = jMSMessageCreateJMSMessage;
        }
        return jMSMessage;
    }

    boolean doJmsDequeue(String queueName, JMSDequeueOptions dequeueOptions, AQMessagePropertiesI msgProp, JMSMessagePropertiesI jmsProp, byte[] payloadTDO, OutputStream payload, byte[][] msgid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doJmsDequeue").fillInStackTrace());
        } catch (Throwable th2) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    boolean doJmsDequeue(String queueName, JMSDequeueOptions dequeueOptions, AQMessagePropertiesI msgProp, JMSMessagePropertiesI jmsProp, byte[] payloadTDO, byte[][] payload, byte[][] msgid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doJmsDequeue").fillInStackTrace());
        } catch (Throwable th2) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public byte[] beginSaga(String initiatorName, int timeout, String currentUser, int version, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        try {
            updateSystemContext();
            byte[] sagaId = doBeginSaga(initiatorName, timeout, currentUser, version, opcode, flags, spareNumeric, spareText);
            return sagaId;
        } catch (SQLException e) {
            resetSystemContext();
            throw e;
        }
    }

    public byte[] doBeginSaga(String initiatorName, int timeout, String currentUser, int version, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doBeginSaga").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Integer joinSaga(String participantName, byte[] sagaId, String coordinatorName, String initiatorName, int timeout, int version, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        try {
            updateSystemContext();
            int joinStatus = doJoinSaga(participantName, sagaId, coordinatorName, initiatorName, timeout, version, opcode, flags, spareNumeric, spareText).intValue();
            return Integer.valueOf(joinStatus);
        } catch (SQLException e) {
            resetSystemContext();
            throw e;
        }
    }

    public Integer doJoinSaga(String participantName, byte[] sagaId, String coordinatorName, String initiatorName, int timeout, int version, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doJoinSaga").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Integer commitRollbackSaga(String participantName, byte[] sagaId, String currentUser, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        try {
            updateSystemContext();
            int sagaStatus = doCommitRollbackSaga(participantName, sagaId, currentUser, opcode, flags, spareNumeric, spareText).intValue();
            return Integer.valueOf(sagaStatus);
        } catch (SQLException e) {
            resetSystemContext();
            throw e;
        }
    }

    public Integer doCommitRollbackSaga(String participantName, byte[] sagaId, String currentUser, int opcode, int flags, int spareNumeric, String spareText) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doCommitRollbackSaga").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    @Deprecated
    public boolean isV8Compatible() throws SQLException {
        return this.mapDateToTimestamp;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean getMapDateToTimestamp() {
        return this.mapDateToTimestamp;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public byte getInstanceProperty(OracleConnection.InstanceProperty whatProperty) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getInstanceProperty").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Map<String, JMSNotificationRegistration> registerJMSNotification(String[] name, Map<String, Properties> options, String selector) throws SQLException {
        if (name == null || options == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "name and options cannot be null").fillInStackTrace());
        }
        Map<String, JMSNotificationRegistration> registrations = doRegisterJMSNotification(name, options, selector);
        return registrations;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Map<String, JMSNotificationRegistration> registerJMSNotification(String[] name, Map<String, Properties> options) throws SQLException {
        if (name == null || options == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "name and options cannot be null").fillInStackTrace());
        }
        Map<String, JMSNotificationRegistration> registrations = doRegisterJMSNotification(name, options, null);
        return registrations;
    }

    Map<String, JMSNotificationRegistration> doRegisterJMSNotification(String[] name, Map<String, Properties> options, String selector) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("JMSNotificationRegistration").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void unregisterJMSNotification(JMSNotificationRegistration registration) throws SQLException {
        if (registration == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "registration cannot be null").fillInStackTrace());
        }
        NTFJMSRegistration jmsRegistration = (NTFJMSRegistration) registration;
        doUnregisterJMSNotification(jmsRegistration);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void startJMSNotification(JMSNotificationRegistration registration) throws SQLException {
        if (registration == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "registration cannot be null").fillInStackTrace());
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NTFJMSRegistration jmsRegistration = (NTFJMSRegistration) registration;
                doStartJMSNotification(jmsRegistration);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void stopJMSNotification(JMSNotificationRegistration registration) throws SQLException {
        if (registration == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "registration cannot be null").fillInStackTrace());
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NTFJMSRegistration jmsRegistration = (NTFJMSRegistration) registration;
                doStopJMSNotification(jmsRegistration);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void doUnregisterJMSNotification(NTFJMSRegistration registration) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doUnregisterJMSNotification").fillInStackTrace());
    }

    void doStartJMSNotification(NTFJMSRegistration registration) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doStartJMSNotification").fillInStackTrace());
    }

    void doStopJMSNotification(NTFJMSRegistration registration) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doStopJMSNotification").fillInStackTrace());
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v26, types: [byte[], byte[][]] */
    @Override // oracle.jdbc.internal.OracleConnection
    public void ackJMSNotification(JMSNotificationRegistration registration, byte[] lastMessageId, JMSNotificationRegistration.Directive directive) throws SQLException {
        if (directive == null || registration == null || lastMessageId == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "directive,registration or lastMessageID cannot be null").fillInStackTrace());
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                short directiveValue = directive.getCode();
                ArrayList<JMSNotificationRegistration> regList = new ArrayList<>(1);
                regList.add(registration);
                doAckJMSNtfn(regList, new byte[]{lastMessageId}, directiveValue);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void ackJMSNotification(ArrayList<JMSNotificationRegistration> regList, byte[][] lastMessageIds, JMSNotificationRegistration.Directive directive) throws SQLException {
        if (directive == null || regList == null || lastMessageIds == null) {
            throw ((SQLException) DatabaseError.createSqlException(68, "directive,registration or lastMessageID cannot be null").fillInStackTrace());
        }
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                short directiveValue = directive.getCode();
                doAckJMSNtfn(regList, lastMessageIds, directiveValue);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void doAckJMSNtfn(ArrayList<JMSNotificationRegistration> regList, byte[][] lastMessageIds, short directiveValue) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doAckJMSNtfn").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public AQNotificationRegistration[] registerAQNotification(String[] name, Properties[] options, @Blind(PropertiesBlinder.class) Properties globalOptions) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String dcnhost = readNTFlocalhost(globalOptions);
            int tcpport = readNTFtcpport(globalOptions);
            boolean useSSL = readNTFuseSSL(globalOptions);
            NTFAQRegistration[] registrations = doRegisterAQNotification(name, dcnhost, tcpport, useSSL, options);
            NTFAQRegistration[] nTFAQRegistrationArr = registrations;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return nTFAQRegistrationArr;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    NTFAQRegistration[] doRegisterAQNotification(String[] name, String dcnhost, int tcpport, boolean useSSL, Properties[] options) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doRegisterAQNotification").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void unregisterAQNotification(AQNotificationRegistration registration) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NTFAQRegistration aqregistration = (NTFAQRegistration) registration;
                doUnregisterAQNotification(aqregistration);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void doUnregisterAQNotification(NTFAQRegistration registration) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doUnregisterAQNotification").fillInStackTrace());
    }

    private String readNTFlocalhost(@Blind(PropertiesBlinder.class) Properties options) throws SQLException {
        try {
            String dcnhost = options.getProperty(oracle.jdbc.OracleConnection.NTF_LOCAL_HOST, InetAddress.getLocalHost().getHostAddress());
            return dcnhost;
        } catch (SecurityException e) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 241).fillInStackTrace());
        } catch (UnknownHostException e2) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NTF_UNKNOWN_LOCALHOST).fillInStackTrace());
        }
    }

    private int readNTFtcpport(@Blind(PropertiesBlinder.class) Properties options) throws SQLException, NumberFormatException {
        try {
            int tcpport = Integer.parseInt(options.getProperty(oracle.jdbc.OracleConnection.NTF_LOCAL_TCP_PORT, "0"));
            if (tcpport < 0) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NTF_TCP_OPTION).fillInStackTrace());
            }
            return tcpport;
        } catch (NumberFormatException e) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NTF_TCP_OPTION).fillInStackTrace());
        }
    }

    int readNTFtimeout(@Blind(PropertiesBlinder.class) Properties options) throws SQLException, NumberFormatException {
        try {
            int timeout = Integer.parseInt(options.getProperty(oracle.jdbc.OracleConnection.NTF_TIMEOUT, "0"));
            return timeout;
        } catch (NumberFormatException e) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NTF_TIMEOUT_OPTION).fillInStackTrace());
        }
    }

    boolean readNTFuseSSL(@Blind(PropertiesBlinder.class) Properties options) throws SQLException {
        boolean useSSL = Boolean.parseBoolean(options.getProperty(oracle.jdbc.OracleConnection.NTF_USE_SSL, "false"));
        return useSSL;
    }

    private Properties prepareAndGetDcnOptions(Properties optionsArg) throws SQLException, IOException {
        if (this.dcnOptions == null) {
            return optionsArg;
        }
        String dcnOptionsFormatted = this.dcnOptions.replaceAll(",", "\n");
        Properties dcnConnectionProperties = new Properties();
        try {
            dcnConnectionProperties.load(new StringReader(dcnOptionsFormatted));
            Properties options = new Properties();
            options.putAll(optionsArg);
            options.putAll(dcnConnectionProperties);
            return options;
        } catch (IOException e) {
            throw new SQLException(e);
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Debug(level = Debug.Level.CONFIG)
    public DatabaseChangeRegistration registerDatabaseChangeNotification(@Blind(PropertiesBlinder.class) Properties optionsArg) throws SQLException {
        try {
            debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "registerDatabaseChangeNotification", "entering args ({0})", (String) null, (String) null, new PropertiesBlinder().blind((PropertiesBlinder) optionsArg));
            Properties options = prepareAndGetDcnOptions(optionsArg);
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                String dcnhost = readNTFlocalhost(options);
                int tcpport = readNTFtcpport(options);
                int timeout = readNTFtimeout(options);
                try {
                    int txnlag = Integer.parseInt(options.getProperty(oracle.jdbc.OracleConnection.DCN_NOTIFY_CHANGELAG, "0"));
                    NTFDCNRegistration registration = doRegisterDatabaseChangeNotification(dcnhost, tcpport, options, timeout, txnlag);
                    ntfManager.addRegistration(registration);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "registerDatabaseChangeNotification", "returning {0}", (String) null, (String) null, registration);
                    return registration;
                } catch (NumberFormatException e) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_DCN_CHANGELAG_OPTION).fillInStackTrace());
                }
            } finally {
            }
        } catch (Throwable th3) {
            debug(Level.CONFIG, SecurityLabel.INTERNAL, "oracle.jdbc.driver.PhysicalConnection", "registerDatabaseChangeNotification", "throwing", (String) null, (String) th3, new Object[0]);
            throw th3;
        }
    }

    NTFDCNRegistration doRegisterDatabaseChangeNotification(String dcnhost, int tcpport, @Blind(PropertiesBlinder.class) Properties options, int timeout, int txnlag) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doRegisterDatabaseChangeNotification").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public DatabaseChangeRegistration getDatabaseChangeRegistration(int regid) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                NTFDCNRegistration registration = new NTFDCNRegistration(this.dbName, regid, this.userName, this.versionNumber);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return registration;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void unregisterDatabaseChangeNotification(DatabaseChangeRegistration registration) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            NTFDCNRegistration dcnregistration = (NTFDCNRegistration) registration;
            if (dcnregistration.getDatabaseName().compareToIgnoreCase(this.dbName) != 0) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 245).fillInStackTrace());
            }
            doUnregisterDatabaseChangeNotification(dcnregistration);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void doUnregisterDatabaseChangeNotification(NTFDCNRegistration registration) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doUnregisterDatabaseChangeNotification").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void unregisterDatabaseChangeNotification(int registrationId) throws SQLException {
        String dcnhost = null;
        try {
            dcnhost = InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
        }
        unregisterDatabaseChangeNotification(registrationId, dcnhost, oracle.jdbc.OracleConnection.NTF_DEFAULT_TCP_PORT);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void unregisterDatabaseChangeNotification(int registrationId, String host, int tcpport) throws SQLException {
        String location = "(ADDRESS=(PROTOCOL=tcp)(HOST=" + host + ")(PORT=" + tcpport + "))?PR=0";
        unregisterDatabaseChangeNotification(registrationId, location);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void unregisterDatabaseChangeNotification(long registrationId, String callback) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                doUnregisterDatabaseChangeNotification(registrationId, callback);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void doUnregisterDatabaseChangeNotification(long registrationId, String callback) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doUnregisterDatabaseChangeNotification").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void addXSEventListener(XSEventListener listener) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("addXSEventListener").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void addXSEventListener(XSEventListener listener, Executor executor) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("addXSEventListener").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void removeXSEventListener(XSEventListener listener) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("removeXSEventListener").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void removeAllXSEventListener() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("removeAllXSEventListener").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setPDBChangeEventListener(PDBChangeEventListener l, Executor e) throws SQLException {
        requireOpenConnection();
        NTFEventListener listener = new NTFEventListener(l);
        listener.setExecutor(e);
        if (this.pdbChangeListener == null) {
            this.pdbChangeListener = listener;
            return;
        }
        throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 248).fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setPDBChangeEventListener(PDBChangeEventListener l) throws SQLException {
        setPDBChangeEventListener(l, null);
    }

    void notify(final NTFPDBChangeEvent event) {
        if (this.pdbChangeListener != null) {
            Executor exec = this.pdbChangeListener.getExecutor();
            if (exec != null) {
                final PDBChangeEventListener l = this.pdbChangeListener.getPDBChangeEventListener();
                exec.execute(new Runnable() { // from class: oracle.jdbc.driver.PhysicalConnection.4
                    @Override // java.lang.Runnable
                    public void run() {
                        l.pdbChanged(event);
                    }
                });
            } else {
                this.pdbChangeListener.getPDBChangeEventListener().pdbChanged(event);
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void addLogicalTransactionIdEventListener(LogicalTransactionIdEventListener listener) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("addLogicalTransactionIdEventListener").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void addLogicalTransactionIdEventListener(LogicalTransactionIdEventListener listener, Executor executor) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("addLogicalTransactionIdEventListener").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void removeLogicalTransactionIdEventListener(LogicalTransactionIdEventListener listener) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("removeLogicalTransactionIdEventListener").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public oracle.jdbc.LogicalTransactionId getLogicalTransactionId() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getLogicalTransactionId").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TypeDescriptor[] getAllTypeDescriptorsInCurrentSchema() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Statement stmt = null;
            beginNonRequestCalls();
            try {
                try {
                    Statement stmt2 = createStatement();
                    ResultSet rst = stmt2.executeQuery("SELECT schema_name, typename, typoid, typecode, version, tds  FROM TABLE(private_jdbc.Get_Type_Shape_Info())");
                    TypeDescriptor[] result = getTypeDescriptorsFromResultSet(rst);
                    rst.close();
                    if (stmt2 != null) {
                        stmt2.close();
                    }
                    endNonRequestCalls();
                    return result;
                } catch (Throwable th2) {
                    if (0 != 0) {
                        stmt.close();
                    }
                    endNonRequestCalls();
                    throw th2;
                }
            } catch (SQLException ex) {
                if (ex.getErrorCode() == 904) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 165).fillInStackTrace());
                }
                throw ex;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TypeDescriptor[] getTypeDescriptorsFromListInCurrentSchema(String[] typeNames) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            PreparedStatement stmt = null;
            beginNonRequestCalls();
            try {
                try {
                    PreparedStatement stmt2 = prepareStatement("SELECT schema_name, typename, typoid, typecode, version, tds  FROM TABLE(private_jdbc.Get_Type_Shape_Info(?))");
                    int typeNamesLength = typeNames.length;
                    StringBuffer sb = new StringBuffer(typeNamesLength * 8);
                    for (int i = 0; i < typeNamesLength; i++) {
                        sb.append(typeNames[i]);
                        if (i < typeNamesLength - 1) {
                            sb.append(',');
                        }
                    }
                    stmt2.setString(1, sb.toString());
                    ResultSet rst = stmt2.executeQuery();
                    TypeDescriptor[] result = getTypeDescriptorsFromResultSet(rst);
                    rst.close();
                    if (stmt2 != null) {
                        stmt2.close();
                    }
                    endNonRequestCalls();
                    return result;
                } catch (Throwable th2) {
                    if (0 != 0) {
                        stmt.close();
                    }
                    endNonRequestCalls();
                    throw th2;
                }
            } catch (SQLException ex) {
                if (ex.getErrorCode() == 904) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 165).fillInStackTrace());
                }
                throw ex;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TypeDescriptor[] getTypeDescriptorsFromList(String[][] schemaAndTypeNamePairs) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            PreparedStatement stmt = null;
            int length = schemaAndTypeNamePairs.length;
            StringBuffer schemaNames = new StringBuffer(length * 8);
            StringBuffer typeNames = new StringBuffer(length * 8);
            for (int i = 0; i < length; i++) {
                schemaNames.append(schemaAndTypeNamePairs[i][0]);
                typeNames.append(schemaAndTypeNamePairs[i][1]);
                if (i < length - 1) {
                    schemaNames.append(',');
                    typeNames.append(',');
                }
            }
            beginNonRequestCalls();
            try {
                try {
                    PreparedStatement stmt2 = prepareStatement("SELECT schema_name, typename, typoid, typecode, version, tds FROM TABLE(private_jdbc.Get_All_Type_Shape_Info(?,?))");
                    stmt2.setString(1, schemaNames.toString());
                    stmt2.setString(2, typeNames.toString());
                    ResultSet rst = stmt2.executeQuery();
                    TypeDescriptor[] result = getTypeDescriptorsFromResultSet(rst);
                    rst.close();
                    if (stmt2 != null) {
                        stmt2.close();
                    }
                    endNonRequestCalls();
                    return result;
                } catch (Throwable th2) {
                    if (0 != 0) {
                        stmt.close();
                    }
                    endNonRequestCalls();
                    throw th2;
                }
            } catch (SQLException ex) {
                if (ex.getErrorCode() == 904) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 165).fillInStackTrace());
                }
                throw ex;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    TypeDescriptor[] getTypeDescriptorsFromResultSet(ResultSet rst) throws SQLException {
        ArrayList<TypeDescriptor> list = new ArrayList<>();
        while (rst.next()) {
            String schema = rst.getString(1);
            String typename = rst.getString(2);
            byte[] typoid = rst.getBytes(3);
            String typecode = rst.getString(4);
            int version = rst.getInt(5);
            byte[] tds = rst.getBytes(6);
            SQLName sqlName = new SQLName(schema, typename, this);
            if (typecode.equals("OBJECT")) {
                TypeDescriptor desc = StructDescriptor.createDescriptor(sqlName, typoid, version, tds, this);
                putDescriptor(typoid, desc);
                putDescriptor(desc.getName(), desc);
                list.add(desc);
            } else if (typecode.equals("COLLECTION")) {
                TypeDescriptor desc2 = ArrayDescriptor.createDescriptor(sqlName, typoid, version, tds, this);
                putDescriptor(typoid, desc2);
                putDescriptor(desc2.getName(), desc2);
                list.add(desc2);
            }
        }
        TypeDescriptor[] result = new TypeDescriptor[list.size()];
        for (int i = 0; i < list.size(); i++) {
            result[i] = list.get(i);
        }
        return result;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean isUsable() {
        return isUsable(true);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isUsable(boolean draining) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.isUsable && draining) {
                    try {
                        if (checkAndDrain()) {
                            if (lock != null) {
                                if (0 != 0) {
                                    try {
                                        lock.close();
                                    } catch (Throwable th2) {
                                        th.addSuppressed(th2);
                                    }
                                } else {
                                    lock.close();
                                }
                            }
                            return false;
                        }
                    } catch (SQLException sqlexc) {
                        debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "isUsable", null, (String) null, sqlexc);
                    }
                }
                boolean z = this.isUsable;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return z;
            } finally {
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setUsable(boolean isUsable) {
        this.isUsable = isUsable;
    }

    void queryFCFProperties() throws SQLException {
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = createStatement();
            stmt.setFetchSize(1);
            rs = stmt.executeQuery("select sys_context('userenv', 'instance_name'),sys_context('userenv', 'server_host'),sys_context('userenv', 'service_name'),sys_context('userenv', 'db_unique_name') from dual");
            while (rs.next()) {
                String val = rs.getString(1);
                if (val != null) {
                    updateSessionProperties(INSTANCE_NAME, val.trim());
                }
                String val2 = rs.getString(2);
                if (val2 != null) {
                    updateSessionProperties(SERVER_HOST, val2.trim());
                }
                String val3 = rs.getString(3);
                if (val3 != null) {
                    updateSessionProperties(SERVICE_NAME, val3.trim());
                }
                String val4 = rs.getString(4);
                if (val4 != null) {
                    updateSessionProperties(DATABASE_NAME, val4.trim());
                }
            }
            if (rs != null) {
                rs.close();
            }
            if (stmt != null) {
                stmt.close();
            }
        } catch (Throwable th) {
            if (rs != null) {
                rs.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            throw th;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setDefaultTimeZone(TimeZone tz) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.defaultTimeZone = tz;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public TimeZone getDefaultTimeZone() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            TimeZone timeZone = this.defaultTimeZone;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return timeZone;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getTimezoneVersionNumber() throws SQLException {
        return this.timeZoneVersionNumber;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public TIMEZONETAB getTIMEZONETAB() throws SQLException {
        if (this.timeZoneTab == null) {
            this.timeZoneTab = TIMEZONETAB.getInstance(getTimezoneVersionNumber());
        }
        return this.timeZoneTab;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isDataInLocatorEnabled() throws SQLException {
        return ((getVersionNumber() >= 10200) & (getVersionNumber() < 11000) & this.enableReadDataInLocator) | this.overrideEnableReadDataInLocator;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isLobStreamPosStandardCompliant() throws SQLException {
        return this.lobStreamPosStandardCompliant;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public long getCurrentSCN() throws SQLException {
        return doGetCurrentSCN();
    }

    long doGetCurrentSCN() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doGetCurrentSCN").fillInStackTrace());
    }

    void doSetSnapshotSCN(long scn) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doSetSnapshotSCN").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public EnumSet<OracleConnection.TransactionState> getTransactionState() throws SQLException {
        return doGetTransactionState();
    }

    EnumSet<OracleConnection.TransactionState> doGetTransactionState() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doGetTransactionState").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean inLocalTransaction() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            EnumSet<OracleConnection.TransactionState> txnStates = getTransactionState();
            debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "inLocalTransaction", "transaction state: {0}", (String) null, (String) null, (Object) txnStates);
            if (txnStates.contains(OracleConnection.TransactionState.LOCAL_TRANSACTION_STARTED)) {
                if (!txnStates.contains(OracleConnection.TransactionState.TRANSACTION_READONLY)) {
                    return true;
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return false;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isConnectionSocketKeepAlive() throws SQLException, SocketException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("isConnectionSocketKeepAlive").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setReplayOperations(EnumSet<OracleConnection.ReplayOperation> ops) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setReplayOperations").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void beginNonRequestCalls() throws SQLException {
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void endNonRequestCalls() throws SQLException {
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setReplayContext(oracle.jdbc.internal.ReplayContext[] context) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setReplayContext").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setReplayingMode(boolean isReplaying) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setReplayingMode").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void registerEndReplayCallback(OracleConnection.EndReplayCallback callback) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("registerEndReplayCallback").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getEOC() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getEOC").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.ReplayContext[] getReplayContext() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getReplayContext").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.ReplayContext getLastReplayContext() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getLastReplayContext").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setLastReplayContext(oracle.jdbc.internal.ReplayContext cxt) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setLastReplayContext").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public byte[] getDerivedKeyInternal(byte[] dhKey, int mode) throws SQLException, InvalidKeySpecException, NoSuchAlgorithmException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getDerivedKeyInternal").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper
    public void abort(Executor executor) throws SQLException {
        SecurityManager security = System.getSecurityManager();
        if (security != null) {
            security.checkPermission(CALL_ABORT_PERMISSION);
        }
        if (executor == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NULL_EXECUTOR).fillInStackTrace());
        }
        if (this.lifecycle == 4 || this.lifecycle == 8) {
            return;
        }
        setLifecycle(8);
        doAbort();
        executor.execute(new Runnable() { // from class: oracle.jdbc.driver.PhysicalConnection.5
            @Override // java.lang.Runnable
            public void run() {
                try {
                    PhysicalConnection.this.close();
                } catch (Exception e) {
                } finally {
                    PhysicalConnection.this.setLifecycle(4);
                }
            }
        });
    }

    @Override // oracle.jdbc.OracleConnectionWrapper
    public String getSchema() throws SQLException {
        return getCurrentSchema();
    }

    @Override // oracle.jdbc.OracleConnectionWrapper
    public final void setNetworkTimeout(Executor executor, int milliseconds) throws SQLException {
        SecurityManager security = System.getSecurityManager();
        if (security != null) {
            security.checkPermission(CALL_SETNETWORKTIMEOUT_PERMISSION);
        }
        if (milliseconds < 0) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NEGATIVE_NETWORK_TIMEOUT).fillInStackTrace());
        }
        if (milliseconds > 0 && executor == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_NULL_EXECUTOR).fillInStackTrace());
        }
        this.closeExecutor = milliseconds == 0 ? null : executor;
        doSetNetworkTimeout(milliseconds);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper
    public int getNetworkTimeout() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getNetworkTimeout").fillInStackTrace());
    }

    protected void doSetNetworkTimeout(int milliseconds) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doSetNetworkTimeout").fillInStackTrace());
    }

    void doAsynchronousClose() {
        if (this.closeExecutor != null) {
            this.closeExecutor.execute(new Runnable() { // from class: oracle.jdbc.driver.PhysicalConnection.6
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        this.close();
                    } catch (Throwable th) {
                    }
                }
            });
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper
    public void setSchema(String schema) throws SQLException {
        if (schema == null) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
        }
        if (!schema.matches("(\"[^��\"]*\")|((\\p{javaLowerCase}|\\p{javaUpperCase})(\\p{javaLowerCase}|\\p{javaUpperCase}|\\d|_|\\$|#)*)")) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68).fillInStackTrace());
        }
        String sql = "alter session set current_schema = " + schema;
        Statement stmt = null;
        try {
            stmt = createStatement();
            stmt.execute(sql);
            if (stmt != null) {
                stmt.close();
            }
        } catch (Throwable th) {
            if (stmt != null) {
                stmt.close();
            }
            throw th;
        }
    }

    void releaseConnectionToPool() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("releaseConnectionToPool").fillInStackTrace());
    }

    boolean reusePooledConnection() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("reusePooledConnection").fillInStackTrace());
    }

    void resetAfterReusePooledConnection() throws SQLException {
        if (needToPurgeStatementCache()) {
            purgeStatementCache();
            closeStatements(false);
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean attachServerConnection() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            requireOpenConnection();
            if (!this.drcpEnabled) {
                return true;
            }
            boolean zReusePooledConnection = reusePooledConnection();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zReusePooledConnection;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean isDRCPEnabled() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            boolean z = this.drcpEnabled;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return z;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Can't find top splitter block for handler:B:22:0x003f
        	at jadx.core.utils.BlockUtils.getTopSplitterForHandler(BlockUtils.java:1178)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.collectHandlerRegions(ExcHandlersRegionMaker.java:53)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.process(ExcHandlersRegionMaker.java:38)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:27)
        */
    /* JADX WARN: Unreachable blocks removed: 14, instructions: 21 */
    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean isDRCPMultitagEnabled() throws java.sql.SQLException {
        /*
            r3 = this;
            r0 = r3
            oracle.jdbc.internal.Monitor$CloseableLock r0 = r0.acquireCloseableLock()
            r4 = r0
            r0 = 0
            r5 = r0
            r0 = 0
            r6 = r0
            r0 = r4
            if (r0 == 0) goto L27
            r0 = r5
            if (r0 == 0) goto L23
            r0 = r4
            r0.close()     // Catch: java.lang.Throwable -> L18
            goto L27
        L18:
            r7 = move-exception
            r0 = r5
            r1 = r7
            r0.addSuppressed(r1)
            goto L27
        L23:
            r0 = r4
            r0.close()
        L27:
            r0 = r6
            return r0
        L29:
            r6 = move-exception
            r0 = r6
            r5 = r0
            r0 = r6
            throw r0     // Catch: java.lang.Throwable -> L2e
        L2e:
            r8 = move-exception
            r0 = r4
            if (r0 == 0) goto L4e
            r0 = r5
            if (r0 == 0) goto L4a
            r0 = r4
            r0.close()     // Catch: java.lang.Throwable -> L3f
            goto L4e
        L3f:
            r9 = move-exception
            r0 = r5
            r1 = r9
            r0.addSuppressed(r1)
            goto L4e
        L4a:
            r0 = r4
            r0.close()
        L4e:
            r0 = r8
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.PhysicalConnection.isDRCPMultitagEnabled():boolean");
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getDRCPReturnTag() throws SQLException {
        return null;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getDRCPPLSQLCallbackName() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String str = this.drcpPLSQLCallback;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return str;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    private boolean isDRCPConnection(String url) throws SQLException {
        NVPair server;
        String sType;
        boolean retVal = false;
        if (getProtocolType().equals("kprb")) {
            return false;
        }
        if (url.matches("(?i:.*:POOLED)")) {
            retVal = true;
        } else {
            int end = url.length();
            int at_sign = url.indexOf(64);
            if (at_sign < end) {
                String tnsString = at_sign >= 0 ? url.substring(at_sign + 1) : url;
                int bracket = tnsString.indexOf(40);
                if (bracket < 0 && this.tnsAdmin != null) {
                    TNSNamesNamingAdapter tnna = new TNSNamesNamingAdapter(this.tnsAdmin);
                    try {
                        String tmpTnsString = tnna.resolve(tnsString);
                        if (tmpTnsString != null) {
                            tnsString = tmpTnsString;
                        }
                    } catch (NetException e) {
                    }
                }
                if (tnsString.matches("(?i:.*:POOLED)\\s*((\\?)(.*))*")) {
                    retVal = true;
                }
                NVFactory nvf = new NVFactory();
                NVNavigator nvn = new NVNavigator();
                NVPair cdata = null;
                try {
                    cdata = nvn.findNVPairRecurse(nvf.createNVPair(tnsString), "connect_data");
                } catch (NLException e2) {
                }
                if (cdata != null && (server = nvn.findNVPair(cdata, "SERVER")) != null && (sType = server.getAtom()) != null && (sType.equalsIgnoreCase("POOLED") || sType.equalsIgnoreCase("EMON"))) {
                    retVal = true;
                }
            }
        }
        return retVal;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void detachServerConnection(String tag) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                requireOpenConnection();
                if (this.drcpEnabled) {
                    if (tag != null && !tag.isEmpty()) {
                        this.drcpTagName = tag;
                    } else {
                        this.drcpTagName = null;
                    }
                    releaseConnectionToPool();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th4) {
                th = th4;
                throw th4;
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean needToPurgeStatementCache() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("needToPurgeStatementCache").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public short getExecutingRPCFunctionCode() {
        return (short) 0;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public String getExecutingRPCSQL() {
        return "";
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.ResultSetCache getResultSetCache() throws SQLException {
        return getResultSetCacheInternal();
    }

    ResultSetCache getResultSetCacheInternal() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getResultSetCacheInternal").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getNegotiatedSDU() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getNegotiatedSDU").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public byte getNegotiatedTTCVersion() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getNegotiatedTTCVersion").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setChecksumMode(OracleConnection.ChecksumMode mode) throws SQLException {
        this.checksumMode = mode;
    }

    public int getVarTypeMaxLenCompat() throws SQLException {
        return this.varTypeMaxLenCompat;
    }

    static final boolean bit(long flag, long mask) {
        return (flag & mask) == mask;
    }

    static final boolean bit(int flag, int mask) {
        return (flag & mask) == mask;
    }

    ArrayList<Long> getResultSetCacheLocalInvalidations() {
        return null;
    }

    void onPDBChange(OracleStatement catalyst) throws SQLException {
        debugp(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "onPDBChange", "Pluggable database has changed. Connection is now connected to PDB. AUTH_DB_ID={0}. ", (String) null, (Throwable) null, () -> {
            return new Object[]{this.sessionProperties.getProperty("AUTH_DB_ID")};
        });
        removeAllDescriptor();
        closeResultsets(catalyst);
        this.cachedCompatibleString = null;
        this.tenantName = getCurrentTenantName();
        updateSessionProperties(TENANT_NAME, this.tenantName);
        this.connectionDiagnosable.enableDebugTenant(this.tenantName);
    }

    void updateSessionProperties(String propertyName, String propertyValue) {
        if (this.sessionProperties == null) {
            this.sessionProperties = new Properties();
        }
        if (this.sessionPropertiesCopy == null) {
            this.sessionPropertiesCopy = new Properties();
        }
        if (this.sessionPropertiesDelta == null) {
            this.sessionPropertiesDelta = new Properties();
        }
        if (!this.isAutoONSConfigUpdated && AUTH_ONS_CONFIG.equals(propertyName)) {
            String newValue = getUpdatedAutoONSConfig(propertyValue);
            if (newValue != null && newValue.length() > 0) {
                this.sessionProperties.setProperty(AUTH_ONS_CONFIG, newValue);
                if (this.startTrackingDelta) {
                    this.sessionPropertiesDelta.setProperty(AUTH_ONS_CONFIG, newValue);
                } else {
                    this.sessionPropertiesCopy.setProperty(AUTH_ONS_CONFIG, newValue);
                }
            }
            this.isAutoONSConfigUpdated = true;
            return;
        }
        this.sessionProperties.setProperty(propertyName, propertyValue);
        if (this.startTrackingDelta) {
            this.sessionPropertiesDelta.setProperty(propertyName, propertyValue);
        } else {
            this.sessionPropertiesCopy.setProperty(propertyName, propertyValue);
        }
    }

    void updateSessionProperties(Properties props) {
        if (this.sessionProperties == null) {
            this.sessionProperties = new Properties();
        }
        if (this.sessionPropertiesCopy == null) {
            this.sessionPropertiesCopy = new Properties();
        }
        if (this.sessionPropertiesDelta == null) {
            this.sessionPropertiesDelta = new Properties();
        }
        this.sessionProperties.putAll(props);
        if (this.startTrackingDelta) {
            this.sessionPropertiesDelta.putAll(props);
        } else {
            this.sessionPropertiesCopy.putAll(props);
        }
        if (!this.isAutoONSConfigUpdated) {
            String onsConfig = this.sessionProperties.getProperty(AUTH_ONS_CONFIG);
            String newValue = getUpdatedAutoONSConfig(onsConfig);
            if (newValue != null && newValue.length() > 0) {
                this.sessionProperties.setProperty(AUTH_ONS_CONFIG, newValue);
                if (this.startTrackingDelta) {
                    this.sessionPropertiesDelta.setProperty(AUTH_ONS_CONFIG, newValue);
                } else {
                    this.sessionPropertiesCopy.setProperty(AUTH_ONS_CONFIG, newValue);
                }
            }
            this.isAutoONSConfigUpdated = true;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Blind(PropertiesBlinder.class)
    public Properties getServerSessionInfo() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            Properties properties = this.sessionProperties;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return properties;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    @Blind(PropertiesBlinder.class)
    public Properties getSessionInfoInternal() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (!this.startTrackingDelta) {
                this.startTrackingDelta = true;
                Properties properties = this.sessionPropertiesCopy;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return properties;
            }
            if (this.sessionPropertiesDelta == null || this.sessionPropertiesDelta.isEmpty()) {
                Properties properties2 = this.sessionPropertiesCopy;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return properties2;
            }
            Properties newCopy = new Properties();
            newCopy.putAll(this.sessionPropertiesCopy);
            newCopy.putAll(this.sessionPropertiesDelta);
            this.sessionPropertiesDelta.clear();
            this.sessionPropertiesCopy = newCopy;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            return newCopy;
        } catch (Throwable th5) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    @Blind
    public String getServerSessionInfo(String key) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                String property = this.sessionProperties == null ? null : this.sessionProperties.getProperty(key);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return property;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Blind
    private String getUpdatedAutoONSConfig(@Blind String autoONSConfig) {
        String _wfile;
        if (autoONSConfig == null || autoONSConfig.isEmpty()) {
            return autoONSConfig;
        }
        StringBuilder onsConfigBuilder = new StringBuilder(autoONSConfig);
        String _wfile2 = null;
        OpaqueString _wpwd = OpaqueString.NULL;
        if (this.onsWalletFile != null && !"".equals(this.onsWalletFile)) {
            _wfile2 = this.onsWalletFile;
            if (this.onsWalletPassword != null && this.onsWalletPassword != OpaqueString.NULL && this.onsWalletPassword != OpaqueString.EMPTY) {
                _wpwd = this.onsWalletPassword;
            }
        } else if (this.onsProtocol != null && "TCPS".equalsIgnoreCase(this.onsProtocol) && this.walletLocation != null && !"".equals(this.walletLocation)) {
            _wfile2 = this.walletLocation;
            if (this.walletPassword != null && this.walletPassword != OpaqueString.NULL && this.walletPassword != OpaqueString.EMPTY) {
                _wpwd = this.walletPassword;
            }
        }
        if (_wfile2 != null) {
            try {
                _wfile = OracleWalletUtils.sqlNetLocationToURI(_wfile2, getDiagnosable());
            } catch (NetException nexc) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "updateAutoONSConfig", "Failure when processing wallet location/file. ", (String) null, nexc);
                _wfile = null;
            }
            if (_wfile != null) {
                onsConfigBuilder.append(ONS_WALLET_CONFIG).append(_wfile);
                if (_wpwd != null && _wpwd != OpaqueString.NULL) {
                    onsConfigBuilder.append(ONS_WALLET_PASSWD_CONFIG).append(_wpwd.get());
                }
            }
        }
        return onsConfigBuilder.toString();
    }

    public boolean isValidCursorId(int cursorId) {
        return cursorId != 0;
    }

    private void closeResultsets(OracleStatement exempt) throws SQLException {
        ResultSet resultSet;
        ResultSet resultSet2;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            OracleStatement s = this.statements;
            while (s != null) {
                OracleStatement n = s.nextChild;
                if (!s.isClosed() && s != exempt && isValidCursorId(s.getCursorId()) && (resultSet2 = s.getResultSet()) != null && !resultSet2.isClosed()) {
                    resultSet2.close();
                }
                s = n;
            }
            OracleStatement s2 = this.statements;
            while (s2 != null) {
                OracleStatement n2 = s2.next;
                if (!s2.isClosed() && s2 != exempt && isValidCursorId(s2.getCursorId()) && (resultSet = s2.getResultSet()) != null && !resultSet.isClosed()) {
                    resultSet.close();
                }
                s2 = n2;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    static final boolean needToQuoteIdentifier(String identifier) {
        return !nonQuotedIdentifierPattern.matcher(identifier).matches();
    }

    public boolean isLifecycleOpen() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            return this.lifecycle == 1;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    protected void setLifecycle(int lifecycle) {
        this.lifecycle = lifecycle;
        if (lifecycle == 1) {
            OracleDiagnosticsMXBean.addPropertyChangeListener(getDiagnosable());
        } else if (lifecycle != 2) {
            OracleDiagnosticsMXBean.removePropertyChangeListener(getDiagnosable());
            this.connectionDiagnosable.onClose();
        }
    }

    protected int getLifecycle() {
        return this.lifecycle;
    }

    public void clearDrcpTagName() throws SQLException {
        this.drcpTagName = null;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void beginRequest() throws SQLException {
        beginRequest(false, false);
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void beginRequest(boolean implicit, boolean enableAC) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (!this.isInRequest) {
                    this.isInRequest = true;
                    if (this.drcpMultiplexingInRequestAPIs && this.lifecycle == 1 && isDRCPEnabled() && this.drcpState == OracleConnection.DRCPState.DETACHED) {
                        attachServerConnection();
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th4) {
                th = th4;
                throw th4;
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    public void endRequest(boolean implicit) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.isInRequest) {
                    this.isInRequest = false;
                    if (this.lifecycle == 1 && !implicit) {
                        checkAndDrain();
                    }
                    if (!implicit && this.drcpMultiplexingInRequestAPIs && this.lifecycle == 1 && isDRCPEnabled() && this.drcpState != OracleConnection.DRCPState.DETACHED) {
                        detachServerConnection(getDRCPReturnTag());
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th4) {
                th = th4;
                throw th4;
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public final void endRequest() throws SQLException {
        endRequest(false);
    }

    protected final boolean isInRequest() {
        return this.isInRequest;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void sendRequestFlags() throws SQLException {
    }

    String getAuditBanner() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getAuditBanner").fillInStackTrace());
    }

    String getAccessBanner() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getAccessBanner").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void addLargeObject(OracleLargeObject lob) throws SQLException {
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void removeLargeObject(OracleLargeObject lob) throws SQLException {
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void addBfile(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void removeBfile(oracle.jdbc.internal.OracleBfile bfile) throws SQLException {
    }

    void addTemporaryLob(OracleLargeObject lob) {
        this.temporaryLobs.put(lob, DUMMY_VAL);
    }

    public int freeTemporaryBlobsAndClobs() throws SQLException {
        if (this.lifecycle == 8) {
            return 0;
        }
        int numberOfLobsFreed = 0;
        HashMap<OracleLargeObject, String> lobsToFree = this.temporaryLobs;
        this.temporaryLobs = new HashMap<>();
        for (OracleLargeObject freeMe : lobsToFree.keySet()) {
            freeMe.freeLOB();
            numberOfLobsFreed++;
        }
        return numberOfLobsFreed;
    }

    public void removeFromTemporaryLobs(OracleLargeObject lob) {
        this.temporaryLobs.remove(lob);
    }

    public boolean isServerBigSCN() throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setChunkInfo(OracleShardingKey key, OracleShardingKey group, String chunkName) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setChunkInfo").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setShardingKey(OracleShardingKey key, OracleShardingKey group) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setShardingKey(key, group)").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean setShardingKeyIfValid(OracleShardingKey key, OracleShardingKey group, int timeout) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setShardingKeyIfValid(key, group, timeout)").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean setShardingKeyIfValid(OracleShardingKey key, int timeout) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setShardingKeyIfValid(key, timeout)").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void setShardingKey(OracleShardingKey key) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setShardingKey(key)").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public HAManager getHAManager() {
        return this.haManager;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setHAManager(HAManager mngr) throws SQLException {
        if (this.haManager != null && this.haManager != NoSupportHAManager.getInstance()) {
            throw new SQLException("Invalid HAManager in connection");
        }
        this.haManager = mngr;
    }

    protected String getCompatibleString() throws SQLException {
        String result = this.cachedCompatibleString;
        if (result == null) {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                String str = this.cachedCompatibleString;
                result = str;
                if (str == null) {
                    CallableStatement cstmt = prepareCall("begin ? := private_jdbc.get_compatible(); end;");
                    Throwable th2 = null;
                    try {
                        try {
                            cstmt.registerOutParameter(1, 12);
                            cstmt.execute();
                            result = cstmt.getString(1);
                            this.cachedCompatibleString = result;
                            if (cstmt != null) {
                                if (0 != 0) {
                                    try {
                                        cstmt.close();
                                    } catch (Throwable th3) {
                                        th2.addSuppressed(th3);
                                    }
                                } else {
                                    cstmt.close();
                                }
                            }
                        } finally {
                        }
                    } catch (Throwable th4) {
                        if (cstmt != null) {
                            if (th2 != null) {
                                try {
                                    cstmt.close();
                                } catch (Throwable th5) {
                                    th2.addSuppressed(th5);
                                }
                            } else {
                                cstmt.close();
                            }
                        }
                        throw th4;
                    }
                }
            } finally {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th6) {
                            th.addSuppressed(th6);
                        }
                    } else {
                        lock.close();
                    }
                }
            }
        }
        return result;
    }

    boolean isCompatible122OrGreater() throws SQLException {
        return getVersionNumber() >= 12200 && getCompatible() >= 12200;
    }

    protected int getCompatible() throws SQLException {
        return decodeCompatibleDottedString(getCompatibleString());
    }

    protected int decodeCompatibleDottedString(String dottedString) throws SQLException {
        int decodedValue = 0;
        StringTokenizer st = new StringTokenizer(dottedString.trim(), " .", false);
        for (int ct = 0; ct < 4; ct++) {
            int num = 0;
            try {
                if (st.hasMoreTokens()) {
                    String tk = st.nextToken();
                    try {
                        num = Integer.decode(tk).intValue();
                    } catch (NumberFormatException e) {
                    }
                } else {
                    num = 0;
                }
                decodedValue = (decodedValue * 10) + num;
            } catch (NoSuchElementException e2) {
            }
        }
        return decodedValue;
    }

    protected void cleanStatementCache() {
        if (isStatementCacheInitialized()) {
            this.statementCache.clearCursorIds();
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public OracleConnection.DRCPState getDRCPState() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            OracleConnection.DRCPState dRCPState = this.drcpState;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return dRCPState;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isNetworkCompressionEnabled() {
        return false;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public int getOutboundConnectTimeout() {
        return 0;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public NetStat getNetworkStat() {
        return null;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean hasNoOpenHandles() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("hasNoOpenHandles").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public oracle.jdbc.internal.DatabaseSessionState getDatabaseSessionState() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getDatabaseSessionState").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setDatabaseSessionState(oracle.jdbc.internal.DatabaseSessionState obj) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("setDatabaseSessionState").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public boolean isSafelyClosed() throws SQLException {
        return this.safelyClosed;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void flushRemoteDatabaseTTCCookieCache() {
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void setSafelyClosed(boolean closed) throws SQLException {
        this.safelyClosed = closed;
    }

    @Blind(PropertiesBlinder.class)
    private Properties getConnectionPropertiesFromFile(@Blind(PropertiesBlinder.class) Properties info, Hashtable<String, ?> url_properties, String tnsAdmin, boolean tnsAdminFromEnv) throws SQLException {
        boolean fileOverridesConnectIdentifier;
        String userDefinedFile = info.getProperty(oracle.jdbc.OracleConnection.CONNECTION_PROPERTY_CONFIG_FILE);
        if (userDefinedFile == null) {
            userDefinedFile = getSystemPropertyConfigFile(null);
        }
        String connectIdentifier = info.getProperty("database");
        if (connectIdentifier == null) {
            connectIdentifier = info.getProperty("oracle.jdbc.database");
        }
        if (connectIdentifier == null) {
            connectIdentifier = getSystemPropertyDatabase(CONNECTION_PROPERTY_DATABASE_DEFAULT);
        }
        if (connectIdentifier == null) {
            fileOverridesConnectIdentifier = true;
            connectIdentifier = info.getProperty("server");
            if (connectIdentifier == null) {
                connectIdentifier = (String) url_properties.get("database");
            }
        } else {
            fileOverridesConnectIdentifier = false;
        }
        return PropertiesFileUtil.loadPropertiesFromFile(userDefinedFile, tnsAdmin, tnsAdminFromEnv, connectIdentifier, fileOverridesConnectIdentifier);
    }

    boolean checkAndDrain() throws SQLException {
        if (this.inbandNotification && drainOnInbandNotification()) {
            return true;
        }
        if (this.fanEnabled) {
            return getHAManager().checkAndDrain(this);
        }
        return false;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, java.sql.Connection
    public boolean isValid(int timeout) throws SQLException {
        return isValid(this.defaultConnectionValidation, timeout);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean isValid(OracleConnection.ConnectionValidation effort, int timeout) throws SQLException {
        Monitor.CloseableLock lock;
        Monitor.CloseableLock lock2 = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.lifecycle != 1) {
                return false;
            }
            if (timeout < 0) {
                throw ((SQLException) DatabaseError.createSqlException(68, "Timeout is negative " + timeout).fillInStackTrace());
            }
            if (effort == null) {
                throw ((SQLException) DatabaseError.createSqlException(68, "ConnectionValidation effort is null").fillInStackTrace());
            }
            if (lock2 != null) {
                if (0 != 0) {
                    try {
                        lock2.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock2.close();
                }
            }
            boolean isValid = false;
            switch (effort) {
                case NONE:
                    lock = acquireCloseableLock();
                    Throwable th3 = null;
                    try {
                        try {
                            isValid = this.lifecycle == 1;
                            if (lock != null) {
                                if (0 == 0) {
                                    lock.close();
                                    break;
                                } else {
                                    try {
                                        lock.close();
                                        break;
                                    } catch (Throwable th4) {
                                        th3.addSuppressed(th4);
                                        break;
                                    }
                                }
                            }
                        } finally {
                        }
                    } finally {
                    }
                    break;
                case LOCAL:
                    Monitor.CloseableLock lock3 = acquireCloseableLock();
                    Throwable th5 = null;
                    try {
                        isValid = isUsable();
                        if (lock3 != null) {
                            if (0 == 0) {
                                lock3.close();
                                break;
                            } else {
                                try {
                                    lock3.close();
                                    break;
                                } catch (Throwable th6) {
                                    th5.addSuppressed(th6);
                                    break;
                                }
                            }
                        }
                    } catch (Throwable th7) {
                        if (lock3 != null) {
                            if (0 != 0) {
                                try {
                                    lock3.close();
                                } catch (Throwable th8) {
                                    th5.addSuppressed(th8);
                                }
                            } else {
                                lock3.close();
                            }
                        }
                        throw th7;
                    }
                    break;
                case INBAND_DOWN:
                    lock = acquireCloseableLock();
                    Throwable th9 = null;
                    try {
                        try {
                            isValid = this.inbandNotification ? !readInbandDownNotification() : true;
                            if (lock != null) {
                                if (0 == 0) {
                                    lock.close();
                                    break;
                                } else {
                                    try {
                                        lock.close();
                                        break;
                                    } catch (Throwable th10) {
                                        th9.addSuppressed(th10);
                                        break;
                                    }
                                }
                            }
                        } finally {
                        }
                    } finally {
                    }
                    break;
                case SOCKET:
                    Monitor.CloseableLock lock4 = acquireCloseableLock();
                    Throwable th11 = null;
                    try {
                        try {
                            isValid = isValidLight(timeout);
                            if (lock4 != null) {
                                if (0 == 0) {
                                    lock4.close();
                                    break;
                                } else {
                                    try {
                                        lock4.close();
                                        break;
                                    } catch (Throwable th12) {
                                        th11.addSuppressed(th12);
                                        break;
                                    }
                                }
                            }
                        } finally {
                        }
                    } finally {
                        if (lock4 != null) {
                            if (th11 != null) {
                                try {
                                    lock4.close();
                                } catch (Throwable th13) {
                                    th11.addSuppressed(th13);
                                }
                            } else {
                                lock4.close();
                            }
                        }
                    }
                    break;
                case NETWORK:
                    isValid = pingDatabase(timeout) == 0;
                    break;
                case SERVER:
                case COMPLETE:
                    lock = acquireCloseableLock();
                    Throwable th14 = null;
                    try {
                        try {
                            isValid = checkSQLEngineStatus(timeout) == 0;
                            if (lock != null) {
                                if (0 == 0) {
                                    lock.close();
                                    break;
                                } else {
                                    try {
                                        lock.close();
                                        break;
                                    } catch (Throwable th15) {
                                        th14.addSuppressed(th15);
                                        break;
                                    }
                                }
                            }
                        } finally {
                        }
                    } finally {
                        if (lock != null) {
                            if (th14 != null) {
                                try {
                                    lock.close();
                                } catch (Throwable th16) {
                                    th14.addSuppressed(th16);
                                }
                            } else {
                                lock.close();
                            }
                        }
                    }
                    break;
            }
            return isValid;
        } finally {
            if (lock2 != null) {
                if (0 != 0) {
                    try {
                        lock2.close();
                    } catch (Throwable th17) {
                        th.addSuppressed(th17);
                    }
                } else {
                    lock2.close();
                }
            }
        }
    }

    int checkSQLEngineStatus(int timeout) throws SQLException {
        if (checkAndDrain()) {
            return -1;
        }
        return executeDefaultConnectionValidationQuery(timeout);
    }

    private int executeDefaultConnectionValidationQuery(int timeout) throws SQLException {
        assertLockHeldByCurrentThread();
        try {
            Statement stmt = createStatement();
            Throwable th = null;
            try {
                try {
                    stmt.setQueryTimeout(timeout);
                    ((oracle.jdbc.OracleStatement) stmt).defineColumnType(1, 12, 1);
                    stmt.executeQuery(DEFAULT_CONNECTION_VALIDATION_QUERY);
                    if (stmt != null) {
                        if (0 != 0) {
                            try {
                                stmt.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            stmt.close();
                        }
                    }
                    return 0;
                } finally {
                }
            } finally {
            }
        } catch (SQLException e) {
            return -1;
        }
    }

    boolean isValidLight(int timeout) throws SQLException {
        return pingDatabase(timeout) == 0;
    }

    boolean drainOnInbandNotification() throws SQLException {
        return false;
    }

    void closeConnectionSafely() throws SQLException {
        try {
            abort();
            close();
        } catch (SQLRecoverableException exception) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "closeConnectionSafely", null, (String) null, exception);
        }
    }

    static final String getTnsAdminFromEnv() {
        String tnsAdmin = (String) AccessController.doPrivileged(() -> {
            String sysProp = System.getProperty("TNS_ADMIN");
            return sysProp == null ? System.getenv("TNS_ADMIN") : sysProp;
        });
        return tnsAdmin;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getEncryptionProviderName() throws SQLException {
        return null;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getChecksumProviderName() throws SQLException {
        return null;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public String getNetConnectionId() throws SQLException {
        return null;
    }

    private String getNetConnectionIdForLogging() {
        try {
            return getNetConnectionId();
        } catch (SQLException e) {
            return e.getMessage();
        }
    }

    protected String getNetConnectionIdPrefix() throws SQLException {
        if (getNetConnectionId().length() > 24) {
            return getNetConnectionId().substring(0, 8);
        }
        return null;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public Properties getJavaNetProperties() throws SQLException {
        return null;
    }

    boolean readInbandDownNotification() {
        return false;
    }

    private String throughDbCharset(String val) throws SQLException {
        byte[] dbCharSetBytes = this.conversion.getDbCharSetObj().convert(val);
        return this.conversion.getDbCharSetObj().toString(dbCharSetBytes, 0, dbCharSetBytes.length);
    }

    public boolean isSimpleIdentifier(String identifier) throws SQLException {
        if (identifier == null) {
            throw new NullPointerException();
        }
        String ok = throughDbCharset(identifier);
        return IS_SIMPLE_IDENTIFIER.test(ok) || IS_QUOTED_IDENTIFIER.test(ok);
    }

    public String enquoteLiteral(String val) throws SQLException {
        if (val == null) {
            throw new NullPointerException();
        }
        String ok = throughDbCharset(val);
        return "'" + ok.replace("'", "''") + "'";
    }

    public String enquoteIdentifier(String identifier, boolean alwaysQuote) throws SQLException {
        if (identifier == null) {
            throw new NullPointerException();
        }
        String ok = throughDbCharset(identifier);
        if (IS_SIMPLE_IDENTIFIER.test(ok)) {
            return alwaysQuote ? "\"" + ok + "\"" : throughDbCharset(ok.toUpperCase());
        }
        if (IS_QUOTED_IDENTIFIER.test(ok)) {
            return ok;
        }
        if (IS_VALID_IDENTIFIER.test(ok)) {
            return "\"" + ok + "\"";
        }
        throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_INVALID_IDENTIFIER_OR_LITERAL).fillInStackTrace());
    }

    OracleJsonFactory getOracleJsonFactory() {
        if (this.jsonFactory == null) {
            this.jsonFactory = new OracleJsonFactory();
        }
        return this.jsonFactory;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public double getPercentageQueryExecutionOnDirectShard() {
        return 0.0d;
    }

    private void loadOsonConverter() {
        try {
            this.osonConverter = (OsonConverter) this.driverResources.getResource(ResourceType.JSON_PROVIDER);
            trace(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "loadOsonConverter", this.osonConverter.equals(DefaultJsonProvider.NO_OP_OSON_CONVERTER) ? "Json provider not registered or json provider matching with the registered provider name not found in the classpath" : "Json provider: " + this.osonConverter.getClass() + " registered", null, null, new Object[0]);
        } catch (SQLException e) {
            trace(Level.CONFIG, SecurityLabel.UNKNOWN, CLASS_NAME, "loadOsonConverter", "failed loading the provider with error: " + e.getMessage(), null, null, new Object[0]);
        }
    }

    protected OsonConverter getOsonConverter() {
        return this.osonConverter;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void addFeature(OracleConnection.ClientFeature cf) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("addFeature").fillInStackTrace());
    }

    ByteBuffer convertClobDataInNetworkCharSet(oracle.jdbc.internal.OracleClob clob, char[] clobData, int length) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    static boolean isValueBasedLocator(byte[] lobLocator) {
        return lobLocator != null && (lobLocator[4] & 32) == 32;
    }

    static boolean isQuasiLocator(byte[] lobLocator) {
        return lobLocator != null && lobLocator[3] == 4;
    }

    static boolean isReadOnly(byte[] lobLocator) {
        return lobLocator != null && (lobLocator[6] & 1) == 1;
    }

    static boolean isTemporary(byte[] locator) {
        if (locator == null) {
            return false;
        }
        return (locator[7] & 1) > 0 || (locator[4] & 64) > 0 || isValueBasedLocator(locator) || isQuasiLocator(locator);
    }

    static boolean isMemoryLocator(byte[] locator) {
        return (locator == null || (locator[6] & 2) == 0) ? false : true;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public final Executor getAsyncExecutor() {
        return this.asyncExecutor;
    }

    void requireOpenConnection() throws SQLException {
        if (this.lifecycle != 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 8).fillInStackTrace());
        }
    }

    void assertNotNull(byte[] bytes, String caller) throws NullPointerException {
        if (bytes == null) {
            throw new NullPointerException("bytes are null");
        }
    }

    private void updateTraceAttributes() {
        if (this.endToEndAnyChanged) {
            for (int i = 0; i < 4; i++) {
                if (this.endToEndHasChanged[i]) {
                    this.connectionDiagnosable.setTraceAttribute(TRACEKEY_AT_END_TO_END_INDEX[i], this.endToEndValues[i]);
                }
            }
            this.connectionDiagnosable.setTraceAttribute(OracleTraceKey.SEQUENCE_NUMBER, Short.toString(this.endToEndECIDSequenceNumber));
        }
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.connectionDiagnosable;
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void disableLogging() throws SQLException {
        this.connectionDiagnosable.setDebugEnabled(false);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void enableLogging() throws SQLException {
        this.connectionDiagnosable.setDebugEnabled(true);
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void dumpLog() throws SQLException {
        this.connectionDiagnosable.dumpDiagnoseFirstFailure();
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void enableDiagnoseFirstFailureDump(boolean enableDump) throws SQLException {
        this.connectionDiagnosable.enableDiagnoseFirstFailureDump(enableDump);
    }

    protected boolean isTraceEventListenerEnabled() {
        return this.traceEventListener != DefaultTraceEventListenerProvider.NO_OP_TRACE_EVENT_LISTENER;
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public TraceEventListener getTraceEventListener() {
        return this.traceEventListener;
    }

    private void setTraceEventListener(TraceEventListener traceEventListener) {
        if (traceEventListener != null) {
            this.traceEventListener = traceEventListener;
        }
    }

    void trace(TraceEventListener.Sequence sequence, ConnectionTraceContext traceContext) {
        if (!isTraceEventListenerEnabled()) {
            return;
        }
        try {
            this.traceUserContext = this.traceEventListener.roundTrip(sequence, traceContext, this.traceUserContext);
        } finally {
            traceContext.reset();
        }
    }

    protected String getTenantName() {
        return this.tenantName;
    }

    protected String getExecutingRpcFunctionCodeDescription() {
        return this.executingRpcFunction.getDescription();
    }

    protected Boolean isLastRpcCompletedExceptionally() {
        return this.isLastRpcCompletedExceptionally;
    }

    protected void beforeRoundTrip(DatabaseFunction function) {
        this.isLastRpcCompletedExceptionally = false;
        this.executingRpcFunction = function;
        trace(TraceEventListener.Sequence.BEFORE, new ConnectionTraceContext(this, this.executingRpcFunction, this.isLastRpcCompletedExceptionally.booleanValue()));
    }

    protected void afterRoundTrip(Boolean isCompletedExceptionally) {
        this.isLastRpcCompletedExceptionally = isCompletedExceptionally;
        trace(TraceEventListener.Sequence.AFTER, new ConnectionTraceContext(this, this.executingRpcFunction, this.isLastRpcCompletedExceptionally.booleanValue()));
        this.executingRpcFunction = null;
    }

    protected DriverResources getDriverResources() {
        return this.driverResources;
    }

    byte[] GTRIDGenerator() {
        UUID uuid = UUID.randomUUID();
        ByteBuffer buf = ByteBuffer.wrap(new byte[16]);
        buf.putLong(uuid.getMostSignificantBits());
        buf.putLong(uuid.getLeastSignificantBits());
        return buf.array();
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public byte[] startTransaction() throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            byte[] bArrStartTransaction = startTransaction(60);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return bArrStartTransaction;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public byte[] startTransaction(int timeout) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            byte[] gtrid = GTRIDGenerator();
            startTransaction(gtrid, timeout);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return gtrid;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void startTransaction(byte[] GTRID) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                startTransaction(GTRID, 60);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void startTransaction(byte[] GTRID, int timeout) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("startTransaction").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void resumeTransaction(byte[] GTRID) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                resumeTransaction(GTRID, 60);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void resumeTransaction(byte[] GTRID, int timeout) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("resumeTransaction").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void suspendTransactionImmediately() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("suspendTransactionImmediately").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public void suspendTransaction() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("suspendTransaction").fillInStackTrace());
    }

    void postCallSuspend() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("postCallSuspend").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public byte[] getTransactionId() throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("getTransactionId").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void doStartOrResume(byte[] GTRID, int timeout, int operation, int txnMode) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doStartOrResume").fillInStackTrace());
    }

    @Override // oracle.jdbc.internal.OracleConnection
    public void doPreCallSuspend(int txnMode) throws SQLException {
        throw ((SQLException) DatabaseError.createSQLFeatureNotSupportedException("doPreCallSuspend").fillInStackTrace());
    }

    @Override // oracle.jdbc.OracleConnectionWrapper, oracle.jdbc.OracleConnection
    public boolean isXAThroughSessionlessTransactions() {
        return this.XAThroughSessionlessTransactions;
    }

    protected void awaitPipeline() throws SQLException {
    }

    void incrementOpenCursorCount() {
        this.openCursorCount++;
    }

    void decrementOpenCursorCount() {
        this.openCursorCount--;
    }

    int openCursorCount() {
        return this.openCursorCount;
    }
}
