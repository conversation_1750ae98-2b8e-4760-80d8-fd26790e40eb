package oracle.core.lmx;

import java.nio.charset.StandardCharsets;

/* loaded from: ojdbc8.jar:oracle/core/lmx/LmxRepConversion.class */
public class LmxRepConversion {
    public static void printInHex(byte b) {
        System.out.print((char) nibbleToHex((byte) ((b & 240) >> 4)));
        System.out.print((char) nibbleToHex((byte) (b & 15)));
    }

    public static byte nibbleToHex(byte nibble) {
        byte nibble2 = (byte) (nibble & 15);
        return (byte) (nibble2 < 10 ? nibble2 + 48 : (nibble2 - 10) + 65);
    }

    public static byte asciiHexToNibble(byte b) {
        byte value;
        if (b >= 97 && b <= 102) {
            value = (byte) ((b - 97) + 10);
        } else if (b >= 65 && b <= 70) {
            value = (byte) ((b - 65) + 10);
        } else if (b >= 48 && b <= 57) {
            value = (byte) (b - 48);
        } else {
            value = b;
        }
        return value;
    }

    public static void bArray2nibbles(byte[] array, byte[] nibbles) {
        for (int i = 0; i < array.length; i++) {
            nibbles[i * 2] = nibbleToHex((byte) ((array[i] & 240) >> 4));
            nibbles[(i * 2) + 1] = nibbleToHex((byte) (array[i] & 15));
        }
    }

    public static String bArray2String(byte[] array) {
        StringBuffer result = new StringBuffer(array.length * 2);
        for (int i = 0; i < array.length; i++) {
            result.append((char) nibbleToHex((byte) ((array[i] & 240) >> 4)));
            result.append((char) nibbleToHex((byte) (array[i] & 15)));
        }
        return result.toString();
    }

    public static byte[] nibbles2bArray(byte[] nibbles) {
        byte[] array = new byte[nibbles.length / 2];
        for (int i = 0; i < array.length; i++) {
            array[i] = (byte) (asciiHexToNibble(nibbles[i * 2]) << 4);
            int i2 = i;
            array[i2] = (byte) (array[i2] | asciiHexToNibble(nibbles[(i * 2) + 1]));
        }
        return array;
    }

    public static void printInHex(long value) {
        byte[] hexValue = toHex(value);
        System.out.print(new String(hexValue, StandardCharsets.US_ASCII));
    }

    public static void printInHex(int value) {
        byte[] hexValue = toHex(value);
        System.out.print(new String(hexValue, StandardCharsets.US_ASCII));
    }

    public static void printInHex(short value) {
        byte[] hexValue = toHex(value);
        System.out.print(new String(hexValue, StandardCharsets.US_ASCII));
    }

    public static byte[] toHex(long value) {
        byte[] hex = new byte[16];
        for (int i = 16 - 1; i >= 0; i--) {
            hex[i] = nibbleToHex((byte) (value & 15));
            value >>= 4;
        }
        return hex;
    }

    public static byte[] toHex(int value) {
        byte[] hex = new byte[8];
        for (int i = 8 - 1; i >= 0; i--) {
            hex[i] = nibbleToHex((byte) (value & 15));
            value >>= 4;
        }
        return hex;
    }

    public static byte[] toHex(short value) {
        byte[] hex = new byte[4];
        for (int i = 4 - 1; i >= 0; i--) {
            hex[i] = nibbleToHex((byte) (value & 15));
            value = (short) (value >> 4);
        }
        return hex;
    }
}
