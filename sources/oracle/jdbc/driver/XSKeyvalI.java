package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import oracle.jdbc.internal.KeywordValueLong;
import oracle.jdbc.internal.XSKeyval;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/XSKeyvalI.class */
final class XSKeyvalI extends XSKeyval {
    KeywordValueLongI[] kpxskvlvl = null;
    long kpxskvlflg = 0;

    XSKeyvalI() {
    }

    private void setKeyval(KeywordValueLongI[] sendKV) throws SQLException {
        this.kpxskvlvl = sendKV;
    }

    @Override // oracle.jdbc.internal.XSKeyval
    public void setKeyval(KeywordValueLong[] sendKV) throws SQLException {
        InternalFactory.xsSecurityCheck();
        if (sendKV != null) {
            this.kpxskvlvl = (KeywordValueLongI[]) Arrays.copyOf((KeywordValueLongI[]) sendKV, sendKV.length);
        }
    }

    @Override // oracle.jdbc.internal.XSKeyval
    public void setFlag(long flag) throws SQLException {
        InternalFactory.xsSecurityCheck();
        this.kpxskvlflg = flag;
    }

    @Override // oracle.jdbc.internal.XSKeyval
    public KeywordValueLong[] getKeyval() {
        InternalFactory.xsSecurityCheck();
        return this.kpxskvlvl;
    }

    @Override // oracle.jdbc.internal.XSKeyval
    public long getFlag() {
        InternalFactory.xsSecurityCheck();
        return this.kpxskvlflg;
    }

    void marshal(T4CMAREngine mar) throws IOException {
        if (this.kpxskvlvl != null) {
            mar.marshalUB4(this.kpxskvlvl.length);
            for (int i = 0; i < this.kpxskvlvl.length; i++) {
                this.kpxskvlvl[i].marshal(mar);
            }
        } else {
            mar.marshalUB4(0L);
        }
        mar.marshalUB4(this.kpxskvlflg);
    }

    static XSKeyvalI unmarshal(T4CMAREngine mar) throws SQLException, IOException {
        int kpxskvlvlLength = (int) mar.unmarshalUB4();
        if (kpxskvlvlLength > 0) {
            mar.unmarshalUB1();
        }
        KeywordValueLongI[] kpxskvlvl = new KeywordValueLongI[kpxskvlvlLength];
        for (int i = 0; i < kpxskvlvlLength; i++) {
            kpxskvlvl[i] = KeywordValueLongI.unmarshal(mar);
        }
        int kpxskvlflg = (int) mar.unmarshalUB4();
        XSKeyvalI keyval = new XSKeyvalI();
        keyval.setKeyval(kpxskvlvl);
        keyval.setFlag(kpxskvlflg);
        return keyval;
    }
}
