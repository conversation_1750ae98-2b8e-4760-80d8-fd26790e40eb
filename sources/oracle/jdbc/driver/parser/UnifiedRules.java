package oracle.jdbc.driver.parser;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import oracle.jdbc.driver.parser.util.Service;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/UnifiedRules.class */
public class UnifiedRules {
    private static final String fname = "allRules.txt";
    private static final String path = "/oracle/jdbc/driver/parser/";

    private UnifiedRules() {
    }

    public static RuleTuple[] getRules(URL url) throws IOException {
        InputStream is = url.openStream();
        String[] lines = Service.readFile(is).split("\n");
        is.close();
        RuleTuple[] rules = new RuleTuple[lines.length];
        for (int i = 0; i < lines.length; i++) {
            String token = lines[i].trim();
            int colonPos = token.indexOf(": ");
            String head = token.substring(0, colonPos);
            String[] rhs = token.substring(colonPos + ": ".length()).split(" ");
            rules[i] = new RuleTuple(head, rhs);
        }
        return rules;
    }

    public static RuleTuple[] getRules() throws IOException {
        return getSQLRules();
    }

    public static RuleTuple[] getSQLRules() throws IOException {
        URL u = UnifiedRules.class.getResource("/oracle/jdbc/driver/parser/allRules.txt");
        return getRules(u);
    }
}
