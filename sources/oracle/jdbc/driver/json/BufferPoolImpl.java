package oracle.jdbc.driver.json;

import java.lang.ref.WeakReference;
import java.util.concurrent.ConcurrentLinkedQueue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/BufferPoolImpl.class */
public class BufferPoolImpl {
    private volatile WeakReference<ConcurrentLinkedQueue<char[]>> queue;

    public final char[] take() {
        char[] t = getQueue().poll();
        if (t == null) {
            return new char[4096];
        }
        return t;
    }

    private ConcurrentLinkedQueue<char[]> getQueue() {
        ConcurrentLinkedQueue<char[]> d;
        WeakReference<ConcurrentLinkedQueue<char[]>> q = this.queue;
        if (q != null && (d = q.get()) != null) {
            return d;
        }
        ConcurrentLinkedQueue<char[]> d2 = new ConcurrentLinkedQueue<>();
        this.queue = new WeakReference<>(d2);
        return d2;
    }

    public final void recycle(char[] t) {
        getQueue().offer(t);
    }
}
