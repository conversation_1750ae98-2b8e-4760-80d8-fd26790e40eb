package oracle.jdbc.babelfish;

import java.io.File;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import oracle.jdbc.driver.DatabaseError;

/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/TranslationManager.class */
public class TranslationManager {
    private static final ConcurrentHashMap<String, TranslationCache> translationCacheRegistry;
    private static Map<String, String> defaultErrorFile;
    private static final String SEPARATOR = "��";
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !TranslationManager.class.desiredAssertionStatus();
        translationCacheRegistry = new ConcurrentHashMap<>();
        defaultErrorFile = new ConcurrentHashMap();
    }

    public static Translator getTranslator(String connectionURL, String user, String translationProfile, String localErrorTranslationFilePath) throws SQLException {
        if (localErrorTranslationFilePath == null && defaultErrorFile.containsKey(translationProfile)) {
            localErrorTranslationFilePath = defaultErrorFile.get(translationProfile);
        }
        File localErrorTranslationFile = null;
        if (localErrorTranslationFilePath != null) {
            localErrorTranslationFile = new File(localErrorTranslationFilePath);
            if (!localErrorTranslationFile.exists()) {
                throw ((SQLException) DatabaseError.createSqlException(277).fillInStackTrace());
            }
        }
        if (!$assertionsDisabled && (".*��.*".matches(connectionURL) || ".*��.*".matches(user) || ".*��.*".matches(translationProfile))) {
            throw new AssertionError();
        }
        String key = connectionURL + SEPARATOR + user + SEPARATOR + translationProfile;
        TranslationCache trnscache = translationCacheRegistry.get(key);
        if (trnscache == null) {
            trnscache = new TranslationCache(localErrorTranslationFile);
            translationCacheRegistry.putIfAbsent(key, trnscache);
        }
        Translator trnsltr = new Translator(translationProfile, localErrorTranslationFile, trnscache);
        return trnsltr;
    }
}
