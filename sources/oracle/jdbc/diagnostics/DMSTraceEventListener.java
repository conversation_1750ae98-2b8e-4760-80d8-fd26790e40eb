package oracle.jdbc.diagnostics;

import oracle.jdbc.TraceEventListener;
import oracle.jdbc.driver.DMSFactory;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/DMSTraceEventListener.class */
final class DMSTraceEventListener implements TraceEventListener {
    public static final DMSTraceEventListener INSTANCE = new DMSTraceEventListener();

    private DMSTraceEventListener() {
    }

    @Override // oracle.jdbc.TraceEventListener
    public Object roundTrip(TraceEventListener.Sequence seq, TraceEventListener.TraceContext traceContext, Object userContext) {
        if (seq == TraceEventListener.Sequence.BEFORE) {
            DMSTraceId.create(DMSFactory.Context.getECForJDBC().getMap());
        }
        return userContext;
    }
}
