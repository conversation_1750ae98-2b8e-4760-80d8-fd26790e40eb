package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.text.DateFormatSymbols;
import java.util.Calendar;
import java.util.TimeZone;
import oracle.jdbc.OracleResultSetMetaData;
import oracle.jdbc.VectorMetaData;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CDateAccessor.class */
class T4CDateAccessor extends DateAccessor {
    T4CMAREngine mare;
    boolean underlyingLongRaw;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CDateAccessor.class.desiredAssertionStatus();
    }

    T4CDateAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len, form, external_type, isOutBind, false);
        this.underlyingLongRaw = false;
        this.mare = _mare;
    }

    T4CDateAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form, int _definedColumnType, int _definedColumnSize, T4CMAREngine _mare) throws SQLException {
        super(stmt, max_len == -1 ? _definedColumnSize : max_len, nullable, flags, precision, scale, contflag, total_elems, form);
        this.underlyingLongRaw = false;
        this.mare = _mare;
        if (stmt != null && stmt.implicitDefineForLobPrefetchDone) {
            this.definedColumnType = 0;
            this.definedColumnSize = 0;
        } else {
            this.definedColumnType = _definedColumnType;
            this.definedColumnSize = _definedColumnSize;
        }
        if (max_len == -1) {
            this.underlyingLongRaw = true;
        }
    }

    public T4CMAREngine getMAREngine() {
        return this.mare;
    }

    public void unmarshalColumnMetadata() throws SQLException, IOException {
        if (this.statement.statementType != 2 && !this.statement.sqlKind.isPlsqlOrCall() && this.securityAttribute == OracleResultSetMetaData.SecurityAttribute.ENABLED) {
            setRowMetadata(this.lastRowProcessed, (byte) this.mare.unmarshalUB1());
        }
    }

    public void processIndicator(int size) throws SQLException, IOException {
        if ((this.internalType == 1 && (this.describeType == 112 || this.describeType == 127)) || ((this.internalType == 23 && this.describeType == 113) || (this.internalType == 112 && this.describeType == 127))) {
            this.mare.unmarshalSB2();
            this.mare.unmarshalUB2();
        } else {
            if (this.statement.connection.versionNumber < 9200) {
                this.mare.unmarshalSB2();
                if (!this.statement.sqlKind.isPlsqlOrCall()) {
                    this.mare.unmarshalSB2();
                    return;
                }
                return;
            }
            if (this.statement.sqlKind.isPlsqlOrCall() || this.isDMLReturnedParam) {
                this.mare.processIndicator(size <= 0, size);
            }
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getPreviousRowProcessed() {
        if (this.previousRowProcessed == -1) {
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        }
        return this.previousRowProcessed;
    }

    @Override // oracle.jdbc.driver.Accessor
    void reinitForResultSetCache(ByteArray dba, OracleStatement stmt) throws SQLException {
        this.rowData = dba;
        this.mare = ((T4CConnection) stmt.connection).mare;
        this.rowNull = null;
        setCapacity(stmt.getFetchSize());
    }

    @Override // oracle.jdbc.driver.Accessor
    boolean unmarshalOneRow() throws SQLException, IOException {
        boolean isStream = false;
        if (!isUseless()) {
            if (isUnexpected()) {
                long pos = this.rowData.getPosition();
                unmarshalColumnMetadata();
                unmarshalBytes();
                this.rowData.setPosition(pos);
                setNull(this.lastRowProcessed, true);
            } else if (isNullByDescribe()) {
                setNull(this.lastRowProcessed, true);
                unmarshalColumnMetadata();
                if (this.statement.connection.versionNumber < 9200) {
                    processIndicator(0);
                }
            } else {
                unmarshalColumnMetadata();
                isStream = unmarshalBytes();
            }
        }
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
        return isStream;
    }

    boolean unmarshalBytes() throws SQLException, IOException {
        int len;
        setOffset(this.lastRowProcessed);
        if (this.statement.maxFieldSize > 0) {
            len = ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare, this.statement.maxFieldSize);
        } else {
            len = ((DynamicByteArray) this.rowData).unmarshalCLR(this.mare);
        }
        processIndicator(len);
        setLength(this.lastRowProcessed, len);
        setNull(this.lastRowProcessed, len == 0);
        return false;
    }

    @Override // oracle.jdbc.driver.Accessor
    void copyRow() throws SQLException, IOException {
        if (this.isNullByDescribe || this.previousRowProcessed == -1) {
            setNull(this.lastRowProcessed, true);
            this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
        } else if (this.lastRowProcessed == 0) {
            if (this.previousRowProcessed == -1) {
                this.previousRowProcessed = this.statement.rowPrefetchInLastFetch - 1;
            }
            if (this.lastCopyRow == this.previousRowProcessed) {
                setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
                this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
                if (!this.lastCopyRowIsNull) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(this.lastCopyRowOffset, this.lastCopyRowLength);
                    setLength(this.lastRowProcessed, this.lastCopyRowLength);
                }
            } else {
                long previousOffset = getOffset(this.previousRowProcessed);
                setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
                this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
                if (!isNull(this.previousRowProcessed)) {
                    setOffset(this.lastRowProcessed);
                    this.rowData.copyLeft(previousOffset, getLength(this.previousRowProcessed));
                    setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
                }
            }
        } else if (this.lastCopyRow == this.previousRowProcessed) {
            setNull(this.lastRowProcessed, this.lastCopyRowIsNull);
            this.rowMetadata[this.lastRowProcessed] = this.lastCopyRowMetaData;
            setOffset(this.lastRowProcessed, this.lastCopyRowOffset);
            setLength(this.lastRowProcessed, this.lastCopyRowLength);
        } else {
            setNull(this.lastRowProcessed, isNull(this.previousRowProcessed));
            this.rowMetadata[this.lastRowProcessed] = this.rowMetadata[this.previousRowProcessed];
            setOffset(this.lastRowProcessed, getOffset(this.previousRowProcessed));
            setLength(this.lastRowProcessed, getLength(this.previousRowProcessed));
        }
        this.lastCopyRow = -1;
        this.previousRowProcessed = this.lastRowProcessed;
        this.lastRowProcessed++;
    }

    @Override // oracle.jdbc.driver.DateTimeCommonAccessor
    String toText(int year, int month, int day, int hour, int min, int sec, int nanos, boolean am, String zone) throws SQLException {
        if (this.definedColumnType == 0 || this.definedColumnType == 91) {
            return super.toText(year, month, day, hour, min, sec, nanos, am, zone);
        }
        String format = (String) this.statement.connection.sessionProperties.get("AUTH_NLS_LXCDATEFM");
        return nlsFormatToText(year, month, day, hour, min, sec, nanos, am, zone, format);
    }

    private static final String nlsFormatToText(int year, int month, int day, int hour, int min, int sec, int nanos, boolean am, String zone, String format) throws SQLException {
        char[] fChars = (format + "      ").toCharArray();
        int fLength = format.length();
        StringBuffer sb = new StringBuffer(fLength + 25);
        String[] sMonths = null;
        String[] lMonths = null;
        TimeZone tz = null;
        int normYear = year < 0 ? Math.abs(year) + 1 : year;
        boolean bJulianBC = false;
        int i = 0;
        while (i < fLength) {
            switch (fChars[i]) {
                case DatabaseError.EOJ_CONV_WAS_NULL /* 65 */:
                case 'a':
                    if (fChars[i + 1] == 'M' || fChars[i + 1] == 'm') {
                        sb.append(am ? "AM" : "PM");
                        i++;
                        break;
                    } else if (fChars[i + 1] != 'D' && fChars[i + 1] != 'd') {
                        break;
                    } else {
                        sb.append(year < 0 ? "BC" : "AD");
                        i++;
                        break;
                    }
                case 'B':
                case 'b':
                    if (fChars[i + 1] != 'C' && fChars[i + 1] != 'c') {
                        break;
                    } else {
                        sb.append(year < 0 ? "BC" : "AD");
                        i++;
                        break;
                    }
                case 'C':
                case DatabaseError.EOJ_USE_XA_EXPLICIT /* 69 */:
                case 'G':
                case ShardingKeyInfo.GWS_KEY_PUSH_BIND_INDEX_20_1 /* 73 */:
                case 'J':
                case DatabaseError.EOJ_INVALID_FORWARD_RSET_OP /* 75 */:
                case 'L':
                case 'N':
                case DatabaseError.EOJ_USER_CREDENTIALS_FAIL /* 79 */:
                case 'Q':
                case DatabaseError.EOJ_UPDATE_CONFLICTS /* 85 */:
                case 'V':
                case DatabaseError.WARN_IGNORE_FETCH_DIRECTION /* 87 */:
                case DatabaseError.EOJ_UNSUPPORTED_SYNTAX /* 88 */:
                case 'Z':
                case '[':
                case '\\':
                case ']':
                case '^':
                case '_':
                case '`':
                case 'c':
                case 'e':
                case 'g':
                case 'i':
                case 'j':
                case 'k':
                case 'l':
                case 'n':
                case DatabaseError.EOJ_WARN_CONN_CACHE_TIMEOUT /* 111 */:
                case 'q':
                case DatabaseError.EOJ_SETSVPT_IN_GLOBAL_TXN /* 117 */:
                case 'v':
                case 'w':
                case 'x':
                default:
                    sb.append(fChars[i]);
                    break;
                case DatabaseError.EOJ_INVALID_ARGUMENTS /* 68 */:
                case 'd':
                    if (fChars[i + 1] != 'D' && fChars[i + 1] != 'd') {
                        break;
                    } else {
                        sb.append((day < 10 ? "0" : "") + day);
                        i++;
                        break;
                    }
                    break;
                case 'F':
                case 'f':
                    if (fChars[i + 1] != 'F' && fChars[i + 1] != 'f') {
                        break;
                    } else {
                        if (nanos >= 0) {
                            sb.append(nanos);
                        } else {
                            sb.append(0);
                        }
                        i++;
                        break;
                    }
                    break;
                case 'H':
                case 'h':
                    if (fChars[i + 1] != 'H' && fChars[i + 1] != 'h') {
                        break;
                    } else if (fChars[i + 2] == '2' || fChars[i + 3] == '4') {
                        sb.append((hour < 10 ? "0" : "") + hour);
                        i += 3;
                        break;
                    } else {
                        if (hour > 12) {
                            hour -= 12;
                        }
                        sb.append((hour < 10 ? "0" : "") + hour);
                        i++;
                        break;
                    }
                case DatabaseError.EOJ_FAIL_REF_SETVALUE /* 77 */:
                case 'm':
                    if (fChars[i + 1] == 'M' || fChars[i + 1] == 'm') {
                        sb.append((month < 10 ? "0" : "") + month);
                        i++;
                        break;
                    } else if (fChars[i + 1] == 'I' || fChars[i + 1] == 'i') {
                        sb.append((min < 10 ? "0" : "") + min);
                        i++;
                        break;
                    } else if ((fChars[i + 1] != 'O' && fChars[i + 1] != 'o') || (fChars[i + 2] != 'N' && fChars[i + 2] != 'n')) {
                        break;
                    } else if ((fChars[i + 3] == 'T' || fChars[i + 3] == 't') && (fChars[i + 4] == 'H' || fChars[i + 4] == 'h')) {
                        if (lMonths == null) {
                            lMonths = new DateFormatSymbols().getMonths();
                        }
                        if (fChars[i] == 'm') {
                            sb.append(lMonths[month - 1].toLowerCase());
                        } else if (fChars[i + 1] == 'O') {
                            sb.append(lMonths[month - 1].toUpperCase());
                        } else {
                            sb.append(lMonths[month - 1]);
                        }
                        i += 4;
                        break;
                    } else {
                        if (sMonths == null) {
                            sMonths = new DateFormatSymbols().getShortMonths();
                        }
                        if (fChars[i] == 'm') {
                            sb.append(sMonths[month - 1].toLowerCase());
                        } else if (fChars[i + 1] == 'O') {
                            sb.append(sMonths[month - 1].toUpperCase());
                        } else {
                            sb.append(sMonths[month - 1]);
                        }
                        i += 2;
                        break;
                    }
                case 'P':
                case 'p':
                    if (fChars[i + 1] != 'M' && fChars[i + 1] != 'm') {
                        break;
                    } else {
                        sb.append(am ? "AM" : "PM");
                        i++;
                        break;
                    }
                    break;
                case 'R':
                case 'r':
                    if (fChars[i + 1] != 'R' && fChars[i + 1] != 'r') {
                        break;
                    } else if ((fChars[i + 2] == 'R' || fChars[i + 2] == 'r') && (fChars[i + 3] == 'R' || fChars[i + 3] == 'r')) {
                        if (normYear < 1000) {
                            sb.append("0" + normYear);
                        } else if (normYear < 100) {
                            sb.append("00" + normYear);
                        } else if (normYear < 10) {
                            sb.append("000" + normYear);
                        } else {
                            sb.append(normYear);
                        }
                        i += 3;
                        break;
                    } else {
                        if (normYear >= 100) {
                            normYear %= 100;
                        }
                        if (normYear < 10) {
                            sb.append("0" + normYear);
                        } else {
                            sb.append(normYear);
                        }
                        i++;
                        break;
                    }
                    break;
                case 'S':
                case 's':
                    if (fChars[i + 1] == 'S' || fChars[i + 1] == 's') {
                        sb.append((sec < 10 ? "0" : "") + sec);
                        i++;
                        if ((fChars[i + 1] != 'X' && fChars[i + 1] != 'x') || ((fChars[i + 2] != 'F' && fChars[i + 2] != 'f') || (fChars[i + 3] != 'F' && fChars[i + 3] != 'f'))) {
                            break;
                        } else {
                            sb.append(oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR);
                            i++;
                            break;
                        }
                    } else if ((fChars[i + 1] != 'Y' && fChars[i + 1] != 'y') || ((fChars[i + 2] != 'Y' && fChars[i + 2] != 'y') || ((fChars[i + 3] != 'Y' && fChars[i + 3] != 'y') || year >= 0))) {
                        break;
                    } else {
                        sb.append("-");
                        bJulianBC = true;
                        break;
                    }
                    break;
                case 'T':
                case DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN /* 116 */:
                    if (fChars[i + 1] != 'Z' && fChars[i + 1] != 'z') {
                        break;
                    } else if (fChars[i + 2] == 'R' || fChars[i + 2] == 'r') {
                        if (zone.length() > 3 && zone.startsWith("GMT")) {
                            sb.append(zone.substring(3));
                        } else {
                            sb.append(zone.toUpperCase());
                        }
                        i += 2;
                        break;
                    } else if (fChars[i + 2] == 'H' || fChars[i + 2] == 'h') {
                        if (tz == null) {
                            tz = TimeZone.getTimeZone(zone);
                        }
                        long offsetHr = tz.getRawOffset() / 3600000;
                        sb.append(offsetHr);
                        i += 2;
                        break;
                    } else if (fChars[i + 2] != 'M' && fChars[i + 2] != 'm') {
                        break;
                    } else {
                        if (tz == null) {
                            tz = TimeZone.getTimeZone(zone);
                        }
                        long offsetMin = (Math.abs(tz.getRawOffset()) % 3600000) / 60000;
                        sb.append((offsetMin < 10 ? "0" : "") + offsetMin);
                        i += 2;
                        break;
                    }
                    break;
                case DatabaseError.EOJ_INTERNAL_ERROR /* 89 */:
                case 'y':
                    if (fChars[i + 1] != 'Y' && fChars[i + 1] != 'y') {
                        break;
                    } else if ((fChars[i + 2] == 'Y' || fChars[i + 2] == 'y') && (fChars[i + 3] == 'Y' || fChars[i + 3] == 'y')) {
                        int localYear = (year >= 0 || !bJulianBC) ? normYear : Math.abs(year);
                        if (localYear < 1000) {
                            sb.append("0" + localYear);
                        } else if (localYear < 100) {
                            sb.append("00" + localYear);
                        } else if (localYear < 10) {
                            sb.append("000" + localYear);
                        } else {
                            sb.append(localYear);
                        }
                        i += 3;
                        break;
                    } else {
                        if (normYear >= 100) {
                            normYear %= 100;
                        }
                        if (normYear < 10) {
                            sb.append("0" + normYear);
                        } else {
                            sb.append(normYear);
                        }
                        i++;
                        break;
                    }
            }
            i++;
        }
        return sb.substring(0, sb.length());
    }

    @Override // oracle.jdbc.driver.DateAccessor, oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException {
        String ret;
        if (this.definedColumnType == 0) {
            ret = super.getString(currentRow);
        } else {
            if (isNull(currentRow)) {
                return null;
            }
            getBytesInternal(currentRow, this.tmpBytes);
            int year = oracleYear(this.tmpBytes);
            int iOracleMonth = oracleMonth(this.tmpBytes) + 1;
            int iOracleDay = oracleDay(this.tmpBytes);
            int hour = oracleHour(this.tmpBytes);
            ret = toText(year, iOracleMonth, iOracleDay, hour, oracleMin(this.tmpBytes), oracleSec(this.tmpBytes), -1, hour < 12, null);
        }
        if (ret != null && this.definedColumnSize > 0 && ret.length() > this.definedColumnSize) {
            ret = ret.substring(0, this.definedColumnSize);
        }
        return ret;
    }

    @Override // oracle.jdbc.driver.DateAccessor, oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        if (this.definedColumnType == 0) {
            return super.getObject(currentRow);
        }
        if (isUnexpected()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 21).fillInStackTrace());
        }
        if (isNull(currentRow)) {
            return null;
        }
        switch (this.definedColumnType) {
            case oracle.jdbc.OracleTypes.NCHAR /* -15 */:
            case oracle.jdbc.OracleTypes.NVARCHAR /* -9 */:
            case -1:
            case 1:
            case 12:
                return getString(currentRow);
            case oracle.jdbc.OracleTypes.LONGVARBINARY /* -4 */:
            case -3:
            case -2:
                return getBytes(currentRow);
            case 91:
                return getDate(currentRow);
            case 92:
                return getTime(currentRow);
            case 93:
                return getTimestamp(currentRow);
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.Accessor
    int getBytes(int currentRow, byte[] buffer, int offset) throws SQLException {
        if (isNull(currentRow)) {
            return -1;
        }
        Calendar cal2 = this.statement.getDefaultCalendar();
        getBytesInternal(currentRow, this.tmpBytes);
        int year = oracleYear(this.tmpBytes);
        cal2.clear();
        cal2.set(1, year);
        cal2.set(2, oracleMonth(this.tmpBytes));
        cal2.set(5, oracleDay(this.tmpBytes));
        cal2.set(11, oracleHour(this.tmpBytes));
        cal2.set(12, oracleMin(this.tmpBytes));
        cal2.set(13, oracleSec(this.tmpBytes));
        cal2.set(14, 0);
        if (year > 0 && cal2.isSet(0)) {
            cal2.set(0, 1);
        }
        int sec = (int) (cal2.getTimeInMillis() / 1000);
        buffer[offset + 3] = (byte) ((sec >> 24) & 255);
        buffer[offset + 2] = (byte) ((sec >> 16) & 255);
        buffer[offset + 1] = (byte) ((sec >> 8) & 255);
        buffer[offset] = (byte) (sec & 255);
        return 4;
    }

    @Override // oracle.jdbc.driver.Accessor
    AccessorPrototype newPrototype(int numRows) {
        if (!$assertionsDisabled && numRows < 0) {
            throw new AssertionError("numRows: " + numRows);
        }
        final VectorMetaData vectorMetaData = getVectorMetaData();
        AccessorPrototype p = new AccessorPrototype(numRows, this, this.statement.rowData) { // from class: oracle.jdbc.driver.T4CDateAccessor.1
            @Override // oracle.jdbc.driver.AccessorPrototype
            Accessor newAccessor(OracleStatement stmt) throws SQLException {
                Accessor acc = new T4CDateAccessor(stmt, T4CDateAccessor.this.describeMaxLength, T4CDateAccessor.this.nullable, -1, T4CDateAccessor.this.precision, T4CDateAccessor.this.scale, T4CDateAccessor.this.contflag, -1, T4CDateAccessor.this.formOfUse, T4CDateAccessor.this.definedColumnType, T4CDateAccessor.this.definedColumnSize, null);
                acc.setVectorMetaData(vectorMetaData);
                initializeRowData(acc);
                return acc;
            }
        };
        return p;
    }
}
