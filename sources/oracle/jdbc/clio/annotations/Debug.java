package oracle.jdbc.clio.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import oracle.jdbc.diagnostics.SecurityLabel;

@Target({ElementType.METHOD, ElementType.CONSTRUCTOR})
@Retention(RetentionPolicy.CLASS)
/* loaded from: ojdbc8.jar:oracle/jdbc/clio/annotations/Debug.class */
public @interface Debug {
    Level level();

    SecurityLabel securityLabel() default SecurityLabel.INTERNAL;

    /* loaded from: ojdbc8.jar:oracle/jdbc/clio/annotations/Debug$Level.class */
    public enum Level {
        SEVERE(java.util.logging.Level.SEVERE),
        WARNING(java.util.logging.Level.WARNING),
        INFO(java.util.logging.Level.INFO),
        CONFIG(java.util.logging.Level.CONFIG),
        FINE(java.util.logging.Level.FINE),
        FINER(java.util.logging.Level.FINER),
        FINEST(java.util.logging.Level.FINEST);

        final java.util.logging.Level level;

        Level(java.util.logging.Level l) {
            this.level = l;
        }

        java.util.logging.Level getLevel() {
            return this.level;
        }
    }
}
