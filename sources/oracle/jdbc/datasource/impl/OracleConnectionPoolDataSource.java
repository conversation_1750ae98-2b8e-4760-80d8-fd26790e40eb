package oracle.jdbc.datasource.impl;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import javax.sql.PooledConnection;
import oracle.jdbc.OraclePooledConnectionBuilder;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.pool.OraclePooledConnection;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/impl/OracleConnectionPoolDataSource.class */
public class OracleConnectionPoolDataSource extends OracleDataSource implements oracle.jdbc.datasource.OracleConnectionPoolDataSource, oracle.jdbc.replay.internal.OracleConnectionPoolDataSource {
    public OracleConnectionPoolDataSource() throws SQLException {
        this.dataSourceName = "OracleConnectionPoolDataSource";
        this.isOracleDataSource = false;
    }

    @Override // javax.sql.ConnectionPoolDataSource, oracle.jdbc.replay.OracleConnectionPoolDataSource
    public PooledConnection getPooledConnection() throws SQLException {
        OpaqueString opaqueString = OpaqueString.NULL;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            String localUser = this.user;
            OpaqueString localPassword = this.password != null ? this.password : OpaqueString.NULL;
            return getPooledConnection(localUser, localPassword);
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // javax.sql.ConnectionPoolDataSource, oracle.jdbc.replay.OracleConnectionPoolDataSource
    public PooledConnection getPooledConnection(String _user, @Blind String _password) throws SQLException {
        return getPooledConnection(_user, OpaqueString.newOpaqueString(_password));
    }

    private PooledConnection getPooledConnection(String _user, OpaqueString _password) throws SQLException {
        Connection conn = getPhysicalConnection(_user, _password);
        OraclePooledConnection opc = new OraclePooledConnection(conn);
        return opc;
    }

    PooledConnection getPooledConnection(@Blind(PropertiesBlinder.class) Properties prop) throws SQLException {
        Connection conn = getPhysicalConnection(prop, (AbstractConnectionBuilder) null);
        OraclePooledConnection opc = new OraclePooledConnection(conn);
        return opc;
    }

    protected Connection getPhysicalConnection() throws SQLException {
        return super.getConnection(this.user, this.password);
    }

    protected Connection getPhysicalConnection(String _url, String _user, @Blind String _passwd) throws SQLException {
        this.url = _url;
        return super.getConnection(_user, OpaqueString.newOpaqueString(_passwd));
    }

    protected Connection getPhysicalConnection(String _user, @Blind String _passwd) throws SQLException {
        return super.getConnection(_user, OpaqueString.newOpaqueString(_passwd));
    }

    protected Connection getPhysicalConnection(String _url, String _user, OpaqueString _passwd) throws SQLException {
        this.url = _url;
        return super.getConnection(_user, _passwd);
    }

    protected Connection getPhysicalConnection(String _user, OpaqueString _passwd) throws SQLException {
        return super.getConnection(_user, _passwd);
    }

    protected OraclePooledConnection getPooledConnection(OracleConnectionBuilderImpl connBuilder) throws SQLException {
        Connection conn = super.getConnection(connBuilder);
        OraclePooledConnection opc = new OraclePooledConnection(conn);
        return opc;
    }

    @Override // oracle.jdbc.datasource.OracleConnectionPoolDataSource
    public OraclePooledConnectionBuilder createPooledConnectionBuilder() throws SQLException {
        return new OraclePooledConnectionBuilderImpl() { // from class: oracle.jdbc.datasource.impl.OracleConnectionPoolDataSource.1
            @Override // oracle.jdbc.internal.AbstractConnectionBuilder, oracle.jdbc.OraclePooledConnectionBuilder
            public oracle.jdbc.datasource.OraclePooledConnection build() throws SQLException {
                OracleConnectionBuilderImpl connBldr = OracleConnectionPoolDataSource.this.createConnectionBuilder().copy(this);
                connBldr.verifyBuildConfiguration();
                return OracleConnectionPoolDataSource.this.getPooledConnection(connBldr);
            }
        };
    }

    @Override // oracle.jdbc.datasource.impl.OracleDataSource, javax.sql.DataSource
    public Connection getConnection() throws SQLException {
        throw DatabaseError.createSqlException(23);
    }

    @Override // oracle.jdbc.datasource.impl.OracleDataSource, javax.sql.DataSource
    public Connection getConnection(String usr, String pwd) throws SQLException {
        throw DatabaseError.createSqlException(23);
    }

    public Connection getConnection(@Blind(PropertiesBlinder.class) Properties props) throws SQLException {
        throw DatabaseError.createSqlException(23);
    }
}
