package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import javax.transaction.xa.XAException;
import javax.transaction.xa.Xid;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.proxy._Proxy_;
import oracle.jdbc.replay.ReplayableConnection;
import oracle.jdbc.xa.OracleXAConnection;
import oracle.jdbc.xa.OracleXAException;
import oracle.jdbc.xa.OracleXid;
import oracle.jdbc.xa.client.OracleXAResource;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CXAResource.class */
class T4CXAResource extends OracleXAResource {
    private static final String CLASS_NAME = T4CXAResource.class.getName();
    int[] applicationValueArr;
    boolean isTransLoose;
    byte[] context;
    int errorNumber;
    private OpaqueString password;

    T4CXAResource(oracle.jdbc.internal.OracleConnection _physicalConn, OracleXAConnection xaconn, boolean _isTransLoose) throws XAException {
        super(_physicalConn, xaconn);
        this.applicationValueArr = new int[1];
        this.isTransLoose = false;
        this.password = null;
        this.isTransLoose = _isTransLoose;
    }

    @Override // oracle.jdbc.xa.client.OracleXAResource
    protected int doStart(Xid xid, int flag) throws SQLException, XAException {
        int t4cflag;
        T4CConnection physicalConn = getT4CConnection();
        Monitor.CloseableLock lock = physicalConn.acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.isTransLoose) {
                flag |= 65536;
            }
            int swtch = flag & 136314880;
            if (swtch == 134217728 && OracleXid.isLocalTransaction(xid)) {
                return 0;
            }
            this.applicationValueArr[0] = 0;
            try {
                try {
                    T4CTTIOtxse otxse = physicalConn.otxse;
                    byte[] xidxid = null;
                    byte[] gtrid = xid.getGlobalTransactionId();
                    byte[] bqual = xid.getBranchQualifier();
                    int gtrid_l = 0;
                    int bqual_l = 0;
                    if (gtrid != null && bqual != null) {
                        gtrid_l = Math.min(gtrid.length, 64);
                        bqual_l = Math.min(bqual.length, 64);
                        xidxid = new byte[128];
                        System.arraycopy(gtrid, 0, xidxid, 0, gtrid_l);
                        System.arraycopy(bqual, 0, xidxid, gtrid_l, bqual_l);
                    }
                    if ((flag & 8) != 0) {
                        t4cflag = 0 | 8;
                    } else if ((flag & 2097152) != 0 || (flag & 134217728) != 0) {
                        t4cflag = 0 | 4;
                    } else {
                        t4cflag = 0 | 1;
                    }
                    if ((flag & 256) != 0) {
                        t4cflag |= 256;
                    }
                    if ((flag & 512) != 0) {
                        t4cflag |= 512;
                    }
                    if ((flag & 1024) != 0) {
                        t4cflag |= 1024;
                    }
                    if ((flag & 65536) != 0) {
                        t4cflag |= 65536;
                    }
                    physicalConn.updateSystemContext();
                    physicalConn.needLine();
                    otxse.doOTXSE(1, null, xidxid, xid.getFormatId(), gtrid_l, bqual_l, this.timeout, t4cflag, this.applicationValueArr);
                    this.applicationValueArr[0] = otxse.getApplicationValue();
                    byte[] ctx = otxse.getContext();
                    if (ctx != null) {
                        this.context = ctx;
                    }
                    physicalConn.currentlyInTransaction = true;
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return 0;
                } catch (IOException ioe) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doStart", (String) null, (String) null, ioe);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioe).fillInStackTrace());
                }
            } catch (SQLException s) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doStart", (String) null, (String) null, s);
                int returnVal = s.getErrorCode();
                if (returnVal == 0) {
                    throw new XAException(-6);
                }
                throw s;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.xa.client.OracleXAResource
    protected int doEnd(Xid xid, int flag, boolean isLocallySuspended) throws SQLException, XAException {
        int returnVal;
        T4CConnection physicalConn = getT4CConnection();
        Monitor.CloseableLock lock = physicalConn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                try {
                    T4CTTIOtxse otxse = physicalConn.otxse;
                    byte[] xidxid = null;
                    byte[] gtrid = xid.getGlobalTransactionId();
                    byte[] bqual = xid.getBranchQualifier();
                    int gtrid_l = 0;
                    int bqual_l = 0;
                    if (gtrid != null && bqual != null) {
                        gtrid_l = Math.min(gtrid.length, 64);
                        bqual_l = Math.min(bqual.length, 64);
                        xidxid = new byte[128];
                        System.arraycopy(gtrid, 0, xidxid, 0, gtrid_l);
                        System.arraycopy(bqual, 0, xidxid, gtrid_l, bqual_l);
                    }
                    if (this.context == null && (returnVal = doStart(xid, 134217728)) != 0) {
                        return returnVal;
                    }
                    byte[] txctx = this.context;
                    int t4cflag = 0;
                    if ((flag & 2) == 2) {
                        t4cflag = 1048576;
                    } else if ((flag & oracle.jdbc.xa.OracleXAResource.TMSUSPEND) == 33554432 && (flag & oracle.jdbc.xa.OracleXAResource.TMMIGRATE) != 1048576) {
                        t4cflag = 1048576;
                    }
                    int[] iArr = this.applicationValueArr;
                    iArr[0] = iArr[0] >> 16;
                    physicalConn.updateSystemContext();
                    physicalConn.needLine();
                    otxse.doOTXSE(2, txctx, xidxid, xid.getFormatId(), gtrid_l, bqual_l, this.timeout, t4cflag, this.applicationValueArr);
                    this.applicationValueArr[0] = otxse.getApplicationValue();
                    byte[] ctx = otxse.getContext();
                    if (ctx != null) {
                        this.context = ctx;
                    }
                    physicalConn.currentlyInTransaction = false;
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return 0;
                } catch (IOException ioe) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doEnd", (String) null, (String) null, ioe);
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioe).fillInStackTrace());
                }
            } catch (SQLException s) {
                debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doEnd", (String) null, (String) null, s);
                if (s.getErrorCode() == 0) {
                    throw new XAException(-6);
                }
                throw s;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:57:0x0185  */
    /* JADX WARN: Removed duplicated region for block: B:77:0x01d7 A[ORIG_RETURN, RETURN] */
    @Override // oracle.jdbc.xa.client.OracleXAResource
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected void doCommit(javax.transaction.xa.Xid r11, boolean r12) throws java.sql.SQLException, javax.transaction.xa.XAException {
        /*
            Method dump skipped, instructions count: 472
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CXAResource.doCommit(javax.transaction.xa.Xid, boolean):void");
    }

    @Override // oracle.jdbc.xa.client.OracleXAResource
    protected int doPrepare(Xid xid) throws SQLException, XAException {
        int returnVal;
        T4CConnection physicalConn = this.connection instanceof ReplayableConnection ? (T4CConnection) ((_Proxy_) this.connection)._getDelegate_() : (T4CConnection) this.connection;
        Monitor.CloseableLock lock = physicalConn.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                int outState = doTransaction(xid, 3, 0);
                if (outState == 8) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doPrepare", "server returns K2CMDtimeout", null, null);
                    throw new XAException(106);
                }
                if (outState == 4) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doPrepare", "server returns K2CMDrdonly", null, null);
                    returnVal = 3;
                } else if (outState == 1) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doPrepare", "server returns K2CMDrqcommit", null, null);
                    returnVal = 0;
                } else {
                    if (outState == 3) {
                        debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doPrepare", "server returns K2CMDabort", null, null);
                        throw new XAException(100);
                    }
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doPrepare", "server returns {0}", (String) null, (String) null, Integer.valueOf(outState));
                    throw new XAException(-6);
                }
                return returnVal;
            } catch (SQLException sqlex) {
                int errorCode = sqlex.getErrorCode();
                if (errorCode == 25351) {
                    XAException xae = new XAException(-6);
                    xae.initCause(sqlex);
                    throw xae;
                }
                throw sqlex;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.xa.client.OracleXAResource
    protected int doForget(Xid xid) throws SQLException, XAException {
        int resumeReturn;
        T4CConnection physicalConn = this.connection instanceof ReplayableConnection ? (T4CConnection) ((_Proxy_) this.connection)._getDelegate_() : (T4CConnection) this.connection;
        Monitor.CloseableLock lock = physicalConn.acquireCloseableLock();
        Throwable th = null;
        try {
            SQLException sqlException = null;
            if (OracleXid.isLocalTransaction(xid)) {
                return 24771;
            }
            try {
                resumeReturn = doStart(xid, 134217728);
            } catch (SQLException sqlex) {
                resumeReturn = sqlex.getErrorCode();
                sqlException = sqlex;
            }
            if (resumeReturn == 24756) {
                kputxrec(xid, 4, 1, null);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return 0;
            }
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doForget", "resume returned : {0}", (String) null, (String) null, Integer.valueOf(resumeReturn));
            if (resumeReturn == 0) {
                try {
                    doEnd(xid, 0, false);
                } catch (Exception e) {
                    debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doForget", (String) null, (String) null, e);
                }
            }
            if (resumeReturn == 0 || resumeReturn == 2079 || resumeReturn == 24754 || resumeReturn == 24761 || resumeReturn == 24774 || resumeReturn == 24776 || resumeReturn == 25351) {
                checkError(24769, sqlException);
            } else if (resumeReturn == 24752) {
                checkError(24771, sqlException);
            } else {
                checkError(resumeReturn, sqlException);
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return 0;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:46:0x0163  */
    /* JADX WARN: Removed duplicated region for block: B:66:0x01b0 A[ORIG_RETURN, RETURN] */
    @Override // oracle.jdbc.xa.client.OracleXAResource
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected void doRollback(javax.transaction.xa.Xid r11) throws java.sql.SQLException, javax.transaction.xa.XAException {
        /*
            Method dump skipped, instructions count: 433
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CXAResource.doRollback(javax.transaction.xa.Xid):void");
    }

    int doTransaction(Xid xid, int mode, int inTransactionState) throws SQLException {
        T4CConnection physicalConn = getT4CConnection();
        try {
            T4CTTIOtxen otxen = physicalConn.otxen;
            byte[] xidxid = null;
            byte[] gtrid = xid.getGlobalTransactionId();
            byte[] bqual = xid.getBranchQualifier();
            int gtrid_l = 0;
            int bqual_l = 0;
            if (gtrid != null && bqual != null) {
                gtrid_l = Math.min(gtrid.length, 64);
                bqual_l = Math.min(bqual.length, 64);
                xidxid = new byte[128];
                System.arraycopy(gtrid, 0, xidxid, 0, gtrid_l);
                System.arraycopy(bqual, 0, xidxid, gtrid_l, bqual_l);
            }
            byte[] txctx = this.context;
            physicalConn.needLine();
            otxen.doOTXEN(mode, txctx, xidxid, xid.getFormatId(), gtrid_l, bqual_l, this.timeout, inTransactionState, 0);
            int transactionOutState = otxen.getOutStateFromServer();
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doTransaction", "transactionOutState={0}", (String) null, (String) null, Integer.valueOf(transactionOutState));
            return transactionOutState;
        } catch (IOException ioe) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "doTransaction", (String) null, (String) null, ioe);
            physicalConn.handleIOException(ioe);
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), ioe).fillInStackTrace());
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: javax.transaction.xa.XAException */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v69, types: [java.lang.Throwable] */
    protected void kputxrec(Xid xid, int opcode, int tries, SQLException sqlexCause) throws SQLException, InterruptedException, XAException {
        int inTransactionMode;
        int incmd;
        OracleXAException xae;
        T4CConnection physicalConn = this.connection instanceof ReplayableConnection ? (T4CConnection) ((_Proxy_) this.connection)._getDelegate_() : (T4CConnection) this.connection;
        switch (opcode) {
            case 1:
                inTransactionMode = 3;
                break;
            case 4:
                inTransactionMode = 2;
                break;
            default:
                inTransactionMode = 0;
                break;
        }
        int endstate = -1;
        while (true) {
            int i = tries;
            tries--;
            if (i > 0) {
                endstate = doTransaction(xid, 5, inTransactionMode);
                if (endstate == 7) {
                    try {
                        Thread.sleep(1000L);
                    } catch (Exception e) {
                    }
                }
            }
        }
        if (endstate == 7 || endstate == -1) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "kputxrec", "T4CXAResource.kputxrec: Try to recover but transaction operation cannot be completed now. Transaction not ready for recovery.", (String) null, null);
            throw new XAException(-6);
        }
        int xaErrorCode = -1;
        int returnErrorCode = 0;
        switch (endstate) {
            case 0:
                if (opcode == 4) {
                    incmd = 8;
                    xaErrorCode = -3;
                    returnErrorCode = 24762;
                    break;
                } else {
                    incmd = 7;
                    if (opcode == 1) {
                        xaErrorCode = -4;
                        returnErrorCode = 24756;
                        break;
                    }
                }
                break;
            case 2:
                if (opcode == 4) {
                    incmd = 8;
                    xaErrorCode = -6;
                    returnErrorCode = 24770;
                    break;
                }
            case 1:
            default:
                incmd = 8;
                xaErrorCode = -3;
                returnErrorCode = 24762;
                break;
            case 3:
                if (opcode == 1) {
                    incmd = 7;
                    break;
                } else {
                    incmd = 8;
                    xaErrorCode = -3;
                    returnErrorCode = 24762;
                    break;
                }
            case 4:
                if (opcode == 4) {
                    incmd = 7;
                    break;
                } else {
                    incmd = 8;
                    xaErrorCode = 6;
                    returnErrorCode = 24765;
                    break;
                }
            case 5:
                if (opcode == 4) {
                    incmd = 7;
                    break;
                } else {
                    incmd = 8;
                    xaErrorCode = 7;
                    returnErrorCode = 24764;
                    break;
                }
            case 6:
                if (opcode == 4) {
                    incmd = 7;
                    break;
                } else {
                    incmd = 8;
                    xaErrorCode = 5;
                    returnErrorCode = 24766;
                    break;
                }
        }
        T4CTTIk2rpc k2rpc = physicalConn.k2rpc;
        try {
            k2rpc.doOK2RPC(3, incmd);
            if (xaErrorCode != -1) {
                if (sqlexCause != null && returnErrorCode == 0) {
                    xae = new OracleXAException(sqlexCause.getErrorCode(), xaErrorCode);
                    xae.initCause(sqlexCause);
                } else {
                    xae = new OracleXAException(returnErrorCode, xaErrorCode);
                }
                throw xae;
            }
        } catch (IOException ioe) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "kputxrec", (String) null, (String) null, ioe);
            XAException xae2 = new XAException(-7);
            xae2.initCause(ioe);
            throw xae2;
        } catch (SQLException s) {
            debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "kputxrec", (String) null, (String) null, s);
            XAException xar = new XAException(-6);
            xar.initCause(s);
            throw xar;
        }
    }

    final void setPasswordInternal(OpaqueString p) {
        this.password = p;
    }

    private T4CConnection getT4CConnection() throws SQLException {
        if (this.connection instanceof AbstractTrueCacheConnection) {
            return (T4CConnection) this.connection.unwrap(oracle.jdbc.internal.OracleConnection.class);
        }
        if (this.connection instanceof ReplayableConnection) {
            return (T4CConnection) ((_Proxy_) this.connection)._getDelegate_();
        }
        return (T4CConnection) this.connection;
    }

    @Override // oracle.jdbc.xa.OracleXAResource
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return (oracle.jdbc.internal.OracleConnection) this.connection;
    }
}
