package oracle.jdbc.driver.utils;

import java.lang.Throwable;
import java.util.function.Function;

@FunctionalInterface
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/ThrowingFunction.class */
public interface ThrowingFunction<T, R, E extends Throwable> extends Function<T, R> {
    R applyOrThrow(T t) throws Throwable;

    @Override // java.util.function.Function
    default R apply(T input) {
        try {
            return applyOrThrow(input);
        } catch (Throwable throwable) {
            throw CheckedExceptionHandler.toRuntimeException(throwable);
        }
    }
}
