package oracle.jdbc.datasource.impl;

import java.sql.SQLException;
import java.util.Properties;
import javax.naming.spi.ObjectFactory;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/impl/OracleDataSourceFactory.class */
public class OracleDataSourceFactory implements ObjectFactory {
    private static final String CONNECTION_PROPERTIES = "connectionProperties";
    private static final String ORACLE_CONN_DATA_POOL_SOURCE = "oracle.jdbc.datasource.impl.OracleConnectionPoolDataSource";
    private static final String ORACLE_CONN_DATA_POOL_SOURCE_OLD = "oracle.jdbc.pool.OracleConnectionPoolDataSource";
    private static final String ORACLE_OCI_CONN_POOL = "oracle.jdbc.pool.OracleOCIConnectionPool";
    private static final String ORACLE_DATA_SOURCE = "oracle.jdbc.datasource.impl.OracleDataSource";
    private static final String ORACLE_DATA_SOURCE_OLD = "oracle.jdbc.pool.OracleDataSource";
    private static final String ORACLE_XA_DATA_SOURCE = "oracle.jdbc.xa.client.OracleXADataSource";

    /* JADX WARN: Removed duplicated region for block: B:104:0x03ab A[PHI: r13
      0x03ab: PHI (r13v18 'st' javax.naming.StringRefAddr) = (r13v17 'st' javax.naming.StringRefAddr), (r13v26 'st' javax.naming.StringRefAddr) binds: [B:101:0x0398, B:103:0x03a8] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:109:0x03d8 A[PHI: r13
      0x03d8: PHI (r13v20 'st' javax.naming.StringRefAddr) = (r13v19 'st' javax.naming.StringRefAddr), (r13v25 'st' javax.naming.StringRefAddr) binds: [B:106:0x03c5, B:108:0x03d5] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:114:0x040c A[PHI: r13
      0x040c: PHI (r13v22 'st' javax.naming.StringRefAddr) = (r13v21 'st' javax.naming.StringRefAddr), (r13v23 'st' javax.naming.StringRefAddr) binds: [B:111:0x03f9, B:113:0x0409] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:71:0x0280 A[PHI: r13
      0x0280: PHI (r13v5 'st' javax.naming.StringRefAddr) = 
      (r13v4 'st' javax.naming.StringRefAddr)
      (r13v32 'st' javax.naming.StringRefAddr)
      (r13v33 'st' javax.naming.StringRefAddr)
     binds: [B:66:0x025d, B:68:0x026d, B:70:0x027d] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:76:0x02ad A[PHI: r13
      0x02ad: PHI (r13v7 'st' javax.naming.StringRefAddr) = (r13v6 'st' javax.naming.StringRefAddr), (r13v31 'st' javax.naming.StringRefAddr) binds: [B:73:0x029a, B:75:0x02aa] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:81:0x02da A[PHI: r13
      0x02da: PHI (r13v9 'st' javax.naming.StringRefAddr) = (r13v8 'st' javax.naming.StringRefAddr), (r13v30 'st' javax.naming.StringRefAddr) binds: [B:78:0x02c7, B:80:0x02d7] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:86:0x0307 A[PHI: r13
      0x0307: PHI (r13v11 'st' javax.naming.StringRefAddr) = (r13v10 'st' javax.naming.StringRefAddr), (r13v29 'st' javax.naming.StringRefAddr) binds: [B:83:0x02f4, B:85:0x0304] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:91:0x0334 A[PHI: r13
      0x0334: PHI (r13v13 'st' javax.naming.StringRefAddr) = (r13v12 'st' javax.naming.StringRefAddr), (r13v28 'st' javax.naming.StringRefAddr) binds: [B:88:0x0321, B:90:0x0331] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:96:0x0361 A[PHI: r13
      0x0361: PHI (r13v15 'st' javax.naming.StringRefAddr) = (r13v14 'st' javax.naming.StringRefAddr), (r13v27 'st' javax.naming.StringRefAddr) binds: [B:93:0x034e, B:95:0x035e] A[DONT_GENERATE, DONT_INLINE]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.lang.Object getObjectInstance(java.lang.Object r5, javax.naming.Name r6, javax.naming.Context r7, java.util.Hashtable r8) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 1114
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.datasource.impl.OracleDataSourceFactory.getObjectInstance(java.lang.Object, javax.naming.Name, javax.naming.Context, java.util.Hashtable):java.lang.Object");
    }

    @Blind(PropertiesBlinder.class)
    private Properties extractConnectionProperties(String val) throws SQLException {
        Properties cprops = new Properties();
        String[] tokens = val.substring(1, val.length() - 1).split(";");
        for (String strTkn : tokens) {
            int len = strTkn.length();
            int equalsIndex = strTkn.indexOf("=");
            if (len == 0 || equalsIndex <= 0) {
                throw ((SQLException) DatabaseError.createSqlException(190).fillInStackTrace());
            }
            String key = strTkn.substring(0, equalsIndex);
            String value = strTkn.substring(equalsIndex + 1, len);
            cprops.setProperty(key.trim(), value.trim());
        }
        return cprops;
    }

    protected OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
