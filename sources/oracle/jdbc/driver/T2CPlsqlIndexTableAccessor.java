package oracle.jdbc.driver;

import java.math.BigDecimal;
import java.sql.SQLException;
import oracle.sql.CHAR;
import oracle.sql.CharacterSet;
import oracle.sql.Datum;
import oracle.sql.NUMBER;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T2CPlsqlIndexTableAccessor.class */
class T2CPlsqlIndexTableAccessor extends PlsqlIndexTableAccessor {
    int ibtMetaIndex;

    T2CPlsqlIndexTableAccessor(OracleStatement stmt, PlsqlIbtBindInfo ibtBindInfo_, short form) throws SQLException {
        super(stmt, ibtBindInfo_, form);
    }

    @Override // oracle.jdbc.driver.PlsqlIndexTableAccessor, oracle.jdbc.driver.Accessor
    void initForDataAccess(int external_type, int max_len, String typeName) throws SQLException {
        unimpl("initForDataAccess");
    }

    @Override // oracle.jdbc.driver.PlsqlIndexTableAccessor
    Object[] getPlsqlIndexTable(int currentRow) throws SQLException {
        Object[] result;
        short[] ibtBindIndicators = this.statement.ibtBindIndicators;
        int actualElements = ((ibtBindIndicators[this.ibtMetaIndex + 4] & 65535) << 16) + (ibtBindIndicators[this.ibtMetaIndex + 5] & 65535);
        long offset = getOffset(currentRow);
        int maxLength = this.ibtBindInfo.elemMaxLen;
        switch (this.ibtBindInfo.element_internal_type) {
            case 6:
                result = new BigDecimal[actualElements];
                for (int i = 0; i < actualElements; i++) {
                    this.rowData.setPosition(offset);
                    int len = this.rowData.get() & 255;
                    if (len == 0) {
                        result[i] = null;
                    } else {
                        result[i] = NUMBER.toBigDecimal(this.rowData.getBytes(len));
                    }
                    offset += maxLength;
                }
                break;
            case 9:
                int[] out_lengthInChars = new int[1];
                result = new String[actualElements];
                for (int i2 = 0; i2 < actualElements; i2++) {
                    this.rowData.setPosition(offset);
                    char[] c = this.rowData.getChars(offset, 1, this.statement.connection.conversion.getCharacterSet((short) 1), out_lengthInChars);
                    int len2 = c[0] / 2;
                    this.rowData.setPosition(offset + 1);
                    if (len2 == 0) {
                        result[i2] = null;
                    } else {
                        result[i2] = this.rowData.getString(len2, this.statement.connection.conversion.getCharacterSet((short) 1));
                    }
                    offset += maxLength;
                }
                break;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 97).fillInStackTrace());
        }
        return result;
    }

    @Override // oracle.jdbc.driver.PlsqlIndexTableAccessor, oracle.jdbc.driver.Accessor
    Datum[] getOraclePlsqlIndexTable(int currentRow) throws SQLException {
        Datum[] result;
        short[] ibtBindIndicators = this.statement.ibtBindIndicators;
        int actualElements = ((ibtBindIndicators[this.ibtMetaIndex + 4] & 65535) << 16) + (ibtBindIndicators[this.ibtMetaIndex + 5] & 65535);
        long offset = getOffset(currentRow);
        int maxLength = this.ibtBindInfo.elemMaxLen;
        switch (this.ibtBindInfo.element_internal_type) {
            case 6:
                result = new NUMBER[actualElements];
                for (int i = 0; i < actualElements; i++) {
                    this.rowData.setPosition(offset);
                    int len = this.rowData.get() & 255;
                    if (len == 0) {
                        result[i] = null;
                    } else {
                        result[i] = new NUMBER(this.rowData.getBytes(len));
                    }
                    offset += maxLength;
                }
                break;
            case 9:
                int[] out_lengthInChars = new int[1];
                result = new CHAR[actualElements];
                CharacterSet charset = this.statement.connection.conversion.getDriverCharSetObj();
                for (int i2 = 0; i2 < actualElements; i2++) {
                    this.rowData.setPosition(offset);
                    char[] c = this.rowData.getChars(offset, 1, this.statement.connection.conversion.getCharacterSet((short) 1), out_lengthInChars);
                    int len2 = c[0] / 2;
                    this.rowData.setPosition(offset + 1);
                    if (len2 == 0) {
                        result[i2] = null;
                    } else {
                        String s = this.rowData.getString(len2, this.statement.connection.conversion.getCharacterSet((short) 1));
                        result[i2] = new CHAR(s, charset);
                    }
                    offset += maxLength;
                }
                break;
            default:
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 97).fillInStackTrace());
        }
        return result;
    }
}
