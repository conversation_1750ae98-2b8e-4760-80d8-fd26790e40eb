package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.diagnostics.Diagnosable;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrdeq.class */
public class T4CTTIkpdnrdeq extends T4CTTIfun {
    static final short COMMIT_KPNAQDEQ = 1;
    static final short ROLLBACK_KPNAQDEQ = 2;
    byte[] clientIdBytes;
    short opCode;
    T4CMAREngine mar;
    T4CConnection connection;
    String queue;
    long[] registrationIdArr;
    byte[][] messageIdArr;
    byte[][] queueNameBytesArr;
    int noOfAck;
    static final /* synthetic */ boolean $assertionsDisabled;

    @Override // oracle.jdbc.driver.T4CTTIMsg, oracle.jdbc.diagnostics.Diagnosable
    public /* bridge */ /* synthetic */ Diagnosable getDiagnosable() {
        return super.getDiagnosable();
    }

    static {
        $assertionsDisabled = !T4CTTIkpdnrdeq.class.desiredAssertionStatus();
    }

    T4CTTIkpdnrdeq(T4CConnection conn) {
        super(conn, (byte) 3);
        this.clientIdBytes = null;
        this.messageIdArr = (byte[][]) null;
        this.queueNameBytesArr = (byte[][]) null;
        this.noOfAck = 1;
        this.mar = conn.mare;
        this.connection = conn;
        setFunCode((short) 186);
    }

    /* JADX WARN: Type inference failed for: r1v11, types: [byte[], byte[][]] */
    void doOAQEMNDEQ(String clientId, short opCode, byte[][] messageIds, long[] registrationIds, String[] queues) throws SQLException, IOException {
        if (!$assertionsDisabled && (clientId == null || queues == null)) {
            throw new AssertionError("cliendId is " + clientId + ", queue is " + queues[0]);
        }
        this.clientIdBytes = this.mar.conv.StringToCharBytes(clientId);
        this.opCode = opCode;
        this.noOfAck = registrationIds.length;
        this.messageIdArr = messageIds;
        this.registrationIdArr = registrationIds;
        this.queueNameBytesArr = new byte[this.noOfAck];
        for (int i = 0; i < this.noOfAck; i++) {
            this.queueNameBytesArr[i] = this.mar.conv.StringToCharBytes(queues[i]);
        }
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        if (this.clientIdBytes != null && this.clientIdBytes.length != 0) {
            this.mar.marshalPTR();
            this.mar.marshalSWORD(this.clientIdBytes.length);
        } else {
            this.mar.marshalNULLPTR();
            this.mar.marshalSWORD(0);
        }
        this.mar.marshalUB1(this.opCode);
        if (this.noOfAck > 0) {
            this.mar.marshalPTR();
            this.mar.marshalUB4(this.noOfAck);
        } else {
            this.mar.marshalNULLPTR();
            this.mar.marshalUB4(0L);
        }
        if (this.clientIdBytes != null && this.clientIdBytes.length != 0) {
            this.mar.marshalCHR(this.clientIdBytes);
        }
        T4CTTIkpdnrack kpdnrack = new T4CTTIkpdnrack(this.connection);
        for (int i = 0; i < this.noOfAck; i++) {
            kpdnrack.send(this.queueNameBytesArr[i], this.registrationIdArr[i], this.messageIdArr[i]);
        }
    }
}
