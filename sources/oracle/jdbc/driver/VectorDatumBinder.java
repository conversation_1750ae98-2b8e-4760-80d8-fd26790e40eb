package oracle.jdbc.driver;

import oracle.sql.VECTOR;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/VectorDatumBinder.class */
final class VectorDatumBinder extends DatumBinder {
    Binder copyingBinder;

    VectorDatumBinder(VECTOR vector) {
        this(vector.shareBytes());
    }

    VectorDatumBinder(byte[] vectorBytes) {
        super(vectorBytes);
        this.copyingBinder = null;
        this.type = (short) 127;
        this.bytelen = vectorBytes.length;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.copyingBinder == null) {
            this.copyingBinder = new VectorCopyingBinder(this);
        }
        return this.copyingBinder;
    }
}
