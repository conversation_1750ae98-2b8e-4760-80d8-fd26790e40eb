package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson.class */
public class ErrorMessagesJson extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "An i/o exception occurred"}, new Object[]{"26302", "The year \"{0}\" is not supported"}, new Object[]{"26303", "Overflow, value too large: {0}."}, new Object[]{"26304", "Unsupported option (not implemented)."}, new Object[]{"26305", "Binary JSON is invalid or corrupt."}, new Object[]{"26306", "Unsupported binary JSON version: {0}."}, new Object[]{"26307", "The UTF-8 encoded key length must not be greater than 256 bytes. The following key exceeds this limit: \"{0}\"."}, new Object[]{"26308", "The specified JSON is too large to be encoded as binary JSON.  The encoded images size must not exceed 2GB."}, new Object[]{"26309", "Binary JSON is invalid or corrupt. Specified image only contains {0} bytes."}, new Object[]{"26310", "The specified java.time.Period has days set but the Oracle year to month interval does not support days."}, new Object[]{"26311", "Generator closed before before end."}, new Object[]{"26312", "An object key must be specified in this context."}, new Object[]{"26313", "Invalid write. A complete value has already been written."}, new Object[]{"26314", "End not allowed in this context."}, new Object[]{"26315", "Key not allowed in this context."}, new Object[]{"26316", "Expected value after key."}, new Object[]{"26317", "Parser state must be {0}."}, new Object[]{"26318", "Parser state must not be {0}."}, new Object[]{"26319", "Parser must be on a value."}, new Object[]{"26320", "\"{0}\" is not a supported wrapper type."}, new Object[]{"26321", "This object can not be modified. To make a modifiable copy, use OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "This array can not be modified. To make a modifiable copy, use OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "JSON object contains duplicate key: {0}."}, new Object[]{"26324", "Cannot auto-detect encoding, not enough chars."}, new Object[]{"26325", "Expected EOF token, but got {0}."}, new Object[]{"26326", "Unexpected character {0} at line {1}, column {2}."}, new Object[]{"26327", "Unexpected character {0} at line {1}, column {2}. Expected: {3}."}, new Object[]{"26328", "Invalid token {0} at line {1}, column {2}. Expected tokens are: {3}."}, new Object[]{"26329", "JsonParser#getString() is valid only KEY_NAME, VALUE_STRING, VALUE_NUMBER parser states. But current parser state is {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() is valid only VALUE_NUMBER parser state. But current parser state is {0}."}, new Object[]{"26331", "JsonParser#getInt() is valid only VALUE_NUMBER parser state. But current parser state is {0}."}, new Object[]{"26332", "JsonParser#getLong() is valid only VALUE_NUMBER parser state. But current parser state is {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() is valid only VALUE_NUMBER parser state. But current parser state is {0}."}, new Object[]{"26334", "JsonParser#getArray() is valid only for START_ARRAY parser state. But current parser state is {0}."}, new Object[]{"26335", "JsonParser#getObject() is valid only for START_OBJECT parser state. But current parser state is {0}."}, new Object[]{"26336", "A timestamp with a region is not supported. Only offset timezones are supported."}, new Object[]{"26337", "The objects and arrays in the JSON value may not nest deeper than {0} levels"}, new Object[]{"26338", "The keys of a JSON object may not exceed 65,535 bytes"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
