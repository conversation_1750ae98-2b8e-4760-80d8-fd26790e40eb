package oracle.jdbc.driver;

import java.sql.Blob;
import java.sql.Clob;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.RowId;
import java.sql.SQLException;
import java.sql.SQLType;
import java.sql.SQLWarning;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import oracle.jdbc.OracleResultSet;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.GeneratedUpdatableResultSet;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleLargeObject;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/UpdatableResultSet.class */
class UpdatableResultSet extends GeneratedUpdatableResultSet {
    private static final String CLASS_NAME;
    static final int BEGIN_COLUMN_INDEX = 0;
    private GeneratedUpdatableResultSet.NullStatus wasNull;
    private OracleStatement scrollStmt;
    private ResultSetMetaData rsetMetaData;
    private int columnCount;
    private OraclePreparedStatement deleteStmt;
    private OraclePreparedStatement insertStmt;
    private List<String> lastUpdateColumns;
    private OraclePreparedStatement updateStmt;
    private int[] indexColsChanged;
    private boolean isUpdating;
    private boolean isInserting;
    private GeneratedUpdatableResultSet.Updater<?>[] updateBuffer;
    ArrayList<Clob> tempClobsToFree;
    ArrayList<Blob> tempBlobsToFree;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !UpdatableResultSet.class.desiredAssertionStatus();
        CLASS_NAME = UpdatableResultSet.class.getName();
    }

    UpdatableResultSet(OracleStatement stmt, OracleResultSet rset) throws SQLException {
        super(stmt, rset);
        this.scrollStmt = null;
        this.rsetMetaData = null;
        this.columnCount = 0;
        this.deleteStmt = null;
        this.lastUpdateColumns = null;
        this.indexColsChanged = null;
        this.isUpdating = false;
        this.isInserting = false;
        this.updateBuffer = null;
        this.tempClobsToFree = null;
        this.tempBlobsToFree = null;
        this.scrollStmt = stmt;
        this.wasNull = GeneratedUpdatableResultSet.NullStatus.VALUE_UNKNOWN;
        getInternalMetadata();
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet
    void ensureOpen() throws SQLException {
        if (this.closed) {
            if (this.connection.isClosed()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 8).fillInStackTrace());
            }
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 10).fillInStackTrace());
        }
        if (this.resultSet == null || this.scrollStmt == null || this.scrollStmt.closed) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 9).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet, java.lang.AutoCloseable
    public void close() throws SQLException {
        if (this.closed) {
            return;
        }
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            doClose();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    protected void doClose() throws SQLException {
        this.connection.assertLockHeldByCurrentThread();
        if (this.closed) {
            return;
        }
        super.doClose();
        if (this.resultSet != null) {
            this.resultSet.doClose();
        }
        if (this.insertStmt != null) {
            this.insertStmt.closeOrCache(null);
        }
        if (this.updateStmt != null) {
            this.updateStmt.closeOrCache(null);
        }
        if (this.deleteStmt != null) {
            this.deleteStmt.closeOrCache(null);
        }
        if (this.scrollStmt != null) {
            this.scrollStmt.notifyCloseRset();
        }
        this.connection = LogicalConnection.closedConnection;
        this.resultSet = null;
        this.scrollStmt = null;
        this.rsetMetaData = null;
        this.scrollStmt = null;
        this.deleteStmt = null;
        this.insertStmt = null;
        this.updateStmt = null;
        this.indexColsChanged = null;
        this.updateBuffer = null;
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean wasNull() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            switch (this.wasNull) {
                case VALUE_UNKNOWN:
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 24).fillInStackTrace());
                case VALUE_IN_RSET:
                    boolean zWasNull = this.resultSet.wasNull();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return zWasNull;
                case VALUE_NULL:
                    return true;
                case VALUE_NOT_NULL:
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th3) {
                                th.addSuppressed(th3);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return false;
                default:
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 24).fillInStackTrace());
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public Statement getStatement() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            OracleStatement oracleStatement = this.scrollStmt;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return oracleStatement;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public SQLWarning getWarnings() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            SQLWarning innerWarnings = this.resultSet.getWarnings();
            if (this.sqlWarning == null) {
                return innerWarnings;
            }
            SQLWarning thisWarning = this.sqlWarning;
            while (thisWarning.getNextWarning() != null) {
                thisWarning = thisWarning.getNextWarning();
            }
            thisWarning.setNextWarning(innerWarnings);
            SQLWarning sQLWarning = this.sqlWarning;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return sQLWarning;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void clearWarnings() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            this.sqlWarning = null;
            this.resultSet.clearWarnings();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.OracleResultSet
    public OracleResultSet.AuthorizationIndicator getAuthorizationIndicator(int columnIndex) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            OracleResultSet.AuthorizationIndicator authorizationIndicator = this.resultSet.getAuthorizationIndicator(columnIndex);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return authorizationIndicator;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean next() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            cancelRowChanges();
            if (this.isRowDeleted) {
                this.isRowDeleted = false;
                boolean zIsValidRow = this.resultSet.isValidRow();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zIsValidRow;
            }
            boolean next = this.resultSet.next();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return next;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean isBeforeFirst() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (!this.isRowDeleted) {
                boolean zIsBeforeFirst = this.resultSet.isBeforeFirst();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zIsBeforeFirst;
            }
            boolean zIsFirst = this.resultSet.isFirst();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return zIsFirst;
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean isAfterLast() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            boolean zIsAfterLast = this.resultSet.isAfterLast();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zIsAfterLast;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean isFirst() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            boolean zIsFirst = this.resultSet.isFirst();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zIsFirst;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean isLast() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            boolean zIsLast = this.resultSet.isLast();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zIsLast;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void beforeFirst() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            cancelRowChanges();
            this.isRowDeleted = false;
            this.resultSet.beforeFirst();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void afterLast() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            cancelRowChanges();
            this.isRowDeleted = false;
            this.resultSet.afterLast();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean first() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            cancelRowChanges();
            this.isRowDeleted = false;
            boolean zFirst = this.resultSet.first();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zFirst;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean last() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            cancelRowChanges();
            this.isRowDeleted = false;
            boolean zLast = this.resultSet.last();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zLast;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public int getRow() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            int row = this.resultSet.getRow();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return row;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean absolute(int row) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                ensureOpen();
                cancelRowChanges();
                this.isRowDeleted = false;
                boolean zAbsolute = this.resultSet.absolute(row);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zAbsolute;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean relative(int rows) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                ensureOpen();
                cancelRowChanges();
                if (this.isRowDeleted) {
                    rows--;
                    this.isRowDeleted = false;
                }
                boolean zRelative = this.resultSet.relative(rows);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return zRelative;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean previous() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            cancelRowChanges();
            this.isRowDeleted = false;
            boolean zPrevious = this.resultSet.previous();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zPrevious;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void addToTempLobsToFree(Clob tclob) {
        if (this.tempClobsToFree == null) {
            this.tempClobsToFree = new ArrayList<>();
        }
        this.tempClobsToFree.add(tclob);
    }

    void addToTempLobsToFree(Blob tblob) {
        if (this.tempBlobsToFree == null) {
            this.tempBlobsToFree = new ArrayList<>();
        }
        this.tempBlobsToFree.add(tblob);
    }

    void cleanTempLobs() {
        cleanTempClobs(this.tempClobsToFree);
        cleanTempBlobs(this.tempBlobsToFree);
        this.tempClobsToFree = null;
        this.tempBlobsToFree = null;
    }

    void cleanTempBlobs(ArrayList<Blob> x) {
        if (x != null) {
            Iterator<Blob> iter = x.iterator();
            while (iter.hasNext()) {
                try {
                    ((OracleLargeObject) iter.next()).freeLOB();
                } catch (SQLException e) {
                    debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "cleanTempBlobs", " exception " + e.getMessage(), (String) null, (Throwable) null);
                }
            }
        }
    }

    void cleanTempClobs(ArrayList<Clob> x) {
        if (x != null) {
            Iterator<Clob> iter = x.iterator();
            while (iter.hasNext()) {
                try {
                    ((OracleLargeObject) iter.next()).freeLOB();
                } catch (SQLException e) {
                    debug(Level.WARNING, SecurityLabel.UNKNOWN, CLASS_NAME, "cleanTempClobs", " exception " + e.getMessage(), (String) null, (Throwable) null);
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public ResultSetMetaData getMetaData() throws SQLException {
        ensureOpen();
        return this.resultSet.getMetaData();
    }

    @Override // oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public int findColumn(String columnName) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            int iFindColumn = this.resultSet.findColumn(columnName);
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return iFindColumn;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void setFetchDirection(int direction) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                ensureOpen();
                this.resultSet.setFetchDirection(direction);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public int getFetchDirection() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            int fetchDirection = this.resultSet.getFetchDirection();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return fetchDirection;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void setFetchSize(int rows) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                ensureOpen();
                this.resultSet.setFetchSize(rows);
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public int getFetchSize() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            int fetchSize = this.resultSet.getFetchSize();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return fetchSize;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public int getType() throws SQLException {
        ensureOpen();
        return doGetType();
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    protected int doGetType() {
        return this.scrollStmt.realRsetType.getType();
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public int getConcurrency() throws SQLException {
        ensureOpen();
        return 1008;
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public String getCursorName() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 23, "getCursorName").fillInStackTrace());
        } catch (Throwable th2) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            throw th2;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean rowUpdated() throws SQLException {
        ensureOpen();
        return false;
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean rowInserted() throws SQLException {
        ensureOpen();
        return false;
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public boolean rowDeleted() throws SQLException {
        ensureOpen();
        return this.isRowDeleted;
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void insertRow() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            if (!isOnInsertRow()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 83).fillInStackTrace());
            }
            prepareInsertRowStatement();
            prepareInsertRowBinds();
            RowId insertedRowId = executeInsertRow();
            this.resultSet.insertRow(insertedRowId);
            cancelRowChanges();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void updateRow() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            if (isOnInsertRow()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 84).fillInStackTrace());
            }
            if (isBeforeFirst()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 14).fillInStackTrace());
            }
            if (isAfterLast()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_RESULTSET_AFTER_LAST_ROW).fillInStackTrace());
            }
            int numColumnsChanged = getNumColumnsChanged();
            if (numColumnsChanged > 0) {
                prepareUpdateRowStatement(numColumnsChanged);
                prepareUpdateRowBinds(numColumnsChanged);
                executeUpdateRow();
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void deleteRow() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            if (isOnInsertRow()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 84).fillInStackTrace());
            }
            if (isBeforeFirst()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 14).fillInStackTrace());
            }
            if (isAfterLast()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_RESULTSET_AFTER_LAST_ROW).fillInStackTrace());
            }
            prepareDeleteRowStatement();
            prepareDeleteRowBinds();
            executeDeleteRow();
            this.isRowDeleted = true;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void refreshRow() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            if (isOnInsertRow()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 84).fillInStackTrace());
            }
            if (isBeforeFirst()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 14).fillInStackTrace());
            }
            if (isAfterLast()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), DatabaseError.EOJ_RESULTSET_AFTER_LAST_ROW).fillInStackTrace());
            }
            this.resultSet.refreshRow();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void cancelRowUpdates() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isUpdating) {
                this.isUpdating = false;
                clearUpdateBuffer();
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void moveToInsertRow() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (!isOnInsertRow()) {
                this.isRowDeleted = false;
                this.isInserting = true;
                if (this.updateBuffer == null) {
                    this.updateBuffer = new GeneratedUpdatableResultSet.Updater[getColumnCount()];
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                    return;
                }
                return;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void moveToCurrentRow() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            cancelRowChanges();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public Object getObject(int columnIndex) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            String cName = getInternalMetadata().getColumnClassName(columnIndex);
            try {
                Object object = getObject(columnIndex, Class.forName(cName));
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return object;
            } catch (ClassNotFoundException ex) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 1, ex).fillInStackTrace());
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    public <T> T getObject(int i, Class<T> cls) throws SQLException {
        Object objConvert;
        Monitor.CloseableLock closeableLockAcquireCloseableLock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            if (!$assertionsDisabled && cls == null) {
                throw new AssertionError("type: null");
            }
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            setIsNull(GeneratedUpdatableResultSet.NullStatus.VALUE_UNKNOWN);
            if (isOnInsertRow() || (isUpdatingRow() && isRowBufferUpdatedAt(i))) {
                GeneratedUpdatableResultSet.Updater<?> updater = getUpdater(i);
                if (updater == null) {
                    throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 3, "getObject").fillInStackTrace());
                }
                objConvert = JavaToJavaConverter.convert(updater.getValue(), cls, this.connection, updater.getExtra(), null);
                setIsNull(updater.getValue() == null);
            } else {
                setIsNull(GeneratedUpdatableResultSet.NullStatus.VALUE_IN_RSET);
                objConvert = this.resultSet.getObject(i, cls);
            }
            return (T) objConvert;
        } finally {
            if (closeableLockAcquireCloseableLock != null) {
                if (0 != 0) {
                    try {
                        closeableLockAcquireCloseableLock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    closeableLockAcquireCloseableLock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet, java.sql.ResultSet
    public void updateNull(int columnIndex) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            if (columnIndex < 1 || columnIndex > getColumnCount()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 3, "updateNull").fillInStackTrace());
            }
            final int colType = getInternalMetadata().getColumnType(columnIndex);
            if (colType == 2006 || colType == 2002 || colType == 2008 || colType == 2007 || colType == 2003 || colType == 2009) {
                final String typeName = getInternalMetadata().getColumnTypeName(columnIndex);
                setUpdater(columnIndex, new GeneratedUpdatableResultSet.Updater<Void>(null) { // from class: oracle.jdbc.driver.UpdatableResultSet.1
                    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet.Updater
                    public void set(OraclePreparedStatement statement, int index) throws SQLException {
                        statement.setNull(index, colType, typeName);
                    }
                });
            } else {
                setUpdater(columnIndex, new GeneratedUpdatableResultSet.Updater<Void>(null) { // from class: oracle.jdbc.driver.UpdatableResultSet.2
                    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet.Updater
                    public void set(OraclePreparedStatement statement, int index) throws SQLException {
                        statement.setNull(index, colType);
                    }
                });
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet, oracle.jdbc.driver.GeneratedResultSet, java.sql.ResultSet
    public void updateObject(int columnIndex, Object value, final int y) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82, "updateObject").fillInStackTrace());
            }
            if (columnIndex < 1 || columnIndex > getColumnCount()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 3, "updateObject").fillInStackTrace());
            }
            final int type = getInternalMetadata().getColumnType(columnIndex);
            setUpdater(columnIndex, new GeneratedUpdatableResultSet.Updater<Object>(value) { // from class: oracle.jdbc.driver.UpdatableResultSet.3
                @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet.Updater
                public void set(OraclePreparedStatement ps, int index) throws SQLException {
                    ps.setObject(index, this.value, type, y);
                }

                @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet.Updater
                public Object getExtra() {
                    return Integer.valueOf(y);
                }
            });
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    public void updateObject(int columnIndex, Object value, final SQLType targetSqlType) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82, "updateObject").fillInStackTrace());
            }
            if (columnIndex < 1 || columnIndex > getColumnCount()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 3, "updateObject").fillInStackTrace());
            }
            setUpdater(columnIndex, new GeneratedUpdatableResultSet.Updater<Object>(value) { // from class: oracle.jdbc.driver.UpdatableResultSet.4
                @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet.Updater
                public void set(OraclePreparedStatement ps, int index) throws SQLException {
                    ps.setObject(index, this.value, targetSqlType);
                }
            });
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    public void updateObject(int columnIndex, Object value, final SQLType targetSqlType, final int scaleOrLength) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            ensureOpen();
            if (this.isRowDeleted) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82, "updateObject").fillInStackTrace());
            }
            if (columnIndex < 1 || columnIndex > getColumnCount()) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 3, "updateObject").fillInStackTrace());
            }
            setUpdater(columnIndex, new GeneratedUpdatableResultSet.Updater<Object>(value) { // from class: oracle.jdbc.driver.UpdatableResultSet.5
                @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet.Updater
                public void set(OraclePreparedStatement ps, int index) throws SQLException {
                    ps.setObject(index, this.value, targetSqlType, scaleOrLength);
                }

                @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet.Updater
                public Object getExtra() {
                    return Integer.valueOf(scaleOrLength);
                }
            });
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    int getColumnCount() throws SQLException {
        if (this.columnCount == 0) {
            this.columnCount = this.resultSet.getColumnCount();
        }
        return this.columnCount;
    }

    OracleResultSetMetaData getInternalMetadata() throws SQLException {
        if (this.rsetMetaData == null) {
            this.rsetMetaData = this.resultSet.getMetaData();
        }
        return (OracleResultSetMetaData) this.rsetMetaData;
    }

    private void cancelRowChanges() throws SQLException {
        if (this.isInserting) {
            cancelRowInserts();
        }
        if (this.isUpdating) {
            cancelRowUpdates();
        }
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet
    boolean isOnInsertRow() {
        return this.isInserting;
    }

    private void cancelRowInserts() {
        if (this.isInserting) {
            this.isInserting = false;
            clearUpdateBuffer();
        }
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet
    boolean isUpdatingRow() {
        return this.isUpdating;
    }

    private void clearUpdateBuffer() {
        if (this.updateBuffer != null) {
            for (int i = 0; i < this.updateBuffer.length; i++) {
                this.updateBuffer[i] = null;
            }
        }
        cleanTempLobs();
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet
    protected void setUpdater(int idx, GeneratedUpdatableResultSet.Updater<?> setter) throws SQLException {
        if (!$assertionsDisabled && (idx <= 0 || idx > getColumnCount())) {
            throw new AssertionError("columnIndex: " + idx + " columnCount: " + getColumnCount());
        }
        if (!$assertionsDisabled && setter == null) {
            throw new AssertionError("setter is null");
        }
        if (!this.isInserting) {
            if (isBeforeFirst() || isAfterLast() || getRow() == 0) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 82).fillInStackTrace());
            }
            this.isUpdating = true;
        }
        if (this.updateBuffer == null) {
            this.updateBuffer = new GeneratedUpdatableResultSet.Updater[getColumnCount()];
        }
        this.updateBuffer[idx - 1] = setter;
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet
    protected GeneratedUpdatableResultSet.Updater<?> getUpdater(int idx) throws SQLException {
        if (idx < 1 || idx > getColumnCount()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "getUpdater").fillInStackTrace());
        }
        if (this.updateBuffer != null) {
            return this.updateBuffer[idx - 1];
        }
        return null;
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet
    protected boolean isRowBufferUpdatedAt(int idx) throws SQLException {
        if (idx < 1 || idx > getColumnCount()) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 68, "isRowBufferUpdatedAt").fillInStackTrace());
        }
        return (this.updateBuffer == null || this.updateBuffer[idx - 1] == null) ? false : true;
    }

    private void prepareInsertRowStatement() throws SQLException {
        ResultSetMetaData rsmd = getInternalMetadata();
        List<String> updateColumns = new ArrayList<>(this.updateBuffer.length);
        boolean anythingToInsert = false;
        for (int i = 1; i <= this.updateBuffer.length; i++) {
            if (isRowBufferUpdatedAt(i)) {
                updateColumns.add(rsmd.getColumnName(i));
                anythingToInsert = true;
            }
        }
        if (!anythingToInsert) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 86).fillInStackTrace());
        }
        if (this.insertStmt == null || !updateColumns.equals(this.lastUpdateColumns)) {
            if (this.insertStmt != null) {
                this.insertStmt.close();
            }
            this.lastUpdateColumns = updateColumns;
            String sql = this.scrollStmt.sqlObject.getInsertSqlForUpdatableResultSet(updateColumns);
            PreparedStatement ps = this.connection.prepareStatement(sql, 1);
            this.insertStmt = (OraclePreparedStatement) ((OraclePreparedStatementWrapper) ps).preparedStatement;
            this.insertStmt.setQueryTimeout(this.scrollStmt.getQueryTimeout());
            if (this.scrollStmt.sqlObject.generatedSqlNeedEscapeProcessing()) {
                this.insertStmt.setEscapeProcessing(true);
            }
        }
    }

    private void prepareInsertRowBinds() throws SQLException {
        int bindIndex = prepareSubqueryBinds(this.insertStmt, 1);
        OracleResultSetMetaData rsmd = getInternalMetadata();
        int param = bindIndex;
        for (int col = 1; col <= getColumnCount(); col++) {
            GeneratedUpdatableResultSet.Updater<?> up = getUpdater(col);
            if (up != null) {
                if (rsmd.isNCHAR(col)) {
                    this.insertStmt.setFormOfUse(param, (short) 2);
                }
                int i = param;
                param++;
                up.set(this.insertStmt, i);
            }
        }
    }

    private RowId executeInsertRow() throws SQLException {
        if (this.insertStmt.executeUpdate() != 1) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 85).fillInStackTrace());
        }
        ResultSet rs = this.insertStmt.getGeneratedKeys();
        try {
            rs.next();
            return rs.getRowId(1);
        } finally {
            rs.close();
        }
    }

    private int getNumColumnsChanged() throws SQLException {
        int numColumnsChanged = 0;
        if (this.indexColsChanged == null) {
            this.indexColsChanged = new int[getColumnCount()];
        }
        if (this.updateBuffer != null) {
            for (int i = 0; i < getColumnCount(); i++) {
                if (this.updateBuffer[i] != null) {
                    int i2 = numColumnsChanged;
                    numColumnsChanged++;
                    this.indexColsChanged[i2] = i;
                }
            }
        }
        return numColumnsChanged;
    }

    private void prepareUpdateRowStatement(int numColumnsChanged) throws SQLException {
        if (this.updateStmt != null) {
            this.updateStmt.close();
        }
        String sql = this.scrollStmt.sqlObject.getUpdateSqlForUpdatableResultSet(this, numColumnsChanged, this.updateBuffer, this.indexColsChanged);
        PreparedStatement ps = this.connection.prepareStatement(sql);
        this.updateStmt = (OraclePreparedStatement) ((OraclePreparedStatementWrapper) ps).preparedStatement;
        this.updateStmt.setQueryTimeout(this.scrollStmt.getQueryTimeout());
        if (this.scrollStmt.sqlObject.generatedSqlNeedEscapeProcessing()) {
            this.updateStmt.setEscapeProcessing(true);
        }
    }

    private void prepareUpdateRowBinds(int numColumnsChanged) throws SQLException {
        int bindIndex = prepareSubqueryBinds(this.updateStmt, 1);
        OracleResultSetMetaData rsmd = getInternalMetadata();
        for (int i = 0; i < numColumnsChanged; i++) {
            int idx = this.indexColsChanged[i];
            GeneratedUpdatableResultSet.Updater<?> up = getUpdater(idx + 1);
            Object value = up.getValue();
            if (value != null) {
                if (rsmd.isNCHAR(idx + 1)) {
                    this.updateStmt.setFormOfUse(bindIndex, (short) 2);
                }
                int i2 = bindIndex;
                bindIndex++;
                up.set(this.updateStmt, i2);
            } else {
                int colType = getInternalMetadata().getColumnType(idx + 1);
                if (colType == 2006 || colType == 2002 || colType == 2008 || colType == 2007 || colType == 2003 || colType == 2009) {
                    int i3 = bindIndex;
                    bindIndex++;
                    this.updateStmt.setNull(i3, colType, getInternalMetadata().getColumnTypeName(idx + 1));
                } else {
                    if (rsmd.isNCHAR(idx + 1)) {
                        this.updateStmt.setFormOfUse(bindIndex, (short) 2);
                    }
                    int i4 = bindIndex;
                    bindIndex++;
                    this.updateStmt.setNull(i4, colType);
                }
            }
        }
        prepareCompareSelfBinds(this.updateStmt, bindIndex);
    }

    private void executeUpdateRow() throws SQLException {
        try {
            if (this.updateStmt.executeUpdate() == 0) {
                throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 85).fillInStackTrace());
            }
            refreshRows(getRow() - 1, 1);
            cancelRowUpdates();
        } finally {
            if (this.updateStmt != null) {
                this.updateStmt.close();
                this.updateStmt = null;
            }
        }
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    int refreshRows(long firstRow, int numberOfRows) throws SQLException {
        return this.resultSet.refreshRows(firstRow, numberOfRows);
    }

    private void prepareDeleteRowStatement() throws SQLException {
        if (this.deleteStmt == null) {
            String sql = this.scrollStmt.sqlObject.getDeleteSqlForUpdatableResultSet(this);
            PreparedStatement ps = this.connection.prepareStatement(sql);
            this.deleteStmt = (OraclePreparedStatement) ((OraclePreparedStatementWrapper) ps).preparedStatement;
            this.deleteStmt.setQueryTimeout(this.scrollStmt.getQueryTimeout());
            if (this.scrollStmt.sqlObject.generatedSqlNeedEscapeProcessing()) {
                this.deleteStmt.setEscapeProcessing(true);
            }
        }
    }

    private void prepareDeleteRowBinds() throws SQLException {
        int bindIndex = prepareSubqueryBinds(this.deleteStmt, 1);
        prepareCompareSelfBinds(this.deleteStmt, bindIndex);
    }

    private void executeDeleteRow() throws SQLException {
        if (this.deleteStmt.executeUpdate() == 0) {
            throw ((SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 85).fillInStackTrace());
        }
        removeCurrentRowFromCache();
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    void removeCurrentRowFromCache() throws SQLException {
        this.resultSet.removeCurrentRowFromCache();
    }

    @Override // oracle.jdbc.OracleResultSet
    public boolean isFromResultSetCache() throws SQLException {
        if (this.resultSet instanceof InsensitiveScrollableResultSet) {
            return ((InsensitiveScrollableResultSet) this.resultSet).isFromResultSetCache();
        }
        return false;
    }

    @Override // oracle.jdbc.OracleResultSet
    public byte[] getCompileKey() throws SQLException {
        return this.scrollStmt.getCompileKey();
    }

    @Override // oracle.jdbc.OracleResultSet
    public byte[] getRuntimeKey() throws SQLException {
        return this.scrollStmt.getRuntimeKey();
    }

    private int prepareCompareSelfBinds(OraclePreparedStatement pstmt, int bindIndex) throws SQLException {
        pstmt.setRowId(bindIndex, ((InsensitiveScrollableResultSet) this.resultSet).getPrependedRowId());
        return bindIndex + 1;
    }

    private int prepareSubqueryBinds(OraclePreparedStatement pstmt, int bindIndex) throws SQLException {
        return bindIndex + this.scrollStmt.copyBinds(pstmt, bindIndex - 1);
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet
    protected void setIsNull(GeneratedUpdatableResultSet.NullStatus status) {
        this.wasNull = status;
    }

    @Override // oracle.jdbc.driver.GeneratedUpdatableResultSet
    protected void setIsNull(boolean isNull) {
        setIsNull(isNull ? GeneratedUpdatableResultSet.NullStatus.VALUE_NULL : GeneratedUpdatableResultSet.NullStatus.VALUE_NOT_NULL);
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    protected void doneFetchingRows(boolean fromPrepareForNewResult) throws SQLException {
        this.resultSet.doneFetchingRows(fromPrepareForNewResult);
    }

    @Override // oracle.jdbc.driver.OracleResultSet
    OracleStatement getOracleStatement() throws SQLException {
        if (this.resultSet == null) {
            return null;
        }
        return this.resultSet.getOracleStatement();
    }

    @Override // oracle.jdbc.internal.OracleResultSet
    public int getCursorId() throws SQLException {
        return this.scrollStmt.getCursorId();
    }
}
