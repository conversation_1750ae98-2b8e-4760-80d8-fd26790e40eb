package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C7Ocommoncall.class */
final class T4C7Ocommoncall extends T4CTTIfun {
    T4C7Ocommoncall(T4CConnection _connection) {
        super(_connection, (byte) 3);
    }

    void doOLOGOFF() throws SQLException, IOException {
        setFunCode((short) 9);
        doRPC();
    }

    void doOROLLBACK() throws SQLException, IOException {
        setFunCode((short) 15);
        doRPC();
    }

    void doOCOMMIT() throws SQLException, IOException {
        setFunCode((short) 14);
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void processError() throws SQLException {
        if (this.connection.getT4CTTIoer().retCode != 2089) {
            this.connection.getT4CTTIoer().processError();
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return null;
    }
}
