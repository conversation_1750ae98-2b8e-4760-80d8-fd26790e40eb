package oracle.jdbc.driver.json.tree;

import java.time.Duration;
import java.util.Arrays;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.binary.OsonPrimitiveConversions;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.INTERVALDS;
import oracle.sql.json.OracleJsonIntervalDS;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonIntervalDSImpl.class */
public class OracleJsonIntervalDSImpl implements OracleJsonIntervalDS {
    public static final int INTERVALDS_LEN = 11;
    byte[] raw;

    public OracleJsonIntervalDSImpl(byte[] raw) {
        this.raw = raw;
    }

    public OracleJsonIntervalDSImpl(Duration d) {
        this.raw = OsonPrimitiveConversions.durationToIntervalDS(d);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.INTERVALDS;
    }

    @Override // oracle.sql.json.OracleJsonIntervalDS
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonIntervalDS)) {
            return false;
        }
        if (!(other instanceof OracleJsonIntervalDSImpl)) {
            throw new UnsupportedOperationException();
        }
        OracleJsonIntervalDSImpl otheri = (OracleJsonIntervalDSImpl) other;
        return Arrays.equals(otheri.raw, this.raw);
    }

    @Override // oracle.sql.json.OracleJsonIntervalDS
    public int hashCode() {
        return Arrays.hashCode(this.raw);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        return JsonSerializerImpl.serializeString(getString());
    }

    @Override // oracle.sql.json.OracleJsonIntervalDS
    public String getString() {
        return OsonPrimitiveConversions.serializeIntervalDS(OracleJsonExceptions.ORACLE_FACTORY, raw());
    }

    public byte[] raw() {
        return this.raw;
    }

    @Override // oracle.sql.json.OracleJsonIntervalDS
    public Duration getDuration() {
        return OsonPrimitiveConversions.intervalDSToDuration(raw());
    }

    public static String serializeDuration(Duration d, OracleJsonExceptions.ExceptionFactory f) {
        byte[] raw = OsonPrimitiveConversions.durationToIntervalDS(d);
        return OsonPrimitiveConversions.serializeIntervalDS(f, raw);
    }

    @Override // oracle.sql.json.OracleJsonIntervalDS
    public INTERVALDS getINTERVALDS() {
        return new INTERVALDS(raw());
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaIntervalDSImpl(this.raw));
        }
        return c.cast(new JsonpPrimitive.JsonpIntervalDSImpl(this.raw));
    }
}
