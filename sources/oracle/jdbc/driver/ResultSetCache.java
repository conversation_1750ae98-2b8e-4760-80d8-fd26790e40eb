package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Random;
import java.util.WeakHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Condition;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.util.RepConversion;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ResultSetCache.class */
final class ResultSetCache implements oracle.jdbc.internal.ResultSetCache, Monitor.WaitableMonitor {
    static final int COMPILE_KEY_SIZE = 16;
    static final int RUNTIME_KEY_SIZE = 16;
    private static final int CACHE_ID_SIZE = 16;
    private static final long STAT_SEND_INTERVAL = 30000;
    private final long cacheLagInMillis;
    private long lastStatSentAt;
    private T4CTTIOqcsta oqcsta;
    private static final String DUMMY_VAL = "xyzzy";
    private long visibleSCN;
    private final CacheStorage cacheStorage;
    static final /* synthetic */ boolean $assertionsDisabled;
    private final byte[] cacheId = new byte[16];
    private final byte[] cacheIdAsNibbles = new byte[32];
    private boolean isCacheIdAsNibblesReady = false;
    private long nextPingTime = 0;
    private AtomicLong invalidationCount = new AtomicLong(0);
    private AtomicLong invalidatedQueryCount = new AtomicLong(0);
    private AtomicLong validQueriesPurged = new AtomicLong(0);
    private AtomicLong invalidatedBeforeCompletion = new AtomicLong(0);
    private AtomicInteger cacheHits = new AtomicInteger(0);
    private AtomicBoolean needToSendStats = new AtomicBoolean(false);
    private long registrationId = -1;
    private ResultSetCacheState state = ResultSetCacheState.INIT;
    private WeakHashMap<OracleConnection, String> cacheRefs = null;
    private final Monitor.CloseableLock monitorLock = Monitor.newDefaultLock();
    private final Condition monitorCondition = newMonitorCondition();

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/ResultSetCache$ResultSetCacheState.class */
    enum ResultSetCacheState {
        INIT,
        STARTING,
        STARTED,
        CLOSED,
        STARTUP_FAILED
    }

    static {
        $assertionsDisabled = !ResultSetCache.class.desiredAssertionStatus();
    }

    ResultSetCache(long cacheMaxSize, int cacheLag) {
        if (!$assertionsDisabled && cacheMaxSize <= 0) {
            throw new AssertionError();
        }
        if (!$assertionsDisabled && cacheLag < 0) {
            throw new AssertionError();
        }
        new Random().nextBytes(this.cacheId);
        this.cacheStorage = new CacheStorage(cacheMaxSize);
        this.cacheLagInMillis = cacheLag;
    }

    void setState(ResultSetCacheState state) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (state != this.state) {
                    this.state = state;
                    if (state == ResultSetCacheState.STARTED) {
                        monitorNotifyAll();
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th4) {
                th = th4;
                throw th4;
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    ResultSetCacheState getState() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            ResultSetCacheState resultSetCacheState = this.state;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return resultSetCacheState;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    byte[] getCacheId() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            byte[] bArr = this.cacheId;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return bArr;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    byte[] getCacheIdAsNibbles() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (!this.isCacheIdAsNibblesReady) {
                RepConversion.bArray2Nibbles(this.cacheId, this.cacheIdAsNibbles);
                this.isCacheIdAsNibblesReady = true;
            }
            byte[] bArr = this.cacheIdAsNibbles;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return bArr;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    long getRegistrationId() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            long j = this.registrationId;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return j;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void setRegistrationId(long registrationId) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.registrationId = registrationId;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    void setOQCSTA(T4CTTIOqcsta value) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.oqcsta = value;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    T4CTTIOqcsta getOQCSTA() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            T4CTTIOqcsta t4CTTIOqcsta = this.oqcsta;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return t4CTTIOqcsta;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void setVisibleSCN(long visibleSCN) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.visibleSCN = visibleSCN;
                this.nextPingTime = System.currentTimeMillis() + this.cacheLagInMillis;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    long getVisibleSCN() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            long j = this.visibleSCN;
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return j;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    void processCommittedInvalidation(T4CTTIqcinv invalidation) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                long queryId = invalidation.kpdqcqid;
                if (queryId != 0) {
                    this.invalidationCount.incrementAndGet();
                    long invalidationScn = invalidation.kpdqcscn.getSCN();
                    if (!T4CTTIkscn.isLessThanUnsigned(invalidationScn, this.visibleSCN)) {
                        int noOfEntriesInvalidated = this.cacheStorage.removeResultsetCacheEntries(Long.valueOf(queryId));
                        this.invalidatedQueryCount.addAndGet(noOfEntriesInvalidated);
                    }
                    this.needToSendStats.set(true);
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                        return;
                    }
                    return;
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th4) {
                th = th4;
                throw th4;
            }
        } catch (Throwable th5) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
            throw th5;
        }
    }

    ResultSetCacheEntry getResultSetCacheEntry(OracleStatement stmt) throws SQLException {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            long queryId = stmt.getQueryId();
            ArrayList<Long> localInvalidations = stmt.connection.getResultSetCacheLocalInvalidations();
            if (queryId == 0 || localInvalidations.contains(Long.valueOf(queryId))) {
                return null;
            }
            byte[] compileKey = stmt.getCompileKey();
            if (compileKey == null || compileKey.length == 0) {
                ResultSetCacheEntry resultSetCacheEntry = (ResultSetCacheEntry) null;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return resultSetCacheEntry;
            }
            byte[] runtimeKey = stmt.getRuntimeKey();
            if (runtimeKey == null) {
                ResultSetCacheEntry resultSetCacheEntry2 = (ResultSetCacheEntry) null;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                        }
                    } else {
                        lock.close();
                    }
                }
                return resultSetCacheEntry2;
            }
            ResultSetCacheEntryKey key = new ResultSetCacheEntryKey(compileKey, runtimeKey);
            if (System.currentTimeMillis() > this.nextPingTime) {
                stmt.connection.pingDatabase();
            }
            ResultSetCacheEntry value = this.cacheStorage.getResultsetCacheEntry(key, queryId, stmt.connection.userName);
            if (value == null || !value.userName.equals(stmt.connection.userName)) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                return null;
            }
            if (value.isValid()) {
                this.cacheHits.incrementAndGet();
                this.needToSendStats.set(true);
                if (this.lastStatSentAt == 0) {
                    this.lastStatSentAt = System.currentTimeMillis();
                }
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            return value;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th6) {
                        th.addSuppressed(th6);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    boolean registerConnection(OracleConnection connection) {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (this.cacheRefs == null) {
                this.cacheRefs = new WeakHashMap<>();
            }
            if (this.state == ResultSetCacheState.INIT) {
                setState(ResultSetCacheState.STARTING);
                this.cacheRefs.put(connection, DUMMY_VAL);
            } else if (this.state == ResultSetCacheState.STARTING) {
                try {
                    monitorWait();
                    this.cacheRefs.put(connection, DUMMY_VAL);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return false;
                }
            } else if (this.state == ResultSetCacheState.CLOSED || this.state == ResultSetCacheState.STARTUP_FAILED) {
                return false;
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
            return true;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    boolean deregisterConnection(OracleConnection conn) {
        boolean retVal = false;
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (this.cacheRefs != null && this.cacheRefs.remove(conn) != null) {
                    if (this.cacheRefs.isEmpty()) {
                        retVal = true;
                    }
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return retVal;
            } finally {
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    boolean needToSendStatsResetIfTrue() {
        Monitor.CloseableLock lock = acquireCloseableLock();
        Throwable th = null;
        try {
            if (System.currentTimeMillis() - this.lastStatSentAt > STAT_SEND_INTERVAL && this.needToSendStats.weakCompareAndSet(true, false)) {
                this.lastStatSentAt = System.currentTimeMillis();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return true;
            }
            return false;
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public int getCacheLag() {
        return (int) this.cacheLagInMillis;
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public long getInvalidationCount() {
        return this.invalidationCount.get();
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public long getInvalidatedQueryCount() {
        return this.invalidatedQueryCount.get();
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public long getInvalidatedBeforeCompletion() {
        return this.invalidatedBeforeCompletion.get();
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public long getValidQueriesPurged() {
        return this.validQueriesPurged.get();
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public int getCacheHits() {
        return this.cacheHits.get();
    }

    void updateCurrentCacheSize(long changeInSize) {
        this.cacheStorage.incrementCacheSize(changeInSize);
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public long getCurrentCacheSize() {
        return this.cacheStorage.getCacheSize();
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public long getMaxCacheSize() {
        return this.cacheStorage.maxSize();
    }

    @Override // oracle.jdbc.internal.ResultSetCache
    public long getNumberOfCacheEntries() {
        return this.cacheStorage.size();
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/ResultSetCache$CacheStorage.class */
    private static class CacheStorage extends LinkedHashMap<ResultSetCacheEntryKey, ResultSetCacheEntry> implements Monitor {
        private static final int INITIAL_SIZE = 10;
        private static final long serialVersionUID = 1;
        private long currentCacheSize;
        private final long maxCacheSize;
        private final Map<Long, LinkedList<ResultSetCacheEntry>> queryIdIndex;
        private final Monitor.CloseableLock monitorLock;

        CacheStorage(long maxCacheSize) {
            super(10);
            this.monitorLock = Monitor.newDefaultLock();
            this.queryIdIndex = new HashMap(10);
            this.maxCacheSize = maxCacheSize;
        }

        ResultSetCacheEntry getResultsetCacheEntry(ResultSetCacheEntryKey key, long queryId, String userName) {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                try {
                    ResultSetCacheEntry entry = (ResultSetCacheEntry) super.get(key);
                    if (entry == null) {
                        entry = new ResultSetCacheEntry(key, queryId);
                        entry.userName = userName;
                        super.put(key, entry);
                        this.currentCacheSize += entry.getSizeInMemory();
                        this.queryIdIndex.computeIfAbsent(Long.valueOf(queryId), k -> {
                            return new LinkedList();
                        }).add(entry);
                    }
                    ResultSetCacheEntry resultSetCacheEntry = entry;
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return resultSetCacheEntry;
                } finally {
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }

        int removeResultsetCacheEntries(Long queryId) {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                try {
                    int invalidateCount = 0;
                    LinkedList<ResultSetCacheEntry> cacheEntriesList = this.queryIdIndex.remove(queryId);
                    if (cacheEntriesList != null) {
                        Iterator<ResultSetCacheEntry> iterator = cacheEntriesList.iterator();
                        while (iterator.hasNext()) {
                            ResultSetCacheEntry cacheEntry = iterator.next();
                            if (remove(cacheEntry.getResultSetCacheEntryKey()) != null) {
                                this.currentCacheSize -= cacheEntry.getSizeInMemory();
                            }
                            cacheEntry.invalidate();
                            invalidateCount++;
                        }
                    }
                    int i = invalidateCount;
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return i;
                } finally {
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }

        void incrementCacheSize(long changeInSize) {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                try {
                    this.currentCacheSize += changeInSize;
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                                return;
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                                return;
                            }
                        }
                        lock.close();
                    }
                } catch (Throwable th3) {
                    th = th3;
                    throw th3;
                }
            } catch (Throwable th4) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th5) {
                            th.addSuppressed(th5);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th4;
            }
        }

        long getCacheSize() {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                long j = this.currentCacheSize;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return j;
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }

        long maxSize() {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                long j = this.maxCacheSize;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return j;
            } catch (Throwable th3) {
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }

        @Override // java.util.LinkedHashMap
        protected boolean removeEldestEntry(Map.Entry<ResultSetCacheEntryKey, ResultSetCacheEntry> eldest) {
            Monitor.CloseableLock lock = acquireCloseableLock();
            Throwable th = null;
            try {
                try {
                    boolean hasExceededMaxSize = this.currentCacheSize >= this.maxCacheSize;
                    if (hasExceededMaxSize) {
                        ResultSetCacheEntry eldestValue = eldest.getValue();
                        this.currentCacheSize = (-1) * eldestValue.getSizeInMemory();
                        LinkedList<ResultSetCacheEntry> entries = this.queryIdIndex.get(Long.valueOf(eldestValue.getQueryId()));
                        entries.remove(eldestValue);
                    }
                    if (lock != null) {
                        if (0 != 0) {
                            try {
                                lock.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            lock.close();
                        }
                    }
                    return hasExceededMaxSize;
                } finally {
                }
            } catch (Throwable th3) {
                if (lock != null) {
                    if (th != null) {
                        try {
                            lock.close();
                        } catch (Throwable th4) {
                            th.addSuppressed(th4);
                        }
                    } else {
                        lock.close();
                    }
                }
                throw th3;
            }
        }

        @Override // oracle.jdbc.internal.Monitor
        public final Monitor.CloseableLock getMonitorLock() {
            return this.monitorLock;
        }
    }

    @Override // oracle.jdbc.internal.Monitor
    public final Monitor.CloseableLock getMonitorLock() {
        return this.monitorLock;
    }

    @Override // oracle.jdbc.internal.Monitor.WaitableMonitor
    public final Condition getMonitorCondition() {
        return this.monitorCondition;
    }
}
