package oracle.jdbc.driver.json.binary;

import oracle.jdbc.driver.json.JakartaExceptionFactory;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JakartaOsonContext.class */
public class JakartaOsonContext extends OsonContext {
    public JakartaOsonContext(OsonBuffer buffer) {
        this(buffer, new OsonHeader(buffer, JakartaExceptionFactory.INSTANCE));
    }

    public JakartaOsonContext(OsonContext other) {
        this(other.getBuffer(), other.getHeader());
    }

    public JakartaOsonContext(OsonBuffer buffer, OsonHeader header) {
        super(buffer, header, JakartaOsonValueFactory.INSTANCE, JakartaExceptionFactory.INSTANCE);
    }
}
