package oracle.jdbc.driver.json.binary;

import oracle.jdbc.driver.json.JsonpExceptionFactory;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JsonpOsonContext.class */
public class JsonpOsonContext extends OsonContext {
    public JsonpOsonContext(OsonBuffer buffer) {
        this(buffer, new OsonHeader(buffer, JsonpExceptionFactory.INSTANCE));
    }

    public JsonpOsonContext(OsonContext other) {
        this(other.getBuffer(), other.getHeader());
    }

    public JsonpOsonContext(OsonBuffer buffer, OsonHeader header) {
        super(buffer, header, JsonpOsonValueFactory.INSTANCE, JsonpExceptionFactory.INSTANCE);
    }
}
