package oracle.jdbc.dcn;

import java.util.EnumSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/dcn/TableChangeDescription.class */
public interface TableChangeDescription {
    EnumSet<TableOperation> getTableOperations();

    String getTableName();

    int getObjectNumber();

    RowChangeDescription[] getRowChangeDescription();

    /* loaded from: ojdbc8.jar:oracle/jdbc/dcn/TableChangeDescription$TableOperation.class */
    public enum TableOperation {
        ALL_ROWS(1),
        INSERT(2),
        UPDATE(4),
        DELETE(8),
        ALTER(16),
        DROP(32);

        private final int code;

        TableOperation(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }

        public static final EnumSet<TableOperation> getTableOperations(int opcode) {
            EnumSet<TableOperation> ret = EnumSet.noneOf(TableOperation.class);
            if ((opcode & ALL_ROWS.getCode()) != 0) {
                ret.add(ALL_ROWS);
            }
            if ((opcode & INSERT.getCode()) != 0) {
                ret.add(INSERT);
            }
            if ((opcode & UPDATE.getCode()) != 0) {
                ret.add(UPDATE);
            }
            if ((opcode & DELETE.getCode()) != 0) {
                ret.add(DELETE);
            }
            if ((opcode & ALTER.getCode()) != 0) {
                ret.add(ALTER);
            }
            if ((opcode & DROP.getCode()) != 0) {
                ret.add(DROP);
            }
            return ret;
        }
    }
}
