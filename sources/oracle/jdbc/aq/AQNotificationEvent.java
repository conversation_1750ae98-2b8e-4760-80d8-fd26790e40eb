package oracle.jdbc.aq;

import java.sql.SQLException;
import java.util.EventObject;

/* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQNotificationEvent.class */
public abstract class AQNotificationEvent extends EventObject {
    public abstract AQMessageProperties getMessageProperties() throws SQLException;

    public abstract String getRegistration() throws SQLException;

    public abstract byte[] getPayload() throws SQLException;

    public abstract String getQueueName() throws SQLException;

    public abstract byte[] getMessageId() throws SQLException;

    public abstract String getConsumerName() throws SQLException;

    public abstract String getConnectionInformation();

    public abstract EventType getEventType();

    public abstract AdditionalEventType getAdditionalEventType();

    @Override // java.util.EventObject
    public abstract String toString();

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQNotificationEvent$EventType.class */
    public enum EventType {
        REGULAR(0),
        DEREG(1);

        private final int code;

        EventType(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQNotificationEvent$AdditionalEventType.class */
    public enum AdditionalEventType {
        NONE(0),
        TIMEOUT(1),
        GROUPING(2);

        private final int code;

        AdditionalEventType(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }

        public static final AdditionalEventType getEventType(int code) {
            if (code == TIMEOUT.getCode()) {
                return TIMEOUT;
            }
            if (code == GROUPING.getCode()) {
                return GROUPING;
            }
            return NONE;
        }
    }

    protected AQNotificationEvent(Object source) {
        super(source);
    }
}
