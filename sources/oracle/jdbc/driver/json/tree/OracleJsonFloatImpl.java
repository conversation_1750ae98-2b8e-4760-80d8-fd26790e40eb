package oracle.jdbc.driver.json.tree;

import java.math.BigDecimal;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.BINARY_FLOAT;
import oracle.sql.json.OracleJsonFloat;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonFloatImpl.class */
public class OracleJsonFloatImpl extends OracleJsonNumberImpl implements OracleJsonFloat {
    private static String NAN = "\"Nan\"";
    float value;

    public OracleJsonFloatImpl(float value) {
        this.value = value;
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.FLOAT;
    }

    @Override // oracle.sql.json.OracleJsonFloat
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonFloat)) {
            return false;
        }
        OracleJsonFloat otherd = (OracleJsonFloat) other;
        return Float.floatToIntBits(this.value) == Float.floatToIntBits(otherd.floatValue());
    }

    @Override // oracle.sql.json.OracleJsonFloat
    public int hashCode() {
        return Float.hashCode(this.value);
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public BigDecimal bigDecimalValue() {
        return BigDecimal.valueOf(this.value);
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public float floatValue() {
        return this.value;
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
    public String getString() {
        if (this.value == Float.POSITIVE_INFINITY) {
            return OracleJsonDecimalImpl.POSITIVE_INF;
        }
        if (this.value == Float.NEGATIVE_INFINITY) {
            return OracleJsonDecimalImpl.NEGATIVE_INF;
        }
        if (Float.isNaN(this.value)) {
            return NAN;
        }
        return Float.toString(this.value);
    }

    @Override // oracle.sql.json.OracleJsonFloat
    public BINARY_FLOAT getFLOAT() {
        return new BINARY_FLOAT(this.value);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaFloatImpl(this.value));
        }
        return c.cast(new JsonpPrimitive.JsonpFloatImpl(this.value));
    }

    @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl, oracle.sql.json.OracleJsonValue
    public String toString() {
        if (this.value == Float.POSITIVE_INFINITY) {
            return OracleJsonDecimalImpl.POSITIVE_INF;
        }
        if (this.value == Float.NEGATIVE_INFINITY) {
            return OracleJsonDecimalImpl.NEGATIVE_INF;
        }
        if (Float.isNaN(this.value)) {
            return NAN;
        }
        return BigDecimal.valueOf(this.value).toString();
    }
}
