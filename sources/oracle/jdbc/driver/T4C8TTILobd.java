package oracle.jdbc.driver;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.SecurityLabel;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4C8TTILobd.class */
class T4C8TTILobd extends T4CTTIMsg {
    static final int LOBD_STATE0 = 0;
    static final int LOBD_STATE1 = 1;
    static final int LOBD_STATE2 = 2;
    static final int LOBD_STATE3 = 3;
    static final int LOBD_STATE_EXIT = 4;
    static final short TTCG_LNG = 254;
    static final short LOBDATALENGTH = 252;
    private static final String CLASS_NAME = T4C8TTILobd.class.getName();
    static byte[] ucs2Char = new byte[2];

    T4C8TTILobd(T4CConnection _conn) {
        super(_conn, (byte) 14);
    }

    void marshalLobData(byte[] inBuffer, long inBufferOffset, long numBytes, boolean useZeroCopyIO) throws IOException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshalLobData", "inBufferOffset={0}, numBytes={1}, useZeroCopyIO={2}", (String) null, (Throwable) null, Long.valueOf(inBufferOffset), Long.valueOf(numBytes), Boolean.valueOf(useZeroCopyIO));
        marshalTTCcode();
        if (useZeroCopyIO) {
            this.meg.writeZeroCopyIO(inBuffer, (int) inBufferOffset, (int) numBytes);
        } else {
            this.meg.marshalCLR(inBuffer, (int) inBufferOffset, (int) numBytes);
        }
    }

    void marshalClobUB2_For9iDB(byte[] inBuffer, long inBufferOffset, long numChars) throws IOException {
        long numUB2 = numChars;
        boolean ttcgLong = false;
        marshalTTCcode();
        if (numUB2 > 84) {
            ttcgLong = true;
            this.meg.marshalUB1((short) 254);
        }
        long count = 0;
        while (numUB2 > 84) {
            this.meg.marshalUB1((short) 252);
            for (int j = 0; j < 84; j++) {
                this.meg.marshalUB1((short) 2);
                this.meg.marshalB1Array(inBuffer, (int) (inBufferOffset + (count * 168) + (j * 2)), 2);
            }
            count++;
            numUB2 -= 84;
        }
        if (numUB2 > 0) {
            long length = numUB2 * 3;
            this.meg.marshalUB1((short) length);
            for (int j2 = 0; j2 < numUB2; j2++) {
                this.meg.marshalUB1((short) 2);
                this.meg.marshalB1Array(inBuffer, (int) (inBufferOffset + (count * 168) + (j2 * 2)), 2);
            }
        }
        if (ttcgLong) {
            this.meg.marshalUB1((short) 0);
        }
    }

    long unmarshalLobData(byte[] outBuffer, int offsetInOutBuffer, boolean useZeroCopyIO) throws SQLException, IOException {
        int bytesRead;
        if (useZeroCopyIO) {
            int nbOfBytesReadSoFar = 0;
            int[] nbOfByteRead = new int[1];
            boolean isMarked = false;
            while (!isMarked) {
                try {
                    isMarked = this.meg.readZeroCopyIO(outBuffer, offsetInOutBuffer + nbOfBytesReadSoFar, nbOfByteRead);
                    nbOfBytesReadSoFar += nbOfByteRead[0];
                } catch (SocketTimeoutException toe) {
                    this.connection.doAsynchronousClose();
                    throw toe;
                }
            }
            bytesRead = nbOfBytesReadSoFar;
        } else {
            int[] readBytes = new int[1];
            this.meg.unmarshalCLR(outBuffer, offsetInOutBuffer, readBytes);
            bytesRead = readBytes[0];
        }
        return bytesRead;
    }

    long unmarshalClobUB2_For9iDB(byte[] outBuffer, int offsetInOutBuffer) throws SQLException, IOException {
        long bytesRead = 0;
        long offset = offsetInOutBuffer;
        short length = 0;
        int state = 0;
        while (state != 4) {
            switch (state) {
                case 0:
                    length = this.meg.unmarshalUB1();
                    if (length == 254) {
                        state = 2;
                        break;
                    } else {
                        state = 1;
                        break;
                    }
                case 1:
                    int i = 0;
                    while (i < length) {
                        int numBytes = this.meg.unmarshalUCS2(outBuffer, offset);
                        i += numBytes;
                        offset += 2;
                        bytesRead += 2;
                    }
                    state = 4;
                    break;
                case 2:
                    length = this.meg.unmarshalUB1();
                    if (length > 0) {
                        state = 3;
                        break;
                    } else {
                        state = 4;
                        break;
                    }
                case 3:
                    int i2 = 0;
                    while (i2 < length) {
                        int numBytes2 = this.meg.unmarshalUCS2(outBuffer, offset);
                        i2 += numBytes2;
                        offset += 2;
                        bytesRead += 2;
                    }
                    state = 2;
                    break;
            }
        }
        return bytesRead;
    }
}
