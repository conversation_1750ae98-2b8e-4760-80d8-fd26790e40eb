package oracle.jdbc.driver.json.tree;

import java.time.LocalDateTime;
import java.util.Arrays;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.binary.OsonPrimitiveConversions;
import oracle.jdbc.driver.json.tree.JakartaPrimitive;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.DATE;
import oracle.sql.json.OracleJsonDate;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonDateImpl.class */
public class OracleJsonDateImpl implements OracleJsonDate {
    byte[] raw;

    public OracleJsonDateImpl(byte[] raw) {
        this.raw = raw;
    }

    public OracleJsonDateImpl(LocalDateTime i) {
        this.raw = OsonPrimitiveConversions.toOracleDate(getExceptionFactory(), i);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.DATE;
    }

    @Override // oracle.sql.json.OracleJsonDate
    public boolean equals(Object other) {
        if (!(other instanceof OracleJsonDate)) {
            return false;
        }
        if (!(other instanceof OracleJsonDateImpl)) {
            throw new UnsupportedOperationException();
        }
        OracleJsonDateImpl otherd = (OracleJsonDateImpl) other;
        return Arrays.equals(otherd.raw, this.raw);
    }

    @Override // oracle.sql.json.OracleJsonDate
    public int hashCode() {
        return Arrays.hashCode(this.raw);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        return JsonSerializerImpl.serializeString(getString());
    }

    @Override // oracle.sql.json.OracleJsonDate
    public LocalDateTime getLocalDateTime() {
        byte[] raw = raw();
        return OsonPrimitiveConversions.dateToLocalDateTime(getExceptionFactory(), raw);
    }

    @Override // oracle.sql.json.OracleJsonDate
    public String getString() {
        return OsonPrimitiveConversions.dateToString(getExceptionFactory(), this.raw);
    }

    public byte[] raw() {
        return this.raw;
    }

    @Override // oracle.sql.json.OracleJsonDate
    public DATE getDATE() {
        return new DATE(raw());
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaPrimitive.JakartaDateImpl(this.raw));
        }
        return c.cast(new JsonpPrimitive.JsonpDateImpl(this.raw));
    }

    public OracleJsonExceptions.ExceptionFactory getExceptionFactory() {
        return OracleJsonExceptions.ORACLE_FACTORY;
    }
}
