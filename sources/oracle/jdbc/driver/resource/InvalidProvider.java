package oracle.jdbc.driver.resource;

import java.sql.SQLException;
import oracle.jdbc.spi.OracleResourceProvider;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/resource/InvalidProvider.class */
final class InvalidProvider<T extends OracleResourceProvider, U> implements ResourceProvider<T, U> {
    private final SQLException sqlException;

    InvalidProvider(SQLException sqlException) {
        this.sqlException = sqlException;
    }

    @Override // oracle.jdbc.driver.resource.ResourceProvider
    public U getResource() throws SQLException {
        throw ((SQLException) this.sqlException.fillInStackTrace());
    }
}
