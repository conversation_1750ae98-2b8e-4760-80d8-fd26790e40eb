package oracle.jdbc.driver;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import oracle.sql.Datum;

/* compiled from: OraclePreparedStatement.java */
/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TimestampBinder.class */
class TimestampBinder extends DateCommonBinder {
    Binder theTimestampCopyingBinder = null;
    Timestamp paramVal;

    static void init(Binder x, int _scale) {
        x.type = (short) 180;
        x.bytelen = 11;
        x.scale = (short) _scale;
    }

    TimestampBinder(Timestamp x) {
        init(this, -1);
        this.paramVal = x;
    }

    TimestampBinder(Timestamp x, int _scale) {
        init(this, _scale);
        this.paramVal = x;
    }

    @Override // oracle.jdbc.driver.Binder
    Binder copyingBinder() {
        if (this.theTimestampCopyingBinder == null) {
            this.theTimestampCopyingBinder = new TimestampCopyingBinder();
        }
        return this.theTimestampCopyingBinder;
    }

    @Override // oracle.jdbc.driver.Binder
    long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
        int offset;
        Timestamp value = this.paramVal;
        byte[] b = null;
        int len = 0;
        if (clearPriorBindValues) {
            this.paramVal = null;
        }
        if (value == null) {
            bindIndicators[indoffset] = -1;
            if (bindUseDBA) {
                bindDataOffsets[bindDataIndex] = -1;
                bindDataLengths[bindDataIndex] = 0;
            }
        } else {
            if (bindUseDBA) {
                long pos = bindData.getPosition();
                bindDataOffsets[bindDataIndex] = pos;
                stmt.lastBoundDataOffsets[bindPosition] = pos;
                b = stmt.connection.methodTempLittleByteBuffer;
                offset = 0;
            } else {
                b = bindBytes;
                offset = byteoffset;
            }
            bindIndicators[indoffset] = 0;
            len = getDatumBytes(stmt, value, b, offset, bindUseDBA, bytePitch);
        }
        if (bindUseDBA) {
            bindData.put(b, 0, len);
            bindIndicators[indoffset] = 0;
            bindDataLengths[bindDataIndex] = len;
            stmt.lastBoundDataLengths[bindPosition] = len;
            bindIndicators[lenoffset] = (short) len;
        } else {
            bindIndicators[indoffset] = 0;
            bindIndicators[lenoffset] = (short) len;
        }
        if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
            if (bindIndicators[indoffset] == -1) {
                localCheckSum = CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
            } else {
                localCheckSum = CRC64.updateChecksum(localCheckSum, value.getTime());
            }
        }
        return localCheckSum;
    }

    private int getDatumBytes(OraclePreparedStatement stmt, Timestamp value, byte[] b, int offset, boolean bindUseDBA, int bytePitch) throws SQLException {
        int len;
        setOracleHMS(setOracleCYMD(value.getTime(), b, offset, stmt), b, offset);
        int nanos = value.getNanos();
        if (nanos != 0) {
            setOracleNanos(nanos, b, offset);
            len = bindUseDBA ? 11 : bytePitch;
        } else {
            len = 7;
        }
        return len;
    }

    @Override // oracle.jdbc.driver.Binder
    Datum getDatum(OraclePreparedStatement stmt, int bindPosition, int formOfUse, int internalType) throws SQLException {
        byte[] b = stmt.connection.methodTempLittleByteBuffer;
        int len = getDatumBytes(stmt, this.paramVal, b, 0, true, 0);
        return SQLUtil.makeDatum(stmt.connection, Arrays.copyOf(b, len), internalType, (String) null, 0);
    }
}
