package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_el.class */
public class ErrorMessagesJson_el extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Παρουσιάστηκε εξαίρεση i/o"}, new Object[]{"26302", "Το έτος \"{0}\" δεν υποστηρίζεται"}, new Object[]{"26303", "Υπερχείλιση, η τιμή είναι υπερβολικά μεγάλη: {0}."}, new Object[]{"26304", "Μη υποστηριζόμενη επιλογή (δεν υλοποιήθηκε)."}, new Object[]{"26305", "Το δυαδικό JSON δεν είναι έγκυρο ή είναι κατεστραμμένο."}, new Object[]{"26306", "Μη υποστηριζόμενη έκδοση δυαδικού JSON: {0}."}, new Object[]{"26307", "Το μήκος κλειδιού με κωδικοποίηση UTF-8 δεν πρέπει να υπερβαίνει τα 256 byte. Το παρακάτω κλειδί υπερβαίνει αυτό το όριο: \"{0}\"."}, new Object[]{"26308", "Το καθορισμένο JSON είναι πολύ μεγάλο για να κωδικοποιηθεί ως δυαδικό JSON. Το μέγεθος των κωδικοποιημένων εικόνων δεν πρέπει να υπερβαίνει τα 2 GB."}, new Object[]{"26309", "Το δυαδικό JSON είναι μη έγκυρο ή κατεστραμμένο. Η καθορισμένη εικόνα περιέχει μόνο {0} byte."}, new Object[]{"26310", "Το καθορισμένο java.time.Period έχει καθορισμένες ημέρες, αλλά το διάστημα Oracle έτους μέχρι μήνα δεν υποστηρίζει ημέρες."}, new Object[]{"26311", "Ο παράγοντας δημιουργίας έκλεισε πριν από τη λήξη."}, new Object[]{"26312", "Ένα κλειδί αντικειμένου πρέπει να καθορίζεται σε αυτό το περιβάλλον."}, new Object[]{"26313", "Μη έγκυρη εγγραφή. Μια πλήρης τιμή έχει ήδη εγγραφεί."}, new Object[]{"26314", "Η λήξη δεν επιτρέπεται σε αυτό το περιβάλλον."}, new Object[]{"26315", "Το κλειδί δεν επιτρέπεται σε αυτό το περιβάλλον."}, new Object[]{"26316", "Αναμενόμενη τιμή μετά το κλειδί."}, new Object[]{"26317", "Η κατάσταση αναλυτή πρέπει να είναι {0}."}, new Object[]{"26318", "Η κατάσταση αναλυτή δεν πρέπει να είναι {0}."}, new Object[]{"26319", "Ο αναλυτής πρέπει να βρίσκεται σε τιμή."}, new Object[]{"26320", "Το \"{0}\" δεν είναι υποστηριζόμενος τύπος wrapper."}, new Object[]{"26321", "Αυτό το αντικείμενο δεν μπορεί να τροποποιηθεί. Για να δημιουργήσετε ένα τροποποιήσιμο αντίγραφο, χρησιμοποιήστε το OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Αυτή η διάταξη δεν μπορεί να τροποποιηθεί. Για να δημιουργήσετε ένα τροποποιήσιμο αντίγραφο, χρησιμοποιήστε το OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "Το αντικείμενο JSON περιέχει διπλότυπο κλειδί: {0}."}, new Object[]{"26324", "Δεν είναι δυνατός ο αυτόματος εντοπισμός κωδικοποίησης. Οι χαρακτήρες δεν επαρκούν."}, new Object[]{"26325", "Αναμενόταν διακριτικό EOF, αλλά ελήφθη {0}."}, new Object[]{"26326", "Μη αναμενόμενος χαρακτήρας {0} στη γραμμή {1}, στήλη {2}."}, new Object[]{"26327", "Μη αναμενόμενος χαρακτήρας {0} στη γραμμή {1}, στήλη {2}. Αναμενόταν: {3}."}, new Object[]{"26328", "Μη αναμενόμενο διακριτικό {0} στη γραμμή {1}, στήλη {2}. Τα αναμενόμενα διακριτικά είναι: {3}."}, new Object[]{"26329", "Το JsonParser#getString() είναι έγκυρο μόνο σε καταστάσεις αναλυτή KEY_NAME, VALUE_STRING, VALUE_NUMBER. Αλλά η τρέχουσα κατάσταση αναλυτή είναι {0}."}, new Object[]{"26330", "Το JsonParser#isIntegralNumber() είναι έγκυρο μόνο σε καταστάσεις αναλυτή VALUE_NUMBER. Αλλά η τρέχουσα κατάσταση αναλυτή είναι {0}."}, new Object[]{"26331", "Το JsonParser#getInt() είναι έγκυρο μόνο σε καταστάσεις αναλυτή VALUE_NUMBER. Αλλά η τρέχουσα κατάσταση αναλυτή είναι {0}."}, new Object[]{"26332", "Το JsonParser#getLong() είναι έγκυρο μόνο σε καταστάσεις αναλυτή VALUE_NUMBER. Αλλά η τρέχουσα κατάσταση αναλυτή είναι {0}."}, new Object[]{"26333", "Το JsonParser#getBigDecimal() είναι έγκυρο μόνο σε καταστάσεις αναλυτή VALUE_NUMBER. Αλλά η τρέχουσα κατάσταση αναλυτή είναι {0}."}, new Object[]{"26334", "Το JsonParser#getArray() είναι έγκυρο μόνο για την κατάσταση της λειτουργίας ανάλυσης START_ARRAY. Ωστόσο, η κατάσταση της λειτουργίας ανάλυσης είναι {0}."}, new Object[]{"26335", "Το JsonParser#getObject() είναι έγκυρο μόνο σε καταστάσεις αναλυτή START_OBJECT. Αλλά η τρέχουσα κατάσταση αναλυτή είναι {0}."}, new Object[]{"26336", "Δεν υποστηρίζεται χρονική ένδειξη με μια περιοχή. Υποστηρίζονται μόνο χρονικές ζώνες μετατόπισης."}, new Object[]{"26337", "Τα αντικείμενα και οι διατάξεις στην τιμή JSON ενδέχεται να μην είναι ένθετα σε περισσότερα από {0} επίπεδα"}, new Object[]{"26338", "Τα κλειδιά ενός αντικειμένου JSON δεν μπορούν να υπερβαίνουν τα 65.535 byte"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
