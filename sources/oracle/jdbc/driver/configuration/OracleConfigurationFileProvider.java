package oracle.jdbc.driver.configuration;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.sql.SQLException;
import oracle.jdbc.util.OracleConfigurationCache;
import oracle.net.resolver.EnvVariableResolver;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/configuration/OracleConfigurationFileProvider.class */
public class OracleConfigurationFileProvider extends OracleConfigurationParsableProvider {
    private static final OracleConfigurationCache CACHE = OracleConfigurationCache.create(100);

    @Override // oracle.jdbc.spi.OracleConfigurationCachableProvider
    public OracleConfigurationCache getCache() {
        return CACHE;
    }

    @Override // oracle.jdbc.driver.configuration.OracleConfigurationParsableProvider
    public InputStream getInputStream(String fileName) throws SQLException {
        try {
            if (fileName.startsWith("/")) {
                return new FileInputStream(fileName);
            }
            InputStream configFile = getClass().getClassLoader().getResourceAsStream(fileName);
            if (configFile != null) {
                return configFile;
            }
            if (this.options.containsKey(EnvVariableResolver.TNS_ADMIN)) {
                return new FileInputStream(this.options.get(EnvVariableResolver.TNS_ADMIN) + File.separator + fileName);
            }
            if (System.getProperty(EnvVariableResolver.TNS_ADMIN) != null) {
                return new FileInputStream(System.getProperty(EnvVariableResolver.TNS_ADMIN) + File.separator + fileName);
            }
            if (System.getenv(EnvVariableResolver.TNS_ADMIN) != null) {
                return new FileInputStream(System.getenv(EnvVariableResolver.TNS_ADMIN) + File.separator + fileName);
            }
            throw new SQLException("Json configuration file not found.");
        } catch (FileNotFoundException ex) {
            throw new SQLException("Error retrieving Json configuration", ex);
        }
    }

    @Override // oracle.jdbc.spi.OracleConfigurationProvider
    public String getType() {
        return "file";
    }
}
