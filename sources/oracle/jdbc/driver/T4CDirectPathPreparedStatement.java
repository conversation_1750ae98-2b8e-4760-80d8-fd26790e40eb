package oracle.jdbc.driver;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.NClob;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Properties;
import java.util.function.Supplier;
import oracle.jdbc.driver.DirectPathBufferMarshaler;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.sql.BLOB;
import oracle.sql.CLOB;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CDirectPathPreparedStatement.class */
class T4CDirectPathPreparedStatement extends T4CPreparedStatement {
    private int directPathCursor;
    static final int DPPSTMT_STATUS_UNKNOWN = 0;
    static final int DPPSTMT_STATUS_PREPARED = 1;
    static final int DPPSTMT_STATUS_LOAD_STREAM = 2;
    static final int DPPSTMT_STATUS_FINISH = 3;
    static final int DPPSTMT_STATUS_ABORT = 4;
    static final int DPPSTMT_STATUS_CLOSED = 5;
    private int directPathStatus;
    private final String schemaName;
    private final String tableName;
    private final String[] colNames;
    private final String partitionName;
    Properties dpStmtProps;
    private int rowInError;
    private boolean codePointCountingEnabled;
    private int[] maxCodePointCounts;
    private static final int DTYBRI_SIZE = 10;
    private static final String IS_DTYBRI_QUERY = "SELECT COUNT(*) FROM SYS.ALL_TAB_COLUMNS WHERE OWNER = ? AND TABLE_NAME = ? AND COLUMN_NAME = ? AND DATA_TYPE='ROWID'";
    private static final int QUERY_BIND_POS_SCHEMA = 1;
    private static final int QUERY_BIND_POS_TABLE = 2;
    private static final int QUERY_BIND_POS_COLUMN = 3;
    private int sdbaOfBits;
    private int sdbaBits;
    private int dbabBits;
    static final /* synthetic */ boolean $assertionsDisabled;

    static {
        $assertionsDisabled = !T4CDirectPathPreparedStatement.class.desiredAssertionStatus();
    }

    T4CDirectPathPreparedStatement(PhysicalConnection connection, String schemaName, String tableName, String[] colNames, String partitionName, OracleResultSet.ResultSetType resultSetType, @Blind(PropertiesBlinder.class) Properties dpStmtProps, String sql) throws SQLException {
        super(connection, sql, resultSetType);
        if (!this.bindUseDBA) {
            throw new IllegalStateException("Dynamic byte array storage of bind values must be enabled for direct path loads. (The oracle.jdbc.bindUseDBA connection property cannot be false)");
        }
        this.schemaName = schemaName;
        this.tableName = tableName;
        this.colNames = colNames;
        this.partitionName = partitionName;
        this.dpStmtProps = dpStmtProps;
        this.directPathStatus = 0;
        this.rowInError = 0;
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.OraclePreparedStatement
    public void registerReturnParameter(int paramIndex, int externalType) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.OraclePreparedStatement
    public void registerReturnParameter(int paramIndex, int externalType, int maxSize) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.OraclePreparedStatement
    public void registerReturnParameter(int paramIndex, int externalType, String typeName) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.OraclePreparedStatement
    public ResultSet getReturnResultSet() throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public ResultSet executeQuery() throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public ResultSetMetaData getMetaData() throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public int executeUpdate(String sql) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public boolean execute(String sql) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public ResultSet getResultSet() throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public int executeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public int executeUpdate(String sql, int[] columnIndexes) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public int executeUpdate(String sql, String[] columnNames) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public boolean execute(String sql, int autoGeneratedKeys) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public boolean execute(String sql, int[] columnIndexes) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public boolean execute(String sql, String[] columnNames) throws SQLException {
        throw ((SQLException) DatabaseError.createUnsupportedFeatureSqlException().fillInStackTrace());
    }

    static String getSQLStatement(String schemaName, String tableName, String[] colNames, String partitionName, PhysicalConnection conn) throws SQLException {
        StringBuffer sql = new StringBuffer();
        sql.append("INSERT INTO ");
        if (schemaName != null && schemaName.length() != 0) {
            sql.append(conn.enquoteIdentifier(schemaName, true));
            sql.append(oracle.jdbc.OracleConnection.CLIENT_INFO_KEY_SEPARATOR);
        }
        sql.append(conn.enquoteIdentifier(tableName, true));
        sql.append("(");
        boolean firstCol = true;
        for (String colName : colNames) {
            if (!firstCol) {
                sql.append(",");
            } else {
                firstCol = false;
            }
            sql.append(conn.enquoteIdentifier(colName, true));
        }
        sql.append(")");
        sql.append(" VALUES ");
        sql.append("(");
        for (int iCol = 0; iCol < colNames.length; iCol++) {
            if (iCol != 0) {
                sql.append(",");
            }
            sql.append("?");
        }
        sql.append(")");
        if (partitionName != null) {
            sql.append("/* Partition Name:");
            sql.append(partitionName);
            sql.append(" */");
        }
        return sql.toString();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public boolean execute() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            checkForDirectPathReprepare();
            boolean zExecute = super.execute();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return zExecute;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    public long executeLargeUpdate() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            checkForDirectPathReprepare();
            long jExecuteLargeUpdate = super.executeLargeUpdate();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return jExecuteLargeUpdate;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement
    public int[] executeBatch() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            checkForDirectPathReprepare();
            int[] iArrExecuteBatch = super.executeBatch();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return iArrExecuteBatch;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.driver.OracleStatement
    public long[] executeLargeBatch() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            checkForDirectPathReprepare();
            long[] jArrExecuteLargeBatch = super.executeLargeBatch();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    lock.close();
                }
            }
            return jArrExecuteLargeBatch;
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.T4CPreparedStatement, oracle.jdbc.driver.OraclePreparedStatement
    protected void doBindValueConversion(int bindersOffset) throws SQLException {
        doBindValueConversion(bindersOffset, this.numberOfBoundRows);
    }

    @Override // oracle.jdbc.driver.T4CPreparedStatement, oracle.jdbc.driver.OraclePreparedStatement
    protected void reallocBinds(int new_number_of_bind_rows_allocated) throws SQLException {
        allocBinds(new_number_of_bind_rows_allocated);
    }

    @Override // oracle.jdbc.driver.T4CPreparedStatement, oracle.jdbc.driver.OraclePreparedStatement
    protected int getAllocBindsRowCount() {
        return this.numberOfBindRowsAllocated;
    }

    @Override // oracle.jdbc.driver.T4CPreparedStatement
    void doOall8(T4C8Oall all8, boolean doParse, boolean doExecute, boolean doFetch, boolean doDescribe, boolean doDefine) throws SQLException, IOException {
        int number_of_bound_rows = 0;
        int number_of_bind_positions = 0;
        int[] errorOffsets = new int[2];
        if (this.bindIndicators != null) {
            number_of_bound_rows = ((this.bindIndicators[this.bindIndicatorSubRange + 3] & 65535) << 16) + (this.bindIndicators[this.bindIndicatorSubRange + 4] & 65535);
            number_of_bind_positions = this.bindIndicators[this.bindIndicatorSubRange + 0] & 65535;
        }
        this.rowInError = 0;
        validateBindLengths();
        validateStreamBindLengths();
        DirectPathBufferMarshaler.BufferPlanner bufferPlanner = DirectPathBufferMarshaler.createBufferPlanner(number_of_bound_rows, number_of_bind_positions, this.bindData, this.bindDataOffsets, this.bindDataLengths, this.parameterStream, this.accessors, this.t4Connection);
        try {
            this.t4Connection.directPathLoadStream(bufferPlanner, this.directPathCursor, errorOffsets);
            setDirectPathStatus(2);
        } catch (SQLException sqe) {
            this.rowInError = bufferPlanner.getRowByOffset(errorOffsets[0], errorOffsets[1]);
            throw sqe;
        }
    }

    @Override // oracle.jdbc.driver.OracleStatement, java.sql.Statement, java.lang.AutoCloseable
    public void close() throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            if (isDirectPathUncommitted()) {
                this.t4Connection.directPathAbort();
            }
            super.close();
            setDirectPathStatus(5);
            this.t4Connection.clearDirectPathState();
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.driver.OracleStatement, oracle.jdbc.OracleStatement
    public void closeWithKey(String key) throws SQLException {
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                if (isDirectPathUncommitted()) {
                    this.t4Connection.directPathAbort();
                }
                super.closeWithKey(key);
                setDirectPathStatus(5);
                this.t4Connection.clearDirectPathState();
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setBlob(int paramIndex, Blob lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setBlob(int paramIndex, InputStream lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setBlob(int paramIndex, InputStream lob, long length) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.OraclePreparedStatement
    public void setBLOB(int paramIndex, BLOB lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.OraclePreparedStatement
    public void setBytesForBlob(int paramIndex, byte[] lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setClob(int paramIndex, Clob lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setClob(int paramIndex, Reader lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setClob(int paramIndex, Reader lob, long length) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setNClob(int paramIndex, NClob lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setNClob(int paramIndex, Reader lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setNClob(int paramIndex, Reader lob, long length) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.OraclePreparedStatement
    public void setCLOB(int paramIndex, CLOB lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, oracle.jdbc.OraclePreparedStatement
    public void setStringForClob(int paramIndex, String lob) throws SQLException {
        throw lobBindsNotSupported();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setNull(int paramIndex, int sqlType, String typeName) throws SQLException {
        requireSupportedType(sqlType);
        super.setNull(paramIndex, sqlType);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement, java.sql.PreparedStatement
    public void setNull(int paramIndex, int sqlType) throws SQLException {
        requireSupportedType(sqlType);
        super.setNull(paramIndex, sqlType);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void setObjectCritical(int paramIndex, Object x, int targetSqlType, int scale) throws SQLException {
        requireSupportedType(targetSqlType);
        super.setObjectCritical(paramIndex, x, targetSqlType, scale);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void setBinaryStreamContentsForBlobCritical(int paramIndex, InputStream inputStream, long length, boolean isLengthSpecified) throws SQLException {
        bindInputStream(paramIndex, isLengthSpecified ? LengthLimiter.limitInputStream(inputStream, length) : inputStream);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void setReaderContentsForClobCritical(int paramIndex, Reader reader, long length, boolean isLengthSpecified) throws SQLException {
        int index = paramIndex - 1;
        bindInputStream(paramIndex, this.connection.conversion.ConvertStream(isLengthSpecified ? LengthLimiter.limitReader(reader, length) : reader, getConversionCodeForCharacterStream(index), 0, this.currentRowFormOfUse[index], this.connection));
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void setStringForClobCritical(int paramIndex, String string) throws SQLException {
        setReaderContentsForClobCritical(paramIndex, new StringReader(string), 0L, false);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void setAsciiStreamContentsForClobCritical(int paramIndex, InputStream asciiStream, long length, boolean isLengthSpecified) throws SQLException {
        setReaderContentsForClobCritical(paramIndex, new InputStreamReader(isLengthSpecified ? LengthLimiter.limitInputStream(asciiStream, length) : asciiStream, StandardCharsets.US_ASCII), length, false);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void basicBindAsciiStream(int paramIndex, InputStream asciiStream, int length) throws SQLException {
        setAsciiStreamContentsForClobCritical(paramIndex, asciiStream, length, length != 0);
    }

    private void bindInputStream(int paramIndex, InputStream inputStream) {
        int index = paramIndex - 1;
        Monitor.CloseableLock lock = this.connection.acquireCloseableLock();
        Throwable th = null;
        try {
            try {
                this.currentRowBinders[index] = this.theLongRawStreamBinder;
                if (this.parameterStream == null) {
                    this.parameterStream = new InputStream[this.numberOfBindRowsAllocated][this.numberOfBindPositions];
                }
                this.parameterStream[this.currentRank][index] = inputStream;
                this.currentRowByteLens[index] = 0;
                this.currentRowCharLens[index] = 0;
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            return;
                        }
                    }
                    lock.close();
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (Throwable th4) {
            if (lock != null) {
                if (th != null) {
                    try {
                        lock.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    lock.close();
                }
            }
            throw th4;
        }
    }

    private void requireSupportedType(int sqlType) throws SQLException {
        switch (sqlType) {
            case oracle.jdbc.OracleTypes.BLOB /* 2004 */:
            case oracle.jdbc.OracleTypes.CLOB /* 2005 */:
            case oracle.jdbc.OracleTypes.NCLOB /* 2011 */:
                throw lobBindsNotSupported();
            default:
                return;
        }
    }

    void setDirectPathCursor(int directPathCursor) {
        this.directPathCursor = directPathCursor;
    }

    int getDirectPathCursor() {
        return this.directPathCursor;
    }

    int getDirectPathStatus() {
        return this.directPathStatus;
    }

    boolean isDirectPathUncommitted() {
        return this.directPathStatus == 1 || this.directPathStatus == 2;
    }

    boolean isDirectPathCommitted() {
        return this.directPathStatus == 3 || this.directPathStatus == 4;
    }

    boolean isDirectPathClosed() {
        return this.directPathStatus == 5;
    }

    void setDirectPathStatus(int directPathStatus) {
        this.directPathStatus = directPathStatus;
    }

    void checkForDirectPathReprepare() throws SQLException {
        try {
            if (isDirectPathCommitted()) {
                this.t4Connection.odpp.doODPP(this.schemaName, this.tableName, this.colNames, this.partitionName, this.dpStmtProps);
                int directPathCursor = (int) this.t4Connection.odpp.getO4Value(3);
                setDirectPathCursor(directPathCursor);
                setDirectPathStatus(1);
            }
        } catch (IOException ex) {
            ((T4CConnection) this.connection).handleIOException(ex);
            throw ((SQLException) DatabaseError.createSqlException(ex).fillInStackTrace());
        }
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    protected String getBatchUpdateErrorMessage() {
        if (this.rowInError > 0) {
            return " Row number " + this.rowInError + " causes batch load failure.";
        }
        return super.getBatchUpdateErrorMessage();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void setupBindBuffers(int firstRow, int rowCount) throws SQLException {
        updateCodePointCounts(firstRow, rowCount);
        super.setupBindBuffers(firstRow, rowCount);
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    final int getConversionCodeForCharacterStream(int index) {
        return bindRequiresUTF16(index) ? 14 : 16;
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    void adjustCharLensForSetCHAR(int index, byte[] b) {
        this.currentRowCharLens[index] = 0;
        this.currentRowByteLens[index] = b.length;
    }

    void updateAccessors(Accessor[] describedAccessors) throws SQLException {
        if (!$assertionsDisabled && describedAccessors == null) {
            throw new AssertionError("describedAccessors is null");
        }
        this.accessors = describedAccessors;
        initCodePointCounting();
        initRowIDAccessors();
    }

    @Override // oracle.jdbc.driver.OraclePreparedStatement
    final CharacterSet getCharacterSetForBind(int index, short formOfUse) {
        DBConversion conversion = this.connection.conversion;
        if (bindRequiresUTF16(index)) {
            return CharacterSet.make(2000);
        }
        return 2 == this.accessors[index].describeFormOfUse ? conversion.serverNCharSet : conversion.serverCharSet;
    }

    @Override // oracle.jdbc.driver.T4CPreparedStatement, oracle.jdbc.driver.OraclePreparedStatement
    protected Binder createRowidBinder(byte[] rowidBytes) throws SQLException {
        if (rowidBytes == null || rowidBytes.length == 0) {
            return createRowidNullBinder();
        }
        return new DirectPathRowIDBinder(rowidBytes);
    }

    private boolean bindRequiresUTF16(int pos) {
        Accessor acc = this.accessors[pos];
        DBConversion conversion = this.connection.conversion;
        if (112 != acc.describeType) {
            return false;
        }
        return 2 == acc.describeFormOfUse ? !conversion.isServerNCharSetFixedWidth : conversion.isServerCSMultiByte && !conversion.isServerCharSetFixedWidth;
    }

    private boolean isCharacterSetFixedWidth(int pos) {
        Accessor acc = this.accessors[pos];
        DBConversion conversion = this.connection.conversion;
        if (bindRequiresUTF16(pos)) {
            return true;
        }
        if (2 == acc.describeFormOfUse) {
            return conversion.isServerNCharSetFixedWidth;
        }
        return !conversion.isServerCSMultiByte || conversion.isServerCharSetFixedWidth;
    }

    private final void initCodePointCounting() {
        this.codePointCountingEnabled = false;
        int i = 0;
        while (true) {
            if (i < this.accessors.length) {
                if (!this.accessors[i].isLengthSemanticChar() || isCharacterSetFixedWidth(i)) {
                    i++;
                } else {
                    this.codePointCountingEnabled = true;
                    break;
                }
            } else {
                break;
            }
        }
        if (this.codePointCountingEnabled) {
            this.maxCodePointCounts = new int[this.numberOfBindPositions];
        } else {
            this.maxCodePointCounts = null;
        }
    }

    private void updateCodePointCounts(int rowOffset, int rowCount) {
        if (this.codePointCountingEnabled) {
            int endRow = rowOffset + rowCount;
            for (int pos = 0; pos < this.numberOfBindPositions; pos++) {
                int max = 0;
                int row = rowOffset;
                while (true) {
                    if (row >= endRow) {
                        break;
                    }
                    String bindVal = getStringBinderVal(row, pos);
                    if (bindVal == null) {
                        max = Integer.MAX_VALUE;
                        break;
                    }
                    int count = bindVal.codePointCount(0, bindVal.length());
                    if (count > max) {
                        max = count;
                    }
                    row++;
                }
                this.maxCodePointCounts[pos] = max;
            }
        }
    }

    private int getMaxCodePointCount(int pos) {
        if (this.codePointCountingEnabled) {
            return this.maxCodePointCounts[pos];
        }
        return Integer.MAX_VALUE;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:14:0x0065  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void validateBindLengths() throws java.sql.SQLException {
        /*
            r8 = this;
            r0 = 0
            r9 = r0
        L2:
            r0 = r9
            r1 = r8
            int r1 = r1.numberOfBindPositions
            if (r0 >= r1) goto Laf
            r0 = r8
            oracle.jdbc.driver.Accessor[] r0 = r0.accessors
            r1 = r9
            r0 = r0[r1]
            r10 = r0
            r0 = r10
            int r0 = r0.describeType
            switch(r0) {
                case 8: goto L48;
                case 24: goto L55;
                case 112: goto L62;
                case 113: goto L62;
                case 119: goto L62;
                default: goto L65;
            }
        L48:
            r0 = r8
            int r0 = r0.maxRawBytesSql
            r1 = 2147483647(0x7fffffff, float:NaN)
            if (r0 <= r1) goto La9
            goto L65
        L55:
            r0 = r8
            int r0 = r0.maxRawBytesSql
            r1 = 2147483647(0x7fffffff, float:NaN)
            if (r0 <= r1) goto La9
            goto L65
        L62:
            goto La9
        L65:
            r0 = r10
            int r0 = r0.describeMaxLength
            r11 = r0
            r0 = r10
            boolean r0 = r0.isLengthSemanticChar()
            if (r0 == 0) goto L79
            r0 = r8
            r1 = r9
            boolean r0 = r0.isCharacterSetFixedWidth(r1)
            if (r0 == 0) goto L82
        L79:
            r0 = r8
            r1 = r9
            r2 = r11
            r0.validateByteLengths(r1, r2)
            goto La9
        L82:
            r0 = r10
            int r0 = r0.describeMaxLengthChars
            r12 = r0
            r0 = r12
            r1 = r8
            r2 = r9
            int r1 = r1.getMaxCodePointCount(r2)
            if (r0 < r1) goto L9b
            r0 = r8
            r1 = r9
            r2 = r11
            r0.validateByteLengths(r1, r2)
            goto La9
        L9b:
            r0 = r8
            r1 = r9
            r2 = r11
            r3 = r12
            r4 = r8
            r5 = r9
            r6 = 0
            oracle.sql.CharacterSet r4 = r4.getCharacterSetForBind(r5, r6)
            r0.validateByteAndCodePointLengths(r1, r2, r3, r4)
        La9:
            int r9 = r9 + 1
            goto L2
        Laf:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.T4CDirectPathPreparedStatement.validateBindLengths():void");
    }

    private void validateByteLengths(int pos, int max) throws SQLException {
        int nBinds = this.numberOfBindPositions * this.numberOfBoundRows;
        int i = pos;
        while (true) {
            int index = i;
            if (index < nBinds) {
                int nBytes = this.bindDataLengths[index];
                if (nBytes <= max) {
                    i = index + this.numberOfBindPositions;
                } else {
                    throw newBindLengthException((index / this.numberOfBindPositions) + 1, pos + 1, nBytes, max, "BYTE");
                }
            } else {
                return;
            }
        }
    }

    private void validateStreamBindLengths() {
        int maxLength;
        if (this.parameterStream == null) {
            return;
        }
        for (int row = 0; row < this.parameterStream.length; row++) {
            InputStream[] parameterRow = this.parameterStream[row];
            if (parameterRow != null) {
                for (int pos = 0; pos < parameterRow.length; pos++) {
                    InputStream parameter = parameterRow[pos];
                    if (parameter != null && (maxLength = getMaxByteLength(this.accessors[pos])) >= 1) {
                        int messageRow = row + 1;
                        int messagePos = row + 1;
                        parameterRow[pos] = LengthLimiter.limitInputStream(parameter, maxLength, () -> {
                            return new StreamLengthException(createBindLengthMessage(messageRow, messagePos, maxLength + 1, maxLength, "BYTE"));
                        });
                    }
                }
            }
        }
    }

    private static int getMaxByteLength(Accessor accessor) {
        switch (accessor.describeType) {
            case 8:
                return Integer.MAX_VALUE;
            case 24:
                return Integer.MAX_VALUE;
            case 112:
            case 113:
                return -1;
            default:
                return accessor.describeMaxLength;
        }
    }

    private void validateByteAndCodePointLengths(int pos, int maxBytes, int maxChars, CharacterSet encoder) throws SQLException {
        int nBinds = this.numberOfBindPositions * this.numberOfBoundRows;
        int i = pos;
        while (true) {
            int index = i;
            if (index < nBinds) {
                int nBytes = this.bindDataLengths[index];
                if (nBytes > maxBytes) {
                    throw newBindLengthException((index / this.numberOfBindPositions) + 1, pos + 1, nBytes, maxBytes, "BYTE");
                }
                if (nBytes > maxChars) {
                    long offset = this.bindDataOffsets[index];
                    int nCodePoints = getCodePointCount(offset, nBytes, encoder);
                    if (nCodePoints > maxChars) {
                        throw newBindLengthException((index / this.numberOfBindPositions) + 1, pos + 1, nCodePoints, maxChars, "CHAR");
                    }
                }
                i = index + this.numberOfBindPositions;
            } else {
                return;
            }
        }
    }

    private int getCodePointCount(long offset, int nBytes, CharacterSet encoder) {
        String decoded;
        switch (encoder.getOracleId()) {
            case 871:
                return getUTF8CodePointCount(offset, nBytes);
            case 873:
                return getAL32UTF8CodePointCount(offset, nBytes);
            default:
                try {
                    decoded = this.bindData.getString(offset, nBytes, encoder);
                } catch (SQLException e) {
                    byte[] encoded = this.bindData.get(offset, nBytes);
                    decoded = encoder.toStringWithReplacement(encoded, 0, encoded.length);
                }
                return decoded.codePointCount(0, decoded.length());
        }
    }

    private int getAL32UTF8CodePointCount(long offset, int nBytes) {
        byte[] buf = this.connection.getByteBuffer(nBytes);
        this.bindData.get(offset, buf, 0, nBytes);
        int nCodePoints = 0;
        int pos = 0;
        while (pos < nBytes) {
            nCodePoints++;
            byte leadingByte = buf[pos];
            if (0 != (128 & leadingByte)) {
                if (192 == (224 & leadingByte)) {
                    pos++;
                } else if (224 == (240 & leadingByte)) {
                    pos += 2;
                } else if (240 == (248 & leadingByte)) {
                    pos += 3;
                } else if (!$assertionsDisabled) {
                    throw new AssertionError("Detected invalid AL32UTF8 code point at buffer position " + (offset + pos) + " with a leading byte of: 0x" + Integer.toHexString(Byte.toUnsignedInt(leadingByte)));
                }
            }
            pos++;
        }
        this.connection.cacheBuffer(buf);
        return nCodePoints;
    }

    private int getUTF8CodePointCount(long offset, int nBytes) {
        byte[] buf = this.connection.getByteBuffer(nBytes);
        this.bindData.get(offset, buf, 0, nBytes);
        int nCodePoints = 0;
        int pos = 0;
        while (pos < nBytes) {
            nCodePoints++;
            byte leadingByte = buf[pos];
            if (0 != (128 & leadingByte)) {
                if (192 == (224 & leadingByte)) {
                    pos++;
                } else if (237 == (255 & leadingByte)) {
                    if (pos + 1 < nBytes) {
                        int next4Bits = 15 & (buf[pos + 1] >> 2);
                        if (8 == next4Bits) {
                            nCodePoints--;
                        }
                        pos += 2;
                    }
                } else if (224 == (240 & leadingByte)) {
                    pos += 2;
                } else if (!$assertionsDisabled) {
                    throw new AssertionError("Detected invalid UTF8 code point at buffer position " + (offset + pos) + " with a leading byte of: 0x" + Integer.toHexString(Byte.toUnsignedInt(leadingByte)));
                }
            }
            pos++;
        }
        this.connection.cacheBuffer(buf);
        return nCodePoints;
    }

    private static SQLException newBindLengthException(int row, int pos, int length, int max, String semantic) {
        return DatabaseError.createSqlException(72, createBindLengthMessage(row, pos, length, max, semantic));
    }

    private static String createBindLengthMessage(int row, int pos, int length, int max, String semantic) {
        return "Maximum Length: " + max + " " + semantic + ". Bind at row " + row + ", position " + pos + ": " + length + " " + semantic;
    }

    void setSDBAOfBits(int sdbaOfBits) {
        this.sdbaOfBits = sdbaOfBits;
    }

    void setSDBABits(int sdbaBits) {
        this.sdbaBits = sdbaBits;
    }

    void setDBABBits(int dbabBits) {
        this.dbabBits = dbabBits;
    }

    private void initRowIDAccessors() throws SQLException {
        for (Accessor acc : this.accessors) {
            if (acc.describeType == 208 && acc.describeMaxLength == 10 && columnIsRowID(acc.columnName)) {
                acc.describeType = 104;
            }
        }
    }

    private boolean columnIsRowID(String columnName) throws SQLException {
        PreparedStatement ps = this.t4Connection.prepareStatement(IS_DTYBRI_QUERY);
        Throwable th = null;
        try {
            try {
                ps.setString(1, this.schemaName.toUpperCase());
                ps.setString(2, this.tableName.toUpperCase());
                ps.setString(3, columnName.toUpperCase());
                ResultSet rs = ps.executeQuery();
                rs.next();
                boolean z = 0 != rs.getInt(1);
                if (ps != null) {
                    if (0 != 0) {
                        try {
                            ps.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        ps.close();
                    }
                }
                return z;
            } finally {
            }
        } catch (Throwable th3) {
            if (ps != null) {
                if (th != null) {
                    try {
                        ps.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    ps.close();
                }
            }
            throw th3;
        }
    }

    private SQLException lobBindsNotSupported() {
        return (SQLException) DatabaseError.createSqlException(getConnectionDuringExceptionHandling(), 4, "BLOB, CLOB, and NCLOB bind values are not supported with Direct Path").fillInStackTrace();
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CDirectPathPreparedStatement$DirectPathRowIDBinder.class */
    private class DirectPathRowIDBinder extends Binder {
        private Binder copyingBinder;
        private final long[] riddef;
        static final /* synthetic */ boolean $assertionsDisabled;

        static {
            $assertionsDisabled = !T4CDirectPathPreparedStatement.class.desiredAssertionStatus();
        }

        private DirectPathRowIDBinder(byte[] rowIDChars) throws SQLException, NumberFormatException {
            long[] jArrStringToRowid;
            if (T4CRowidAccessor.isRestricted(rowIDChars)) {
                jArrStringToRowid = T4CRowidAccessor.rcToRowid(rowIDChars, 0, rowIDChars.length);
            } else {
                jArrStringToRowid = T4CRowidAccessor.stringToRowid(rowIDChars, 0, rowIDChars.length);
            }
            this.riddef = jArrStringToRowid;
            this.type = (short) 104;
            this.bytelen = 10;
        }

        @Override // oracle.jdbc.driver.Binder
        Binder copyingBinder() {
            if (this.copyingBinder == null) {
                this.copyingBinder = new ByteCopyingBinder() { // from class: oracle.jdbc.driver.T4CDirectPathPreparedStatement.DirectPathRowIDBinder.1
                };
                this.copyingBinder.type = this.type;
                this.copyingBinder.bytelen = this.bytelen;
            }
            return this.copyingBinder;
        }

        @Override // oracle.jdbc.driver.Binder
        long bind(OraclePreparedStatement stmt, int bindPosition, int rankInBuffer, int rank, byte[] bindBytes, char[] bindChars, short[] bindIndicators, int bytePitch, int charPitch, int byteoffset, int charoffset, int lenoffset, int indoffset, boolean clearPriorBindValues, long localCheckSum, ByteArray bindData, long[] bindDataOffsets, int[] bindDataLengths, int bindDataIndex, boolean bindUseDBA, int formOfUse) throws SQLException {
            byte[] bArrRowidToDTYBURI;
            if (!$assertionsDisabled && !bindUseDBA) {
                throw new AssertionError("bindUseDBA is false");
            }
            long pos = bindData.getPosition();
            bindDataOffsets[bindDataIndex] = pos;
            stmt.lastBoundDataOffsets[bindPosition] = pos;
            if (104 == T4CDirectPathPreparedStatement.this.accessors[bindPosition].describeType) {
                bArrRowidToDTYBURI = T4CRowidAccessor.rowidToDTYBRI(this.riddef, T4CDirectPathPreparedStatement.this.sdbaOfBits, T4CDirectPathPreparedStatement.this.sdbaBits, T4CDirectPathPreparedStatement.this.dbabBits);
            } else {
                bArrRowidToDTYBURI = T4CRowidAccessor.rowidToDTYBURI(this.riddef);
            }
            byte[] encoding = bArrRowidToDTYBURI;
            bindData.put(encoding);
            int len = encoding.length;
            bindDataLengths[bindDataIndex] = len;
            stmt.lastBoundDataLengths[bindPosition] = len;
            bindIndicators[lenoffset] = (short) len;
            if (stmt.connection.checksumMode.needToCalculateBindChecksum()) {
                if (bindIndicators[indoffset] == -1) {
                    return CRC64.updateChecksum(localCheckSum, Accessor.NULL_DATA_BYTES, 0, Accessor.NULL_DATA_BYTES.length);
                }
                return CRC64.updateChecksum(localCheckSum, encoding, 0, len);
            }
            return localCheckSum;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CDirectPathPreparedStatement$LengthLimiter.class */
    private static class LengthLimiter {
        final long limit;
        long count = 0;

        LengthLimiter(long limit) {
            this.limit = limit;
        }

        long remaining() {
            return this.limit - this.count;
        }

        int readOne(int readResult) {
            if (readResult != -1) {
                this.count++;
            }
            return readResult;
        }

        int readMany(int readResult) {
            if (readResult != -1) {
                this.count += readResult;
            }
            return readResult;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static InputStream limitInputStream(InputStream delegate, long limit) {
            return limitInputStream(delegate, limit, null);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static InputStream limitInputStream(final InputStream delegate, final long limit, final Supplier<StreamLengthException> errorSupplier) {
            return new InputStream() { // from class: oracle.jdbc.driver.T4CDirectPathPreparedStatement.LengthLimiter.1
                LengthLimiter limiter;

                {
                    this.limiter = new LengthLimiter(limit);
                }

                @Override // java.io.InputStream
                public int read() throws IOException {
                    if (this.limiter.remaining() > 0) {
                        return this.limiter.readOne(delegate.read());
                    }
                    return terminate();
                }

                @Override // java.io.InputStream
                public int read(byte[] dst) throws IOException {
                    return read(dst, 0, dst.length);
                }

                @Override // java.io.InputStream
                public int read(byte[] dst, int offset, int length) throws IOException {
                    long remaining = this.limiter.remaining();
                    if (remaining > 0) {
                        return this.limiter.readMany(delegate.read(dst, offset, (int) Math.min(length, remaining)));
                    }
                    return terminate();
                }

                @Override // java.io.InputStream
                public int available() throws IOException {
                    return delegate.available();
                }

                @Override // java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
                public void close() throws IOException {
                    delegate.close();
                }

                int terminate() throws IOException {
                    if (errorSupplier != null && delegate.read() != -1) {
                        throw ((StreamLengthException) errorSupplier.get());
                    }
                    return -1;
                }
            };
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static Reader limitReader(Reader delegate, long limit) {
            return limitReader(delegate, limit, null);
        }

        private static Reader limitReader(final Reader delegate, final long limit, final Supplier<StreamLengthException> errorSupplier) {
            return new Reader() { // from class: oracle.jdbc.driver.T4CDirectPathPreparedStatement.LengthLimiter.2
                LengthLimiter limiter;

                {
                    this.limiter = new LengthLimiter(limit);
                }

                @Override // java.io.Reader
                public int read() throws IOException {
                    if (this.limiter.remaining() > 0) {
                        return this.limiter.readOne(delegate.read());
                    }
                    return terminate();
                }

                @Override // java.io.Reader
                public int read(char[] dst) throws IOException {
                    return read(dst, 0, dst.length);
                }

                @Override // java.io.Reader
                public int read(char[] dst, int offset, int length) throws IOException {
                    long remaining = this.limiter.remaining();
                    if (remaining > 0) {
                        return this.limiter.readMany(delegate.read(dst, offset, (int) Math.min(length, remaining)));
                    }
                    return terminate();
                }

                @Override // java.io.Reader
                public boolean ready() throws IOException {
                    return delegate.ready();
                }

                @Override // java.io.Reader, java.io.Closeable, java.lang.AutoCloseable
                public void close() throws IOException {
                    delegate.close();
                }

                int terminate() throws IOException {
                    if (errorSupplier != null && delegate.read() != -1) {
                        throw ((StreamLengthException) errorSupplier.get());
                    }
                    return -1;
                }
            };
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CDirectPathPreparedStatement$StreamLengthException.class */
    static class StreamLengthException extends IOException {
        StreamLengthException(String message) {
            super(message);
        }
    }
}
