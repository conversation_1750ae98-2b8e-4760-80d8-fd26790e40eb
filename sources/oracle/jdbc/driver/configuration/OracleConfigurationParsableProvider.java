package oracle.jdbc.driver.configuration;

import java.io.InputStream;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.spi.OracleConfigurationCachableProvider;
import oracle.jdbc.spi.OracleConfigurationParser;
import oracle.jdbc.spi.OracleConfigurationProvider;
import oracle.jdbc.util.OracleConfigurationCache;
import oracle.jdbc.util.OracleConfigurationProviderNetworkError;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/configuration/OracleConfigurationParsableProvider.class */
public abstract class OracleConfigurationParsableProvider implements OracleConfigurationCachableProvider {
    private static final String CONFIG_TTL_JSON_OBJECT_NAME = "config_time_to_live";
    private final long msTimeout = 60000;
    private final long msRefreshInterval = 60000;
    protected static final OracleConfigurationCache CACHE = OracleConfigurationCache.create(100);
    protected Map<String, String> options;

    public abstract InputStream getInputStream(String str) throws SQLException;

    @Override // oracle.jdbc.spi.OracleConfigurationProvider
    public Properties getConnectionProperties(String location) throws SQLException, NumberFormatException {
        Properties cachedProp = CACHE.get(location);
        if (Objects.nonNull(cachedProp)) {
            return cachedProp;
        }
        Properties properties = retrieveProperties(location);
        if (properties.containsKey(CONFIG_TTL_JSON_OBJECT_NAME)) {
            long configTimeToLive = Long.parseLong(properties.getProperty(CONFIG_TTL_JSON_OBJECT_NAME));
            properties.remove(CONFIG_TTL_JSON_OBJECT_NAME);
            CACHE.put(location, properties, configTimeToLive, () -> {
                return refreshProperties(location);
            }, 60000L, 60000L);
        } else {
            CACHE.put(location, properties, () -> {
                return refreshProperties(location);
            }, 60000L, 60000L);
        }
        return properties;
    }

    private Properties retrieveProperties(String location) throws SQLException {
        String[] params = location.split("\\?");
        if (!(this instanceof OracleConfigurationHttpsProvider)) {
            location = params[0];
        }
        if (params.length > 1) {
            this.options = OracleConfigurationProvider.mapOptions(params[1]);
        } else {
            this.options = Collections.emptyMap();
        }
        InputStream inputStream = getInputStream(location);
        String parserType = getParserType(location);
        OracleConfigurationParser parser = OracleConfigurationParser.find(parserType);
        return parser.parse(inputStream, this.options);
    }

    private Properties refreshProperties(String location) throws OracleConfigurationProviderNetworkError {
        try {
            return retrieveProperties(location);
        } catch (SQLException e) {
            throw new OracleConfigurationProviderNetworkError(e);
        }
    }

    public String getParserType(String location) {
        String parser = this.options.get("parser");
        if (parser != null) {
            return parser;
        }
        String[] params = location.split("\\?");
        return params[0].substring(location.lastIndexOf(OracleConnection.CLIENT_INFO_KEY_SEPARATOR) + 1);
    }
}
