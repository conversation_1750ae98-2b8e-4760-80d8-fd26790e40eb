package oracle.jdbc.driver.json.binary;

import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.ref.WeakReference;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.Arrays;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.tree.OracleJsonDateImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDecimalImpl;
import oracle.jdbc.driver.json.tree.OracleJsonIntervalDSImpl;
import oracle.jdbc.driver.json.tree.OracleJsonIntervalYMImpl;
import oracle.jdbc.driver.json.tree.OracleJsonTimestampImpl;
import oracle.jdbc.driver.json.tree.OracleJsonTimestampTZImpl;
import oracle.jdbc.driver.json.tree.OracleJsonVectorImpl;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonDate;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonDouble;
import oracle.sql.json.OracleJsonFloat;
import oracle.sql.json.OracleJsonGenerator;
import oracle.sql.json.OracleJsonIntervalDS;
import oracle.sql.json.OracleJsonIntervalYM;
import oracle.sql.json.OracleJsonParser;
import oracle.sql.json.OracleJsonString;
import oracle.sql.json.OracleJsonTimestamp;
import oracle.sql.json.OracleJsonTimestampTZ;
import oracle.sql.json.OracleJsonVector;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonGeneratorImpl.class */
public final class OsonGeneratorImpl extends AbstractGenerator implements OracleJsonGenerator {
    private static final DuplicateKeyMode DEFAULT_DUPLICATE_KEY_MODE;
    private OsonGeneratorState state;
    private static boolean DEFAULT_SIMPLE_VALUE_SHARING = "true".equals(System.getProperty("oracle.jdbc.driver.json.binary.DEFAULT_SIMPLE_VALUE_SHARING", "false"));
    private static boolean DEFAULT_LAST_VALUE_SHARING = "true".equals(System.getProperty("oracle.jdbc.driver.json.binary.DEFAULT_LAST_VALUE_SHARING", "false"));
    private static boolean DEFAULT_RELATIVE_OFFSETS = "true".equals(System.getProperty("oracle.jdbc.driver.json.binary.DEFAULT_RELATIVE_OFFSETS", "false"));
    private static boolean DEFAULT_TINYNODE = "true".equals(System.getProperty("oracle.jdbc.driver.json.binary.DEFAULT_TINYNODE", "true"));
    private static int INITIAL_OPS = 64;
    private static int OUT_BUFFER_SIZE = 8192;
    private static int SEEN_HASH_THRESHOLD = 64;
    private static byte[] ONE = OsonPrimitiveConversions.toNumber(1);
    private static byte[] ZERO = OsonPrimitiveConversions.toNumber(0);

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonGeneratorImpl$DuplicateKeyMode.class */
    public enum DuplicateKeyMode {
        ALLOW,
        DISALLOW
    }

    static {
        DuplicateKeyMode mode;
        String modeStr = System.getProperty("oracle.jdbc.driver.json.binary.OsonGeneratorImpl.DEFAULT_DUPLICATE_KEY_MODE");
        if (modeStr == null) {
            mode = DuplicateKeyMode.DISALLOW;
        } else {
            mode = DuplicateKeyMode.valueOf(modeStr);
        }
        DEFAULT_DUPLICATE_KEY_MODE = mode;
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonGeneratorImpl$OsonGeneratorState.class */
    private static final class OsonGeneratorState {
        private int[][] keys;
        private int[][] keysLastSeenValue;
        private boolean keysNeedReset;
        int[] seenHash;
        int seenHashSize;
        int keyI;
        int keyJ;
        private String[] distinctKeys;
        private int distinctKeysSize;
        private byte[] keyHeap;
        private int keyHeapSize;
        private int[] keyHeapOffsets;
        private int[] fidMap;
        TreeMap<BigKey, Integer> bigKeys;
        int bigKeysHeapSize;
        AtomicInteger keylen;
        private int numOps;
        private short[] ops;
        private int[] nextSiblings;
        private int[] fieldIDs;
        private short[] depths;
        private int[] valueIndex;
        private int[] numChildren;
        private int[] offsets;
        private int treeSegmentSize;
        private byte[] valueHeap;
        private int valueHeapSize;
        private int tinyNodeCount;
        short headerFlags;
        private int[] opStack;
        private int depth;
        private int previousSiblingIdx;
        private int[] temporaryIntArray;
        private long[] temporaryLongArray;
        private final StreamContext ctx;
        private OutputStream out;
        private byte[] outBuffer;
        private int outBufferPos;
        public boolean relativeOffsets;
        public boolean simpleValueSharing;
        public boolean lastValueSharing;
        int opNull;
        int opTrue;
        int opFalse;
        int opZero;
        int opOne;
        int opEmptyString;
        int opEmptyObject;
        int opEmptyArray;
        int opLastValue;
        private OsonGeneratorStatePool pool;
        private DuplicateKeyMode duplicateKeyMode;

        /* JADX WARN: Type inference failed for: r1v1, types: [int[], int[][]] */
        private OsonGeneratorState(OsonGeneratorStatePool pool, OutputStream out) {
            this.keys = new int[256];
            this.keysNeedReset = true;
            this.seenHash = new int[OsonGeneratorImpl.SEEN_HASH_THRESHOLD];
            this.distinctKeys = new String[16];
            this.bigKeysHeapSize = 0;
            this.keylen = new AtomicInteger();
            this.ops = new short[OsonGeneratorImpl.INITIAL_OPS];
            this.nextSiblings = new int[OsonGeneratorImpl.INITIAL_OPS];
            this.fieldIDs = new int[OsonGeneratorImpl.INITIAL_OPS];
            this.depths = new short[OsonGeneratorImpl.INITIAL_OPS];
            this.valueIndex = new int[OsonGeneratorImpl.INITIAL_OPS];
            this.numChildren = new int[OsonGeneratorImpl.INITIAL_OPS];
            this.valueHeap = new byte[1024];
            this.opStack = new int[2];
            this.ctx = new StreamContext(null);
            this.outBuffer = new byte[OsonGeneratorImpl.OUT_BUFFER_SIZE];
            this.duplicateKeyMode = OsonGeneratorImpl.DEFAULT_DUPLICATE_KEY_MODE;
            this.pool = pool;
            this.out = out;
            this.ctx.setExceptionFactory(getExceptionFactory());
        }

        private void writeNumber(byte[] bytes) {
            if (bytes.length > 8) {
                if (bytes.length < 256) {
                    addOpAndValue(52, bytes);
                    return;
                }
                return;
            }
            int op = (bytes.length - 1) | OsonConstants.MASK_ORANUM_16;
            if (this.simpleValueSharing) {
                if (!Arrays.equals(OsonGeneratorImpl.ONE, bytes)) {
                    if (Arrays.equals(OsonGeneratorImpl.ZERO, bytes)) {
                        addOpAndValueNoPostOp(op, bytes);
                        if (this.opZero == -1) {
                            this.opZero = this.numOps - 1;
                        } else {
                            this.headerFlags = (short) (this.headerFlags | 32);
                            markDuplicate(this.numOps - 1, this.opZero);
                        }
                        postOp(false);
                        return;
                    }
                } else {
                    addOpAndValueNoPostOp(op, bytes);
                    if (this.opOne == -1) {
                        this.opOne = this.numOps - 1;
                    } else {
                        this.headerFlags = (short) (this.headerFlags | 32);
                        markDuplicate(this.numOps - 1, this.opOne);
                    }
                    postOp(false);
                    return;
                }
            }
            addOpAndValue(op, bytes);
        }

        private void push(int opIndex) {
            if (this.ctx.depth >= this.opStack.length) {
                this.opStack = Arrays.copyOf(this.opStack, this.opStack.length * 2);
            }
            this.opStack[this.depth] = opIndex;
            this.depth++;
            if (this.depth >= 65536) {
                throw OracleJsonExceptions.NEST_DEPTH_EXCEEDED.create(getExceptionFactory(), 65536);
            }
            this.previousSiblingIdx = -1;
        }

        private void addOp(int op) {
            int thisOp = this.numOps;
            this.numOps = thisOp + 1;
            this.ops[thisOp] = (short) op;
            this.depths[thisOp] = (short) this.depth;
            if (this.previousSiblingIdx != -1) {
                this.nextSiblings[this.previousSiblingIdx] = thisOp;
            }
            this.nextSiblings[thisOp] = -1;
            if (this.depth > 0) {
                int[] iArr = this.numChildren;
                int i = this.opStack[this.depth - 1];
                iArr[i] = iArr[i] + 1;
            }
            this.previousSiblingIdx = thisOp;
        }

        private void expandOp() {
            int l = this.ops.length * 2;
            this.ops = Arrays.copyOf(this.ops, l);
            this.nextSiblings = Arrays.copyOf(this.nextSiblings, l);
            this.fieldIDs = Arrays.copyOf(this.fieldIDs, l);
            this.depths = Arrays.copyOf(this.depths, l);
            this.numChildren = Arrays.copyOf(this.numChildren, l);
            this.valueIndex = Arrays.copyOf(this.valueIndex, l);
        }

        private void preOp() {
            if (this.numOps >= this.ops.length) {
                expandOp();
            }
            this.numChildren[this.numOps] = 0;
        }

        private void postOp(boolean shareable) {
            if (this.lastValueSharing && this.keyI != -1 && this.keyJ != -1) {
                initKeysLastSeenValue(this.keyI);
                int idx = this.numOps - 1;
                if (this.numChildren[idx] >= 0 && shareable) {
                    this.keysLastSeenValue[this.keyI][this.keyJ] = idx;
                }
                this.keyJ = -1;
                this.keyI = -1;
            }
            this.opLastValue = -1;
        }

        private void addValue(byte[] bytes) {
            expandValueHeap(bytes.length);
            addValueNoCheck(bytes);
        }

        private void addValueNoCheck(byte[] bytes) {
            this.valueIndex[this.numOps] = this.valueHeapSize;
            System.arraycopy(bytes, 0, this.valueHeap, this.valueHeapSize, bytes.length);
            this.valueHeapSize += bytes.length;
        }

        private boolean equals(byte[] a1, int a1Start, byte[] a2, int a2Start, int len) {
            for (int i = 0; i < len; i++) {
                if (a1[a1Start] != a2[a2Start]) {
                    return false;
                }
                a1Start++;
                a2Start++;
            }
            return true;
        }

        private void expandValueHeap(int len) {
            if (len + this.valueHeapSize >= this.valueHeap.length) {
                int newSize = (len + this.valueHeapSize) * 2;
                if (newSize <= 0) {
                    throw OracleJsonExceptions.IMAGE_TOO_BIG.create(getExceptionFactory(), new Object[0]);
                }
                this.valueHeap = Arrays.copyOf(this.valueHeap, newSize);
            }
        }

        private void initializeKeyHeap() throws UnsupportedEncodingException {
            if (this.keyHeap == null) {
                this.keyHeap = new byte[this.distinctKeysSize * 15];
            }
            if (this.keyHeapOffsets == null || this.keyHeapOffsets.length < this.distinctKeysSize) {
                this.keyHeapOffsets = new int[this.distinctKeysSize];
            }
            this.keyHeapSize = 0;
            for (int i = 0; i < this.distinctKeysSize; i++) {
                this.keyHeapOffsets[i] = this.keyHeapSize;
                String key = this.distinctKeys[i];
                int maxBytesAdded = 1 + (key.length() * 4);
                if (maxBytesAdded + this.keyHeapSize >= this.keyHeap.length) {
                    this.keyHeap = Arrays.copyOf(this.keyHeap, (this.keyHeap.length + maxBytesAdded) * 2);
                }
                int result = writeString(key, this.keyHeap, this.keyHeapSize + 1);
                int len = (result - this.keyHeapSize) - 1;
                this.keyHeap[this.keyHeapSize] = (byte) len;
                this.keyHeapSize = result;
            }
        }

        public OracleJsonExceptions.ExceptionFactory getExceptionFactory() {
            return OracleJsonExceptions.ORACLE_FACTORY;
        }

        private int writeString(String value, byte[] destination, int destinationPos) {
            int ct = destinationPos;
            int len = value.length();
            for (int i = 0; i < len; i++) {
                char c = value.charAt(i);
                if (c >= 127) {
                    return slowWriteString(value, destination, destinationPos);
                }
                int i2 = ct;
                ct++;
                destination[i2] = (byte) c;
            }
            return ct;
        }

        private int writeUTF8String(byte[] source, int sourceOffset, int len, byte[] destination, int destinationPos) {
            int ct = destinationPos;
            for (int i = 0; i < len; i++) {
                int i2 = ct;
                ct++;
                int i3 = sourceOffset;
                sourceOffset++;
                destination[i2] = source[i3];
            }
            return ct;
        }

        private int slowWriteString(String value, byte[] destination, int destinationPos) {
            byte[] result = value.getBytes(StandardCharsets.UTF_8);
            for (byte b : result) {
                int i = destinationPos;
                destinationPos++;
                destination[i] = b;
            }
            return destinationPos;
        }

        private void writeHeader() throws IOException {
            if (this.bigKeys == null) {
                writeInt(-11904511);
            } else {
                writeInt(-11904509);
            }
            if (this.distinctKeysSize >= 65536) {
                this.headerFlags = (short) (this.headerFlags | 8);
            } else if (this.distinctKeysSize >= 256) {
                this.headerFlags = (short) (this.headerFlags | 1024);
            }
            if (this.distinctKeysSize > 0) {
                this.headerFlags = (short) (this.headerFlags | 256);
            }
            if (this.keyHeapSize >= 65536) {
                this.headerFlags = (short) (this.headerFlags | 2048);
            }
            if (this.treeSegmentSize > 65536) {
                this.headerFlags = (short) (this.headerFlags | 4096);
            }
            if (this.relativeOffsets) {
                this.headerFlags = (short) (this.headerFlags | 1);
            }
            if (this.numOps == 1 && !isObject(this.ops[0]) && !isArray(this.ops[0])) {
                int flags = this.headerFlags;
                writeShort((flags & (-8193)) | 16);
                writeTreeSegmentSize();
                return;
            }
            writeShort(this.headerFlags);
            if (this.distinctKeysSize >= 65536) {
                writeInt(this.distinctKeysSize);
            } else if (this.distinctKeysSize >= 256) {
                writeShort(this.distinctKeysSize);
            } else {
                writeByte(this.distinctKeysSize);
            }
            if (this.keyHeapSize >= 65536) {
                writeInt(this.keyHeapSize);
            } else {
                writeShort(this.keyHeapSize);
            }
            if (this.bigKeys != null) {
                for (BigKey bk : this.bigKeys.keySet()) {
                    this.bigKeysHeapSize += bk.key.length;
                }
                this.bigKeysHeapSize += this.bigKeys.size() * 2;
                if (this.bigKeysHeapSize < 65536) {
                    writeShort(256);
                } else {
                    writeShort(0);
                }
                writeInt(this.bigKeys.size());
                writeInt(this.bigKeysHeapSize);
            }
            writeTreeSegmentSize();
            if ((this.headerFlags & 8192) != 0) {
                writeShort(this.tinyNodeCount);
            } else {
                writeShort(0);
            }
        }

        private void writeTreeSegmentSize() throws IOException {
            if (this.treeSegmentSize > 65536) {
                writeInt(this.treeSegmentSize);
            } else {
                writeShort(this.treeSegmentSize);
            }
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private void writeNameDictionary() throws java.io.IOException {
            /*
                Method dump skipped, instructions count: 225
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: oracle.jdbc.driver.json.binary.OsonGeneratorImpl.OsonGeneratorState.writeNameDictionary():void");
        }

        private int processBucket(int offIdx, int hash) throws IOException {
            int off;
            int[] bucket = this.keys[hash];
            int[] lastValuesBucket = this.lastValueSharing ? this.keysLastSeenValue[hash] : null;
            sortBucket(bucket);
            for (int j = 0; j < bucket.length && (off = bucket[j] - 1) != -1; j++) {
                writeByte(hash);
                bucket[j] = 0;
                if (this.lastValueSharing) {
                    lastValuesBucket[j] = 0;
                }
                this.fidMap[off] = offIdx;
                int i = offIdx;
                offIdx++;
                this.temporaryIntArray[i] = this.keyHeapOffsets[off];
            }
            return offIdx;
        }

        private void sortBucket(int[] bucket) {
            for (int i = 0; i < bucket.length && bucket[i] != 0; i++) {
                for (int j = i + 1; j < bucket.length && bucket[j] != 0; j++) {
                    int keyHeapOff1 = this.keyHeapOffsets[bucket[i] - 1];
                    int keyHeapOff2 = this.keyHeapOffsets[bucket[j] - 1];
                    int l1 = this.keyHeap[keyHeapOff1] & 255;
                    int l2 = this.keyHeap[keyHeapOff2] & 255;
                    if (l2 < l1 || (l2 == l1 && memcmp(keyHeapOff2 + 1, keyHeapOff1 + 1, l1) < 0)) {
                        int tmp = bucket[i];
                        bucket[i] = bucket[j];
                        bucket[j] = tmp;
                    }
                }
            }
        }

        private int memcmp(int i, int j, int length) {
            for (int k = 0; k < length; k++) {
                int d = (this.keyHeap[i + k] & 255) - (this.keyHeap[j + k] & 255);
                if (d != 0) {
                    return d;
                }
            }
            return 0;
        }

        private void writeNameDictionary2() throws IOException {
            int i = 0;
            for (Map.Entry<BigKey, Integer> e : this.bigKeys.entrySet()) {
                int i2 = i;
                i++;
                this.fidMap[(this.distinctKeysSize + e.getValue().intValue()) - 1] = this.distinctKeysSize + i2;
                writeShort(e.getKey().hash);
            }
            int offset = 0;
            for (BigKey bk : this.bigKeys.keySet()) {
                if (this.bigKeysHeapSize < 65536) {
                    writeShort(offset);
                } else {
                    writeInt(offset);
                }
                offset += 2 + bk.key.length;
            }
            for (BigKey bk2 : this.bigKeys.keySet()) {
                writeShort(bk2.key.length);
                write(bk2.key, 0, bk2.key.length);
            }
        }

        private void writeTreeNodeSegment() throws IOException {
            for (int index = 0; index < this.numOps; index++) {
                short s = this.ops[index];
                if (!isShared(index)) {
                    if (isArray(s)) {
                        int parentOffset = this.offsets[index];
                        int childCt = this.numChildren[index];
                        writeByte(flagObjectOrArray(s, childCt));
                        if (childCt < 256) {
                            writeByte(childCt);
                        } else if (childCt < 65536) {
                            writeShort(childCt);
                        } else {
                            writeInt(childCt);
                        }
                        initTemporaryIntArray(childCt);
                        int childIdx = index + 1;
                        for (int i = 0; i < childCt; i++) {
                            this.temporaryIntArray[i] = this.offsets[childIdx];
                            childIdx = this.nextSiblings[childIdx];
                        }
                        writeChildOffsets(childCt, this.temporaryIntArray, parentOffset);
                    } else if (isObject(s)) {
                        int parentOffset2 = this.offsets[index];
                        int childCt2 = this.numChildren[index];
                        writeByte(flagObject(s, childCt2));
                        initTemporaryLongArray(childCt2);
                        if (sharesFields(s)) {
                            int firstChild = firstChild(index);
                            int delagateIdx = this.fieldIDs[firstChild];
                            int delagate = this.offsets[delagateIdx];
                            this.fieldIDs[firstChild] = this.fieldIDs[firstChild(delagateIdx)];
                            if (this.treeSegmentSize < 65536) {
                                writeShort(delagate);
                            } else {
                                writeInt(delagate);
                            }
                            packOffsets(index, childCt2, this.temporaryLongArray);
                            if (childCt2 > 10 && (s & 4) == 0) {
                                Arrays.sort(this.temporaryLongArray, 0, childCt2);
                            }
                            writeChildOffsets(childCt2, this.temporaryLongArray, parentOffset2);
                        } else {
                            packOffsets(index, childCt2, this.temporaryLongArray);
                            if (childCt2 > 10 && (s & 4) == 0) {
                                Arrays.sort(this.temporaryLongArray, 0, childCt2);
                            } else if (this.duplicateKeyMode == DuplicateKeyMode.DISALLOW) {
                                checkDuplicateKeys(this.temporaryLongArray, childCt2);
                            }
                            if (childCt2 < 256) {
                                writeByte(childCt2);
                            } else if (childCt2 < 65536) {
                                writeShort(childCt2);
                            } else {
                                writeInt(childCt2);
                            }
                            int lastFid = -1;
                            for (int i2 = 0; i2 < childCt2; i2++) {
                                int fid = unpackFid(this.temporaryLongArray[i2]);
                                if (fid == lastFid && this.duplicateKeyMode == DuplicateKeyMode.DISALLOW) {
                                    throw OracleJsonExceptions.DUPLICATE_KEY.create(getExceptionFactory(), reverseFidMap(fid));
                                }
                                lastFid = fid;
                                if (this.distinctKeysSize >= 65536) {
                                    writeInt(fid);
                                } else if (this.distinctKeysSize >= 256) {
                                    writeShort(fid);
                                } else {
                                    writeByte(fid);
                                }
                            }
                            writeChildOffsets(childCt2, this.temporaryLongArray, parentOffset2);
                        }
                    } else if (s <= 31) {
                        writeOpAndData(s, this.valueHeap, this.valueIndex[index], s);
                    } else if (OsonConstants.isSB4(s) || OsonConstants.isSB8(s) || OsonConstants.isOraNum16(s) || OsonConstants.isDec_16(s)) {
                        writeByte(s);
                        write(this.valueHeap, this.valueIndex[index], this.numChildren[index]);
                    } else {
                        switch (s) {
                            case 48:
                                writeByte(s);
                                break;
                            case 49:
                                writeByte(s);
                                break;
                            case 50:
                                writeByte(s);
                                break;
                            case 51:
                            case 52:
                            case DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN /* 116 */:
                                writeByte(s);
                                int size = this.numChildren[index];
                                writeByte(size);
                                write(this.valueHeap, this.valueIndex[index], size);
                                break;
                            case DatabaseError.EOJ_INVALID_SIZE /* 53 */:
                                int size2 = this.numChildren[index];
                                if (size2 == 0) {
                                    break;
                                } else {
                                    writeByte(s);
                                    writeByte(size2);
                                    write(this.valueHeap, this.valueIndex[index], size2);
                                    break;
                                }
                            case 54:
                                writeByte(s);
                                write(this.valueHeap, this.valueIndex[index], 8);
                                break;
                            case DatabaseError.EOJ_FAIL_CONVERSION_CHARACTER /* 55 */:
                                writeByte(s);
                                int size3 = this.numChildren[index];
                                writeShort(size3);
                                write(this.valueHeap, this.valueIndex[index], size3);
                                break;
                            case 56:
                                writeByte(s);
                                int size4 = this.numChildren[index];
                                writeInt(size4);
                                write(this.valueHeap, this.valueIndex[index], size4);
                                break;
                            case 57:
                                writeByte(s);
                                write(this.valueHeap, this.valueIndex[index], OsonPrimitiveConversions.SIZE_TIMESTAMP);
                                break;
                            case 58:
                                writeByte(s);
                                int size5 = this.numChildren[index];
                                writeShort(size5);
                                write(this.valueHeap, this.valueIndex[index], size5);
                                break;
                            case 59:
                                writeByte(s);
                                int size6 = this.numChildren[index];
                                writeInt(size6);
                                write(this.valueHeap, this.valueIndex[index], size6);
                                break;
                            case 60:
                                writeByte(s);
                                write(this.valueHeap, this.valueIndex[index], OsonPrimitiveConversions.SIZE_DATE);
                                break;
                            case 61:
                                writeByte(s);
                                write(this.valueHeap, this.valueIndex[index], 5);
                                break;
                            case 62:
                                writeByte(s);
                                write(this.valueHeap, this.valueIndex[index], 11);
                                break;
                            case DatabaseError.EOJ_WARN_CACHE_INACTIVITY_TIMEOUT /* 124 */:
                                writeByte(s);
                                write(this.valueHeap, this.valueIndex[index], OsonPrimitiveConversions.SIZE_TIMESTAMPTZ);
                                break;
                            case 125:
                                writeByte(s);
                                write(this.valueHeap, this.valueIndex[index], OsonPrimitiveConversions.SIZE_TIMESTAMP_NOFRAC);
                                break;
                            case 126:
                                writeByte(s);
                                int size7 = this.numChildren[index];
                                writeByte(size7);
                                write(this.valueHeap, this.valueIndex[index], size7);
                                break;
                            case 127:
                                writeByte(s);
                                write(this.valueHeap, this.valueIndex[index], 4);
                                break;
                            case 31489:
                                int size8 = this.numChildren[index];
                                writeShort(s);
                                writeInt(size8);
                                write(this.valueHeap, this.valueIndex[index], size8);
                                break;
                            default:
                                throw new UnsupportedOperationException(String.valueOf((int) s));
                        }
                    }
                }
            }
        }

        private String reverseFidMap(int fid) {
            for (int i = 0; i < this.distinctKeysSize; i++) {
                if (this.fidMap[i] == fid - 1) {
                    return this.distinctKeys[i];
                }
            }
            return "";
        }

        private void packOffsets(int index, int childCt, long[] packedArray) {
            int childIdx = index + 1;
            for (int i = 0; i < childCt; i++) {
                int childKeyIdx = this.fieldIDs[childIdx];
                if (childKeyIdx < 0) {
                    childKeyIdx = (this.distinctKeysSize + Math.abs(childKeyIdx)) - 1;
                }
                long fid = this.fidMap[childKeyIdx] + 1;
                packedArray[i] = (fid << 32) | this.offsets[childIdx];
                childIdx = this.nextSiblings[childIdx];
            }
        }

        private boolean sharesFields(int op) {
            return (op & 24) == 24;
        }

        private boolean isReferredTo(int op) {
            return (op & 2) == 2;
        }

        private void tryFieldIdSharing(int primaryIndex) {
            int i = this.nextSiblings[primaryIndex];
            while (true) {
                int siblingIndex = i;
                if (siblingIndex != -1) {
                    if (sameFieldIds(primaryIndex, siblingIndex)) {
                        short[] sArr = this.ops;
                        sArr[siblingIndex] = (short) (sArr[siblingIndex] | 24);
                        short[] sArr2 = this.ops;
                        sArr2[primaryIndex] = (short) (sArr2[primaryIndex] | 2);
                        int firstChild = firstChild(siblingIndex);
                        this.fieldIDs[firstChild] = primaryIndex;
                    }
                    i = this.nextSiblings[siblingIndex];
                } else {
                    return;
                }
            }
        }

        private int firstChild(int index) {
            if (index + 1 >= this.numOps) {
                return -1;
            }
            int thisDepth = this.depths[index] & 65535;
            int nextDepth = this.depths[index + 1] & 65535;
            if (nextDepth == thisDepth + 1) {
                return index + 1;
            }
            return -1;
        }

        private boolean sameFieldIds(int p1, int p2) {
            if (!isObject(this.ops[p1]) || !isObject(this.ops[p2]) || this.numChildren[p1] != this.numChildren[p2] || this.numChildren[p1] == 0 || (this.ops[p1] & 4) != 0 || (this.ops[p2] & 4) != 0) {
                return false;
            }
            int child1 = firstChild(p1);
            int child2 = firstChild(p2);
            while (this.fieldIDs[child1] == this.fieldIDs[child2]) {
                child1 = this.nextSiblings[child1];
                child2 = this.nextSiblings[child2];
                if (child1 == -1) {
                    return child2 == -1;
                }
                if (child2 == -1) {
                    return false;
                }
            }
            return false;
        }

        private boolean isArray(int op) {
            return (op & 192) == 192;
        }

        private boolean isObject(int op) {
            return (op & 192) == 128;
        }

        private boolean isStructure(int op) {
            return ((byte) op) < 0;
        }

        private void writeChildOffsets(int childCt, long[] arr, int fixedOffset) throws IOException {
            int delta = this.relativeOffsets ? fixedOffset : 0;
            if (this.treeSegmentSize < 65536) {
                for (int i = 0; i < childCt; i++) {
                    short off = (short) (arr[i] & 65535);
                    writeShort((short) (off - delta));
                }
                return;
            }
            for (int i2 = 0; i2 < childCt; i2++) {
                int off2 = (int) (arr[i2] & (-1));
                writeInt(off2 - delta);
            }
        }

        private void writeChildOffsets(int childCt, int[] arr, int fixedOffset) throws IOException {
            int delta = this.relativeOffsets ? fixedOffset : 0;
            if (this.treeSegmentSize < 65536) {
                for (int i = 0; i < childCt; i++) {
                    short off = (short) (arr[i] & 65535);
                    writeShort(off - delta);
                }
                return;
            }
            for (int i2 = 0; i2 < childCt; i2++) {
                int off2 = arr[i2];
                writeInt(off2 - delta);
            }
        }

        private void initTemporaryLongArray(int ct) {
            if (this.temporaryLongArray == null || this.temporaryLongArray.length < ct) {
                this.temporaryLongArray = new long[ct];
            }
        }

        private int unpackFid(long packed) {
            return (int) (packed >>> 32);
        }

        public void checkDuplicateKeys(long[] children, int count) {
            for (int i = 0; i < count; i++) {
                int fid = unpackFid(children[i]);
                for (int j = i + 1; j < count; j++) {
                    if (unpackFid(children[j]) == fid) {
                        String key = fid <= this.distinctKeysSize ? reverseFidMap(fid) : getBigKeyByFid(fid);
                        throw OracleJsonExceptions.DUPLICATE_KEY.create(getExceptionFactory(), key);
                    }
                }
            }
        }

        private String getBigKeyByFid(int fid) {
            int fieldId = -1;
            int k = 0;
            while (true) {
                if (k >= this.fidMap.length) {
                    break;
                }
                if (this.fidMap[k] != fid) {
                    k++;
                } else {
                    fieldId = k;
                    break;
                }
            }
            for (Map.Entry<BigKey, Integer> k2 : this.bigKeys.entrySet()) {
                if (k2.getValue().intValue() == fieldId) {
                    return new String(k2.getKey().key);
                }
            }
            return "";
        }

        private void initTemporaryIntArray(int ct) {
            if (this.temporaryIntArray == null || this.temporaryIntArray.length < ct) {
                this.temporaryIntArray = new int[ct];
            }
        }

        private void computeOffsets() {
            if (this.offsets == null || this.numOps > this.offsets.length) {
                this.offsets = new int[this.numOps];
            }
            int offset = 0;
            this.tinyNodeCount = 0;
            int i = 0;
            while (true) {
                if (i >= this.numOps) {
                    break;
                }
                if (isShared(i)) {
                    this.offsets[i] = this.offsets[-this.numChildren[i]];
                } else {
                    this.offsets[i] = offset;
                    if (isFirstChildObjectOfArray(i)) {
                        tryFieldIdSharing(i);
                    }
                    int size = sizeOfOp(i, 2);
                    countTiny(i, size);
                    offset += size;
                    if (offset >= 65536) {
                        offset = -1;
                        break;
                    }
                }
                i++;
            }
            if (offset != -1) {
                this.treeSegmentSize = offset;
                return;
            }
            int offset2 = 0;
            this.tinyNodeCount = 0;
            for (int i2 = 0; i2 < this.numOps; i2++) {
                if (isShared(i2)) {
                    this.offsets[i2] = this.offsets[-this.numChildren[i2]];
                } else {
                    this.offsets[i2] = offset2;
                    if (isFirstChildObjectOfArray(i2)) {
                        tryFieldIdSharing(i2);
                    }
                    int size2 = sizeOfOp(i2, 4);
                    countTiny(i2, size2);
                    offset2 += size2;
                    if (offset2 < 0) {
                        throw OracleJsonExceptions.IMAGE_TOO_BIG.create(getExceptionFactory(), new Object[0]);
                    }
                }
            }
            this.treeSegmentSize = offset2;
        }

        private boolean isShared(int opIndex) {
            return this.numChildren[opIndex] < 0;
        }

        private void countTiny(int i, int size) {
            if (isStructure(this.ops[i])) {
                if (size < 5 || (isObject(this.ops[i]) && isReferredTo(this.ops[i]))) {
                    this.tinyNodeCount++;
                }
            }
        }

        private boolean isFirstChildObjectOfArray(int i) {
            return isObject(this.ops[i]) && i > 0 && firstChild(i - 1) == i && isArray(this.ops[i - 1]);
        }

        private int sizeOfOp(int index, int offsetSize) {
            short s = this.ops[index];
            if (isShared(index)) {
                return 0;
            }
            if (isArray(s)) {
                int numOfChildren = this.numChildren[index];
                int bytesForNumChildren = bytesForNum(numOfChildren);
                return 1 + bytesForNumChildren + (offsetSize * numOfChildren);
            }
            if (isObject(s)) {
                int numOfChildren2 = this.numChildren[index];
                if (sharesFields(s)) {
                    return 1 + offsetSize + (numOfChildren2 * offsetSize);
                }
                int bytesForNumChildren2 = bytesForNum(numOfChildren2);
                int fidArraySize = numOfChildren2;
                if (this.distinctKeysSize >= 65536) {
                    fidArraySize *= 4;
                } else if (this.distinctKeysSize >= 256) {
                    fidArraySize *= 2;
                }
                return 1 + bytesForNumChildren2 + fidArraySize + (numOfChildren2 * offsetSize);
            }
            if (s <= 31) {
                return 1 + s;
            }
            if (OsonConstants.isSB4(s) || OsonConstants.isSB8(s) || OsonConstants.isOraNum16(s) || OsonConstants.isDec_16(s)) {
                return 1 + this.numChildren[index];
            }
            switch (s) {
                case 48:
                    return 1;
                case 49:
                    return 1;
                case 50:
                    return 1;
                case 51:
                    return 2 + this.numChildren[index];
                case 52:
                case DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN /* 116 */:
                    return 2 + this.numChildren[index];
                case DatabaseError.EOJ_INVALID_SIZE /* 53 */:
                    int size = this.numChildren[index];
                    if (size == 0) {
                        return 0;
                    }
                    return 2 + size;
                case 54:
                    return 9;
                case DatabaseError.EOJ_FAIL_CONVERSION_CHARACTER /* 55 */:
                    return 3 + this.numChildren[index];
                case 56:
                    return 5 + this.numChildren[index];
                case 57:
                    return OsonPrimitiveConversions.SIZE_TIMESTAMP + 1;
                case 58:
                    return 3 + this.numChildren[index];
                case 59:
                    return 5 + this.numChildren[index];
                case 60:
                    return OsonPrimitiveConversions.SIZE_DATE + 1;
                case 61:
                    return 6;
                case 62:
                    return 12;
                case DatabaseError.EOJ_WARN_CACHE_INACTIVITY_TIMEOUT /* 124 */:
                    return OsonPrimitiveConversions.SIZE_TIMESTAMPTZ + 1;
                case 125:
                    return OsonPrimitiveConversions.SIZE_TIMESTAMP_NOFRAC + 1;
                case 126:
                    return 2 + this.numChildren[index];
                case 127:
                    return 5;
                case 31489:
                    return 6 + this.numChildren[index];
                default:
                    throw new UnsupportedOperationException(String.valueOf((int) s));
            }
        }

        private int bytesForNum(int i) {
            if (i < 256) {
                return 1;
            }
            if (i < 65536) {
                return 2;
            }
            return 4;
        }

        private int flagObject(int op, int numChildren) {
            if (numChildren <= 10) {
                return flagObjectOrArray(op, numChildren) | 4;
            }
            return flagObjectOrArray(op, numChildren);
        }

        private int flagObjectOrArray(int op, int numChildren) {
            if (numChildren >= 256) {
                if (numChildren < 65536) {
                    op |= 8;
                } else {
                    op |= 16;
                }
            }
            if (this.treeSegmentSize > 65536) {
                op |= 32;
            }
            return op;
        }

        private void writeUb2Array(int[] arr, int len) throws IOException {
            for (int i = 0; i < len; i++) {
                writeShort(arr[i]);
            }
        }

        private void writeUb4Array(int[] arr, int len) throws IOException {
            for (int i = 0; i < len; i++) {
                writeInt(arr[i]);
            }
        }

        private final void writeInt(int value) throws IOException {
            if (this.outBufferPos + 3 >= this.outBuffer.length) {
                flushBuffer();
            }
            byte[] bArr = this.outBuffer;
            int i = this.outBufferPos;
            this.outBufferPos = i + 1;
            bArr[i] = (byte) ((value >>> 24) & 255);
            byte[] bArr2 = this.outBuffer;
            int i2 = this.outBufferPos;
            this.outBufferPos = i2 + 1;
            bArr2[i2] = (byte) ((value >>> 16) & 255);
            byte[] bArr3 = this.outBuffer;
            int i3 = this.outBufferPos;
            this.outBufferPos = i3 + 1;
            bArr3[i3] = (byte) ((value >>> 8) & 255);
            byte[] bArr4 = this.outBuffer;
            int i4 = this.outBufferPos;
            this.outBufferPos = i4 + 1;
            bArr4[i4] = (byte) ((value >>> 0) & 255);
        }

        private final void writeShort(int value) throws IOException {
            if (this.outBufferPos + 1 >= this.outBuffer.length) {
                flushBuffer();
            }
            byte[] bArr = this.outBuffer;
            int i = this.outBufferPos;
            this.outBufferPos = i + 1;
            bArr[i] = (byte) ((value >>> 8) & 255);
            byte[] bArr2 = this.outBuffer;
            int i2 = this.outBufferPos;
            this.outBufferPos = i2 + 1;
            bArr2[i2] = (byte) ((value >>> 0) & 255);
        }

        private final void writeByte(int b) throws IOException {
            if (this.outBufferPos >= this.outBuffer.length) {
                flushBuffer();
            }
            byte[] bArr = this.outBuffer;
            int i = this.outBufferPos;
            this.outBufferPos = i + 1;
            bArr[i] = (byte) b;
        }

        private void flushBuffer() throws IOException {
            this.out.write(this.outBuffer, 0, this.outBufferPos);
            this.outBufferPos = 0;
        }

        private final void write(byte[] bytes, int start, int len) throws IOException {
            if (this.outBufferPos + len > this.outBuffer.length) {
                flushBuffer();
                if (len >= this.outBuffer.length) {
                    this.out.write(bytes, start, len);
                    return;
                }
            }
            System.arraycopy(bytes, start, this.outBuffer, this.outBufferPos, len);
            this.outBufferPos += len;
        }

        private final void writeOpAndData(int op, byte[] bytes, int start, int len) throws IOException {
            if (this.outBufferPos + len + 1 > this.outBuffer.length) {
                flushBuffer();
                if (len + 1 >= this.outBuffer.length) {
                    this.out.write(op);
                    this.out.write(bytes, start, len);
                    return;
                }
            }
            byte[] bArr = this.outBuffer;
            int i = this.outBufferPos;
            this.outBufferPos = i + 1;
            bArr[i] = (byte) op;
            System.arraycopy(bytes, start, this.outBuffer, this.outBufferPos, len);
            this.outBufferPos += len;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void reset(OutputStream out) {
            this.out = out;
            this.valueHeapSize = 0;
            this.numOps = 0;
            this.distinctKeysSize = 0;
            this.seenHashSize = 0;
            this.headerFlags = (short) 8198;
            this.bigKeys = null;
            this.bigKeysHeapSize = 0;
            setUseRelativeOffsets(OsonGeneratorImpl.DEFAULT_RELATIVE_OFFSETS);
            setTinyNodeStat(OsonGeneratorImpl.DEFAULT_TINYNODE);
            setSimpleValueSharing(OsonGeneratorImpl.DEFAULT_SIMPLE_VALUE_SHARING);
            setLastValueSharing(OsonGeneratorImpl.DEFAULT_LAST_VALUE_SHARING);
            if (this.keysNeedReset) {
                for (int i = 0; i < this.keys.length; i++) {
                    for (int j = 0; this.keys[i] != null && j < this.keys[i].length && this.keys[i][j] != 0; j++) {
                        this.keys[i][j] = 0;
                        if (this.lastValueSharing) {
                            this.keysLastSeenValue[i][j] = 0;
                        }
                    }
                }
            }
            this.opLastValue = -1;
            this.keysNeedReset = true;
            this.depth = 0;
            this.outBufferPos = 0;
            this.tinyNodeCount = 0;
            this.duplicateKeyMode = OsonGeneratorImpl.DEFAULT_DUPLICATE_KEY_MODE;
            this.opEmptyArray = -1;
            this.opEmptyObject = -1;
            this.opEmptyString = -1;
            this.opOne = -1;
            this.opZero = -1;
            this.opNull = -1;
            this.opFalse = -1;
            this.opTrue = -1;
            this.keyJ = -1;
            this.keyI = -1;
            this.ctx.init();
            this.ctx.setExceptionFactory(getExceptionFactory());
        }

        /* JADX WARN: Type inference failed for: r1v9, types: [int[], int[][]] */
        private void initKeysLastSeenValue(int i) {
            if (this.keysLastSeenValue == null) {
                this.keysLastSeenValue = new int[256];
            }
            if (this.keysLastSeenValue[i] == null) {
                this.keysLastSeenValue[i] = new int[this.keys[i].length];
            } else if (this.keysLastSeenValue[i].length < this.keys[i].length) {
                this.keysLastSeenValue[i] = Arrays.copyOf(this.keysLastSeenValue[i], this.keys[i].length);
            }
        }

        public void setTinyNodeStat(boolean value) {
            if (value) {
                this.headerFlags = (short) (this.headerFlags | 8192);
            } else {
                this.headerFlags = (short) (this.headerFlags & (-8193));
            }
        }

        public void setUseRelativeOffsets(boolean value) {
            this.relativeOffsets = value;
        }

        public void setSimpleValueSharing(boolean value) {
            this.simpleValueSharing = value;
        }

        public void setLastValueSharing(boolean value) {
            this.lastValueSharing = value;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeTimestamp(byte[] raw) {
            if (raw.length == OsonPrimitiveConversions.SIZE_TIMESTAMP) {
                fixedBinary(57, raw.length, raw);
            } else {
                fixedBinary(125, raw.length, raw);
            }
        }

        public void writeTimestampTZ(byte[] raw) {
            OsonPrimitiveConversions.assertNoRegionTimestampTZ(getExceptionFactory(), raw);
            fixedBinary(DatabaseError.EOJ_WARN_CACHE_INACTIVITY_TIMEOUT, raw.length, raw);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeDate(byte[] raw) {
            fixedBinary(60, OsonPrimitiveConversions.SIZE_DATE, raw);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeIntervalYM(byte[] raw) {
            fixedBinary(61, 5, raw);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeIntervalDS(byte[] raw) {
            fixedBinary(62, 11, raw);
        }

        public void writeVector(byte[] raw) {
            fixedBinary(31489, raw.length, raw);
        }

        private void fixedBinary(int op, int len, byte[] bytes) {
            if (len != bytes.length) {
                throw new IllegalArgumentException();
            }
            addOpAndValue(op, bytes);
        }

        public void close() throws IOException {
            this.ctx.close();
            try {
                initializeKeyHeap();
                computeOffsets();
                writeHeader();
                writeNameDictionary();
                if (this.bigKeys != null) {
                    writeNameDictionary2();
                }
                writeTreeNodeSegment();
                flushBuffer();
                this.out.close();
            } catch (IOException e) {
                throw OracleJsonExceptions.IO.create(getExceptionFactory(), e, new Object[0]);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeString(String value) {
            preOp();
            expandValueHeap(value.length() * 4);
            this.valueIndex[this.numOps] = this.valueHeapSize;
            int newPos = writeString(value, this.valueHeap, this.valueHeapSize);
            int len = newPos - this.valueHeapSize;
            this.valueHeapSize = newPos;
            writeStringOp(newPos, len);
            boolean duplicate = checkStringDuplicate(len);
            postOp(!duplicate);
        }

        private boolean checkStringDuplicate(int len) {
            if (this.lastValueSharing && this.opLastValue != -1 && this.ops[this.numOps - 1] == this.ops[this.opLastValue] && this.numChildren[this.opLastValue] == len && equals(this.valueHeap, this.valueIndex[this.numOps - 1], this.valueHeap, this.valueIndex[this.opLastValue], len)) {
                markDuplicate(this.numOps - 1, this.opLastValue);
                this.headerFlags = (short) (this.headerFlags | 64);
                return true;
            }
            if (len == 0 && this.simpleValueSharing) {
                if (this.opEmptyString == -1) {
                    this.opEmptyString = this.numOps - 1;
                    return true;
                }
                this.headerFlags = (short) (this.headerFlags | 32);
                markDuplicate(this.numOps - 1, this.opEmptyString);
                return true;
            }
            return false;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeUTF8String(byte[] array, int offset, int len) {
            preOp();
            expandValueHeap(len);
            this.valueIndex[this.numOps] = this.valueHeapSize;
            int newPos = writeUTF8String(array, offset, len, this.valueHeap, this.valueHeapSize);
            this.valueHeapSize = newPos;
            writeStringOp(newPos, len);
            checkStringDuplicate(len);
            postOp(true);
        }

        private void writeStringOp(int newPos, int len) {
            if (len <= 31) {
                addOp(len);
            } else if (len < 256) {
                addOp(51);
            } else if (len < 65536) {
                addOp(55);
            } else {
                addOp(56);
            }
            this.numChildren[this.numOps - 1] = len;
            this.ctx.primitive();
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeStartObject() {
            preOp();
            addOp(128);
            push(this.numOps - 1);
            this.ctx.startObject();
            postOp(false);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeStartObjectNoSort() {
            preOp();
            addOp(132);
            push(this.numOps - 1);
            this.ctx.startObject();
            postOp(false);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeStartArray() {
            preOp();
            addOp(192);
            push(this.numOps - 1);
            this.ctx.startArray();
            postOp(false);
        }

        public void writeEnd() {
            this.ctx.end();
            this.depth--;
            this.previousSiblingIdx = this.opStack[this.depth];
            if (this.simpleValueSharing && this.numChildren[this.opStack[this.depth]] == 0) {
                int index = this.opStack[this.depth];
                if (isArray(this.ops[index])) {
                    if (this.opEmptyArray == -1) {
                        this.opEmptyArray = index;
                        return;
                    } else {
                        this.headerFlags = (short) (this.headerFlags | 32);
                        markDuplicate(index, this.opEmptyArray);
                        return;
                    }
                }
                if (this.opEmptyObject == -1) {
                    this.opEmptyObject = index;
                } else {
                    this.headerFlags = (short) (this.headerFlags | 32);
                    markDuplicate(index, this.opEmptyObject);
                }
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeDouble(double value) {
            byte[] bytes = OsonPrimitiveConversions.doubleToCanonicalFormatBytes(value);
            addOpAndValue(54, bytes);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeBoolean(boolean value) {
            preOp();
            if (value) {
                addOp(49);
                if (this.simpleValueSharing) {
                    if (this.opTrue == -1) {
                        this.opTrue = this.numOps - 1;
                    } else {
                        this.headerFlags = (short) (this.headerFlags | 32);
                        markDuplicate(this.numOps - 1, this.opTrue);
                    }
                }
            } else {
                addOp(50);
                if (this.simpleValueSharing) {
                    if (this.opFalse == -1) {
                        this.opFalse = this.numOps - 1;
                    } else {
                        this.headerFlags = (short) (this.headerFlags | 32);
                        markDuplicate(this.numOps - 1, this.opFalse);
                    }
                }
            }
            this.ctx.primitive();
            postOp(true);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeOraNumber(OracleJsonDecimal value) {
            OracleJsonDecimalImpl impl = (OracleJsonDecimalImpl) value;
            if (impl.isDec()) {
                writeDecimal(impl.bigDecimalValue());
                return;
            }
            if (impl.isSB4()) {
                writeSB4(impl.intValue());
            } else if (impl.isSB8()) {
                writeSB8(impl.longValue());
            } else {
                writeNumber(impl.raw());
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeDecimal(BigDecimal value) {
            byte[] bytes = OsonPrimitiveConversions.toNumber(value);
            writeDecimal(bytes);
        }

        private void writeDecimal(byte[] bytes) {
            if (bytes.length <= 8) {
                addOpAndValue((bytes.length - 1) | OsonConstants.MASK_DEC_16, bytes);
            } else if (bytes.length < 256) {
                addOpAndValue(DatabaseError.EOJ_AUTOCOMMIT_IN_GLOBAL_SESSIONLESS_TXN, bytes);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeDecimal(BigInteger value) {
            writeDecimal(OsonPrimitiveConversions.toNumber(value));
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeSB4(int value) {
            byte[] raw = OsonPrimitiveConversions.toNumber(value);
            int op = raw.length | OsonConstants.MASK_SB4;
            addOpAndValue(op, raw);
        }

        private void markDuplicate(int index, int replacingIndex) {
            this.numChildren[index] = -replacingIndex;
        }

        private boolean tryMarkDuplicate(int op, byte[] bytes) {
            if (this.lastValueSharing && this.opLastValue != -1 && op == this.ops[this.opLastValue] && this.numChildren[this.opLastValue] == bytes.length && equals(bytes, 0, this.valueHeap, this.valueIndex[this.opLastValue], bytes.length)) {
                markDuplicate(this.numOps, this.opLastValue);
                this.headerFlags = (short) (this.headerFlags | 64);
                return true;
            }
            return false;
        }

        private void addOpAndValue(int op, byte[] raw) {
            addOpAndValueNoPostOp(op, raw);
            postOp(true);
        }

        private void addOpAndValueNoPostOp(int op, byte[] raw) {
            preOp();
            if (!tryMarkDuplicate(op, raw)) {
                addValue(raw);
                this.numChildren[this.numOps] = raw.length;
            }
            addOp(op);
            this.ctx.primitive();
        }

        public void writeSB8(long value) {
            byte[] raw = OsonPrimitiveConversions.toNumber(value);
            addOpAndValue(raw.length | OsonConstants.MASK_SB8, raw);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeNumberAsString(BigDecimal bd) {
            byte[] bytes = bd.toString().getBytes(StandardCharsets.UTF_8);
            if (bytes.length > 256) {
                throw new IllegalArgumentException();
            }
            addOpAndValue(53, bytes);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeBytes(byte[] bytes) {
            int op = bytes.length < 65536 ? 58 : 59;
            addOpAndValue(op, bytes);
        }

        protected void writeId(byte[] bytes) {
            if (bytes.length > 16) {
                throw new UnsupportedOperationException();
            }
            addOpAndValue(126, bytes);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeFloat(float value) {
            byte[] bytes = OsonPrimitiveConversions.floatToCanonicalFormatBytes(value);
            addOpAndValue(127, bytes);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeKey(String key) {
            int fid;
            this.ctx.pendingKey();
            if (this.numOps >= this.ops.length) {
                expandOp();
            }
            this.keyI = OsonHeader.ohash(key, this.keylen);
            if (this.keylen.get() > OsonConstants.MAX_SMALL_KEY_LENGTH) {
                if (this.keylen.get() > OsonConstants.MAX_BIG_KEY_LENGTH) {
                    throw OracleJsonExceptions.KEY_TOO_LONG.create(getExceptionFactory(), new Object[0]);
                }
                if (this.bigKeys == null) {
                    this.bigKeys = new TreeMap<>();
                }
                BigKey bigKey = new BigKey(key);
                Integer fid2 = this.bigKeys.get(bigKey);
                if (fid2 == null) {
                    fid2 = Integer.valueOf(this.bigKeys.size() + 1);
                    this.bigKeys.put(bigKey, fid2);
                }
                this.fieldIDs[this.numOps] = -fid2.intValue();
                return;
            }
            int[] fids = this.keys[this.keyI];
            if (fids == null) {
                int[] iArr = new int[2];
                this.keys[this.keyI] = iArr;
                fids = iArr;
            }
            this.keyJ = 0;
            while (this.keyJ < fids.length && (fid = fids[this.keyJ] - 1) != -1) {
                if (!this.distinctKeys[fid].equals(key)) {
                    this.keyJ++;
                } else {
                    this.fieldIDs[this.numOps] = fid;
                    if (this.lastValueSharing) {
                        this.opLastValue = this.keysLastSeenValue[this.keyI][this.keyJ];
                        return;
                    }
                    return;
                }
            }
            if (this.keyJ >= fids.length) {
                int[][] iArr2 = this.keys;
                int i = this.keyI;
                int[] iArrCopyOf = Arrays.copyOf(this.keys[this.keyI], this.keys[this.keyI].length * 2);
                iArr2[i] = iArrCopyOf;
                fids = iArrCopyOf;
            } else if (this.keyJ == 0 && this.seenHashSize < OsonGeneratorImpl.SEEN_HASH_THRESHOLD) {
                int[] iArr3 = this.seenHash;
                int i2 = this.seenHashSize;
                this.seenHashSize = i2 + 1;
                iArr3[i2] = this.keyI;
            }
            if (this.distinctKeysSize + 1 >= this.distinctKeys.length) {
                this.distinctKeys = (String[]) Arrays.copyOf(this.distinctKeys, this.distinctKeys.length * 2);
            }
            this.fieldIDs[this.numOps] = this.distinctKeysSize;
            String[] strArr = this.distinctKeys;
            int i3 = this.distinctKeysSize;
            this.distinctKeysSize = i3 + 1;
            strArr[i3] = key;
            fids[this.keyJ] = this.distinctKeysSize;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void writeNull() {
            preOp();
            addOp(48);
            if (this.simpleValueSharing) {
                if (this.opNull == -1) {
                    this.opNull = this.numOps - 1;
                } else {
                    this.headerFlags = (short) (this.headerFlags | 32);
                    markDuplicate(this.numOps - 1, this.opNull);
                }
            }
            this.ctx.primitive();
            postOp(true);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonGeneratorImpl$BigKey.class */
    private static class BigKey implements Comparable<BigKey> {
        byte[] key;
        int hash;

        public BigKey(String key) {
            this.key = key.getBytes(StandardCharsets.UTF_8);
            this.hash = OsonHeader.ohash(key, null);
        }

        @Override // java.lang.Comparable
        public int compareTo(BigKey o) {
            if (o.hash != this.hash) {
                return this.hash - o.hash;
            }
            if (this.key.length != o.key.length) {
                return this.key.length - o.key.length;
            }
            for (int i = 0; i < this.key.length; i++) {
                int res = Byte.compare(this.key[i], o.key[i]);
                if (res != 0) {
                    return res;
                }
            }
            return 0;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonGeneratorImpl$OsonGeneratorStatePool.class */
    public static final class OsonGeneratorStatePool {
        private volatile WeakReference<ConcurrentLinkedQueue<OsonGeneratorState>> queue;

        /* JADX INFO: Access modifiers changed from: private */
        public OsonGeneratorState getState(OutputStream out) {
            ConcurrentLinkedQueue<OsonGeneratorState> list = getQueue();
            OsonGeneratorState result = null;
            if (list != null) {
                result = list.poll();
            }
            if (result == null) {
                result = new OsonGeneratorState(this, out);
            }
            return result;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void putState(OsonGeneratorState state) {
            ConcurrentLinkedQueue<OsonGeneratorState> list = getQueue();
            if (list == null) {
                ConcurrentLinkedQueue<OsonGeneratorState> list2 = new ConcurrentLinkedQueue<>();
                list2.offer(state);
                this.queue = new WeakReference<>(list2);
                return;
            }
            list.offer(state);
        }

        private ConcurrentLinkedQueue<OsonGeneratorState> getQueue() {
            WeakReference<ConcurrentLinkedQueue<OsonGeneratorState>> queue = this.queue;
            if (queue == null) {
                return null;
            }
            return queue.get();
        }
    }

    public OsonGeneratorImpl(OsonGeneratorStatePool pool, OutputStream out) {
        if (pool != null) {
            this.state = pool.getState(out);
        } else {
            this.state = new OsonGeneratorState(null, out);
        }
        this.state.reset(out);
    }

    public void reset(OutputStream out) {
        this.state.reset(out);
    }

    public void setTinyNodeStat(boolean value) {
        this.state.setTinyNodeStat(value);
    }

    public void setUseRelativeOffsets(boolean value) {
        this.state.setUseRelativeOffsets(value);
    }

    public void setSimpleValueSharing(boolean value) {
        this.state.setSimpleValueSharing(value);
    }

    public void setLastValueSharing(boolean value) {
        this.state.setLastValueSharing(value);
    }

    public boolean getLastValueSharing() {
        return this.state.lastValueSharing;
    }

    public boolean getSimpleValuesharing() {
        return this.state.simpleValueSharing;
    }

    public boolean getRelativeOffsets() {
        return this.state.relativeOffsets;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeStartObject() {
        this.state.writeStartObject();
        return this;
    }

    public OracleJsonGenerator writeStartObject(boolean sort) {
        if (sort) {
            this.state.writeStartObject();
        } else {
            this.state.writeStartObjectNoSort();
        }
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator, oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeKey(String key) {
        this.state.writeKey(key);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeStartArray() {
        this.state.writeStartArray();
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeEnd() {
        this.state.writeEnd();
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String value) {
        this.state.writeString(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(BigDecimal value) {
        this.state.writeDecimal(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(BigInteger value) {
        this.state.writeDecimal(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(int value) {
        this.state.writeSB4(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(long value) {
        this.state.writeSB8(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(double value) {
        this.state.writeDouble(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(boolean value) {
        this.state.writeBoolean(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeNull() {
        this.state.writeNull();
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        if (this.state != null) {
            this.state.close();
            if (this.state.pool != null) {
                this.state.pool.putState(this.state);
            }
            this.state = null;
        }
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String key, byte[] value) {
        writeKey(key);
        write(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String key, LocalDateTime value) {
        writeKey(key);
        write(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(String key, OffsetDateTime value) {
        writeKey(key);
        write(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(byte[] bytes) {
        this.state.writeBytes(bytes);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator writeId(byte[] bytes) {
        this.state.writeId(bytes);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(float value) {
        this.state.writeFloat(value);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(LocalDateTime local) {
        byte[] bytes = OsonPrimitiveConversions.toOracleTimestamp(this.state.getExceptionFactory(), local);
        this.state.writeTimestamp(bytes);
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(OffsetDateTime offset) {
        byte[] bytes = OsonPrimitiveConversions.toOracleTimestampTZ(this.state.getExceptionFactory(), offset);
        this.state.writeTimestampTZ(bytes);
        return this;
    }

    public OracleJsonGenerator writeIntervalDS(Duration value) {
        byte[] bytes = OsonPrimitiveConversions.durationToIntervalDS(value);
        this.state.writeIntervalDS(bytes);
        return this;
    }

    public OracleJsonGenerator writeIntervalYM(Period value) {
        byte[] bytes = OsonPrimitiveConversions.periodToIntervalYM(this.state.getExceptionFactory(), value);
        this.state.writeIntervalYM(bytes);
        return this;
    }

    public OracleJsonGenerator writeNumberAsString(BigDecimal bd) {
        this.state.writeNumberAsString(bd);
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeBinary(OracleJsonBinary value) {
        byte[] bytes = value.getBytes();
        if (!value.isId()) {
            this.state.writeBytes(bytes);
        } else {
            this.state.writeId(bytes);
        }
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeDouble(OracleJsonDouble value) {
        return write(value.doubleValue());
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeFloat(OracleJsonFloat value) {
        return write(value.floatValue());
    }

    public void writeDecimal(BigDecimal value) {
        this.state.writeDecimal(value);
    }

    public void writeSB4(int value) {
        this.state.writeSB4(value);
    }

    public void writeSB8(long value) {
        this.state.writeSB8(value);
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeOraNumber(OracleJsonDecimal value) {
        this.state.writeOraNumber(value);
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeTimestamp(OracleJsonTimestamp value) {
        this.state.writeTimestamp(((OracleJsonTimestampImpl) value).raw());
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeTimestampTZ(OracleJsonTimestampTZ value) {
        this.state.writeTimestampTZ(((OracleJsonTimestampTZImpl) value).raw());
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeDate(OracleJsonDate value) {
        this.state.writeDate(((OracleJsonDateImpl) value).raw());
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeIntervalDS(OracleJsonIntervalDS value) {
        this.state.writeIntervalDS(((OracleJsonIntervalDSImpl) value).raw());
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeIntervalYM(OracleJsonIntervalYM value) {
        this.state.writeIntervalYM(((OracleJsonIntervalYMImpl) value).raw());
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeVector(OracleJsonVector value) {
        this.state.writeVector(((OracleJsonVectorImpl) value).raw());
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected OracleJsonGenerator writeString(OracleJsonString value) {
        return write(value.getString());
    }

    @Override // oracle.sql.json.OracleJsonGenerator, java.io.Flushable
    public void flush() {
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(Period p) {
        this.state.writeIntervalYM(OsonPrimitiveConversions.periodToIntervalYM(this.state.getExceptionFactory(), p));
        return this;
    }

    @Override // oracle.sql.json.OracleJsonGenerator
    public OracleJsonGenerator write(Duration d) {
        this.state.writeIntervalDS(OsonPrimitiveConversions.durationToIntervalDS(d));
        return this;
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected void writeStringFromParser(OracleJsonParser parser) {
        if (!(parser instanceof OsonParserImpl)) {
            this.state.writeString(parser.getString());
            return;
        }
        OsonParserImpl oparser = (OsonParserImpl) parser;
        byte[] arr = oparser.getContext().b.buffer.array();
        this.state.writeUTF8String(arr, oparser.getCurrentStringPos(), oparser.getCurrentStringLen());
    }

    @Override // oracle.jdbc.driver.json.binary.AbstractGenerator
    protected void writeDecimalFromParser(OracleJsonParser parser) {
        write(parser.getValue());
    }

    public void setDuplicateKeyMode(DuplicateKeyMode mode) {
        this.state.duplicateKeyMode = mode;
    }
}
