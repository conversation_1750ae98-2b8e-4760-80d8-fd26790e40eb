package oracle.jdbc.diagnostics;

import oracle.jdbc.TraceKey;
import oracle.jdbc.pool.OracleOCIConnectionPool;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/OracleTraceKey.class */
public enum OracleTraceKey implements <PERSON><PERSON>ey {
    CONNECTION_ID(null, OracleOCIConnectionPool.CONNECTION_ID),
    TENAN<PERSON>(null, "tenant"),
    SQL(null, "sql_text"),
    CLIENTID("OSCID", "client_id"),
    MODULE("OSCID", "module"),
    ACTION("OSCID", "action"),
    ECID("OSCID", "ecid"),
    SEQUENCE_NUMBER("OSCID", "sequence_number"),
    <PERSON><PERSON>("OSCID", "dbop"),
    CLIENTINFO("OSCID", "client_info");

    private final int index = TraceKey.nextIndex();
    private final String nameSpace;
    private final String keyName;

    OracleTraceKey(String nameSpace, String keyName) {
        this.keyName = keyName;
        this.nameSpace = nameSpace;
        TraceKey.register(this);
    }

    @Override // oracle.jdbc.TraceKey
    public int index() {
        return this.index;
    }

    @Override // oracle.jdbc.TraceKey
    public String keyName() {
        return this.keyName;
    }

    @Override // oracle.jdbc.TraceKey
    public String namespace() {
        return null;
    }
}
