package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrgnc.class */
public class T4CTTIkpdnrgnc {
    byte[] kpdnrgnclsc;
    T4CMAREngine mar;

    T4CTTIkpdnrgnc(T4CConnection connection) {
        this.mar = connection.mare;
    }

    void receive() throws SQLException, IOException {
        int kpdnrgnclscl = this.mar.unmarshalSWORD();
        if (kpdnrgnclscl > 0) {
            this.kpdnrgnclsc = new byte[kpdnrgnclscl];
            int[] intArray = new int[1];
            this.mar.unmarshalCLR(this.kpdnrgnclsc, 0, intArray, kpdnrgnclscl);
            return;
        }
        this.kpdnrgnclsc = null;
    }

    public byte[] getKpdnrgnclsc() {
        return this.kpdnrgnclsc;
    }
}
