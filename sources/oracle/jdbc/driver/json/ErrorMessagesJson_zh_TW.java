package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_zh_TW.class */
public class ErrorMessagesJson_zh_TW extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "發生 I/O 異常狀況"}, new Object[]{"26302", "不支援年份 \"{0}\""}, new Object[]{"26303", "溢位, 值太大: {0}."}, new Object[]{"26304", "不支援的選項 (未實行)."}, new Object[]{"26305", "二進位 JSON 無效或損毀."}, new Object[]{"26306", "不支援的二進位 JSON 版本: {0}."}, new Object[]{"26307", "UTF-8 編碼的索引鍵長度不得超過 256 個位元組. 以下索引鍵超出此限制: \"{0}\"."}, new Object[]{"26308", "指定的 JSON 太長, 無法編碼成二進位 JSON.  編碼的映像檔大小不得超過 2GB."}, new Object[]{"26309", "二進位 JSON 無效或損毀. 指定的映像檔只有 {0} 個位元組."}, new Object[]{"26310", "指定的 java.time.Period 已設定天數, 但 Oracle 年份至月份間隔不支援天數設定值."}, new Object[]{"26311", "產生器在結束之前關閉."}, new Object[]{"26312", "必須在這個相關資訊環境中指定物件索引鍵."}, new Object[]{"26313", "寫入無效. 已寫入完整的值."}, new Object[]{"26314", "此相關資訊環境中不允許結束."}, new Object[]{"26315", "此相關資訊環境中不允許使用索引鍵."}, new Object[]{"26316", "索引鍵後應該要有值."}, new Object[]{"26317", "剖析器狀態必須為 {0}."}, new Object[]{"26318", "剖析器狀態不可為 {0}."}, new Object[]{"26319", "剖析器必須針對值."}, new Object[]{"26320", "\"{0}\" 不是支援的包裝函式類型."}, new Object[]{"26321", "無法修改此物件. 若要製作可修改的複本, 請使用 OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "無法修改此陣列. 若要製作可修改的複本, 請使用 OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "JSON 物件包含重複的索引鍵: {0}."}, new Object[]{"26324", "無法自動偵測編碼, 字元數不足."}, new Object[]{"26325", "預期應為 EOF 記號, 但實際為 {0}."}, new Object[]{"26326", "在第 {1} 行第 {2} 欄中, 發現非預期的字元 {0}."}, new Object[]{"26327", "在第 {1} 行第 {2} 欄中, 發現非預期的字元 {0}. 預期應為: {3}."}, new Object[]{"26328", "在第 {1} 行第 {2} 欄中, 發現無效的記號 {0}. 預期的記號為: {3}."}, new Object[]{"26329", "JsonParser#getString() 只對 KEY_NAME、VALUE_STRING 以及 VALUE_NUMBER 剖析器狀態有效. 但目前的剖析器狀態為 {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() 只對 VALUE_NUMBER 剖析器狀態有效. 但目前的剖析器狀態為 {0}."}, new Object[]{"26331", "JsonParser#getInt() 只對 VALUE_NUMBER 剖析器狀態有效. 但目前的剖析器狀態為 {0}."}, new Object[]{"26332", "JsonParser#getLong() 只對 VALUE_NUMBER 剖析器狀態有效. 但目前的剖析器狀態為 {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() 只對 VALUE_NUMBER 剖析器狀態有效. 但目前的剖析器狀態為 {0}."}, new Object[]{"26334", "JsonParser#getArray() 只對 START_ARRAY 剖析器狀態有效. 但目前的剖析器狀態為 {0}."}, new Object[]{"26335", "JsonParser#getObject() 只對 START_OBJECT 剖析器狀態有效. 但目前的剖析器狀態為 {0}."}, new Object[]{"26336", "不支援區域的時戳. 只支援偏移時區."}, new Object[]{"26337", "JSON 值的物件和陣列巢狀層級不可超過 {0} 層"}, new Object[]{"26338", "JSON 物件的索引鍵不可超過 65,535 個位元組"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
