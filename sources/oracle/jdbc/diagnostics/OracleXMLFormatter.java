package oracle.jdbc.diagnostics;

import java.util.Iterator;
import java.util.logging.Level;
import java.util.logging.LogRecord;
import java.util.logging.XMLFormatter;
import oracle.jdbc.TraceKey;
import oracle.sql.CharacterSet;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/OracleXMLFormatter.class */
public class Oracle<PERSON><PERSON>Formatter extends XMLFormatter {
    @Override // java.util.logging.XMLFormatter, java.util.logging.Formatter
    public String format(LogRecord record) {
        if (record instanceof OracleLogRecord) {
            OracleLogRecord oracleLogRecord = (OracleLogRecord) record;
            StringBuilder sb = new StringBuilder(CharacterSet.AR8ASMO8X_CHARSET);
            appendStartTag(sb);
            appendSecurityLabel(sb, oracleLogRecord);
            String formattedText = super.format(record);
            sb.append(formattedText.substring("<record>\n".length(), formattedText.length() - "</record>\n".length()));
            appendTraceAttributes(sb, oracleLogRecord);
            appendEndTag(sb);
            return sb.toString();
        }
        return super.format(record);
    }

    protected void appendStartTag(StringBuilder sb) {
        sb.append("<record>\n");
    }

    protected void appendSecurityLabel(StringBuilder sb, OracleLogRecord record) {
        sb.append("  <securityLabel>");
        sb.append(record.getSecurityLabel().getLabel());
        sb.append("</securityLabel>\n");
    }

    protected void appendTraceAttributes(StringBuilder sb, OracleLogRecord record) {
        TraceAttributes attributes = record.getTraceAttributes();
        if (attributes != null) {
            Iterator<TraceKey> i = TraceKey.iterator();
            while (i.hasNext()) {
                TraceKey k = i.next();
                String traceValue = attributes.get(k);
                if (traceValue != null && (k != OracleTraceKey.SQL || record.getLevel().intValue() < Level.INFO.intValue())) {
                    sb.append("<trace_attribute>\n");
                    sb.append("  <key>" + k.xmlAttributeName() + "</key>\n");
                    sb.append("  <value>");
                    escape(sb, traceValue);
                    sb.append("</value>\n");
                    sb.append("</trace_attribute>\n");
                }
            }
        }
    }

    protected void appendEndTag(StringBuilder sb) {
        sb.append("</record>\n");
    }

    protected void a2(StringBuilder sb, int x) {
        if (x < 10) {
            sb.append('0');
        }
        sb.append(x);
    }

    protected void escape(StringBuilder sb, String text) {
        if (text == null) {
            text = "<null>";
        }
        for (int i = 0; i < text.length(); i++) {
            char ch = text.charAt(i);
            if (ch == '<') {
                sb.append("&lt;");
            } else if (ch == '>') {
                sb.append("&gt;");
            } else if (ch == '&') {
                sb.append("&amp;");
            } else {
                sb.append(ch);
            }
        }
    }
}
