package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxssync.class */
final class T4CTTIoxssync extends T4CTTIfun {
    T4CTTIoxssync(T4CConnection _conn) {
        super(_conn, (byte) 17);
        setFunCode((short) 176);
    }

    void doOXSSYNC() throws SQLException, IOException {
        doPigRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
    }

    @Override // oracle.jdbc.driver.T4CTTIfun, oracle.jdbc.driver.T4CTTIMsg
    protected oracle.jdbc.internal.OracleConnection getConnectionDuringExceptionHandling() {
        return this.connection;
    }
}
