package oracle.jdbc.driver;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import java.util.concurrent.CompletionStage;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.OracleResultSet;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.internal.CompletionStageUtil;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.proxy.ProxyFactory;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ShardingDriverExtension.class */
class ShardingDriverExtension extends OracleDriverExtension implements Diagnosable {
    static ProxyFactory PROXY_FACTORY;
    private static final String CLASS_NAME = ShardingDriverExtension.class.getName();
    private static final Monitor proxyFactoryLock = Monitor.newInstance();

    ShardingDriverExtension() {
    }

    static {
        PROXY_FACTORY = null;
        Monitor.CloseableLock lock = proxyFactoryLock.acquireCloseableLock();
        Throwable th = null;
        try {
            if (PROXY_FACTORY == null) {
                PROXY_FACTORY = ProxyFactory.createProxyFactory(AbstractShardingConnection.class, AbstractShardingStatement.class, AbstractShardingPreparedStatement.class, AbstractShardingCallableStatement.class, AbstractShardingResultSet.class, AbstractShardingDatabaseMetaData.class, AbstractShardingLob.class);
            }
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                        return;
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                        return;
                    }
                }
                lock.close();
            }
        } catch (Throwable th3) {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
            throw th3;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    Connection getConnection(String url, @Blind(PropertiesBlinder.class) Properties info, AbstractConnectionBuilder<?, ?> builder) throws SQLException {
        try {
            Connection connection = (Connection) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OracleConnection.class);
            ((AbstractShardingConnection) connection).initialize(url, info, this, builder);
            return connection;
        } catch (SQLException ex) {
            int errorCode = ex.getErrorCode() - DatabaseError.JDBC_ERROR_BASE;
            trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getConnection()", "failed to initialize sharding datasource connection, error code={0}", null, (Throwable) null, Integer.valueOf(errorCode));
            if (errorCode == 1708 || errorCode == 1709) {
                try {
                    OracleDriverExtension driverExtension = (OracleDriverExtension) Class.forName("oracle.jdbc.driver.T4CDriverExtension").newInstance();
                    T4CConnection conn = new T4CConnection(url, info, driverExtension);
                    conn.connect(builder);
                    conn.protocolId = 0;
                    return conn;
                } catch (Exception e) {
                    throw new SQLException(e);
                }
            }
            throw ex;
        }
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    final CompletionStage<Connection> getConnectionAsync(String url, @Blind(PropertiesBlinder.class) Properties info, AbstractConnectionBuilder<?, ?> builder) {
        return CompletionStageUtil.failedStage(new UnsupportedOperationException("Asynchronous connection is not supported by the sharding driver"));
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    oracle.jdbc.internal.OracleStatement allocateStatement(oracle.jdbc.internal.OracleConnection oracleConnection, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        oracle.jdbc.internal.OracleStatement oracleStatement = (oracle.jdbc.internal.OracleStatement) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OracleStatement.class, oracleConnection);
        ((AbstractShardingStatement) oracleStatement).initialize((AbstractShardingConnection) oracleConnection, resultSetType);
        return oracleStatement;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    oracle.jdbc.internal.OraclePreparedStatement allocatePreparedStatement(oracle.jdbc.internal.OracleConnection oracleConnection, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        oracle.jdbc.internal.OraclePreparedStatement oraclePreparedStatement = (oracle.jdbc.internal.OraclePreparedStatement) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OraclePreparedStatement.class, oracleConnection);
        ((AbstractShardingPreparedStatement) oraclePreparedStatement).initialize((AbstractShardingConnection) oracleConnection, sql, resultSetType);
        return oraclePreparedStatement;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    oracle.jdbc.internal.OraclePreparedStatement allocatePreparedStatement(oracle.jdbc.internal.OracleConnection oracleConnection, String sql, AutoKeyInfo autoKeyInfo) throws SQLException {
        oracle.jdbc.internal.OraclePreparedStatement oraclePreparedStatement = (oracle.jdbc.internal.OraclePreparedStatement) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OraclePreparedStatement.class, oracleConnection);
        ((AbstractShardingPreparedStatement) oraclePreparedStatement).initialize((AbstractShardingConnection) oracleConnection, sql, autoKeyInfo);
        return oraclePreparedStatement;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // oracle.jdbc.driver.OracleDriverExtension
    oracle.jdbc.internal.OracleCallableStatement allocateCallableStatement(oracle.jdbc.internal.OracleConnection oracleConnection, String sql, OracleResultSet.ResultSetType resultSetType) throws SQLException {
        oracle.jdbc.internal.OracleCallableStatement oracleCallableStatement = (oracle.jdbc.internal.OracleCallableStatement) PROXY_FACTORY.proxyForType(oracle.jdbc.internal.OracleCallableStatement.class, oracleConnection);
        ((AbstractShardingCallableStatement) oracleCallableStatement).initialize((AbstractShardingConnection) oracleConnection, sql, resultSetType);
        return oracleCallableStatement;
    }

    @Override // oracle.jdbc.driver.OracleDriverExtension
    OracleInputStream createInputStream(OracleStatement stmt, int index, Accessor accessor) throws SQLException {
        return new T4CInputStream(stmt, index, accessor);
    }

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return CommonDiagnosable.getInstance();
    }
}
