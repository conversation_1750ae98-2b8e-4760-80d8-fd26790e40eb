package oracle.jdbc.babelfish;

import java.lang.reflect.Method;
import java.sql.CallableStatement;
import java.sql.SQLException;
import oracle.jdbc.OracleCallableStatement;
import oracle.jdbc.proxy.annotation.GetCreator;
import oracle.jdbc.proxy.annotation.GetDelegate;
import oracle.jdbc.proxy.annotation.OnError;
import oracle.jdbc.proxy.annotation.Post;
import oracle.jdbc.proxy.annotation.ProxyFor;

@ProxyFor({OracleCallableStatement.class, CallableStatement.class})
/* loaded from: ojdbc8.jar:oracle/jdbc/babelfish/BabelfishCallableStatement.class */
public abstract class BabelfishCallableStatement extends BabelfishPreparedStatement {
    @Override // oracle.jdbc.babelfish.BabelfishPreparedStatement, oracle.jdbc.babelfish.BabelfishStatement, oracle.jdbc.babelfish.BabelfishGenericProxy
    @GetCreator
    protected abstract Object getCreator();

    @Override // oracle.jdbc.babelfish.BabelfishPreparedStatement, oracle.jdbc.babelfish.BabelfishStatement, oracle.jdbc.babelfish.BabelfishGenericProxy
    @GetDelegate
    protected abstract Object getDelegate();

    @Override // oracle.jdbc.babelfish.BabelfishPreparedStatement, oracle.jdbc.babelfish.BabelfishStatement, oracle.jdbc.babelfish.BabelfishGenericProxy
    @OnError(SQLException.class)
    protected Object translateError(Method m, SQLException ex) throws SQLException {
        throw this.translator.translateError(ex);
    }

    @Override // oracle.jdbc.babelfish.BabelfishPreparedStatement, oracle.jdbc.babelfish.BabelfishStatement, oracle.jdbc.babelfish.BabelfishGenericProxy
    @Post
    protected Object post_Methods(Method m, Object result) {
        if (result instanceof BabelfishGenericProxy) {
            ((BabelfishGenericProxy) result).setTranslator(this.translator);
        }
        return result;
    }

    public void registerOutParameter(int parameterIndex, int sqlType) throws SQLException {
        try {
            ((OracleCallableStatement) getDelegate()).registerOutParameterAtName(String.valueOf(parameterIndex), sqlType);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void registerOutParameter(int parameterIndex, int sqlType, int scale) throws SQLException {
        try {
            ((OracleCallableStatement) getDelegate()).registerOutParameterAtName(String.valueOf(parameterIndex), sqlType, scale);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }

    public void registerOutParameter(int parameterIndex, int sqlType, String typeName) throws SQLException {
        try {
            ((OracleCallableStatement) getDelegate()).registerOutParameterAtName(String.valueOf(parameterIndex), sqlType, typeName);
        } catch (SQLException ex) {
            throw this.translator.translateError(ex);
        }
    }
}
