package oracle.jdbc.driver;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.aq.AQDequeueOptions;
import oracle.jdbc.aq.AQEnqueueOptions;
import oracle.jdbc.aq.AQMessage;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.JMSDequeueOptions;
import oracle.jdbc.internal.JMSEnqueueOptions;
import oracle.jdbc.internal.JMSMessage;
import oracle.jdbc.internal.JMSMessageProperties;
import oracle.sql.TypeDescriptor;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIaqi.class */
class T4CTTIaqi implements Diagnosable {
    private static final String CLASS_NAME = T4CTTIaqi.class.getName();
    static final int AQIVER_DEFAULT = 1;
    static final int AQIVER_12_2 = 2;
    static final int AQIVER_12_1 = 1;
    static final int AQTTC_ENQ_STREAMING_DISABLED = 0;
    static final int AQTTC_ENQ_STREAMING_ENABLED = 1;
    static final int AQTCC_OCI_ONE_PIECE = 0;
    static final int AQTCC_OCI_FIRST_PIECE = 1;
    static final int AQTCC_OCI_NEXT_PIECE = 2;
    static final int AQTCC_OCI_LAST_PIECE = 3;
    T4CConnection connection;
    T4CMAREngine meg;
    T4CTTIaqm aqm;
    T4Ctoh toh;
    private AQMessagePropertiesI messageProperties;
    private JMSEnqueueOptions jmsEnqueueOptions;
    private JMSMessageProperties jmsProp;
    private JMSDequeueOptions jmsDequeueOptions;
    private AQEnqueueOptions aqEnqueueOptions;
    private AQDequeueOptions aqDequeueOptions;
    private int aqxaqopt;
    private boolean isAQMsg;
    private byte[] aqmcorBytes;
    private byte[] aqmeqnBytes;
    private byte[] senderAgentName;
    private byte[] senderAgentAddress;
    private byte senderAgentProtocol;
    private byte[] queueNameBytes;
    private AQAgentI[] attrRecipientList;
    private byte[][] recipientTextValues;
    private byte[][] recipientBinaryValues;
    private int[] recipientKeywords;
    private byte[] consumerNameBytes;
    private byte[] correlationBytes;
    private byte[] conditionBytes;
    private int nbExtensions;
    private byte[][] extensionTextValues;
    private byte[][] extensionBinaryValues;
    private int[] extensionKeywords;
    private byte[] messageOid;
    private int aqiver;
    private byte[] messageData;
    private boolean isRawQueue;
    private boolean isJsonQueue;
    private boolean bStreamingMode;
    private int blockSize;
    private InputStream payloadStream;
    private int bitMappedEnqueueOption;
    private byte[] headerPropBytes;
    private byte[] userPropBytes;
    private long aqiflg;
    private boolean useEnqOpt;
    private boolean lcrx2y;
    private T4CTTIaqjms aqjms;

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.connection.getDiagnosable();
    }

    T4CTTIaqi(T4CConnection _connection, int _aqxaqopt, String _queueName, JMSEnqueueOptions _enqueueOptions, JMSMessage _mesg, AQMessagePropertiesI _messageProperties, JMSMessageProperties _jmsProp, JMSDequeueOptions _jmsDequeueOpt) throws SQLException, IOException {
        this.messageProperties = null;
        this.jmsEnqueueOptions = null;
        this.jmsProp = null;
        this.jmsDequeueOptions = null;
        this.aqEnqueueOptions = null;
        this.aqDequeueOptions = null;
        this.aqxaqopt = 0;
        this.isAQMsg = false;
        this.senderAgentName = null;
        this.senderAgentAddress = null;
        this.senderAgentProtocol = (byte) 0;
        this.queueNameBytes = null;
        this.attrRecipientList = null;
        this.recipientTextValues = (byte[][]) null;
        this.recipientBinaryValues = (byte[][]) null;
        this.recipientKeywords = null;
        this.consumerNameBytes = null;
        this.correlationBytes = null;
        this.conditionBytes = null;
        this.nbExtensions = 0;
        this.extensionTextValues = (byte[][]) null;
        this.extensionBinaryValues = (byte[][]) null;
        this.extensionKeywords = null;
        this.messageOid = null;
        this.aqiver = 1;
        this.messageData = null;
        this.isRawQueue = false;
        this.isJsonQueue = false;
        this.bStreamingMode = false;
        this.blockSize = 8192;
        this.payloadStream = null;
        this.bitMappedEnqueueOption = 0;
        this.headerPropBytes = null;
        this.userPropBytes = null;
        this.aqiflg = 0L;
        this.useEnqOpt = false;
        this.lcrx2y = false;
        this.isAQMsg = false;
        initCommon(_connection, _aqxaqopt, _queueName, _messageProperties);
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "<init>", "_aqxaqopt={0}, _queueName={1}, _enqueueOptions={2}, _mesg={3}, _messageProperties={4}, _jmsProp={5}, _jmsDequeueOpt={6}", (String) null, (Throwable) null, Integer.valueOf(_aqxaqopt), _queueName, _enqueueOptions, _mesg, _messageProperties, _jmsProp, _jmsDequeueOpt);
        this.aqjms = new T4CTTIaqjms(_connection);
        this.jmsProp = _jmsProp;
        this.isRawQueue = true;
        if (this.aqxaqopt == 1) {
            this.jmsEnqueueOptions = _enqueueOptions;
            if (_mesg.getStreamPayload() == null) {
                setStreamingMode(false);
                setInputStream(null);
                this.messageData = _mesg.getPayload();
            } else {
                setStreamingMode(true);
                setBlockSize(_mesg.getChunkSize());
                setInputStream(_mesg.getStreamPayload());
                this.messageData = null;
            }
            this.messageOid = _mesg.getToid();
            this.bitMappedEnqueueOption = _enqueueOptions.getDeliveryMode().getCode() + _enqueueOptions.getVisibility().getCode();
            if (this.jmsProp != null) {
                this.headerPropBytes = this.meg.conv.StringToCharBytes(this.jmsProp.getHeaderProperties());
                this.userPropBytes = this.meg.conv.StringToCharBytes(this.jmsProp.getUserProperties());
            } else {
                this.headerPropBytes = null;
                this.userPropBytes = null;
            }
        } else {
            this.jmsDequeueOptions = _jmsDequeueOpt;
            this.messageOid = TypeDescriptor.RAWTOID;
            String consumerNameStr = this.jmsDequeueOptions.getConsumerName();
            if (consumerNameStr != null && consumerNameStr.length() > 0) {
                this.consumerNameBytes = this.meg.conv.StringToCharBytes(consumerNameStr);
            } else {
                this.consumerNameBytes = null;
            }
            String correlation = this.jmsDequeueOptions.getCorrelation();
            if (correlation != null && correlation.length() != 0) {
                this.correlationBytes = this.meg.conv.StringToCharBytes(correlation);
            } else {
                this.correlationBytes = null;
            }
            String condition = this.jmsDequeueOptions.getCondition();
            if (condition != null && condition.length() > 0) {
                this.conditionBytes = this.meg.conv.StringToCharBytes(condition);
            } else {
                this.conditionBytes = null;
            }
        }
        initVersion();
        initFlag();
    }

    /* JADX WARN: Type inference failed for: r1v72, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v75, types: [byte[], byte[][]] */
    T4CTTIaqi(T4CConnection _connection, int _aqxaqopt, String _queueName, AQEnqueueOptions _aqEnqueueOptions, AQMessage _mesg, AQMessagePropertiesI _messageProperties, AQDequeueOptions _aqDequeueOptions, byte[] _tdo, int _version) throws SQLException, IOException {
        String transformation;
        this.messageProperties = null;
        this.jmsEnqueueOptions = null;
        this.jmsProp = null;
        this.jmsDequeueOptions = null;
        this.aqEnqueueOptions = null;
        this.aqDequeueOptions = null;
        this.aqxaqopt = 0;
        this.isAQMsg = false;
        this.senderAgentName = null;
        this.senderAgentAddress = null;
        this.senderAgentProtocol = (byte) 0;
        this.queueNameBytes = null;
        this.attrRecipientList = null;
        this.recipientTextValues = (byte[][]) null;
        this.recipientBinaryValues = (byte[][]) null;
        this.recipientKeywords = null;
        this.consumerNameBytes = null;
        this.correlationBytes = null;
        this.conditionBytes = null;
        this.nbExtensions = 0;
        this.extensionTextValues = (byte[][]) null;
        this.extensionBinaryValues = (byte[][]) null;
        this.extensionKeywords = null;
        this.messageOid = null;
        this.aqiver = 1;
        this.messageData = null;
        this.isRawQueue = false;
        this.isJsonQueue = false;
        this.bStreamingMode = false;
        this.blockSize = 8192;
        this.payloadStream = null;
        this.bitMappedEnqueueOption = 0;
        this.headerPropBytes = null;
        this.userPropBytes = null;
        this.aqiflg = 0L;
        this.useEnqOpt = false;
        this.lcrx2y = false;
        this.isAQMsg = true;
        initCommon(_connection, _aqxaqopt, _queueName, _messageProperties);
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "<init>", "_aqxaqopt={0}, _queueName={1}, _aqEnqueueOptions={2}, _mesg={3}, _messageProperties={4}, _aqDequeueOptions={5}, _version={6}", (String) null, (Throwable) null, Integer.valueOf(_aqxaqopt), _queueName, _aqEnqueueOptions, _mesg, _messageProperties, _aqDequeueOptions, Integer.valueOf(_version));
        if (this.aqxaqopt == 1) {
            this.aqEnqueueOptions = _aqEnqueueOptions;
            AQMessageI imesg = (AQMessageI) _mesg;
            this.messageData = imesg.getPayload();
            this.messageOid = imesg.getPayloadTOID();
            this.aqiver = imesg.getPayloadVersion();
            this.isRawQueue = imesg.isRAWPayload();
            this.isJsonQueue = AQMessageI.compareToid(this.messageOid, TypeDescriptor.JSONTOID);
            transformation = this.aqEnqueueOptions.getTransformation();
        } else {
            this.messageOid = _tdo;
            this.aqiver = _version;
            this.isRawQueue = AQMessageI.compareToid(_tdo, TypeDescriptor.RAWTOID);
            this.aqDequeueOptions = _aqDequeueOptions;
            this.isJsonQueue = AQMessageI.compareToid(this.messageOid, TypeDescriptor.JSONTOID);
            String consumerNameStr = this.aqDequeueOptions.getConsumerName();
            if (consumerNameStr != null && consumerNameStr.length() > 0) {
                this.consumerNameBytes = this.meg.conv.StringToCharBytes(consumerNameStr);
            } else {
                this.consumerNameBytes = null;
            }
            String correlation = this.aqDequeueOptions.getCorrelation();
            if (correlation != null && correlation.length() != 0) {
                this.correlationBytes = this.meg.conv.StringToCharBytes(correlation);
            } else {
                this.correlationBytes = null;
            }
            String condition = this.aqDequeueOptions.getCondition();
            if (condition != null && condition.length() > 0) {
                this.conditionBytes = this.meg.conv.StringToCharBytes(condition);
            } else {
                this.conditionBytes = null;
            }
            transformation = this.aqDequeueOptions.getTransformation();
        }
        if (transformation != null && transformation.length() > 0) {
            this.nbExtensions = 1;
            this.extensionTextValues = new byte[this.nbExtensions];
            this.extensionBinaryValues = new byte[this.nbExtensions];
            this.extensionKeywords = new int[this.nbExtensions];
            this.extensionTextValues[0] = this.meg.conv.StringToCharBytes(transformation);
            this.extensionBinaryValues[0] = null;
            this.extensionKeywords[0] = 196;
        } else {
            this.nbExtensions = 0;
        }
        initFlag();
    }

    /* JADX WARN: Type inference failed for: r1v33, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v38, types: [byte[], byte[][]] */
    void initCommon(T4CConnection _connection, int _aqxaqopt, String _queueName, AQMessagePropertiesI _messageProperties) throws SQLException, IOException {
        this.connection = _connection;
        this.meg = this.connection.getMarshalEngine();
        this.aqxaqopt = _aqxaqopt;
        this.toh = new T4Ctoh(_connection);
        this.aqm = new T4CTTIaqm(this.connection, this.toh);
        this.messageProperties = _messageProperties;
        if (this.aqxaqopt == 1 && this.messageProperties != null) {
            String aqmcor = this.messageProperties.getCorrelation();
            if (aqmcor != null && aqmcor.length() != 0) {
                this.aqmcorBytes = this.meg.conv.StringToCharBytes(aqmcor);
            } else {
                this.aqmcorBytes = null;
            }
            String aqmeqn = this.messageProperties.getExceptionQueue();
            if (aqmeqn != null && aqmeqn.length() != 0) {
                this.aqmeqnBytes = this.meg.conv.StringToCharBytes(aqmeqn);
            } else {
                this.aqmeqnBytes = null;
            }
            AQAgentI senderAgent = (AQAgentI) this.messageProperties.getSender();
            if (senderAgent != null) {
                if (senderAgent.getName() != null) {
                    this.senderAgentName = this.meg.conv.StringToCharBytes(senderAgent.getName());
                } else {
                    this.senderAgentName = null;
                }
                if (senderAgent.getAddress() != null) {
                    this.senderAgentAddress = this.meg.conv.StringToCharBytes(senderAgent.getAddress());
                } else {
                    this.senderAgentAddress = null;
                }
                this.senderAgentProtocol = (byte) senderAgent.getProtocol();
            } else {
                this.senderAgentName = null;
                this.senderAgentAddress = null;
                this.senderAgentProtocol = (byte) 0;
            }
            this.attrRecipientList = (AQAgentI[]) this.messageProperties.getRecipientList();
            if (this.attrRecipientList != null && this.attrRecipientList.length > 0) {
                this.recipientTextValues = new byte[this.attrRecipientList.length * 3];
                this.recipientBinaryValues = new byte[this.attrRecipientList.length * 3];
                this.recipientKeywords = new int[this.attrRecipientList.length * 3];
                for (int i = 0; i < this.attrRecipientList.length; i++) {
                    if (this.attrRecipientList[i].getName() != null) {
                        this.recipientTextValues[3 * i] = this.meg.conv.StringToCharBytes(this.attrRecipientList[i].getName());
                    }
                    if (this.attrRecipientList[i].getAddress() != null) {
                        this.recipientTextValues[(3 * i) + 1] = this.meg.conv.StringToCharBytes(this.attrRecipientList[i].getAddress());
                    }
                    this.recipientBinaryValues[(3 * i) + 2] = new byte[1];
                    this.recipientBinaryValues[(3 * i) + 2][0] = (byte) this.attrRecipientList[i].getProtocol();
                    this.recipientKeywords[3 * i] = 3 * i;
                    this.recipientKeywords[(3 * i) + 1] = (3 * i) + 1;
                    this.recipientKeywords[(3 * i) + 2] = (3 * i) + 2;
                }
            }
        } else {
            this.aqmcorBytes = null;
            this.aqmeqnBytes = null;
            this.senderAgentName = null;
            this.senderAgentAddress = null;
            this.senderAgentProtocol = (byte) 0;
        }
        if (_queueName != null && _queueName.length() != 0) {
            this.queueNameBytes = this.meg.conv.StringToCharBytes(_queueName);
        } else {
            this.queueNameBytes = null;
        }
    }

    void initVersion() {
        try {
            if (this.connection.getVersionNumber() >= 12200 && TypeDescriptor.isV2available(this.messageOid)) {
                this.aqiver = 2;
            } else {
                this.aqiver = 1;
            }
        } catch (Exception e) {
            this.aqiver = 1;
        }
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "initVersion", "aqiver={0}", (String) null, (String) null, (Object) Integer.valueOf(this.aqiver));
    }

    void initFlag() {
        this.aqiflg = 0L;
        if (this.aqxaqopt == 1) {
            if (this.connection.autocommit) {
                this.aqiflg = 32L;
            }
            if ((!this.isAQMsg && this.jmsEnqueueOptions.getDeliveryMode() == JMSEnqueueOptions.DeliveryMode.BUFFERED) || (this.isAQMsg && this.aqEnqueueOptions.getDeliveryMode() == AQEnqueueOptions.DeliveryMode.BUFFERED)) {
                this.aqiflg |= 2;
            }
            if (this.bStreamingMode) {
                this.aqiflg |= 1;
            } else {
                this.aqiflg |= 0;
            }
        } else {
            if (this.connection.autocommit) {
                this.aqiflg = 2L;
            } else {
                this.aqiflg = 1L;
            }
            if (this.isAQMsg) {
                if (this.aqDequeueOptions.getDeliveryFilter() == AQDequeueOptions.DeliveryFilter.BUFFERED) {
                    this.aqiflg |= 2;
                } else if (this.aqDequeueOptions.getDeliveryFilter() == AQDequeueOptions.DeliveryFilter.PERSISTENT_OR_BUFFERED) {
                    this.aqiflg |= 16;
                }
            } else {
                this.aqiflg |= this.jmsDequeueOptions.getDeliveryMode().getCode();
                this.aqiflg |= this.jmsDequeueOptions.getDequeueMode().getCode();
                this.aqiflg |= this.jmsDequeueOptions.getVisibility().getCode();
                if (this.jmsDequeueOptions.getWait() == 4) {
                    this.aqiflg |= 4;
                }
            }
        }
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "initFlag", "aqiflg={0}", (String) null, (String) null, (Object) Long.valueOf(this.aqiflg));
    }

    void marshalPropagation() throws IOException {
        byte[] attrRelativeMessageId = this.isAQMsg ? this.aqEnqueueOptions.getRelativeMessageId() : null;
        this.meg.marshalDALC(attrRelativeMessageId);
        if (attrRelativeMessageId != null) {
            if (this.isAQMsg) {
                this.meg.marshalSB4(this.aqEnqueueOptions.getSequenceDeviation().getCode());
            } else {
                this.meg.marshalSB4(0);
            }
        }
    }

    void marshalHeader() throws IOException {
        this.meg.marshalUB1((short) 6);
        if (this.queueNameBytes != null && this.queueNameBytes.length != 0) {
            this.meg.marshalSWORD(this.queueNameBytes.length);
            this.meg.marshalCHR(this.queueNameBytes);
        } else {
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalB1Array(this.messageOid);
        this.meg.marshalUB2(this.aqiver);
        this.meg.marshalUB4(this.aqiflg);
        if (this.useEnqOpt) {
            if (this.isAQMsg) {
                this.meg.marshalSB4(this.aqEnqueueOptions.getVisibility().getCode());
            } else {
                this.meg.marshalSB4(this.jmsEnqueueOptions.getVisibility().getCode());
            }
            byte[] attrRelativeMessageId = this.isAQMsg ? this.aqEnqueueOptions.getRelativeMessageId() : null;
            if (attrRelativeMessageId != null && attrRelativeMessageId.length > 0) {
                this.meg.marshalSWORD(attrRelativeMessageId.length);
                this.meg.marshalB1Array(attrRelativeMessageId);
            } else {
                this.meg.marshalSWORD(0);
            }
            if (this.isAQMsg) {
                this.meg.marshalSB4(this.aqEnqueueOptions.getSequenceDeviation().getCode());
            } else {
                this.meg.marshalSB4(0);
            }
            if (this.nbExtensions > 0) {
                this.meg.marshalSWORD(this.nbExtensions);
                this.meg.marshalKPDKV(this.extensionTextValues, this.extensionBinaryValues, this.extensionKeywords);
            } else {
                this.meg.marshalSWORD(0);
            }
            this.meg.marshalSB4(0);
        }
    }

    void marshalData() throws IOException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshalData", "aqiflg={0}, lcrx2y={1}, useEnqOpt={2}, isRawQueue={3}", (String) null, (Throwable) null, Long.valueOf(this.aqiflg), Boolean.valueOf(this.lcrx2y), Boolean.valueOf(this.useEnqOpt), Boolean.valueOf(this.isRawQueue));
        this.meg.marshalUB1((short) 7);
        this.meg.marshalUB4(this.aqiflg);
        if (!this.lcrx2y) {
            marshalAQM();
        }
        AQAgentI[] attrRecipientList = (AQAgentI[]) this.messageProperties.getRecipientList();
        if (attrRecipientList != null && attrRecipientList.length > 0) {
            this.meg.marshalSWORD(attrRecipientList.length * 3);
            this.meg.marshalKPDKV(this.recipientTextValues, this.recipientBinaryValues, this.recipientKeywords);
        } else {
            this.meg.marshalSWORD(0);
        }
        if (!this.useEnqOpt) {
            this.meg.marshalSB4(this.aqEnqueueOptions.getVisibility().getCode());
            byte[] attrRelativeMessageId = this.aqEnqueueOptions.getRelativeMessageId();
            if (attrRelativeMessageId != null && attrRelativeMessageId.length > 0) {
                this.meg.marshalSWORD(attrRelativeMessageId.length);
                this.meg.marshalB1Array(attrRelativeMessageId);
            } else {
                this.meg.marshalSWORD(0);
            }
            this.meg.marshalSB4(this.aqEnqueueOptions.getSequenceDeviation().getCode());
        }
        if (this.messageData != null) {
            if (this.isJsonQueue) {
                byte[] quasiLocator = this.connection.setupJsonQuasiLocator(this.messageData.length);
                this.meg.marshalUB4(quasiLocator.length);
                this.meg.marshalB1Array(quasiLocator);
                if (this.connection.isZeroCopyIOEnabled(quasiLocator)) {
                    this.meg.writeZeroCopyIO(this.messageData, 0, this.messageData.length);
                    return;
                } else {
                    this.meg.marshalCLR(this.messageData, 0, this.messageData.length);
                    return;
                }
            }
            if (!this.isRawQueue) {
                this.toh.init(this.messageOid, this.messageData.length);
                this.toh.marshal(this.meg);
                this.meg.marshalCLR(this.messageData, 0, this.messageData.length);
                return;
            } else {
                this.meg.marshalUB4(this.messageData.length);
                this.meg.marshalB1Array(this.messageData);
                return;
            }
        }
        this.meg.marshalUB4(0L);
    }

    void marshalJmsData() throws IOException {
        this.meg.marshalUB1((short) 7);
        marshalAQM();
        int bitMappedEnqueueOption = this.jmsEnqueueOptions.getDeliveryMode().getCode() + this.jmsEnqueueOptions.getVisibility().getCode();
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshalJmsData", "bitMappedEnqueueOption={0},", (String) null, (String) null, (Object) Integer.valueOf(bitMappedEnqueueOption));
        this.meg.marshalSB4(bitMappedEnqueueOption);
        if (this.jmsProp != null) {
            this.aqjms.aqjmsflags = this.jmsProp.getJMSMessageType().getCode();
            this.aqjms.aqjmshdrpcnt = 0;
            this.aqjms.aqjmsusrprpcnt = 0;
        } else {
            this.aqjms.aqjmsflags = 0;
            this.aqjms.aqjmshdrpcnt = 0;
            this.aqjms.aqjmsusrprpcnt = 0;
        }
        this.aqjms.aqjmshdrprop = this.headerPropBytes;
        this.aqjms.aqjmsuserprop = this.userPropBytes;
        this.aqjms.marshal();
        if (this.messageOid != null) {
            this.meg.marshalSWORD(16);
            this.meg.marshalB1Array(this.messageOid);
        } else {
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalUB2(this.aqiver);
        if (this.messageData != null) {
            this.meg.marshalUB4(this.messageData.length);
            this.meg.marshalB1Array(this.messageData);
        } else {
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalSB4((int) this.aqiflg);
    }

    void marshalAQM() throws IOException {
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshalAQM", "senderAgentName={0}, senderAgentAddress={1}, senderAgentProtocol{2}", (String) null, (Throwable) null, this.senderAgentName, this.senderAgentAddress, Byte.valueOf(this.senderAgentProtocol));
        this.aqm.initToDefaultValues();
        if (this.messageProperties != null) {
            this.aqm.aqmpri = this.messageProperties.getPriority();
            this.aqm.aqmdel = this.messageProperties.getDelay();
            this.aqm.aqmexp = this.messageProperties.getExpiration();
            this.aqm.originalMsgId = this.messageProperties.getPreviousQueueMessageId();
            this.aqm.aqmshardNum = this.messageProperties.getShardNum();
        }
        this.aqm.aqmcorBytes = this.aqmcorBytes;
        this.aqm.aqmeqnBytes = this.aqmeqnBytes;
        this.aqm.senderAgentName = this.senderAgentName;
        this.aqm.senderAgentAddress = this.senderAgentAddress;
        this.aqm.senderAgentProtocol = this.senderAgentProtocol;
        this.aqm.marshal();
    }

    void marshalDone() throws IOException {
        this.meg.marshalUB1((short) 9);
    }

    void marshal() throws IOException {
        long aqiflg;
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "marshal", "aqxaqopt={0}, isAQMsg={1}, queueNameBytes={2},isRawQueue={3}", (String) null, (Throwable) null, Integer.valueOf(this.aqxaqopt), Boolean.valueOf(this.isAQMsg), this.queueNameBytes, Boolean.valueOf(this.isRawQueue));
        this.meg.marshalDALC(this.queueNameBytes);
        marshalAQM();
        AQAgentI[] attrRecipientList = (AQAgentI[]) this.messageProperties.getRecipientList();
        if (attrRecipientList != null && attrRecipientList.length > 0) {
            this.meg.marshalSWORD(attrRecipientList.length * 3);
            this.meg.marshalKPDKV(this.recipientTextValues, this.recipientBinaryValues, this.recipientKeywords);
        } else {
            this.meg.marshalSWORD(0);
        }
        if (this.aqxaqopt == 2) {
            this.meg.marshalDALC(this.consumerNameBytes);
        } else {
            this.meg.marshalSWORD(0);
        }
        if (this.aqxaqopt == 2) {
            if (this.isAQMsg) {
                this.meg.marshalSB4(this.aqDequeueOptions.getDequeueMode().getCode());
            } else {
                this.meg.marshalSB4(this.jmsDequeueOptions.getDequeueMode().getCode());
            }
        } else {
            this.meg.marshalSB4(0);
        }
        if (this.isAQMsg && this.aqxaqopt == 2) {
            this.meg.marshalSB4(this.aqDequeueOptions.getNavigation().getCode());
        } else {
            this.meg.marshalSB4(0);
        }
        if (this.aqxaqopt == 1) {
            if (this.isAQMsg) {
                this.meg.marshalSB4(this.aqEnqueueOptions.getVisibility().getCode());
            } else {
                this.meg.marshalSB4(this.jmsEnqueueOptions.getVisibility().getCode());
            }
        } else if (this.isAQMsg) {
            this.meg.marshalSB4(this.aqDequeueOptions.getVisibility().getCode());
        } else {
            this.meg.marshalSB4(this.jmsDequeueOptions.getVisibility().getCode());
        }
        if (this.aqxaqopt == 2) {
            if (this.isAQMsg) {
                this.meg.marshalSB4(this.aqDequeueOptions.getWait());
            } else {
                this.meg.marshalSB4(this.jmsDequeueOptions.getWait());
            }
        } else {
            this.meg.marshalSB4(0);
        }
        byte[] mesgId = null;
        if (this.aqxaqopt == 2) {
            if (this.isAQMsg) {
                mesgId = this.aqDequeueOptions.getDequeueMessageId();
            } else {
                mesgId = this.jmsDequeueOptions.getDequeueMessageId();
            }
        }
        this.meg.marshalDALC(mesgId);
        if (this.aqxaqopt == 2) {
            this.meg.marshalDALC(this.correlationBytes);
        } else {
            this.meg.marshalSWORD(0);
        }
        if (this.connection.getTTCVersion() >= 1) {
            this.meg.marshalDALC(this.conditionBytes);
            this.meg.marshalSWORD(0);
        }
        byte[] attrRelativeMessageId = null;
        if (this.aqxaqopt == 1) {
            attrRelativeMessageId = this.isAQMsg ? this.aqEnqueueOptions.getRelativeMessageId() : null;
        }
        this.meg.marshalDALC(attrRelativeMessageId);
        if (this.aqxaqopt == 1 && this.isAQMsg) {
            this.meg.marshalSB4(this.aqEnqueueOptions.getSequenceDeviation().getCode());
        } else {
            this.meg.marshalSB4(0);
        }
        this.meg.marshalDALC(this.messageOid);
        this.meg.marshalUB2(this.aqiver);
        if (this.aqxaqopt == 1 && this.messageData != null) {
            if (!this.isRawQueue) {
                this.meg.marshalSWORD(this.messageData.length);
                this.toh.init(this.messageOid, this.messageData.length);
                this.toh.marshal(this.meg);
                this.meg.marshalCLR(this.messageData, 0, this.messageData.length);
                this.meg.marshalUB4(0L);
            } else {
                this.meg.marshalSWORD(0);
                this.meg.marshalDALC(this.messageData);
            }
        } else {
            this.meg.marshalSWORD(0);
            this.meg.marshalUB4(0L);
        }
        this.meg.marshalUB4(0L);
        long aqiflg2 = 0;
        if (this.aqxaqopt == 1) {
            if (this.connection.autocommit) {
                aqiflg2 = 32;
            }
            if ((!this.isAQMsg && this.jmsEnqueueOptions.getDeliveryMode() == JMSEnqueueOptions.DeliveryMode.BUFFERED) || (this.isAQMsg && this.aqEnqueueOptions.getDeliveryMode() == AQEnqueueOptions.DeliveryMode.BUFFERED)) {
                aqiflg2 |= 2;
            }
            if (this.bStreamingMode) {
                aqiflg = aqiflg2 | 1;
            } else {
                aqiflg = aqiflg2 | 0;
            }
        } else {
            if (this.connection.autocommit) {
                aqiflg = 2;
            } else {
                aqiflg = 1;
            }
            if (this.isAQMsg) {
                if (this.aqDequeueOptions.getDeliveryFilter() == AQDequeueOptions.DeliveryFilter.BUFFERED) {
                    aqiflg |= 2;
                } else if (this.aqDequeueOptions.getDeliveryFilter() == AQDequeueOptions.DeliveryFilter.PERSISTENT_OR_BUFFERED) {
                    aqiflg |= 16;
                }
            } else {
                aqiflg = aqiflg | this.jmsDequeueOptions.getDeliveryMode().getCode() | this.jmsDequeueOptions.getDequeueMode().getCode() | this.jmsDequeueOptions.getVisibility().getCode();
                if (this.jmsDequeueOptions.getWait() == 4) {
                    aqiflg |= 4;
                }
            }
        }
        this.meg.marshalUB4(aqiflg);
        if (this.nbExtensions > 0) {
            this.meg.marshalSWORD(this.nbExtensions);
            this.meg.marshalKPDKV(this.extensionTextValues, this.extensionBinaryValues, this.extensionKeywords);
        } else {
            this.meg.marshalSWORD(0);
        }
        this.meg.marshalSWORD(0);
        if (this.aqxaqopt == 1 && this.bStreamingMode) {
            writeStreamingPayload();
        }
    }

    private void setStreamingMode(boolean flag) {
        this.bStreamingMode = flag;
    }

    private void setBlockSize(int _blockSize) {
        if (_blockSize > 0) {
            this.blockSize = _blockSize;
        }
    }

    private void setInputStream(InputStream inputStream) {
        this.payloadStream = inputStream;
    }

    private void writeStreamingPayload() throws IOException {
        byte[] bytearray = new byte[this.blockSize];
        boolean firstTime = true;
        while (true) {
            int noOfBytesRead = this.payloadStream.read(bytearray);
            if (noOfBytesRead < this.blockSize) {
                writeLast(bytearray, noOfBytesRead);
                this.payloadStream.close();
                this.payloadStream = null;
                return;
            } else if (firstTime) {
                writeFirst(bytearray, noOfBytesRead);
                firstTime = false;
            } else {
                writeNext(bytearray, noOfBytesRead);
            }
        }
    }

    private void writeFirst(byte[] _byteArray, int length) throws IOException {
        this.meg.marshalUB1((short) 1);
        this.meg.marshalSB8(length);
        this.meg.marshalB1Array(_byteArray, 0, length);
    }

    private void writeNext(byte[] _byteArray, int length) throws IOException {
        this.meg.marshalUB1((short) 2);
        this.meg.marshalSB8(length);
        this.meg.marshalB1Array(_byteArray, 0, length);
    }

    private void writeLast(byte[] _byteArray, int length) throws IOException {
        if (length > 0) {
            this.meg.marshalUB1((short) 3);
            this.meg.marshalSB8(length);
            this.meg.marshalB1Array(_byteArray, 0, length);
        } else {
            this.meg.marshalUB1((short) 3);
            this.meg.marshalSB8(0L);
        }
    }

    boolean isRawQueue() {
        return this.isRawQueue;
    }

    boolean isJsonQueue() {
        return this.isJsonQueue;
    }
}
