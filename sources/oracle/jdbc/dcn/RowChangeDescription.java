package oracle.jdbc.dcn;

import java.util.EnumSet;
import oracle.jdbc.dcn.TableChangeDescription;
import oracle.sql.ROWID;

/* loaded from: ojdbc8.jar:oracle/jdbc/dcn/RowChangeDescription.class */
public interface RowChangeDescription {
    @Deprecated
    RowOperation getRowOperation();

    EnumSet<RowOperation> getRowOperations();

    ROWID getRowid();

    /* loaded from: ojdbc8.jar:oracle/jdbc/dcn/RowChangeDescription$RowOperation.class */
    public enum RowOperation {
        INSERT(TableChangeDescription.TableOperation.INSERT.getCode()),
        UPDATE(TableChangeDescription.TableOperation.UPDATE.getCode()),
        DELETE(TableChangeDescription.TableOperation.DELETE.getCode());

        private final int code;

        RowOperation(int _code) {
            this.code = _code;
        }

        public final int getCode() {
            return this.code;
        }

        @Deprecated
        public static final RowOperation getRowOperation(int code) {
            if ((code & INSERT.getCode()) != 0) {
                return INSERT;
            }
            if ((code & UPDATE.getCode()) != 0) {
                return UPDATE;
            }
            if ((code & DELETE.getCode()) != 0) {
                return DELETE;
            }
            return DELETE;
        }

        public static final EnumSet<RowOperation> getRowOperations(int code) {
            EnumSet<RowOperation> ret = EnumSet.noneOf(RowOperation.class);
            if ((code & INSERT.getCode()) != 0) {
                ret.add(INSERT);
            }
            if ((code & UPDATE.getCode()) != 0) {
                ret.add(UPDATE);
            }
            if ((code & DELETE.getCode()) != 0) {
                ret.add(DELETE);
            }
            return ret;
        }
    }
}
