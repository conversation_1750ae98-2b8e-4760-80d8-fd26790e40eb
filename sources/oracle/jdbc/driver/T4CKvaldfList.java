package oracle.jdbc.driver;

import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CKvaldfList.class */
class T4CKvaldfList {
    static final int INTIAL_CAPACITY = 30;
    private int capacity;
    private int offset;
    private byte[][] keys;
    private byte[][] values;
    private byte[] flags;
    DBConversion conv;

    T4CKvaldfList(DBConversion _conv) {
        this.conv = _conv;
        initializeList();
    }

    /* JADX WARN: Type inference failed for: r1v4, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v7, types: [byte[], byte[][]] */
    void initializeList() {
        this.capacity = 30;
        this.offset = 0;
        this.keys = new byte[this.capacity];
        this.values = new byte[this.capacity];
        this.flags = new byte[this.capacity];
    }

    /* JADX WARN: Type inference failed for: r0v11, types: [byte[], byte[][], java.lang.Object] */
    /* JADX WARN: Type inference failed for: r0v15, types: [byte[], byte[][], java.lang.Object] */
    void add(byte[] key, byte[] value, byte flag) {
        if (this.offset == this.capacity) {
            ?? r0 = new byte[this.capacity * 2];
            ?? r02 = new byte[this.capacity * 2];
            byte[] flags2 = new byte[this.capacity * 2];
            System.arraycopy(this.keys, 0, r0, 0, this.capacity);
            System.arraycopy(this.values, 0, r02, 0, this.capacity);
            System.arraycopy(this.flags, 0, flags2, 0, this.capacity);
            this.keys = r0;
            this.values = r02;
            this.flags = flags2;
            this.capacity *= 2;
        }
        this.keys[this.offset] = key;
        this.values[this.offset] = value;
        byte[] bArr = this.flags;
        int i = this.offset;
        this.offset = i + 1;
        bArr[i] = flag;
    }

    void add(byte[] key, byte[] value) {
        add(key, value, (byte) 0);
    }

    void add(String key, byte[] value) throws SQLException {
        add(this.conv.StringToCharBytes(key), value, (byte) 0);
    }

    void add(String key, byte[] value, byte flag) throws SQLException {
        add(this.conv.StringToCharBytes(key), value, flag);
    }

    int size() {
        return this.offset;
    }

    byte[][] getKeys() {
        return this.keys;
    }

    byte[][] getValues() {
        return this.values;
    }

    byte[] getFlags() {
        return this.flags;
    }
}
