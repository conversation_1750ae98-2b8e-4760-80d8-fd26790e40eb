package oracle.jdbc.driver.json.binary;

import javax.json.JsonValue;
import oracle.jdbc.driver.json.tree.JsonpPrimitive;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/JsonpOsonValueFactory.class */
public class JsonpOsonValueFactory extends OsonValueFactory {
    public static JsonpOsonValueFactory INSTANCE = new JsonpOsonValueFactory();

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    Object createBinary(OsonContext ctx, int pos, int len, boolean isId) {
        byte[] raw = new byte[len];
        ctx.b.position(pos);
        ctx.b.get(raw);
        return new JsonpPrimitive.JsonpBinaryImpl(raw, isId);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OsonAbstractArray createArray(OsonContext ctx, int pos) {
        return new JsonpOsonArray(ctx, pos);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public OsonAbstractObject createObject(OsonContext ctx, int pos) {
        return new JsonpOsonObject(ctx, pos);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpStringImpl createString(OsonContext ctx, int pos, int len) {
        ctx.b.position(pos);
        return new JsonpPrimitive.JsonpStringImpl(ctx.b.readString(len));
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpNumberImpl createNumber(byte[] raw, OracleJsonDecimal.TargetType type) {
        return new JsonpPrimitive.JsonpNumberImpl(raw, type);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpStringNumberImpl createStringNumber(String value) {
        return new JsonpPrimitive.JsonpStringNumberImpl(value);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpDoubleImpl createDouble(double value) {
        return new JsonpPrimitive.JsonpDoubleImpl(value);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpTimestampImpl createTimestamp(byte[] raw) {
        return new JsonpPrimitive.JsonpTimestampImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createTimestampTZ(byte[] raw) {
        return new JsonpPrimitive.JsonpTimestampTZImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpFloatImpl createFloat(float flt) {
        return new JsonpPrimitive.JsonpFloatImpl(flt);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpDateImpl createDate(byte[] raw) {
        return new JsonpPrimitive.JsonpDateImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpIntervalYMImpl createIntervalYM(byte[] raw) {
        return new JsonpPrimitive.JsonpIntervalYMImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public JsonpPrimitive.JsonpIntervalDSImpl createIntervalDS(byte[] raw) {
        return new JsonpPrimitive.JsonpIntervalDSImpl(raw);
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createTrue() {
        return JsonValue.TRUE;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createFalse() {
        return JsonValue.FALSE;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createNull() {
        return JsonValue.NULL;
    }

    @Override // oracle.jdbc.driver.json.binary.OsonValueFactory
    Object createVector(OsonContext ctx, int pos, int len) {
        throw new UnsupportedOperationException(OracleJsonValue.OracleJsonType.VECTOR.toString());
    }
}
