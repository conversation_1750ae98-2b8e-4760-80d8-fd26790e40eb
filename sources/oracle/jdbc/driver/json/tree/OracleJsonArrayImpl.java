package oracle.jdbc.driver.json.tree;

import java.io.StringWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.sql.json.OracleJsonArray;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonException;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/OracleJsonArrayImpl.class */
public class OracleJsonArrayImpl implements OracleJsonArray {
    List<OracleJsonValue> list = new ArrayList();

    public OracleJsonArrayImpl() {
    }

    public OracleJsonArrayImpl(OracleJsonArray other) {
        for (OracleJsonValue value : other) {
            switch (value.getOracleJsonType()) {
                case OBJECT:
                    this.list.add(new OracleJsonObjectImpl(value.asJsonObject()));
                    break;
                case ARRAY:
                    this.list.add(new OracleJsonArrayImpl(value.asJsonArray()));
                    break;
                default:
                    this.list.add(value);
                    break;
            }
        }
    }

    @Override // oracle.sql.json.OracleJsonValue
    public OracleJsonValue.OracleJsonType getOracleJsonType() {
        return OracleJsonValue.OracleJsonType.ARRAY;
    }

    @Override // java.util.List, java.util.Collection
    public int size() {
        return this.list.size();
    }

    @Override // java.util.List, java.util.Collection
    public boolean isEmpty() {
        return this.list.isEmpty();
    }

    @Override // java.util.List, java.util.Collection
    public boolean contains(Object o) {
        return this.list.contains(o);
    }

    @Override // java.util.List, java.util.Collection, java.lang.Iterable
    public Iterator<OracleJsonValue> iterator() {
        return this.list.iterator();
    }

    @Override // java.util.List, java.util.Collection
    public Object[] toArray() {
        return this.list.toArray();
    }

    @Override // java.util.List, java.util.Collection
    public <T> T[] toArray(T[] tArr) {
        return (T[]) this.list.toArray(tArr);
    }

    @Override // java.util.List, java.util.Collection
    public boolean containsAll(Collection<?> c) {
        return this.list.containsAll(c);
    }

    @Override // java.util.List, java.util.Collection
    public void clear() {
        this.list.clear();
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // java.util.List
    public OracleJsonValue get(int index) {
        return this.list.get(index);
    }

    @Override // java.util.List
    public int indexOf(Object o) {
        return this.list.indexOf(o);
    }

    @Override // java.util.List
    public int lastIndexOf(Object o) {
        return this.list.lastIndexOf(o);
    }

    @Override // java.util.List
    public ListIterator<OracleJsonValue> listIterator() {
        return this.list.listIterator();
    }

    @Override // java.util.List
    public ListIterator<OracleJsonValue> listIterator(int index) {
        return this.list.listIterator(index);
    }

    @Override // oracle.sql.json.OracleJsonArray
    public <T extends OracleJsonValue> List<T> getValuesAs(Class<T> c) {
        return this;
    }

    @Override // oracle.sql.json.OracleJsonArray
    public String getString(int index) {
        return this.list.get(index).asJsonString().getString();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public int getInt(int index) {
        return ((OracleJsonNumber) this.list.get(index)).intValue();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public double getDouble(int index) {
        return ((OracleJsonNumber) this.list.get(index)).doubleValue();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public BigDecimal getBigDecimal(int index) {
        return ((OracleJsonNumber) this.list.get(index)).bigDecimalValue();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public long getLong(int index) {
        return ((OracleJsonNumber) this.list.get(index)).longValue();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public boolean getBoolean(int index) {
        OracleJsonValue v = this.list.get(index);
        if (v.equals(OracleJsonValue.TRUE)) {
            return true;
        }
        if (v.equals(OracleJsonValue.FALSE)) {
            return false;
        }
        throw new ClassCastException();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public boolean isNull(int index) {
        return OracleJsonValue.NULL.equals(this.list.get(index));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public LocalDateTime getLocalDateTime(int index) {
        OracleJsonValue value = this.list.get(index);
        if (value.getOracleJsonType() == OracleJsonValue.OracleJsonType.DATE) {
            return value.asJsonDate().getLocalDateTime();
        }
        return value.asJsonTimestamp().getLocalDateTime();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OffsetDateTime getOffsetDateTime(int index) {
        OracleJsonValue value = this.list.get(index);
        return value.asJsonTimestampTZ().getOffsetDateTime();
    }

    @Override // oracle.sql.json.OracleJsonArray
    public byte[] getBytes(int index) {
        return this.list.get(index).asJsonBinary().getBytes();
    }

    @Override // java.util.List
    public OracleJsonValue set(int index, OracleJsonValue element) {
        checkNull(element);
        return this.list.set(index, element);
    }

    @Override // java.util.List
    /* renamed from: subList, reason: merged with bridge method [inline-methods] */
    public List<OracleJsonValue> subList2(int fromIndex, int toIndex) {
        OracleJsonArray arr = new OracleJsonArrayImpl();
        for (int i = fromIndex; i < toIndex; i++) {
            arr.add((OracleJsonArray) get(i));
        }
        return arr;
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(double value) {
        add((OracleJsonValue) new OracleJsonDoubleImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(long value) {
        add((OracleJsonValue) new OracleJsonDecimalImpl(value, OracleJsonDecimal.TargetType.LONG));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(BigDecimal value) {
        add((OracleJsonValue) new OracleJsonDecimalImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(boolean value) {
        add(value ? OracleJsonValue.TRUE : OracleJsonValue.FALSE);
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(String value) {
        add((OracleJsonValue) new OracleJsonStringImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(int value) {
        add((OracleJsonValue) new OracleJsonDecimalImpl(value, OracleJsonDecimal.TargetType.INT));
    }

    @Override // java.util.List, java.util.Collection
    public boolean addAll(Collection<? extends OracleJsonValue> c) {
        for (OracleJsonValue v : c) {
            checkNull(v);
            this.list.add(v);
        }
        return c.size() > 0;
    }

    @Override // java.util.List
    public boolean addAll(int index, Collection<? extends OracleJsonValue> c) {
        for (OracleJsonValue v : c) {
            checkNull(v);
        }
        return this.list.addAll(index, c);
    }

    @Override // java.util.List, java.util.Collection
    public boolean removeAll(Collection<?> c) {
        return this.list.removeAll(c);
    }

    @Override // java.util.List, java.util.Collection
    public boolean retainAll(Collection<?> c) {
        return this.list.retainAll(c);
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, boolean value) {
        return this.list.set(index, value ? OracleJsonValue.TRUE : OracleJsonValue.FALSE);
    }

    @Override // java.util.List
    public void add(int index, OracleJsonValue element) {
        checkNull(element);
        this.list.add(index, element);
    }

    private void checkNull(OracleJsonValue element) {
        if (element == null) {
            throw new NullPointerException();
        }
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // java.util.List
    public OracleJsonValue remove(int index) {
        return this.list.remove(index);
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, String value) {
        return this.list.set(index, new OracleJsonStringImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, int value) {
        return this.list.set(index, new OracleJsonDecimalImpl(value, OracleJsonDecimal.TargetType.INT));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, double value) {
        return this.list.set(index, new OracleJsonDoubleImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, long value) {
        return this.list.set(index, new OracleJsonDecimalImpl(value, OracleJsonDecimal.TargetType.LONG));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, BigDecimal value) throws OracleJsonException {
        return this.list.set(index, new OracleJsonDecimalImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue setNull(int index) {
        return this.list.set(index, OracleJsonValue.NULL);
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void addNull() {
        this.list.add(OracleJsonValue.NULL);
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, LocalDateTime value) {
        return this.list.set(index, new OracleJsonTimestampImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, OffsetDateTime value) {
        return this.list.set(index, new OracleJsonTimestampTZImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(LocalDateTime value) {
        this.list.add(new OracleJsonTimestampImpl(value));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(OffsetDateTime value) {
        this.list.add(new OracleJsonTimestampTZImpl(value));
    }

    @Override // java.util.List, java.util.Collection
    public boolean remove(Object o) {
        return this.list.remove(o);
    }

    @Override // oracle.sql.json.OracleJsonArray
    public OracleJsonValue set(int index, byte[] value) {
        return this.list.set(index, new OracleJsonBinaryImpl(value, false));
    }

    @Override // oracle.sql.json.OracleJsonArray
    public void add(byte[] value) {
        this.list.add(new OracleJsonBinaryImpl(value, false));
    }

    @Override // java.util.List, java.util.Collection
    public boolean add(OracleJsonValue e) {
        checkNull(e);
        return this.list.add(e);
    }

    @Override // oracle.sql.json.OracleJsonValue
    public String toString() {
        StringWriter writer = new StringWriter();
        JsonSerializerImpl ser = new JsonSerializerImpl(writer);
        ser.write(this);
        ser.close();
        return writer.toString();
    }

    @Override // java.util.List, java.util.Collection
    public boolean equals(Object other) {
        return this.list.equals(other);
    }

    @Override // java.util.List, java.util.Collection
    public int hashCode() {
        return this.list.hashCode();
    }

    @Override // oracle.sql.json.OracleJsonValue
    public <T> T wrap(Class<T> c) {
        if (Jsonp.isJakartaJson(c)) {
            return c.cast(new JakartaArrayImpl(this));
        }
        return c.cast(new JsonpArrayImpl(this));
    }
}
