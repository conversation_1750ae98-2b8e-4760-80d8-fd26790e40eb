package oracle.jdbc.driver.json.tree;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Wrapper;
import javax.json.JsonNumber;
import javax.json.JsonString;
import javax.json.JsonValue;
import oracle.jdbc.driver.json.JsonpExceptionFactory;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.binary.JsonSerializerImpl;
import oracle.jdbc.driver.json.binary.OsonPrimitiveConversions;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive.class */
public final class JsonpPrimitive {

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpNumberImpl.class */
    public static class JsonpNumberImpl extends DefaultJsonNumber {
        byte[] raw;
        OracleJsonDecimal.TargetType type;

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public /* bridge */ /* synthetic */ String getString() {
            return super.getString();
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ boolean isWrapperFor(Class cls) throws SQLException {
            return super.isWrapperFor(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ Object unwrap(Class cls) throws SQLException {
            return super.unwrap(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber
        public /* bridge */ /* synthetic */ JsonValue.ValueType getValueType() {
            return super.getValueType();
        }

        public JsonpNumberImpl(byte[] raw, OracleJsonDecimal.TargetType type) {
            super();
            this.raw = raw;
            this.type = type;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public final BigDecimal bigDecimalValue() {
            return OsonPrimitiveConversions.toBigDecimal(this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonDecimalImpl(this.raw, this.type);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpFloatImpl.class */
    public static class JsonpFloatImpl extends DefaultJsonNumber {
        private float value;

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public /* bridge */ /* synthetic */ String getString() {
            return super.getString();
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ boolean isWrapperFor(Class cls) throws SQLException {
            return super.isWrapperFor(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ Object unwrap(Class cls) throws SQLException {
            return super.unwrap(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber
        public /* bridge */ /* synthetic */ JsonValue.ValueType getValueType() {
            return super.getValueType();
        }

        public JsonpFloatImpl(float value) {
            super();
            this.value = value;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public BigDecimal bigDecimalValue() {
            return BigDecimal.valueOf(this.value);
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public float floatValue() {
            return this.value;
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonFloatImpl(this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpDoubleImpl.class */
    public static class JsonpDoubleImpl extends DefaultJsonNumber {
        double value;

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public /* bridge */ /* synthetic */ String getString() {
            return super.getString();
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ boolean isWrapperFor(Class cls) throws SQLException {
            return super.isWrapperFor(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ Object unwrap(Class cls) throws SQLException {
            return super.unwrap(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber
        public /* bridge */ /* synthetic */ JsonValue.ValueType getValueType() {
            return super.getValueType();
        }

        public JsonpDoubleImpl(double value) {
            super();
            this.value = value;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public double doubleValue() {
            return this.value;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public BigDecimal bigDecimalValue() {
            return BigDecimal.valueOf(this.value);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonDoubleImpl(this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpStringImpl.class */
    public static class JsonpStringImpl implements DefaultJsonString {
        String value;

        public JsonpStringImpl(String value) {
            this.value = value;
        }

        public boolean equals(Object other) {
            return JsonpPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String getString() {
            return this.value;
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonStringImpl(this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpBinaryImpl.class */
    public static class JsonpBinaryImpl implements DefaultJsonString {
        byte[] bytes;
        boolean isId;

        public JsonpBinaryImpl(byte[] bytes, boolean isId) {
            this.bytes = bytes;
            this.isId = isId;
        }

        public boolean equals(Object other) {
            return JsonpPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OracleJsonBinaryImpl.getString(this.bytes, this.isId);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonBinaryImpl(this.bytes, this.isId);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpDateImpl.class */
    public static class JsonpDateImpl implements DefaultJsonString {
        byte[] raw;

        public JsonpDateImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JsonpPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.dateToString(JsonpExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonDateImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpTimestampImpl.class */
    public static class JsonpTimestampImpl implements DefaultJsonString {
        private byte[] raw;

        public JsonpTimestampImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JsonpPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.timestampToString(JsonpExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonTimestampImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpTimestampTZImpl.class */
    public static class JsonpTimestampTZImpl implements DefaultJsonString {
        private byte[] raw;

        public JsonpTimestampTZImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JsonpPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.timestampTZToString(JsonpExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonTimestampTZImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpIntervalDSImpl.class */
    public static class JsonpIntervalDSImpl implements DefaultJsonString {
        private byte[] raw;

        public JsonpIntervalDSImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JsonpPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.serializeIntervalDS(JsonpExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonIntervalDSImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpIntervalYMImpl.class */
    public static class JsonpIntervalYMImpl implements DefaultJsonString {
        byte[] raw;

        public JsonpIntervalYMImpl(byte[] raw) {
            this.raw = raw;
        }

        public boolean equals(Object other) {
            return JsonpPrimitive.stringsEqual(this, other);
        }

        public int hashCode() {
            return getString().hashCode();
        }

        public String toString() {
            return JsonSerializerImpl.serializeString(getString());
        }

        public String getString() {
            return OsonPrimitiveConversions.serializeIntervalYM(JsonpExceptionFactory.INSTANCE, this.raw);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonString
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonIntervalYMImpl(this.raw);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$JsonpStringNumberImpl.class */
    public static class JsonpStringNumberImpl extends DefaultJsonNumber {
        private String value;

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public /* bridge */ /* synthetic */ String getString() {
            return super.getString();
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ boolean isWrapperFor(Class cls) throws SQLException {
            return super.isWrapperFor(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber, java.sql.Wrapper
        public /* bridge */ /* synthetic */ Object unwrap(Class cls) throws SQLException {
            return super.unwrap(cls);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber
        public /* bridge */ /* synthetic */ JsonValue.ValueType getValueType() {
            return super.getValueType();
        }

        public JsonpStringNumberImpl(String value) {
            super();
            this.value = value;
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public BigDecimal bigDecimalValue() {
            return new BigDecimal(this.value);
        }

        @Override // oracle.jdbc.driver.json.tree.JsonpPrimitive.DefaultJsonNumber
        public OracleJsonValue getUnwrapped() {
            return new OracleJsonStringNumberImpl(this.value);
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$DefaultJsonNumber.class */
    private static abstract class DefaultJsonNumber extends OracleJsonNumberImpl implements JsonNumber, Wrapper {
        abstract OracleJsonValue getUnwrapped();

        private DefaultJsonNumber() {
        }

        public JsonValue.ValueType getValueType() {
            return JsonValue.ValueType.NUMBER;
        }

        @Override // java.sql.Wrapper
        public <T> T unwrap(Class<T> cls) throws SQLException {
            T t = (T) getUnwrapped();
            if (cls.isInstance(t)) {
                return t;
            }
            throw new SQLException(OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, cls.getName()).getMessage());
        }

        @Override // java.sql.Wrapper
        public boolean isWrapperFor(Class<?> iface) throws SQLException {
            return iface.isInstance(getUnwrapped());
        }

        @Override // oracle.jdbc.driver.json.tree.OracleJsonNumberImpl
        public String getString() {
            try {
                return ((OracleJsonNumberImpl) unwrap(OracleJsonNumber.class)).getString();
            } catch (SQLException e) {
                throw new IllegalStateException();
            }
        }

        public final boolean equals(Object other) {
            if (!(other instanceof JsonNumber)) {
                return false;
            }
            return bigDecimalValue().equals(((JsonNumber) other).bigDecimalValue());
        }

        public final int hashCode() {
            return bigDecimalValue().hashCode();
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/tree/JsonpPrimitive$DefaultJsonString.class */
    private interface DefaultJsonString extends JsonString, Wrapper {
        OracleJsonValue getUnwrapped();

        default JsonValue.ValueType getValueType() {
            return JsonValue.ValueType.STRING;
        }

        @Override // java.sql.Wrapper
        default <T> T unwrap(Class<T> cls) throws SQLException {
            T t = (T) getUnwrapped();
            if (cls.isInstance(t)) {
                return t;
            }
            throw new SQLException(OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, cls.getName()).getMessage());
        }

        @Override // java.sql.Wrapper
        default boolean isWrapperFor(Class<?> iface) throws SQLException {
            return iface.isInstance(getUnwrapped());
        }

        default CharSequence getChars() {
            return getString();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static boolean stringsEqual(JsonString ths, Object other) {
        if (other instanceof JsonString) {
            return ths == other || ths.getString().equals(((JsonString) other).getString());
        }
        return false;
    }

    private JsonpPrimitive() {
    }
}
