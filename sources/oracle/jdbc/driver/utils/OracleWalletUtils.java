package oracle.jdbc.driver.utils;

import java.sql.SQLException;
import java.util.Enumeration;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.driver.DatabaseError;
import oracle.jdbc.internal.OpaqueString;
import oracle.jdbc.internal.OracleConnection;
import oracle.net.jdbc.nl.NVFactory;
import oracle.net.jdbc.nl.NVNavigator;
import oracle.net.jdbc.nl.NVPair;
import oracle.net.ns.NetException;
import oracle.net.nt.SSLConfig;
import oracle.security.pki.OracleSecretStore;
import oracle.security.pki.OracleWallet;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/utils/OracleWalletUtils.class */
public final class OracleWalletUtils {
    private static final String SECRET_STORE_CONNECT = "oracle.security.client.connect_string";
    private static final String SECRET_STORE_USERNAME = "oracle.security.client.username";
    private static final String SECRET_STORE_PASSWORD = "oracle.security.client.password";
    private static final String SECRET_STORE_DEFAULT_USERNAME = "oracle.security.client.default_username";
    private static final String SECRET_STORE_DEFAULT_PASSWORD = "oracle.security.client.default_password";
    private static final String CLASS_NAME = OracleWalletUtils.class.getName();

    private OracleWalletUtils() {
    }

    public static String[] getSecretStoreCredentials(String connectString, String walletLocation, OpaqueString walletPassword) throws SQLException {
        String[] userPwd = {null, null};
        try {
            if (walletLocation == null) {
                return userPwd;
            }
            try {
                try {
                    OracleWallet wallet = openWallet(walletLocation, walletPassword);
                    if (wallet != null) {
                        OracleSecretStore secretStore = wallet.getSecretStore();
                        if (secretStore.containsAlias(SECRET_STORE_DEFAULT_USERNAME)) {
                            userPwd[0] = new String(secretStore.getSecret(SECRET_STORE_DEFAULT_USERNAME));
                        }
                        if (secretStore.containsAlias(SECRET_STORE_DEFAULT_PASSWORD)) {
                            userPwd[1] = new String(secretStore.getSecret(SECRET_STORE_DEFAULT_PASSWORD));
                        }
                        Enumeration<String> list = secretStore.internalAliases();
                        while (true) {
                            if (!list.hasMoreElements()) {
                                break;
                            }
                            String alias = list.nextElement();
                            if (alias.startsWith(SECRET_STORE_CONNECT) && connectString.equalsIgnoreCase(new String(secretStore.getSecret(alias)))) {
                                String idx = alias.substring(SECRET_STORE_CONNECT.length());
                                userPwd[0] = new String(secretStore.getSecret(SECRET_STORE_USERNAME + idx));
                                userPwd[1] = new String(secretStore.getSecret(SECRET_STORE_PASSWORD + idx));
                                break;
                            }
                        }
                        CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getSecretStoreCredentials", "Searched {0} for SEPS credentials keyed to connection string {1}. Found username? {2}. Found password? {3}.", (String) null, null, walletLocation, connectString, userPwd[0] == null ? "NO" : "YES", userPwd[1] == null ? "NO" : "YES");
                        return userPwd;
                    }
                    CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getSecretStoreCredentials", "Searched {0} for SEPS credentials keyed to connection string {1}. Found username? {2}. Found password? {3}.", (String) null, null, walletLocation, connectString, userPwd[0] == null ? "NO" : "YES", userPwd[1] == null ? "NO" : "YES");
                    return userPwd;
                } catch (NoClassDefFoundError classNotFoundException) {
                    throw oraclePkiNotFound(classNotFoundException);
                }
            } catch (Exception exception) {
                throw secretStoreError(walletLocation, exception);
            }
        } catch (Throwable th) {
            CommonDiagnosable.getInstance().debug(Level.FINE, SecurityLabel.UNKNOWN, CLASS_NAME, "getSecretStoreCredentials", "Searched {0} for SEPS credentials keyed to connection string {1}. Found username? {2}. Found password? {3}.", (String) null, null, walletLocation, connectString, userPwd[0] == null ? "NO" : "YES", userPwd[1] == null ? "NO" : "YES");
            throw th;
        }
    }

    public static OpaqueString getSecret(String walletLocation, OpaqueString password, String secretName) throws Throwable {
        try {
            OracleWallet wallet = openWallet(walletLocation, OpaqueString.NULL);
            if (wallet == null) {
                throw secretStoreError(walletLocation, null);
            }
            OracleSecretStore secretStore = wallet.getSecretStore();
            return OpaqueString.newOpaqueString(secretStore.getSecret(secretName));
        } catch (Exception exception) {
            throw secretStoreError(walletLocation, exception);
        } catch (NoClassDefFoundError classNotFoundException) {
            throw oraclePkiNotFound(classNotFoundException);
        }
    }

    private static OracleWallet openWallet(String walletLocation, OpaqueString password) throws Throwable {
        OracleWallet wallet = new OracleWallet();
        String locationToURI = sqlNetLocationToURI(walletLocation);
        if (!wallet.exists(locationToURI)) {
            return null;
        }
        if (password == null) {
            password = OpaqueString.NULL;
        }
        password.peek(passwordChars -> {
            wallet.open(locationToURI, passwordChars);
        });
        return wallet;
    }

    private static String sqlNetLocationToURI(String walletLocation) throws NetException {
        return sqlNetLocationToURI(walletLocation, CommonDiagnosable.getInstance());
    }

    public static String sqlNetLocationToURI(String walletLocation, Diagnosable diagnosable) throws NetException {
        if (walletLocation.startsWith("(")) {
            return "file:" + parseWalletLocationDirectory(walletLocation, diagnosable);
        }
        return walletLocation;
    }

    public static String sqlNetLocationToUnixPath(String walletLocation, Diagnosable diagnosable) throws NetException {
        if (walletLocation.startsWith("(")) {
            return parseWalletLocationDirectory(walletLocation, diagnosable);
        }
        return walletLocation;
    }

    private static String parseWalletLocationDirectory(String walletLocation, Diagnosable diagnosable) throws NetException {
        try {
            NVNavigator nav = new NVNavigator();
            NVPair nvpWallet = new NVFactory().createNVPair(walletLocation);
            NVPair nvpMethod = nav.findNVPair(nvpWallet, "METHOD");
            NVPair nvpMethodData = nav.findNVPair(nvpWallet, "METHOD_DATA");
            NVPair nvpDirectory = nav.findNVPair(nvpMethodData, "DIRECTORY");
            String method = nvpMethod.getAtom();
            diagnosable.debug(Level.FINEST, SecurityLabel.UNKNOWN, CLASS_NAME, "processWalletLocation", "Wallet Parameter Configuration : Method {0}, Directory {1}", null, null, method, nvpDirectory.getAtom());
            if (!method.equalsIgnoreCase(SSLConfig.SUPPORTED_METHOD_TYPE)) {
                throw new NetException(NetException.UNSUPPORTED_METHOD_IN_WALLET_LOCATION, method);
            }
            return nvpDirectory.getAtom();
        } catch (Exception ex) {
            diagnosable.debug(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "processWalletLocation", "Error in parsing wallet location {0}", (String) null, (String) null, ex);
            throw ((NetException) new NetException(NetException.UNABLE_TO_PARSE_WALLET_LOCATION).initCause(ex));
        }
    }

    private static SQLException oraclePkiNotFound(Throwable cause) {
        return (SQLException) DatabaseError.createSqlException(167, cause).fillInStackTrace();
    }

    private static SQLException secretStoreError(String walletLocation, Exception cause) {
        String path;
        NetException invalidLocationString = null;
        try {
            path = sqlNetLocationToURI(walletLocation);
        } catch (NetException netException) {
            invalidLocationString = netException;
            path = walletLocation;
        }
        SQLException sqlException = DatabaseError.createSqlException((OracleConnection) null, 168, path, cause);
        if (invalidLocationString != null) {
            sqlException.addSuppressed(invalidLocationString);
        }
        sqlException.fillInStackTrace();
        return sqlException;
    }
}
