package oracle.jdbc.driver;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import oracle.jdbc.diagnostics.CommonDiagnosable;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.Monitor;
import oracle.jdbc.internal.OracleConnection;
import oracle.jdbc.logging.annotations.Blind;
import oracle.jdbc.logging.annotations.PropertiesBlinder;
import oracle.jdbc.pool.OracleDataSource;
import oracle.jdbc.replay.OracleDataSourceImpl;
import oracle.net.resolver.AddrResolution;
import oracle.ucp.jdbc.PoolDataSource;
import oracle.ucp.jdbc.PoolDataSourceFactory;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/ShardingConnectionUtil.class */
class ShardingConnectionUtil {
    private static final int DB_SHARDING_ENABLED = 1;
    private static final int DB_SHARD_CATALOG = 4;
    static PoolDataSource catalogDatabasePoolDataSource;
    static short dbCharSet;
    private static final String CLASS_NAME = ShardingConnectionUtil.class.getName();
    private static final Monitor shardingConnectionUtilLock = Monitor.newInstance();
    static ConcurrentHashMap<Integer, ShardingPoolDataSourceEntry> shardDatabasePoolDataSourceMap = new ConcurrentHashMap<>();
    static Diagnosable diagnosable = CommonDiagnosable.getInstance();

    ShardingConnectionUtil() {
    }

    static ShardingPoolDataSourceEntry getShardingDatabasePoolDataSource(String url, @Blind(PropertiesBlinder.class) Properties info, String gsmServiceName, boolean shardingReplayEnable, String resolvedUrl) throws SQLException {
        Monitor.CloseableLock lock = shardingConnectionUtilLock.acquireCloseableLock();
        Throwable th = null;
        try {
            Connection connection = null;
            try {
                int key = calculateConnectionInfoHashKey(url, info);
                if (shardDatabasePoolDataSourceMap.containsKey(Integer.valueOf(key))) {
                    ShardingPoolDataSourceEntry shardDatabasePoolDatasourceEntry = shardDatabasePoolDataSourceMap.get(Integer.valueOf(key));
                    if (0 != 0) {
                        connection.close();
                    }
                    return shardDatabasePoolDatasourceEntry;
                }
                PoolDataSource shardDatabasePoolDatasource = PoolDataSourceFactory.getPoolDataSource();
                if (shardingReplayEnable) {
                    shardDatabasePoolDatasource.setConnectionFactoryClassName(OracleDataSourceImpl.class.getName());
                } else {
                    shardDatabasePoolDatasource.setConnectionFactoryClassName(OracleDataSource.class.getName());
                }
                shardDatabasePoolDatasource.setURL(url);
                shardDatabasePoolDatasource.setConnectionProperties(info);
                shardDatabasePoolDatasource.setConnectionWaitTimeout(0);
                shardDatabasePoolDatasource.setCreateConnectionInBorrowThread(true);
                shardDatabasePoolDatasource.setValidateConnectionOnBorrow(true);
                shardDatabasePoolDatasource.setInactiveConnectionTimeout(120);
                shardDatabasePoolDatasource.setAbandonedConnectionTimeout(300);
                shardDatabasePoolDatasource.setMaxPoolSize(Integer.MAX_VALUE);
                OracleDataSource ds = new OracleDataSource();
                ds.setURL(url);
                ds.setConnectionProperties(info);
                Connection connection2 = ds.getConnection();
                ((oracle.jdbc.internal.OracleConnection) connection2).addFeature(OracleConnection.ClientFeature.SHARDING_DRIVER);
                short dbver = ((oracle.jdbc.internal.OracleConnection) connection2).getVersionNumber();
                if (dbver < 20000) {
                    throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_UNSUPPORTED_SHARDING_DATABASE_VERSION).fillInStackTrace());
                }
                if (catalogDatabasePoolDataSource == null) {
                    dbCharSet = ((oracle.jdbc.internal.OracleConnection) connection2).getDbCsId();
                    catalogDatabasePoolDataSource = getCatalogDatabasePoolDataSource(connection2, resolvedUrl == null ? url : resolvedUrl, info, gsmServiceName, shardingReplayEnable);
                } else {
                    validateConnectionToShardedDatabase(connection2);
                }
                ShardingPoolDataSourceEntry shardDatabasePoolDatasourceEntry2 = new ShardingPoolDataSourceEntry(shardDatabasePoolDatasource, ((oracle.jdbc.internal.OracleConnection) connection2).getUserName(), ((oracle.jdbc.internal.OracleConnection) connection2).getCurrentSchema(), ((oracle.jdbc.internal.OracleConnection) connection2).getServerSessionInfo());
                shardDatabasePoolDataSourceMap.put(Integer.valueOf(key), shardDatabasePoolDatasourceEntry2);
                diagnosable.trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "getShardingDatabasePoolDataSource", "create new pool datasource with key={0}", (String) null, (Throwable) null, Integer.valueOf(key));
                if (connection2 != null) {
                    connection2.close();
                }
                if (lock != null) {
                    if (0 != 0) {
                        try {
                            lock.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        lock.close();
                    }
                }
                return shardDatabasePoolDatasourceEntry2;
            } catch (Throwable th3) {
                if (0 != 0) {
                    connection.close();
                }
                throw th3;
            }
        } finally {
            if (lock != null) {
                if (0 != 0) {
                    try {
                        lock.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    lock.close();
                }
            }
        }
    }

    static int calculateConnectionInfoHashKey(String url, @Blind(PropertiesBlinder.class) Properties info) {
        int tempHashCode = url != null ? (31 * 1) + url.hashCode() : 1;
        if (info != null) {
            Set<String> keys = new TreeSet<>(info.stringPropertyNames());
            for (String key : keys) {
                String value = info.getProperty(key);
                tempHashCode = (31 * ((31 * tempHashCode) + key.hashCode())) + (value != null ? value.hashCode() : 0);
            }
        }
        return tempHashCode;
    }

    static PoolDataSource getCatalogDatabasePoolDataSource() throws SQLException {
        return catalogDatabasePoolDataSource;
    }

    static short getDbCharsSet() {
        return dbCharSet;
    }

    static PoolDataSource getCatalogDatabasePoolDataSource(Connection connection, String url, @Blind(PropertiesBlinder.class) Properties info, String gsmServiceName, boolean shardingReplayEnable) throws SQLException {
        String catalogServiceURL = getCatalogServiceUrl(connection, url, gsmServiceName);
        PoolDataSource pds = PoolDataSourceFactory.getPoolDataSource();
        if (shardingReplayEnable) {
            pds.setConnectionFactoryClassName(OracleDataSourceImpl.class.getName());
        } else {
            pds.setConnectionFactoryClassName(OracleDataSource.class.getName());
        }
        pds.setURL(catalogServiceURL);
        pds.setConnectionProperties(info);
        pds.setConnectionWaitTimeout(0);
        pds.setCreateConnectionInBorrowThread(true);
        pds.setValidateConnectionOnBorrow(true);
        pds.setInactiveConnectionTimeout(120);
        pds.setAbandonedConnectionTimeout(300);
        pds.setMaxPoolSize(Integer.MAX_VALUE);
        return pds;
    }

    static String getCatalogServiceUrl(Connection connection, String applicationUrl, String gsmServiceName) throws SQLException {
        String catalogServiceUrl;
        CallableStatement cstmt = connection.prepareCall("{call GSMADMIN_INTERNAL.getShardingParams(?,?)}");
        Throwable th = null;
        try {
            cstmt.registerOutParameter(1, 2);
            cstmt.registerOutParameter(2, 12);
            cstmt.execute();
            int shardMode = cstmt.getInt(1);
            String catalogServiceName = cstmt.getString(2);
            if (catalogServiceName == null) {
                throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_UNSUPPORTED_SHARDING_MODE).fillInStackTrace());
            }
            if ((shardMode & 4) != 0) {
                throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_UNSUPPORTED_CATALOG_SERVICE_CONNECTION).fillInStackTrace());
            }
            if ((shardMode & 1) == 0) {
                throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_UNSUPPORTED_SHARDING_MODE).fillInStackTrace());
            }
            if (!catalogServiceName.equals(gsmServiceName)) {
                catalogServiceUrl = replaceGsmServiceNameWithCatalogServiceName(applicationUrl, gsmServiceName, catalogServiceName);
            } else {
                catalogServiceUrl = applicationUrl;
            }
            return catalogServiceUrl;
        } finally {
            if (cstmt != null) {
                if (0 != 0) {
                    try {
                        cstmt.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    cstmt.close();
                }
            }
        }
    }

    private static String replaceGsmServiceNameWithCatalogServiceName(String url, String gsmServiceName, String catalogServiceName) {
        String replacedUrl = AddrResolution.replaceServiceNameInUrl(url, gsmServiceName, catalogServiceName);
        diagnosable.trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "replaceGsmServiceNameWithCatalogServiceName", "url={0} gsmServiceName={1} catalogServiceName={2} replacedUrl={3}", (String) null, (Throwable) null, url, gsmServiceName, catalogServiceName, replacedUrl);
        return replacedUrl;
    }

    /* JADX WARN: Finally extract failed */
    private static void validateConnectionToShardedDatabase(Connection connection) throws SQLException {
        Statement stmt = connection.createStatement();
        Throwable th = null;
        try {
            ResultSet rs = stmt.executeQuery("select GSMADMIN_INTERNAL.GETSHARDINGMODE from dual");
            Throwable th2 = null;
            try {
                if (rs.next()) {
                    int shardMode = rs.getInt(1);
                    diagnosable.trace(Level.INFO, SecurityLabel.UNKNOWN, CLASS_NAME, "validateConnectionToShardedDatabase", "sharding mode={0}", (String) null, (Throwable) null, Integer.valueOf(shardMode));
                    if ((shardMode & 4) != 0) {
                        throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_UNSUPPORTED_CATALOG_SERVICE_CONNECTION).fillInStackTrace());
                    }
                    if ((shardMode & 1) == 0) {
                        throw ((SQLException) DatabaseError.createSqlException(DatabaseError.EOJ_UNSUPPORTED_SHARDING_MODE).fillInStackTrace());
                    }
                }
                if (rs != null) {
                    if (0 != 0) {
                        try {
                            rs.close();
                        } catch (Throwable th3) {
                            th2.addSuppressed(th3);
                        }
                    } else {
                        rs.close();
                    }
                }
                if (stmt != null) {
                    if (0 == 0) {
                        stmt.close();
                        return;
                    }
                    try {
                        stmt.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                }
            } catch (Throwable th5) {
                if (rs != null) {
                    if (0 != 0) {
                        try {
                            rs.close();
                        } catch (Throwable th6) {
                            th2.addSuppressed(th6);
                        }
                    } else {
                        rs.close();
                    }
                }
                throw th5;
            }
        } catch (Throwable th7) {
            if (stmt != null) {
                if (0 != 0) {
                    try {
                        stmt.close();
                    } catch (Throwable th8) {
                        th.addSuppressed(th8);
                    }
                } else {
                    stmt.close();
                }
            }
            throw th7;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/ShardingConnectionUtil$ShardingPoolDataSourceEntry.class */
    protected static final class ShardingPoolDataSourceEntry {
        private PoolDataSource pds;
        private String userName;
        private String schemaName;
        private Properties serverSessionInfo;

        public ShardingPoolDataSourceEntry(PoolDataSource pds, String userName, String schemaName, @Blind(PropertiesBlinder.class) Properties serverSessionInfo) {
            this.pds = pds;
            this.userName = userName;
            this.schemaName = schemaName;
            this.serverSessionInfo = serverSessionInfo;
        }

        public PoolDataSource getPds() {
            return this.pds;
        }

        public String getUserName() {
            return this.userName;
        }

        public String getSchemaName() {
            return this.schemaName;
        }

        @Blind(PropertiesBlinder.class)
        public Properties getServerSessionInfo() {
            return this.serverSessionInfo;
        }
    }
}
