package oracle.jdbc.connector;

import javax.resource.ResourceException;
import javax.resource.spi.ConnectionManager;
import javax.resource.spi.ConnectionRequestInfo;
import javax.resource.spi.ManagedConnection;
import javax.resource.spi.ManagedConnectionFactory;
import javax.security.auth.Subject;

/* loaded from: ojdbc8.jar:oracle/jdbc/connector/OracleConnectionManager.class */
public class OracleConnectionManager implements ConnectionManager {
    public Object allocateConnection(ManagedConnectionFactory mcf, ConnectionRequestInfo cxRequestInfo) throws ResourceException {
        ManagedConnection mc = mcf.createManagedConnection((Subject) null, cxRequestInfo);
        return mc.getConnection((Subject) null, cxRequestInfo);
    }
}
