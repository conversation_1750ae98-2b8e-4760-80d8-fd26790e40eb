package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import oracle.jdbc.internal.KeywordValueLong;
import oracle.jdbc.internal.XSKeyval;
import oracle.jdbc.internal.XSNamespace;
import oracle.jdbc.internal.XSPrincipal;
import oracle.jdbc.internal.XSSecureId;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIoxsatt.class */
final class T4CTTIoxsatt extends T4CTTIfun {
    private int opcode;
    private byte[] sessionId;
    private XSSecureId sidp;
    private byte[] cookie;
    private XSPrincipal username;
    private byte[][] disabledRolesBytes;
    private byte[][] enabledRolesBytes;
    private byte[][] externalRolesBytes;
    private XSNamespace[] namespaces;
    private XSNamespace[] cacheNamespace;
    private XSNamespace[] deleteNamespace;
    private TIMESTAMPTZ midTierTimestamp;
    private TIMESTAMPTZ authtime;
    private int roleVersion;
    private long inputFlag;
    private XSKeyval kv;
    private int[] roleVersionOutput;

    T4CTTIoxsatt(T4CConnection _conn) {
        super(_conn, (byte) 3);
        setFunCode((short) 180);
    }

    /* JADX WARN: Type inference failed for: r1v46, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v55, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r1v64, types: [byte[], byte[][]] */
    void doOXSATT(int opCode, byte[] sessionId, XSSecureId sidp, byte[] cookie, XSPrincipal username, String[] disabledRoles, String[] enabledRoles, String[] externalRoles, XSNamespace[] namespaces, XSNamespace[] cacheNamespace, XSNamespace[] deleteNamespace, TIMESTAMPTZ midTierTimestamp, TIMESTAMPTZ authtime, int roleVersion, long inputFlag, XSKeyval kv, int[] roleVersionOutput) throws SQLException, IOException {
        KeywordValueLong[] kvl;
        this.opcode = opCode;
        this.sessionId = sessionId;
        this.sidp = sidp;
        this.cookie = cookie;
        this.username = username;
        if (username != null) {
            ((XSPrincipalI) username).doCharConversion(this.meg.conv);
        }
        if (disabledRoles != null && disabledRoles.length > 0) {
            this.disabledRolesBytes = new byte[disabledRoles.length];
            for (int i = 0; i < disabledRoles.length; i++) {
                if (disabledRoles[i] != null && disabledRoles[i].length() > 0) {
                    this.disabledRolesBytes[i] = this.meg.conv.StringToCharBytes(disabledRoles[i]);
                } else {
                    this.disabledRolesBytes[i] = null;
                }
            }
        } else {
            this.disabledRolesBytes = (byte[][]) null;
        }
        if (enabledRoles != null && enabledRoles.length > 0) {
            this.enabledRolesBytes = new byte[enabledRoles.length];
            for (int i2 = 0; i2 < enabledRoles.length; i2++) {
                if (enabledRoles[i2] != null && enabledRoles[i2].length() > 0) {
                    this.enabledRolesBytes[i2] = this.meg.conv.StringToCharBytes(enabledRoles[i2]);
                } else {
                    this.enabledRolesBytes[i2] = null;
                }
            }
        } else {
            this.enabledRolesBytes = (byte[][]) null;
        }
        if (externalRoles != null && externalRoles.length > 0) {
            this.externalRolesBytes = new byte[externalRoles.length];
            for (int i3 = 0; i3 < externalRoles.length; i3++) {
                if (externalRoles[i3] != null && externalRoles[i3].length() > 0) {
                    this.externalRolesBytes[i3] = this.meg.conv.StringToCharBytes(externalRoles[i3]);
                } else {
                    this.externalRolesBytes[i3] = null;
                }
            }
        } else {
            this.externalRolesBytes = (byte[][]) null;
        }
        this.namespaces = namespaces;
        if (namespaces != null) {
            for (XSNamespace xSNamespace : namespaces) {
                ((XSNamespaceI) xSNamespace).doCharConversion(this.meg.conv);
            }
        }
        this.cacheNamespace = cacheNamespace;
        if (cacheNamespace != null) {
            for (XSNamespace xSNamespace2 : cacheNamespace) {
                ((XSNamespaceI) xSNamespace2).doCharConversion(this.meg.conv);
            }
        }
        this.deleteNamespace = deleteNamespace;
        if (deleteNamespace != null) {
            for (XSNamespace xSNamespace3 : deleteNamespace) {
                ((XSNamespaceI) xSNamespace3).doCharConversion(this.meg.conv);
            }
        }
        this.midTierTimestamp = midTierTimestamp;
        this.authtime = authtime;
        this.roleVersion = roleVersion;
        this.inputFlag = inputFlag;
        this.kv = kv;
        if (kv != null && (kvl = kv.getKeyval()) != null) {
            for (KeywordValueLong keywordValueLong : kvl) {
                ((KeywordValueLongI) keywordValueLong).doCharConversion(this.meg.conv);
            }
        }
        this.roleVersionOutput = roleVersionOutput;
        doRPC();
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void marshal() throws IOException {
        this.meg.marshalUB4(this.opcode);
        boolean sendSessionId = false;
        if (this.sessionId != null && this.sessionId.length > 0) {
            sendSessionId = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.sessionId.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendSidp = false;
        if (this.sidp != null) {
            sendSidp = true;
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        boolean sendcookie = false;
        if (this.cookie != null && this.cookie.length > 0) {
            sendcookie = true;
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.cookie.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendUsername = false;
        if (this.username == null) {
            this.meg.marshalNULLPTR();
        } else {
            sendUsername = true;
            this.meg.marshalPTR();
        }
        if (this.disabledRolesBytes != null && this.disabledRolesBytes.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.disabledRolesBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.enabledRolesBytes != null && this.enabledRolesBytes.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.enabledRolesBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        if (this.externalRolesBytes != null && this.externalRolesBytes.length > 0) {
            this.meg.marshalPTR();
            this.meg.marshalUB4(this.externalRolesBytes.length);
        } else {
            this.meg.marshalNULLPTR();
            this.meg.marshalUB4(0L);
        }
        boolean sendNamespaces = false;
        this.meg.marshalPTR();
        if (this.namespaces != null && this.namespaces.length > 0) {
            sendNamespaces = true;
            this.meg.marshalUB4(this.namespaces.length);
        } else {
            this.meg.marshalUB4(0L);
        }
        boolean sendCacheNamespace = false;
        this.meg.marshalPTR();
        if (this.cacheNamespace != null && this.cacheNamespace.length > 0) {
            sendCacheNamespace = true;
            this.meg.marshalUB4(this.cacheNamespace.length);
        } else {
            this.meg.marshalUB4(0L);
        }
        boolean sendDeleteNamespace = false;
        this.meg.marshalPTR();
        if (this.deleteNamespace != null && this.deleteNamespace.length > 0) {
            sendDeleteNamespace = true;
            this.meg.marshalUB4(this.deleteNamespace.length);
        } else {
            this.meg.marshalUB4(0L);
        }
        if (this.midTierTimestamp != null) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (this.authtime != null) {
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        this.meg.marshalPTR();
        this.meg.marshalUB4(this.inputFlag);
        boolean sendKv = false;
        if (this.kv != null) {
            sendKv = true;
            this.meg.marshalPTR();
        } else {
            this.meg.marshalNULLPTR();
        }
        if (sendSessionId) {
            this.meg.marshalB1Array(this.sessionId);
        }
        if (sendSidp) {
            ((XSSecureIdI) this.sidp).marshal(this.meg);
        }
        if (sendcookie) {
            this.meg.marshalB1Array(this.cookie);
        }
        if (sendUsername) {
            ((XSPrincipalI) this.username).marshal(this.meg);
        }
        if (this.disabledRolesBytes != null && this.disabledRolesBytes.length > 0) {
            for (int i = 0; i < this.disabledRolesBytes.length; i++) {
                if (this.disabledRolesBytes[i] == null) {
                    this.meg.marshalUB4(0L);
                } else {
                    this.meg.marshalUB4(this.disabledRolesBytes[i].length);
                    this.meg.marshalCLR(this.disabledRolesBytes[i], this.disabledRolesBytes[i].length);
                }
            }
        }
        if (this.enabledRolesBytes != null && this.enabledRolesBytes.length > 0) {
            for (int i2 = 0; i2 < this.enabledRolesBytes.length; i2++) {
                if (this.enabledRolesBytes[i2] == null) {
                    this.meg.marshalUB4(0L);
                } else {
                    this.meg.marshalUB4(this.enabledRolesBytes[i2].length);
                    this.meg.marshalCLR(this.enabledRolesBytes[i2], this.enabledRolesBytes[i2].length);
                }
            }
        }
        if (this.externalRolesBytes != null && this.externalRolesBytes.length > 0) {
            for (int i3 = 0; i3 < this.externalRolesBytes.length; i3++) {
                if (this.externalRolesBytes[i3] == null) {
                    this.meg.marshalUB4(0L);
                } else {
                    this.meg.marshalUB4(this.externalRolesBytes[i3].length);
                    this.meg.marshalCLR(this.externalRolesBytes[i3], this.externalRolesBytes[i3].length);
                }
            }
        }
        if (sendNamespaces) {
            for (int i4 = 0; i4 < this.namespaces.length; i4++) {
                ((XSNamespaceI) this.namespaces[i4]).marshal(this.meg);
            }
        }
        if (sendCacheNamespace) {
            for (int i5 = 0; i5 < this.cacheNamespace.length; i5++) {
                ((XSNamespaceI) this.cacheNamespace[i5]).marshal(this.meg);
            }
        }
        if (sendDeleteNamespace) {
            for (int i6 = 0; i6 < this.deleteNamespace.length; i6++) {
                ((XSNamespaceI) this.deleteNamespace[i6]).marshal(this.meg);
            }
        }
        if (this.midTierTimestamp != null) {
            this.meg.marshalB1Array(this.midTierTimestamp.getBytes());
        }
        if (this.authtime != null) {
            this.meg.marshalB1Array(this.authtime.getBytes());
        }
        this.meg.marshalUB4(this.roleVersion);
        if (sendKv) {
            ((XSKeyvalI) this.kv).marshal(this.meg);
        }
    }

    @Override // oracle.jdbc.driver.T4CTTIfun
    void readRPA() throws SQLException, IOException {
        int roleOut = (int) this.meg.unmarshalUB4();
        if (this.roleVersionOutput != null && this.roleVersionOutput.length == 1) {
            this.roleVersionOutput[0] = roleOut;
        }
    }
}
