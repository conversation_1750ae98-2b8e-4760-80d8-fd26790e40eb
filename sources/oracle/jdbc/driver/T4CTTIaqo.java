package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import oracle.jdbc.aq.AQDequeueOptions;
import oracle.jdbc.aq.AQEnqueueOptions;
import oracle.jdbc.aq.AQMessageProperties;
import oracle.jdbc.diagnostics.Diagnosable;
import oracle.jdbc.diagnostics.SecurityLabel;
import oracle.jdbc.internal.JMSDequeueOptions;
import oracle.jdbc.internal.JMSEnqueueOptions;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIaqo.class */
class T4CTTIaqo implements Diagnosable {
    private static final String CLASS_NAME = T4CTTIaqo.class.getName();
    T4CConnection connection;
    T4CMAREngine meg;
    T4CTTIaqm aqm;
    T4Ctoh toh;
    private byte[] payload;
    private AQMessagePropertiesI aqMessageProperties;
    private JMSDequeueOptions jmsDequeueOptions;
    private byte[] outMsgId;
    private int aqxaqopt;
    private JMSEnqueueOptions jmsEnqueueOptions;
    private boolean isAQMsg;
    private AQEnqueueOptions aqEnqueueOptions;
    private AQDequeueOptions aqDequeueOptions;
    private boolean isRawQueue;
    private boolean isJsonQueue;

    @Override // oracle.jdbc.diagnostics.Diagnosable
    public Diagnosable getDiagnosable() {
        return this.connection.getDiagnosable();
    }

    T4CTTIaqo(T4CConnection _connection, int _aqxaqopt, JMSEnqueueOptions _enqueueOptions, AQMessagePropertiesI _aqMessageProperties, JMSDequeueOptions _dequeueOptions, boolean _isRawQueue) throws SQLException, IOException {
        this.payload = null;
        this.aqMessageProperties = null;
        this.jmsDequeueOptions = null;
        this.outMsgId = null;
        this.aqxaqopt = 0;
        this.isAQMsg = false;
        this.aqEnqueueOptions = null;
        this.aqDequeueOptions = null;
        this.isRawQueue = false;
        this.isJsonQueue = false;
        initCommon(_connection, _aqxaqopt, _aqMessageProperties, _isRawQueue, this.isJsonQueue);
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "<init>", "_isRawQueue={0}, _aqxaqopt={1}, _enqueueOptions={2}, _aqMessageProperties={3}, _dequeueOptions={4}", (String) null, (Throwable) null, Boolean.valueOf(_isRawQueue), Integer.valueOf(_aqxaqopt), _enqueueOptions, _aqMessageProperties, _dequeueOptions);
        this.jmsEnqueueOptions = _enqueueOptions;
        this.jmsDequeueOptions = _dequeueOptions;
    }

    T4CTTIaqo(T4CConnection _connection, int _aqxaqopt, AQEnqueueOptions _aqEnqueueOptions, AQMessagePropertiesI _aqMessageProperties, AQDequeueOptions _aqDequeueOptions, boolean _isRawQueue, boolean _isJsonQueue) throws SQLException, IOException {
        this.payload = null;
        this.aqMessageProperties = null;
        this.jmsDequeueOptions = null;
        this.outMsgId = null;
        this.aqxaqopt = 0;
        this.isAQMsg = false;
        this.aqEnqueueOptions = null;
        this.aqDequeueOptions = null;
        this.isRawQueue = false;
        this.isJsonQueue = false;
        this.isAQMsg = true;
        initCommon(_connection, _aqxaqopt, _aqMessageProperties, _isRawQueue, _isJsonQueue);
        debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "<init>", "_isRawQueue={0}, _aqxaqopt={1}, _aqEnqueueOptions={2}, _aqMessageProperties={3}, _aqDequeueOptions={4}", (String) null, (Throwable) null, Boolean.valueOf(_isRawQueue), Integer.valueOf(_aqxaqopt), _aqEnqueueOptions, _aqMessageProperties, _aqDequeueOptions);
        this.aqEnqueueOptions = _aqEnqueueOptions;
        this.aqDequeueOptions = _aqDequeueOptions;
    }

    void initCommon(T4CConnection _connection, int _aqxaqopt, AQMessagePropertiesI _aqMessageProperties, boolean _isRawQueue, boolean _isJsonQueue) {
        this.connection = _connection;
        this.meg = this.connection.getMarshalEngine();
        this.aqxaqopt = _aqxaqopt;
        this.aqMessageProperties = _aqMessageProperties;
        this.toh = new T4Ctoh(_connection);
        this.aqm = new T4CTTIaqm(this.connection, this.toh);
        this.isRawQueue = _isRawQueue;
        this.isJsonQueue = _isJsonQueue;
    }

    void unmarshal() throws SQLException, IOException {
        int msgPropLength = this.meg.unmarshalUB2();
        if (msgPropLength > 0) {
            short len = this.meg.unmarshalUB1();
            this.aqm.initToDefaultValues();
            this.aqm.receive();
            this.aqMessageProperties.setPriority(this.aqm.aqmpri);
            this.aqMessageProperties.setDelay(this.aqm.aqmdel);
            this.aqMessageProperties.setExpiration(this.aqm.aqmexp);
            if (this.aqm.aqmcorBytes != null) {
                String aqmcor = this.meg.conv.CharBytesToString(this.aqm.aqmcorBytes, this.aqm.aqmcorBytesLength, true);
                this.aqMessageProperties.setCorrelation(aqmcor);
            }
            this.aqMessageProperties.setAttempts(this.aqm.aqmatt);
            if (this.aqm.aqmeqnBytes != null) {
                String aqmeqn = this.meg.conv.CharBytesToString(this.aqm.aqmeqnBytes, this.aqm.aqmeqnBytesLength, true);
                this.aqMessageProperties.setExceptionQueue(aqmeqn);
            }
            this.aqMessageProperties.setMessageState(AQMessageProperties.MessageState.getMessageState(this.aqm.aqmsta));
            if (this.aqm.aqmeqt != null) {
                this.aqMessageProperties.setEnqueueTime(this.aqm.aqmeqt.timestampValue());
            }
            AQAgentI senderAgent = new AQAgentI();
            if (this.aqm.senderAgentName != null) {
                senderAgent.setName(this.meg.conv.CharBytesToString(this.aqm.senderAgentName, this.aqm.senderAgentNameLength, true));
            }
            if (this.aqm.senderAgentAddress != null) {
                senderAgent.setAddress(this.meg.conv.CharBytesToString(this.aqm.senderAgentAddress, this.aqm.senderAgentAddressLength, true));
            }
            senderAgent.setProtocol(this.aqm.senderAgentProtocol);
            this.aqMessageProperties.setSender(senderAgent);
            this.aqMessageProperties.setPreviousQueueMessageId(this.aqm.originalMsgId);
            this.aqMessageProperties.setDeliveryMode(AQMessageProperties.DeliveryMode.getDeliveryMode(this.aqm.aqmflg));
            if (this.aqm.aqmetiBytes != null) {
                String aqmeti = this.meg.conv.CharBytesToString(this.aqm.aqmetiBytes, this.aqm.aqmetiBytes.length, true);
                this.aqMessageProperties.setTransactionGroup(aqmeti);
            }
            debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshal", "msgPropLength={0}, len={1}, aqMessageProperties={2}", (String) null, (Throwable) null, Integer.valueOf(msgPropLength), Short.valueOf(len), this.aqMessageProperties);
        }
        int aqorel = this.meg.unmarshalUB2();
        if (aqorel > 0) {
        }
        int lengthOfPayload = this.meg.unmarshalUB2();
        if (this.aqxaqopt == 2) {
            this.toh.unmarshal(this.meg);
            lengthOfPayload = this.toh.imageLength;
        }
        if (lengthOfPayload > 0) {
            int bufferToAllocate = lengthOfPayload;
            if (this.isRawQueue || this.isJsonQueue) {
                if (lengthOfPayload > 4) {
                    bufferToAllocate -= 4;
                }
                int maxBufLength = this.isAQMsg ? this.aqDequeueOptions.getMaximumBufferLength() : bufferToAllocate;
                bufferToAllocate = Math.min(bufferToAllocate, maxBufLength);
                byte[] image = new byte[bufferToAllocate];
                int[] intAr = new int[1];
                if (lengthOfPayload > 4) {
                    this.meg.unmarshalCLR(image, 0, intAr, image.length, 4);
                } else {
                    this.meg.unmarshalCLR(image, 0, intAr, image.length);
                }
                this.payload = image;
            } else {
                byte[] image2 = new byte[bufferToAllocate];
                this.meg.unmarshalCLR(image2, 0, new int[1], image2.length);
                this.payload = image2;
            }
            debug(Level.FINER, SecurityLabel.UNKNOWN, CLASS_NAME, "unmarshal", "lengthOfPayload={0}, bufferToAllocate={1}, isRawQueue={2}", (String) null, (Throwable) null, Integer.valueOf(lengthOfPayload), Integer.valueOf(bufferToAllocate), Boolean.valueOf(this.isRawQueue));
        }
        int msgIdLen = this.meg.unmarshalSWORD();
        if (((this.aqxaqopt == 1 && !this.isAQMsg && this.jmsEnqueueOptions != null && this.jmsEnqueueOptions.isRetrieveMessageId()) || ((this.aqxaqopt == 2 && !this.isAQMsg && this.jmsDequeueOptions != null && this.jmsDequeueOptions.isRetrieveMessageId()) || ((this.aqxaqopt == 1 && this.isAQMsg && this.aqEnqueueOptions != null && this.aqEnqueueOptions.getRetrieveMessageId()) || (this.aqxaqopt == 2 && this.isAQMsg && this.aqDequeueOptions != null && this.aqDequeueOptions.getRetrieveMessageId())))) && msgIdLen > 0) {
            byte[] aqomsi = new byte[msgIdLen];
            int[] readBytes = new int[1];
            this.meg.unmarshalCLR(aqomsi, 0, readBytes);
            this.outMsgId = aqomsi;
        }
        int aqoexl = this.meg.unmarshalUB2();
        if (aqoexl > 0) {
        }
        this.meg.unmarshalUB2();
    }

    byte[] getMsgId() {
        return this.outMsgId;
    }

    byte[] getPayload() {
        return this.payload;
    }
}
