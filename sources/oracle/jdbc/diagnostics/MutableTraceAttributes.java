package oracle.jdbc.diagnostics;

import java.util.Objects;
import oracle.jdbc.TraceKey;

/* loaded from: ojdbc8.jar:oracle/jdbc/diagnostics/MutableTraceAttributes.class */
public class MutableTraceAttributes extends TraceAttributes {
    private ImmutableTraceAttributes cachedImmutableCopy;
    private ImmutableTraceAttributes otherCachedImmutableCopy;

    public MutableTraceAttributes() {
        this.cachedImmutableCopy = null;
        this.otherCachedImmutableCopy = null;
    }

    public MutableTraceAttributes(ImmutableTraceAttributes immutableTraceAttributes) {
        super(immutableTraceAttributes.values);
        this.cachedImmutableCopy = null;
        this.otherCachedImmutableCopy = null;
    }

    public void set(Trace<PERSON>ey key, String value) {
        if (key != null && !Objects.equals(this.values[key.index()], value)) {
            if (key == OracleTraceKey.SQL) {
                if (this.values[key.index()] != null && value == null) {
                    swapCachedAttributes();
                } else if (this.values[key.index()] != null && value != null) {
                    this.cachedImmutableCopy = null;
                } else if (this.values[key.index()] == null && value != null) {
                    swapCachedAttributes();
                    if (this.cachedImmutableCopy != null && this.cachedImmutableCopy.values[key.index()] != null && !this.cachedImmutableCopy.values[key.index()].equals(value)) {
                        this.cachedImmutableCopy = null;
                    }
                }
                this.values[key.index()] = value;
                return;
            }
            this.values[key.index()] = value;
            this.cachedImmutableCopy = null;
            this.otherCachedImmutableCopy = null;
        }
    }

    private void swapCachedAttributes() {
        ImmutableTraceAttributes temp = this.cachedImmutableCopy;
        this.cachedImmutableCopy = this.otherCachedImmutableCopy;
        this.otherCachedImmutableCopy = temp;
    }

    @Override // oracle.jdbc.diagnostics.TraceAttributes
    public ImmutableTraceAttributes toReadOnly() {
        if (this.cachedImmutableCopy == null) {
            this.cachedImmutableCopy = new ImmutableTraceAttributes(this.values);
        }
        return this.cachedImmutableCopy;
    }

    public ImmutableTraceAttributes toReadOnlyWithoutKey(OracleTraceKey key) {
        set(key, null);
        return toReadOnly();
    }
}
