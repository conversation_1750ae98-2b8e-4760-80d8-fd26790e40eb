package oracle.jdbc.driver.parser;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import oracle.jdbc.driver.parser.util.Array;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/Parser.class */
public abstract class Parser implements Serializable {
    public String[] allSymbols;
    public Map<String, Integer> symbolIndexes;
    public Tuple[] rules;

    public abstract ParseNode parse(List<LexerToken> list);

    public Parser(RuleTuple[] originalRules) {
        this.allSymbols = new String[0];
        this.symbolIndexes = new HashMap();
        extractSymbols(originalRules);
        this.rules = new Tuple[originalRules.length];
        int p = 0;
        for (RuleTuple t : originalRules) {
            if (t.rhs.length == 0) {
                throw new AssertionError("empty production " + t.toString());
            }
            int h = this.symbolIndexes.get(t.head).intValue();
            int[] rhs = new int[t.rhs.length];
            for (int i = 0; i < rhs.length; i++) {
                rhs[i] = this.symbolIndexes.get(t.rhs[i]).intValue();
            }
            int i2 = p;
            p++;
            this.rules[i2] = new Tuple(h, rhs);
        }
    }

    public Parser() {
        this.allSymbols = new String[0];
        this.symbolIndexes = new HashMap();
    }

    protected void extractSymbols(RuleTuple[] symbolicRules) {
        Set<String> tmpSymbols = new TreeSet<>();
        if (!this.symbolIndexes.containsKey("!nil")) {
            tmpSymbols.add("!nil");
        }
        for (RuleTuple ct : symbolicRules) {
            if (ct.head == null || ct.rhs.length == 0 || ct.rhs[0] == null || (ct.rhs.length > 1 && ct.rhs[1] == null)) {
                throw new AssertionError("grammar has null symbols (or empty productions): " + ct.toString());
            }
            if (!this.symbolIndexes.containsKey(ct.head)) {
                tmpSymbols.add(ct.head);
            }
            for (String s : ct.rhs) {
                if (!this.symbolIndexes.containsKey(s)) {
                    if (s.length() < 2 && (this instanceof SqlEarley)) {
                        throw new AssertionError("ct=" + ct.toString());
                    }
                    tmpSymbols.add(s);
                }
            }
        }
        int k = this.allSymbols.length;
        this.allSymbols = (String[]) Arrays.copyOf(this.allSymbols, this.allSymbols.length + tmpSymbols.size());
        for (String s2 : tmpSymbols) {
            this.symbolIndexes.put(s2, Integer.valueOf(k));
            this.allSymbols[k] = s2;
            k++;
        }
    }

    public boolean isTerminal(int terminal) {
        return this.allSymbols[terminal].charAt(0) == '\'';
    }

    public ParseNode forest(List<LexerToken> src, Matrix matrix) {
        return forest(src, matrix, false);
    }

    public ParseNode forest(List<LexerToken> src, Matrix matrix, boolean full) {
        return forest(src, matrix, false, null);
    }

    protected boolean isAsc(int ruleHead) {
        return false;
    }

    public ParseNode forest(List<LexerToken> src, Matrix matrix, boolean full, String input) {
        EarleyCell cell;
        ParseNode root;
        new HashMap();
        try {
            int len = src.size();
            if (len == 0) {
                return new ParseNode(0, len, -1, this);
            }
            EarleyCell cell2 = matrix.get(0, len);
            if (cell2 != null && 0 < cell2.size() && (root = replaceLeaves(treeForACell(src, matrix, cell2, 0, len), input, src)) != null) {
                return root;
            }
            ParseNode pseudoRoot = new ParseNode(0, len, -1, this);
            int Y = matrix.lastY();
            for (int offset = 0; offset < Y; offset++) {
                int t = 0;
                while (t < offset + 1) {
                    int x = 0 + t;
                    int y = (Y + t) - offset;
                    ParseNode coverX = pseudoRoot.coveredByOnTopLevel(x);
                    ParseNode coverY = pseudoRoot.coveredByOnTopLevel(y);
                    while (true) {
                        if (coverX == null && coverY == null) {
                            break;
                        }
                        int delta = 0;
                        if (coverX != null) {
                            delta = (coverX.to - y) + 1;
                        }
                        if (coverY != null) {
                            delta = (coverY.to - y) + 1;
                        }
                        if (delta <= 0) {
                            break;
                        }
                        t += delta;
                        x = 0 + t;
                        y = (Y + t) - offset;
                        coverX = pseudoRoot.coveredByOnTopLevel(x);
                        coverY = pseudoRoot.coveredByOnTopLevel(y);
                    }
                    if (offset + 1 > t && (cell = matrix.get(x, y)) != null) {
                        new HashMap();
                        ParseNode node = treeForACell(src, matrix, cell, x, y);
                        if (node != null) {
                            pseudoRoot.addTopLevel(node);
                            node.parent = pseudoRoot;
                            if (!full) {
                                return replaceLeaves(pseudoRoot, input, src);
                            }
                        } else {
                            continue;
                        }
                    }
                    t++;
                }
            }
            if (full) {
                return replaceLeaves(pseudoRoot, input, src);
            }
            return new ParseNode(0, 1, -1, this);
        } catch (AssertionError e) {
            System.err.println("Parser.forest(): AssertionError " + e.getMessage());
            return null;
        }
    }

    protected ParseNode replaceLeaves(ParseNode root, String input, List<LexerToken> src) {
        return root;
    }

    ParseNode treeForACell(List<LexerToken> src, Matrix m, EarleyCell cell, int x, int y) {
        throw new AssertionError("Abstract method");
    }

    void toHtml(int ruleNo, int pos, boolean selected, int x, int mid, int y, Matrix matrix, StringBuffer sb) {
        throw new AssertionError("Abstract method");
    }

    public int getSymbol(String string) {
        try {
            return this.symbolIndexes.get(string).intValue();
        } catch (NullPointerException e) {
            return -1;
        } catch (Exception e2) {
            e2.printStackTrace();
            return -1;
        }
    }

    public void swapRules(String rule2, String rule3) {
        Tuple t2 = null;
        int i2 = -1;
        Tuple t3 = null;
        int i3 = -1;
        for (int i = 0; i < this.rules.length; i++) {
            Tuple t = this.rules[i];
            if (rule2.equals(t.toString())) {
                t2 = t;
                i2 = i;
            }
            if (rule3.equals(t.toString())) {
                t3 = t;
                i3 = i;
            }
        }
        if (i2 == -1 || i3 == -1) {
            return;
        }
        this.rules[i2] = t3;
        this.rules[i3] = t2;
    }

    public boolean isAuxNode(int symbol) {
        return false;
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/Parser$Tuple.class */
    public class Tuple implements Comparable<Tuple>, Serializable {
        public int head;
        public int[] rhs;

        public Tuple(int h, int[] r) {
            this.head = h;
            this.rhs = r;
        }

        public int size() {
            return this.rhs.length;
        }

        public int content(int i) {
            return this.rhs[i];
        }

        public boolean equals(Object obj) {
            return this == obj || ((obj instanceof Tuple) && compareTo((Tuple) obj) == 0);
        }

        public int hashCode() {
            throw new RuntimeException("hashCode inconsistent with equals");
        }

        @Override // java.lang.Comparable
        public int compareTo(Tuple src) {
            if (this.head == 0 || src.head == 0) {
                throw new RuntimeException("head==0 || src.head==0");
            }
            int cmp = this.head - src.head;
            if (cmp != 0) {
                return cmp;
            }
            int cmp2 = this.rhs.length - src.rhs.length;
            if (cmp2 != 0) {
                return cmp2;
            }
            for (int i = 0; i < this.rhs.length; i++) {
                int cmp3 = this.rhs[i] - src.rhs[i];
                if (cmp3 != 0) {
                    return cmp3;
                }
            }
            return 0;
        }

        public String toString() {
            StringBuilder s = new StringBuilder(Parser.this.allSymbols[this.head] + ":");
            for (int i : this.rhs) {
                s.append("  " + Parser.this.allSymbols[i]);
            }
            s.append(";");
            return s.toString();
        }

        public String toString(int pos) {
            StringBuilder s = new StringBuilder(Parser.this.allSymbols[this.head] + ":");
            for (int i = 0; i < this.rhs.length; i++) {
                s.append(' ');
                if (pos == i) {
                    s.append('!');
                }
                s.append(Parser.this.allSymbols[this.rhs[i]]);
            }
            if (pos == this.rhs.length) {
                s.append('!');
            }
            s.append(";");
            return s.toString();
        }
    }

    protected static long makeMatrixCellElem(int rule, int pos, Tuple t) {
        long activeVar = 0;
        if (pos < t.rhs.length) {
            activeVar = t.rhs[pos];
        }
        return (rule << 16) | pos | (activeVar << 48);
    }

    public static int ruleFromEarleyCell(long code) {
        return (int) ((code & 281474976710655L) >> 16);
    }

    public static int posFromEarleyCell(long code) {
        return (int) (code & 65535);
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/parser/Parser$EarleyCell.class */
    public class EarleyCell implements Cell {
        long[] content;
        int size;

        public EarleyCell(long[] content) {
            this.content = null;
            this.size = 0;
            this.content = content;
            if (content != null) {
                this.size = content.length;
            }
        }

        public EarleyCell() {
            this.content = null;
            this.size = 0;
        }

        @Override // oracle.jdbc.driver.parser.Cell
        public int getRule(int index) {
            return Parser.ruleFromEarleyCell(this.content[index]);
        }

        @Override // oracle.jdbc.driver.parser.Cell
        public int getPosition(int index) {
            return Parser.posFromEarleyCell(this.content[index]);
        }

        @Override // oracle.jdbc.driver.parser.Cell
        public int size() {
            return this.size;
        }

        @Override // oracle.jdbc.driver.parser.Cell
        public long[] getContent() {
            return this.content;
        }

        @Override // oracle.jdbc.driver.parser.Cell
        public void insertContent(long value) {
            if (this.content == null || this.size == 0) {
                this.content = new long[1];
                this.content[0] = value;
                this.size = 1;
                return;
            }
            int index = Array.indexOf(this.content, 0, this.size, value);
            if (index < this.size && this.content[index] == value) {
                return;
            }
            long[] ret = this.content;
            if (this.content.length == this.size) {
                ret = new long[2 * this.size];
                for (int i = 0; i < index; i++) {
                    ret[i] = this.content[i];
                }
            }
            for (int i2 = this.size; index + 1 <= i2; i2--) {
                ret[i2] = this.content[i2 - 1];
            }
            ret[index] = value;
            this.content = ret;
            this.size++;
        }

        public void merge(long[] addendum) {
            if (addendum == null) {
                return;
            }
            if (this.content == null) {
                this.content = addendum;
                this.size = addendum.length;
                return;
            }
            int m = this.size;
            int n = addendum.length;
            long[] tmp = new long[m + n];
            int i = 0;
            int j = 0;
            int k = 0;
            while (i < m && j < n) {
                if (this.content[i] == addendum[j]) {
                    int i2 = k;
                    k++;
                    int i3 = i;
                    i++;
                    tmp[i2] = this.content[i3];
                    j++;
                } else if (this.content[i] < addendum[j]) {
                    int i4 = k;
                    k++;
                    int i5 = i;
                    i++;
                    tmp[i4] = this.content[i5];
                } else {
                    int i6 = k;
                    k++;
                    int i7 = j;
                    j++;
                    tmp[i6] = addendum[i7];
                }
            }
            if (i < m) {
                for (int p = i; p < m; p++) {
                    int i8 = k;
                    k++;
                    tmp[i8] = this.content[p];
                }
            } else {
                for (int p2 = j; p2 < n; p2++) {
                    int i9 = k;
                    k++;
                    tmp[i9] = addendum[p2];
                }
            }
            this.content = tmp;
            this.size = k;
        }

        public String toString() {
            StringBuilder sb = new StringBuilder("{ ");
            for (int i = 0; i < size(); i++) {
                if (0 < i) {
                    sb.append(" , ");
                }
                Tuple t = Parser.this.rules[getRule(i)];
                sb.append(t.toString(getPosition(i)));
            }
            sb.append(" }");
            return sb.toString();
        }
    }
}
