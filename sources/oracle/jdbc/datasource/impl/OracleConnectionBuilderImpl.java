package oracle.jdbc.datasource.impl;

import java.util.concurrent.Executor;
import java.util.function.Function;
import javax.net.ssl.SSLContext;
import oracle.jdbc.AccessToken;
import oracle.jdbc.OracleCommonConnectionBuilder;
import oracle.jdbc.OracleConnection;
import oracle.jdbc.OracleConnectionBuilder;
import oracle.jdbc.OracleHostnameResolver;
import oracle.jdbc.OracleShardingKey;
import oracle.jdbc.TraceEventListener;
import oracle.jdbc.internal.AbstractConnectionBuilder;
import oracle.jdbc.logging.annotations.Blind;
import org.ietf.jgss.GSSCredential;

/* loaded from: ojdbc8.jar:oracle/jdbc/datasource/impl/OracleConnectionBuilderImpl.class */
public abstract class OracleConnectionBuilderImpl extends AbstractConnectionBuilder<OracleConnectionBuilderImpl, OracleConnection> implements OracleConnectionBuilder {
    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder accessToken(@Blind AccessToken accessToken) {
        return (OracleConnectionBuilder) super.accessToken(accessToken);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder, oracle.jdbc.OracleCommonConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder executorOracle(Executor executor) {
        return (OracleConnectionBuilder) super.executorOracle(executor);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder readOnlyInstanceAllowed(boolean z) {
        return (OracleConnectionBuilder) super.readOnlyInstanceAllowed(z);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder hostnameResolver(OracleHostnameResolver oracleHostnameResolver) {
        return (OracleConnectionBuilder) super.hostnameResolver(oracleHostnameResolver);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder traceEventListener(TraceEventListener traceEventListener) {
        return (OracleConnectionBuilder) super.traceEventListener(traceEventListener);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder radiusChallengeResponseHandler(Function function) {
        return (OracleConnectionBuilder) super.radiusChallengeResponseHandler((Function<byte[], byte[]>) function);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder sslContext(SSLContext sSLContext) {
        return (OracleConnectionBuilder) super.sslContext(sSLContext);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder gssCredential(GSSCredential gSSCredential) {
        return (OracleConnectionBuilder) super.gssCredential(gSSCredential);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder superShardingKey(OracleShardingKey oracleShardingKey) {
        return (OracleConnectionBuilder) super.superShardingKey(oracleShardingKey);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder shardingKey(OracleShardingKey oracleShardingKey) {
        return (OracleConnectionBuilder) super.shardingKey(oracleShardingKey);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder password(@Blind String str) {
        return (OracleConnectionBuilder) super.password(str);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder
    public /* bridge */ /* synthetic */ OracleConnectionBuilder user(String str) {
        return (OracleConnectionBuilder) super.user(str);
    }

    @Override // oracle.jdbc.OracleConnectionBuilder, oracle.jdbc.OracleCommonConnectionBuilder
    public /* bridge */ /* synthetic */ OracleCommonConnectionBuilder executorOracle(Executor executor) {
        return (OracleCommonConnectionBuilder) super.executorOracle(executor);
    }
}
