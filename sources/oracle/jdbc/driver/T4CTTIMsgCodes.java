package oracle.jdbc.driver;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIMsgCodes.class */
public class T4CTTIMsgCodes {
    public static final byte TTIPRO = 1;
    public static final byte TTIDTY = 2;
    public static final byte TTIFUN = 3;
    public static final byte TTIOER = 4;
    public static final byte TTIRXH = 6;
    public static final byte TTIRXD = 7;
    public static final byte TTIRPA = 8;
    public static final byte TTISTA = 9;
    public static final byte TTIIOV = 11;
    public static final byte TTISLG = 12;
    public static final byte TTIOAC = 13;
    public static final byte TTILOBD = 14;
    public static final byte TTIWRN = 15;
    public static final byte TTIDCB = 16;
    public static final byte TTIPFN = 17;
    public static final byte TTIFOB = 19;
    public static final byte TTIQC = 24;
    public static final byte TTIRSH = 25;
    public static final byte TTINTY = 1;
    public static final byte TTIBVC = 21;
    public static final byte TTISPF = 23;
    public static final byte TTIONEWAYFN = 26;
    public static final byte TTIIMPLRES = 27;
    public static final byte TTIRENEG = 28;
    public static final byte TTICOOKIE = 30;
    public static final byte TTITKN = 33;
    public static final byte TTIINIT = 34;
    static final byte OERFSPND = 1;
    static final byte OERFATAL = 2;
    static final byte OERFPLSW = 4;
    static final byte OERFUPD = 8;
    static final byte OERFEXIT = 16;
    static final byte OERFNCF = 32;
    static final byte OERFRDONLY = 64;
    static final short OERFSBRK = 128;
    static final byte OERwANY = 1;
    static final byte OERwTRUN = 2;
    static final byte OERwLICM = 2;
    static final byte OERwNVIC = 4;
    static final byte OERwITCE = 8;
    static final byte OERwUDnW = 16;
    static final byte OERwCPER = 32;
    static final byte OERwPLEX = 64;
    static final short ORACLE8_PROD_VERSION = 8030;
    static final short ORACLE81_PROD_VERSION = 8100;
    static final short MIN_OVERSION_SUPPORTED = 7230;
    static final short MIN_TTCVER_SUPPORTED = 4;
    static final short V8_TTCVER_SUPPORTED = 5;
    static final short MAX_TTCVER_SUPPORTED = 6;
    static final int REFCURSOR_SIZE = 5;
    static final byte OCQCINV = 1;
    static final byte OCOSPID = 2;
    static final byte OCTRCEVT = 3;
    static final byte OCSESSRET = 4;
    static final byte OCSSYNC = 5;
    static final byte OCXSSS = 6;
    static final byte OCLTXID = 7;
    static final byte OCAPPCONTCTL = 8;
    static final byte OCXSSS2 = 9;
    static final byte OSESSSIGN = 10;
    static final byte OCSHRDKEY = 11;
    static final byte MAX_OCFN = 11;
    static final int TTIEOCFRO = 1;
    static final int TTIEOCCUR = 2;
    static final int TTIEOCDON = 4;
    static final int TTIEOCECT = 8;
    static final int TTIEOCFSE = 16;
    static final int TTIEOCFPR = 32;
    static final int TTIEOCFSW = 64;
    static final int TTIEOCFMF = 128;
    static final int TTIEOCETS = 256;
    static final int TTIEOCFCP = 512;
    static final int TTIEOCFTI = 1024;
    static final int TTIEOCFIV = Integer.MIN_VALUE;
    static final int TTIEOCF_DROP_WHEN_RETURNED = 2048;
    static final int TTIEOCREL = 32768;
}
