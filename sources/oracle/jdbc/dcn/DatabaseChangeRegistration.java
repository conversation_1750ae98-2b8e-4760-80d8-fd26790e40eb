package oracle.jdbc.dcn;

import java.sql.SQLException;
import java.util.concurrent.Executor;
import oracle.jdbc.NotificationRegistration;

/* loaded from: ojdbc8.jar:oracle/jdbc/dcn/DatabaseChangeRegistration.class */
public interface DatabaseChangeRegistration extends NotificationRegistration {
    int getRegistrationId();

    long getRegId();

    String[] getTables();

    void addListener(DatabaseChangeListener databaseChangeListener) throws SQLException;

    void addListener(DatabaseChangeListener databaseChangeListener, Executor executor) throws SQLException;

    void removeListener(DatabaseChangeListener databaseChangeListener) throws SQLException;
}
