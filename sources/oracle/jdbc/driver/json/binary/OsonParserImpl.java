package oracle.jdbc.driver.json.binary;

import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.Period;
import java.util.Arrays;
import java.util.NoSuchElementException;
import oracle.jdbc.driver.json.JakartaParserWrapper;
import oracle.jdbc.driver.json.Jsonp;
import oracle.jdbc.driver.json.JsonpParserWrapper;
import oracle.jdbc.driver.json.OracleJsonExceptions;
import oracle.jdbc.driver.json.tree.OracleJsonBinaryImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDateImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDecimalImpl;
import oracle.jdbc.driver.json.tree.OracleJsonDoubleImpl;
import oracle.jdbc.driver.json.tree.OracleJsonFloatImpl;
import oracle.jdbc.driver.json.tree.OracleJsonIntervalDSImpl;
import oracle.jdbc.driver.json.tree.OracleJsonIntervalYMImpl;
import oracle.jdbc.driver.json.tree.OracleJsonNumberImpl;
import oracle.jdbc.driver.json.tree.OracleJsonStringImpl;
import oracle.jdbc.driver.json.tree.OracleJsonStringNumberImpl;
import oracle.jdbc.driver.json.tree.OracleJsonTimestampImpl;
import oracle.jdbc.driver.json.tree.OracleJsonTimestampTZImpl;
import oracle.jdbc.driver.json.tree.OracleJsonVectorImpl;
import oracle.sql.CHAR;
import oracle.sql.RAW;
import oracle.sql.json.OracleJsonArray;
import oracle.sql.json.OracleJsonBinary;
import oracle.sql.json.OracleJsonDate;
import oracle.sql.json.OracleJsonDecimal;
import oracle.sql.json.OracleJsonIntervalDS;
import oracle.sql.json.OracleJsonIntervalYM;
import oracle.sql.json.OracleJsonNumber;
import oracle.sql.json.OracleJsonObject;
import oracle.sql.json.OracleJsonParser;
import oracle.sql.json.OracleJsonString;
import oracle.sql.json.OracleJsonStructure;
import oracle.sql.json.OracleJsonTimestamp;
import oracle.sql.json.OracleJsonTimestampTZ;
import oracle.sql.json.OracleJsonValue;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonParserImpl.class */
public class OsonParserImpl extends OracleOsonValueFactory implements OracleJsonParser {
    private static int INITIAL_DEPTH_CAPACITY = 4;
    private final OsonContext ctx;
    private State state;
    private int depth;
    private OracleJsonDecimalImpl numberCache;
    private OracleJsonValue currentPrimitive;
    private Closeable closeable;
    OracleJsonParser.Event event;
    private StringPointer stringPointer = new StringPointer();
    private BinaryPointer binaryPointer = new BinaryPointer();
    private OracleJsonStructure[] depthStack = new OracleJsonStructure[INITIAL_DEPTH_CAPACITY];
    private int[] currentChild = new int[INITIAL_DEPTH_CAPACITY];
    private OsonAbstractObject[] objectCache = new OsonAbstractObject[INITIAL_DEPTH_CAPACITY];
    private OsonAbstractArray[] arrayCache = new OsonAbstractArray[INITIAL_DEPTH_CAPACITY];

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonParserImpl$State.class */
    private enum State {
        START,
        NEXT_VALUE,
        AFTER_KEY,
        FINISHED
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonParserImpl$StringPointer.class */
    private class StringPointer implements OracleJsonString {
        int pos;
        int len;

        private StringPointer() {
        }

        @Override // oracle.sql.json.OracleJsonValue
        public OracleJsonValue.OracleJsonType getOracleJsonType() {
            return OracleJsonValue.OracleJsonType.STRING;
        }

        @Override // oracle.sql.json.OracleJsonValue
        public <T> T wrap(Class<T> wrapper) {
            throw new UnsupportedOperationException();
        }

        @Override // oracle.sql.json.OracleJsonString
        public String getString() {
            OsonParserImpl.this.ctx.b.position(this.pos);
            return OsonParserImpl.this.ctx.b.readString(this.len);
        }

        @Override // oracle.sql.json.OracleJsonString
        public CharSequence getChars() {
            return getString();
        }

        @Override // oracle.sql.json.OracleJsonString
        public CHAR getCHAR() {
            throw new UnsupportedOperationException();
        }

        public void reset(int pos, int len) {
            this.pos = pos;
            this.len = len;
        }

        public int getLen() {
            return this.len;
        }

        public int getPos() {
            return this.pos;
        }
    }

    /* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/binary/OsonParserImpl$BinaryPointer.class */
    private class BinaryPointer implements OracleJsonBinary {
        int pos;
        int len;
        boolean isId;

        private BinaryPointer() {
        }

        @Override // oracle.sql.json.OracleJsonValue
        public OracleJsonValue.OracleJsonType getOracleJsonType() {
            return OracleJsonValue.OracleJsonType.BINARY;
        }

        @Override // oracle.sql.json.OracleJsonValue
        public <T> T wrap(Class<T> wrapper) {
            throw new UnsupportedOperationException();
        }

        @Override // oracle.sql.json.OracleJsonBinary
        public String getString() {
            OsonParserImpl.this.ctx.b.position(this.pos);
            return OsonParserImpl.this.ctx.b.readString(this.len);
        }

        @Override // oracle.sql.json.OracleJsonBinary
        public byte[] getBytes() {
            OsonParserImpl.this.ctx.b.position(this.pos);
            byte[] res = new byte[this.len];
            OsonParserImpl.this.ctx.b.get(res);
            return res;
        }

        protected void getBytes(OutputStream out) throws IOException {
            OsonParserImpl.this.ctx.b.position(this.pos);
            OsonParserImpl.this.ctx.b.readBytes(out, this.len);
        }

        @Override // oracle.sql.json.OracleJsonBinary
        public RAW getRAW() {
            return new RAW(getBytes());
        }

        @Override // oracle.sql.json.OracleJsonBinary
        public boolean isId() {
            return this.isId;
        }

        public void reset(int pos, int len, boolean isId) {
            this.pos = pos;
            this.len = len;
            this.isId = isId;
        }
    }

    public OsonParserImpl(OsonContext ctx) {
        this.ctx = ctx;
        init();
    }

    private void init() {
        this.state = State.START;
        this.depth = -1;
        this.event = null;
    }

    @Override // oracle.sql.json.OracleJsonParser
    public boolean hasNext() {
        return this.state != State.FINISHED;
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OracleJsonParser.Event next() {
        switch (this.state) {
            case START:
                int pos = this.ctx.getHeader().getTreeSegmentOffset();
                pushDepth();
                OracleJsonValue root = (OracleJsonValue) OsonStructureImpl.getValueInternal(pos, this, this.ctx);
                if (root.getOracleJsonType() != OracleJsonValue.OracleJsonType.ARRAY && root.getOracleJsonType() != OracleJsonValue.OracleJsonType.OBJECT) {
                    this.state = State.FINISHED;
                    this.currentPrimitive = root;
                    OracleJsonParser.Event eventOracleJsonTypeToEvent = OracleJsonTypeToEvent(root);
                    this.event = eventOracleJsonTypeToEvent;
                    return eventOracleJsonTypeToEvent;
                }
                setCurrent((OracleJsonStructure) root);
                this.state = State.NEXT_VALUE;
                if (root.getOracleJsonType() == OracleJsonValue.OracleJsonType.ARRAY) {
                    OracleJsonParser.Event event = OracleJsonParser.Event.START_ARRAY;
                    this.event = event;
                    return event;
                }
                OracleJsonParser.Event event2 = OracleJsonParser.Event.START_OBJECT;
                this.event = event2;
                return event2;
            case NEXT_VALUE:
                OracleJsonParser.Event eventNextValue = nextValue();
                this.event = eventNextValue;
                return eventNextValue;
            case AFTER_KEY:
                this.state = State.NEXT_VALUE;
                OracleJsonParser.Event eventNextChild = nextChild();
                this.event = eventNextChild;
                return eventNextChild;
            case FINISHED:
            default:
                throw new NoSuchElementException();
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public String getString() {
        if (this.state == State.AFTER_KEY) {
            return ((OsonAbstractObject) this.depthStack[this.depth]).getFieldName(this.currentChild[this.depth]);
        }
        if (this.currentPrimitive == null) {
            throw OracleJsonExceptions.BAD_PARSER_STATE3.create(this.ctx.getExceptionFactory(), this.event);
        }
        switch (this.currentPrimitive.getOracleJsonType()) {
            case BINARY:
                return ((OracleJsonBinary) this.currentPrimitive).getString();
            case DATE:
                return ((OracleJsonDate) this.currentPrimitive).getString();
            case DECIMAL:
            case FLOAT:
            case DOUBLE:
                return ((OracleJsonNumberImpl) this.currentPrimitive).getString();
            case INTERVALDS:
                return ((OracleJsonIntervalDS) this.currentPrimitive).getString();
            case INTERVALYM:
                return ((OracleJsonIntervalYM) this.currentPrimitive).getString();
            case STRING:
                return ((OracleJsonString) this.currentPrimitive).getString();
            case TIMESTAMP:
                return ((OracleJsonTimestamp) this.currentPrimitive).getString();
            case TIMESTAMPTZ:
                return ((OracleJsonTimestampTZ) this.currentPrimitive).getString();
            default:
                throw OracleJsonExceptions.BAD_PARSER_STATE3.create(this.ctx.getExceptionFactory(), this.event);
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public boolean isIntegralNumber() {
        assertNumeric();
        return ((OracleJsonNumber) this.currentPrimitive).isIntegral();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public int getInt() {
        assertNumeric();
        return ((OracleJsonNumber) this.currentPrimitive).intValue();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public long getLong() {
        assertNumeric();
        return ((OracleJsonNumber) this.currentPrimitive).longValue();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public BigDecimal getBigDecimal() {
        assertNumeric();
        return ((OracleJsonNumber) this.currentPrimitive).bigDecimalValue();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public BigInteger getBigInteger() {
        assertNumeric();
        return ((OracleJsonNumber) this.currentPrimitive).bigIntegerValue();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public double getDouble() {
        assertNumeric();
        return ((OracleJsonNumber) this.currentPrimitive).doubleValue();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public float getFloat() {
        assertNumeric();
        return ((OracleJsonNumber) this.currentPrimitive).floatValue();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OffsetDateTime getOffsetDateTime() {
        if (this.currentPrimitive == null) {
            throw OracleJsonExceptions.BAD_PARSER_STATE.create(this.ctx.getExceptionFactory(), OracleJsonValue.OracleJsonType.TIMESTAMPTZ.toString());
        }
        if (this.currentPrimitive.getOracleJsonType() == OracleJsonValue.OracleJsonType.TIMESTAMPTZ) {
            return ((OracleJsonTimestampTZ) this.currentPrimitive).getOffsetDateTime();
        }
        throw OracleJsonExceptions.BAD_PARSER_STATE.create(this.ctx.getExceptionFactory(), OracleJsonValue.OracleJsonType.TIMESTAMPTZ.toString());
    }

    @Override // oracle.sql.json.OracleJsonParser
    public LocalDateTime getLocalDateTime() {
        if (this.currentPrimitive == null) {
            throw OracleJsonExceptions.BAD_PARSER_STATE.create(this.ctx.getExceptionFactory(), OracleJsonValue.OracleJsonType.TIMESTAMP.toString());
        }
        if (this.currentPrimitive.getOracleJsonType() == OracleJsonValue.OracleJsonType.DATE) {
            return ((OracleJsonDate) this.currentPrimitive).getLocalDateTime();
        }
        if (this.currentPrimitive.getOracleJsonType() == OracleJsonValue.OracleJsonType.TIMESTAMP) {
            return ((OracleJsonTimestamp) this.currentPrimitive).getLocalDateTime();
        }
        throw OracleJsonExceptions.BAD_PARSER_STATE.create(this.ctx.getExceptionFactory(), OracleJsonValue.OracleJsonType.TIMESTAMP.toString());
    }

    @Override // oracle.sql.json.OracleJsonParser
    public byte[] getBytes() {
        assertJsonType(OracleJsonValue.OracleJsonType.BINARY);
        return this.binaryPointer.getBytes();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public void getBytes(OutputStream out) {
        assertJsonType(OracleJsonValue.OracleJsonType.BINARY);
        try {
            this.binaryPointer.getBytes(out);
        } catch (IOException e) {
            throw OracleJsonExceptions.IO.create(this.ctx.getExceptionFactory(), e, new Object[0]);
        }
    }

    public boolean isId() {
        assertJsonType(OracleJsonValue.OracleJsonType.BINARY);
        return this.binaryPointer.isId();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public Period getPeriod() {
        assertJsonType(OracleJsonValue.OracleJsonType.INTERVALYM);
        return ((OracleJsonIntervalYMImpl) this.currentPrimitive).getPeriod();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public Duration getDuration() {
        assertJsonType(OracleJsonValue.OracleJsonType.INTERVALDS);
        return ((OracleJsonIntervalDSImpl) this.currentPrimitive).getDuration();
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OracleJsonObject getObject() {
        assertJsonType(OracleJsonValue.OracleJsonType.OBJECT);
        Object obj = (OsonAbstractObject) getCurrent();
        this.objectCache[this.depth] = null;
        pop();
        return (OracleJsonObject) obj;
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OracleJsonValue getValue() {
        OracleOsonValueFactory f = OracleOsonValueFactory.INSTANCE;
        if (this.event == null || this.event == OracleJsonParser.Event.END_ARRAY || this.event == OracleJsonParser.Event.END_OBJECT) {
            throw OracleJsonExceptions.BAD_PARSER_STATE_VALUE.create(this.ctx.getExceptionFactory(), new Object[0]);
        }
        if (this.event == OracleJsonParser.Event.KEY_NAME) {
            return new OracleJsonStringImpl(getString());
        }
        if (this.event == OracleJsonParser.Event.VALUE_NULL) {
            return OracleJsonValue.NULL;
        }
        if (this.event == OracleJsonParser.Event.VALUE_FALSE) {
            return OracleJsonValue.FALSE;
        }
        if (this.event == OracleJsonParser.Event.VALUE_TRUE) {
            return OracleJsonValue.TRUE;
        }
        OracleJsonValue val = this.currentPrimitive == null ? getCurrent() : this.currentPrimitive;
        switch (val.getOracleJsonType()) {
            case BINARY:
                OracleJsonBinary bin = (OracleJsonBinary) val;
                return new OracleJsonBinaryImpl(bin.getBytes(), bin.isId());
            case DATE:
                OracleJsonDateImpl date = (OracleJsonDateImpl) val;
                return f.createDate(date.raw());
            case DECIMAL:
                if (val instanceof OracleJsonStringNumberImpl) {
                    OracleJsonStringNumberImpl stringNumber = (OracleJsonStringNumberImpl) val;
                    return new OracleJsonStringNumberImpl(stringNumber.getString());
                }
                OracleJsonDecimalImpl num = (OracleJsonDecimalImpl) val;
                return new OracleJsonDecimalImpl(num.raw(), num.getTargetType());
            case FLOAT:
                OracleJsonFloatImpl flt = (OracleJsonFloatImpl) val;
                return new OracleJsonFloatImpl(flt.floatValue());
            case DOUBLE:
                OracleJsonDoubleImpl dbl = (OracleJsonDoubleImpl) val;
                return new OracleJsonDoubleImpl(dbl.doubleValue());
            case INTERVALDS:
                OracleJsonIntervalDSImpl intDs = (OracleJsonIntervalDSImpl) val;
                return f.createIntervalDS(intDs.raw());
            case INTERVALYM:
                OracleJsonIntervalYMImpl intYm = (OracleJsonIntervalYMImpl) val;
                return f.createIntervalYM(intYm.raw());
            case STRING:
                return new OracleJsonStringImpl(((OracleJsonString) val).getString());
            case TIMESTAMP:
                OracleJsonTimestampImpl ts = (OracleJsonTimestampImpl) val;
                return f.createTimestamp(ts.raw());
            case TIMESTAMPTZ:
                OracleJsonTimestampTZImpl tstz = (OracleJsonTimestampTZImpl) val;
                return f.createTimestampTZ(tstz.raw());
            case ARRAY:
                return getArray();
            case VECTOR:
                return (OracleJsonVectorImpl) val;
            case OBJECT:
            default:
                return getObject();
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public OracleJsonArray getArray() {
        assertJsonType(OracleJsonValue.OracleJsonType.ARRAY);
        Object obj = (OsonAbstractArray) getCurrent();
        this.arrayCache[this.depth] = null;
        pop();
        return (OracleJsonArray) obj;
    }

    @Override // oracle.sql.json.OracleJsonParser
    public void skipArray() {
        if (this.depth >= 0 && getCurrent().getOracleJsonType() == OracleJsonValue.OracleJsonType.ARRAY) {
            pop();
            this.currentPrimitive = null;
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public void skipObject() {
        if (this.depth >= 0 && getCurrent().getOracleJsonType() == OracleJsonValue.OracleJsonType.OBJECT) {
            pop();
            this.currentPrimitive = null;
        }
    }

    @Override // oracle.sql.json.OracleJsonParser, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        if (this.closeable != null) {
            try {
                this.closeable.close();
                this.closeable = null;
            } catch (IOException e) {
                throw OracleJsonExceptions.IO.create(this.ctx.getExceptionFactory(), e, new Object[0]);
            }
        }
        this.depth = -1;
        this.state = State.FINISHED;
        this.event = null;
    }

    public void setCloseable(Closeable closeable) {
        this.closeable = closeable;
    }

    @Override // oracle.jdbc.driver.json.binary.OracleOsonValueFactory, oracle.jdbc.driver.json.binary.OsonValueFactory
    public OsonAbstractArray createArray(OsonContext ctx, int pos) {
        if (this.arrayCache[this.depth] == null) {
            this.arrayCache[this.depth] = ctx.getFactory().createArray(ctx, pos);
        } else {
            this.arrayCache[this.depth].init(pos);
        }
        return this.arrayCache[this.depth];
    }

    @Override // oracle.jdbc.driver.json.binary.OracleOsonValueFactory, oracle.jdbc.driver.json.binary.OsonValueFactory
    public OsonAbstractObject createObject(OsonContext ctx, int pos) {
        if (this.objectCache[this.depth] == null) {
            this.objectCache[this.depth] = ctx.getFactory().createObject(ctx, pos);
        } else {
            this.objectCache[this.depth].init(pos);
        }
        return this.objectCache[this.depth];
    }

    @Override // oracle.jdbc.driver.json.binary.OracleOsonValueFactory, oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createString(OsonContext ctx, int pos, int len) {
        this.stringPointer.reset(pos, len);
        return this.stringPointer;
    }

    @Override // oracle.jdbc.driver.json.binary.OracleOsonValueFactory, oracle.jdbc.driver.json.binary.OsonValueFactory
    public Object createBinary(OsonContext ctx, int pos, int len, boolean isId) {
        this.binaryPointer.reset(pos, len, isId);
        return this.binaryPointer;
    }

    @Override // oracle.jdbc.driver.json.binary.OracleOsonValueFactory, oracle.jdbc.driver.json.binary.OsonValueFactory
    public OracleJsonDecimalImpl createNumber(byte[] raw, OracleJsonDecimal.TargetType type) {
        if (this.numberCache == null) {
            this.numberCache = super.createNumber(raw, type);
        } else {
            this.numberCache.reset(raw, type);
        }
        return this.numberCache;
    }

    public boolean toEntry(String key) {
        if (this.event != OracleJsonParser.Event.START_OBJECT) {
            throw new IllegalStateException();
        }
        OsonObjectImpl obj = (OsonObjectImpl) getCurrent();
        int offset = obj.getChildPosition(key);
        if (offset == -1) {
            return false;
        }
        this.currentChild[this.depth] = offset;
        this.event = OracleJsonParser.Event.KEY_NAME;
        this.state = State.AFTER_KEY;
        return true;
    }

    public void reset() {
        init();
    }

    private void setCurrent(OracleJsonStructure v) {
        this.currentChild[this.depth] = 0;
        this.depthStack[this.depth] = v;
        this.currentPrimitive = null;
    }

    private OracleJsonStructure getCurrent() {
        return this.depthStack[this.depth];
    }

    private void pop() {
        this.depth--;
        if (this.depth < 0) {
            this.state = State.FINISHED;
        }
    }

    private OracleJsonParser.Event nextChild() {
        int child = this.currentChild[this.depth];
        int[] iArr = this.currentChild;
        int i = this.depth;
        iArr[i] = iArr[i] + 1;
        int pos = ((OsonStructureImpl) this.depthStack[this.depth]).getChildOffset(child);
        pushDepth();
        OracleJsonValue v = (OracleJsonValue) OsonStructureImpl.getValueInternal(pos, this, this.ctx);
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.OBJECT) {
            setCurrent((OracleJsonStructure) v);
            return OracleJsonParser.Event.START_OBJECT;
        }
        if (v.getOracleJsonType() == OracleJsonValue.OracleJsonType.ARRAY) {
            setCurrent((OracleJsonStructure) v);
            return OracleJsonParser.Event.START_ARRAY;
        }
        this.currentPrimitive = v;
        this.depth--;
        return OracleJsonTypeToEvent(v);
    }

    private OracleJsonParser.Event OracleJsonTypeToEvent(OracleJsonValue v) {
        switch (v.getOracleJsonType()) {
            case BINARY:
                return OracleJsonParser.Event.VALUE_BINARY;
            case DATE:
                return OracleJsonParser.Event.VALUE_DATE;
            case DECIMAL:
                return OracleJsonParser.Event.VALUE_DECIMAL;
            case FLOAT:
                return OracleJsonParser.Event.VALUE_FLOAT;
            case DOUBLE:
                return OracleJsonParser.Event.VALUE_DOUBLE;
            case INTERVALDS:
                return OracleJsonParser.Event.VALUE_INTERVALDS;
            case INTERVALYM:
                return OracleJsonParser.Event.VALUE_INTERVALYM;
            case STRING:
                return OracleJsonParser.Event.VALUE_STRING;
            case TIMESTAMP:
                return OracleJsonParser.Event.VALUE_TIMESTAMP;
            case TIMESTAMPTZ:
                return OracleJsonParser.Event.VALUE_TIMESTAMPTZ;
            case ARRAY:
            case OBJECT:
            default:
                throw new IllegalStateException(v.toString());
            case VECTOR:
                return OracleJsonParser.Event.VALUE_VECTOR;
            case FALSE:
                return OracleJsonParser.Event.VALUE_FALSE;
            case NULL:
                return OracleJsonParser.Event.VALUE_NULL;
            case TRUE:
                return OracleJsonParser.Event.VALUE_TRUE;
        }
    }

    private void pushDepth() {
        this.depth++;
        if (this.depth >= this.depthStack.length) {
            expand();
        }
    }

    private void expand() {
        this.depthStack = (OracleJsonStructure[]) Arrays.copyOf(this.depthStack, this.depthStack.length * 2);
        this.currentChild = Arrays.copyOf(this.currentChild, this.depthStack.length);
        this.objectCache = (OsonAbstractObject[]) Arrays.copyOf(this.objectCache, this.depthStack.length);
        this.arrayCache = (OsonAbstractArray[]) Arrays.copyOf(this.arrayCache, this.depthStack.length);
    }

    private OracleJsonParser.Event nextValue() {
        if (this.currentChild[this.depth] >= ((OsonStructureImpl) this.depthStack[this.depth]).size()) {
            if (this.depthStack[this.depth].getOracleJsonType() == OracleJsonValue.OracleJsonType.OBJECT) {
                pop();
                return OracleJsonParser.Event.END_OBJECT;
            }
            pop();
            return OracleJsonParser.Event.END_ARRAY;
        }
        if (this.depthStack[this.depth].getOracleJsonType() == OracleJsonValue.OracleJsonType.OBJECT) {
            this.state = State.AFTER_KEY;
            return OracleJsonParser.Event.KEY_NAME;
        }
        return nextChild();
    }

    private void assertNumeric() {
        OracleJsonValue val = this.currentPrimitive == null ? getCurrent() : this.currentPrimitive;
        OracleJsonValue.OracleJsonType type = val.getOracleJsonType();
        if (type != OracleJsonValue.OracleJsonType.DECIMAL && type != OracleJsonValue.OracleJsonType.DOUBLE && type != OracleJsonValue.OracleJsonType.FLOAT) {
            throw OracleJsonExceptions.BAD_PARSER_STATE3.create(this.ctx.getExceptionFactory(), this.event);
        }
    }

    private void assertJsonType(OracleJsonValue.OracleJsonType type) {
        if (this.depth < 0) {
            throw OracleJsonExceptions.BAD_PARSER_STATE.create(this.ctx.getExceptionFactory(), type.toString());
        }
        switch (type) {
            case DECIMAL:
            case FLOAT:
            case DOUBLE:
            case INTERVALDS:
            case INTERVALYM:
            case STRING:
            case TIMESTAMP:
            case TIMESTAMPTZ:
            case VECTOR:
            case FALSE:
            case NULL:
            case TRUE:
            default:
                if (this.currentPrimitive == null || this.currentPrimitive.getOracleJsonType() != type) {
                    throw OracleJsonExceptions.BAD_PARSER_STATE.create(this.ctx.getExceptionFactory(), type.toString());
                }
                return;
            case ARRAY:
                if (this.currentPrimitive != null || getCurrent().getOracleJsonType() != OracleJsonValue.OracleJsonType.ARRAY) {
                    throw OracleJsonExceptions.BAD_PARSER_STATE.create(this.ctx.getExceptionFactory(), type.toString());
                }
                return;
            case OBJECT:
                if (this.currentPrimitive != null || getCurrent().getOracleJsonType() != OracleJsonValue.OracleJsonType.OBJECT) {
                    throw OracleJsonExceptions.BAD_PARSER_STATE.create(this.ctx.getExceptionFactory(), type.toString());
                }
                return;
        }
    }

    @Override // oracle.sql.json.OracleJsonParser
    public <T> T wrap(Class<T> wrapper) {
        try {
            if (Jsonp.isJakartaJsonStream(wrapper)) {
                return wrapper.cast(new JakartaParserWrapper(this));
            }
            return wrapper.cast(new JsonpParserWrapper(this));
        } catch (ClassCastException e) {
            throw OracleJsonExceptions.BAD_WRAP.create(OracleJsonExceptions.ORACLE_FACTORY, e, wrapper.getName());
        }
    }

    public long getStreamOffset() {
        if (this.state == State.START) {
            return 0L;
        }
        return -1L;
    }

    public OsonContext getContext() {
        return this.ctx;
    }

    public int getCurrentStringPos() {
        return this.stringPointer.getPos();
    }

    public int getCurrentStringLen() {
        return this.stringPointer.getLen();
    }
}
