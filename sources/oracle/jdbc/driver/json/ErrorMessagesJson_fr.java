package oracle.jdbc.driver.json;

import java.util.ListResourceBundle;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/json/ErrorMessagesJson_fr.class */
public class ErrorMessagesJson_fr extends ListResourceBundle {
    public static final Object[][] contents = {new Object[]{"26301", "Exception d'E/S"}, new Object[]{"26302", "L''année \"{0}\" n''est pas prise en charge"}, new Object[]{"26303", "Débordement, valeur trop élevée : {0}."}, new Object[]{"26304", "Option non prise en charge (non implémentée)."}, new Object[]{"26305", "Le fichier JSON binaire n'est pas valide ou est endommagé."}, new Object[]{"26306", "Version de fichier JSON binaire non prise en charge : {0}."}, new Object[]{"26307", "La longueur de clé encodée en UTF-8 ne doit pas dépasser 256 octets. La clé suivante dépasse cette limite : \"{0}\"."}, new Object[]{"26308", "Le fichier JSON spécifié est trop volumineux pour être encodé en tant que fichier JSON binaire. La taille des images encodées ne doit pas dépasser 2 Go."}, new Object[]{"26309", "Le fichier JSON binaire n''est pas valide ou est endommagé. L''image spécifiée contient uniquement {0} octets."}, new Object[]{"26310", "Des jours sont définis pour l'élément java.time.Period indiqué mais le type de données INTERVAL YEAR TO MONTH d'Oracle ne prend pas en charge les jours."}, new Object[]{"26311", "Le générateur a été fermé avant la fin."}, new Object[]{"26312", "Une clé d'objet doit être indiquée dans ce contexte."}, new Object[]{"26313", "Ecriture non valide. Une valeur complète a déjà été écrite."}, new Object[]{"26314", "Fin non autorisée dans ce contexte."}, new Object[]{"26315", "Clé non autorisée dans ce contexte."}, new Object[]{"26316", "Valeur attendue après la clé."}, new Object[]{"26317", "L''état de l''analyseur doit être {0}."}, new Object[]{"26318", "L''état de l''analyseur ne doit pas être {0}."}, new Object[]{"26319", "L'analyseur doit être défini sur une valeur."}, new Object[]{"26320", "\"{0}\" n''est pas un type de wrapper pris en charge."}, new Object[]{"26321", "Impossible de modifier cet objet. Pour créer une copie modifiable, utilisez OracleJsonFactory.createObject(OracleJsonObject)."}, new Object[]{"26322", "Impossible de modifier ce tableau. Pour créer une copie modifiable, utilisez OracleJsonFactory.createArray(OracleJsonArray)."}, new Object[]{"26323", "L''objet JSON contient une clé en double : {0}."}, new Object[]{"26324", "Détection automatique de l'encodage impossible, nombre de caractères insuffisant."}, new Object[]{"26325", "Token EOF attendu, obtenu : {0}."}, new Object[]{"26326", "Caractère inattendu {0} à la ligne {1}, colonne {2}."}, new Object[]{"26327", "Caractère inattendu {0} à la ligne {1}, colonne {2}. Valeur attendue : {3}."}, new Object[]{"26328", "Token non valide {0} à la ligne {1}, colonne {2}. Tokens attendus : {3}."}, new Object[]{"26329", "JsonParser#getString() est valide uniquement pour les états d''analyseur KEY_NAME, VALUE_STRING, VALUE_NUMBER, mais l''état d''analyseur en cours est {0}."}, new Object[]{"26330", "JsonParser#isIntegralNumber() est valide uniquement pour l''état d''analyseur VALUE_NUMBER, mais l''état d''analyseur en cours est {0}."}, new Object[]{"26331", "JsonParser#getInt() est valide uniquement pour l''état d''analyseur VALUE_NUMBER, mais l''état d''analyseur en cours est {0}."}, new Object[]{"26332", "JsonParser#getLong() est valide uniquement pour l''état d''analyseur VALUE_NUMBER, mais l''état d''analyseur en cours est {0}."}, new Object[]{"26333", "JsonParser#getBigDecimal() est valide uniquement pour l''état d''analyseur VALUE_NUMBER, mais l''état d''analyseur en cours est {0}."}, new Object[]{"26334", "JsonParser#getArray() est valide uniquement pour l''état d''analyseur START_ARRAY, mais l''état d''analyseur en cours est {0}."}, new Object[]{"26335", "JsonParser#getObject() est valide uniquement pour l''état d''analyseur START_OBJECT, mais l''état d''analyseur en cours est {0}."}, new Object[]{"26336", "Un horodatage avec une région n'est pas pris en charge. Seuls les fuseaux horaires de décalage sont pris en charge."}, new Object[]{"26337", "Les objets et les tableaux dans la valeur JSON ne peuvent pas imbriquer plus de {0} niveaux"}, new Object[]{"26338", "Les clés d'un objet JSON ne peuvent pas dépasser 65 535 octets"}};

    @Override // java.util.ListResourceBundle
    public Object[][] getContents() {
        return contents;
    }
}
