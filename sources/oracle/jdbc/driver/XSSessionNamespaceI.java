package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Arrays;
import oracle.jdbc.internal.XSPrincipal;
import oracle.jdbc.internal.XSSessionNamespace;
import oracle.sql.TIMESTAMPTZ;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/XSSessionNamespaceI.class */
final class XSSessionNamespaceI extends XSSessionNamespace {
    XSPrincipalI kpxssessnsuser = null;
    String kpxssessnstenant = null;
    byte[] kpxssessnstenantBytes = null;
    byte[] kpxssessnssid = null;
    byte[] kpxssessnscookie = null;
    long kpxssessnsproxy = 0;
    long kpxssessnsaclids = 0;
    long kpxssessnscreator = 0;
    long kpxssessnsupdater = 0;
    byte[] kpxssessnscrets = null;
    byte[] kpxssessnsaccts = null;
    byte[] kpxssessnsautts = null;
    int kpxssessnstimeout = 0;

    XSSessionNamespaceI() {
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public XSPrincipal getPrincipal() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssessnsuser;
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public String getTenant() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssessnstenant;
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public byte[] getSessionId() {
        InternalFactory.xsSecurityCheck();
        if (this.kpxssessnssid == null) {
            return null;
        }
        return Arrays.copyOf(this.kpxssessnssid, this.kpxssessnssid.length);
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public byte[] getCookie() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssessnscookie == null ? this.kpxssessnscookie : Arrays.copyOf(this.kpxssessnscookie, this.kpxssessnscookie.length);
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public long getProxyId() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssessnsproxy;
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public long getACLId() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssessnsaclids;
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public long getCreatedBy() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssessnscreator;
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public long getUpdatedBy() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssessnsupdater;
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public TIMESTAMPTZ getCreateTimestamp() {
        InternalFactory.xsSecurityCheck();
        return new TIMESTAMPTZ(this.kpxssessnscrets);
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public TIMESTAMPTZ getAccessTimestamp() {
        InternalFactory.xsSecurityCheck();
        return new TIMESTAMPTZ(this.kpxssessnsaccts);
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public TIMESTAMPTZ getAuthTimestamp() {
        InternalFactory.xsSecurityCheck();
        return new TIMESTAMPTZ(this.kpxssessnsautts);
    }

    @Override // oracle.jdbc.internal.XSSessionNamespace
    public int getTimeout() {
        InternalFactory.xsSecurityCheck();
        return this.kpxssessnstimeout;
    }

    private void setPrincipal(XSPrincipal principal) throws SQLException {
        this.kpxssessnsuser = (XSPrincipalI) principal;
    }

    private void setTenant(String tenant) throws SQLException {
        this.kpxssessnstenant = tenant;
    }

    private void setSessionId(byte[] sid) throws SQLException {
        this.kpxssessnssid = sid;
    }

    private void setCookie(byte[] cookie) throws SQLException {
        this.kpxssessnscookie = cookie;
    }

    private void setProxyId(long proxy) throws SQLException {
        this.kpxssessnsproxy = proxy;
    }

    private void setACLId(long aclid) throws SQLException {
        this.kpxssessnsaclids = aclid;
    }

    private void setCreatedBy(long creator) throws SQLException {
        this.kpxssessnscreator = creator;
    }

    private void setUpdatedBy(long updater) throws SQLException {
        this.kpxssessnsupdater = updater;
    }

    private void setCreateTimestamp(byte[] _kpxssessnscrets) throws SQLException {
        this.kpxssessnscrets = _kpxssessnscrets;
    }

    private void setAccessTimestamp(byte[] _kpxssessnsaccts) throws SQLException {
        this.kpxssessnsaccts = _kpxssessnsaccts;
    }

    private void setAuthTimestamp(byte[] _kpxssessnsautts) throws SQLException {
        this.kpxssessnsautts = _kpxssessnsautts;
    }

    private void setTimeout(int timeout) throws SQLException {
        this.kpxssessnstimeout = timeout;
    }

    static XSSessionNamespaceI unmarshal(T4CMAREngine mar) throws SQLException, IOException {
        XSPrincipalI principal = XSPrincipalI.unmarshal(mar);
        int[] intArr = new int[1];
        String tenant = null;
        int tenantLength = (int) mar.unmarshalUB4();
        if (tenantLength > 0) {
            byte[] tenantBytesTemp = new byte[tenantLength];
            mar.unmarshalCLR(tenantBytesTemp, 0, intArr);
            tenant = mar.conv.CharBytesToString(tenantBytesTemp, intArr[0]);
        }
        byte[] sessionId = null;
        int sessionIdLength = (int) mar.unmarshalUB4();
        if (sessionIdLength > 0) {
            mar.unmarshalUB1();
            sessionId = mar.unmarshalNBytes(sessionIdLength);
        }
        byte[] cookie = null;
        int cookieLength = (int) mar.unmarshalUB4();
        if (cookieLength > 0) {
            mar.unmarshalUB1();
            cookie = mar.unmarshalNBytes(cookieLength);
        }
        long proxyId = mar.unmarshalSB8();
        long kpxssessnsaclids = mar.unmarshalSB8();
        long kpxssessnscreator = mar.unmarshalSB8();
        long kpxssessnsupdater = mar.unmarshalSB8();
        byte[] kpxssessnscrets = null;
        if (mar.unmarshalUB1() == 1) {
            int timestampLength = (int) mar.unmarshalUB4();
            kpxssessnscrets = mar.unmarshalNBytes(timestampLength);
        }
        byte[] kpxssessnsaccts = null;
        if (mar.unmarshalUB1() == 1) {
            int timestampLength2 = (int) mar.unmarshalUB4();
            kpxssessnsaccts = mar.unmarshalNBytes(timestampLength2);
        }
        byte[] kpxssessnsautts = null;
        if (mar.unmarshalUB1() == 1) {
            int timestampLength3 = (int) mar.unmarshalUB4();
            kpxssessnsautts = mar.unmarshalNBytes(timestampLength3);
        }
        int kpxssessnstimeout = (int) mar.unmarshalUB4();
        XSSessionNamespaceI namespace = new XSSessionNamespaceI();
        namespace.setPrincipal(principal);
        namespace.setTenant(tenant);
        namespace.setSessionId(sessionId);
        namespace.setCookie(cookie);
        namespace.setProxyId(proxyId);
        namespace.setACLId(kpxssessnsaclids);
        namespace.setCreatedBy(kpxssessnscreator);
        namespace.setUpdatedBy(kpxssessnsupdater);
        namespace.setCreateTimestamp(kpxssessnscrets);
        namespace.setAccessTimestamp(kpxssessnsaccts);
        namespace.setAuthTimestamp(kpxssessnsautts);
        namespace.setTimeout(kpxssessnstimeout);
        return namespace;
    }
}
