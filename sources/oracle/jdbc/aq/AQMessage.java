package oracle.jdbc.aq;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Struct;
import oracle.sql.ANYDATA;
import oracle.sql.RAW;
import oracle.sql.STRUCT;
import oracle.sql.json.OracleJsonDatum;
import oracle.xdb.XMLType;

/* loaded from: ojdbc8.jar:oracle/jdbc/aq/AQMessage.class */
public interface AQMessage {
    byte[] getMessageId() throws SQLException;

    AQMessageProperties getMessageProperties() throws SQLException;

    void setPayload(byte[] bArr) throws SQLException;

    void setPayload(byte[] bArr, byte[] bArr2) throws SQLException;

    void setPayload(Struct struct) throws SQLException;

    void setPayload(STRUCT struct) throws SQLException;

    void setPayload(ANYDATA anydata) throws SQLException;

    void setPayload(RAW raw) throws SQLException;

    void setPayload(XMLType xMLType) throws SQLException;

    void setPayload(OracleJsonDatum oracleJsonDatum) throws SQLException;

    byte[] getPayload() throws SQLException;

    byte[] getPayloadTOID();

    STRUCT getSTRUCTPayload() throws SQLException;

    STRUCT getSTRUCTPayload(Connection connection) throws SQLException;

    Struct getStructPayload(Connection connection) throws SQLException;

    Struct getStructPayload() throws SQLException;

    boolean isSTRUCTPayload() throws SQLException;

    ANYDATA getANYDATAPayload() throws SQLException;

    boolean isANYDATAPayload() throws SQLException;

    RAW getRAWPayload() throws SQLException;

    boolean isRAWPayload() throws SQLException;

    XMLType getXMLTypePayload() throws SQLException;

    boolean isXMLTypePayload() throws SQLException;

    OracleJsonDatum getJSONPayload() throws SQLException;

    boolean isJSONPayload() throws SQLException;

    String toString();
}
