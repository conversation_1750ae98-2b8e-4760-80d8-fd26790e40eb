package oracle.jdbc.driver;

import java.io.IOException;
import java.sql.SQLException;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/T4CTTIkpdnrri.class */
class T4CTTIkpdnrri {
    byte[] kpdnrrinm;
    T4CMAREngine mar;

    T4CTTIkpdnrri(T4CConnection connection) {
        this.mar = connection.mare;
    }

    void receive() throws SQLException, IOException {
        int kpdnrrinml = this.mar.unmarshalSWORD();
        if (kpdnrrinml > 0) {
            this.kpdnrrinm = new byte[kpdnrrinml];
            int[] intArray = new int[1];
            this.mar.unmarshalCLR(this.kpdnrrinm, 0, intArray, kpdnrrinml);
            return;
        }
        this.kpdnrrinm = null;
    }

    byte[] getKpdnrrinm() {
        return this.kpdnrrinm;
    }
}
