package oracle.jdbc.driver;

import java.sql.SQLException;
import java.util.Map;
import oracle.sql.Datum;
import oracle.sql.ROWID;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/RowidAccessor.class */
class RowidAccessor extends Accessor {
    static final int MAXLENGTH = 4000;
    static final int EXTENDED_ROWID_MAX_LENGTH = 18;

    RowidAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind) throws SQLException {
        this(stmt, max_len, form, external_type, isOutBind, isOutBind ? stmt.areOutBindsStoredInBindData() : false);
    }

    RowidAccessor(OracleStatement stmt, int max_len, short form, int external_type, boolean isOutBind, boolean isStoredInBindData) throws SQLException {
        super(Representation.ROWID, stmt, 4000, isStoredInBindData);
        init(stmt, 104, 9, form, isOutBind);
        initForDataAccess(external_type, max_len, null);
    }

    RowidAccessor(OracleStatement stmt, int max_len, boolean nullable, int flags, int precision, int scale, long contflag, int total_elems, short form) throws SQLException {
        super(Representation.ROWID, stmt, 4000, false);
        init(stmt, 104, 9, form, false);
        initForDescribe(104, max_len, nullable, flags, precision, scale, contflag, total_elems, form, null);
        initForDataAccess(0, max_len, null);
    }

    @Override // oracle.jdbc.driver.Accessor
    void initForDataAccess(int external_type, int max_len, String typeName) throws SQLException {
        if (external_type != 0) {
            this.externalType = external_type;
        }
        this.byteLength = this.representationMaxLength + 2;
    }

    @Override // oracle.jdbc.driver.Accessor, oracle.jdbc.driver.GeneratedAccessor
    String getString(int currentRow) throws SQLException {
        if (isNull(currentRow)) {
            return null;
        }
        return this.rowData.getString(getOffset(currentRow), getLength(currentRow), this.statement.connection.conversion.getCharacterSet((short) 1));
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow) throws SQLException {
        return getROWID(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Datum getOracleObject(int currentRow) throws SQLException {
        return getROWID(currentRow);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    ROWID getROWID(int currentRow) throws SQLException {
        byte[] b = getBytes(currentRow);
        if (b == null) {
            return null;
        }
        return new ROWID(b);
    }

    @Override // oracle.jdbc.driver.GeneratedAccessor
    Object getObject(int currentRow, Map<String, Class<?>> map) throws SQLException {
        return getROWID(currentRow);
    }
}
