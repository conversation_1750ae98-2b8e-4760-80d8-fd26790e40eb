package oracle.jdbc.driver;

/* loaded from: ojdbc8.jar:oracle/jdbc/driver/TemplateOverflow.class */
final class TemplateOverflow {
    long templateId;
    byte[] overflow;
    boolean complete;
    long overflowSignature;

    TemplateOverflow(long templateId, byte[] overflow, boolean complete, long overflowSignature) {
        this.templateId = templateId;
        this.overflow = overflow;
        this.complete = complete;
        this.overflowSignature = overflowSignature;
    }

    final long getTemplateId() {
        return this.templateId;
    }

    final byte[] getOverflow() {
        return this.overflow;
    }

    final boolean isOverflowComplete() {
        return this.complete;
    }

    public String toString() {
        return "TemplateOverflow[TemplateID=" + Long.toHexString(getTemplateId()) + (this.complete ? ", FULL-overflow" : ", DELTA-overflow") + ", Length=" + (this.overflow == null ? 0 : this.overflow.length) + ", Signature=" + Long.toHexString(this.overflowSignature) + "]";
    }
}
